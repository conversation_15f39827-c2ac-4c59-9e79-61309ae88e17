import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';
import { Exclude } from 'class-transformer';
import { GenerateUUID } from '@/common/utils/index';
// 移除直接导入其他模块的Entity，使用字符串关系定义避免循环依赖
// import { ServiceEntity } from '../../service/entities/service.entity';
// import { UserEntity } from '../../user/entities/user.entity';

export enum OrderType {
  RECHARGE = 'RECHARGE', // 充值额度
  SERVICE_PURCHASE = 'SERVICE', // 购买服务
  REWARD = 'REWARD', // 奖励订单
}
export enum PaymentStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
  CANCELLED = 'CANCELLED',
}

@Entity('open_order', {
  comment: '服务订单表',
})
export class OrderEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 50, unique: true })
  orderNumber: string; // 订单号

  @Column({
    type: 'enum',
    enum: OrderType,
    enumName: 'order_type_enum',
  })
  type: OrderType;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ name: 'purchase_count', nullable: true })
  purchaseCount?: number; // 购买次数

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    enumName: 'payment_status_enum',
    default: PaymentStatus.PENDING,
  })
  status: PaymentStatus;

  @Column({ name: 'paid_at', type: 'timestamp', nullable: true })
  paidAt?: Date; // 支付时间

  @Column({ name: 'transaction_id', type: 'varchar', length: 100, nullable: true })
  transactionId?: string; // 第三方交易ID

  @Column({ type: 'text', nullable: true })
  metadata?: Record<string, any>; // 元数据

  // 关系 - 使用字符串关系定义，避免循环引用
  @ManyToOne('UserEntity', 'orders')
  @JoinColumn({ name: 'userId' })
  user: any;

  @ManyToOne('ServiceEntity', 'orders', {
    nullable: true,
  })
  @JoinColumn({ name: 'serviceId' })
  service?: any;
}
