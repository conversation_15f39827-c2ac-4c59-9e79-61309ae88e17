import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { createBullBoard } from '@bull-board/api';
import { ExpressAdapter } from '@bull-board/express';
import { BullAdapter } from '@bull-board/api/bullAdapter';
import { QUEUE_CONFIG } from '../config/queue.config';
import { BULL_BOARD_CONFIG } from '../config/bull-board.config';
import { StructuredLogger } from '../../../common/logging/structured-logger';

/**
 * Bull面板服务
 * 为Bull队列提供Web界面监控
 */
@Injectable()
export class BullBoardService implements OnModuleInit {
  private readonly serverAdapter = new ExpressAdapter();
  
  constructor(
    @InjectQueue(QUEUE_CONFIG.queues.OCR_QUEUE.name)
    private readonly ocrQueue: Queue,
    @InjectQueue(QUEUE_CONFIG.queues.STO_OCR_QUEUE.name)
    private readonly stoOcrQueue: Queue,
    @InjectQueue(QUEUE_CONFIG.queues.ADDRESS_QUEUE.name)
    private readonly addressQueue: Queue,
    @InjectQueue(QUEUE_CONFIG.queues.GEO_QUEUE.name)
    private readonly geoQueue: Queue,
    private readonly logger: StructuredLogger,
  ) {
    this.serverAdapter.setBasePath(BULL_BOARD_CONFIG.route);
  }
  
  /**
   * 模块初始化时设置Bull面板
   */
  onModuleInit(): void {
    createBullBoard({
      queues: [
        new BullAdapter(this.ocrQueue),
        new BullAdapter(this.stoOcrQueue),
        new BullAdapter(this.addressQueue),
        new BullAdapter(this.geoQueue),
      ],
      serverAdapter: this.serverAdapter,
    });
    
    // 日志记录
    this.logger.log(
      `Bull面板已初始化，访问路径: ${BULL_BOARD_CONFIG.route}`,
      { module: 'BullBoardService' }
    );
  }
  
  /**
   * 获取Express路由处理器
   */
  getRouter() {
    return this.serverAdapter.getRouter();
  }
  
  /**
   * 获取基础路径
   */
  getBasePath(): string {
    return BULL_BOARD_CONFIG.route;
  }
  
  /**
   * 获取用户名密码
   */
  getCredentials(): { username: string; password: string } {
    return {
      username: BULL_BOARD_CONFIG.username,
      password: BULL_BOARD_CONFIG.password,
    };
  }
} 