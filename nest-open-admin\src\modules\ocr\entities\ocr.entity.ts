import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';
import { ApiProperty } from '@nestjs/swagger';

export enum OcrSourceType {
  FILE = 'file',
  BASE64 = 'base64',
}

@Entity('open_ocr', {
  comment: 'ocr表',
})
export class OcrEntity  extends BaseEntity {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: '记录ID' })
  id: number;

  @Column({ length: 255, nullable: true })
  @ApiProperty({ description: '原始文件名' })
  originalFilename: string;

  @Column({ length: 255, nullable: true })
  @ApiProperty({ description: '存储文件路径' })
  filePath: string;
  
  @Column({ 
    type: 'enum', 
    enum: OcrSourceType, 
    default: OcrSourceType.FILE,
    comment: '识别来源类型'
  })
  @ApiProperty({ description: '识别来源类型', enum: OcrSourceType })
  sourceType: OcrSourceType;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({ description: 'OCR识别结果JSON' })
  ocrResult: string;

  @Column({ default: false })
  @ApiProperty({ description: '是否识别成功' })
  isSuccess: boolean;

  @Column({ length: 255, nullable: true })
  @ApiProperty({ description: '错误信息' })
  errorMessage: string;

  @Column({ type: 'float', default: 0 })
  @ApiProperty({ description: '处理时间(ms)' })
  processingTime: number;

}