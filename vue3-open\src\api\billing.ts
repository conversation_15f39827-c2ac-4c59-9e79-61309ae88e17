import { request } from '@/utils/request'
import type { Bill, BillDetail, AccountInfo, RechargeRecord } from '@/types/billing'

/**
 * 账单相关API
 */
export const billingApi = {
  /**
   * 获取账户信息
   */
  getAccountInfo(): Promise<AccountInfo> {
    return request.get('/op/billing/account')
  },

  /**
   * 获取账单列表
   * @param params 查询参数
   */
  getBills(params?: {
    page?: number
    pageSize?: number
    startTime?: string
    endTime?: string
    status?: string
    type?: string
  }): Promise<{
    data: Bill[]
    total: number
    page: number
    pageSize: number
  }> {
    return request.get('/op/billing/bills', { params })
  },

  /**
   * 获取账单详情
   * @param billId 账单ID
   */
  getBillDetail(billId: string): Promise<BillDetail> {
    return request.get(`/op/billing/bills/${billId}`)
  },

  /**
   * 下载账单
   * @param billId 账单ID
   * @param format 文件格式
   */
  downloadBill(billId: string, format: 'pdf' | 'excel' = 'pdf'): Promise<Blob> {
    return request.get(`/op/billing/bills/${billId}/download`, {
      params: { format },
      responseType: 'blob'
    })
  },

  /**
   * 批量下载账单
   * @param params 下载参数
   */
  downloadBillsBatch(params: {
    startTime: string
    endTime: string
    format?: 'pdf' | 'excel'
  }): Promise<Blob> {
    return request.post('/op/billing/bills/download-batch', params, {
      responseType: 'blob'
    })
  },

  /**
   * 获取使用统计
   * @param params 查询参数
   */
  getUsageStats(params?: {
    startTime?: string
    endTime?: string
    serviceId?: string
    groupBy?: 'day' | 'week' | 'month'
  }): Promise<{
    totalCalls: number
    totalCost: number
    services: {
      serviceId: string
      serviceName: string
      calls: number
      cost: number
    }[]
    timeline: {
      date: string
      calls: number
      cost: number
    }[]
  }> {
    return request.get('/op/billing/usage-stats', { params })
  },

  /**
   * 账户充值
   * @param data 充值数据
   */
  recharge(data: {
    amount: number
    paymentMethod: string
    returnUrl?: string
  }): Promise<{
    rechargeId: string
    paymentUrl?: string
    qrCode?: string
  }> {
    return request.post('/op/billing/recharge', data)
  },

  /**
   * 获取充值记录
   * @param params 查询参数
   */
  getRechargeRecords(params?: {
    page?: number
    pageSize?: number
    startTime?: string
    endTime?: string
    status?: string
  }): Promise<{
    data: RechargeRecord[]
    total: number
    page: number
    pageSize: number
  }> {
    return request.get('/op/billing/recharge-records', { params })
  },

  /**
   * 查询充值状态
   * @param rechargeId 充值ID
   */
  getRechargeStatus(rechargeId: string): Promise<{
    status: 'pending' | 'success' | 'failed' | 'cancelled'
    amount: number
    completedAt?: string
    failReason?: string
  }> {
    return request.get(`/op/billing/recharge/${rechargeId}/status`)
  },

  /**
   * 获取消费预测
   * @param params 预测参数
   */
  getCostPrediction(params?: {
    days?: number
    serviceId?: string
  }): Promise<{
    predictedCost: number
    confidence: number
    factors: {
      name: string
      impact: number
      description: string
    }[]
  }> {
    return request.get('/op/billing/cost-prediction', { params })
  },

  /**
   * 设置消费预警
   * @param data 预警设置
   */
  setCostAlert(data: {
    threshold: number
    type: 'daily' | 'monthly'
    enabled: boolean
    notificationMethods: string[]
  }): Promise<void> {
    return request.post('/op/billing/cost-alert', data)
  },

  /**
   * 获取消费预警设置
   */
  getCostAlert(): Promise<{
    threshold: number
    type: 'daily' | 'monthly'
    enabled: boolean
    notificationMethods: string[]
  }> {
    return request.get('/op/billing/cost-alert')
  },

  /**
   * 获取价格表
   */
  getPricing(): Promise<{
    services: {
      serviceId: string
      serviceName: string
      pricing: {
        type: 'per_call' | 'per_minute' | 'per_mb'
        price: number
        unit: string
        description?: string
      }[]
    }[]
  }> {
    return request.get('/op/billing/pricing')
  }
}