# 订单需求文档

## 1. 文档概述

本文档描述了开放平台订单模块的需求规范，该模块基于单一职责原则设计，专注于订单的创建、管理和查询功能。订单模块作为支付充值模块的协作模块，负责记录和跟踪用户的充值和服务购买行为，维护订单的完整生命周期，确保交易记录的准确性和可追溯性。

## 2. 功能需求

### 2.1 核心功能

- **订单创建**
  - 支持充值订单创建
  - 支持服务购买订单创建
  - 生成唯一订单号
  - 记录订单详细信息（金额、用户、商品、描述等）

- **订单状态管理**
  - 维护订单完整生命周期
  - 订单状态变更记录
  - 处理订单取消和退款
  - 订单超时自动关闭

- **订单查询**
  - 按用户查询订单
  - 按条件筛选订单（状态、时间、金额等）
  - 查询订单详情
  - 导出订单数据

- **统计分析**
  - 订单总量和金额统计
  - 完成率和转化率分析
  - 时段分布分析
  - 用户购买行为分析

### 2.2 非功能需求

- **高性能**：订单查询响应时间<200ms，创建订单<500ms
- **高可用性**：系统容错和自动恢复机制
- **数据一致性**：保证订单数据的完整性和准确性
- **安全性**：防止未授权访问和数据泄露
- **可扩展性**：支持未来业务增长和新订单类型

## 3. 技术规范

### 3.1 订单模型

```typescript
// 简化示意
export enum OrderType {
  RECHARGE = 'recharge',           // 充值订单
  SERVICE_PURCHASE = 'purchase',    // 服务购买订单
}

export enum OrderStatus {
  CREATED = 'created',             // 已创建
  PENDING_PAYMENT = 'pending_payment', // 待支付
  PAID = 'paid',                   // 已支付
  COMPLETED = 'completed',         // 已完成
  CANCELLED = 'cancelled',         // 已取消
  PAYMENT_FAILED = 'payment_failed',   // 支付失败
  REFUNDING = 'refunding',         // 退款中
  REFUNDED = 'refunded',           // 已退款
  CLOSED = 'closed'                // 已关闭
}

@Entity('orders')
export class OrderEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;
  
  @Column()
  orderNo: string;  // 业务订单号，例如 ORD202308150001
  
  @Column()
  userId: number;
  
  @Column({ type: 'enum', enum: OrderType })
  type: OrderType;
  
  @Column({ type: 'enum', enum: OrderStatus, default: OrderStatus.CREATED })
  status: OrderStatus;
  
  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;
  
  @Column()
  description: string;
  
  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>;  // 存储订单相关的额外数据
  
  @Column({ nullable: true })
  paymentId: string;  // 关联的支付ID
  
  @Column()
  createdAt: Date;
  
  @Column({ nullable: true })
  paidAt: Date;
  
  @Column({ nullable: true })
  completedAt: Date;
  
  @Column({ nullable: true })
  cancelledAt: Date;
  
  @Column({ type: 'jsonb', nullable: true })
  statusHistory: Array<{
    status: OrderStatus;
    timestamp: Date;
    reason?: string;
  }>;  // 订单状态历史记录
}
```

### 3.2 订单服务实现

```typescript
// 简化示意
@Injectable()
export class OrderService {
  constructor(
    @InjectRepository(OrderEntity)
    private orderRepository: Repository<OrderEntity>,
    private logger: LoggerService,
    private configService: ConfigService,
    private eventEmitter: EventEmitter2,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache
  ) {}

  /**
   * 创建订单
   */
  async createOrder(orderData: CreateOrderDto): Promise<OrderEntity> {
    const { userId, type, amount, description, metadata } = orderData;
    
    // 生成唯一订单号
    const orderNo = await this.generateOrderNo(type);
    
    // 创建订单实体
    const order = this.orderRepository.create({
      orderNo,
      userId,
      type,
      status: OrderStatus.PENDING_PAYMENT,
      amount,
      description,
      metadata,
      createdAt: new Date(),
      statusHistory: [{
        status: OrderStatus.CREATED,
        timestamp: new Date()
      }, {
        status: OrderStatus.PENDING_PAYMENT,
        timestamp: new Date()
      }]
    });
    
    // 保存订单
    const savedOrder = await this.orderRepository.save(order);
    
    // 发送订单创建事件
    this.eventEmitter.emit('order.created', {
      orderId: savedOrder.id,
      userId,
      type,
      amount
    });
    
    // 设置订单超时关闭任务
    await this.setOrderExpirationTimeout(savedOrder.id);
    
    this.logger.log(
      `订单创建 - ID: ${savedOrder.id}, 订单号: ${orderNo}, 用户: ${userId}, 类型: ${type}, 金额: ${amount}`,
      'OrderService'
    );
    
    return savedOrder;
  }

  /**
   * 更新订单状态
   */
  async updateOrderStatus(
    orderId: string, 
    status: OrderStatus, 
    additionalData: {
      paymentId?: string;
      reason?: string;
    } = {}
  ): Promise<OrderEntity> {
    const order = await this.orderRepository.findOne({ where: { id: orderId } });
    
    if (!order) {
      throw new NotFoundException(`订单 ${orderId} 不存在`);
    }
    
    // 检查状态转换是否有效
    this.validateStatusTransition(order.status, status);
    
    // 更新订单状态
    order.status = status;
    order.statusHistory = order.statusHistory || [];
    
    // 添加状态历史记录
    order.statusHistory.push({
      status,
      timestamp: new Date(),
      reason: additionalData.reason
    });
    
    // 根据状态设置相关字段
    if (status === OrderStatus.PAID) {
      order.paidAt = new Date();
      order.paymentId = additionalData.paymentId;
    } else if (status === OrderStatus.COMPLETED) {
      order.completedAt = new Date();
    } else if (status === OrderStatus.CANCELLED) {
      order.cancelledAt = new Date();
    }
    
    // 保存订单
    const updatedOrder = await this.orderRepository.save(order);
    
    // 发送订单状态变更事件
    this.eventEmitter.emit('order.status_changed', {
      orderId: updatedOrder.id,
      userId: updatedOrder.userId,
      status,
      previousStatus: order.status,
      reason: additionalData.reason
    });
    
    this.logger.log(
      `订单状态更新 - ID: ${orderId}, 订单号: ${order.orderNo}, 状态: ${status}`,
      'OrderService'
    );
    
    return updatedOrder;
  }

  /**
   * 查询用户订单列表
   */
  async getUserOrders(
    userId: number,
    queryParams: OrderQueryDto
  ): Promise<{ orders: OrderEntity[]; total: number }> {
    const { status, type, startDate, endDate, page, limit, sortBy, sortOrder } = queryParams;
    
    // 构建查询条件
    const queryBuilder = this.orderRepository.createQueryBuilder('order')
      .where('order.userId = :userId', { userId });
    
    // 添加过滤条件
    if (status) {
      queryBuilder.andWhere('order.status = :status', { status });
    }
    
    if (type) {
      queryBuilder.andWhere('order.type = :type', { type });
    }
    
    if (startDate) {
      queryBuilder.andWhere('order.createdAt >= :startDate', { 
        startDate: new Date(startDate) 
      });
    }
    
    if (endDate) {
      queryBuilder.andWhere('order.createdAt <= :endDate', { 
        endDate: new Date(endDate) 
      });
    }
    
    // 添加排序
    const sortField = sortBy || 'createdAt';
    const order = sortOrder === 'asc' ? 'ASC' : 'DESC';
    queryBuilder.orderBy(`order.${sortField}`, order);
    
    // 添加分页
    const pageNum = page || 1;
    const pageSize = limit || 10;
    queryBuilder.skip((pageNum - 1) * pageSize).take(pageSize);
    
    // 执行查询
    const [orders, total] = await queryBuilder.getManyAndCount();
    
    return { orders, total };
  }

  /**
   * 获取订单详情
   */
  async getOrderById(
    orderId: string,
    options: { userId?: number } = {}
  ): Promise<OrderEntity> {
    const queryBuilder = this.orderRepository.createQueryBuilder('order')
      .where('order.id = :orderId', { orderId });
    
    // 如果指定了用户ID，则检查订单是否属于该用户
    if (options.userId) {
      queryBuilder.andWhere('order.userId = :userId', { userId: options.userId });
    }
    
    const order = await queryBuilder.getOne();
    
    if (!order) {
      throw new NotFoundException(`订单 ${orderId} 不存在`);
    }
    
    return order;
  }

  /**
   * 取消订单
   */
  async cancelOrder(
    orderId: string,
    reason: string,
    options: { userId?: number } = {}
  ): Promise<OrderEntity> {
    const order = await this.getOrderById(orderId, options);
    
    // 检查订单是否可以取消
    if (order.status !== OrderStatus.PENDING_PAYMENT && order.status !== OrderStatus.CREATED) {
      throw new BadRequestException(`订单 ${orderId} 当前状态 ${order.status} 不可取消`);
    }
    
    // 更新订单状态为已取消
    return this.updateOrderStatus(orderId, OrderStatus.CANCELLED, { reason });
  }

  /**
   * 获取订单统计数据
   */
  async getOrderStatistics(
    userId: number,
    period: 'day' | 'week' | 'month' = 'month'
  ): Promise<OrderStatisticsDto> {
    // 计算统计时间范围
    const endDate = new Date();
    let startDate: Date;
    
    switch (period) {
      case 'day':
        startDate = subDays(endDate, 30); // 最近30天
        break;
      case 'week':
        startDate = subMonths(endDate, 3); // 最近3个月
        break;
      case 'month':
        startDate = subMonths(endDate, 12); // 最近12个月
        break;
    }
    
    // 查询订单总数和总金额
    const totalStats = await this.orderRepository.createQueryBuilder('order')
      .select('COUNT(order.id)', 'count')
      .addSelect('SUM(order.amount)', 'totalAmount')
      .where('order.userId = :userId', { userId })
      .andWhere('order.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getRawOne();
    
    // 查询已完成订单数和金额
    const completedStats = await this.orderRepository.createQueryBuilder('order')
      .select('COUNT(order.id)', 'count')
      .addSelect('SUM(order.amount)', 'totalAmount')
      .where('order.userId = :userId', { userId })
      .andWhere('order.status = :status', { status: OrderStatus.COMPLETED })
      .andWhere('order.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getRawOne();
    
    // 按时间分组统计
    const timeFormat = period === 'day' 
      ? 'YYYY-MM-DD' 
      : period === 'week' 
        ? 'YYYY-WW' 
        : 'YYYY-MM';
    
    const timeGroupStats = await this.orderRepository.createQueryBuilder('order')
      .select(`TO_CHAR(order.createdAt, '${timeFormat}')`, 'timeGroup')
      .addSelect('COUNT(order.id)', 'count')
      .addSelect('SUM(order.amount)', 'totalAmount')
      .where('order.userId = :userId', { userId })
      .andWhere('order.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .groupBy('timeGroup')
      .orderBy('timeGroup', 'ASC')
      .getRawMany();
    
    // 计算完成率
    const totalCount = Number(totalStats.count) || 0;
    const completedCount = Number(completedStats.count) || 0;
    const completionRate = totalCount > 0 ? (completedCount / totalCount) : 0;
    
    return {
      totalOrders: totalCount,
      totalAmount: Number(totalStats.totalAmount) || 0,
      completedOrders: completedCount,
      completedAmount: Number(completedStats.totalAmount) || 0,
      completionRate,
      timeGroupStats: timeGroupStats.map(item => ({
        timeGroup: item.timeGroup,
        count: Number(item.count),
        amount: Number(item.totalAmount) || 0
      }))
    };
  }

  /**
   * 生成唯一订单号
   */
  private async generateOrderNo(type: OrderType): Promise<string> {
    // 生成日期前缀，例如：ORD20230815
    const datePrefix = format(new Date(), 'yyyyMMdd');
    const typePrefix = type === OrderType.RECHARGE ? 'RCH' : 'PUR';
    const prefix = `${typePrefix}${datePrefix}`;
    
    // 获取当天的订单序列号并自增
    const sequenceKey = `order_sequence:${datePrefix}`;
    let sequence = await this.cacheManager.get<number>(sequenceKey);
    
    if (sequence === undefined) {
      // 如果缓存中不存在，则从数据库查询当天最大序列号
      const lastOrder = await this.orderRepository.createQueryBuilder('order')
        .where('order.orderNo LIKE :prefix', { prefix: `${prefix}%` })
        .orderBy('order.orderNo', 'DESC')
        .getOne();
      
      if (lastOrder) {
        const lastSequence = parseInt(lastOrder.orderNo.substring(prefix.length), 10);
        sequence = isNaN(lastSequence) ? 0 : lastSequence;
      } else {
        sequence = 0;
      }
    }
    
    // 递增序列号
    sequence++;
    
    // 更新缓存，设置24小时过期
    await this.cacheManager.set(sequenceKey, sequence, { ttl: 86400 });
    
    // 生成6位序列号，前导补0
    const sequencePart = sequence.toString().padStart(6, '0');
    
    return `${prefix}${sequencePart}`;
  }

  /**
   * 设置订单超时关闭定时任务
   */
  private async setOrderExpirationTimeout(orderId: string): Promise<void> {
    // 获取订单支付超时时间，默认30分钟
    const expirationMinutes = this.configService.get<number>(
      'order.expirationMinutes', 
      30
    );
    
    // 添加订单到超时队列
    await this.cacheManager.set(
      `order_timeout:${orderId}`,
      {
        orderId,
        expirationTime: new Date(Date.now() + expirationMinutes * 60 * 1000).toISOString()
      },
      { ttl: expirationMinutes * 60 }
    );
    
    this.logger.debug(
      `设置订单 ${orderId} 超时关闭任务，超时时间: ${expirationMinutes} 分钟`,
      'OrderService'
    );
  }

  /**
   * 验证订单状态转换是否有效
   */
  private validateStatusTransition(currentStatus: OrderStatus, newStatus: OrderStatus): void {
    // 定义有效的状态转换
    const validTransitions: Record<OrderStatus, OrderStatus[]> = {
      [OrderStatus.CREATED]: [
        OrderStatus.PENDING_PAYMENT,
        OrderStatus.CANCELLED,
        OrderStatus.CLOSED
      ],
      [OrderStatus.PENDING_PAYMENT]: [
        OrderStatus.PAID,
        OrderStatus.PAYMENT_FAILED,
        OrderStatus.CANCELLED,
        OrderStatus.CLOSED
      ],
      [OrderStatus.PAID]: [
        OrderStatus.COMPLETED,
        OrderStatus.REFUNDING
      ],
      [OrderStatus.COMPLETED]: [
        OrderStatus.REFUNDING
      ],
      [OrderStatus.CANCELLED]: [],
      [OrderStatus.PAYMENT_FAILED]: [
        OrderStatus.PENDING_PAYMENT,
        OrderStatus.CANCELLED,
        OrderStatus.CLOSED
      ],
      [OrderStatus.REFUNDING]: [
        OrderStatus.REFUNDED
      ],
      [OrderStatus.REFUNDED]: [],
      [OrderStatus.CLOSED]: []
    };
    
    // 检查状态转换是否有效
    if (!validTransitions[currentStatus]?.includes(newStatus)) {
      throw new BadRequestException(
        `无效的状态转换：从 ${currentStatus} 到 ${newStatus}`
      );
    }
  }
}
```

### 3.3 定时任务处理

```typescript
// 简化示意
@Injectable()
export class OrderTasksService {
  constructor(
    private readonly orderService: OrderService,
    private readonly cacheManager: Cache,
    private readonly logger: LoggerService
  ) {}

  /**
   * 处理过期订单，定时执行
   */
  @Cron('0 */5 * * * *') // 每5分钟执行一次
  async handleExpiredOrders(): Promise<void> {
    this.logger.log('开始处理过期订单', 'OrderTasksService');
    
    try {
      // 获取所有等待处理的超时订单
      const cacheKeys = await this.scanCacheKeys('order_timeout:*');
      
      if (cacheKeys.length === 0) {
        return;
      }
      
      const now = new Date();
      let closedCount = 0;
      
      // 处理每个超时订单
      for (const key of cacheKeys) {
        const orderTimeout = await this.cacheManager.get<{
          orderId: string;
          expirationTime: string;
        }>(key);
        
        if (!orderTimeout) continue;
        
        const expirationTime = new Date(orderTimeout.expirationTime);
        
        // 检查订单是否已过期
        if (now >= expirationTime) {
          try {
            // 获取订单信息
            const order = await this.orderService.getOrderById(orderTimeout.orderId);
            
            // 只关闭待支付状态的订单
            if (order.status === OrderStatus.PENDING_PAYMENT) {
              await this.orderService.updateOrderStatus(
                order.id,
                OrderStatus.CLOSED,
                { reason: '支付超时自动关闭' }
              );
              closedCount++;
            }
            
            // 从缓存中删除超时记录
            await this.cacheManager.del(key);
          } catch (error) {
            this.logger.error(
              `处理超时订单 ${orderTimeout.orderId} 失败: ${error.message}`,
              error.stack,
              'OrderTasksService'
            );
          }
        }
      }
      
      if (closedCount > 0) {
        this.logger.log(
          `成功关闭 ${closedCount} 个超时订单`,
          'OrderTasksService'
        );
      }
    } catch (error) {
      this.logger.error(
        `处理超时订单失败: ${error.message}`,
        error.stack,
        'OrderTasksService'
      );
    }
  }

  /**
   * 扫描缓存中的键
   */
  private async scanCacheKeys(pattern: string): Promise<string[]> {
    // 实现取决于所使用的缓存服务
    // 这里假设我们使用 Redis 作为缓存服务
    if (this.cacheManager['store'] && typeof this.cacheManager['store'].keys === 'function') {
      return await this.cacheManager['store'].keys(pattern);
    }
    return [];
  }
}
```

## 4. 系统架构

### 4.1 模块结构

```
order/
├── dto/
│   ├── create-order.dto.ts         # 创建订单DTO
│   ├── order-query.dto.ts          # 订单查询DTO
│   └── order-statistics.dto.ts     # 订单统计DTO
├── entities/
│   └── order.entity.ts             # 订单实体
├── services/
│   ├── order.service.ts            # 订单核心服务
│   └── order-tasks.service.ts      # 订单定时任务服务
├── controllers/
│   └── order.controller.ts         # 订单控制器
├── order.module.ts                 # 订单模块定义
└── order-status.enum.ts            # 订单状态枚举
```

### 4.2 订单流程图

```
graph TD
    A[创建订单请求] --> B[生成唯一订单号]
    B --> C[创建订单记录]
    C --> D[设置支付超时定时任务]
    D --> E{订单类型?}
    
    E -->|充值| F1[创建充值支付]
    E -->|服务购买| F2[创建服务购买支付]
    
    F1 --> G[等待支付完成]
    F2 --> G
    
    G --> H{支付是否成功?}
    H -->|是| I[更新订单状态为已支付]
    H -->|否| J[更新订单状态为支付失败]
    
    I --> K{订单类型?}
    K -->|充值| L1[完成充值流程]
    K -->|服务购买| L2[完成购买流程]
    
    L1 --> M[更新订单状态为已完成]
    L2 --> M
    
    J --> N{是否重试支付?}
    N -->|是| O[重置订单状态为待支付]
    O --> G
    N -->|否| P[更新订单状态为已关闭]
    
    D --> Q[订单支付超时检查]
    Q --> R{订单是否已支付?}
    R -->|是| S[不处理]
    R -->|否| T[更新订单状态为已关闭]
    
    M --> U[结束订单流程]
    P --> U
    T --> U
```

### 4.3 数据流图

```
graph LR
    Client[客户端] -->|创建订单| OrderController
    OrderController -->|请求处理| OrderService
    
    OrderService -->|创建订单| Database[(数据库)]
    OrderService -->|查询订单| Database
    OrderService -->|更新订单状态| Database
    
    OrderService -->|订单创建事件| EventBus[事件总线]
    OrderService -->|订单状态变更事件| EventBus
    
    EventBus -->|订单创建通知| PaymentService[支付服务]
    PaymentService -->|支付结果| OrderService
    
    OrderService -->|设置超时任务| Cache[(缓存)]
    OrderTasksService -->|查询超时订单| Cache
    OrderTasksService -->|处理超时订单| OrderService
    
    OrderService -->|订单数据| OrderController
    OrderController -->|响应| Client
```

## 5. 接口定义

### 5.1 创建订单

```
POST /orders
Content-Type: application/json
Authorization: Bearer <token>

Request:
{
  "type": "recharge",
  "amount": 100,
  "description": "账户充值",
  "metadata": {
    "paymentMethod": "alipay"
  }
}

Response:
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "orderNo": "RCH20230815000001",
  "userId": 123,
  "type": "recharge",
  "status": "pending_payment",
  "amount": 100,
  "description": "账户充值",
  "metadata": {
    "paymentMethod": "alipay"
  },
  "createdAt": "2023-08-15T10:30:00Z"
}
```

### 5.2 查询订单列表

```
GET /orders?status=pending_payment&type=recharge&page=1&limit=10
Authorization: Bearer <token>

Response:
{
  "orders": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "orderNo": "RCH20230815000001",
      "userId": 123,
      "type": "recharge",
      "status": "pending_payment",
      "amount": 100,
      "description": "账户充值",
      "createdAt": "2023-08-15T10:30:00Z"
    },
    // 更多订单...
  ],
  "total": 25,
  "page": 1,
  "limit": 10,
  "pages": 3
}
```

### 5.3 获取订单详情

```
GET /orders/550e8400-e29b-41d4-a716-446655440000
Authorization: Bearer <token>

Response:
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "orderNo": "RCH20230815000001",
  "userId": 123,
  "type": "recharge",
  "status": "pending_payment",
  "amount": 100,
  "description": "账户充值",
  "metadata": {
    "paymentMethod": "alipay"
  },
  "createdAt": "2023-08-15T10:30:00Z",
  "statusHistory": [
    {
      "status": "created",
      "timestamp": "2023-08-15T10:30:00Z"
    },
    {
      "status": "pending_payment",
      "timestamp": "2023-08-15T10:30:00Z"
    }
  ]
}
```

### 5.4 取消订单

```
POST /orders/550e8400-e29b-41d4-a716-446655440000/cancel
Content-Type: application/json
Authorization: Bearer <token>

Request:
{
  "reason": "用户主动取消"
}

Response:
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "orderNo": "RCH20230815000001",
  "status": "cancelled",
  "cancelledAt": "2023-08-15T10:45:00Z"
}
```

### 5.5 获取订单统计

```
GET /orders/statistics?period=month
Authorization: Bearer <token>

Response:
{
  "totalOrders": 45,
  "totalAmount": 4320.50,
  "completedOrders": 42,
  "completedAmount": 4100.50,
  "completionRate": 0.93,
  "timeGroupStats": [
    {
      "timeGroup": "2023-07",
      "count": 22,
      "amount": 2100.50
    },
    {
      "timeGroup": "2023-08",
      "count": 23,
      "amount": 2220.00
    }
  ]
}
```

## 6. 实现要点

### 6.1 订单唯一性

- **唯一订单号生成**：使用日期前缀+类型前缀+自增序列的方式生成可读且唯一的订单号
- **幂等性设计**：确保重复提交的订单请求不会创建多个订单
- **数据库约束**：使用唯一索引和约束确保订单号的唯一性

### 6.2 状态管理

- **严格的状态转换**：定义清晰的状态转换规则，确保订单状态变更符合业务逻辑
- **状态历史记录**：记录订单状态的完整变更历史，包括时间和原因
- **事件驱动**：状态变更时触发对应事件，通知相关模块进行后续处理

### 6.3 超时处理

- **分布式超时管理**：使用缓存存储订单超时信息，支持分布式环境
- **定时任务处理**：定期检查并处理超时订单，避免资源占用
- **优雅降级**：超时处理失败时的重试机制和降级策略

### 6.4 并发控制

- **乐观锁**：使用版本号控制并发更新冲突
- **事务管理**：确保订单创建和更新的原子性
- **异步处理**：耗时操作异步执行，不阻塞主流程

## 7. 与其他模块的集成

### 7.1 与支付模块集成

- **订单创建**：支付模块请求订单创建，订单模块返回订单信息
- **支付通知**：支付模块通知订单状态更新（支付成功、失败等）
- **订单查询**：支付模块查询订单信息用于支付处理

### 7.2 与用户服务模块集成

- **用户验证**：验证订单是否属于当前用户
- **服务验证**：验证用户购买的服务是否有效
- **服务购买通知**：通知用户服务模块进行服务购买后的处理

## 8. 后续优化方向

- **高级搜索**：支持更复杂的订单搜索和筛选功能
- **订单审计**：完善审计日志，记录订单全生命周期的操作
- **批量处理**：支持批量订单创建和处理
- **导出功能**：支持多种格式的订单数据导出
- **服务订阅**：支持订阅型服务的订单管理
- **退款流程**：完善退款处理流程和策略
- **可视化分析**：增强订单数据的可视化分析功能
- **多语言支持**：订单描述和通知的多语言支持 