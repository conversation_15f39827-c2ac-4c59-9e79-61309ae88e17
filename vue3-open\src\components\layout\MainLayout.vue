<template>
  <div class="main-layout">
    <!-- 顶部导航栏 -->
    <el-header v-if="!isConsolePage" class="header">
      <div class="header-content">
        <div class="logo">
          <router-link to="/" class="logo-link">
            <img class="logo-icon" :src="logo" alt="">
            <!-- <el-icon class="logo-icon"><Platform /></el-icon> -->
            <span class="logo-text">{{ appTitle }}</span>
          </router-link>
        </div>

        <div class="nav-menu">
          <el-menu mode="horizontal" :default-active="activeMenu" class="nav-menu-items" @select="handleMenuSelect">
            <el-menu-item index="/">首页</el-menu-item>
            <el-menu-item index="/services">服务列表</el-menu-item>
            <el-menu-item index="/playground">API测试</el-menu-item>
            <el-menu-item index="/docs">开发文档</el-menu-item>
            <el-menu-item index="/faq">常见问题</el-menu-item>
            <el-menu-item index="/about">关于我们</el-menu-item>
          </el-menu>
        </div>

        <!-- 移动端菜单按钮 -->
        <div class="mobile-menu-btn" @click="toggleMobileMenu">
          <el-icon>
            <Operation />
          </el-icon>
        </div>

        <div class="user-actions">
          <template v-if="userStore.isLoggedIn">
            <el-dropdown @command="handleUserCommand">
              <span class="user-info">
                <el-avatar :size="32" :src="userStore.userInfo?.avatar">
                  <el-icon>
                    <User />
                  </el-icon>
                </el-avatar>
                <span class="username">{{ userStore.userInfo?.username }}</span>
                <el-icon class="dropdown-icon">
                  <ArrowDown />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="console">
                    <el-icon>
                      <Setting />
                    </el-icon>
                    开放平台
                  </el-dropdown-item>
                  <el-dropdown-item command="profile">
                    <el-icon>
                      <User />
                    </el-icon>
                    个人资料
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon>
                      <SwitchButton />
                    </el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template v-else>
            <div class="auth-buttons">
              <el-button class="login-btn" @click="$router.push('/login')">登录</el-button>
              <el-button class="register-btn" @click="$router.push('/register')">注册</el-button>
            </div>
          </template>
        </div>
      </div>
    </el-header>

    <!-- 移动端菜单 -->
    <div v-if="!isConsolePage" class="mobile-menu" :class="{ 'show': showMobileMenu }">
      <div class="mobile-menu-overlay" @click="toggleMobileMenu"></div>
      <div class="mobile-menu-content">
        <div class="mobile-menu-header">
          <div class="mobile-logo">
            <img class="logo-icon" :src="logo" alt="">
            <span class="logo-text">{{ appTitle }}</span>
          </div>
          <div class="mobile-menu-close" @click="toggleMobileMenu">
            <el-icon>
              <Close />
            </el-icon>
          </div>
        </div>

        <div class="mobile-menu-items">
          <div class="mobile-menu-item" @click="handleMobileMenuSelect('/')">
            <span>首页</span>
          </div>
          <div class="mobile-menu-item" @click="handleMobileMenuSelect('/services')">
            <span>服务列表</span>
          </div>
          <!-- <div class="mobile-menu-item" @click="handleMobileMenuSelect('/packages')">
            <span>套餐包</span>
          </div> -->
          <div class="mobile-menu-item" @click="handleMobileMenuSelect('/docs')">
            <span>开发文档</span>
          </div>
          <div class="mobile-menu-item" @click="handleMobileMenuSelect('/faq')">
            <span>常见问题</span>
          </div>
          <div class="mobile-menu-item" @click="handleMobileMenuSelect('/about')">
            <span>关于我们</span>
          </div>
        </div>

        <div class="mobile-menu-actions">
          <template v-if="userStore.isLoggedIn">
            <div class="mobile-user-info">
              <el-avatar :size="40" :icon="User" />
              <span class="mobile-username">{{ userStore.userInfo?.username }}</span>
            </div>
            <div class="mobile-menu-item" @click="handleMobileMenuSelect('/console')">
              <el-icon>
                <Setting />
              </el-icon>
              <span>控制台</span>
            </div>
            <div class="mobile-menu-item" @click="handleUserCommand('logout')">
              <el-icon>
                <SwitchButton />
              </el-icon>
              <span>退出登录</span>
            </div>
          </template>
          <template v-else>
            <div class="mobile-auth-buttons">
              <el-button type="primary" @click="handleMobileMenuSelect('/login')" class="mobile-auth-btn">
                登录
              </el-button>
              <el-button @click="handleMobileMenuSelect('/register')" class="mobile-auth-btn">
                注册
              </el-button>
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <el-main class="main-content">
      <div class="page-container" :class="{ 'is-transitioning': isTransitioning }">
        <!-- 页面加载指示器 -->
        <div v-if="isTransitioning" class="page-loading">
          <div class="loading-spinner">
            <el-icon class="rotating">
              <Loading />
            </el-icon>
          </div>
        </div>

        <router-view v-slot="{ Component, route }">
          <transition name="page-transition" mode="default" @before-enter="onBeforeEnter" @enter="onEnter"
            @after-enter="onAfterEnter" @before-leave="onBeforeLeave" @leave="onLeave" @after-leave="onAfterLeave">
            <component :is="Component" :key="route.path" />
          </transition>
        </router-view>
      </div>
    </el-main>

    <!-- 底部 -->
    <el-footer v-if="!isConsolePage" class="footer">
      <div class="footer-main">
        <div class="footer-content">
          <!-- 公司信息 -->
          <div class="footer-section company-info">
            <div class="footer-logo">
              <img class="logo-icon" :src="logo" alt="">
              <span class="logo-text">{{ appTitle }}</span>
            </div>
            <p class="company-desc">
              专业的AI服务平台，为开发者提供高质量的人工智能API服务，
              助力企业数字化转型和智能化升级。
            </p>
            <div class="company-features">
              <span class="feature-tag">高性能</span>
              <span class="feature-tag">安全可靠</span>
              <span class="feature-tag">易于集成</span>
            </div>
          </div>

          <!-- 产品服务 -->
          <div class="footer-section">
            <h4 class="section-title">产品服务</h4>
            <ul class="footer-links">
              <li><router-link to="/services">AI工具服务</router-link></li>
              <!-- <li><router-link to="/packages">套餐包</router-link></li> -->
              <li><router-link to="/playground">API测试</router-link></li>
              <li><router-link to="/docs">开发文档</router-link></li>
            </ul>
          </div>

          <!-- 帮助支持 -->
          <div class="footer-section">
            <h4 class="section-title">帮助支持</h4>
            <ul class="footer-links">
              <li><router-link to="/faq">常见问题</router-link></li>
              <li><router-link to="/about">关于我们</router-link></li>
              <li><a href="mailto:<EMAIL>">技术支持</a></li>
              <li><a href="tel:************">客服热线</a></li>
            </ul>
          </div>

          <!-- 联系我们 -->
          <div class="footer-section contact-info">
            <h4 class="section-title">联系我们</h4>
            <!-- <div class="contact-item">
              <el-icon><Phone /></el-icon>
              <span>************</span>
            </div> -->
            <div class="contact-item">
              <el-icon>
                <Message />
              </el-icon>
              <span><EMAIL></span>
            </div>
            <div class="contact-item">
              <el-icon>
                <Location />
              </el-icon>
              <span>上海市奉贤区贤益园区</span>
            </div>

            <!-- 二维码 -->
            <div class="qr-codes">
              <div class="qr-item">
                <div class="qr-placeholder">
                  <!-- <el-icon size="40"><QrCode /></el-icon> -->
                </div>
                <span>微信公众号</span>
              </div>
              <div class="qr-item">
                <div class="qr-placeholder">
                  <!-- <el-icon size="40"><QrCode /></el-icon> -->
                </div>
                <span>技术交流群</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 备案信息 -->
      <div class="footer-bottom">
        <div class="footer-bottom-content">
          <div class="copyright">
            <span>&copy; 2025 {{ appTitle }}. All rights reserved.</span>
            <!-- <span class="separator">|</span> -->
            <!-- <span>基于 NestJS + Vue3 构建</span> -->
          </div>
          <div class="icp-info">
            <span>ICP备案编号:</span>
            <span>沪ICP备2025124358号-1</span>
            <!-- <span class="separator">|</span> -->
            <!-- <span>京公网安备 11010502012345号</span> -->
          </div>
        </div>
      </div>
    </el-footer>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Platform,
  User,
  Setting,
  SwitchButton,
  ArrowDown,
  Phone,
  Message,
  Location,
  Operation,
  Close,
  Loading,
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import logo from '@/assets/logo.svg'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const appTitle = import.meta.env.VITE_APP_TITLE
const showMobileMenu = ref(false)
const isTransitioning = ref(false)

// 切换移动端菜单
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

// 处理移动端菜单选择
const handleMobileMenuSelect = (path: string) => {
  router.push(path)
  showMobileMenu.value = false
}

// 判断是否为控制台页面
const isConsolePage = computed(() => {
  return route.path.startsWith('/console')
})

// 当前激活的菜单项
const activeMenu = computed(() => {
  const path = route.path
  if (path.startsWith('/services')) return '/services'
  if (path.startsWith('/packages')) return '/packages'
  if (path.startsWith('/docs')) return '/docs'
  if (path.startsWith('/faq')) return '/faq'
  if (path.startsWith('/about')) return '/about'
  if (path.startsWith('/playground')) return '/playground'
  return '/'
})

// 处理菜单选择
const handleMenuSelect = (index: string) => {
  router.push(index)
}

// 处理用户下拉菜单命令
const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'console':
      router.push('/console')
      break
    case 'profile':
      router.push('/console/profile')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        await userStore.logout()
        ElMessage.success('已退出登录')
        router.push('/login')
      } catch {
        // 用户取消
      }
      break
  }
}

// 页面过渡动画处理
const onBeforeEnter = (el: Element) => {
  // 新页面准备进入时
  isTransitioning.value = true
}

const onEnter = (el: Element, done: () => void) => {
  // 新页面开始进入动画
  setTimeout(() => {
    done()
  }, 300)
}

const onAfterEnter = (el: Element) => {
  // 新页面进入完成
  isTransitioning.value = false
}

const onBeforeLeave = (el: Element) => {
  // 旧页面准备离开
  isTransitioning.value = true
}

const onLeave = (el: Element, done: () => void) => {
  // 旧页面开始离开动画
  setTimeout(() => {
    done()
  }, 200)
}

const onAfterLeave = (el: Element) => {
  // 旧页面离开完成
  // isTransitioning 状态由 onAfterEnter 控制
}

// 组件挂载时初始化用户状态
onMounted(async () => {
  try {
    await userStore.initializeUserState()
  } catch (error) {
    console.error('初始化用户状态失败:', error)
  }
})
</script>

<style scoped>
.main-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
  border-bottom: none;
  padding: 0;
  height: 70px;
  box-shadow: 0 4px 20px rgba(15, 23, 42, 0.3);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.05) 100%);
  pointer-events: none;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #ffffff;
  font-weight: 700;
  font-size: 20px;
}

.logo-icon {
  width: 50px;
  height: 50px;
  margin-right: 10px;
  max-height: 50px;
}

.logo-text {
  color: #ffffff;
}

.nav-menu {
  flex: 1;
  display: flex;
  justify-content: center;
  min-width: 0;
  overflow: visible;
}

.nav-menu-items {
  border-bottom: none !important;
  background: transparent !important;
  flex-wrap: nowrap !important;
  overflow: visible !important;
  min-width: max-content !important;
  width: 100% !important;
  max-width: none !important;
  display: flex !important;
  justify-content: center !important;
}

.nav-menu-items .el-menu-item {
  border-bottom: none;
  color: #ffffff;
  font-weight: 500;
  margin: 0 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  opacity: 0.85;
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: unset !important;
  min-width: auto;
  width: auto;
  padding: 0 16px;
  max-width: none !important;
  flex-shrink: 0;
}

.nav-menu-items .el-menu-item:hover {
  background: rgba(255, 255, 255, 0.15);
  color: #ffffff;
  opacity: 1;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-menu-items .el-menu-item.is-active {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border-bottom: none;
  opacity: 1;
  font-weight: 600;
}

.user-actions {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.username {
  margin: 0 8px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.dropdown-icon {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.auth-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.login-btn {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: #ffffff !important;
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.login-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.register-btn {
  background: #ffffff !important;
  border: 1px solid #ffffff !important;
  color: #1e3a8a !important;
  font-weight: 600;
  padding: 10px 20px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.register-btn:hover {
  background: #f8fafc !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.main-content {
  flex: 1;
  padding: 0;
  background: #f5f7fa;
  min-height: 0;
  overflow-y: auto;
  margin-top: 60px;
}

.footer {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 25%, #cbd5e1 50%, #94a3b8 75%, #64748b 100%);
  color: #1e293b;
  position: relative;
  overflow: hidden;
  border-top: 1px solid #e2e8f0;
  min-height: 400px;
  height: auto;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
  pointer-events: none;
}

.footer-main {
  position: relative;
  z-index: 1;
  padding: 60px 0 40px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 40px;
  padding: 0 20px;
}

.footer-section {
  display: flex;
  flex-direction: column;
}

.company-info {
  max-width: 300px;
}

.footer-logo {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.footer-logo .logo-icon {
  width: 32px;
  height: 32px;
  margin-right: 12px;
}

.footer-logo .logo-text {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
}

.company-desc {
  color: #475569;
  line-height: 1.6;
  margin-bottom: 20px;
  font-size: 14px;
}

.company-features {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.feature-tag {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.section-title {
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #1e40af);
  border-radius: 1px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  color: #64748b;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
  display: inline-block;
}

.footer-links a:hover {
  color: #3b82f6;
  transform: translateX(4px);
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  color: #475569;
  font-size: 14px;
}

.contact-item .el-icon {
  margin-right: 8px;
  color: #3b82f6;
  font-size: 16px;
}

.qr-codes {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.qr-item {
  text-align: center;
}

.qr-placeholder {
  width: 60px;
  height: 60px;
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.qr-placeholder:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.4);
}

.qr-placeholder .el-icon {
  color: #64748b;
}

.qr-item span {
  font-size: 12px;
  color: #64748b;
}

.footer-bottom {
  border-top: 1px solid rgba(59, 130, 246, 0.1);
  padding: 20px 0;
  position: relative;
  z-index: 1;
}

.footer-bottom-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.copyright,
.icp-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #64748b;
}

.separator {
  color: #94a3b8;
}

.footer-links .el-link {
  color: #909399;
  font-size: 14px;
}

@media (max-width: 768px) {
  .header-content {
    padding: 0 15px;
    justify-content: space-between;
  }

  .nav-menu-items .el-menu-item {
    margin: 0 4px;
    font-size: 14px;
    padding: 0 12px;
  }

  .logo-link {
    font-size: 16px;
  }

  .logo-icon {
    width: 24px;
    height: 24px;
    margin-right: 6px;
  }

  .footer {
    padding: 40px 0 20px;
    min-height: auto;
  }

  .footer-main {
    padding: 40px 0 20px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
    padding: 0 20px;
  }

  .footer-section {
    margin-bottom: 0;
  }

  .company-info {
    max-width: 100%;
    order: 1;
  }

  .footer-section:nth-child(2) {
    order: 2;
  }

  .footer-section:nth-child(3) {
    order: 3;
  }

  .footer-section:nth-child(4) {
    order: 4;
  }

  .footer-logo {
    justify-content: center;
    margin-bottom: 16px;
  }

  .footer-logo .logo-text {
    font-size: 20px;
  }

  .company-desc {
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 16px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }

  .company-features {
    justify-content: center;
    margin-bottom: 20px;
  }

  .section-title {
    font-size: 16px;
    margin-bottom: 16px;
    text-align: center;
  }

  .section-title::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .footer-links {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .footer-links li {
    margin-bottom: 10px;
  }

  .footer-links a {
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
  }

  .footer-links a:hover {
    background: rgba(59, 130, 246, 0.1);
    transform: none;
  }

  .contact-item {
    margin-bottom: 12px;
    font-size: 14px;
    justify-content: center;
  }

  .qr-codes {
    gap: 20px;
    margin-top: 20px;
    justify-content: center;
  }

  .qr-placeholder {
    width: 60px;
    height: 60px;
  }

  .qr-item span {
    font-size: 12px;
  }

  .footer-bottom {
    padding: 20px 0;
    margin-top: 20px;
  }

  .footer-bottom-content {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .copyright,
  .icp-info {
    font-size: 12px;
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 12px;
  }

  .logo-link {
    font-size: 14px;
  }

  .logo-icon {
    width: 20px;
    height: 20px;
    margin-right: 4px;
  }

  .footer {
    padding: 30px 0 15px;
    min-height: auto;
  }

  .footer-main {
    padding: 30px 0 15px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 25px;
    text-align: center;
    padding: 0 15px;
  }

  .footer-section {
    margin-bottom: 0;
  }

  .company-info {
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  }

  .footer-logo .logo-text {
    font-size: 18px;
  }

  .footer-logo .logo-icon {
    font-size: 28px;
  }

  .company-desc {
    font-size: 13px;
    line-height: 1.5;
    margin-bottom: 15px;
  }

  .company-features {
    gap: 6px;
  }

  .feature-tag {
    font-size: 11px;
    padding: 3px 10px;
  }

  .section-title {
    font-size: 15px;
    margin-bottom: 15px;
  }

  .footer-links li {
    margin-bottom: 8px;
  }

  .footer-links a {
    font-size: 13px;
    padding: 6px 12px;
    border-radius: 6px;
    display: inline-block;
  }

  .contact-item {
    margin-bottom: 10px;
    font-size: 13px;
    justify-content: center;
  }

  .contact-item .el-icon {
    font-size: 16px;
  }

  .qr-codes {
    gap: 16px;
    margin-top: 16px;
    justify-content: center;
  }

  .qr-placeholder {
    width: 50px;
    height: 50px;
  }

  .qr-item span {
    font-size: 11px;
  }

  .footer-bottom {
    padding: 16px 0;
    margin-top: 15px;
  }

  .footer-bottom-content {
    padding: 0 15px;
    gap: 10px;
  }

  .copyright,
  .icp-info {
    font-size: 11px;
  }

  .mobile-menu-btn {
    width: 40px;
    height: 40px;
  }

  .mobile-menu-btn .el-icon {
    font-size: 18px;
  }
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
  display: none;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 197, 253, 0.1));
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.mobile-menu-btn:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(147, 197, 253, 0.2));
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.mobile-menu-btn .el-icon {
  font-size: 20px;
}

/* 移动端菜单 */
.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.mobile-menu.show {
  visibility: visible;
  opacity: 1;
}

.mobile-menu-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.mobile-menu-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 280px;
  height: 100%;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
  transform: translateX(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.mobile-menu.show .mobile-menu-content {
  transform: translateX(0);
}

.mobile-menu-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-logo {
  display: flex;
  align-items: center;
  color: #ffffff;
  font-weight: 700;
  font-size: 18px;
}

.mobile-logo .logo-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.mobile-menu-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mobile-menu-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.mobile-menu-items {
  flex: 1;
  padding: 20px 0;
}

.mobile-menu-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.mobile-menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-left-color: #60a5fa;
  color: #ffffff;
}

.mobile-menu-item .el-icon {
  margin-right: 12px;
  font-size: 18px;
}

.mobile-menu-actions {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-user-info {
  display: flex;
  align-items: center;
  padding: 16px 0;
  margin-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-username {
  margin-left: 12px;
  color: #ffffff;
  font-weight: 500;
}

.mobile-auth-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mobile-auth-btn {
  width: 100% !important;
  height: 44px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

@media (max-width: 768px) {
  .mobile-menu-btn {
    display: flex;
  }

  .user-actions {
    display: none;
  }

  .nav-menu {
    display: none;
  }

  .auth-buttons {
    display: none;
  }
}

@media (max-width: 480px) {
  .header {
    height: 60px;
  }

  .logo-text {
    display: none;
  }

  .mobile-menu-content {
    width: 100%;
  }
}

/* 页面过渡动画样式 */
.main-content {
  position: relative;
  overflow: hidden;
}

.page-container {
  position: relative;
  min-height: 100%;
  width: 100%;
}

.page-container.is-transitioning {
  overflow: hidden;
}

/* 页面加载指示器 */
.page-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.rotating {
  animation: rotate 1s linear infinite;
  color: #409eff;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 优化的页面过渡动画 */
.page-transition-enter-active {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  z-index: 2;
}

.page-transition-leave-active {
  transition: all 0.2s cubic-bezier(0.55, 0.06, 0.68, 0.19);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
}

.page-transition-enter-from {
  opacity: 0;
  transform: translateX(30px) scale(0.98);
}

.page-transition-leave-to {
  opacity: 0;
  transform: translateX(-20px) scale(1.02);
}

.page-transition-enter-to,
.page-transition-leave-from {
  opacity: 1;
  transform: translateX(0) scale(1);
}

/* 响应式动画优化 */
@media (prefers-reduced-motion: reduce) {

  .page-transition-enter-active,
  .page-transition-leave-active {
    transition: opacity 0.2s ease;
  }

  .page-transition-enter-from,
  .page-transition-leave-to {
    transform: none;
    opacity: 0;
  }

  .page-transition-enter-to,
  .page-transition-leave-from {
    transform: none;
    opacity: 1;
  }

  .rotating {
    animation: none;
  }
}

/* 移动端动画优化 */
@media (max-width: 768px) {
  .page-transition-enter-active {
    transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .page-transition-leave-active {
    transition: all 0.2s cubic-bezier(0.55, 0.06, 0.68, 0.19);
  }

  .page-transition-enter-from {
    opacity: 0;
    transform: translateX(20px) scale(0.99);
  }

  .page-transition-leave-to {
    opacity: 0;
    transform: translateX(-15px) scale(1.01);
  }

  .loading-spinner {
    width: 36px;
    height: 36px;
  }
}
</style>