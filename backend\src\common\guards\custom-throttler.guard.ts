import { Injectable, ExecutionContext, CanActivate, SetMetadata } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { ApiException } from '../exceptions/api.exception';

/**
 * 自定义限流守卫
 * 支持基于用户、API密钥、IP的差异化限流策略
 */
@Injectable()
export class CustomThrottlerGuard implements CanActivate {
  private readonly concurrentRequests = new Map<string, number>();
  private readonly requestCounts = new Map<string, { count: number; resetTime: number }>();

  constructor(protected readonly reflector: Reflector) {}

  /**
   * 守卫主方法
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 检查是否跳过限流
    const skipThrottle = this.reflector.get<boolean>('skip-throttle', context.getHandler());
    if (skipThrottle) {
      return true;
    }

    const request = context.switchToHttp().getRequest<Request>();

    try {
      // 检查并发限制
      await this.checkConcurrencyLimit(request);

      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 检查并发限制
   */
  private async checkConcurrencyLimit(request: Request): Promise<void> {
    const user = (request as any).user;
    const apiKey = (request as any).apiKey;

    let userKey: string;
    if (user?.id) {
      userKey = `user:${user.id}`;
    } else if (apiKey?.id) {
      userKey = `apikey:${apiKey.id}`;
    } else {
      userKey = `ip:${request.ip}`;
    }

    // 检查当前并发数
    const currentConcurrency = this.concurrentRequests.get(userKey) || 0;
    const maxConcurrency = this.getMaxConcurrency(user, apiKey);

    if (currentConcurrency >= maxConcurrency) {
      throw new ApiException(80006); // 并发请求数超过限制
    }

    // 增加并发计数
    this.concurrentRequests.set(userKey, currentConcurrency + 1);

    // 请求完成后减少计数（这里简化处理）
    setTimeout(() => {
      const count = this.concurrentRequests.get(userKey) || 0;
      if (count <= 1) {
        this.concurrentRequests.delete(userKey);
      } else {
        this.concurrentRequests.set(userKey, count - 1);
      }
    }, 30000); // 30秒后自动清理
  }

  /**
   * 根据用户类型获取最大并发数
   */
  private getMaxConcurrency(user?: any, apiKey?: any): number {
    if (user?.subscription) {
      switch (user.subscription) {
        case 'premium':
          return 100;
        case 'pro':
          return 50;
        case 'basic':
          return 30;
        default:
          return 20; // 提高默认用户并发数
      }
    }

    if (apiKey?.tier) {
      switch (apiKey.tier) {
        case 'enterprise':
          return 200;
        case 'business':
          return 100;
        case 'standard':
          return 50;
        default:
          return 30; // 提高默认API密钥并发数
      }
    }

    return 20; // 提高默认并发数
  }

  /**
   * 获取跟踪器标识
   * 优先级：用户ID > API密钥 > IP地址
   */
  protected async getTracker(req: Record<string, any>): Promise<string> {
    const user = (req as any).user;
    const apiKey = (req as any).apiKey;
    
    // 使用用户ID作为跟踪器
    if (user?.id) {
      return `user:${user.id}`;
    }
    
    // 使用API密钥作为跟踪器
    if (apiKey?.id) {
      return `apikey:${apiKey.id}`;
    }
    
    // 使用IP地址作为跟踪器
    return `ip:${req.ip}`;
  }



  /**
   * 检查是否跳过限流
   */
  protected async shouldSkip(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    
    // 检查是否有跳过限流的标记
    const skipThrottle = this.reflector.get<boolean>('skip-throttle', context.getHandler());
    if (skipThrottle) {
      return true;
    }

    // 内部服务调用跳过限流
    const isInternalCall = request.headers['x-internal-call'] === 'true';
    if (isInternalCall) {
      return true;
    }

    // 管理员用户跳过限流
    const user = (request as any).user;
    if (user?.role === 'admin' || user?.isAdmin) {
      return true;
    }

    return false;
  }
}





/**
 * 跳过限流装饰器
 */
export const SkipThrottle = () => SetMetadata('skip-throttle', true);

/**
 * 自定义限流配置装饰器
 */
export const ThrottleConfig = (limit: number, ttl: number) => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    if (propertyKey && descriptor) {
      SetMetadata('throttle-limit', limit)(target, propertyKey, descriptor);
      SetMetadata('throttle-ttl', ttl)(target, propertyKey, descriptor);
    }
  };
};
