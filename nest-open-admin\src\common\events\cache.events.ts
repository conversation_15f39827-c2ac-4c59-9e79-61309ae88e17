/**
 * 缓存相关事件定义
 */

/**
 * 计费处理完成事件
 */
export class BillingProcessedEvent {
  constructor(
    public readonly userId: string,
    public readonly serviceId: string,
    public readonly amount: number,
    public readonly type: 'deduct' | 'refund',
    public readonly timestamp: Date = new Date(),
  ) {}
}

/**
 * 缓存清理事件
 */
export class CacheClearEvent {
  constructor(
    public readonly pattern: string,
    public readonly reason: string,
    public readonly timestamp: Date = new Date(),
  ) {}
}

/**
 * 缓存更新事件
 */
export class CacheUpdateEvent {
  constructor(
    public readonly key: string,
    public readonly value: any,
    public readonly ttl?: number,
    public readonly timestamp: Date = new Date(),
  ) {}
}

/**
 * 用户更新事件
 */
export class UserUpdatedEvent {
  constructor(
    public readonly userId: string,
    public readonly userData: any,
    public readonly timestamp: Date = new Date(),
  ) {}
}