/**
 * 标准响应结果类
 */
export class Result<T = any> {
  success: boolean;
  code: number;
  message: string;
  data?: T;
  timestamp: string;
  requestId?: string;

  constructor(
    success: boolean,
    code: number,
    message: string,
    data?: T,
    requestId?: string
  ) {
    this.success = success;
    this.code = code;
    this.message = message;
    this.data = data;
    this.timestamp = new Date().toISOString();
    this.requestId = requestId;
  }

  /**
   * 创建成功响应
   */
  static success<T>(data?: T, message = '操作成功', requestId?: string): Result<T> {
    return new Result(true, 200, message, data, requestId);
  }

  /**
   * 创建失败响应
   */
  static error<T>(message = '操作失败', code = 500, data?: T, requestId?: string): Result<T> {
    return new Result(false, code, message, data, requestId);
  }

  /**
   * 创建分页成功响应
   */
  static page<T>(
    data: T[],
    total: number,
    page: number,
    limit: number,
    message = '查询成功',
    requestId?: string
  ): Result<{
    list: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const totalPages = Math.ceil(total / limit);
    return new Result(
      true,
      200,
      message,
      {
        list: data,
        total,
        page,
        limit,
        totalPages
      },
      requestId
    );
  }

  /**
   * 创建验证失败响应
   */
  static validation(message = '参数验证失败', requestId?: string): Result {
    return new Result(false, 400, message, null, requestId);
  }

  /**
   * 创建未授权响应
   */
  static unauthorized(message = '未授权访问', requestId?: string): Result {
    return new Result(false, 401, message, null, requestId);
  }

  /**
   * 创建禁止访问响应
   */
  static forbidden(message = '禁止访问', requestId?: string): Result {
    return new Result(false, 403, message, null, requestId);
  }

  /**
   * 创建资源不存在响应
   */
  static notFound(message = '资源不存在', requestId?: string): Result {
    return new Result(false, 404, message, null, requestId);
  }
}

/**
 * 分页响应数据接口
 */
export interface PageResult<T> {
  list: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * 响应状态码常量
 */
export const ResultCode = {
  SUCCESS: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
} as const;

export type ResultCodeType = typeof ResultCode[keyof typeof ResultCode];