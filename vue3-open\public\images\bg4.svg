<svg width="300" height="150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="forestGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#a8e6cf;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2d5016;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="treeGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#27ae60;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e8449;stop-opacity:1" />
    </radialGradient>
    <linearGradient id="trunkGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#8b4513;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#654321;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 森林背景 -->
  <rect width="300" height="150" fill="url(#forestGradient)"/>
  
  <!-- 远景树木 -->
  <rect x="20" y="100" width="6" height="30" fill="url(#trunkGradient)" opacity="0.7"/>
  <circle cx="23" cy="100" r="12" fill="url(#treeGradient)" opacity="0.6"/>
  
  <rect x="60" y="95" width="8" height="35" fill="url(#trunkGradient)" opacity="0.7"/>
  <circle cx="64" cy="95" r="15" fill="url(#treeGradient)" opacity="0.6"/>
  
  <rect x="100" y="105" width="5" height="25" fill="url(#trunkGradient)" opacity="0.7"/>
  <circle cx="102.5" cy="105" r="10" fill="url(#treeGradient)" opacity="0.6"/>
  
  <!-- 中景树木 -->
  <rect x="40" y="80" width="10" height="50" fill="url(#trunkGradient)"/>
  <circle cx="45" cy="80" r="18" fill="url(#treeGradient)"/>
  <circle cx="38" cy="75" r="12" fill="url(#treeGradient)" opacity="0.8"/>
  <circle cx="52" cy="75" r="14" fill="url(#treeGradient)" opacity="0.8"/>
  
  <rect x="120" y="70" width="12" height="60" fill="url(#trunkGradient)"/>
  <circle cx="126" cy="70" r="22" fill="url(#treeGradient)"/>
  <circle cx="115" cy="65" r="15" fill="url(#treeGradient)" opacity="0.8"/>
  <circle cx="137" cy="65" r="17" fill="url(#treeGradient)" opacity="0.8"/>
  
  <rect x="180" y="85" width="8" height="45" fill="url(#trunkGradient)"/>
  <circle cx="184" cy="85" r="16" fill="url(#treeGradient)"/>
  <circle cx="175" cy="80" r="11" fill="url(#treeGradient)" opacity="0.8"/>
  <circle cx="193" cy="80" r="13" fill="url(#treeGradient)" opacity="0.8"/>
  
  <!-- 近景树木 -->
  <rect x="10" y="60" width="15" height="70" fill="url(#trunkGradient)"/>
  <circle cx="17.5" cy="60" r="25" fill="url(#treeGradient)"/>
  <circle cx="5" cy="55" r="18" fill="url(#treeGradient)" opacity="0.9"/>
  <circle cx="30" cy="55" r="20" fill="url(#treeGradient)" opacity="0.9"/>
  <circle cx="17.5" cy="40" r="15" fill="url(#treeGradient)" opacity="0.8"/>
  
  <rect x="220" y="50" width="18" height="80" fill="url(#trunkGradient)"/>
  <circle cx="229" cy="50" r="30" fill="url(#treeGradient)"/>
  <circle cx="210" cy="45" r="22" fill="url(#treeGradient)" opacity="0.9"/>
  <circle cx="248" cy="45" r="25" fill="url(#treeGradient)" opacity="0.9"/>
  <circle cx="229" cy="25" r="18" fill="url(#treeGradient)" opacity="0.8"/>
  
  <!-- 灌木丛 -->
  <ellipse cx="80" cy="120" rx="15" ry="8" fill="#2ecc71" opacity="0.8"/>
  <ellipse cx="95" cy="125" rx="12" ry="6" fill="#2ecc71" opacity="0.7"/>
  <ellipse cx="160" cy="115" rx="18" ry="10" fill="#2ecc71" opacity="0.8"/>
  <ellipse cx="200" cy="125" rx="14" ry="7" fill="#2ecc71" opacity="0.7"/>
  
  <!-- 阳光透过树叶 -->
  <polygon points="70,20 75,10 80,20 75,30" fill="#f1c40f" opacity="0.6"/>
  <circle cx="150" cy="25" r="8" fill="#f39c12" opacity="0.5"/>
  <polygon points="250,15 255,5 260,15 255,25" fill="#f1c40f" opacity="0.6"/>
  
  <!-- 森林地面装饰 -->
  <ellipse cx="50" cy="135" rx="8" ry="3" fill="#8b4513" opacity="0.6"/>
  <ellipse cx="130" cy="140" rx="6" ry="2" fill="#8b4513" opacity="0.5"/>
  <ellipse cx="190" cy="138" rx="10" ry="4" fill="#8b4513" opacity="0.6"/>
  
  <!-- 蘑菇 -->
  <rect x="85" y="128" width="3" height="8" fill="#f4f4f4"/>
  <ellipse cx="86.5" cy="128" rx="5" ry="3" fill="#e74c3c"/>
  <circle cx="84" cy="127" r="1" fill="white" opacity="0.8"/>
  <circle cx="89" cy="127" r="0.8" fill="white" opacity="0.8"/>
  
  <rect x="170" y="130" width="2.5" height="6" fill="#f4f4f4"/>
  <ellipse cx="171.25" cy="130" rx="4" ry="2.5" fill="#e67e22"/>
  <circle cx="169" cy="129" r="0.8" fill="white" opacity="0.8"/>
  
  <!-- 花朵 -->
  <circle cx="110" cy="130" r="2" fill="#e91e63"/>
  <circle cx="108" cy="128" r="1.5" fill="#f8bbd9" opacity="0.8"/>
  <circle cx="112" cy="128" r="1.5" fill="#f8bbd9" opacity="0.8"/>
  <circle cx="110" cy="126" r="1.5" fill="#f8bbd9" opacity="0.8"/>
  <circle cx="110" cy="132" r="1.5" fill="#f8bbd9" opacity="0.8"/>
</svg>