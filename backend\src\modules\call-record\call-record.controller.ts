import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  ParseUUIDPipe,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CallRecordService } from './call-record.service';
import {
  CreateCallRecordDto,
  CallRecordQueryDto,
  CallRecordResponseDto,
  CallRecordListResponseDto,
  CallStatisticsQueryDto,
  CallStatisticsResponseDto,
} from './dto';
import { ApiResult } from '@/common/decorators/api-result.decorator';
import { Roles } from '@/common/decorators/roles.decorator';

@ApiTags('调用记录')
@Controller('call-records')
@ApiBearerAuth()
export class CallRecordController {
  constructor(private readonly callRecordService: CallRecordService) {}

  @Post()
  @ApiOperation({ summary: '创建调用记录' })
  @ApiResult({
    status: HttpStatus.CREATED,
    description: '调用记录创建成功',
    dataSchema: { $ref: '#/components/schemas/CallRecordResponseDto' }
  })
  create(@Body() createCallRecordDto: CreateCallRecordDto): Promise<CallRecordResponseDto> {
    return this.callRecordService.create(createCallRecordDto);
  }

  @Get()
  @Roles('admin')
  @ApiOperation({ summary: '查询调用记录列表' })
  @ApiResult({
    description: '调用记录列表查询成功',
    dataSchema: { $ref: '#/components/schemas/CallRecordListResponseDto' }
  })
  findAll(@Query() queryDto: CallRecordQueryDto): Promise<CallRecordListResponseDto> {
    return this.callRecordService.findAll(queryDto);
  }

  @Get(':id')
  @Roles('admin')
  @ApiOperation({ summary: '根据ID查询调用记录' })
  @ApiParam({ name: 'id', description: '调用记录ID' })
  @ApiResult({
    description: '调用记录查询成功',
    dataSchema: { $ref: '#/components/schemas/CallRecordResponseDto' }
  })
  findOne(@Param('id', ParseUUIDPipe) id: string): Promise<CallRecordResponseDto> {
    return this.callRecordService.findOne(id);
  }

  @Get('user/:userId/service/:serviceId')
  @ApiOperation({ summary: '查询用户服务调用记录' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiParam({ name: 'serviceId', description: '服务ID' })
  @ApiResult({
    description: '用户服务调用记录查询成功',
    dataSchema: { $ref: '#/components/schemas/CallRecordListResponseDto' }
  })
  getUserServiceCallRecords(
    @Param('userId') userId: number,
    @Param('serviceId') serviceId: number,
    @Query() queryDto: CallRecordQueryDto,
  ): Promise<CallRecordListResponseDto> {
    return this.callRecordService.getUserServiceCallRecords(userId, serviceId, queryDto);
  }

  @Get('statistics')
  @ApiOperation({ summary: '获取调用统计数据' })
  @ApiResult({
    description: '调用统计数据查询成功',
    dataSchema: { $ref: '#/components/schemas/CallStatisticsResponseDto' }
  })
  getCallStatistics(@Query() queryDto: CallStatisticsQueryDto): Promise<CallStatisticsResponseDto> {
    return this.callRecordService.getCallStatistics(queryDto);
  }
}
