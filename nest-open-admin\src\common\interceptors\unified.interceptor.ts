import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON>ler,
  Logger,
  HttpException,
  HttpStatus,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { Request, Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '../../shared/redis.service';
import { ResponseBuilder, StandardResponse } from '../utils/response-builder';
import { ErrorHandlerService } from '../../shared/error-handler.service';
import { AuthUser, AuthApiKey, RequestLogData } from '../types/auth.types';
import { RequestUtils } from '../utils/request.utils';
import { SKIP_RESPONSE_FORMAT_KEY } from '../decorators/public.decorator';

/**
 * 统一拦截器
 * 响应格式化功能
 */
@Injectable()
export class UnifiedInterceptor implements NestInterceptor {
  protected readonly logger = new Logger(UnifiedInterceptor.name);

  constructor(
    protected readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly errorHandlerService: ErrorHandlerService,
    private readonly reflector: Reflector,
  ) {
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    const startTime = Date.now();
    const requestId = this.generateRequestId();

    // 添加请求ID到响应头
    response.setHeader('X-Request-ID', requestId);

    // 检查是否跳过响应格式化
    const skipResponseFormat = this.reflector.get<boolean>(
      SKIP_RESPONSE_FORMAT_KEY,
      context.getHandler(),
    );

    // 记录请求开始
    this.logRequestStart(request, requestId);

    // 检查是否为SSE请求（URL包含/stream）
    if (request.url.includes('/stream')) {
      this.logger.debug(`检测到SSE请求: ${request.url}，跳过响应拦截`);
      // 对于SSE请求，不进行响应格式化，直接返回处理结果
      return next.handle().pipe(
        catchError((error) => {
          const endTime = Date.now();
          const duration = endTime - startTime;
          // 仅记录错误，不格式化响应
          this.logRequestError(request, requestId, duration, error);
          return throwError(() => error);
        })
      );
    }

    return next.handle().pipe(
      // 处理成功响应
      map((data) => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        // 记录请求成功
        this.logRequestSuccess(request, requestId, duration, data);

        // 如果标记为跳过响应格式化，直接返回原始数据
        if (skipResponseFormat) {
          this.logger.debug(`跳过响应格式化: ${request.url}`);
          return data;
        }

        // 格式化响应
        return this.formatResponse(data, requestId);
      }),

      // 处理错误响应
      catchError((error) => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        // 记录请求错误
        this.logRequestError(request, requestId, duration, error);

        // 格式化错误响应
        const formattedError = this.formatErrorResponse(error, requestId);
        return throwError(() => formattedError);
      }),

      // 记录性能指标
      tap(() => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        this.recordPerformanceMetrics(request, duration);
      })
    );
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return RequestUtils.generateRequestId();
  }

  /**
   * 记录请求开始
   */
  private logRequestStart(request: Request, requestId: string): void {
    const user = request['user'] as AuthUser;
    const authType = request['authType'] as string;
    const apiKey = request['apiKey'] as AuthApiKey;

    const logData: RequestLogData = {
      requestId,
      method: request.method,
      url: request.url,
      userAgent: request.headers['user-agent'],
      ip: this.getClientIp(request),
      authType,
      userId: user?.id,
      apiKeyId: apiKey?.id,
      timestamp: new Date().toISOString(),
    };

    this.logger.log(`请求开始: ${JSON.stringify(logData)}`);

    // 异步记录到Redis（用于监控和统计）
    this.recordRequestToRedis(logData);
  }

  /**
   * 记录请求成功
   */
  private logRequestSuccess(
    request: Request,
    requestId: string,
    duration: number,
    data: any
  ): void {
    const user = request['user'] as AuthUser;
    const authType = request['authType'] as string;

    const logData: RequestLogData = {
      requestId,
      method: request.method,
      url: request.url,
      duration,
      status: 'success',
      authType,
      userId: user?.id,
      responseSize: JSON.stringify(data).length,
      timestamp: new Date().toISOString(),
      ip: this.getClientIp(request),
    };

    this.logger.log(`请求成功: ${JSON.stringify(logData)}`);
  }

  /**
   * 记录请求错误
   */
  private logRequestError(
    request: Request,
    requestId: string,
    duration: number,
    error: any
  ): void {
    const user = request['user'] as AuthUser;
    const authType = request['authType'] as string;

    const logData: RequestLogData = {
      requestId,
      method: request.method,
      url: request.url,
      duration,
      status: 'error',
      authType,
      userId: user?.id,
      timestamp: new Date().toISOString(),
      ip: this.getClientIp(request),
    };
    // 添加错误信息到日志数据（不在接口中定义，避免类型污染）
    const errorLogData = {
      ...logData,
      errorMessage: error.message,
      errorStack: error.stack,
    };

    this.logger.error(`请求错误: ${JSON.stringify(errorLogData)}`);
  }

  /**
   * 记录请求到Redis
   */
  private async recordRequestToRedis(logData: any): Promise<void> {
    try {
      // 异步记录，不阻塞请求
      setImmediate(async () => {
        try {
          // 记录到请求日志队列
          await this.redisService.lpush('request_logs', JSON.stringify(logData));
          // 限制队列长度
          await this.redisService.ltrim('request_logs', 0, 49999);
          // 更新统计信息
          const statsKey = `api_stats:${this.getDateKey()}`;
          await this.redisService.hincrby(statsKey, 'total_requests', 1);
          await this.redisService.expire(statsKey, 86400 * 7); // 保留7天
          if (logData.authType === 'api-key') {
            await this.redisService.hincrby(statsKey, 'api_key_requests', 1);
          } else if (logData.authType === 'jwt') {
            await this.redisService.hincrby(statsKey, 'jwt_requests', 1);
          }
        } catch (error) {
          this.logger.error('记录请求到Redis失败:', error);
        }
      });
    } catch (error) {
      this.logger.error('异步记录请求失败:', error);
    }
  }

  /**
   * 记录性能指标
   */
  private async recordPerformanceMetrics(
    request: Request,
    duration: number
  ): Promise<void> {
    try {
      setImmediate(async () => {
        try {
          const metricsKey = `performance_metrics:${this.getDateKey()}`;
          // 记录响应时间
          await this.redisService.lpush(
            `${metricsKey}:response_times`,
            duration.toString()
          );
          // 限制列表长度
          await this.redisService.ltrim(`${metricsKey}:response_times`, 0, 9999);
          // 更新平均响应时间
          await this.redisService.hincrby(metricsKey, 'total_duration', duration);
          await this.redisService.hincrby(metricsKey, 'request_count', 1);
          await this.redisService.expire(metricsKey, 86400 * 7);

          // 记录慢查询（超过1秒）
          if (duration > 1000) {
            const slowQueryData = {
              method: request.method,
              url: request.url,
              duration,
              timestamp: new Date().toISOString(),
            };
            await this.redisService.lpush(
              'slow_queries',
              JSON.stringify(slowQueryData)
            );
            await this.redisService.ltrim('slow_queries', 0, 999);
          }
        } catch (error) {
          this.logger.error('记录性能指标失败:', error);
        }
      });
    } catch (error) {
      this.logger.error('异步记录性能指标失败:', error);
    }
  }

  /**
   * 格式化成功响应
   */
  private formatResponse(data: any, requestId: string): StandardResponse<any> {
    return ResponseBuilder.success(data, '操作成功', {
      requestId,
    });
  }

  /**
   * 格式化错误响应
   */
  private formatErrorResponse(error: any, requestId: string): HttpException {
    const errorResponse = this.errorHandlerService.handleUnknownError(error);
    
    // 添加请求ID到错误响应
    if (errorResponse.getResponse && typeof errorResponse.getResponse === 'object') {
      const response = errorResponse.getResponse() as any;
      response.requestId = requestId;
      response.timestamp = new Date().toISOString();
    }

    return errorResponse;
  }

  /**
   * 获取客户端IP地址
   */
  private getClientIp(request: Request): string {
    return RequestUtils.getClientIp(request);
  }

  /**
   * 获取日期键（用于Redis键名）
   */
  private getDateKey(): string {
    return RequestUtils.getDateKey();
  }
}
