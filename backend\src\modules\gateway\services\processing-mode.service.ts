import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { 
  IProcessingModeContext, 
  IClientLimits 
} from '../interfaces/gateway.interfaces';
import { 
  ProcessingMode, 
  GATEWAY_CONFIG 
} from '../config/gateway.constants';

/**
 * 处理模式决策服务
 * 负责根据请求上下文智能决策最佳的处理模式
 * 遵循单一职责原则，专注于处理模式决策逻辑
 */
@Injectable()
export class ProcessingModeService {
  private readonly logger = new Logger(ProcessingModeService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * 估算处理时间
   * 基于请求特征、历史数据和系统负载进行智能估算
   */
  estimateProcessingTime(context: IProcessingModeContext): number {
    const { request, requestSize, routeConfig } = context;

    // 基础处理时间（根据服务类型）
    const baseTime = this.getBaseProcessingTime(routeConfig.prefix);

    // 大小因子
    const sizeFactor = this.calculateSizeFactor(requestSize, routeConfig.prefix);

    // 复杂度因子
    const complexityFactor = this.calculateComplexityFactor(request);

    // 系统负载因子
    const loadFactor = this.calculateLoadFactor(context.systemLoad || 0.5);

    // 历史数据因子
    const historyFactor = this.getHistoryFactor(routeConfig.prefix);

    // 计算最终估算时间
    const estimatedTime = baseTime * sizeFactor * complexityFactor * loadFactor * historyFactor;

    // 应用最小和最大限制
    const minTime = 100; // 最小100ms
    const maxTime = 300000; // 最大5分钟

    const finalTime = Math.max(minTime, Math.min(estimatedTime, maxTime));

    this.logger.debug(`处理时间估算: 基础=${baseTime}ms, 大小因子=${sizeFactor}, 复杂度因子=${complexityFactor}, 负载因子=${loadFactor}, 历史因子=${historyFactor}, 最终=${finalTime}ms`);

    return Math.round(finalTime);
  }

  /**
   * 确定处理模式
   */
  determineMode(context: IProcessingModeContext): ProcessingMode {
    this.logger.debug(`开始处理模式决策，请求大小: ${context.requestSize}字节`);

    // 1. 检查用户指定的模式
    if (context.request.mode) {
      const userMode = this.validateUserMode(context);
      if (userMode) {
        this.logger.debug(`使用用户指定模式: ${userMode}`);
        return userMode;
      }
    }

    // 2. 基于请求特征自动决策
    const autoMode = this.autoDecideMode(context);
    this.logger.debug(`自动决策模式: ${autoMode}`);
    
    return autoMode;
  }

  /**
   * 验证用户指定的模式是否可用
   */
  private validateUserMode(context: IProcessingModeContext): ProcessingMode | null {
    const userMode = context.request.mode!;
    const routeConfig = context.routeConfig;

    // 检查路由是否支持该模式
    switch (userMode) {
      case ProcessingMode.SYNC:
        return ProcessingMode.SYNC; // 同步模式总是支持的
        
      case ProcessingMode.ASYNC:
        if (routeConfig.allowAsync) {
          return ProcessingMode.ASYNC;
        }
        this.logger.warn(`路由 ${routeConfig.prefix} 不支持异步模式`);
        break;
        
      case ProcessingMode.PROXY_ASYNC:
        if (routeConfig.allowProxyAsync) {
          // 检查客户端限制
          if (this.checkClientLimits(context)) {
            return ProcessingMode.PROXY_ASYNC;
          }
          this.logger.warn('客户端代理异步请求数量超过限制');
        } else {
          this.logger.warn(`路由 ${routeConfig.prefix} 不支持代理异步模式`);
        }
        break;
    }

    return null;
  }

  /**
   * 自动决策处理模式
   */
  private autoDecideMode(context: IProcessingModeContext): ProcessingMode {
    const { requestSize, estimatedProcessingTime, routeConfig } = context;

    // 决策因子
    const factors = this.calculateDecisionFactors(context);
    
    this.logger.debug(`决策因子: ${JSON.stringify(factors)}`);

    // 决策逻辑
    
    // 1. 如果请求很大，优先使用异步模式
    if (requestSize > GATEWAY_CONFIG.ASYNC_SIZE_THRESHOLD) {
      if (routeConfig.allowAsync) {
        return ProcessingMode.ASYNC;
      }
    }

    // 2. 如果预估处理时间很长，使用代理异步模式
    if (estimatedProcessingTime > (routeConfig.maxProxyWaitTime || GATEWAY_CONFIG.MAX_PROXY_WAIT_TIME)) {
      if (routeConfig.allowAsync) {
        return ProcessingMode.ASYNC;
      }
    }

    // 3. 如果处理时间适中且支持代理异步，使用代理异步模式
    if (estimatedProcessingTime > 5000 && estimatedProcessingTime <= (routeConfig.maxProxyWaitTime || 60000)) {
      if (routeConfig.allowProxyAsync && this.checkClientLimits(context)) {
        return ProcessingMode.PROXY_ASYNC;
      }
    }

    // 4. 默认使用路由配置的默认模式
    if (this.isModeAvailable(routeConfig.defaultMode, context)) {
      return routeConfig.defaultMode;
    }

    // 5. 最后回退到同步模式
    return ProcessingMode.SYNC;
  }

  /**
   * 计算决策因子
   */
  private calculateDecisionFactors(context: IProcessingModeContext): {
    sizeScore: number;
    timeScore: number;
    complexityScore: number;
    loadScore: number;
  } {
    const { requestSize, estimatedProcessingTime, request } = context;

    // 大小评分 (0-1)
    const sizeScore = Math.min(requestSize / GATEWAY_CONFIG.ASYNC_SIZE_THRESHOLD, 1);

    // 时间评分 (0-1)
    const timeScore = Math.min(estimatedProcessingTime / 30000, 1); // 30秒为满分

    // 复杂度评分 (0-1)
    let complexityScore = 0.1; // 基础分
    if (request.file || request.body?.imageBase64) {
      complexityScore += 0.4; // 文件处理
    }
    if (request.body?.text && request.body.text.length > 1000) {
      complexityScore += 0.3; // 长文本处理
    }
    if (request.body?.items && Array.isArray(request.body.items)) {
      complexityScore += 0.2 * Math.min(request.body.items.length / 10, 1); // 批量处理
    }

    // 系统负载评分 (0-1)
    const loadScore = context.systemLoad || 0.5; // 默认中等负载

    return {
      sizeScore: Math.min(sizeScore, 1),
      timeScore: Math.min(timeScore, 1),
      complexityScore: Math.min(complexityScore, 1),
      loadScore: Math.min(loadScore, 1),
    };
  }

  /**
   * 检查客户端限制
   */
  private checkClientLimits(context: IProcessingModeContext): boolean {
    const clientLimits = context.clientLimits;
    
    if (!clientLimits) {
      return true; // 没有限制信息，默认允许
    }

    // 检查并发请求数限制
    if (clientLimits.currentConcurrentRequests >= clientLimits.maxConcurrentRequests) {
      this.logger.warn(`客户端并发请求数超限: ${clientLimits.currentConcurrentRequests}/${clientLimits.maxConcurrentRequests}`);
      return false;
    }

    // 检查是否允许代理异步
    if (!clientLimits.allowProxyAsync) {
      this.logger.warn('客户端不允许使用代理异步模式');
      return false;
    }

    return true;
  }

  /**
   * 检查模式是否可用
   */
  private isModeAvailable(mode: ProcessingMode, context: IProcessingModeContext): boolean {
    const routeConfig = context.routeConfig;

    switch (mode) {
      case ProcessingMode.SYNC:
        return true;
        
      case ProcessingMode.ASYNC:
        return routeConfig.allowAsync;
        
      case ProcessingMode.PROXY_ASYNC:
        return routeConfig.allowProxyAsync && this.checkClientLimits(context);
        
      default:
        return false;
    }
  }

  /**
   * 获取模式推荐理由
   */
  getRecommendationReason(context: IProcessingModeContext, selectedMode: ProcessingMode): string {
    const { requestSize, estimatedProcessingTime } = context;
    const factors = this.calculateDecisionFactors(context);

    const reasons: string[] = [];

    if (selectedMode === ProcessingMode.ASYNC) {
      if (requestSize > GATEWAY_CONFIG.ASYNC_SIZE_THRESHOLD) {
        reasons.push('请求数据量较大');
      }
      if (estimatedProcessingTime > 30000) {
        reasons.push('预估处理时间较长');
      }
      if (factors.complexityScore > 0.7) {
        reasons.push('请求复杂度较高');
      }
    }

    if (selectedMode === ProcessingMode.PROXY_ASYNC) {
      if (estimatedProcessingTime > 5000 && estimatedProcessingTime <= 60000) {
        reasons.push('处理时间适中，适合代理等待');
      }
      if (factors.loadScore < 0.7) {
        reasons.push('系统负载较低');
      }
    }

    if (selectedMode === ProcessingMode.SYNC) {
      if (estimatedProcessingTime <= 5000) {
        reasons.push('预估处理时间较短');
      }
      if (requestSize <= GATEWAY_CONFIG.ASYNC_SIZE_THRESHOLD / 4) {
        reasons.push('请求数据量较小');
      }
    }

    return reasons.length > 0 ? reasons.join(', ') : '基于默认配置';
  }

  /**
   * 获取模式性能预测
   */
  getPerfomancePrediction(context: IProcessingModeContext, mode: ProcessingMode): {
    estimatedResponseTime: number;
    estimatedTotalTime: number;
    resourceUsage: 'low' | 'medium' | 'high';
    userExperience: 'excellent' | 'good' | 'fair' | 'poor';
  } {
    const { estimatedProcessingTime } = context;

    let estimatedResponseTime: number;
    let estimatedTotalTime: number;
    let resourceUsage: 'low' | 'medium' | 'high';
    let userExperience: 'excellent' | 'good' | 'fair' | 'poor';

    switch (mode) {
      case ProcessingMode.SYNC:
        estimatedResponseTime = estimatedProcessingTime + 200; // 网络延迟
        estimatedTotalTime = estimatedResponseTime;
        resourceUsage = estimatedProcessingTime > 10000 ? 'high' : 'medium';
        userExperience = estimatedProcessingTime <= 3000 ? 'excellent' : 
                        estimatedProcessingTime <= 10000 ? 'good' : 'poor';
        break;

      case ProcessingMode.ASYNC:
        estimatedResponseTime = 500; // 快速响应
        estimatedTotalTime = estimatedProcessingTime + 1000; // 队列延迟
        resourceUsage = 'low';
        userExperience = 'good';
        break;

      case ProcessingMode.PROXY_ASYNC:
        estimatedResponseTime = estimatedProcessingTime + 500; // 代理等待
        estimatedTotalTime = estimatedResponseTime;
        resourceUsage = 'medium';
        userExperience = estimatedProcessingTime <= 30000 ? 'good' : 'fair';
        break;

      default:
        estimatedResponseTime = estimatedProcessingTime;
        estimatedTotalTime = estimatedProcessingTime;
        resourceUsage = 'medium';
        userExperience = 'fair';
    }

    return {
      estimatedResponseTime,
      estimatedTotalTime,
      resourceUsage,
      userExperience,
    };
  }

  /**
   * 获取模式统计信息
   */
  getModeStats(): Record<ProcessingMode, number> {
    // 这里可以添加实际的统计逻辑
    // 暂时返回模拟数据
    return {
      [ProcessingMode.SYNC]: 0,
      [ProcessingMode.ASYNC]: 0,
      [ProcessingMode.PROXY_ASYNC]: 0,
    };
  }

  /**
   * 获取基础处理时间
   */
  private getBaseProcessingTime(serviceType: string): number {
    const baseTimes = {
      'ocr': 8000,        // OCR基础8秒
      'address': 3000,    // 地址提取基础3秒
      'geo': 2000,        // 地理坐标基础2秒
      'default': 5000,    // 默认5秒
    };

    return baseTimes[serviceType] || baseTimes.default;
  }

  /**
   * 计算大小因子
   */
  private calculateSizeFactor(requestSize: number, serviceType: string): number {
    // 不同服务类型的大小敏感度不同
    const sizeThresholds = {
      'ocr': 1024 * 1024,      // 1MB
      'address': 10 * 1024,    // 10KB
      'geo': 5 * 1024,         // 5KB
      'default': 100 * 1024,   // 100KB
    };

    const threshold = sizeThresholds[serviceType] || sizeThresholds.default;

    if (requestSize <= threshold) {
      return 1.0; // 标准大小
    }

    // 超过阈值时，按比例增加处理时间
    const ratio = requestSize / threshold;
    return Math.min(1 + Math.log(ratio) * 0.5, 5.0); // 最多增加5倍
  }

  /**
   * 计算复杂度因子
   */
  private calculateComplexityFactor(request: any): number {
    let factor = 1.0;

    // 文件处理复杂度
    if (request.file) {
      const fileSize = request.file.size || 0;
      if (fileSize > 5 * 1024 * 1024) { // 5MB以上
        factor *= 2.0;
      } else if (fileSize > 1024 * 1024) { // 1MB以上
        factor *= 1.5;
      }

      // 文件类型复杂度
      const mimeType = request.file.mimetype || '';
      if (mimeType.includes('pdf')) {
        factor *= 1.8; // PDF处理更复杂
      } else if (mimeType.includes('image')) {
        factor *= 1.2; // 图片处理中等复杂
      }
    }

    // Base64图片处理
    if (request.body?.imageBase64) {
      const base64Length = request.body.imageBase64.length;
      if (base64Length > 1024 * 1024) { // 大图片
        factor *= 1.6;
      } else if (base64Length > 100 * 1024) { // 中等图片
        factor *= 1.3;
      }
    }

    // 批量处理复杂度
    if (request.body?.items && Array.isArray(request.body.items)) {
      const itemCount = request.body.items.length;
      if (itemCount > 100) {
        factor *= 3.0;
      } else if (itemCount > 10) {
        factor *= 1 + (itemCount - 10) * 0.1;
      }
    }

    // 文本长度复杂度
    if (request.body?.text) {
      const textLength = request.body.text.length;
      if (textLength > 10000) {
        factor *= 1.5;
      } else if (textLength > 1000) {
        factor *= 1.2;
      }
    }

    return Math.min(factor, 10.0); // 最多增加10倍
  }

  /**
   * 计算负载因子
   */
  private calculateLoadFactor(systemLoad: number): number {
    // 系统负载越高，处理时间越长
    if (systemLoad >= 0.9) {
      return 3.0; // 高负载时增加3倍时间
    } else if (systemLoad >= 0.7) {
      return 2.0; // 中高负载时增加2倍时间
    } else if (systemLoad >= 0.5) {
      return 1.5; // 中等负载时增加1.5倍时间
    } else {
      return 1.0; // 低负载时正常时间
    }
  }

  /**
   * 获取历史数据因子
   */
  private getHistoryFactor(serviceType: string): number {
    // 这里可以基于历史数据调整估算
    // 暂时返回固定值，实际应该从数据库或缓存中获取
    const historyFactors = {
      'ocr': 1.1,      // OCR通常比预期慢10%
      'address': 0.9,  // 地址提取通常比预期快10%
      'geo': 0.8,      // 地理坐标通常比预期快20%
      'default': 1.0,
    };

    return historyFactors[serviceType] || historyFactors.default;
  }

  /**
   * 更新决策策略
   */
  updateDecisionStrategy(strategy: {
    asyncSizeThreshold?: number;
    maxProxyWaitTime?: number;
    complexityWeights?: Record<string, number>;
  }): void {
    this.logger.log('更新处理模式决策策略');
    // 这里可以实现动态策略更新逻辑
  }
}
