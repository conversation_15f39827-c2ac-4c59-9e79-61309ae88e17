import { Injectable, Logger } from '@nestjs/common';
import { Request } from 'express';

/**
 * API请求分类接口
 */
export interface ApiCategory {
  category: string;
  serviceCode?: string;
  priority: number;
  description: string;
}

/**
 * API请求分类器服务
 * 负责对不同类型的API请求进行分类
 */
@Injectable()
export class ApiCategorizerService {
  private readonly logger = new Logger(ApiCategorizerService.name);
  
  // API分类规则定义
  private readonly categoryRules: Array<{
    pattern: RegExp;
    category: ApiCategory;
  }> = [
    // OCR相关API
    {
      pattern: /^\/api\/ocr\/(upload|recognize)/,
      category: {
        category: 'ocr',
        serviceCode: 'ocr',
        priority: 10,
        description: 'OCR识别服务'
      }
    },
    // 地址提取API
    {
      pattern: /^\/api\/address\/(extract|extract-batch)/,
      category: {
        category: 'address',
        serviceCode: 'address',
        priority: 20,
        description: '地址提取服务'
      }
    },
    // 地理编码API
    {
      pattern: /^\/api\/geo\/(reverse|reverse-batch)/,
      category: {
        category: 'geo',
        serviceCode: 'geo',
        priority: 30,
        description: '地理编码服务'
      }
    },
    // 任务管理API
    {
      pattern: /^\/api\/tasks/,
      category: {
        category: 'task',
        priority: 40,
        description: '任务管理'
      }
    },
    // 健康检查API
    {
      pattern: /^\/api\/health/,
      category: {
        category: 'system',
        priority: 50,
        description: '系统健康检查'
      }
    },
    // 统计API
    {
      pattern: /^\/api\/stats/,
      category: {
        category: 'system',
        priority: 60,
        description: '系统统计'
      }
    },
    // 认证API
    {
      pattern: /^\/api\/auth/,
      category: {
        category: 'auth',
        priority: 5,
        description: '认证服务'
      }
    },
    // 用户API
    {
      pattern: /^\/api\/users/,
      category: {
        category: 'user',
        priority: 15,
        description: '用户服务'
      }
    },
    // 默认分类
    {
      pattern: /.*/,
      category: {
        category: 'other',
        priority: 100,
        description: '其他API'
      }
    }
  ];

  /**
   * 根据请求路径获取API分类
   * @param request 请求对象
   * @returns API分类信息
   */
  categorizeRequest(request: Request): ApiCategory {
    const path = request.path;
    
    // 查找匹配的分类规则
    for (const rule of this.categoryRules) {
      if (rule.pattern.test(path)) {
        this.logger.debug(`API分类: ${path} => ${rule.category.category}`);
        return rule.category;
      }
    }
    
    // 默认返回其他分类
    return {
      category: 'other',
      priority: 100,
      description: '其他API'
    };
  }

  /**
   * 获取所有API分类
   * @returns API分类列表
   */
  getAllCategories(): ApiCategory[] {
    return this.categoryRules
      .map(rule => rule.category)
      .filter((category, index, self) => 
        index === self.findIndex(c => c.category === category.category)
      )
      .sort((a, b) => a.priority - b.priority);
  }

  /**
   * 根据分类名称获取分类信息
   * @param categoryName 分类名称
   * @returns API分类信息
   */
  getCategoryByName(categoryName: string): ApiCategory | undefined {
    return this.categoryRules
      .map(rule => rule.category)
      .find(category => category.category === categoryName);
  }
} 