import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsObject, IsPhoneNumber } from 'class-validator';
import { CodeType } from './send-email-code.dto';

export class SendSmsCodeDto {
  @IsPhoneNumber('CN', { message: '手机号格式不正确' })
  @IsNotEmpty({ message: '手机号不能为空' })
  @ApiProperty({ description: '手机号' })
  phone: string;
  
  @IsEnum(CodeType, { message: '验证码类型无效' })
  @ApiProperty({ description: '验证码类型', enum: CodeType })
  type: CodeType;
  
  @IsObject()
  @ApiProperty({ description: '安全验证数据' })
  securityVerification: {
    level: number;
    behaviorPattern: any;
    verificationTimestamp: number;
  };
} 