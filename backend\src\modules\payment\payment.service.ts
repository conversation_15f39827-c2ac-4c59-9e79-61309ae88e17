import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, Between } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { PaymentEntity } from './entities/payment.entity';
import { OrderService } from '../order/order.service';
import { UserService } from '../user/user.service';
import { AlipayService } from './services/alipay.service';
import { WechatPayService } from './services/wechat-pay.service';
import { BalancePayService } from './services/balance-pay.service';
import {
  CreatePaymentDto,
  PaymentCallbackDto,
  QueryPaymentDto,
  UpdatePaymentDto,
  PaymentResponseDto,
  PaymentListResponseDto,
  PaymentResultDto,
} from './dto/payment.dto';
import { PaymentMethod, PaymentStatus, OrderStatus } from '../order/enums/order.enum';
import { IPaymentProvider } from './interfaces/payment-provider.interface';

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);
  private readonly paymentProviders = new Map<PaymentMethod, IPaymentProvider>();

  constructor(
    @InjectRepository(PaymentEntity)
    private readonly paymentRepository: Repository<PaymentEntity>,
    @Inject(forwardRef(() => OrderService))
    private readonly orderService: OrderService,
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    private readonly alipayService: AlipayService,
    private readonly wechatPayService: WechatPayService,
    private readonly balancePayService: BalancePayService,
    private readonly dataSource: DataSource,
    private readonly eventEmitter: EventEmitter2,
  ) {
    // 注册支付提供商
    this.paymentProviders.set(PaymentMethod.ALIPAY, this.alipayService);
    this.paymentProviders.set(PaymentMethod.WECHAT, this.wechatPayService);
    this.paymentProviders.set(PaymentMethod.BALANCE, this.balancePayService);
  }

  /**
   * 创建支付
   */
  async createPayment(createPaymentDto: CreatePaymentDto): Promise<PaymentResultDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 获取订单信息
      const order = await this.orderService.findById(createPaymentDto.orderId);
      if (!order) {
        throw new NotFoundException('订单不存在');
      }

      // 检查订单状态
      if (!order.canPay) {
        throw new BadRequestException('订单状态不允许支付');
      }

      // 验证支付金额
      if (createPaymentDto.amount !== order.totalAmount) {
        throw new BadRequestException('支付金额与订单金额不符');
      }

      // 创建支付记录
      const payment = queryRunner.manager.create(PaymentEntity, {
        orderId: createPaymentDto.orderId,
        userId: order.userId,
        paymentMethod: createPaymentDto.paymentMethod,
        amount: createPaymentDto.amount,
        status: PaymentStatus.PENDING,
      });

      const savedPayment = await queryRunner.manager.save(payment);

      // 获取支付提供商
      const provider = this.paymentProviders.get(createPaymentDto.paymentMethod);
      if (!provider) {
        throw new BadRequestException(`不支持的支付方式: ${createPaymentDto.paymentMethod}`);
      }

      // 构建支付请求
      const paymentRequest = {
        paymentNo: savedPayment.paymentNo,
        orderNo: order.orderNo,
        amount: createPaymentDto.amount,
        subject: this.buildPaymentSubject(order),
        body: this.buildPaymentBody(order),
        notifyUrl: createPaymentDto.callbackUrl || this.getDefaultNotifyUrl(),
        returnUrl: createPaymentDto.returnUrl || this.getDefaultReturnUrl(),
        userId: order.userId,
        extraParams: createPaymentDto.paymentConfig,
      };

      // 调用支付提供商创建支付
      const paymentResult = await provider.createPayment(paymentRequest);

      if (!paymentResult.success) {
        // 支付创建失败，更新支付状态
        savedPayment.status = PaymentStatus.FAILED;
        savedPayment.failureReason = paymentResult.errorMessage;
        await queryRunner.manager.save(savedPayment);
        
        await queryRunner.commitTransaction();
        
        throw new BadRequestException(`支付创建失败: ${paymentResult.errorMessage}`);
      }

      // 更新支付记录
      if (paymentResult.thirdPartyNo) {
        savedPayment.thirdPartyNo = paymentResult.thirdPartyNo;
      }
      
      savedPayment.paymentData = {
        paymentUrl: paymentResult.paymentUrl,
        qrCode: paymentResult.qrCode,
        paymentParams: paymentResult.paymentParams,
      };

      await queryRunner.manager.save(savedPayment);

      // 如果是余额支付且成功，直接处理支付成功
      if (createPaymentDto.paymentMethod === PaymentMethod.BALANCE && paymentResult.success) {
        await this.handlePaymentSuccess(savedPayment.id, {
          paymentNo: savedPayment.paymentNo,
          thirdPartyNo: paymentResult.thirdPartyNo!,
          status: PaymentStatus.SUCCESS,
          paidAt: new Date().toISOString(),
          callbackData: { source: 'balance_payment' },
        }, queryRunner);
      }

      await queryRunner.commitTransaction();

      this.logger.log(`支付创建成功: ${savedPayment.paymentNo}, 订单: ${order.orderNo}, 支付方式: ${createPaymentDto.paymentMethod}`);

      // 发送支付创建事件
      this.eventEmitter.emit('payment.created', {
        paymentId: savedPayment.id,
        orderId: order.id,
        userId: order.userId,
        paymentMethod: createPaymentDto.paymentMethod,
        amount: createPaymentDto.amount,
      });

      return {
        paymentId: savedPayment.id,
        paymentNo: savedPayment.paymentNo,
        paymentMethod: createPaymentDto.paymentMethod,
        amount: createPaymentDto.amount,
        paymentUrl: paymentResult.paymentUrl,
        qrCode: paymentResult.qrCode,
        paymentParams: paymentResult.paymentParams,
        expiresAt: paymentResult.expiresAt || new Date(Date.now() + 30 * 60 * 1000),
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`创建支付失败: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 处理支付回调
   */
  async handleCallback(
    paymentMethod: PaymentMethod,
    callbackData: Record<string, any>,
  ): Promise<{ success: boolean; message: string }> {
    try {
      this.logger.log(`处理支付回调: ${paymentMethod}, 数据: ${JSON.stringify(callbackData)}`);

      // 获取支付提供商
      const provider = this.paymentProviders.get(paymentMethod);
      if (!provider) {
        throw new BadRequestException(`不支持的支付方式: ${paymentMethod}`);
      }

      // 验证回调数据
      const verifyResult = await provider.verifyCallback(callbackData);
      if (!verifyResult.success) {
        this.logger.error(`支付回调验证失败: ${verifyResult.errorMessage}`);
        return { success: false, message: verifyResult.errorMessage || '回调验证失败' };
      }

      // 查找支付记录
      const payment = await this.paymentRepository.findOne({
        where: { paymentNo: verifyResult.paymentNo },
      });

      if (!payment) {
        this.logger.error(`支付记录不存在: ${verifyResult.paymentNo}`);
        return { success: false, message: '支付记录不存在' };
      }

      // 检查支付状态
      if (payment.status === PaymentStatus.SUCCESS) {
        this.logger.warn(`支付已处理: ${payment.paymentNo}`);
        return { success: true, message: '支付已处理' };
      }

      // 处理支付结果
      if (verifyResult.status === 'success') {
        await this.handlePaymentSuccess(payment.id, {
          paymentNo: verifyResult.paymentNo!,
          thirdPartyNo: verifyResult.thirdPartyNo!,
          status: PaymentStatus.SUCCESS,
          paidAt: verifyResult.paidAt?.toISOString(),
          callbackData: verifyResult.rawData,
        });
        return { success: true, message: '支付成功' };
      } else if (verifyResult.status === 'failed') {
        await this.handlePaymentFailure(payment.id, {
          paymentNo: verifyResult.paymentNo!,
          thirdPartyNo: verifyResult.thirdPartyNo || '',
          status: PaymentStatus.FAILED,
          failureReason: '支付失败',
          callbackData: verifyResult.rawData,
        });
        return { success: true, message: '支付失败已处理' };
      }

      return { success: true, message: '回调处理完成' };
    } catch (error) {
      this.logger.error(`处理支付回调失败: ${error.message}`, error.stack);
      return { success: false, message: error.message };
    }
  }

  /**
   * 处理支付成功
   */
  private async handlePaymentSuccess(
    paymentId: number,
    callbackDto: PaymentCallbackDto,
    queryRunner?: any,
  ): Promise<void> {
    const shouldManageTransaction = !queryRunner;
    if (shouldManageTransaction) {
      queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();
    }

    try {
      // 更新支付状态
      const payment = await queryRunner.manager.findOne(PaymentEntity, { where: { id: paymentId } });
      if (!payment) {
        throw new NotFoundException('支付记录不存在');
      }

      payment.status = PaymentStatus.SUCCESS;
      payment.thirdPartyNo = callbackDto.thirdPartyNo;
      payment.paidAt = callbackDto.paidAt ? new Date(callbackDto.paidAt) : new Date();
      payment.callbackData = callbackDto.callbackData;

      await queryRunner.manager.save(payment);

      // 更新订单状态为已支付
      await queryRunner.manager.update(
        'orders',
        { id: payment.orderId },
        {
          status: OrderStatus.PAID,
          paidAmount: payment.amount,
          paymentMethod: payment.paymentMethod,
        }
      );

      if (shouldManageTransaction) {
        await queryRunner.commitTransaction();
      }

      this.logger.log(`支付成功处理完成: ${payment.paymentNo}`);

      // 发送支付成功事件
      this.eventEmitter.emit('payment.success', {
        paymentId: payment.id,
        orderId: payment.orderId,
        userId: payment.userId,
        paymentMethod: payment.paymentMethod,
        amount: payment.amount,
        paidAt: payment.paidAt,
      });
    } catch (error) {
      if (shouldManageTransaction) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      if (shouldManageTransaction) {
        await queryRunner.release();
      }
    }
  }

  /**
   * 根据ID查找支付记录
   */
  async findById(id: number): Promise<PaymentResponseDto> {
    const payment = await this.paymentRepository.findOne({ where: { id } });
    if (!payment) {
      throw new NotFoundException('支付记录不存在');
    }
    return this.toPaymentResponseDto(payment);
  }

  /**
   * 根据支付单号查找支付记录
   */
  async findByPaymentNo(paymentNo: string): Promise<PaymentResponseDto> {
    const payment = await this.paymentRepository.findOne({ where: { paymentNo } });
    if (!payment) {
      throw new NotFoundException('支付记录不存在');
    }
    return this.toPaymentResponseDto(payment);
  }

  /**
   * 查询支付列表
   */
  async findAll(queryDto: QueryPaymentDto): Promise<PaymentListResponseDto> {
    const { page = 1, limit = 20, ...filters } = queryDto;
    const skip = (page - 1) * limit;

    const queryBuilder = this.paymentRepository
      .createQueryBuilder('payment')
      .orderBy('payment.createdAt', 'DESC');

    // 应用过滤条件
    if (filters.userId) {
      queryBuilder.andWhere('payment.userId = :userId', { userId: filters.userId });
    }

    if (filters.orderId) {
      queryBuilder.andWhere('payment.orderId = :orderId', { orderId: filters.orderId });
    }

    if (filters.paymentNo) {
      queryBuilder.andWhere('payment.paymentNo LIKE :paymentNo', { paymentNo: `%${filters.paymentNo}%` });
    }

    if (filters.thirdPartyNo) {
      queryBuilder.andWhere('payment.thirdPartyNo LIKE :thirdPartyNo', { thirdPartyNo: `%${filters.thirdPartyNo}%` });
    }

    if (filters.paymentMethod) {
      queryBuilder.andWhere('payment.paymentMethod = :paymentMethod', { paymentMethod: filters.paymentMethod });
    }

    if (filters.status) {
      queryBuilder.andWhere('payment.status = :status', { status: filters.status });
    }

    if (filters.startTime && filters.endTime) {
      queryBuilder.andWhere('payment.createdAt BETWEEN :startTime AND :endTime', {
        startTime: filters.startTime,
        endTime: filters.endTime,
      });
    }

    const [payments, totalItems] = await queryBuilder
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    const items = payments.map(payment => this.toPaymentResponseDto(payment));

    return {
      items,
      meta: {
        page,
        limit,
        totalItems,
        totalPages: Math.ceil(totalItems / limit),
      },
    };
  }

  /**
   * 更新支付记录
   */
  async update(id: number, updateDto: UpdatePaymentDto): Promise<PaymentResponseDto> {
    const payment = await this.paymentRepository.findOne({ where: { id } });
    if (!payment) {
      throw new NotFoundException('支付记录不存在');
    }

    // 更新支付信息
    Object.assign(payment, updateDto);
    if (updateDto.paidAt) {
      payment.paidAt = new Date(updateDto.paidAt);
    }

    await this.paymentRepository.save(payment);

    this.logger.log(`支付记录更新成功: ${payment.paymentNo}`);

    return this.toPaymentResponseDto(payment);
  }

  /**
   * 处理支付失败
   */
  private async handlePaymentFailure(
    paymentId: number,
    callbackDto: PaymentCallbackDto,
  ): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 更新支付状态
      const payment = await queryRunner.manager.findOne(PaymentEntity, { where: { id: paymentId } });
      if (!payment) {
        throw new NotFoundException('支付记录不存在');
      }

      payment.status = PaymentStatus.FAILED;
      payment.thirdPartyNo = callbackDto.thirdPartyNo;
      payment.failureReason = callbackDto.failureReason;
      payment.callbackData = callbackDto.callbackData;

      await queryRunner.manager.save(payment);
      await queryRunner.commitTransaction();

      this.logger.log(`支付失败处理完成: ${payment.paymentNo}, 原因: ${callbackDto.failureReason}`);

      // 发送支付失败事件
      this.eventEmitter.emit('payment.failed', {
        paymentId: payment.id,
        orderId: payment.orderId,
        userId: payment.userId,
        paymentMethod: payment.paymentMethod,
        amount: payment.amount,
        failureReason: callbackDto.failureReason,
      });
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 转换为响应DTO
   */
  private toPaymentResponseDto(payment: PaymentEntity): PaymentResponseDto {
    return {
      id: payment.id,
      orderId: payment.orderId,
      userId: payment.userId,
      paymentNo: payment.paymentNo,
      thirdPartyNo: payment.thirdPartyNo,
      paymentMethod: payment.paymentMethod,
      status: payment.status,
      amount: payment.amount,
      paymentData: payment.paymentData,
      paidAt: payment.paidAt,
      failureReason: payment.failureReason,
      callbackData: payment.callbackData,
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt,
      isSuccess: payment.isSuccess(),
      isFailed: payment.isFailed(),
      canRetry: payment.canRetry(),
    };
  }

  /**
   * 构建支付主题
   */
  private buildPaymentSubject(order: any): string {
    switch (order.orderType) {
      case 'service':
        return '服务购买';
      case 'recharge':
        return '账户充值';
      default:
        return '订单支付';
    }
  }

  /**
   * 构建支付描述
   */
  private buildPaymentBody(order: any): string {
    return `订单号: ${order.orderNo}, 金额: ${order.totalAmount}元`;
  }

  /**
   * 获取默认回调地址
   */
  private getDefaultNotifyUrl(): string {
    return 'https://your-domain.com/api/v1/payments/callback';
  }

  /**
   * 获取默认返回地址
   */
  private getDefaultReturnUrl(): string {
    return 'https://your-domain.com/payment/result';
  }
}