import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  Index,
} from 'typeorm';
import { CallStatus } from '../enums/call-status.enum';

/**
 * 调用记录实体 - 精简版
 */
@Entity('call_records')
export class CallRecordEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Index()
  @Column()
  userId: number;

  @Index()
  @Column()
  serviceId: number;

  @Column({ length: 10 })
  method: string;  // HTTP方法: GET, POST等

  @Column({ nullable: true, length: 64 })
  requestId: string;  // 请求唯一标识

  @Index()
  @Column({ type: 'enum', enum: CallStatus })
  status: CallStatus;  // 调用状态: success, failed, quota_exceeded

  @Column({ nullable: true, length: 255 })
  failReason: string;  // 失败原因，仅当status=failed时有值

  @Column({ type: 'int' })
  duration: number;  // 调用耗时(ms)

  @CreateDateColumn()
  createdAt: Date;

  @Column({ nullable: true })
  ipAddress: string;

  @Index()
  @Column({ nullable: true, length: 36 })
  apiKeyId: string;  // 使用的API密钥ID
} 