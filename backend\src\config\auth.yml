# 认证模块配置
auth:
  jwt:
    secret: ${JWT_SECRET}
    refreshSecret: ${JWT_REFRESH_SECRET}
    expiresIn: 86400 # 24小时
    refreshExpiresIn: 604800 # 7天
  security:
    rateLimit:
      loginAttempts: 5 # 最大尝试次数
      lockDuration: 900 # 锁定时长(秒)
    verification:
      codeLength: 6 # 验证码长度
      codeExpiry: 300 # 验证码有效期(秒)
      cooldown: 60 # 发送冷却时间(秒)
      maxAttempts: 3 # 最大尝试次数
  passwordPolicy:
    minLength: 8
    requireLowercase: true
    requireUppercase: true
    requireNumbers: true
    requireSpecialChars: true
  features:
    silentRegister: true # 是否启用静默注册
    multiFactorAuth: false # 是否启用多因素认证
  oauth:
    wechat:
      appId: ${WECHAT_APPID}
      appSecret: ${WECHAT_SECRET}
    alipay:
      appId: ${ALIPAY_APPID}
      appSecret: ${ALIPAY_SECRET}
    github:
      clientId: ${GITHUB_CLIENT_ID}
      clientSecret: ${GITHUB_CLIENT_SECRET}
    google:
      clientId: ${GOOGLE_CLIENT_ID}
      clientSecret: ${GOOGLE_CLIENT_SECRET}
      redirectUri: ${GOOGLE_REDIRECT_URI}