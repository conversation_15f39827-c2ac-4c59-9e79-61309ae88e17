import { request } from '@/utils/request'
import type { Order, OrderDetail, OrderStats } from '@/types/order'

/**
 * 订单相关API
 */
export const orderApi = {
  /**
   * 创建订单
   * @param data 订单数据
   */
  createOrder(data: {
    packageId: string
    quantity?: number
    paymentMethod?: string
  }): Promise<Order> {
    return request.post('/op/orders', data)
  },

  /**
   * 查询订单列表
   * @param params 查询参数
   */
  getOrders(params?: {
    page?: number
    pageSize?: number
    status?: string
    startTime?: string
    endTime?: string
    keyword?: string
  }): Promise<{
    data: Order[]
    total: number
    page: number
    pageSize: number
  }> {
    return request.get('/op/orders', { params })
  },

  /**
   * 获取订单详情
   * @param orderId 订单ID
   */
  getOrderDetail(orderId: string): Promise<OrderDetail> {
    return request.get(`/op/orders/${orderId}`)
  },

  /**
   * 获取订单统计
   * @param params 统计参数
   */
  getOrderStatistics(params?: {
    startTime?: string
    endTime?: string
    groupBy?: 'day' | 'week' | 'month'
  }): Promise<OrderStats> {
    return request.get('/op/orders/statistics', { params })
  },

  /**
   * 取消订单
   * @param orderId 订单ID
   * @param reason 取消原因
   */
  cancelOrder(orderId: string, reason?: string): Promise<void> {
    return request.post(`/op/orders/${orderId}/cancel`, { reason })
  },

  /**
   * 支付订单
   * @param orderId 订单ID
   * @param paymentData 支付数据
   */
  payOrder(orderId: string, paymentData: {
    paymentMethod: string
    returnUrl?: string
  }): Promise<{
    paymentUrl?: string
    qrCode?: string
    orderId: string
  }> {
    return request.post(`/op/orders/${orderId}/pay`, paymentData)
  },

  /**
   * 查询支付状态
   * @param orderId 订单ID
   */
  getPaymentStatus(orderId: string): Promise<{
    status: 'pending' | 'paid' | 'failed' | 'cancelled'
    paidAt?: string
    failReason?: string
  }> {
    return request.get(`/op/orders/${orderId}/payment-status`)
  },

  /**
   * 获取支付方式列表
   */
  getPaymentMethods(): Promise<{
    id: string
    name: string
    icon: string
    enabled: boolean
    description?: string
  }[]> {
    return request.get('/op/orders/payment-methods')
  },

  /**
   * 申请退款
   * @param orderId 订单ID
   * @param reason 退款原因
   */
  requestRefund(orderId: string, reason: string): Promise<void> {
    return request.post(`/op/orders/${orderId}/refund`, { reason })
  },

  /**
   * 下载订单发票
   * @param orderId 订单ID
   */
  downloadInvoice(orderId: string): Promise<Blob> {
    return request.get(`/op/orders/${orderId}/invoice`, {
      responseType: 'blob'
    })
  }
}