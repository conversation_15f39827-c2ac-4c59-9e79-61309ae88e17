import { Injectable, Logger, BadRequestException, NotFoundException, Inject, forwardRef } from '@nestjs/common';
import { ApiKeyService } from '../api-key.service';
import { UserService } from '../../user/user.service';
import { ServiceService } from '../../service/service.service';
import { UserServiceService } from '../../user-service/user-service.service';
import { CreateApiKeyDto, ApiKeyResponseDto } from '../dto/api-key.dto';
import { UserType, TierEnum } from '../../user/entities/user.entity';
import { ApiKeyStatus } from '../entities/api-key.entity';

/**
 * 密钥管理服务
 * 负责实现统一密钥多服务授权模式
 */
@Injectable()
export class KeyManagementService {
  private readonly logger = new Logger(KeyManagementService.name);

  constructor(
    @Inject(forwardRef(() => ApiKeyService))
    private readonly apiKeyService: ApiKeyService,
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    private readonly serviceService: ServiceService,
    private readonly userServiceService: UserServiceService,
  ) {}

  /**
   * 同步所有活跃的API密钥到缓存
   * 用于应用启动时预加载
   */
  async syncAllApiKeysToCache(): Promise<number> {
    try {
      this.logger.log('开始同步所有活跃API密钥到缓存...');
      const count = await this.apiKeyService.loadApiKeysToCache();
      this.logger.log(`成功同步 ${count} 个API密钥到缓存`);
      return count;
    } catch (error) {
      this.logger.error(`同步API密钥到缓存失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 为用户创建主密钥（可访问所有授权服务）
   * 在用户注册/创建后自动调用
   */
  async createUserMasterKey(userId: number): Promise<ApiKeyResponseDto> {
    try {
      // 验证用户是否存在
      const user = await this.userService.findById(userId);
      if (!user) {
        throw new NotFoundException(`用户 #${userId} 不存在`);
      }

      // 检查用户是否已有主密钥
      const existingKeys = await this.apiKeyService.findAll({ userId, keyType: user.tier || 'trial' });
      if (existingKeys?.data?.length > 0) {
        this.logger.log(`用户 #${userId} 已有主密钥，无需重复创建`);
        return existingKeys.data[0];
      }

      // 确定密钥类型和权限
      // 使用用户的tier作为密钥类型，确保是枚举中的有效值
      const keyType = user.tier || 'trial';
      const permissions = this.getDefaultPermissions(user.userType);
      
      // 创建主密钥，使用serviceId = 0表示平台级密钥，不绑定特定服务
      const dto: CreateApiKeyDto = {
        userId,
        serviceId: 0, // 0表示平台级密钥，不绑定特定服务
        name: '用户主密钥',
        keyType,
        permissions,
        description: '用于访问所有授权服务的主密钥',
      };

      const result = await this.apiKeyService.createApiKey(dto);
      this.logger.log(`为用户 #${userId} 创建主密钥成功`);
      
      return result;
    } catch (error) {
      this.logger.error(`为用户 #${userId} 创建主密钥失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 为企业用户创建服务特定密钥
   * 企业用户可以为不同服务创建专用密钥，便于权限管理
   */
  async createServiceSpecificKey(
    userId: number,
    serviceId: number,
    name: string,
    permissions: string[] = [],
    description?: string
  ): Promise<ApiKeyResponseDto> {
    try {
      // 验证用户是否存在且为企业用户
      const user = await this.userService.findById(userId);
      if (!user) {
        throw new NotFoundException(`用户 #${userId} 不存在`);
      }
      
      if (user.userType !== UserType.ENTERPRISE && user.userType !== UserType.ORGANIZATION) {
        throw new BadRequestException('只有企业用户或组织用户可以创建服务特定密钥');
      }

      // 验证服务是否存在
      const service = await this.serviceService.findOne(serviceId);
      if (!service) {
        throw new NotFoundException(`服务 #${serviceId} 不存在`);
      }

      // 检查用户是否订阅了该服务
      const userService = await this.userServiceService.findByUserAndService(userId, serviceId);
      if (!userService) {
        throw new BadRequestException(`用户未订阅服务 #${serviceId}`);
      }

      // 确定密钥类型
      const keyType = 'service-specific';

      // 如果权限为空，则使用服务默认权限
      if (!permissions || permissions.length === 0) {
        permissions = [`service:${service.code}:*`];
      }

      // 创建服务特定密钥
      const dto: CreateApiKeyDto = {
        userId,
        serviceId,
        name: name || `${service.name}专用密钥`,
        keyType,
        permissions,
        description: description || `用于访问${service.name}服务的专用密钥`,
      };

      const result = await this.apiKeyService.createApiKey(dto);
      this.logger.log(`为用户 #${userId} 创建服务 #${serviceId} 密钥成功`);
      
      return result;
    } catch (error) {
      this.logger.error(`为用户 #${userId} 创建服务特定密钥失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 为用户重新生成密钥
   * 适用于密钥泄露或过期需要更换的情况
   */
  async regenerateKey(userId: number, keyId: number): Promise<ApiKeyResponseDto> {
    try {
      // 验证密钥存在且属于该用户
      const apiKey = await this.apiKeyService.findOne(keyId);
      if (!apiKey || apiKey.userId !== userId) {
        throw new NotFoundException('密钥不存在或不属于该用户');
      }

      // 重新生成密钥
      const result = await this.apiKeyService.regenerateApiKey({ id: keyId });
      this.logger.log(`为用户 #${userId} 重新生成密钥 #${keyId} 成功`);
      
      return result;
    } catch (error) {
      this.logger.error(`为用户 #${userId} 重新生成密钥失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 检查用户是否有权限访问特定服务
   * 用于网关服务调用验证，专注于服务授权和使用次数检查
   * 注意：基础密钥验证已经由UnifiedAuthGuard完成
   */
  async checkServiceAccess(userId: number, apiKeyId: string, serviceId: number): Promise<boolean> {
    try {
      // 验证API密钥是否属于该用户
      const apiKey = await this.apiKeyService.findOne(Number(apiKeyId));
      if (!apiKey || apiKey.userId !== userId) {
        this.logger.warn(`API密钥 ${apiKeyId} 不存在或不属于用户 #${userId}`);
        return false;
      }

      // 检查密钥状态是否活跃
      if (apiKey.keyStatus !== ApiKeyStatus.ACTIVE) {
        this.logger.warn(`API密钥 ${apiKeyId} 状态非活跃: ${apiKey.keyStatus}`);
        return false;
      }

      // 获取服务信息
      const service = await this.serviceService.findOne(serviceId);
      if (!service) {
        this.logger.warn(`服务 #${serviceId} 不存在`);
        return false;
      }

      // 检查用户-服务关联
      const userService = await this.userServiceService.findByUserAndService(userId, serviceId);
      if (!userService) {
        this.logger.warn(`用户 #${userId} 未订阅服务 #${serviceId}`);
        return false;
      }

      // 检查用户是否有可用的调用次数
      const hasEnoughCount = userService.totalCount > userService.usedCount;
      if (!hasEnoughCount) {
        this.logger.warn(`用户 #${userId} 服务 #${serviceId} 使用次数不足: ${userService.usedCount}/${userService.totalCount}`);
        return false;
      }
      
      this.logger.log(`用户 #${userId} 可以访问服务 #${serviceId}, 剩余次数: ${userService.totalCount - userService.usedCount}`);
      return true;
    } catch (error) {
      this.logger.error(`检查用户 #${userId} 服务 #${serviceId} 权限失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 检查用户是否有权限访问特定服务（通过服务代码）
   * 用于网关服务调用验证，专注于服务授权和使用次数检查
   * 注意：基础密钥验证已经由UnifiedAuthGuard完成
   */
  async checkServiceAccessByCode(userId: number, serviceCode: string): Promise<boolean> {
    try {
      // 获取服务ID
      const service = await this.serviceService.findByCode(serviceCode);
      if (!service) {
        this.logger.warn(`服务 ${serviceCode} 不存在`);
        return false;
      }

      // 检查用户-服务关联
      const userService = await this.userServiceService.findByUserAndService(userId, service.id);
      if (!userService) {
        this.logger.warn(`用户 #${userId} 未订阅服务 ${serviceCode}`);
        return false;
      }

      // 检查用户是否有可用的调用次数
      const hasEnoughCount = userService.totalCount > userService.usedCount;
      if (!hasEnoughCount) {
        this.logger.warn(`用户 #${userId} 服务 ${serviceCode} 使用次数不足: ${userService.usedCount}/${userService.totalCount}`);
        return false;
      }
      
      this.logger.log(`用户 #${userId} 可以访问服务 ${serviceCode}, 剩余次数: ${userService.totalCount - userService.usedCount}`);
      return true;
    } catch (error) {
      this.logger.error(`检查用户 #${userId} 服务 ${serviceCode} 权限失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 批量授权服务
   * 为用户批量订阅服务，适用于新用户注册赠送免费服务等场景
   */
  async batchAssignServices(userId: number, serviceIds: number[], freeCount: number = 10): Promise<void> {
    try {
      // 验证用户是否存在
      const user = await this.userService.findById(userId);
      if (!user) {
        throw new NotFoundException(`用户 #${userId} 不存在`);
      }

      // 对每个服务单独进行授权
      for (const serviceId of serviceIds) {
        try {
          // 批量授权单个服务
          await this.userServiceService.batchAssignService({
            userIds: [userId],
            serviceId, // 使用单个serviceId而不是serviceIds
            freeCount,
            purchasedCount: 0
          });
        } catch (error) {
          this.logger.error(`为用户 #${userId} 授权服务 #${serviceId} 失败: ${error.message}`);
        }
      }

      this.logger.log(`为用户 #${userId} 批量授权 ${serviceIds.length} 个服务成功`);
    } catch (error) {
      this.logger.error(`为用户 #${userId} 批量授权服务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取密钥详细信息
   * 包括关联的服务权限
   */
  async getKeyDetails(keyId: number): Promise<any> {
    try {
      const apiKey = await this.apiKeyService.findOne(keyId);
      if (!apiKey) {
        throw new NotFoundException(`密钥 #${keyId} 不存在`);
      }

      // 获取用户已订阅的服务
      const userServices = await this.userServiceService.findByUserId(apiKey.userId);
      
      // 关联密钥权限信息
      return {
        ...apiKey,
        availableServices: userServices.map(us => ({
          serviceId: us.serviceId,
          serviceName: us.service?.name,
          serviceCode: us.service?.code,
          totalCount: us.totalCount,
          usedCount: us.usedCount,
          remainingCount: us.totalCount - us.usedCount,
        }))
      };
    } catch (error) {
      this.logger.error(`获取密钥 #${keyId} 详细信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取解密后的Secret Key
   * 用于签名验证，根据API密钥ID获取对应的Secret Key
   * @param apiKeyId API密钥ID
   * @returns 解密后的Secret Key或null
   */
  async getDecryptedSecretKey(apiKeyId: number): Promise<string | null> {
    try {
      this.logger.debug(`尝试获取API密钥 #${apiKeyId} 的解密Secret Key`);
      
      // 获取API密钥实体
      const apiKeyEntity = await this.apiKeyService['repository'].findOne({
        where: { id: apiKeyId }
      });
      
      if (!apiKeyEntity) {
        this.logger.warn(`API密钥 #${apiKeyId} 不存在`);
        return null;
      }
      
      // 如果存储了临时明文密钥（通常是刚创建的密钥）
      if (apiKeyEntity.tempSecretKey) {
        this.logger.debug(`API密钥 #${apiKeyId} 存在临时明文密钥`);
        return apiKeyEntity.tempSecretKey;
      }
      
      // 目前系统不支持解密已存储的密钥哈希
      // 在实际生产环境中，这里可以集成密钥管理系统(KMS)来解密存储的密钥
      this.logger.warn(`API密钥 #${apiKeyId} 没有可用的明文密钥，无法进行签名验证`);
      return null;
    } catch (error) {
      this.logger.error(`获取解密Secret Key失败: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 根据用户类型确定默认密钥类型
   * @private
   */
  private determineKeyTypeByUserType(userType: UserType): string {
    switch (userType) {
      case UserType.ENTERPRISE:
        return TierEnum.ENTERPRISE;
      case UserType.ORGANIZATION:
        return TierEnum.PREMIUM;
      case UserType.INDIVIDUAL:
        return TierEnum.BASIC;
      default:
        return TierEnum.BASIC;
    }
  }

  /**
   * 根据用户类型获取默认权限
   * @private
   */
  private getDefaultPermissions(userType: UserType): string[] {
    switch (userType) {
      case UserType.ENTERPRISE:
        return ['*']; // 企业用户可访问所有权限
      case UserType.ORGANIZATION:
        return ['service:*:read', 'service:*:write']; // 组织用户可读写所有服务
      case UserType.INDIVIDUAL:
        return ['service:*:read']; // 个人用户只能读取服务
      default:
        return ['service:basic:read']; // 试用用户只能访问基础服务
    }
  }
} 