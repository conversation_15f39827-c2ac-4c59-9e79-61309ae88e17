-- 种子数据SQL语句（包含表结构）
-- 生成时间: 2025-07-13T14:19:01.929Z

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS=0;

-- 表结构

-- 用户表
DROP TABLE IF EXISTS `open_user`;
CREATE TABLE `open_user` (
  `id` int NOT NULL AUTO_INCREMENT,
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` datetime(6) DEFAULT NULL,
  `createdBy` varchar(255) DEFAULT NULL,
  `updatedBy` varchar(255) DEFAULT NULL,
  `baseStatus` tinyint NOT NULL DEFAULT '1',
  `del_flag` char(1) NOT NULL DEFAULT '0',
  `remark` varchar(500) DEFAULT NULL,
  `username` varchar(100) NOT NULL COMMENT '用户名',
  `email` varchar(200) DEFAULT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码哈希',
  `nickname` varchar(30) NOT NULL COMMENT '用户昵称',
  `realName` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `bio` text COMMENT '个人简介',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `gender` enum('male','female','unknown') NOT NULL DEFAULT 'unknown' COMMENT '性别',
  `userType` enum('individual','enterprise','organization') NOT NULL DEFAULT 'individual' COMMENT '用户类型',
  `verificationStatus` enum('unverified','personal_verified','enterprise_verified') NOT NULL DEFAULT 'unverified' COMMENT '认证状态',
  `role` enum('user','admin','super') NOT NULL DEFAULT 'user' COMMENT '角色',
  `openid` varchar(255) DEFAULT NULL,
  `userStatus` enum('active','suspended','banned','pending','locked') NOT NULL DEFAULT 'active' COMMENT '账户状态：active-活跃，suspended-暂停，banned-封禁，pending-待审核，locked-锁定',
  `emailVerified` tinyint NOT NULL DEFAULT '0' COMMENT '邮箱是否验证：false-未验证，true-已验证',
  `phoneVerified` tinyint NOT NULL DEFAULT '0' COMMENT '手机是否验证：false-未验证，true-已验证',
  `lastLoginAt` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `lastLoginIp` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `loginFailCount` int NOT NULL DEFAULT '0' COMMENT '登录失败次数',
  `lockedUntil` timestamp NULL DEFAULT NULL COMMENT '账户锁定截止时间',
  `preferences` text COMMENT '用户偏好设置',
  `settings` text COMMENT '用户设置',
  `lastDailyResetDate` timestamp NULL DEFAULT NULL COMMENT '每日免费使用次数重置日期',
  `tier` enum('basic','premium','enterprise') NOT NULL DEFAULT 'basic' COMMENT '用户等级：basic-基础，premium-高级，enterprise-企业',
  `remainingUsage` int NOT NULL DEFAULT '0' COMMENT '剩余使用次数',
  `totalUsage` int NOT NULL DEFAULT '0' COMMENT '总使用次数',
  `dailyFreeUsageRemaining` int NOT NULL DEFAULT '0' COMMENT '每日免费使用次数剩余',
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '账户余额',
  `isFreeQuotaEligible` tinyint NOT NULL DEFAULT '1' COMMENT '是否有资格获取免费额度',
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_open_user_username` (`username`),
  UNIQUE KEY `IDX_open_user_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户信息表';


-- API密钥表
DROP TABLE IF EXISTS `open_api_key`;
CREATE TABLE `open_api_key` (
  `id` int NOT NULL AUTO_INCREMENT,
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` datetime(6) DEFAULT NULL,
  `createdBy` varchar(255) DEFAULT NULL,
  `updatedBy` varchar(255) DEFAULT NULL,
  `baseStatus` tinyint NOT NULL DEFAULT '1',
  `del_flag` char(1) NOT NULL DEFAULT '0',
  `remark` varchar(500) DEFAULT NULL,
  `user_id` int NOT NULL COMMENT '用户ID',
  `service_id` int NOT NULL COMMENT '服务ID',
  `name` varchar(100) NOT NULL COMMENT '密钥名称',
  `apiKey` varchar(64) NOT NULL COMMENT 'API密钥（明文）',
  `secretHash` varchar(64) NOT NULL COMMENT 'API密钥秘钥哈希',
  `keyType` enum('trial','basic','premium','enterprise') NOT NULL DEFAULT 'trial' COMMENT '密钥类型',
  `key-status` enum('active','inactive','expired','revoked') NOT NULL DEFAULT 'active' COMMENT '密钥状态',
  `description` text COMMENT '密钥描述',
  `permissions` text COMMENT '权限范围',
  `expiresAt` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `lastUsedAt` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `is_secret_viewed` tinyint NOT NULL DEFAULT '0' COMMENT '密钥是否已被查看过（用于仅显示一次）',
  `lastUsedIp` varchar(45) DEFAULT NULL COMMENT '最后使用IP',
  `tempSecretKey` varchar(128) DEFAULT NULL COMMENT '临时明文密钥，仅未查看时保存',
  `userId` int DEFAULT NULL,
  `serviceId` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_open_api_key_apiKey` (`apiKey`),
  UNIQUE KEY `IDX_open_api_key_secretHash` (`secretHash`),
  KEY `IDX_open_api_key_userId` (`user_id`),
  KEY `IDX_open_api_key_keyStatus` (`key-status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='API密钥表';


-- 服务表
DROP TABLE IF EXISTS `open_service`;
CREATE TABLE `open_service` (
  `id` int NOT NULL AUTO_INCREMENT,
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` datetime(6) DEFAULT NULL,
  `createdBy` varchar(255) DEFAULT NULL,
  `updatedBy` varchar(255) DEFAULT NULL,
  `baseStatus` tinyint NOT NULL DEFAULT '1',
  `del_flag` char(1) NOT NULL DEFAULT '0',
  `remark` varchar(500) DEFAULT NULL,
  `user_id` int NOT NULL COMMENT '用户ID',
  `service_id` int NOT NULL COMMENT '服务ID',
  `code` varchar(100) NOT NULL COMMENT '服务代码',
  `name` varchar(200) NOT NULL COMMENT '服务名称',
  `description` text COMMENT '服务描述',
  `type` enum('OCR','NLP','CV','AI','DATA','OTHER') NOT NULL COMMENT '服务类型：OCR、NLP、CV、AI、DATA、OTHER等',
  `serviceStatus` enum('active','inactive','maintenance','deprecated') NOT NULL COMMENT '服务状态枚举：active、inactive、maintenance、deprecated等',
  `pricingModel` enum('free','pay_per_use','subscription','tiered') NOT NULL COMMENT '定价模式枚举：free、pay_per_use、subscription、tiered等',
  `currentVersion` varchar(20) NOT NULL DEFAULT 'v1.0.0' COMMENT '当前版本',
  `features` varchar(200) NOT NULL DEFAULT '' COMMENT '服务特性',
  `endpoint` varchar(500) DEFAULT NULL COMMENT '服务端点URL',
  `config` text COMMENT '服务配置参数',
  `callCount` int NOT NULL DEFAULT '0' COMMENT '调用次数',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '服务费用（每次调用）',
  `dailyLimit` int NOT NULL DEFAULT '1000' COMMENT '每日调用限制',
  `minuteLimit` int NOT NULL DEFAULT '100' COMMENT '每分钟调用限制',
  `requireAuth` tinyint NOT NULL DEFAULT '1' COMMENT '是否需要认证：0-否，1-是',
  `isAsync` tinyint NOT NULL DEFAULT '0' COMMENT '是否异步处理：0-否，1-是',
  `timeout` int NOT NULL DEFAULT '30' COMMENT '超时时间（秒）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_open_service_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='API服务表';


-- 用户服务表
DROP TABLE IF EXISTS `user_service`;
CREATE TABLE `user_service` (
  `id` int NOT NULL AUTO_INCREMENT,
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` datetime(6) DEFAULT NULL,
  `createdBy` varchar(255) DEFAULT NULL,
  `updatedBy` varchar(255) DEFAULT NULL,
  `baseStatus` tinyint NOT NULL DEFAULT '1',
  `del_flag` char(1) NOT NULL DEFAULT '0',
  `remark` varchar(500) DEFAULT NULL,
  `total_count` int NOT NULL DEFAULT '0' COMMENT '总调用次数',
  `used_count` int NOT NULL DEFAULT '0' COMMENT '已用次数',
  `free_used_today` int NOT NULL DEFAULT '0' COMMENT '今日已用免费次数',
  `last_reset_date` date DEFAULT NULL COMMENT '上次免费额度重置日期',
  `alert_sent` tinyint NOT NULL DEFAULT '0' COMMENT '是否已发送预警',
  `userId` int DEFAULT NULL,
  `serviceId` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户服务表';


-- 管理员用户
INSERT INTO open_user (username, email, nickname, realName, password, role, userStatus, userType, verificationStatus, tier, emailVerified, phoneVerified, bio, gender, loginFailCount, remainingUsage, totalUsage, dailyFreeUsageRemaining, balance, preferences, settings) 
VALUES ('admin', '<EMAIL>', '系统管理员', '系统管理员', '$2b$10$PTWGL.f4i2yqZmpOA8w/bOrGkuq/9mQvmV.GBFFQ40VOV9PSUJsHq', 'admin', 'active', 'organization', 'enterprise_verified', 'enterprise', 1, 0, '系统默认管理员账户', 'unknown', 0, 999999999, 0, 999999, 10000.00, '{"language":"zh-CN","theme":"light","timezone":"Asia/Shanghai"}', '{"notifications":{"email":true,"sms":false,"push":true},"security":{"twoFactorAuth":false,"loginNotification":true}}');

-- 测试用户
INSERT INTO open_user (username, email, nickname, realName, password, role, userStatus, userType, verificationStatus, tier, emailVerified, phoneVerified, phone, bio, gender, loginFailCount, remainingUsage, totalUsage, dailyFreeUsageRemaining, balance, preferences, settings) 
VALUES ('testuser', '<EMAIL>', '测试用户', '张测试', '$2b$10$aQitxudmoAhCmPDw4FFOfuUU0UUqdXCpJ061ZcLsP7.udfMbzV52W', 'user', 'active', 'individual', 'personal_verified', 'basic', 1, 1, '13800138000', '测试账户，用于演示和测试', 'male', 0, 1000, 0, 100, 100.00, '{"language":"zh-CN","theme":"light","timezone":"Asia/Shanghai"}', '{"notifications":{"email":true,"sms":true,"push":true},"security":{"twoFactorAuth":false,"loginNotification":true}}');

-- 管理员API密钥
INSERT INTO open_api_key (user_id, service_id, name, apiKey, secretHash, keyType, `key-status`, description, permissions, is_secret_viewed) 
VALUES (1, 0, '管理员主密钥', 'ak_admin_test_key_12345678901234567890', '$2b$10$lbTUrWLVR9hgwKJyos.1S.z2nesGr3CMTnyR5iLaYhcLf9t8Zh92u', 'enterprise', 'active', '管理员全局访问密钥，可以访问所有服务', '["*:*:*"]', 1);

-- 测试用户API密钥
INSERT INTO open_api_key (user_id, service_id, name, apiKey, secretHash, keyType, `key-status`, description, permissions, is_secret_viewed) 
VALUES (2, 0, '测试用户主密钥', 'ak_a1fb793ecce140d3851dbae3e5568dbf', '$2b$10$xw/QOVu4jVgYyBggTWBh.O708oUnJGUqtCE96Y3FF7UsKlk82rH6O', 'basic', 'active', '测试用户访问密钥，有调用次数限制', '["service:OCR_EXPRESS:*","service:ADDRESS_EXTRACT:*","service:GEO_REVERSE:*"]', 1);

-- 服务数据
INSERT INTO open_service (user_id, service_id, code, name, description, type, serviceStatus, pricingModel, currentVersion, features, endpoint, config, callCount, price, dailyLimit, minuteLimit, requireAuth, isAsync, timeout) 
VALUES (1, 0, 'OCR_EXPRESS', '物流面单OCR识别', '智能识别物流面单信息，提取收发件人、地址、电话等关键信息，支持多种面单格式，准确率高达95%以上。适用于物流企业、电商平台等自动化数据录入场景。', 'OCR', 'active', 'pay_per_use', 'v1.0.0', '高精度识别,多格式支持,实时处理,结构化输出,批量处理', '/op/ocr/upload', '{"supportedFormats":["jpg","jpeg","png","pdf","bmp","tiff"],"maxFileSize":"10MB","accuracy":0.95,"supportedCouriers":["顺丰","圆通","中通","申通","韵达","百世","邮政","京东","天天","德邦"],"outputFields":["sender_name","sender_phone","sender_address","receiver_name","receiver_phone","receiver_address","tracking_number","courier_name","weight","package_count"],"batchLimit":10,"preProcessing":{"autoRotate":true,"enhanceImage":true,"cropToContent":true},"returnOriginalImage":false,"accessMethods":[{"type":"file_upload","endpoint":"/op/ocr/upload","method":"POST","contentType":"multipart/form-data","maxFileSize":"5MB","supportedFormats":["jpg","jpeg","png","gif","bmp","webp"]},{"type":"base64","endpoint":"/op/ocr/recognize","method":"POST","contentType":"application/json","maxBase64Length":7000000}]}', 0, 0.05, 10000, 100, 1, 0, 30);
INSERT INTO open_service (user_id, service_id, code, name, description, type, serviceStatus, pricingModel, currentVersion, features, endpoint, config, callCount, price, dailyLimit, minuteLimit, requireAuth, isAsync, timeout) 
VALUES (1, 0, 'ADDRESS_EXTRACT', '物流文本精确提取省市区详细地址', '从文本中精确提取省市区详细地址信息，支持非结构化文本解析，可处理各种格式的地址信息，并进行标准化输出，支持省市区到街道社区的精确识别。', 'NLP', 'active', 'pay_per_use', 'v1.0.0', '智能分词,地址标准化,高准确率,支持模糊地址,批量处理,多维度纠错', '/op/address/extract', '{"supportedRegions":["全国省市区县"],"outputFormat":"standard","accuracy":0.98,"maxTextLength":2000,"batchLimit":50,"supportedOutputFields":{"province":true,"city":true,"district":true,"street":true,"community":true,"building":true,"room":true,"detail":true,"postalCode":true,"formatted":true},"addressNormalization":true,"fuzzyMatching":true,"confidenceScoreThreshold":0.7,"returnMultipleCandidates":true,"candidateLimit":3}', 0, 0.05, 20000, 200, 1, 0, 15);
INSERT INTO open_service (user_id, service_id, code, name, description, type, serviceStatus, pricingModel, currentVersion, features, endpoint, config, callCount, price, dailyLimit, minuteLimit, requireAuth, isAsync, timeout) 
VALUES (1, 0, 'GEO_REVERSE', '地理坐标逆解析出地址', '根据经纬度坐标逆向解析出详细地址信息，支持多种坐标系，可输出丰富的POI信息，精确到建筑物级别。适用于物流配送、位置服务、用户轨迹分析等场景。', 'DATA', 'active', 'pay_per_use', 'v1.0.0', '高精度定位,实时解析,多坐标系支持,POI识别,跨平台兼容,批量处理', '/op/address/rev-geo', '{"supportedCoordSystems":["WGS84","GCJ02","BD09"],"precision":"building","language":["zh-CN","en-US"],"radius":50,"batchLimit":100,"includePoiInfo":true,"maxPois":10,"poiTypes":["餐饮","住宿","购物","交通","教育","医疗","金融","休闲娱乐","旅游景点","商务办公"],"includeRoadInfo":true,"includeAddressComponents":true,"includeBusinessAreas":true,"formatOptions":{"formatted":true,"short":true,"long":true},"distanceCalculation":true}', 0, 0.05, 50000, 500, 1, 0, 10);

-- 用户服务关联
-- 管理员用户服务关联
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (1, 1, 999999999, 0, 0, NOW(), 0);
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (1, 2, 999999999, 0, 0, NOW(), 0);
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (1, 3, 999999999, 0, 0, NOW(), 0);
-- 测试用户服务关联
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (2, 1, 1000, 0, 0, NOW(), 0);
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (2, 2, 1000, 0, 0, NOW(), 0);
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (2, 3, 1000, 0, 0, NOW(), 0);

SET FOREIGN_KEY_CHECKS=1;