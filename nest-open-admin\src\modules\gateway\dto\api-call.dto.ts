import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsObject, IsEnum, IsNumber, IsBoolean } from 'class-validator';

/**
 * API调用请求DTO
 */
export class ApiCallRequestDto {
  @ApiProperty({
    description: '服务代码',
    example: 'ocr-service',
  })
  @IsString()
  serviceCode: string;

  @ApiProperty({
    description: 'API路径',
    example: 'v1/recognize',
  })
  @IsString()
  path: string;

  @ApiProperty({
    description: 'HTTP方法',
    enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    example: 'POST',
  })
  @IsEnum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])
  method: string;

  @ApiPropertyOptional({
    description: '请求体数据',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  body?: any;

  @ApiPropertyOptional({
    description: '查询参数',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  query?: Record<string, any>;

  @ApiPropertyOptional({
    description: '请求头',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  headers?: Record<string, string>;
}

/**
 * API调用响应DTO
 */
export class ApiCallResponseDto {
  @ApiProperty({
    description: '响应数据',
    type: 'object',
    additionalProperties: true,
  })
  data: any;

  @ApiProperty({
    description: 'HTTP状态码',
    example: 200,
  })
  statusCode: number;

  @ApiProperty({
    description: '响应头',
    type: 'object',
    additionalProperties: true,
  })
  headers: Record<string, string>;

  @ApiProperty({
    description: '响应时间(毫秒)',
    example: 1500,
  })
  responseTime: number;

  @ApiProperty({
    description: '元数据信息',
    type: 'object',
    additionalProperties: true,
  })
  metadata: {
    serviceId: number;
    callRecordId: number;
    requestId: string;
    cached: boolean;
  };
}

/**
 * 网关状态响应DTO
 */
export class GatewayStatusDto {
  @ApiProperty({
    description: '网关状态',
    example: 'healthy',
  })
  status: string;

  @ApiProperty({
    description: '时间戳',
    example: '2024-01-01T00:00:00.000Z',
  })
  timestamp: Date;

  @ApiProperty({
    description: '当前活跃请求数',
    example: 25,
  })
  activeRequests: number;

  @ApiProperty({
    description: '最大并发请求数',
    example: 1000,
  })
  maxConcurrentRequests: number;

  @ApiProperty({
    description: '熔断器状态',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        serviceId: { type: 'string' },
        isOpen: { type: 'boolean' },
        failures: { type: 'number' },
        lastFailure: { type: 'number' },
      },
    },
  })
  circuitBreakers: Array<{
    serviceId: string;
    isOpen: boolean;
    failures: number;
    lastFailure: number;
  }>;

  @ApiProperty({
    description: '配置信息',
    type: 'object',
    additionalProperties: true,
  })
  config: {
    requestTimeout: number;
    cacheEnabled: boolean;
    rateLimitingEnabled: boolean;
  };
}

/**
 * 可用服务响应DTO
 */
export class AvailableServiceDto {
  @ApiProperty({
    description: '服务ID',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: '服务代码',
    example: 'ocr-service',
  })
  code: string;

  @ApiProperty({
    description: '服务名称',
    example: 'OCR识别服务',
  })
  name: string;

  @ApiPropertyOptional({
    description: '服务描述',
    example: '提供图像文字识别功能',
  })
  description?: string;

  @ApiProperty({
    description: '服务版本',
    example: '1.0.0',
  })
  version: string;

  @ApiPropertyOptional({
    description: '服务分类',
    example: 'AI',
  })
  category?: string;

  @ApiProperty({
    description: '服务状态',
    example: 'active',
  })
  status: string;

  @ApiPropertyOptional({
    description: '定价信息',
    type: 'object',
    additionalProperties: true,
  })
  pricing?: any;
}

/**
 * 批量API调用请求DTO
 */
export class BatchApiCallRequestDto {
  @ApiProperty({
    description: 'API调用列表',
    type: [ApiCallRequestDto],
  })
  requests: ApiCallRequestDto[];

  @ApiPropertyOptional({
    description: '是否并行执行',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  parallel?: boolean = true;

  @ApiPropertyOptional({
    description: '最大并发数',
    default: 5,
  })
  @IsOptional()
  @IsNumber()
  maxConcurrency?: number = 5;
}

/**
 * 批量API调用响应DTO
 */
export class BatchApiCallResponseDto {
  @ApiProperty({
    description: '调用结果列表',
    type: [ApiCallResponseDto],
  })
  results: (ApiCallResponseDto | { error: string })[];

  @ApiProperty({
    description: '成功数量',
    example: 8,
  })
  successCount: number;

  @ApiProperty({
    description: '失败数量',
    example: 2,
  })
  failureCount: number;

  @ApiProperty({
    description: '总执行时间(毫秒)',
    example: 3500,
  })
  totalTime: number;
}