import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStoOcrService1704268800000 implements MigrationInterface {
  name = 'AddStoOcrService1704268800000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 插入申通面单OCR服务记录
    await queryRunner.query(`
      INSERT INTO open_service (
        code,
        name,
        type,
        status,
        description,
        pricing_model,
        unit_price,
        endpoint,
        sort_order,
        created_at,
        updated_at
      ) VALUES (
        'sto-ocr',
        '申通面单识别',
        'ocr',
        'active',
        '专门针对申通物流面单的OCR识别服务，优化识别准确率和字段提取，支持申通特有字段识别如分拣码、路由码等',
        'per_request',
        0.12,
        '/v1/op/ocr/sto',
        101,
        NOW(),
        NOW()
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除申通面单OCR服务记录
    await queryRunner.query(`
      DELETE FROM open_service WHERE code = 'sto-ocr'
    `);
  }
}
