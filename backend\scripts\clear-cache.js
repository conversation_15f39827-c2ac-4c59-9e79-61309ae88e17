const Redis = require('ioredis');

/**
 * 清除缓存脚本
 */
async function clearCache() {
  console.log('开始清除缓存...');
  
  const redis = new Redis({
    host: 'localhost',
    port: 6379,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
  });

  try {
    // 清除API密钥相关的缓存
    const patterns = [
      'api_key:*',
      'api_key_secret:*',
      'api-key:*',
      'auth:nonce:*'
    ];
    
    for (const pattern of patterns) {
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        console.log(`删除 ${keys.length} 个 ${pattern} 缓存项`);
        await redis.del(...keys);
      } else {
        console.log(`没有找到 ${pattern} 缓存项`);
      }
    }
    
    console.log('✅ 缓存清除完成');
    
  } catch (error) {
    console.error('❌ 清除缓存失败:', error.message);
  } finally {
    redis.disconnect();
    console.log('Redis连接已关闭');
  }
}

// 运行脚本
clearCache().catch(console.error);
