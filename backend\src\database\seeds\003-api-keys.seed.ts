import { DataSource } from 'typeorm';
import { BaseSeed } from './base/base.seed';
import { SeedConfig, SeedResult } from './base/seed.interface';
import { ApiKeyEntity } from '../../modules/api-key/entities/api-key.entity';
import { ApiKeyStatus } from '../../modules/api-key/enums/api-key-status.enum';
import { UserEntity, RoleEnum } from '../../modules/user/entities/user.entity';
import * as crypto from 'crypto';

/**
 * API密钥种子数据
 * 为用户创建默认的API密钥
 */
export class ApiKeysSeed extends BaseSeed {
  config: SeedConfig = {
    name: 'api-keys',
    description: '为用户创建默认的API密钥',
    environments: ['development', 'test', 'production'],
    dependencies: ['users'],
    version: '1.0.0',
    priority: 3,
  };

  protected async execute(dataSource: DataSource): Promise<SeedResult> {
    const apiKeyRepository = this.getRepository(dataSource, ApiKeyEntity);
    const userRepository = this.getRepository(dataSource, UserEntity);
    
    // 获取所有用户
    const users = await userRepository.find();
    
    if (users.length === 0) {
      return {
        success: false,
        message: '未找到用户，无法创建API密钥',
      };
    }
    
    let affectedRows = 0;
    
    for (const user of users) {
      try {
        // 为每个用户创建一个默认API密钥
        const keyName = user.role === RoleEnum.ADMIN ? '管理员密钥' : '默认密钥';
        const keyPrefix = user.role === RoleEnum.ADMIN ? 'admin' : 'user';
        const apiKey = `${keyPrefix}_${user.username}_${Date.now()}`;
        const secretKey = this.generateSecretKey();
        
        // 检查API密钥是否已存在
        const existingKey = await apiKeyRepository.findOne({
          where: { 
            userId: user.id,
            name: keyName
          }
        });
        
        if (existingKey && !this.config.force) {
          this.logger.log(`用户 ${user.username} 的API密钥已存在，跳过创建`);
          continue;
        }
        
        // 生成加密的密钥（使用与API密钥服务相同的加密方式）
        const algorithm = 'aes-256-cbc';
        const key = crypto.scryptSync('api-key-secret', 'salt', 32);
        const iv = crypto.randomBytes(16);

        const cipher = crypto.createCipheriv(algorithm, key, iv);
        let encrypted = cipher.update(secretKey, 'utf8', 'hex');
        encrypted += cipher.final('hex');

        // 将iv和加密数据组合
        const encryptedSecretKey = iv.toString('hex') + ':' + encrypted;
        
        const apiKeyData = {
          key: apiKey,
          name: keyName,
          userId: user.id,
          status: ApiKeyStatus.ACTIVE,
          scopes: this.getDefaultScopes(user.role),
          allowedIps: user.role === RoleEnum.ADMIN ? [] : ['127.0.0.1'], // 管理员不限制IP
          encryptedSecretKey,
          isViewed: false,
        };
        
        if (existingKey) {
          // 更新现有密钥
          await apiKeyRepository.update({ id: existingKey.id }, apiKeyData);
          this.logger.log(`更新用户 ${user.username} 的API密钥`);
        } else {
          // 创建新密钥
          const newApiKey = apiKeyRepository.create(apiKeyData);
          await apiKeyRepository.save(newApiKey);
          this.logger.log(`为用户 ${user.username} 创建API密钥: ${apiKey}`);
          this.logger.log(`密钥Secret: ${secretKey}`);
        }
        
        affectedRows++;
        
      } catch (error) {
        this.logger.error(`为用户 ${user.username} 创建API密钥失败: ${error.message}`);
      }
    }
    
    return {
      success: true,
      message: `成功创建/更新 ${affectedRows} 个API密钥`,
      affectedRows,
    };
  }
  
  /**
   * 生成密钥Secret
   */
  private generateSecretKey(): string {
    return crypto.randomBytes(32).toString('hex');
  }
  
  /**
   * 获取默认权限范围
   */
  private getDefaultScopes(role: RoleEnum): string[] {
    if (role === RoleEnum.ADMIN) {
      return ['*']; // 管理员拥有所有权限
    } else {
      return ['ocr', 'address', 'geo', 'ai']; // 普通用户的默认权限
    }
  }
  
  /**
   * 检查是否需要执行
   */
  async shouldRun(dataSource: DataSource): Promise<boolean> {
    const apiKeyRepository = this.getRepository(dataSource, ApiKeyEntity);
    
    // 检查是否存在任何API密钥
    const keyCount = await apiKeyRepository.count();
    
    return keyCount === 0;
  }
}
