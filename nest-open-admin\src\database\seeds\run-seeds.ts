#!/usr/bin/env ts-node
// 确保路径别名可以正确解析
import 'tsconfig-paths/register';

import { DataSource } from 'typeorm';
import AppDataSource from '../../config/typeorm.config';
import { AdminUserSeed } from './admin-user.seed';
import { ServiceSeed } from './service.seed';
import { ApiKeySeed } from './api-key.seed';
import { UserServiceSeed } from './user-service.seed';
import { ApiKeyEntity } from '../../modules/api-key/entities/api-key.entity';
import { ApiKeyStatus } from '../../modules/api-key/entities/api-key.entity';
import Redis from 'ioredis';

/**
 * 将API密钥加载到Redis缓存
 */
async function cacheApiKeys(dataSource: DataSource): Promise<void> {
  console.log('🔑 开始加载API密钥到Redis缓存...');
  
  // 创建Redis连接
  const redis = new Redis({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD || undefined,
    db: parseInt(process.env.REDIS_DB || '0'),
  });
  
  try {
    // 查询所有活跃的API密钥
    const apiKeyRepository = dataSource.getRepository(ApiKeyEntity);
    const activeApiKeys = await apiKeyRepository.find({
      where: { keyStatus: ApiKeyStatus.ACTIVE },
    });
    
    if (activeApiKeys.length === 0) {
      console.log('⚠️ 没有找到活跃的API密钥');
      return;
    }
    
    console.log(`📋 找到 ${activeApiKeys.length} 个活跃的API密钥`);
    
    // 批量加载到缓存
    let cachedCount = 0;
    for (const apiKey of activeApiKeys) {
      try {
        console.log(`🔄 处理API密钥 #${apiKey.id}, API密钥: ${apiKey.apiKey.substring(0, 10)}...`);
        
        const cacheData = {
          id: apiKey.id,
          userId: apiKey.userId,
          serviceId: apiKey.serviceId,
          name: apiKey.name,
          status: apiKey.keyStatus,
          keyType: apiKey.keyType,
          description: apiKey.description,
          permissions: apiKey.permissions,
          expiresAt: apiKey.expiresAt,
          lastUsedAt: apiKey.lastUsedAt,
          createdAt: apiKey.createdAt,
          updatedAt: apiKey.updatedAt,
        };
        
        const cacheKey = `api_key:${apiKey.apiKey}`;
        await redis.setex(cacheKey, 86400, JSON.stringify(cacheData)); // 默认缓存24小时
        
        // 添加辅助索引，方便通过ID查找API密钥
        await redis.setex(`api_key_id:${apiKey.id}`, 86400, apiKey.apiKey);
        
        console.log(`✅ API密钥 #${apiKey.id} 已缓存, 缓存键: ${cacheKey}`);
        
        // 验证缓存是否成功
        const cachedValue = await redis.get(cacheKey);
        if (cachedValue) {
          console.log(`✓ 缓存验证成功: ${cacheKey}`);
        } else {
          console.log(`❌ 缓存验证失败: ${cacheKey}`);
        }
        
        cachedCount++;
      } catch (error) {
        console.error(`❌ 缓存API密钥 #${apiKey.id} 失败:`, error.message);
      }
    }
    
    console.log(`✅ 成功加载 ${cachedCount}/${activeApiKeys.length} 个API密钥到缓存`);
  } catch (error) {
    console.error('❌ 加载API密钥到缓存失败:', error);
  } finally {
    // 关闭Redis连接
    await redis.quit();
  }
}

async function runSeeds() {
  try {
    console.log('🌱 开始运行种子数据...');
    
    // 初始化数据源
    await AppDataSource.initialize();
    console.log('✅ 数据库连接成功');

    // 运行种子数据
    const seeds = [
      new AdminUserSeed(),
      new ServiceSeed(),
      new ApiKeySeed(),
      new UserServiceSeed()
    ];

    for (const seed of seeds) {
      console.log(`🌱 运行种子: ${seed.constructor.name}`);
      await seed.run(AppDataSource);
      console.log(`✅ 完成种子: ${seed.constructor.name}`);
    }
    
    console.log('🎉 所有种子数据运行完成!');
    
    // 加载API密钥到Redis缓存
    console.log('🚀 开始加载API密钥到缓存...');
    await cacheApiKeys(AppDataSource);
    
  } catch (error) {
    console.error('❌ 种子数据运行失败:', error);
    process.exit(1);
  } finally {
    await AppDataSource.destroy();
  }
}

runSeeds();