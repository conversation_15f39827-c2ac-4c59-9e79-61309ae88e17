import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Repository } from 'typeorm';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { RechargeService } from '../services/recharge.service';
import { OrderEntity } from '../entities/order.entity';
import { OrderItemEntity } from '../entities/order-item.entity';
import { UserService } from '../../user/user.service';
import { OrderType, OrderStatus, PaymentMethod } from '../enums/order.enum';
import {
  createTestUser,
  createTestRechargeData,
} from './order.test-config';

describe('RechargeService', () => {
  let service: RechargeService;
  let orderRepository: Repository<OrderEntity>;
  let orderItemRepository: Repository<OrderItemEntity>;
  let userService: UserService;
  let eventEmitter: EventEmitter2;

  const mockUser = { id: 1, ...createTestUser() };
  const mockRechargeOrder = {
    id: 1,
    orderNo: 'ORD20231201001',
    userId: 1,
    orderType: OrderType.RECHARGE,
    status: OrderStatus.PENDING,
    totalAmount: 100.00,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RechargeService,
        {
          provide: getRepositoryToken(OrderEntity),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            findOne: jest.fn(),
            find: jest.fn(),
            createQueryBuilder: jest.fn(() => ({
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              orderBy: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              take: jest.fn().mockReturnThis(),
              getManyAndCount: jest.fn(),
              select: jest.fn().mockReturnThis(),
              addSelect: jest.fn().mockReturnThis(),
              groupBy: jest.fn().mockReturnThis(),
              getRawMany: jest.fn(),
              getRawOne: jest.fn(),
            })),
            count: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(OrderItemEntity),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: UserService,
          useValue: {
            findById: jest.fn(),
          },
        },
        {
          provide: EventEmitter2,
          useValue: {
            emit: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<RechargeService>(RechargeService);
    orderRepository = module.get<Repository<OrderEntity>>(getRepositoryToken(OrderEntity));
    orderItemRepository = module.get<Repository<OrderItemEntity>>(getRepositoryToken(OrderItemEntity));
    userService = module.get<UserService>(UserService);
    eventEmitter = module.get<EventEmitter2>(EventEmitter2);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createRechargeOrder', () => {
    const rechargeData = createTestRechargeData();

    it('应该成功创建充值订单', async () => {
      // Arrange
      jest.spyOn(userService, 'findById').mockResolvedValue(mockUser as any);
      jest.spyOn(orderRepository, 'create').mockReturnValue(mockRechargeOrder as any);
      jest.spyOn(orderRepository, 'save').mockResolvedValue(mockRechargeOrder as any);
      jest.spyOn(orderItemRepository, 'create').mockReturnValue({ id: 1 } as any);
      jest.spyOn(orderItemRepository, 'save').mockResolvedValue({ id: 1 } as any);

      // Act
      const result = await service.createRechargeOrder(1, rechargeData);

      // Assert
      expect(userService.findById).toHaveBeenCalledWith(1);
      expect(orderRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 1,
          orderType: OrderType.RECHARGE,
          totalAmount: rechargeData.amount,
        })
      );
      expect(orderItemRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          itemName: '账户充值',
          totalPrice: rechargeData.amount,
        })
      );
      expect(eventEmitter.emit).toHaveBeenCalledWith('order.created', expect.any(Object));
      expect(result).toEqual(mockRechargeOrder);
    });

    it('当用户不存在时应该抛出异常', async () => {
      // Arrange
      jest.spyOn(userService, 'findById').mockRejectedValue(new NotFoundException('用户不存在'));

      // Act & Assert
      await expect(service.createRechargeOrder(1, rechargeData))
        .rejects.toThrow(BadRequestException);
    });

    it('当充值金额小于最小值时应该抛出异常', async () => {
      // Arrange
      const invalidData = { ...rechargeData, amount: 0.5 };
      jest.spyOn(userService, 'findById').mockResolvedValue(mockUser as any);

      // Act & Assert
      await expect(service.createRechargeOrder(1, invalidData))
        .rejects.toThrow(BadRequestException);
    });

    it('当充值金额大于最大值时应该抛出异常', async () => {
      // Arrange
      const invalidData = { ...rechargeData, amount: 15000 };
      jest.spyOn(userService, 'findById').mockResolvedValue(mockUser as any);

      // Act & Assert
      await expect(service.createRechargeOrder(1, invalidData))
        .rejects.toThrow(BadRequestException);
    });

    it('当充值金额格式不正确时应该抛出异常', async () => {
      // Arrange
      const invalidData = { ...rechargeData, amount: 100.123 };
      jest.spyOn(userService, 'findById').mockResolvedValue(mockUser as any);

      // Act & Assert
      await expect(service.createRechargeOrder(1, invalidData))
        .rejects.toThrow(BadRequestException);
    });
  });

  describe('getRechargeConfig', () => {
    it('应该返回充值配置', async () => {
      // Act
      const result = await service.getRechargeConfig();

      // Assert
      expect(result).toHaveProperty('presetAmounts');
      expect(result).toHaveProperty('minAmount', 1);
      expect(result).toHaveProperty('maxAmount', 10000);
      expect(result).toHaveProperty('promotions');
      expect(Array.isArray(result.presetAmounts)).toBe(true);
      expect(Array.isArray(result.promotions)).toBe(true);
    });
  });

  describe('getUserRechargeRecords', () => {
    it('应该返回用户充值记录', async () => {
      // Arrange
      const mockRecords = [mockRechargeOrder];
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([mockRecords, 1]),
      };
      
      jest.spyOn(orderRepository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder as any);

      // Act
      const result = await service.getUserRechargeRecords(1, 1, 20);

      // Assert
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('order.userId = :userId', { userId: 1 });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('order.orderType = :orderType', { orderType: OrderType.RECHARGE });
      expect(result).toHaveProperty('items');
      expect(result).toHaveProperty('meta');
      expect(result.items).toHaveLength(1);
      expect(result.meta.totalItems).toBe(1);
    });
  });

  describe('getRechargeStats', () => {
    it('应该返回充值统计信息', async () => {
      // Arrange
      jest.spyOn(orderRepository, 'count').mockResolvedValue(10);
      
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ total: '1000' }),
        addSelect: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          { paymentMethod: 'alipay', count: '5', amount: '500' },
          { paymentMethod: 'wechat', count: '3', amount: '300' },
        ]),
      };
      
      jest.spyOn(orderRepository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder as any);

      // Act
      const result = await service.getRechargeStats();

      // Assert
      expect(result).toHaveProperty('totalRecharges');
      expect(result).toHaveProperty('totalAmount');
      expect(result).toHaveProperty('todayRecharges');
      expect(result).toHaveProperty('todayAmount');
      expect(result).toHaveProperty('averageAmount');
      expect(result).toHaveProperty('byPaymentMethod');
      expect(result).toHaveProperty('byAmountRange');
      expect(typeof result.totalRecharges).toBe('number');
      expect(typeof result.totalAmount).toBe('number');
    });
  });
});
