const http = require('http');

// 测试修复后的短信验证码登录
async function testFixedLogin() {
  const phone = '18501791550';
  
  console.log('=== 测试修复后的短信验证码登录 ===');
  console.log(`手机号: ${phone}`);
  
  // 1. 先发送验证码
  console.log('\n1. 发送短信验证码...');
  const smsResult = await sendRequest('/v1/auth/send-sms-code', 'POST', {
    phone: phone,
    type: 'login',
    securityVerification: {
      type: 'behavior',
      level: 2,
      behaviorPattern: { verified: true },
      verificationTimestamp: Date.now()
    }
  });
  
  console.log(`发送验证码结果: ${smsResult.success ? '成功' : '失败'}`);
  if (!smsResult.success) {
    console.log('错误:', smsResult.data);
    return;
  }
  
  // 等待用户输入验证码
  console.log('\n请查看后端日志获取验证码，然后手动输入验证码进行测试');
  console.log('验证码格式示例: 195070');
  
  // 这里使用一个示例验证码，实际使用时需要从日志中获取
  const smsCode = '143839'; // 从后端日志中获取的验证码
  
  console.log(`\n2. 使用验证码登录: ${smsCode}`);
  
  // 2. 使用验证码登录
  const loginResult = await sendRequest('/v1/auth/login', 'POST', {
    phone: phone,
    smsCode: smsCode,
    isSilentRegister: true, // 启用静默注册
    securityVerification: {
      type: 'behavior',
      level: 2,
      behaviorPattern: { verified: true },
      verificationTimestamp: Date.now()
    }
  });
  
  console.log('\n登录结果:');
  console.log(`HTTP状态码: ${loginResult.statusCode}`);
  console.log(`成功: ${loginResult.success}`);
  console.log('响应数据:', JSON.stringify(loginResult.data, null, 2));
  
  if (loginResult.success) {
    console.log('\n🎉 登录成功！静默注册功能正常工作！');
  } else {
    console.log('\n❌ 登录失败，需要进一步调试');
  }
}

// 发送HTTP请求的辅助函数
function sendRequest(path, method, data) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    
    const options = {
      hostname: '127.0.0.1',
      port: 8088,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve({
            success: res.statusCode === 200 && jsonData.success,
            statusCode: res.statusCode,
            data: jsonData
          });
        } catch (e) {
          resolve({
            success: false,
            statusCode: res.statusCode,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', (e) => {
      reject(e);
    });
    
    req.write(postData);
    req.end();
  });
}

// 运行测试
testFixedLogin().catch(console.error);
