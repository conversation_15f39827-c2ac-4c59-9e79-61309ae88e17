import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsEmail, IsNotEmpty, IsObject, IsOptional, IsPhoneNumber, IsString, ValidateIf } from 'class-validator';

export class LoginDto {
  @ValidateIf(o => o.email) // 只有当提供了邮箱时才验证邮箱格式
  @IsEmail({}, { message: '邮箱格式不正确' })
  @ApiPropertyOptional({ description: '邮箱' })
  email?: string;

  @ValidateIf(o => o.phone) // 只有当提供了手机号时才验证手机号格式
  @IsPhoneNumber('CN', { message: '手机号格式不正确' })
  @ApiPropertyOptional({ description: '手机号' })
  phone?: string;

  @ValidateIf(o => o.username) // 只有当提供了用户名时才验证
  @IsString()
  @ApiPropertyOptional({ description: '用户名' })
  username?: string;

  @ValidateIf(o => o.password) // 只有当提供了密码时才验证密码
  @IsString()
  @IsNotEmpty({ message: '密码不能为空' })
  @ApiPropertyOptional({ description: '密码' })
  password?: string;

  @ValidateIf(o => o.emailCode) // 只有当提供了邮箱验证码时才验证
  @IsString()
  @ApiPropertyOptional({ description: '邮箱验证码' })
  emailCode?: string;

  @ValidateIf(o => o.smsCode) // 只有当提供了短信验证码时才验证
  @IsString()
  @ApiPropertyOptional({ description: '短信验证码' })
  smsCode?: string;
  
  @IsOptional()
  @IsObject()
  @ApiPropertyOptional({ description: '安全验证数据' })
  securityVerification?: {
    level: number;
    behaviorPattern: any;
    verificationTimestamp: number;
    type?: string;
  };
  
  @IsOptional()
  @IsBoolean()
  @ApiPropertyOptional({ description: '是否为快捷登录(静默注册)', default: false })
  isSilentRegister?: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiPropertyOptional({ description: '是否记住登录状态', default: false })
  rememberMe?: boolean;
}