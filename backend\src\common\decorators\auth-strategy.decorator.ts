import { SetMetadata } from '@nestjs/common';

/**
 * 认证策略类型
 * 用于明确指定接口或控制器需要的认证方式
 */
export const AUTH_STRATEGY_KEY = 'auth_strategy';

/**
 * 认证策略枚举
 * JWT: 仅使用JWT令牌认证
 * API_KEY: 仅使用API密钥认证
 * NONE: 不需要认证（如SSE接口）
 */
export enum AuthStrategy {
  JWT = 'jwt',
  API_KEY = 'api_key',
  NONE = 'none',
}

/**
 * 认证策略装饰器
 * 用于指定接口或控制器使用的认证方式
 * @param strategy 认证策略
 * @returns 装饰器
 * @example
 * @UseAuthStrategy(AuthStrategy.API_KEY)
 * export class OcrController {}
 */
export const UseAuthStrategy = (strategy: AuthStrategy) => {
  return SetMetadata(AUTH_STRATEGY_KEY, strategy);
};