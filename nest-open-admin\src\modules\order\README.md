# 订单模块 (Order Module)

## 概述

订单模块负责处理系统中的订单管理功能，包括订单的创建、查询、更新和删除操作。支持两种订单类型：充值订单和服务购买订单。

## 功能特性

### 订单类型
- **充值订单 (RECHARGE)**: 用户账户余额充值
- **服务购买订单 (SERVICE_PURCHASE)**: 购买特定服务的调用次数

### 支付状态
- **待支付 (PENDING)**: 订单已创建，等待支付
- **已完成 (COMPLETED)**: 支付成功，订单完成
- **支付失败 (FAILED)**: 支付失败
- **已退款 (REFUNDED)**: 订单已退款

## API 接口

### 创建订单
```http
POST /op/order
```

**请求参数:**
```json
{
  "type": "SERVICE_PURCHASE",
  "amount": 100.00,
  "userId": 1,
  "serviceId": 1,
  "purchaseCount": 100
}
```

### 获取订单列表
```http
GET /op/order?page=1&limit=10&type=SERVICE_PURCHASE&status=PENDING
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10, 最大: 100)
- `orderNumber`: 订单号搜索
- `type`: 订单类型筛选
- `status`: 支付状态筛选
- `userId`: 用户ID筛选
- `serviceId`: 服务ID筛选
- `startDate`: 开始日期
- `endDate`: 结束日期

### 获取订单详情
```http
GET /op/order/:id
```

### 更新订单
```http
PATCH /op/order/:id
```

**请求参数:**
```json
{
  "amount": 150.00,
  "purchaseCount": 150,
  "status": "COMPLETED"
}
```

### 删除订单
```http
DELETE /op/order/:id
```

## 业务规则

### 创建订单
1. 必须验证用户存在
2. 购买服务订单时必须提供服务ID和购买次数
3. 自动生成唯一订单号
4. 默认状态为待支付

### 更新订单
1. 已完成或已退款的订单不能修改
2. 只能更新金额、购买次数和支付状态

### 删除订单
1. 只有待支付和失败的订单可以删除
2. 已完成和已退款的订单不能删除

## 数据模型

### OrderEntity
```typescript
{
  id: number;                    // 订单ID
  orderNumber: string;           // 订单号
  type: OrderType;              // 订单类型
  amount: number;               // 订单金额
  purchaseCount?: number;       // 购买次数
  status: PaymentStatus;        // 支付状态
  user: UserEntity;             // 关联用户
  service?: ServiceEntity;      // 关联服务
  createdAt: Date;              // 创建时间
  updatedAt: Date;              // 更新时间
}
```

## 依赖关系

- **UserModule**: 用于验证用户信息
- **ServiceModule**: 用于验证服务信息
- **TypeORM**: 数据库操作

## 错误处理

模块遵循全局异常处理规范，使用标准的HTTP状态码和错误消息：

- `400 Bad Request`: 请求参数错误
- `404 Not Found`: 资源不存在
- `409 Conflict`: 业务规则冲突
- `500 Internal Server Error`: 服务器内部错误

## 日志记录

所有关键操作都会记录日志，包括：
- 订单创建成功
- 订单更新成功
- 订单删除成功
- 错误信息记录