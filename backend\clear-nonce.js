const Redis = require('ioredis');

async function clearNonce() {
  const redis = new Redis({ 
    host: 'localhost', 
    port: 6379, 
    db: 0 
  });
  
  try {
    const keys = await redis.keys('nonce:*');
    if (keys.length > 0) {
      await redis.del(...keys);
      console.log('清除了', keys.length, '个Nonce缓存项');
    } else {
      console.log('没有找到Nonce缓存项');
    }
    console.log('✅ Nonce缓存清除完成');
  } catch (error) {
    console.error('清除失败:', error.message);
  } finally {
    redis.disconnect();
  }
}

clearNonce();
