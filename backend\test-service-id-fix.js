// 测试修复后的服务ID传递
const axios = require('axios');
const crypto = require('crypto');

// 测试配置
const config = {
  baseURL: 'http://localhost:3000',
  apiKey: 'ak-579b27759508f152525a4e5a567efd5a',
  secretKey: 'sk-579b27759508f152525a4e5a567efd5a'
};

// 生成签名
function generateSignature(method, path, query, body, timestamp) {
  const nonce = crypto.randomBytes(16).toString('hex');
  
  // 构建规范请求
  const queryString = Object.keys(query || {})
    .sort()
    .map(key => `${key}=${encodeURIComponent(query[key])}`)
    .join('&');
  
  const bodyString = body ? JSON.stringify(body) : '';
  
  const canonical = [
    method.toUpperCase(),
    path,
    queryString,
    bodyString,
    timestamp
  ].join('\n');
  
  const signature = crypto
    .createHmac('sha256', config.secretKey)
    .update(canonical)
    .digest('hex');
  
  return { signature, nonce };
}

// 测试地址解析接口（POST请求，serviceId在请求体中）
async function testAddressExtract() {
  console.log('\n=== 测试地址解析接口（POST请求） ===');
  
  const path = '/v1/op/address/extract';
  const method = 'POST';
  const query = { mode: 'async' };
  const body = {
    text: '张三，13800138000，广东省深圳市南山区科技园',
    serviceId: 5 // 在请求体中传递服务ID
  };
  
  const timestamp = Math.floor(Date.now() / 1000).toString();
  const { signature, nonce } = generateSignature(method, path, query, body, timestamp);
  
  try {
    const response = await axios({
      method,
      url: `${config.baseURL}${path}`,
      params: query,
      data: body,
      headers: {
        'Content-Type': 'application/json',
        'X-API-KEY': config.apiKey,
        'X-Timestamp': timestamp,
        'X-Nonce': nonce,
        'X-Signature': signature
      }
    });
    
    console.log('✅ 请求成功');
    console.log('状态码:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('❌ 请求失败');
    console.log('错误信息:', error.response?.data || error.message);
    console.log('状态码:', error.response?.status);
  }
}

// 测试地理编码接口（GET请求，serviceId在查询参数中）
async function testGeoReverse() {
  console.log('\n=== 测试逆地理编码接口（GET请求） ===');
  
  const path = '/v1/op/geo/reverse';
  const method = 'GET';
  const query = {
    lat: 22.543096,
    lng: 114.057865,
    serviceId: 7 // 在查询参数中传递服务ID
  };
  const body = null;
  
  const timestamp = Math.floor(Date.now() / 1000).toString();
  const { signature, nonce } = generateSignature(method, path, query, body, timestamp);
  
  try {
    const response = await axios({
      method,
      url: `${config.baseURL}${path}`,
      params: query,
      headers: {
        'X-API-KEY': config.apiKey,
        'X-Timestamp': timestamp,
        'X-Nonce': nonce,
        'X-Signature': signature
      }
    });
    
    console.log('✅ 请求成功');
    console.log('状态码:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('❌ 请求失败');
    console.log('错误信息:', error.response?.data || error.message);
    console.log('状态码:', error.response?.status);
  }
}

// 运行测试
async function runTests() {
  console.log('开始测试修复后的服务ID传递...');
  
  await testAddressExtract();
  await testGeoReverse();
  
  console.log('\n测试完成！');
}

runTests().catch(console.error);
