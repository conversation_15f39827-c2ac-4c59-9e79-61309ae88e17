<template>
  <div class="dashboard-page">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">仪表盘</h1>
        <p class="page-subtitle">欢迎回来，{{ userStore.userInfo?.username }}！这里是您的使用概览。</p>
      </div>
      <div class="header-right">
        <div class="header-actions">
          <el-tooltip content="最后更新时间" placement="bottom">
            <span class="last-updated" v-if="lastUpdated">
              {{ formatTime(lastUpdated) }}
            </span>
          </el-tooltip>
          <el-button :icon="Refresh" :loading="loading" @click="handleRefresh" circle />
          <el-dropdown @command="handleTimeRangeChange">
            <el-button>
              {{ getTimeRangeLabel(currentTimeRange) }}
              <el-icon class="el-icon--right">
                <ArrowDown />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="7d">最近7天</el-dropdown-item>
                <el-dropdown-item command="30d">最近30天</el-dropdown-item>
                <el-dropdown-item command="90d">最近90天</el-dropdown-item>
                <el-dropdown-item command="1y">最近1年</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid" v-loading="loading">
      <div class="stat-card" v-for="(card, key) in displayStats" :key="key">
        <div class="stat-icon">
          <el-icon size="32" :color="card.color">
            <component :is="getIconComponent(card.icon)" />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatStatValue(card.value, card.unit) }}</div>
          <div class="stat-label">{{ card.title }}</div>
          <div v-if="card.trend"
            :class="['stat-change', card.trend.type === 'up' ? 'positive' : card.trend.type === 'down' ? 'negative' : 'neutral']">
            {{ card.trend.type === 'up' ? '+' : '' }}{{ card.trend.value }}% {{ card.trend.period }}
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <el-row :gutter="24" class="charts-section">
      <!-- 使用趋势图 -->
      <el-col :lg="16" :md="24">
        <div class="chart-card">
          <div class="card-header">
            <h3>使用趋势</h3>
            <el-radio-group v-model="trendPeriod" size="small">
              <el-radio-button label="7d">7天</el-radio-button>
              <el-radio-button label="30d">30天</el-radio-button>
              <el-radio-button label="90d">90天</el-radio-button>
            </el-radio-group>
          </div>
          <div class="chart-content">
            <div ref="trendChartRef" class="chart" style="height: 300px;"></div>
          </div>
        </div>
      </el-col>

      <!-- 服务分布图 -->
      <el-col :lg="8" :md="24">
        <div class="chart-card">
          <div class="card-header">
            <h3>服务使用分布</h3>
          </div>
          <div class="chart-content">
            <div ref="serviceChartRef" class="chart" style="height: 300px;"></div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 系统通知 -->
    <div class="notifications-section" v-if="notifications.length > 0">
      <h3>系统通知</h3>
      <div class="notifications-list">
        <el-alert v-for="notification in notifications" :key="notification.id" :title="notification.title"
          :description="notification.content" :type="notification.type" :closable="true"
          @close="dismissNotification(notification.id)" class="notification-item" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  DataAnalysis,
  Money,
  Key,
  Warning,
  Plus,
  Promotion,
  Document,
  ChatDotRound,
  Check,
  Close,
  Clock,
  Refresh,
  Setting,
  ArrowDown
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { useServiceStore } from '@/stores/service'
import { useDashboardStore } from '@/stores/dashboard'
import type { TimeRange } from '@/types/dashboard'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import * as echarts from 'echarts'

dayjs.extend(relativeTime)

const router = useRouter()
const userStore = useUserStore()
const serviceStore = useServiceStore()
const dashboardStore: any = useDashboardStore()

// 使用新的store结构
const {
  loading,
  overview,
  settings,
  currentTimeRange,
  lastUpdated,
  statsCards,
  usageTrendData,
  serviceDistributionData,
  quickActions,
  recentActivitiesData,
  // 向后兼容的旧数据
  stats,
  usageTrend,
  serviceDistribution,
  recentActivities,
  notifications,
  // 方法
  fetchOverview,
  refreshAll,
  updateSettings,
  init
} = dashboardStore

// 图表实例
let trendChart: echarts.ECharts | null = null
let serviceChart: echarts.ECharts | null = null

const trendPeriod = ref<TimeRange>('30d')
const trendChartRef = ref<HTMLElement>()
const serviceChartRef = ref<HTMLElement>()

// 计算属性
const displayStats = computed(() => {
  // 优先使用新的statsCards，如果没有则使用旧的stats
  if (statsCards && statsCards.value && Object.keys(statsCards.value).length > 0) {
    // statsCards.value 是一个包含 totalCalls, totalAmount, activeKeys, errorRate 的对象
    // 每个属性都是 StatCard 类型，直接返回这个对象
    return statsCards.value
  }

  // 向后兼容：使用旧的stats数据
  const statsValue = stats.value || {}
  return {
    totalCalls: {
      title: '总调用次数',
      value: statsValue?.totalCalls || 0,
      unit: 'number',
      icon: 'DataAnalysis',
      color: '#409eff',
      trend: statsValue?.callsGrowth ? {
        type: statsValue.callsGrowth > 0 ? 'up' : 'down',
        value: Math.abs(statsValue.callsGrowth),
        period: '较上期'
      } : undefined
    },
    totalSpent: {
      title: '总消费金额',
      value: statsValue?.totalSpent || 0,
      unit: 'currency',
      icon: 'Money',
      color: '#67c23a',
      trend: statsValue?.spentGrowth ? {
        type: statsValue.spentGrowth > 0 ? 'up' : 'down',
        value: Math.abs(statsValue.spentGrowth),
        period: '较上期'
      } : undefined
    },
    activeKeys: {
      title: '活跃API密钥',
      value: statsValue?.activeKeys || 0,
      unit: 'number',
      icon: 'Key',
      color: '#e6a23c',
      trend: undefined
    },
    errorRate: {
      title: '错误率',
      value: statsValue?.errorRate || 0,
      unit: 'percentage',
      icon: 'Warning',
      color: '#f56c6c',
      trend: statsValue?.errorChange ? {
        type: statsValue.errorChange > 0 ? 'up' : 'down',
        value: Math.abs(statsValue.errorChange),
        period: '较上期'
      } : undefined
    }
  }
})

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 格式化统计值
const formatStatValue = (value: number, unit?: string) => {
  switch (unit) {
    case 'currency':
      return `¥${formatNumber(value)}`
    case 'percentage':
      return `${value}%`
    case 'number':
    default:
      return formatNumber(value)
  }
}

// 格式化时间
const formatTime = (time: string | Date) => {
  return dayjs(time).fromNow()
}

// 获取图标组件
const getIconComponent = (iconName: string) => {
  const iconMap: Record<string, any> = {
    DataAnalysis,
    Money,
    Key,
    Warning,
    Plus,
    Promotion,
    Document,
    ChatDotRound,
    Check,
    Close,
    Clock
  }
  return iconMap[iconName] || DataAnalysis
}

// 获取时间范围标签
const getTimeRangeLabel = (range: TimeRange) => {
  const labels: Record<TimeRange, string> = {
    '7d': '最近7天',
    '30d': '最近30天',
    '90d': '最近90天',
    '1y': '最近1年'
  }
  return labels[range] || '最近30天'
}

// 获取活动图标
const getActivityIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    api_call: DataAnalysis,
    api_key: Key,
    billing: Money,
    system: Warning
  }
  return iconMap[type] || Clock
}

// 获取活动颜色
const getActivityColor = (type: string) => {
  const colorMap: Record<string, string> = {
    api_call: '#409eff',
    api_key: '#67c23a',
    billing: '#e6a23c',
    system: '#f56c6c'
  }
  return colorMap[type] || '#909399'
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '成功': 'success',
    '完成': 'success',
    '失败': 'danger',
    '处理中': 'warning',
    '待处理': 'info'
  }
  return typeMap[status] || 'info'
}

// 事件处理
const handleRefresh = async () => {
  try {
    // await refreshAll()
    ElMessage.success('开发中……')
  } catch (error) {
    console.error('刷新失败:', error)
    ElMessage.error('刷新失败，请稍后重试')
  }
}

const handleTimeRangeChange = async (range: TimeRange) => {
  try {
    if (updateSettings) {
      // await updateSettings({ defaultTimeRange: range })
    }
    // await fetchOverview(range)
    ElMessage.success(`已切换到${getTimeRangeLabel(range)}`)
  } catch (error) {
    console.error('切换时间范围失败:', error)
    ElMessage.error('切换失败，请稍后重试')
  }
}

// 创建API密钥
const createApiKey = () => {
  router.push('/console/api-keys')
}

// 前往API测试
const goToPlayground = () => {
  router.push('/console/playground')
}

// 查看文档
const viewDocs = () => {
  window.open('https://docs.example.com', '_blank')
}

// 联系支持
const contactSupport = () => {
  ElMessage.info('技术支持功能开发中...')
}

// 查看所有活动
const viewAllActivities = () => {
  router.push('/console/activities')
}

// 关闭通知
const dismissNotification = (id: string) => {
  // 这里应该调用API来标记通知为已读
  ElMessage.success('通知已关闭')
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  initTrendChart()
  initServiceChart()
}

// 初始化使用趋势图表
const initTrendChart = () => {
  if (!trendChartRef.value) return

  trendChart = echarts.init(trendChartRef.value)

  // 确保数据存在，提供默认值
  const trendData = usageTrendData.value || {
    dates: [],
    series: [
      { name: 'calls', data: [] },
      { name: 'amount', data: [] }
    ]
  }

  const option = {
    title: {
      text: '使用趋势',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal',
        color: '#303133'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['调用次数', '消费金额'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: trendData.dates || []
    },
    yAxis: [
      {
        type: 'value',
        name: '调用次数',
        position: 'left',
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '消费金额',
        position: 'right',
        axisLabel: {
          formatter: '¥{value}'
        }
      }
    ],
    series: [
      {
        name: '调用次数',
        type: 'line',
        smooth: true,
        data: trendData.series?.find((s: any) => s.name === 'calls')?.data || [],
        itemStyle: {
          color: '#409eff'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ])
        }
      },
      {
        name: '消费金额',
        type: 'line',
        smooth: true,
        yAxisIndex: 1,
        data: trendData.series?.find((s: any) => s.name === 'amount')?.data || [],
        itemStyle: {
          color: '#67c23a'
        }
      }
    ]
  }

  trendChart.setOption(option)
}

// 初始化服务分布图表
const initServiceChart = () => {
  if (!serviceChartRef.value) return

  serviceChart = echarts.init(serviceChartRef.value)

  // 确保数据存在，提供默认值
  const distributionData = serviceDistributionData.value || []
  const data = distributionData.map((item: any) => ({
    name: item.name,
    value: item.value
  })) || []

  const option = {
    title: {
      text: '服务使用分布',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal',
        color: '#303133'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: data.map((item: any) => item.name)
    },
    series: [
      {
        name: '服务使用',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data
      }
    ]
  }

  serviceChart.setOption(option)
}

// 更新图表数据
const updateCharts = () => {
  if (trendChart && usageTrendData.value) {
    const trendData = usageTrendData.value
    trendChart.setOption({
      xAxis: {
        data: trendData.dates || []
      },
      series: [
        {
          data: trendData.series?.find((s: any) => s.name === 'calls')?.data || []
        },
        {
          data: trendData.series?.find((s: any) => s.name === 'amount')?.data || []
        }
      ]
    })
  }

  if (serviceChart && serviceDistributionData.value) {
    const distributionData = serviceDistributionData.value
    if (distributionData && distributionData.length > 0) {
      const data = distributionData.map((item: any) => ({
        name: item.name,
        value: item.value
      }))

      serviceChart.setOption({
        legend: {
          data: data.map((item: any) => item.name)
        },
        series: [{
          data: data
        }]
      })
    }
  }
}

// 响应式调整图表大小
const resizeCharts = () => {
  trendChart?.resize()
  serviceChart?.resize()
}

// 加载仪表盘数据
const loadDashboardDataWrapper = async () => {
  try {
    // 优先使用新的初始化方法
    if (init) {
      // await init()
    } else {
      // 向后兼容：使用旧的加载方法
      await dashboardStore.loadDashboardData?.()
    }
  } catch (error) {
    console.error('加载仪表盘数据失败:', error)
    ElMessage.error('加载数据失败，请刷新页面重试')
  }
}

// 初始化默认数据
const initDefaultData = () => {
  // 确保关键数据有默认值，避免空值错误
  if (!usageTrendData.value) {
    usageTrendData.value = {
      dates: [],
      series: [
        { name: 'calls', data: [] },
        { name: 'amount', data: [] }
      ]
    }
  }
  
  if (!serviceDistributionData.value) {
    serviceDistributionData.value = []
  }
  
  if (!stats.value) {
    stats.value = {
      totalCalls: 0,
      totalSpent: 0,
      activeKeys: 0,
      errorRate: 0
    }
  }
}

// 监听数据变化，更新图表
watch([usageTrendData, serviceDistributionData], () => {
  updateCharts()
}, { deep: true })

// 监听窗口大小变化
const handleResize = () => {
  resizeCharts()
}

// 页面挂载时加载数据
onMounted(async () => {
  // 先初始化默认数据，避免空值错误
  initDefaultData()
  
  await loadDashboardDataWrapper()
  await initCharts()
  window.addEventListener('resize', handleResize)
})

// 页面卸载时清理
onUnmounted(() => {
  // 停止自动刷新等清理工作
  if (dashboardStore.stopAutoRefresh) {
    dashboardStore.stopAutoRefresh()
  }

  // 清理图表实例
  trendChart?.dispose()
  serviceChart?.dispose()

  // 移除事件监听
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.dashboard-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.last-updated {
  font-size: 12px;
  color: #909399;
  margin-right: 8px;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #606266;
  margin: 0;
  font-size: 1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  padding: 12px;
  background: #f5f7fa;
  border-radius: 8px;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #67c23a;
}

.stat-change.negative {
  color: #f56c6c;
}

.stat-change.neutral {
  color: #909399;
}

.charts-section {
  margin-bottom: 32px;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.chart {
  background: #f8f9fa;
  border-radius: 8px;
}

.bottom-section {
  margin-bottom: 32px;
}

.quick-actions-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.quick-actions-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 20px 0;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px 12px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
}

.action-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.action-item span {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.recent-activity-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border: 1px solid #f0f2f5;
  border-radius: 8px;
  transition: background-color 0.3s;
}

.activity-item:hover {
  background: #f8f9fa;
}

.activity-icon {
  padding: 8px;
  background: #f5f7fa;
  border-radius: 6px;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.activity-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 4px;
}

.activity-time {
  color: #909399;
  font-size: 12px;
}

.activity-status {
  flex-shrink: 0;
}

.notifications-section {
  margin-bottom: 32px;
}

.notifications-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.notification-item {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .chart-card,
  .quick-actions-card,
  .recent-activity-card {
    padding: 16px;
  }
}
</style>