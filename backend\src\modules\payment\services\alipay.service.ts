import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PaymentMethod } from '../../order/enums/order.enum';
import {
  IPaymentProvider,
  PaymentRequest,
  PaymentResult,
  CallbackVerifyResult,
  PaymentQueryResult,
  RefundRequest,
  RefundResult,
} from '../interfaces/payment-provider.interface';

@Injectable()
export class AlipayService implements IPaymentProvider {
  private readonly logger = new Logger(AlipayService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * 获取支付方式
   */
  getPaymentMethod(): PaymentMethod {
    return PaymentMethod.ALIPAY;
  }

  /**
   * 创建支付宝支付
   */
  async createPayment(request: PaymentRequest): Promise<PaymentResult> {
    try {
      this.logger.log(`创建支付宝支付: ${request.paymentNo}, 金额: ${request.amount}`);

      // 获取支付宝配置
      const config = this.getAlipayConfig();
      
      // 构建支付参数
      const params = {
        app_id: config.appId,
        method: 'alipay.trade.page.pay', // PC网站支付
        charset: 'utf-8',
        sign_type: 'RSA2',
        timestamp: new Date().toISOString().replace('T', ' ').substring(0, 19),
        version: '1.0',
        notify_url: request.notifyUrl,
        return_url: request.returnUrl,
        biz_content: JSON.stringify({
          out_trade_no: request.paymentNo,
          product_code: 'FAST_INSTANT_TRADE_PAY',
          total_amount: request.amount.toFixed(2),
          subject: request.subject,
          body: request.body,
          timeout_express: '30m', // 30分钟超时
        }),
      };

      // 生成签名
      const sign = this.generateSign(params, config.privateKey!);
      params['sign'] = sign;

      // 构建支付链接
      const paymentUrl = `${config.gatewayUrl}?${this.buildQueryString(params)}`;

      return {
        success: true,
        paymentUrl,
        expiresAt: new Date(Date.now() + 30 * 60 * 1000), // 30分钟后过期
      };
    } catch (error) {
      this.logger.error(`创建支付宝支付失败: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message,
      };
    }
  }

  /**
   * 验证支付宝回调
   */
  async verifyCallback(callbackData: Record<string, any>): Promise<CallbackVerifyResult> {
    try {
      this.logger.log(`验证支付宝回调: ${JSON.stringify(callbackData)}`);

      const config = this.getAlipayConfig();
      
      // 验证签名
      const isValid = this.verifySign(callbackData, config.alipayPublicKey!);
      
      if (!isValid) {
        return {
          success: false,
          errorMessage: '签名验证失败',
        };
      }

      // 解析回调数据
      const tradeStatus = callbackData.trade_status;
      let status: 'success' | 'failed' | 'pending' = 'pending';

      switch (tradeStatus) {
        case 'TRADE_SUCCESS':
        case 'TRADE_FINISHED':
          status = 'success';
          break;
        case 'TRADE_CLOSED':
          status = 'failed';
          break;
        default:
          status = 'pending';
      }

      return {
        success: true,
        paymentNo: callbackData.out_trade_no,
        thirdPartyNo: callbackData.trade_no,
        status,
        amount: parseFloat(callbackData.total_amount),
        paidAt: callbackData.gmt_payment ? new Date(callbackData.gmt_payment) : new Date(),
        rawData: callbackData,
      };
    } catch (error) {
      this.logger.error(`验证支付宝回调失败: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message,
      };
    }
  }

  /**
   * 查询支付宝支付状态
   */
  async queryPayment(paymentNo: string, thirdPartyNo?: string): Promise<PaymentQueryResult> {
    try {
      this.logger.log(`查询支付宝支付状态: ${paymentNo}`);

      const config = this.getAlipayConfig();
      
      // 构建查询参数
      const params = {
        app_id: config.appId,
        method: 'alipay.trade.query',
        charset: 'utf-8',
        sign_type: 'RSA2',
        timestamp: new Date().toISOString().replace('T', ' ').substring(0, 19),
        version: '1.0',
        biz_content: JSON.stringify({
          out_trade_no: paymentNo,
          trade_no: thirdPartyNo,
        }),
      };

      // 生成签名
      const sign = this.generateSign(params, config.privateKey!);
      params['sign'] = sign;

      // 发送查询请求
      const response = await this.sendRequest(config.gatewayUrl, params);
      const result = response.alipay_trade_query_response;

      if (result.code !== '10000') {
        return {
          success: false,
          errorMessage: result.msg || result.sub_msg,
        };
      }

      // 解析支付状态
      let status: 'success' | 'failed' | 'pending' | 'closed' = 'pending';
      switch (result.trade_status) {
        case 'TRADE_SUCCESS':
        case 'TRADE_FINISHED':
          status = 'success';
          break;
        case 'TRADE_CLOSED':
          status = 'closed';
          break;
        case 'WAIT_BUYER_PAY':
          status = 'pending';
          break;
        default:
          status = 'pending';
      }

      return {
        success: true,
        status,
        thirdPartyNo: result.trade_no,
        amount: parseFloat(result.total_amount),
        paidAt: result.send_pay_date ? new Date(result.send_pay_date) : undefined,
      };
    } catch (error) {
      this.logger.error(`查询支付宝支付状态失败: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message,
      };
    }
  }

  /**
   * 支付宝退款
   */
  async refund(request: RefundRequest): Promise<RefundResult> {
    try {
      this.logger.log(`申请支付宝退款: ${request.refundNo}, 金额: ${request.refundAmount}`);

      const config = this.getAlipayConfig();
      
      // 构建退款参数
      const params = {
        app_id: config.appId,
        method: 'alipay.trade.refund',
        charset: 'utf-8',
        sign_type: 'RSA2',
        timestamp: new Date().toISOString().replace('T', ' ').substring(0, 19),
        version: '1.0',
        biz_content: JSON.stringify({
          out_trade_no: request.paymentNo,
          trade_no: request.thirdPartyNo,
          refund_amount: request.refundAmount.toFixed(2),
          out_request_no: request.refundNo,
          refund_reason: request.refundReason || '用户申请退款',
        }),
      };

      // 生成签名
      const sign = this.generateSign(params, config.privateKey!);
      params['sign'] = sign;

      // 发送退款请求
      const response = await this.sendRequest(config.gatewayUrl, params);
      const result = response.alipay_trade_refund_response;

      if (result.code !== '10000') {
        return {
          success: false,
          errorMessage: result.msg || result.sub_msg,
        };
      }

      return {
        success: true,
        refundNo: request.refundNo,
        thirdPartyRefundNo: result.trade_no,
        refundAmount: parseFloat(result.refund_fee),
      };
    } catch (error) {
      this.logger.error(`支付宝退款失败: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message,
      };
    }
  }

  /**
   * 查询支付宝退款状态
   */
  async queryRefund(refundNo: string, thirdPartyRefundNo?: string): Promise<RefundResult> {
    try {
      this.logger.log(`查询支付宝退款状态: ${refundNo}`);

      // 支付宝退款查询接口实现
      // 这里简化处理，实际应该调用 alipay.trade.fastpay.refund.query 接口
      
      return {
        success: true,
        refundNo,
        thirdPartyRefundNo,
      };
    } catch (error) {
      this.logger.error(`查询支付宝退款状态失败: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message,
      };
    }
  }

  /**
   * 获取支付宝配置
   */
  private getAlipayConfig() {
    return {
      appId: this.configService.get<string>('alipay.appId'),
      privateKey: this.configService.get<string>('alipay.privateKey'),
      alipayPublicKey: this.configService.get<string>('alipay.publicKey'),
      gatewayUrl: this.configService.get<string>('alipay.gatewayUrl', 'https://openapi.alipay.com/gateway.do'),
    };
  }

  /**
   * 生成签名
   */
  private generateSign(params: Record<string, any>, privateKey: string): string {
    // 实际实现中需要使用 RSA 签名算法
    // 这里简化处理
    return 'mock_signature';
  }

  /**
   * 验证签名
   */
  private verifySign(params: Record<string, any>, publicKey: string): boolean {
    // 实际实现中需要使用 RSA 验签算法
    // 这里简化处理
    return true;
  }

  /**
   * 构建查询字符串
   */
  private buildQueryString(params: Record<string, any>): string {
    return Object.keys(params)
      .sort()
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');
  }

  /**
   * 发送HTTP请求
   */
  private async sendRequest(url: string, params: Record<string, any>): Promise<any> {
    // 实际实现中需要使用 HTTP 客户端发送请求
    // 这里简化处理
    return {
      alipay_trade_query_response: {
        code: '10000',
        msg: 'Success',
        trade_status: 'TRADE_SUCCESS',
        trade_no: 'mock_trade_no',
        total_amount: '100.00',
      },
      alipay_trade_refund_response: {
        code: '10000',
        msg: 'Success',
        trade_no: 'mock_trade_no',
        refund_fee: '100.00',
      },
    };
  }
}
