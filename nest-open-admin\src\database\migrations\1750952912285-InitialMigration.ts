import { MigrationInterface, QueryRunner } from "typeorm";

export class InitialMigration1750952912285 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 创建用户表
        await queryRunner.query(`
        CREATE TABLE IF NOT EXISTS \`open_user\` (
            \`id\` int NOT NULL AUTO_INCREMENT,
            \`createdAt\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            \`updatedAt\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            \`deletedAt\` timestamp NULL DEFAULT NULL,
            \`createdBy\` varchar(50) DEFAULT NULL,
            \`updatedBy\` varchar(50) DEFAULT NULL,
            \`baseStatus\` tinyint NOT NULL DEFAULT '1',
            \`del_flag\` char(1) NOT NULL DEFAULT '0',
            \`remark\` text,
            \`username\` varchar(100) NOT NULL,
            \`email\` varchar(100) NOT NULL,
            \`password\` varchar(100) NOT NULL,
            \`nickname\` varchar(100) DEFAULT NULL,
            \`realName\` varchar(100) DEFAULT NULL,
            \`bio\` text,
            \`phone\` varchar(20) DEFAULT NULL,
            \`avatar\` varchar(255) DEFAULT NULL,
            \`gender\` enum('male','female','unknown') DEFAULT 'unknown',
            \`userType\` enum('individual','enterprise','organization') DEFAULT 'individual',
            \`verificationStatus\` enum('unverified','personal_verified','enterprise_verified') DEFAULT 'unverified',
            \`role\` enum('admin','user') NOT NULL DEFAULT 'user',
            \`openid\` varchar(100) DEFAULT NULL,
            \`userStatus\` enum('active','inactive','suspended','banned') NOT NULL DEFAULT 'active',
            \`emailVerified\` tinyint(1) DEFAULT '0',
            \`phoneVerified\` tinyint(1) DEFAULT '0',
            \`lastLoginAt\` timestamp NULL DEFAULT NULL,
            \`lastLoginIp\` varchar(45) DEFAULT NULL,
            \`loginFailCount\` int DEFAULT '0',
            \`lockedUntil\` timestamp NULL DEFAULT NULL,
            \`preferences\` text,
            \`settings\` text,
            \`lastDailyResetDate\` date DEFAULT NULL,
            \`tier\` enum('trial','basic','premium','enterprise') NOT NULL DEFAULT 'trial',
            \`remainingUsage\` int NOT NULL DEFAULT '100',
            \`totalUsage\` int NOT NULL DEFAULT '0',
            \`dailyFreeUsageRemaining\` int NOT NULL DEFAULT '10',
            \`balance\` decimal(10,2) NOT NULL DEFAULT '0.00',
            \`isFreeQuotaEligible\` tinyint(1) NOT NULL DEFAULT '1',
            PRIMARY KEY (\`id\`),
            UNIQUE KEY \`IDX_username\` (\`username\`),
            UNIQUE KEY \`IDX_email\` (\`email\`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        `);

        // 创建服务表
        await queryRunner.query(`
        CREATE TABLE IF NOT EXISTS \`open_service\` (
            \`id\` int NOT NULL AUTO_INCREMENT,
            \`createdAt\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            \`updatedAt\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            \`deletedAt\` timestamp NULL DEFAULT NULL,
            \`createdBy\` varchar(50) DEFAULT NULL,
            \`updatedBy\` varchar(50) DEFAULT NULL,
            \`baseStatus\` tinyint NOT NULL DEFAULT '1',
            \`del_flag\` char(1) NOT NULL DEFAULT '0',
            \`remark\` text,
            \`user_id\` int NOT NULL,
            \`service_id\` int NOT NULL,
            \`code\` varchar(100) NOT NULL,
            \`name\` varchar(200) NOT NULL,
            \`description\` text,
            \`type\` enum('OCR','NLP','CV','AI','DATA','OTHER') NOT NULL,
            \`serviceStatus\` enum('active','inactive','maintenance','deprecated') NOT NULL DEFAULT 'active',
            \`pricingModel\` enum('free','pay_per_use','subscription','tiered') NOT NULL DEFAULT 'pay_per_use',
            \`currentVersion\` varchar(20) NOT NULL DEFAULT 'v1.0.0',
            \`features\` varchar(200) NOT NULL DEFAULT '',
            \`endpoint\` varchar(500) DEFAULT NULL,
            \`config\` text,
            \`callCount\` int NOT NULL DEFAULT '0',
            \`price\` decimal(10,2) NOT NULL DEFAULT '0.00',
            \`dailyLimit\` int NOT NULL DEFAULT '1000',
            \`minuteLimit\` int NOT NULL DEFAULT '100',
            \`requireAuth\` tinyint NOT NULL DEFAULT '1',
            \`isAsync\` tinyint NOT NULL DEFAULT '0',
            \`timeout\` int NOT NULL DEFAULT '30',
            PRIMARY KEY (\`id\`),
            UNIQUE KEY \`IDX_code\` (\`code\`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        `);

        // 创建API密钥表
        await queryRunner.query(`
        CREATE TABLE IF NOT EXISTS \`open_api_key\` (
            \`id\` int NOT NULL AUTO_INCREMENT,
            \`createdAt\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            \`updatedAt\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            \`deletedAt\` timestamp NULL DEFAULT NULL,
            \`createdBy\` varchar(50) DEFAULT NULL,
            \`updatedBy\` varchar(50) DEFAULT NULL,
            \`baseStatus\` tinyint NOT NULL DEFAULT '1',
            \`del_flag\` char(1) NOT NULL DEFAULT '0',
            \`remark\` text,
            \`user_id\` int NOT NULL,
            \`service_id\` int NOT NULL,
            \`name\` varchar(100) NOT NULL,
            \`keyHash\` varchar(64) NOT NULL,
            \`secretHash\` varchar(64) NOT NULL,
            \`keyType\` enum('trial','basic','premium','enterprise') NOT NULL DEFAULT 'trial',
            \`key-status\` enum('active','inactive','expired','revoked') NOT NULL DEFAULT 'active',
            \`description\` text,
            \`permissions\` text,
            \`expiresAt\` timestamp NULL DEFAULT NULL,
            \`lastUsedAt\` timestamp NULL DEFAULT NULL,
            \`is_secret_viewed\` tinyint(1) NOT NULL DEFAULT '0',
            \`lastUsedIp\` varchar(45) DEFAULT NULL,
            \`userId\` int DEFAULT NULL,
            \`serviceId\` int DEFAULT NULL,
            PRIMARY KEY (\`id\`),
            UNIQUE KEY \`IDX_keyHash\` (\`keyHash\`),
            UNIQUE KEY \`IDX_secretHash\` (\`secretHash\`),
            KEY \`IDX_userId\` (\`userId\`),
            KEY \`IDX_key-status\` (\`key-status\`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        `);

        // 创建用户服务表
        await queryRunner.query(`
        CREATE TABLE IF NOT EXISTS \`user_service\` (
            \`id\` int NOT NULL AUTO_INCREMENT,
            \`createdAt\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            \`updatedAt\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            \`deletedAt\` timestamp NULL DEFAULT NULL,
            \`createdBy\` varchar(50) DEFAULT NULL,
            \`updatedBy\` varchar(50) DEFAULT NULL,
            \`baseStatus\` tinyint NOT NULL DEFAULT '1',
            \`del_flag\` char(1) NOT NULL DEFAULT '0',
            \`remark\` text,
            \`total_count\` int NOT NULL DEFAULT '100',
            \`used_count\` int NOT NULL DEFAULT '0',
            \`free_used_today\` int NOT NULL DEFAULT '0',
            \`last_reset_date\` date DEFAULT NULL,
            \`alert_sent\` tinyint(1) NOT NULL DEFAULT '0',
            \`userId\` int NOT NULL,
            \`serviceId\` int NOT NULL,
            PRIMARY KEY (\`id\`),
            KEY \`FK_user_service_userId\` (\`userId\`),
            KEY \`FK_user_service_serviceId\` (\`serviceId\`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE IF EXISTS \`user_service\``);
        await queryRunner.query(`DROP TABLE IF EXISTS \`open_api_key\``);
        await queryRunner.query(`DROP TABLE IF EXISTS \`open_service\``);
        await queryRunner.query(`DROP TABLE IF EXISTS \`open_user\``);
    }
}
