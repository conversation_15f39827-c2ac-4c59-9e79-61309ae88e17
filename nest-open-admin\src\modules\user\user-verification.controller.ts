import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { UserVerificationService } from './user-verification.service';
import { Roles } from '@/common/decorators/roles.decorator';
import {
  ReviewUserVerificationDto,
  BatchReviewUserVerificationDto,
  UserVerificationListResponseDto,
  UserVerificationStatsDto,
  QueryUserVerificationDto,
} from './dto/user-verification.dto';

/**
 * 用户认证审核控制器
 * 提供管理员审核用户实名认证的接口
 */
@ApiTags('用户认证审核管理')
@Controller('user-verification')
@ApiBearerAuth()
export class UserVerificationController {
  constructor(
    private readonly userVerificationService: UserVerificationService,
  ) {}

  /**
   * 审核用户认证
   */
  @Post(':id/review')
  @ApiOperation({
    summary: '审核用户认证',
    description: '管理员审核用户实名认证申请，审核通过后自动发放认证奖励',
  })
  @ApiParam({ name: 'id', description: '认证记录ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: '审核成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '审核通过，奖励已发放' },
      },
    },
  })
  @Roles('admin')
  async reviewVerification(
    @Param('id', ParseIntPipe) id: number,
    @Body() reviewDto: ReviewUserVerificationDto,
  ) {
    return await this.userVerificationService.reviewVerification(id, reviewDto);
  }

  /**
   * 批量审核认证
   */
  @Post('batch-review')
  @ApiOperation({
    summary: '批量审核认证',
    description: '批量审核多个用户的实名认证申请',
  })
  @ApiResponse({
    status: 200,
    description: '批量审核完成',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        processed: { type: 'number', example: 5 },
        failed: { type: 'number', example: 0 },
      },
    },
  })
  @Roles('admin')
  async batchReviewVerification(
    @Body() batchReviewDto: BatchReviewUserVerificationDto,
  ) {
    return await this.userVerificationService.batchReviewVerification(
      batchReviewDto,
    );
  }

  /**
   * 获取认证列表
   */
  @Get('list')
  @ApiOperation({
    summary: '获取认证列表',
    description: '分页查询用户认证申请列表，支持多种筛选条件',
  })
  @ApiQuery({ name: 'page', required: false, description: '页码', example: 1 })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: '每页数量',
    example: 10,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: '审核状态',
    enum: ['pending', 'approved', 'rejected'],
  })
  @ApiQuery({
    name: 'userId',
    required: false,
    description: '用户ID',
    type: 'number',
  })
  @ApiQuery({
    name: 'realName',
    required: false,
    description: '真实姓名（模糊搜索）',
    type: 'string',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    description: '开始日期',
    type: 'string',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    description: '结束日期',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: UserVerificationListResponseDto,
  })
  @Roles('admin')
  async getVerificationList(@Query() queryDto: QueryUserVerificationDto) {
    return await this.userVerificationService.findVerificationList(queryDto);
  }

  /**
   * 获取认证统计信息
   */
  @Get('stats')
  @ApiOperation({
    summary: '获取认证统计信息',
    description: '获取用户认证申请的统计数据',
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: UserVerificationStatsDto,
  })
  @Roles('admin')
  async getVerificationStats() {
    return await this.userVerificationService.getVerificationStats();
  }

  /**
   * 获取待审核认证数量
   */
  @Get('pending-count')
  @ApiOperation({
    summary: '获取待审核认证数量',
    description: '获取当前待审核的认证申请数量',
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        count: { type: 'number', example: 15 },
      },
    },
  })
  @Roles('admin')
  async getPendingCount() {
    const stats = await this.userVerificationService.getVerificationStats();
    return { count: stats.pendingCount };
  }
}