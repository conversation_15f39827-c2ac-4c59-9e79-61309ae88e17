import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString, IsOptional, IsIn, Min, Max } from 'class-validator';

export class CreateTopUpDto {
  @ApiProperty({
    description: '充值金额',
    example: 100,
    minimum: 0.01,
    maximum: 10000,
  })
  @IsNumber({}, { message: '充值金额必须是数字' })
  @Min(0.01, { message: '充值金额最少0.01元' })
  @Max(10000, { message: '充值金额最多10000元' })
  amount: number;

  @ApiProperty({
    description: '支付方式',
    example: 'alipay',
    enum: ['alipay', 'wechat', 'bank'],
  })
  @IsString({ message: '支付方式必须是字符串' })
  @IsIn(['alipay', 'wechat', 'bank'], { message: '支付方式必须是 alipay、wechat 或 bank' })
  paymentMethod: 'alipay' | 'wechat' | 'bank';

  @ApiProperty({
    description: '支付成功后的返回地址',
    example: 'https://example.com/payment/success',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '返回地址必须是字符串' })
  returnUrl?: string;

  @ApiProperty({
    description: '支付结果通知地址',
    example: 'https://example.com/api/payment/notify',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '通知地址必须是字符串' })
  notifyUrl?: string;
}