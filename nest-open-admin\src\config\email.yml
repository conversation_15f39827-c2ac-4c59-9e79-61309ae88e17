# 邮件服务配置
email:
  # 邮件服务提供商配置
  providers:
    # 163邮箱配置 - 主账号
    - name: 'primary'
      host: 'smtp.163.com'
      port: 465
      secure: true
      user: '<EMAIL>'
      pass: '${EMAIL_PASS_1:-MJ37vD5xyzByNXhp}' # 授权码，优先使用环境变量，否则使用默认值
      from: '<EMAIL>'
      fromName: '数智云链'
      weight: 40 # 权重，用于负载均衡
      status: 'active'
      dailyLimit: 200 # 每日发送限制
      
    # 163邮箱配置 - 备用账号1
    - name: 'secondary'
      host: 'smtp.163.com'
      port: 465
      secure: true
      user: '<EMAIL>'
      pass: '${EMAIL_PASS_2:-ZQ3T97D9dunBSfL7}' # 授权码，优先使用环境变量，否则使用默认值
      from: '<EMAIL>'
      fromName: '数智云链'
      weight: 30
      status: 'active'
      dailyLimit: 200
      
    # 163邮箱配置 - 备用账号2
    - name: 'tertiary'
      host: 'smtp.163.com'
      port: 465
      secure: true
      user: '<EMAIL>'
      pass: '${EMAIL_PASS_3:-JV3Mk34i5QZ5j5F2}' # 授权码，优先使用环境变量，否则使用默认值
      from: '<EMAIL>'
      fromName: '数智云链'
      weight: 30
      status: 'active'
      dailyLimit: 200

  # 负载均衡策略
  loadBalance:
    strategy: 'weighted_round_robin' # weighted_round_robin, random, least_used
    healthCheck: true
    healthCheckInterval: 7200000 # 120分钟检查一次（进一步降低频率，避免风控风险）
    # 智能健康检查配置
    smartHealthCheck:
      enabled: true
      recentSuccessThreshold: 1800000 # 30分钟内有成功发送记录则跳过检查
      minCheckInterval: 3600000 # 最小检查间隔60分钟（避免频繁检查）
      maxConsecutiveFailures: 5 # 增加最大连续失败次数容忍度
    
  # 发送频率控制
  rateLimit:
    # 每个邮箱地址的发送限制
    perEmail:
      window: 3600000 # 1小时窗口期
      max: 5 # 最多发送5次
    # 每个IP的发送限制
    perIp:
      window: 3600000 # 1小时窗口期
      max: 10 # 最多发送10次
    # 全局发送限制
    global:
      window: 60000 # 1分钟窗口期
      max: 20 # 最多发送20次
      
  # 临时邮箱检测
  tempEmailDetection:
    enabled: true
    # 临时邮箱域名黑名单
    blacklist:
      - '10minutemail.com'
      - 'guerrillamail.com'
      - 'mailinator.com'
      - 'tempmail.org'
      - 'yopmail.com'
      - '0-mail.com'
      - '33mail.com'
      - 'throwaway.email'
      - 'temp-mail.org'
      - 'getnada.com'
    # API检测服务（可选）
    apiCheck:
      enabled: false
      url: 'https://api.disposable-email.net/check'
      timeout: 5000
      
  # 邮件模板配置
  templates:
    register:
      subject: '【数智云链】注册验证码'
      template: |
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px;">
          <h2 style="color: #333; text-align: center;">注册验证码</h2>
          <p style="color: #666; font-size: 16px;">您好，</p>
          <p style="color: #666; font-size: 16px;">您正在注册我们的服务，验证码为：</p>
          <div style="text-align: center; margin: 30px 0;">
            <span style="font-size: 32px; font-weight: bold; color: #007bff; background: #f8f9fa; padding: 15px 30px; border-radius: 5px; letter-spacing: 5px;">{{code}}</span>
          </div>
          <p style="color: #666; font-size: 14px;">验证码有效期为5分钟，请及时使用。</p>
          <p style="color: #999; font-size: 12px; margin-top: 30px;">如果您没有进行此操作，请忽略此邮件。</p>
        </div>
    login:
      subject: '【数智云链】登录验证码'
      template: |
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px;">
          <h2 style="color: #333; text-align: center;">登录验证码</h2>
          <p style="color: #666; font-size: 16px;">您好，</p>
          <p style="color: #666; font-size: 16px;">您正在登录我们的服务，验证码为：</p>
          <div style="text-align: center; margin: 30px 0;">
            <span style="font-size: 32px; font-weight: bold; color: #28a745; background: #f8f9fa; padding: 15px 30px; border-radius: 5px; letter-spacing: 5px;">{{code}}</span>
          </div>
          <p style="color: #666; font-size: 14px;">验证码有效期为5分钟，请及时使用。</p>
          <p style="color: #999; font-size: 12px; margin-top: 30px;">如果您没有进行此操作，请立即修改密码。</p>
        </div>
    reset:
      subject: '【数智云链】密码重置验证码'
      template: |
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px;">
          <h2 style="color: #333; text-align: center;">密码重置验证码</h2>
          <p style="color: #666; font-size: 16px;">您好，</p>
          <p style="color: #666; font-size: 16px;">您正在重置密码，验证码为：</p>
          <div style="text-align: center; margin: 30px 0;">
            <span style="font-size: 32px; font-weight: bold; color: #dc3545; background: #f8f9fa; padding: 15px 30px; border-radius: 5px; letter-spacing: 5px;">{{code}}</span>
          </div>
          <p style="color: #666; font-size: 14px;">验证码有效期为5分钟，请及时使用。</p>
          <p style="color: #999; font-size: 12px; margin-top: 30px;">如果您没有进行此操作，请立即联系客服。</p>
        </div>
        
  # 安全配置
  security:
    # 设备指纹验证
    deviceFingerprint:
      enabled: true
      riskThreshold: 0.7 # 风险阈值
      blockHighRisk: true # 是否阻止高风险设备
      
    # 行为模式分析
    behaviorAnalysis:
      enabled: true
      # 可疑行为检测
      suspiciousPatterns:
        - rapidClicks: 10 # 快速点击次数
        - shortStayTime: 5000 # 停留时间过短(ms)
        - multipleAttempts: 3 # 多次尝试
      riskScore:
        low: 0.3
        medium: 0.6
        high: 0.8
        
  # 监控和日志
  monitoring:
    enabled: true
    logLevel: 'info'
    metrics:
      - sendCount
      - failureRate
      - responseTime
      - providerHealth
    alerts:
      failureThreshold: 0.1 # 失败率超过10%时告警
      responseTimeThreshold: 5000 # 响应时间超过5秒时告警