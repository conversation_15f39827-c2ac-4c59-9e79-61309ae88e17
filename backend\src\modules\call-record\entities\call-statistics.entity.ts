import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm';

/**
 * 调用统计实体 - 用于聚合存储
 */
@Entity('call_statistics')
@Index(['userId', 'serviceId', 'date'], { unique: true })
export class CallStatisticsEntity {
  @PrimaryGeneratedColumn()
  id: number;
  
  @Index()
  @Column()
  userId: number;
  
  @Index()
  @Column()
  serviceId: number;
  
  @Index()
  @Column({ type: 'date' })
  date: Date;  // 统计日期
  
  @Column({ type: 'int' })
  totalCalls: number;  // 总调用次数
  
  @Column({ type: 'int' })
  successCalls: number;  // 成功调用次数
  
  @Column({ type: 'int' })
  failedCalls: number;  // 失败调用次数
  
  @Column({ type: 'int' })
  totalDuration: number;  // 总耗时
  
  @Column({ type: 'float' })
  avgDuration: number;  // 平均耗时
} 