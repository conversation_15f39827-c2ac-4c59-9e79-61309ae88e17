/**
 * 用户使用次数配置
 */
export interface UserUsageConfig {
  /** 未认证用户默认使用次数 */
  unverifiedDefaultUsage: number;
  /** 个人实名认证用户默认使用次数 */
  personalVerifiedDefaultUsage: number;
  /** 企业实名认证用户默认使用次数 */
  enterpriseVerifiedDefaultUsage: number;
  /** 个人认证通过后额外赠送的使用次数 */
  personalVerificationBonus: number;
  /** 企业认证通过后额外赠送的使用次数 */
  enterpriseVerificationBonus: number;
  /** 每日免费使用次数（所有用户） */
  dailyFreeUsage: number;
  /** VIP用户每月免费使用次数 */
  vipMonthlyFreeUsage: number;
}

/**
 * 默认用户使用次数配置
 * 可通过环境变量或配置文件覆盖
 */
export const DEFAULT_USER_USAGE_CONFIG: UserUsageConfig = {
  unverifiedDefaultUsage: 10,           // 未认证用户默认10次
  personalVerifiedDefaultUsage: 100,    // 个人认证用户默认100次
  enterpriseVerifiedDefaultUsage: 500,  // 企业认证用户默认500次
  personalVerificationBonus: 50,        // 个人认证通过额外赠送50次
  enterpriseVerificationBonus: 200,     // 企业认证通过额外赠送200次
  dailyFreeUsage: 3,                    // 每日免费3次
  vipMonthlyFreeUsage: 1000,            // VIP用户每月免费1000次
};

/**
 * 获取用户使用次数配置
 * 优先从环境变量读取，否则使用默认配置
 */
export function getUserUsageConfig(): UserUsageConfig {
  return {
    unverifiedDefaultUsage: parseInt(process.env.USER_UNVERIFIED_DEFAULT_USAGE || '10'),
    personalVerifiedDefaultUsage: parseInt(process.env.USER_PERSONAL_VERIFIED_DEFAULT_USAGE || '100'),
    enterpriseVerifiedDefaultUsage: parseInt(process.env.USER_ENTERPRISE_VERIFIED_DEFAULT_USAGE || '500'),
    personalVerificationBonus: parseInt(process.env.USER_PERSONAL_VERIFICATION_BONUS || '50'),
    enterpriseVerificationBonus: parseInt(process.env.USER_ENTERPRISE_VERIFICATION_BONUS || '200'),
    dailyFreeUsage: parseInt(process.env.USER_DAILY_FREE_USAGE || '3'),
    vipMonthlyFreeUsage: parseInt(process.env.USER_VIP_MONTHLY_FREE_USAGE || '1000')
  };
}

/**
 * 根据用户认证状态获取默认使用次数
 */
export function getDefaultUsageByVerificationStatus(verificationStatus: string): number {
  const config = getUserUsageConfig();
  
  switch (verificationStatus) {
    case 'personal_verified':
      return config.personalVerifiedDefaultUsage;
    case 'enterprise_verified':
      return config.enterpriseVerifiedDefaultUsage;
    default:
      return config.unverifiedDefaultUsage;
  }
}

/**
 * 根据用户认证状态获取认证奖励次数
 */
export function getVerificationBonus(verificationStatus: string): number {
  const config = getUserUsageConfig();
  
  switch (verificationStatus) {
    case 'personal_verified':
      return config.personalVerificationBonus;
    case 'enterprise_verified':
      return config.enterpriseVerificationBonus;
    default:
      return 0;
  }
}