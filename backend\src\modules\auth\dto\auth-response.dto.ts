import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UserType } from '@/modules/user/entities/user.entity';

/**
 * API密钥简要信息
 */
export class ApiKeyInfo {
  /** API密钥ID */
  @ApiProperty({ description: 'API密钥ID' })
  id: number;

  /** API密钥名称 */
  @ApiProperty({ description: 'API密钥名称' })
  name: string;

  /** 创建时间 */
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;
}

/**
 * 令牌响应DTO
 */
export class TokenResponseDto {
  /** 访问令牌 */
  @ApiProperty({ description: '访问令牌', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' })
  token: string;

  /** 刷新令牌 */
  @ApiProperty({ description: '刷新令牌', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' })
  refreshToken: string;

  /** 令牌过期时间（秒） */
  @ApiProperty({ description: '令牌过期时间（秒）', example: 86400 })
  expiresIn: number;
}

/**
 * 注册响应DTO
 */
export class RegisterResponseDto extends TokenResponseDto {
  /** 用户ID */
  @ApiProperty({ description: '用户ID', example: 123 })
  userId: number;

  /** 邮箱 */
  @ApiPropertyOptional({ description: '邮箱', example: '<EMAIL>' })
  email?: string;

  /** 手机号 */
  @ApiPropertyOptional({ description: '手机号', example: '13800138000' })
  phone?: string;

  /** 昵称 */
  @ApiProperty({ description: '昵称', example: '用户昵称' })
  nickname: string;

  /** API密钥列表 */
  @ApiProperty({ description: 'API密钥列表', type: [ApiKeyInfo] })
  apiKeys: ApiKeyInfo[];
}

/**
 * 登录响应DTO
 */
export class LoginResponseDto extends TokenResponseDto {
  /** 用户ID */
  @ApiProperty({ description: '用户ID', example: 123 })
  userId: number;

  /** 邮箱 */
  @ApiPropertyOptional({ description: '邮箱', example: '<EMAIL>' })
  email?: string;

  /** 手机号 */
  @ApiPropertyOptional({ description: '手机号', example: '13800138000' })
  phone?: string;

  /** 昵称 */
  @ApiProperty({ description: '昵称', example: '用户昵称' })
  nickname: string;

  /** 是否新用户（静默注册） */
  @ApiPropertyOptional({ description: '是否新用户（静默注册）', example: true })
  isNewUser?: boolean;

  /** API密钥列表（静默注册时返回） */
  @ApiPropertyOptional({ description: 'API密钥列表（静默注册时返回）', type: [ApiKeyInfo] })
  apiKeys?: ApiKeyInfo[];
}

/**
 * 验证码验证响应DTO
 */
export class VerificationResponseDto {
  /** 邮箱（脱敏） */
  @ApiPropertyOptional({ description: '邮箱（脱敏）', example: 'u****@example.com' })
  email?: string;

  /** 手机号（脱敏） */
  @ApiPropertyOptional({ description: '手机号（脱敏）', example: '138****8000' })
  phone?: string;

  /** 验证码过期时间（秒） */
  @ApiProperty({ description: '验证码过期时间（秒）', example: 300 })
  expireIn: number;
}

/**
 * 安全验证数据DTO
 */
export class SecurityVerificationDto {
  /** 验证类型 */
  @ApiProperty({ description: '验证类型', example: 'slide' })
  type: string;

  /** 验证等级 */
  @ApiProperty({ description: '验证等级', example: 2 })
  level: number;

  /** 行为模式数据 */
  @ApiProperty({ description: '行为模式数据' })
  behaviorPattern: any;

  /** 验证时间戳 */
  @ApiProperty({ description: '验证时间戳', example: 1625097600000 })
  verificationTimestamp: number;
}

/**
 * 静默注册数据DTO
 */
export class SilentRegisterDataDto {
  /** 邮箱 */
  @ApiPropertyOptional({ description: '邮箱', example: '<EMAIL>' })
  email?: string;

  /** 手机号 */
  @ApiPropertyOptional({ description: '手机号', example: '13800138000' })
  phone?: string;

  /** 安全验证数据 */
  @ApiProperty({ description: '安全验证数据', type: SecurityVerificationDto })
  securityVerification: SecurityVerificationDto;
}