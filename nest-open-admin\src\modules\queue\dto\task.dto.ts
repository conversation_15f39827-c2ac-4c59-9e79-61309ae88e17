/**
 * 任务相关DTO
 * 定义API调用和告警任务的数据传输对象
 */

import { IsString, IsOptional, IsNumber, IsEnum, IsObject, IsArray, IsUrl, IsIn, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TaskType, TaskPriority, AlertLevel, AlertType } from '../queue.constants';

// 基础任务DTO
export class BaseTaskDto {
  @ApiPropertyOptional({ description: '任务ID' })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ description: '任务类型', enum: ['api_call', 'usage_alert'] })
  @IsEnum(['api_call', 'usage_alert'])
  type: TaskType;

  @ApiPropertyOptional({ description: '任务优先级', enum: [1, 5, 10, 15], default: 5 })
  @IsOptional()
  @IsEnum([1, 5, 10, 15])
  priority?: TaskPriority;

  @ApiPropertyOptional({ description: '延迟执行时间(毫秒)', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  delay?: number;

  @ApiPropertyOptional({ description: '重试次数', minimum: 1, maximum: 10, default: 3 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  attempts?: number;

  @ApiPropertyOptional({ description: '用户ID' })
  @IsOptional()
  @IsNumber()
  userId?: number;
}

// API调用任务DTO
export class ApiCallTaskDto extends BaseTaskDto {
  @ApiProperty({ description: '请求URL' })
  @IsUrl()
  url: string;

  @ApiProperty({ description: 'HTTP方法', enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'] })
  @IsIn(['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

  @ApiPropertyOptional({ description: '请求头' })
  @IsOptional()
  @IsObject()
  headers?: Record<string, string>;

  @ApiPropertyOptional({ description: '请求体' })
  @IsOptional()
  body?: any;

  @ApiPropertyOptional({ description: '超时时间(毫秒)', minimum: 1000, maximum: 60000, default: 30000 })
  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(60000)
  timeout?: number;

  @ApiPropertyOptional({ description: '重试配置' })
  @IsOptional()
  @IsObject()
  retryConfig?: {
    maxRetries: number;
    retryDelay: number;
  };
}

// 告警任务DTO
export class AlertTaskDto extends BaseTaskDto {
  @ApiProperty({ description: '用户ID' })
  @IsNumber()
  userId: number;

  @ApiProperty({ description: '告警类型', enum: ['usage_warning', 'system_error', 'api_limit'] })
  @IsEnum(['usage_warning', 'system_error', 'api_limit'])
  alertType: AlertType;

  @ApiProperty({ description: '告警级别', enum: ['info', 'warning', 'error', 'critical'] })
  @IsEnum(['info', 'warning', 'error', 'critical'])
  level: AlertLevel;

  @ApiProperty({ description: '告警标题' })
  @IsString()
  title: string;

  @ApiProperty({ description: '告警消息' })
  @IsString()
  message: string;

  @ApiPropertyOptional({ description: '元数据' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({ description: '通知渠道', type: [String], enum: ['email', 'sms', 'webhook'] })
  @IsOptional()
  @IsArray()
  @IsIn(['email', 'sms', 'webhook'], { each: true })
  channels?: ('email' | 'sms' | 'webhook')[];
}

// 任务查询DTO
export class TaskQueryDto {
  @ApiPropertyOptional({ description: '队列名称' })
  @IsOptional()
  @IsString()
  queueName?: string;

  @ApiPropertyOptional({ description: '任务状态' })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiPropertyOptional({ description: '页码', minimum: 1, default: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100, default: 20 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;
}

// 任务操作DTO
export class TaskActionDto {
  @ApiProperty({ description: '任务ID' })
  @IsString()
  jobId: string;

  @ApiPropertyOptional({ description: '操作类型', enum: ['cancel', 'retry', 'remove'] })
  @IsOptional()
  @IsIn(['cancel', 'retry', 'remove'])
  action?: 'cancel' | 'retry' | 'remove';
}