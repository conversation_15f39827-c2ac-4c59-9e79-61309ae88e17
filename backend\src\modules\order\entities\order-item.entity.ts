import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { OrderEntity } from './order.entity';
import { ServiceEntity } from '../../service/entities/service.entity';

@Entity('order_items')
@Index(['orderId'])
@Index(['serviceId'])
export class OrderItemEntity {
  @ApiProperty({ description: '订单项ID', example: 1 })
  @PrimaryGeneratedColumn({ comment: '订单项ID' })
  id: number;

  @ApiProperty({ description: '订单ID', example: 1 })
  @Column({ type: 'int', name: 'order_id', comment: '订单ID' })
  orderId: number;

  @ApiProperty({ description: '服务ID', example: 1 })
  @Column({ type: 'int', name: 'service_id', nullable: true, comment: '服务ID' })
  serviceId?: number;

  @ApiProperty({ description: '商品名称', example: 'OCR识别服务' })
  @Column({ type: 'varchar', length: 200, name: 'item_name', comment: '商品名称' })
  itemName: string;

  @ApiProperty({ description: '商品描述', example: '提供高精度OCR识别服务' })
  @Column({ type: 'text', name: 'item_description', nullable: true, comment: '商品描述' })
  itemDescription?: string;

  @ApiProperty({ description: '单价', example: 0.10 })
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 4,
    name: 'unit_price',
    comment: '单价'
  })
  unitPrice: number;

  @ApiProperty({ description: '数量', example: 1000 })
  @Column({ type: 'int', comment: '数量' })
  quantity: number;

  @ApiProperty({ description: '小计', example: 100.00 })
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    name: 'total_price',
    comment: '小计'
  })
  totalPrice: number;

  @ApiProperty({ description: '商品配置', example: '{"type": "ocr", "accuracy": "high"}' })
  @Column({
    type: 'json',
    name: 'item_config',
    nullable: true,
    comment: '商品配置'
  })
  itemConfig?: Record<string, any>;

  @ApiProperty({ description: '创建时间', example: '2023-12-01T10:00:00Z' })
  @CreateDateColumn({ name: 'created_at', comment: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间', example: '2023-12-01T10:30:00Z' })
  @UpdateDateColumn({ name: 'updated_at', comment: '更新时间' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => OrderEntity, order => order.orderItems, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'order_id' })
  order: OrderEntity;

  @ManyToOne(() => ServiceEntity, { lazy: true, nullable: true })
  @JoinColumn({ name: 'service_id' })
  service?: Promise<ServiceEntity>;

  /**
   * 计算小计
   */
  calculateTotalPrice(): number {
    return Number((this.unitPrice * this.quantity).toFixed(2));
  }

  /**
   * 更新小计
   */
  updateTotalPrice(): void {
    this.totalPrice = this.calculateTotalPrice();
  }
}
