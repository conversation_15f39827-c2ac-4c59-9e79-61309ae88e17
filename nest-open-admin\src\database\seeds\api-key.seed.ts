import { DataSource } from 'typeorm';
import { Logger } from '@nestjs/common';
import { ApiKeyEntity } from '../../modules/api-key/entities/api-key.entity';
import { ApiKeyStatus } from '../../modules/api-key/entities/api-key.entity';
import { TierEnum } from '../../modules/user/entities/user.entity';
import * as bcrypt from 'bcrypt';
import { GenerateUUID } from '../../common/utils/index';
import { UserEntity } from '../../modules/user/entities/user.entity';

export class ApiKeySeed {
  private readonly logger = new Logger(ApiKeySeed.name);

  async run(dataSource: DataSource): Promise<void> {
    const apiKeyRepository = dataSource.getRepository(ApiKeyEntity);
    const userRepository = dataSource.getRepository(UserEntity);

    // 检查是否已存在API密钥数据
    const existingApiKeys = await apiKeyRepository.count();
    if (existingApiKeys > 0) {
      console.log('API密钥数据已存在，跳过创建');
      return;
    }

    // 检查是否需要运行修复脚本
    const runFix = process.env.RUN_API_KEY_FIX === 'true';
    if (runFix) {
      this.logger.log('正在运行API密钥修复脚本...');
      await this.fixMissingApiKeys(dataSource, apiKeyRepository, userRepository);
    }

    try {
      // 创建管理员的API密钥 - 使用固定的测试密钥
      const adminApiKey = 'ak_admin_test_key_12345678901234567890';
      const adminSecretKey = 'sk_admin_test_secret_12345678901234567890';
      const adminSecretHash = await bcrypt.hash(adminSecretKey, 10);

      const adminKey = new ApiKeyEntity();
      adminKey.userId = 1; // 管理员用户ID
      adminKey.serviceId = 0; // 平台服务ID
      adminKey.name = '管理员主密钥';
      adminKey.description = '管理员全局访问密钥，可以访问所有服务';
      adminKey.keyType = TierEnum.ENTERPRISE;
      adminKey.apiKey = adminApiKey; // 明文存储API密钥
      adminKey.secretHash = adminSecretHash;
      
      // 直接赋值数组，让实体的transformer自动处理
      adminKey.permissions = ['*:*:*']; // 全部权限
      
      adminKey.keyStatus = ApiKeyStatus.ACTIVE;
      adminKey.isSecretViewed = true;
      adminKey.lastUsedAt = undefined;
      
      await apiKeyRepository.save(adminKey);
      console.log('✅ 管理员API密钥创建成功');
      console.log(`   API Key: ${adminApiKey}`);
      console.log(`   Secret Key: ${adminSecretKey}`);
      console.log('⚠️  请保存这些密钥信息，它们只会显示一次!');

      // 创建测试用户的API密钥 - 使用固定的测试密钥
      const testApiKey = 'ak_a1fb793ecce140d3851dbae3e5568dbf';
      const testSecretKey = 'sk_2ed1efaf41f14c3d9dafd63aac35cadf';
      const testSecretHash = await bcrypt.hash(testSecretKey, 10);
      
      console.log(`测试用户API密钥加密哈希: ${testSecretHash}`);
      console.log(`原始Secret Key: ${testSecretKey}`);
      const verifyResult = await bcrypt.compare(testSecretKey, testSecretHash);
      console.log(`验证结果: ${verifyResult ? '成功' : '失败'}`);

      const testKey = new ApiKeyEntity();
      testKey.userId = 2; // 测试用户ID
      testKey.serviceId = 0; // 平台服务ID
      testKey.name = '测试用户主密钥';
      testKey.description = '测试用户访问密钥，有调用次数限制';
      testKey.keyType = TierEnum.BASIC;
      testKey.apiKey = testApiKey; // 明文存储API密钥
      testKey.secretHash = testSecretHash;
      
      // 直接赋值数组，让实体的transformer自动处理
      testKey.permissions = [
        'service:OCR_EXPRESS:*',
        'service:ADDRESS_EXTRACT:*',
        'service:GEO_REVERSE:*'
      ]; // 特定服务权限
      
      testKey.keyStatus = ApiKeyStatus.ACTIVE;
      testKey.isSecretViewed = true;
      testKey.lastUsedAt = undefined;
      
      await apiKeyRepository.save(testKey);
      console.log('✅ 测试用户API密钥创建成功');
      console.log(`   API Key: ${testApiKey}`);
      console.log(`   Secret Key: ${testSecretKey}`);
      console.log('⚠️  请保存这些密钥信息，它们只会显示一次!');
    } catch (error) {
      console.error('❌ API密钥创建失败:', error);
      throw error;
    }
  }
  
  /**
   * 修复没有API密钥的用户
   * 为每个没有API密钥的用户创建一个主密钥
   */
  private async fixMissingApiKeys(
    dataSource: DataSource,
    apiKeyRepository: any,
    userRepository: any,
  ): Promise<void> {
    this.logger.log('开始检查并修复没有API密钥的用户...');
    
    // 获取所有用户ID
    const users = await userRepository.find({
      select: ['id', 'email', 'phone', 'username'],
    });
    this.logger.log(`总共发现 ${users.length} 个用户`);
    
    // 检查每个用户是否有API密钥
    let fixedCount = 0;
    let errorCount = 0;
    
    for (const user of users) {
      // 检查用户是否有API密钥
      const apiKeyCount = await apiKeyRepository.count({
        where: { userId: user.id },
      });
      
      if (apiKeyCount === 0) {
        try {
          this.logger.log(`用户 #${user.id} (${user.email || user.phone || user.username}) 没有API密钥，正在创建...`);
          
          // 生成API密钥
          const apiKeyId = GenerateUUID();
          const apiKey = `ak_${apiKeyId}`;
          
          // 生成秘钥
          const secretKeyId = GenerateUUID();
          const secretKey = `sk_${secretKeyId}`;
          
          // 哈希秘钥
          const salt = await bcrypt.genSalt(10);
          const secretHash = await bcrypt.hash(secretKey, salt);
          
          // 创建API密钥实体
          const apiKeyEntity = apiKeyRepository.create({
            userId: user.id,
            serviceId: 0, // 平台级密钥
            apiKey,
            secretHash,
            name: '用户主密钥',
            keyType: 'master',
            keyStatus: 'active',
            permissions: ['*'],
            description: '系统自动修复创建的主密钥',
            isSecretViewed: false,
          });
          
          // 保存API密钥
          await apiKeyRepository.save(apiKeyEntity);
          
          this.logger.log(`成功为用户 #${user.id} 创建API密钥: ${apiKey}`);
          fixedCount++;
        } catch (error) {
          this.logger.error(`为用户 #${user.id} 创建API密钥失败: ${error.message}`);
          errorCount++;
        }
      }
    }
    
    this.logger.log(`修复完成: ${fixedCount} 个用户已创建API密钥，${errorCount} 个用户创建失败`);
  }
}