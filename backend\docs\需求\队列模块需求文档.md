# 队列模块需求文档

## 1. 文档概述

本文档描述了开放平台队列模块的需求规范，该模块基于单一职责原则设计，专注于异步任务的处理和结果推送。队列模块负责接收来自网关的异步请求，使用Redis作为队列存储，处理完成后通过SSE方式主动推送结果给客户端，无需客户端轮询查询任务状态。

## 2. 功能需求

### 2.1 核心功能

- **任务调度与管理**
  - 接收并入队异步任务
  - 按优先级和接收顺序调度任务
  - 任务状态跟踪和更新
  - 失败任务自动重试

- **实时结果推送**
  - 通过SSE推送任务状态和进度
  - 任务完成后推送结果
  - SSE连接管理和异常处理

- **支持的任务类型**
  - 物流面单OCR识别
  - 申通面单OCR识别
  - 地址文字块提取
  - 地理坐标处理
  - 可扩展的自定义任务类型

### 2.2 非功能需求

- **高性能**：任务调度和状态更新响应时间<100ms
- **可靠性**：任务数据持久化，系统崩溃后可恢复
- **可扩展性**：支持水平扩展以处理更多任务
- **监控能力**：提供任务执行日志和性能指标

## 3. 技术规范

### 3.1 队列配置

```typescript
// 简化示意
export const QUEUE_CONFIG = {
  // Redis连接配置
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
  },
  
  // 队列配置
  queues: {
    OCR_QUEUE: {
      name: 'ocr-tasks',
      concurrency: 5,               // 并发处理任务数
      attempts: 3,                  // 失败重试次数
      backoff: {                    // 重试延迟策略
        type: 'exponential',
        delay: 5000,                // 初始延迟时间(ms)
      },
      removeOnComplete: true,       // 完成后删除任务
      removeOnFail: false,          // 失败后保留任务
      defaultJobOptions: {
        timeout: 180000,            // 3分钟超时
        priority: 1,                // 默认优先级
      }
    },

    STO_OCR_QUEUE: {
      name: 'sto-ocr-tasks',
      concurrency: 3,               // 申通OCR专用队列，并发数较低以保证质量
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 5000,
      },
      removeOnComplete: true,
      removeOnFail: false,
      defaultJobOptions: {
        timeout: 200000,            // 申通OCR可能需要更长处理时间
        priority: 2,                // 稍高优先级
      }
    },

    ADDRESS_QUEUE: {
      name: 'address-tasks',
      concurrency: 8,
      attempts: 2,
      backoff: { type: 'exponential', delay: 3000 },
      removeOnComplete: true,
      removeOnFail: false,
      defaultJobOptions: {
        timeout: 60000,             // 1分钟超时
        priority: 1,
      }
    },
  },
};

// 优先级定义
export enum QueuePriority {
  LOW = 10,
  NORMAL = 5,
  HIGH = 1,
  CRITICAL = 0,
}
```

### 3.2 任务状态定义

```typescript
// 简化示意
export enum TaskStatus {
  QUEUED = 'queued',        // 任务已入队，等待处理
  ACTIVE = 'active',        // 任务处理中
  PROCESSING = 'processing',// 业务处理中（处理子状态）
  COMPLETED = 'completed',  // 任务已完成
  FAILED = 'failed',        // 任务失败
  WAITING = 'waiting',      // 等待依赖任务完成
  PAUSED = 'paused',        // 任务暂停
  DELAYED = 'delayed',      // 任务延迟执行
}
```

### 3.3 SSE实现

```typescript
// 简化示意
@Controller('tasks')
export class TaskEventsController {
  @Get(':taskId/events')
  @Header('Content-Type', 'text/event-stream')
  @Header('Cache-Control', 'no-cache')
  @Header('Connection', 'keep-alive')
  async streamTaskEvents(
    @Param('taskId') taskId: string,
    @Res() response: Response,
  ): Promise<void> {
    // 验证任务ID
    const taskExists = await this.taskService.checkTaskExists(taskId);
    if (!taskExists) {
      return response.status(404).json({
        code: 404,
        message: `任务${taskId}不存在`,
      });
    }
    
    // 设置SSE头部
    response.flushHeaders();
    
    // 初始化SSE连接
    const sendEvent = (data: any) => {
      response.write(`data: ${JSON.stringify(data)}\n\n`);
    };
    
    // 发送初始状态
    const initialStatus = await this.taskService.getTaskStatus(taskId);
    sendEvent({
      taskId,
      ...initialStatus,
      timestamp: Date.now(),
    });
    
    // 如果任务已完成或失败，直接结束连接
    if ([TaskStatus.COMPLETED, TaskStatus.FAILED].includes(initialStatus.status)) {
      return response.end();
    }
    
    // 订阅任务更新
    const subscription = this.taskService.subscribeToTaskUpdates(
      taskId,
      (update) => {
        sendEvent({
          taskId,
          ...update,
          timestamp: Date.now(),
        });
        
        // 任务完成或失败时关闭连接
        if ([TaskStatus.COMPLETED, TaskStatus.FAILED].includes(update.status)) {
          response.end();
        }
      }
    );
    
    // 处理客户端断开连接
    response.on('close', () => {
      subscription.unsubscribe();
    });
    
    // 设置连接超时保护
    setTimeout(() => {
      if (!response.writableEnded) {
        response.end();
        subscription.unsubscribe();
      }
    }, 3600000); // 1小时超时
  }
}
```

### 3.4 任务处理器实现

```typescript
// 简化示意
@Processor('ocr-tasks')
export class OcrProcessor {
  @Process()
  async processOcrTask(job: Job<OcrJobData>): Promise<OcrResult> {
    const { taskId, imageData } = job.data;
    
    // 更新任务状态为处理中
    await this.taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING, {
      progress: 0,
    });
    
    // 实际OCR处理逻辑
    const result = await this.ocrService.recognizeImage(imageData, {
      onProgress: async (progress) => {
        // 更新任务进度
        await this.taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING, {
          progress,
        });
      },
    });
    
    return result;
  }
  
  @OnQueueActive()
  async onActive(job: Job): Promise<void> {
    await this.taskService.updateTaskStatus(job.data.taskId, TaskStatus.ACTIVE);
  }
  
  @OnQueueCompleted()
  async onCompleted(job: Job, result: OcrResult): Promise<void> {
    await this.taskService.updateTaskStatus(job.data.taskId, TaskStatus.COMPLETED, {
      result,
      progress: 1,
    });
  }
  
  @OnQueueFailed()
  async onFailed(job: Job, error: Error): Promise<void> {
    await this.taskService.updateTaskStatus(job.data.taskId, TaskStatus.FAILED, {
      error: error.message,
    });
  }
}
```

### 3.5 任务管理服务

```typescript
// 简化示意
@Injectable()
export class TaskService {
  constructor(
    @InjectRedis() private readonly redis: Redis,
    private readonly pubSub: RedisPubSubService,
  ) {}
  
  /**
   * 创建新任务
   */
  async createTask(data: any, type: string): Promise<string> {
    const taskId = uuidv4();
    
    // 存储任务信息
    await this.redis.hmset(`task:${taskId}`, {
      status: TaskStatus.QUEUED,
      type,
      createdAt: Date.now(),
      data: JSON.stringify(data),
    });
    
    // 设置过期时间（24小时）
    await this.redis.expire(`task:${taskId}`, 86400);
    
    return taskId;
  }
  
  /**
   * 更新任务状态
   */
  async updateTaskStatus(
    taskId: string,
    status: TaskStatus,
    data?: Record<string, any>,
  ): Promise<void> {
    const update: Record<string, any> = {
      status,
      updatedAt: Date.now(),
    };
    
    if (data) {
      if (data.result) update.result = JSON.stringify(data.result);
      if (data.error) update.error = data.error;
      if (typeof data.progress === 'number') update.progress = data.progress.toString();
    }
    
    // 更新Redis中的任务状态
    await this.redis.hmset(`task:${taskId}`, update);
    
    // 发布状态更新事件
    await this.pubSub.publish(`task:${taskId}:updates`, {
      status,
      ...data,
    });
  }
  
  /**
   * 获取任务状态
   */
  async getTaskStatus(taskId: string): Promise<Record<string, any>> {
    const task = await this.redis.hgetall(`task:${taskId}`);
    
    if (!task || Object.keys(task).length === 0) {
      throw new NotFoundException(`任务${taskId}不存在`);
    }
    
    const result: Record<string, any> = {
      status: task.status,
      type: task.type,
      createdAt: parseInt(task.createdAt),
    };
    
    if (task.progress) result.progress = parseFloat(task.progress);
    if (task.error) result.error = task.error;
    if (task.result) result.result = JSON.parse(task.result);
    if (task.updatedAt) result.updatedAt = parseInt(task.updatedAt);
    
    return result;
  }
  
  /**
   * 订阅任务状态更新
   */
  subscribeToTaskUpdates(
    taskId: string,
    callback: (data: any) => void,
  ): Subscription {
    return this.pubSub.subscribe(`task:${taskId}:updates`, callback);
  }
}
```

### 3.6 队列管理服务

```typescript
// 简化示意
@Injectable()
export class QueueManagerService {
  constructor(
    @InjectQueue('ocr-tasks') private readonly ocrQueue: Queue,
    @InjectQueue('address-tasks') private readonly addressQueue: Queue,
    private readonly taskService: TaskService,
  ) {
    this.queues = {
      'ocr-tasks': ocrQueue,
      'address-tasks': addressQueue,
    };
    
    this.initQueueEvents();
  }
  
  /**
   * 添加任务到队列
   */
  async addTask(
    taskType: string,
    data: any,
    options: {
      priority?: number;
      delay?: number;
    } = {},
  ): Promise<string> {
    // 创建任务记录并获取任务ID
    const taskId = await this.taskService.createTask(data, taskType);
    
    // 添加任务到队列
    const queue = this.getQueueByTaskType(taskType);
    const jobData = { taskId, ...data };
    
    await queue.add(jobData, {
      priority: options.priority || QueuePriority.NORMAL,
      delay: options.delay || 0,
      jobId: taskId,
    });
    
    return taskId;
  }
  
  /**
   * 获取队列状态
   */
  async getQueueStatus(queueName?: string): Promise<Record<string, any>> {
    if (queueName) {
      const queue = this.queues[queueName];
      if (!queue) {
        throw new NotFoundException(`队列${queueName}不存在`);
      }
      
      const [waiting, active, completed, failed] = await Promise.all([
        queue.getWaitingCount(),
        queue.getActiveCount(),
        queue.getCompletedCount(),
        queue.getFailedCount(),
      ]);
      
      return {
        name: queueName,
        waiting,
        active,
        completed,
        failed,
      };
    }
    
    // 获取所有队列的状态
    const status = await Promise.all(
      Object.entries(this.queues).map(async ([name, queue]) => {
        const [waiting, active, completed, failed] = await Promise.all([
          queue.getWaitingCount(),
          queue.getActiveCount(),
          queue.getCompletedCount(),
          queue.getFailedCount(),
        ]);
        
        return {
          name,
          waiting,
          active,
          completed,
          failed,
        };
      })
    );
    
    return { queues: status };
  }
}
```

## 4. 系统架构

### 4.1 模块结构

```
queue/
├── config/
│   └── queue.config.ts              # 队列配置
├── controllers/
│   ├── task-events.controller.ts    # SSE事件控制器
│   └── queue-admin.controller.ts    # 队列管理控制器
├── processors/
│   ├── ocr.processor.ts             # OCR任务处理器
│   └── address.processor.ts         # 地址任务处理器
├── services/
│   ├── task.service.ts              # 任务管理服务
│   ├── queue-manager.service.ts     # 队列管理服务
│   └── pub-sub.service.ts           # 发布订阅服务
└── queue.module.ts                  # 队列模块定义
```

### 4.2 处理流程图

```
1. 网关模块发送异步任务请求
2. 队列管理服务创建任务记录和生成任务ID
3. 任务被添加到Redis队列
4. 队列管理服务返回任务ID和SSE连接URL
5. 客户端建立SSE连接
6. 任务处理器从队列获取任务并处理
7. 处理过程中更新任务状态和进度
8. 状态更新通过Redis Pub/Sub发布
9. SSE控制器接收状态更新并推送给客户端
10. 任务完成后，最终结果推送给客户端并关闭SSE连接
```

## 5. 接口定义

### 5.1 任务状态SSE接口

```
GET /tasks/{taskId}/events
Headers:
  Authorization: Bearer <token>
  Accept: text/event-stream
```

### 5.2 任务状态查询接口

```
GET /tasks/{taskId}
Headers:
  Authorization: Bearer <token>

Response:
{
  "success": true,
  "jobId": "job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a",
  "status": "completed",
  "createdAt": 1633456789000,
  "updatedAt": 1633456792000,
  "result": {
    // 任务结果数据
  },
  "requestId": "req_abcdef123456",
  "responseTime": 45
}
```

### 5.3 队列管理接口

```
GET /queues/status?name=ocr-tasks
Headers:
  X-API-KEY: <内部API密钥>

Response:
{
  "name": "ocr-tasks",
  "waiting": 5,
  "active": 3,
  "completed": 150,
  "failed": 2
}
```

## 6. 实现要点

### 6.1 任务状态管理

- 使用Redis存储任务状态和元数据
- 采用发布/订阅模式推送状态更新
- 提供统一的状态更新接口

### 6.2 SSE连接管理

- 直接管理SSE连接的生命周期
- 处理客户端意外断开连接的情况
- 实现连接超时保护

### 6.3 任务优先级和限流

- 基于优先级的任务调度
- 实现并发控制，避免资源耗尽
- 支持任务延迟执行

## 7. 后续优化方向

- **任务依赖关系管理**：支持任务流和工作流
- **动态扩缩容**：根据队列负载自动调整处理器数量
- **批量任务处理**：优化对大批量相似任务的处理
- **任务进度估算**：提供更精确的任务完成时间预估 