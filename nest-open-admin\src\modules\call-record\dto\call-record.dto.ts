import { Type } from 'class-transformer';
import {
  IsString,
  IsArray,
  IsOptional,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsBoolean,
  MinLength,
  <PERSON><PERSON>ength,
  <PERSON>,
  IsInt,
  Max,
  IsIn,
  IsDateString,
  IsObject,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * 创建调用记录DTO
 */
export class CreateCallRecordDto {
  @ApiProperty({ description: '用户ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  userId: number;

  @ApiProperty({ description: '服务ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  serviceId: number;

  @ApiProperty({ description: '唯一请求ID', example: 'req_1234567890abcdef' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  requestId: string;

  @ApiProperty({ description: '请求输入', example: '{"text": "需要处理的文本"}' })
  @IsString()
  @IsNotEmpty()
  input: string;

  @ApiPropertyOptional({ description: '响应输出', example: '{"result": "处理结果"}' })
  @IsOptional()
  @IsString()
  output?: string;

  @ApiProperty({ description: '本次调用消耗次数', example: 1, minimum: 0 })
  @IsInt()
  @Min(0)
  cost: number;

  @ApiProperty({ description: '本次调用金额', example: 0.10, minimum: 0 })
  @IsNumber({ maxDecimalPlaces: 4 })
  @Min(0)
  amount: number;

  @ApiProperty({ description: '是否成功', example: true })
  @IsBoolean()
  success: boolean;

  @ApiPropertyOptional({ description: '错误信息', example: '请求超时' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  errorMessage?: string;

  @ApiPropertyOptional({ description: '响应时间（毫秒）', example: 1500, minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  responseTime?: number;

  @ApiPropertyOptional({ description: 'HTTP状态码', example: 200 })
  @IsOptional()
  @IsInt()
  @Min(100)
  @Max(599)
  httpStatus?: number;

  @ApiPropertyOptional({ description: '客户端IP', example: '***********' })
  @IsOptional()
  @IsString()
  @MaxLength(45)
  clientIp?: string;

  @ApiPropertyOptional({ description: 'User-Agent', example: 'Mozilla/5.0...' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  userAgent?: string;

  @ApiPropertyOptional({ description: '请求头信息' })
  @IsOptional()
  @IsObject()
  requestHeaders?: Record<string, any>;

  @ApiPropertyOptional({ description: '响应头信息' })
  @IsOptional()
  @IsObject()
  responseHeaders?: Record<string, any>;
}

/**
 * 更新调用记录DTO
 */
export class UpdateCallRecordDto {
  @ApiPropertyOptional({ description: '响应输出', example: '{"result": "处理结果"}' })
  @IsOptional()
  @IsString()
  output?: string;

  @ApiPropertyOptional({ description: '是否成功', example: true })
  @IsOptional()
  @IsBoolean()
  success?: boolean;

  @ApiPropertyOptional({ description: '错误信息', example: '请求超时' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  errorMessage?: string;

  @ApiPropertyOptional({ description: '响应时间（毫秒）', example: 1500, minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  responseTime?: number;

  @ApiPropertyOptional({ description: 'HTTP状态码', example: 200 })
  @IsOptional()
  @IsInt()
  @Min(100)
  @Max(599)
  httpStatus?: number;

  @ApiPropertyOptional({ description: '响应头信息' })
  @IsOptional()
  @IsObject()
  responseHeaders?: Record<string, any>;
}

/**
 * 调用记录查询DTO
 */
export class QueryCallRecordDto {
  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', example: 10, minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: '用户ID', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  userId?: number;

  @ApiPropertyOptional({ description: '服务ID', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  serviceId?: number;

  @ApiPropertyOptional({ description: '请求ID搜索', example: 'req_123' })
  @IsOptional()
  @IsString()
  requestId?: string;

  @ApiPropertyOptional({ description: '是否成功' })
  @IsOptional()
  @IsBoolean()
  success?: boolean;

  @ApiPropertyOptional({ description: '最小消耗次数', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  minCost?: number;

  @ApiPropertyOptional({ description: '最大消耗次数', example: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  maxCost?: number;

  @ApiPropertyOptional({ description: '最小金额', example: 0.01 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minAmount?: number;

  @ApiPropertyOptional({ description: '最大金额', example: 10.00 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxAmount?: number;

  @ApiPropertyOptional({ description: '最小响应时间（毫秒）', example: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  minResponseTime?: number;

  @ApiPropertyOptional({ description: '最大响应时间（毫秒）', example: 5000 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  maxResponseTime?: number;

  @ApiPropertyOptional({ description: 'HTTP状态码', example: 200 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(100)
  @Max(599)
  httpStatus?: number;

  @ApiPropertyOptional({ description: '客户端IP', example: '***********' })
  @IsOptional()
  @IsString()
  clientIp?: string;

  @ApiPropertyOptional({ description: '开始时间' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: '结束时间' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: '排序字段', example: 'createdAt' })
  @IsOptional()
  @IsString()
  @IsIn(['id', 'requestId', 'cost', 'amount', 'success', 'responseTime', 'httpStatus', 'createdAt'])
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', example: 'DESC' })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}

/**
 * 调用记录响应DTO
 */
export class CallRecordResponseDto {
  @ApiProperty({ description: '记录ID', example: 1 })
  id: number;

  @ApiProperty({ description: '用户ID', example: 1 })
  userId: number;

  @ApiProperty({ description: '服务ID', example: 1 })
  serviceId: number;

  @ApiProperty({ description: '唯一请求ID', example: 'req_1234567890abcdef' })
  requestId: string;

  @ApiProperty({ description: '请求输入', example: '{"text": "需要处理的文本"}' })
  input: string;

  @ApiPropertyOptional({ description: '响应输出', example: '{"result": "处理结果"}' })
  output?: string;

  @ApiProperty({ description: '本次调用消耗次数', example: 1 })
  cost: number;

  @ApiProperty({ description: '本次调用金额', example: 0.10 })
  amount: number;

  @ApiProperty({ description: '是否成功', example: true })
  success: boolean;

  @ApiPropertyOptional({ description: '错误信息' })
  errorMessage?: string;

  @ApiPropertyOptional({ description: '响应时间（毫秒）', example: 1500 })
  responseTime?: number;

  @ApiPropertyOptional({ description: 'HTTP状态码', example: 200 })
  httpStatus?: number;

  @ApiPropertyOptional({ description: '客户端IP', example: '***********' })
  clientIp?: string;

  @ApiPropertyOptional({ description: 'User-Agent' })
  userAgent?: string;

  @ApiPropertyOptional({ description: '请求头信息' })
  requestHeaders?: Record<string, any>;

  @ApiPropertyOptional({ description: '响应头信息' })
  responseHeaders?: Record<string, any>;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 关联信息
  @ApiPropertyOptional({ description: '用户信息' })
  user?: {
    id: number;
    username: string;
    email: string;
  };

  @ApiPropertyOptional({ description: '服务信息' })
  service?: {
    id: number;
    code: string;
    name: string;
    type: string;
  };
}

/**
 * 调用记录列表响应DTO
 */
export class CallRecordListResponseDto {
  @ApiProperty({ description: '调用记录列表', type: [CallRecordResponseDto] })
  data: CallRecordResponseDto[];

  @ApiProperty({ description: '总数', example: 500 })
  total: number;

  @ApiProperty({ description: '当前页', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  limit: number;

  @ApiProperty({ description: '总页数', example: 50 })
  totalPages: number;
}

/**
 * 调用记录统计DTO
 */
export class CallRecordStatsDto {
  @ApiProperty({ description: '总调用次数', example: 5000 })
  totalCalls: number;

  @ApiProperty({ description: '成功调用次数', example: 4800 })
  successCalls: number;

  @ApiProperty({ description: '失败调用次数', example: 200 })
  failedCalls: number;

  @ApiProperty({ description: '成功率', example: 96.0 })
  successRate: number;

  @ApiProperty({ description: '总消耗次数', example: 5000 })
  totalCost: number;

  @ApiProperty({ description: '总金额', example: 500.00 })
  totalAmount: number;

  @ApiProperty({ description: '平均响应时间（毫秒）', example: 1200 })
  avgResponseTime: number;

  @ApiProperty({ description: '按服务分组统计' })
  byService: Record<string, {
    calls: number;
    successCalls: number;
    failedCalls: number;
    totalCost: number;
    totalAmount: number;
    avgResponseTime: number;
  }>;

  @ApiProperty({ description: '按用户分组统计' })
  byUser: Record<string, {
    calls: number;
    successCalls: number;
    failedCalls: number;
    totalCost: number;
    totalAmount: number;
  }>;

  @ApiProperty({ description: '按时间分组统计（小时）' })
  byHour: Record<string, {
    calls: number;
    successCalls: number;
    failedCalls: number;
  }>;

  @ApiProperty({ description: '按HTTP状态码分组统计' })
  byHttpStatus: Record<string, number>;
}

/**
 * 调用记录导出DTO
 */
export class ExportCallRecordDto extends QueryCallRecordDto {
  @ApiPropertyOptional({ description: '导出格式', example: 'xlsx' })
  @IsOptional()
  @IsString()
  @IsIn(['xlsx', 'csv', 'json'])
  format?: string = 'xlsx';

  @ApiPropertyOptional({ description: '导出字段', example: ['requestId', 'success', 'cost', 'amount', 'createdAt'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  fields?: string[];

  @ApiPropertyOptional({ description: '是否包含关联信息', example: true })
  @IsOptional()
  @IsBoolean()
  includeRelations?: boolean = false;
}