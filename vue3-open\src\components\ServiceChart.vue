<template>
  <div class="service-chart" ref="chartContainer"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

interface ServiceData {
  name: string
  value: number
  color?: string
}

interface Props {
  data: ServiceData[]
  title?: string
  height?: number
  chartType?: 'pie' | 'doughnut' | 'bar'
  showLegend?: boolean
  showLabel?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '服务分布',
  height: 300,
  chartType: 'doughnut',
  showLegend: true,
  showLabel: true,
  loading: false
})

const chartContainer = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 默认颜色配置
const defaultColors = [
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399',
  '#53A8FF', '#85CE61', '#EEBE77', '#F78989', '#A6A9AD',
  '#337ECC', '#529B2E', '#B88230', '#C45656', '#73767A'
]

const initChart = () => {
  if (!chartContainer.value) return

  chartInstance = echarts.init(chartContainer.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance) return

  let option: echarts.EChartsOption

  if (props.chartType === 'bar') {
    option = getBarChartOption()
  } else {
    option = getPieChartOption()
  }

  chartInstance.setOption(option, true)

  if (props.loading) {
    chartInstance.showLoading({
      text: '加载中...',
      color: '#409EFF',
      textColor: '#909399',
      maskColor: 'rgba(255, 255, 255, 0.8)',
      zlevel: 0
    })
  } else {
    chartInstance.hideLoading()
  }
}

const getPieChartOption = (): echarts.EChartsOption => {
  const total = props.data.reduce((sum, item) => sum + item.value, 0)
  
  return {
    title: {
      text: props.title,
      left: 'left',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
        color: '#303133'
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff'
      },
      formatter: (params: any) => {
        const percentage = ((params.value / total) * 100).toFixed(1)
        return `
          <div style="padding: 8px;">
            <div style="margin-bottom: 4px; font-weight: bold;">${params.name}</div>
            <div style="display: flex; align-items: center;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${params.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span>数量: ${params.value.toLocaleString()}</span>
            </div>
            <div style="margin-top: 4px; color: #ccc;">占比: ${percentage}%</div>
          </div>
        `
      }
    },
    legend: props.showLegend ? {
      orient: 'vertical',
      right: '10%',
      top: 'center',
      itemGap: 12,
      textStyle: {
        color: '#606266',
        fontSize: 12
      },
      formatter: (name: string) => {
        const item = props.data.find(d => d.name === name)
        if (item) {
          const percentage = ((item.value / total) * 100).toFixed(1)
          return `${name} (${percentage}%)`
        }
        return name
      }
    } : undefined,
    series: [
      {
        name: props.title,
        type: 'pie',
        radius: props.chartType === 'doughnut' ? ['40%', '70%'] : '70%',
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: props.showLabel ? {
          show: true,
          position: 'outside',
          formatter: (params: any) => {
            const percentage = ((params.value / total) * 100).toFixed(1)
            return `${params.name}\n${percentage}%`
          },
          fontSize: 12,
          color: '#606266'
        } : {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          },
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: props.showLabel
        },
        data: props.data.map((item, index) => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.color || defaultColors[index % defaultColors.length]
          }
        }))
      }
    ],
    animation: true,
    animationType: 'scale',
    animationEasing: 'elasticOut',
    animationDelay: (idx: number) => Math.random() * 200
  }
}

const getBarChartOption = (): echarts.EChartsOption => {
  return {
    title: {
      text: props.title,
      left: 'left',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
        color: '#303133'
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff'
      },
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const data = params[0]
        return `
          <div style="padding: 8px;">
            <div style="margin-bottom: 4px; font-weight: bold;">${data.name}</div>
            <div style="display: flex; align-items: center;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${data.color}; border-radius: 2px; margin-right: 8px;"></span>
              <span>数量: ${data.value.toLocaleString()}</span>
            </div>
          </div>
        `
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.data.map(item => item.name),
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      },
      axisLabel: {
        color: '#909399',
        fontSize: 12,
        interval: 0,
        rotate: props.data.length > 6 ? 45 : 0
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisLabel: {
        color: '#909399',
        fontSize: 12,
        formatter: (value: number) => {
          if (value >= 1000000) {
            return (value / 1000000).toFixed(1) + 'M'
          } else if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'K'
          }
          return value.toString()
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#F2F6FC',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: props.title,
        type: 'bar',
        barWidth: '60%',
        itemStyle: {
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        },
        data: props.data.map((item, index) => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: item.color || defaultColors[index % defaultColors.length]
                },
                {
                  offset: 1,
                  color: (item.color || defaultColors[index % defaultColors.length]) + '80'
                }
              ]
            }
          }
        }))
      }
    ],
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut',
    animationDelay: (idx: number) => idx * 100
  }
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 监听数据变化
watch(
  () => [props.data, props.title, props.chartType, props.showLegend, props.showLabel, props.loading],
  () => {
    nextTick(() => {
      updateChart()
    })
  },
  { deep: true }
)

// 监听容器高度变化
watch(
  () => props.height,
  () => {
    if (chartContainer.value) {
      chartContainer.value.style.height = props.height + 'px'
      nextTick(() => {
        resizeChart()
      })
    }
  }
)

onMounted(() => {
  nextTick(() => {
    if (chartContainer.value) {
      chartContainer.value.style.height = props.height + 'px'
      initChart()
    }
  })

  // 监听窗口大小变化
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', resizeChart)
})

// 暴露方法给父组件
defineExpose({
  resize: resizeChart,
  getInstance: () => chartInstance
})
</script>

<style scoped>
.service-chart {
  width: 100%;
  min-height: 200px;
}
</style>