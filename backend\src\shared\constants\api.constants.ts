/**
 * 统一的API常量定义
 * 避免在多个模块中重复定义相同的常量
 */

/**
 * API路径常量
 */
export const API_PATHS = {
  // OCR服务相关
  OCR: {
    PREFIX: '/v1/op/ocr',
    UPLOAD: '/v1/op/ocr/upload',
    RECOGNIZE: '/v1/op/ocr/recognize',
    QUEUE_NAME: 'ocr',
  },

  // 申通面单OCR服务相关
  STO_OCR: {
    PREFIX: '/v1/op/ocr/sto',
    UPLOAD: '/v1/op/ocr/sto/upload',
    RECOGNIZE: '/v1/op/ocr/sto/recognize',
    QUEUE_NAME: 'sto-ocr',
  },
  
  // 地址服务相关
  ADDRESS: {
    PREFIX: '/v1/op/address',
    EXTRACT: '/v1/op/address/extract',
    EXTRACT_BATCH: '/v1/op/address/extract-batch',
    QUEUE_NAME: 'extract-address',
  },
  
  // 地理坐标服务相关
  GEO: {
    PREFIX: '/v1/op/geo',
    REVERSE: '/v1/op/geo/reverse',
    FORWARD: '/v1/op/geo/forward',
    BATCH_REVERSE: '/v1/op/geo/batch-reverse',
    BATCH_FORWARD: '/v1/op/geo/batch-forward',
    QUEUE_NAME: 'rev-geo',
  },
  
  // 任务管理相关
  TASKS: {
    PREFIX: '/v1/op/tasks',
    STATUS: '/v1/op/tasks/:taskId',
    EVENTS: '/v1/op/tasks/:taskId/events',
    LIST: '/v1/op/tasks',
    CLEANUP: '/v1/op/tasks/cleanup',
  },

  // 系统管理相关
  SYSTEM: {
    HEALTH: '/v1/op/health',
    STATS: '/v1/op/stats',
    ROUTES: '/v1/op/routes',
    ROUTES_RESET: '/v1/op/routes/reset',
  },
} as const;

/**
 * 服务端点配置
 */
export const SERVICE_ENDPOINTS = {
  OCR_SERVICE: process.env.OCR_SERVICE_URL || 'http://localhost:8866',
  // 申通面单OCR单独服务键，默认回退到 OCR_SERVICE
  STO_OCR_SERVICE: process.env.STO_OCR_SERVICE_URL || process.env.OCR_SERVICE_URL || 'http://localhost:8866',
  ADDRESS_SERVICE: process.env.ADDRESS_SERVICE_URL || 'http://localhost:8866',
  GEO_SERVICE: process.env.GEO_SERVICE_URL || 'http://localhost:8866',
} as const;

/**
 * 队列配置常量
 */
export const QUEUE_CONFIGS = {
  OCR_QUEUE: {
    name: 'ocr',
    concurrency: 5,
    attempts: 3,
    timeout: 180000, // 3分钟
    priority: 1,
  },

  STO_OCR_QUEUE: {
    name: 'sto-ocr',
    concurrency: 3, // 申通OCR专用队列，并发数较低以保证质量
    attempts: 3,
    timeout: 200000, // 申通OCR可能需要更长处理时间
    priority: 2, // 稍高优先级
  },
  
  ADDRESS_QUEUE: {
    name: 'extract-address',
    concurrency: 8,
    attempts: 2,
    timeout: 120000, // 2分钟
    priority: 1,
  },
  
  GEO_QUEUE: {
    name: 'rev-geo',
    concurrency: 10,
    attempts: 2,
    timeout: 60000, // 1分钟
    priority: 1,
  },
} as const;

/**
 * 处理模式枚举
 */
export enum ProcessingMode {
  SYNC = 'sync',
  ASYNC = 'async',
  PROXY_ASYNC = 'proxy-async',
}

/**
 * 队列优先级
 */
export enum QueuePriority {
  LOW = 10,
  NORMAL = 5,
  HIGH = 1,
  CRITICAL = 0,
}

/**
 * 任务状态
 */
export enum TaskStatus {
  QUEUED = 'queued',
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  TIMEOUT = 'timeout',
  CANCELLED = 'cancelled',
}

/**
 * 支持的文件类型
 */
export const SUPPORTED_MIME_TYPES = {
  IMAGES: [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp',
  ],
  DOCUMENTS: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ],
  ALL: [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ],
} as const;

/**
 * 文件大小限制
 */
export const FILE_SIZE_LIMITS = {
  IMAGE_MAX_SIZE: 5 * 1024 * 1024, // 5MB
  DOCUMENT_MAX_SIZE: 10 * 1024 * 1024, // 10MB
  BATCH_MAX_FILES: 50, // 批量处理最大文件数
} as const;

/**
 * 限流配置
 */
export const RATE_LIMIT_CONFIG = {
  // 全局限制
  GLOBAL: {
    REQUESTS_PER_MINUTE: 1000,
    REQUESTS_PER_HOUR: 10000,
    REQUESTS_PER_DAY: 100000,
  },
  
  // 用户级别限制
  USER: {
    REQUESTS_PER_MINUTE: 60,
    REQUESTS_PER_HOUR: 1000,
    REQUESTS_PER_DAY: 10000,
    CONCURRENT_REQUESTS: 5,
  },
  
  // API密钥级别限制
  API_KEY: {
    REQUESTS_PER_MINUTE: 100,
    REQUESTS_PER_HOUR: 2000,
    REQUESTS_PER_DAY: 20000,
    CONCURRENT_REQUESTS: 10,
  },
  
  // 特定服务限制
  SERVICES: {
    OCR: {
      REQUESTS_PER_MINUTE: 30,
      FILE_UPLOADS_PER_HOUR: 100,
    },
    ADDRESS: {
      REQUESTS_PER_MINUTE: 100,
      BATCH_REQUESTS_PER_HOUR: 50,
    },
    GEO: {
      REQUESTS_PER_MINUTE: 200,
      BATCH_REQUESTS_PER_HOUR: 100,
    },
  },
} as const;

/**
 * 缓存配置
 */
export const CACHE_CONFIG = {
  // API密钥缓存
  API_KEY: {
    TTL: 3600, // 1小时
    MAX_ITEMS: 10000,
  },
  
  // 路由配置缓存
  ROUTES: {
    TTL: 3600, // 1小时
    MAX_ITEMS: 100,
  },
  
  // 任务结果缓存
  TASK_RESULTS: {
    TTL: 86400, // 24小时
    MAX_ITEMS: 50000,
  },
  
  // 用户会话缓存
  USER_SESSION: {
    TTL: 7200, // 2小时
    MAX_ITEMS: 10000,
  },
} as const;

/**
 * 错误码定义
 */
export const ERROR_CODES = {
  // 认证相关
  AUTH: {
    INVALID_CREDENTIALS: '10001',
    TOKEN_EXPIRED: '10002',
    TOKEN_INVALID: '10003',
    ACCOUNT_LOCKED: '10004',
    VERIFICATION_CODE_INVALID: '10005',
  },
  
  // API密钥相关
  API_KEY: {
    INVALID: '20001',
    EXPIRED: '20002',
    SCOPE_INSUFFICIENT: '20003',
    RATE_LIMITED: '20004',
    IP_RESTRICTED: '20005',
  },
  
  // 业务相关
  BUSINESS: {
    INSUFFICIENT_BALANCE: '30001',
    SERVICE_UNAVAILABLE: '30002',
    FILE_TOO_LARGE: '30003',
    UNSUPPORTED_FORMAT: '30004',
    PROCESSING_FAILED: '30005',
  },
  
  // 系统相关
  SYSTEM: {
    INTERNAL_ERROR: '50001',
    SERVICE_TIMEOUT: '50002',
    RESOURCE_EXHAUSTED: '50003',
    MAINTENANCE_MODE: '50004',
  },
} as const;

/**
 * API版本配置
 */
export const API_VERSIONS = {
  V1: 'v1',
  V2: 'v2',
  CURRENT: 'v1',
  SUPPORTED: ['v1', 'v2'] as string[],
  DEPRECATED: [] as string[],
} as const;
