### 项目背景业务介绍

1、开放平台提供多种API调用服务，目前暂提供物流面单OCR识别、物流文本精确提取省市区详细地址、地理坐标逆解析出地址三种API调用服务产品。
2、此三种服务产品对于新用户都给予每日5次免费调用次数，新用户在创建对应服务产品的密钥时需要系统自动下单对应服务产品0元购买后更新对应服务产品调用次数来达到免费调用的结果。
3、用户可以直接购买API服务产品得到调用次数，也可以充值额度，每调用一次就记录且会扣减费用。个人/企业实名认证会奖励调用次数5次/10次。当用户充值或者购买服务后，对应会增加调用次数，当可调用达到20%时触发预警临界值需要告知用户可用次数不足建议尽快充值。
4、系统需要默认设置管理员角色用户账户，如果是管理员角色，则可以通过后台管理服务产品，包括且不限于产品价格和类型种类等，包括查看财务、订单等。

### 技术栈
- 后端框架 : NestJS 11.x
- 数据库 : MySQL 5.x + TypeORM
- 缓存 : Redis (ioredis)
- 消息队列 : RabbitMQ (基于Redis)
- 认证 : JWT + Passport
- 文档 : Swagger
- 日志 : Winston
- 验证 : class-validator + class-transformer

### 开发环境配置

#### RabbitMQ 服务启动

使用 Docker 启动 RabbitMQ 服务：

```bash
# 启动 RabbitMQ 服务（带管理界面）
docker run -d \
  --name rabbitmq-dev \
  -p 5672:5672 \
  -p 15672:15672 \
  -e RABBITMQ_DEFAULT_USER=admin \
  -e RABBITMQ_DEFAULT_PASS=szyl_open \
  rabbitmq:3-management

# 或者使用 docker-compose（推荐）
# 创建 docker-compose.yml 文件，内容如下：
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    ports:
      - "5672:5672"    # AMQP 端口
      - "15672:15672"  # 管理界面端口
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: szyl_open
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    restart: unless-stopped

volumes:
  rabbitmq_data:
```

```bash
# 使用 docker-compose 启动
docker-compose up -d
docker compose up -d --build
# 停止服务
docker-compose down
```

**访问管理界面：**
- URL: http://localhost:15672
- 用户名: admin
- 密码: szyl_open

**注意事项：**
- 确保端口 5672 和 15672 未被占用
- 生产环境请修改默认密码
- 可通过环境变量 `RABBITMQ_USERNAME` 和 `RABBITMQ_PASSWORD` 覆盖配置

### 模块结构
项目采用模块化设计，主要包含：

- 认证模块 (auth): 用户登录、JWT认证
- 用户模块 (user): 用户管理、实名认证
- 服务模块 (service): API服务定义和管理
- API密钥模块 (api-key): 密钥生成和管理
- 计费模块 (billing): 计费检查、扣费处理
- 订单模块 (order): 订单管理、支付处理
- OCR模块 (ocr): 核心业务API服务
- 用户服务关联模块 (user-service): 用户与服务的关联管理
- 调用记录模块 (call-record): API调用记录
- 预警记录模块 (alert-record): 系统预警管理

### 精简高效的架构设计

```
graph TD
  A[客户端] --> B[NestJS网关]
  B --> C[认证服务]
  B --> D[计费服务]
  B --> E[API服务集群]
  D --> F[RabbitMQ]
  F --> G[订单服务]
  F --> H[通知服务]
  C --> I[Redis]
  D --> I
  G --> J[MySQL]
  H --> J

```

### 微服务拆分

我们将系统拆分为以下几个微服务，通过RabbitMQ通信：


### 高并发处理

1. **网关层**：

- 使用NestJS作为网关，接收所有HTTP请求。

- 在网关层进行API密钥验证（调用AuthService微服务验证）。

- 验证通过后，将请求转发到对应的API服务（通过RabbitMQ消息队列，异步处理）。

2. **异步调用API**：

- 用户调用API时，网关验证密钥和余量（通过Redis缓存快速读取余量，避免每次查库）。

- 如果余量足够，网关将调用请求发送到RabbitMQ队列，立即返回用户“请求已接受，处理中”的响应。

- 后端微服务（如OCR服务）从队列中取出请求进行处理，处理完成后将结果存储，并通过WebSocket或回调通知用户（或者用户轮询结果）。

- 计费服务监听队列，处理调用后的扣费。

3. **扣费原子操作**：

- 使用Redis的INCRBY/DECRBY命令来原子性扣减余量（或者使用Lua脚本保证原子性）。

- 同时，将扣费记录发送到消息队列，由计费服务异步更新MySQL中的已使用次数。

4. **预警机制**：

- 在计费服务扣减余量后，检查剩余比例。如果低于20%且未发送预警，则发送预警消息到通知服务。

### 免费调用实现

1. 新用户注册时，为每个服务创建一条user_service记录，total_count=10（每日免费次数），并设置过期时间为当天结束。

2. 每日零点，通过定时任务重置免费次数（只重置免费用户的次数，付费用户购买的服务次数不会重置）。

3. 用户调用时，优先使用免费次数（如果还在有效期内），免费次数用尽后再使用购买的次数。

### RabbitMQ消息处理流程

```
sequenceDiagram
  participant 网关
  participant RabbitMQ
  participant 计费服务
  participant API服务
  
  网关->>+RabbitMQ: 发布API调用消息
  RabbitMQ->>计费服务: 消费消息（扣减额度）
  计费服务->>Redis: 原子操作扣减
  计费服务->>RabbitMQ: 发布API执行消息
  RabbitMQ->>API服务: 消费消息（执行API）
  API服务->>数据库: 存储结果
  API服务->>RabbitMQ: 发布通知消息（可选）

```

### 以下是核心实体：

```
1. UserEntity (用户表)

2. ServiceEntity (API服务表)

3. UserServiceEntity (用户服务关联表，记录用户购买的服务及其额度)

4. ApiKeyEntity (API密钥表)

5. OrderEntity (订单表)

6. CallRecordEntity (调用记录表)

7. AlertRecordEntity (预警记录表)
```

#### 实体关系总结

```
erDiagram
    User ||--o{ ApiKey : has
    User ||--o{ UserService : subscribes
    User ||--o{ Order : places
    User ||--o{ CallRecord : generates
    User ||--o{ AlertRecord : receives
    
    Service ||--o{ ApiKey : has
    Service ||--o{ UserService : assigned
    Service ||--o{ Order : purchased
    Service ||--o{ CallRecord : processes
    Service ||--o{ AlertRecord : triggers
    
    ApiKey }|--|| User : "belongs to"
    ApiKey }|--|| Service : "belongs to"
    
    UserService }|--|| User : "belongs to"
    UserService }|--|| Service : "belongs to"
    
    Order }|--|| User : "belongs to"
    Order }|--|| Service : "optional"
    
    CallRecord }|--|| User : "belongs to"
    CallRecord }|--|| Service : "belongs to"
    
    AlertRecord }|--|| User : "belongs to"
    AlertRecord }|--|| Service : "optional"
```

