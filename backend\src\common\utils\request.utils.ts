import { Request } from 'express';

/**
 * 请求相关的工具函数
 */
export class RequestUtils {
  /**
   * 获取客户端IP地址
   * @param request Express请求对象
   * @returns 客户端IP地址
   */
  static getClientIp(request: Request): string {
    const xForwardedFor = request.headers['x-forwarded-for'] as string;
    if (xForwardedFor) {
      return xForwardedFor.split(',')[0];
    }
    return request.ip || '未知IP';
  }

  /**
   * 生成请求ID
   * @returns 唯一的请求ID
   */
  static generateRequestId(): string {
    return require('crypto').randomBytes(16).toString('hex');
  }

  /**
   * 获取日期键（YYYY-MM-DD格式）
   * @returns 日期字符串
   */
  static getDateKey(): string {
    return new Date().toISOString().split('T')[0];
  }

  /**
   * 从请求中提取API密钥
   * @param request Express请求对象
   * @returns API密钥
   */
  static extractApiKey(request: any): string | null {
    // 检查请求头
    const apiKey = request.headers['x-api-key'] || request.headers['api-key'];
    if (apiKey) {
      console.log(`[RequestUtils] 从请求头提取到API Key: ${apiKey}`);
      return apiKey;
    }

    // 检查查询参数
    if (request.query && request.query.api_key) {
      console.log(`[RequestUtils] 从查询参数提取到API Key: ${request.query.api_key}`);
      return request.query.api_key;
    }

    console.log(`[RequestUtils] 未找到API Key`);
    return null;
  }

  /**
   * 从请求中提取密钥秘钥
   * @param request Express请求对象
   * @returns 密钥秘钥
   */
  static extractSecretKey(request: any): string | null {
    console.log(`[RequestUtils] 请求头信息: X-API-SECRET=${request.headers['x-api-secret']}, X-SECRET-KEY=${request.headers['x-secret-key']}, API-SECRET=${request.headers['api-secret']}`);
    
    // 检查请求头 - 同时支持X-API-SECRET和X-SECRET-KEY两种格式
    const secretKey = request.headers['x-api-secret'] || request.headers['x-secret-key'] || request.headers['api-secret'];
    if (secretKey) {
      console.log(`[RequestUtils] 从请求头提取到Secret Key: ${secretKey.substring(0, 5)}...`);
      return secretKey;
    }

    // 检查查询参数
    if (request.query && request.query.secret_key) {
      console.log(`[RequestUtils] 从查询参数提取到Secret Key: ${request.query.secret_key.substring(0, 5)}...`);
      return request.query.secret_key;
    }

    console.log(`[RequestUtils] 未找到Secret Key`);
    return null;
  }

  /**
   * 从请求中提取JWT令牌
   * @param request Express请求对象
   * @returns JWT令牌字符串或null
   */
  static extractJwtToken(request: Request): string | null {
    // 从Authorization头中获取JWT令牌
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    
    // 从Cookie中获取JWT令牌
    const jwtCookie = request.cookies?.jwt;
    if (jwtCookie) {
      return jwtCookie;
    }
    
    return null;
  }

  /**
   * 从请求中提取签名认证信息
   * @param request Express请求对象
   * @returns 包含API密钥、时间戳、nonce和签名的对象，如果缺少任何一项则返回null
   */
  static extractSignatureAuth(request: Request): { apiKey: string; timestamp: string; nonce: string; signature: string } | null {
    const apiKey = request.headers['x-api-key'] as string;
    const timestamp = request.headers['x-timestamp'] as string;
    const nonce = request.headers['x-nonce'] as string;
    const signature = request.headers['x-signature'] as string;
    
    if (!apiKey || !timestamp || !nonce || !signature) {
      console.log(`[RequestUtils] 签名认证信息不完整: apiKey=${!!apiKey}, timestamp=${!!timestamp}, nonce=${!!nonce}, signature=${!!signature}`);
      return null;
    }
    
    console.log(`[RequestUtils] 提取到签名认证信息: apiKey=${apiKey}, timestamp=${timestamp}, nonce=${nonce}, signature=${signature}`);
    return { apiKey, timestamp, nonce, signature };
  }

  /**
   * 获取请求的完整URL
   * @param request Express请求对象
   * @returns 请求的完整URL
   */
  static getFullUrl(request: Request): string {
    const protocol = request.headers['x-forwarded-proto'] || request.protocol;
    const host = request.headers.host || 'localhost';
    return `${protocol}://${host}${request.originalUrl}`;
  }

  /**
   * 获取请求的User-Agent
   * @param request Express请求对象
   * @returns 请求的User-Agent
   */
  static getUserAgent(request: Request): string {
    return request.headers['user-agent'] || '未知用户代理';
  }

  /**
   * 获取请求的来源
   * @param request Express请求对象
   * @returns 请求的来源
   */
  static getReferer(request: Request): string {
    return request.headers.referer || '直接访问';
  }
}