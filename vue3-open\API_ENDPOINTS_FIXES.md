# API端点修复总结

## 修复概述

根据后端实际路由配置，修复了前端API测试工具和服务列表页面的端点路径映射，确保前后端路径完全一致。

## 后端路由配置

### 全局配置
- **全局前缀**: `v1` (在 `main.ts` 中设置)
- **开放API前缀**: `op` (在 `OpenModule` 中通过 `RouterModule.register` 设置)
- **最终路径格式**: `/v1/op/{service}/{method}`

### 具体API端点

#### OCR服务
- **OCR文件上传识别**: `/v1/op/ocr/upload`
- **OCR Base64图片识别**: `/v1/op/ocr/recognize`
- **申通面单OCR文件上传识别**: `/v1/op/ocr/sto/upload`
- **申通面单OCR Base64识别**: `/v1/op/ocr/sto/recognize`

#### 地址处理服务
- **从文本中提取地址信息**: `/v1/op/address/extract`
- **地址标准化**: `/v1/op/address/normalize`

#### 地理编码服务
- **逆地理编码（坐标转地址）**: `/v1/op/geo/reverse`
- **正地理编码（地址转坐标）**: `/v1/op/geo/forward`

#### 任务管理
- **获取任务状态**: `/v1/op/tasks/{taskId}`
- **任务事件流（SSE）**: `/v1/op/tasks/{taskId}/events`

## 前端修复内容

### 1. ApiTestView.vue 修复

#### 端点路径映射
```typescript
const endpointMap: Record<string, string> = {
  // OCR服务
  'OCR_UPLOAD': '/v1/op/ocr/upload',                    // OCR文件上传识别
  'OCR_RECOGNIZE': '/v1/op/ocr/recognize',              // OCR Base64图片识别
  'OCR_STO_UPLOAD': '/v1/op/ocr/sto/upload',            // 申通面单OCR文件上传识别
  'OCR_STO_RECOGNIZE': '/v1/op/ocr/sto/recognize',      // 申通面单OCR Base64识别
  
  // 地址服务
  'ADDRESS_EXTRACT': '/v1/op/address/extract',          // 从文本中提取地址信息
  'ADDRESS_NORMALIZE': '/v1/op/address/normalize',      // 地址标准化
  
  // 地理服务
  'GEO_REVERSE': '/v1/op/geo/reverse',                  // 逆地理编码（坐标转地址）
  'GEO_FORWARD': '/v1/op/geo/forward',                  // 正地理编码（地址转坐标）
}
```

#### 任务相关路径修复
- 任务状态查询: `/v1/op/tasks/{taskId}`
- 任务事件流: `/v1/op/tasks/{taskId}/events`

### 2. ServiceListView.vue 修复

#### 服务分类优化
```typescript
const categories = ref([
  { value: 'all', label: '全部' },
  { value: 'ocr', label: 'OCR识别' },
  { value: 'nlp', label: '自然语言处理' },
  { value: 'geo', label: '地理编码' },
  { value: 'ai_service', label: 'AI服务' },
  { value: 'cv', label: '计算机视觉' },
  { value: 'data', label: '数据处理' },
  { value: 'other', label: '其他服务' }
])
```

#### 文档映射更新
```typescript
const docSectionMap: Record<string, string> = {
  // OCR服务
  'OCR_UPLOAD': 'ocr-upload',                    // OCR文件上传识别
  'OCR_RECOGNIZE': 'ocr-recognize',              // OCR Base64图片识别
  'OCR_STO_UPLOAD': 'ocr-sto-upload',            // 申通面单OCR文件上传识别
  'OCR_STO_RECOGNIZE': 'ocr-sto-recognize',      // 申通面单OCR Base64识别
  
  // 地址服务
  'ADDRESS_EXTRACT': 'address-extract',          // 从文本中提取地址信息
  'ADDRESS_NORMALIZE': 'address-normalize',      // 地址标准化
  
  // 地理服务
  'GEO_REVERSE': 'geo-reverse',                  // 逆地理编码（坐标转地址）
  'GEO_FORWARD': 'geo-forward',                  // 正地理编码（地址转坐标）
}
```

#### 图标和颜色优化
- 地理服务使用 `Location` 图标和青色 `#06b6d4`
- 其他服务使用 `Document` 图标作为默认图标

## 请求流程验证

### 完整请求流程
1. **前端请求**: `/v1/op/geo/reverse?lat=22.543096&lng=114.057865&mode=async`
2. **Vite代理**: 将 `/v1` 转发到 `http://127.0.0.1:8088/v1/op/geo/reverse`
3. **后端路由**: 全局前缀 `v1` + 开放前缀 `op` + 控制器路径 `geo` + 方法路径 `reverse`
4. **最终路径**: `/v1/op/geo/reverse` ✅ 完全匹配

### 签名验证流程
1. **规范请求构建**: 使用正确的路径 `/v1/op/geo/reverse`
2. **签名生成**: 基于规范请求生成HMAC-SHA256签名
3. **请求发送**: 包含所有必要的认证头信息

## 测试建议

### 1. API测试工具测试
- [ ] 测试OCR文件上传识别
- [ ] 测试OCR Base64图片识别
- [ ] 测试申通面单OCR识别
- [ ] 测试地址提取服务
- [ ] 测试地址标准化服务
- [ ] 测试逆地理编码
- [ ] 测试正地理编码

### 2. 服务列表页面测试
- [ ] 验证服务分类筛选功能
- [ ] 验证服务卡片显示
- [ ] 验证"立即体验"按钮跳转
- [ ] 验证"查看文档"按钮跳转

### 3. 异步任务测试
- [ ] 测试异步模式请求
- [ ] 测试SSE事件流连接
- [ ] 测试任务状态查询

## 注意事项

1. **路径一致性**: 确保前端所有API请求都使用 `/v1/op/` 前缀
2. **签名验证**: Secret Key仅用于本地签名生成，不会随请求发送
3. **错误处理**: 401错误会触发登出确认弹窗
4. **缓存策略**: 任务状态和SSE连接需要正确清理

## 后续优化建议

1. **服务发现**: 考虑从后端动态获取服务列表和端点映射
2. **文档集成**: 完善API文档与前端页面的集成
3. **监控告警**: 添加API调用失败监控和告警机制
4. **性能优化**: 考虑添加请求缓存和重试机制 