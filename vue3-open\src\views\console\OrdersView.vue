<template>
  <div class="orders-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">订单管理</h1>
        <p class="page-subtitle">查看和管理您的所有订单</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建订单
        </el-button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <div class="stat-item">
        <div class="stat-value">{{ orderStore.stats.totalOrders }}</div>
        <div class="stat-label">总订单数</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ orderStore.stats.pendingOrders }}</div>
        <div class="stat-label">待支付</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ orderStore.stats.paidOrders }}</div>
        <div class="stat-label">已支付</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ orderStore.stats.completedOrders }}</div>
        <div class="stat-label">已完成</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">¥{{ (orderStore.stats.totalAmount / 100).toFixed(2) }}</div>
        <div class="stat-label">总金额</div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-select v-model="filters.status" placeholder="订单状态" clearable style="width: 120px;">
          <el-option label="全部" value="" />
          <el-option label="待支付" value="pending" />
          <el-option label="已支付" value="paid" />
          <el-option label="处理中" value="processing" />
          <el-option label="已完成" value="completed" />
          <el-option label="已取消" value="cancelled" />
          <el-option label="已退款" value="refunded" />
        </el-select>
        
        <el-select v-model="filters.type" placeholder="订单类型" clearable style="width: 120px;">
          <el-option label="全部" value="" />
          <el-option label="服务" value="service" />
          <el-option label="套餐" value="package" />
          <el-option label="充值" value="recharge" />
        </el-select>
        
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 240px;"
        />
      </div>
      
      <div class="filter-right">
        <el-input
          v-model="filters.keyword"
          placeholder="搜索订单号或描述"
          style="width: 200px;"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-button @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </div>
    </div>

    <!-- 订单列表 -->
    <div class="orders-section">
      <el-table 
        :data="orderStore.orders" 
        v-loading="orderStore.loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="orderNo" label="订单号" width="180" />
        
        <el-table-column label="订单类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)" size="small">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="订单状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="支付状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getPaymentStatusTagType(row.paymentStatus)" size="small">
              {{ getPaymentStatusLabel(row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="finalAmount" label="订单金额" width="120">
          <template #default="{ row }">
            ¥{{ (row.finalAmount / 100).toFixed(2) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewOrder(row)">
              查看
            </el-button>
            
            <el-button 
              v-if="row.status === 'pending' && row.paymentStatus === 'pending'"
              type="primary" 
              size="small" 
              @click="payOrder(row)"
            >
              支付
            </el-button>
            
            <el-button 
              v-if="row.status === 'pending'"
              type="warning" 
              size="small" 
              @click="cancelOrder(row)"
            >
              取消
            </el-button>
            
            <el-button 
              v-if="row.status === 'paid' && row.paymentStatus === 'paid'"
              type="success" 
              size="small" 
              @click="confirmOrder(row)"
            >
              确认
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="orderStore.pagination.page"
          v-model:page-size="orderStore.pagination.pageSize"
          :total="orderStore.pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建订单对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建订单"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="100px"
      >
        <el-form-item label="订单类型" prop="type">
          <el-select v-model="createForm.type" placeholder="请选择订单类型">
            <el-option label="服务" value="service" />
            <el-option label="套餐" value="package" />
            <el-option label="充值" value="recharge" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="支付方式" prop="paymentMethod">
          <el-select v-model="createForm.paymentMethod" placeholder="请选择支付方式">
            <el-option label="支付宝" value="alipay" />
            <el-option label="微信支付" value="wechat" />
            <el-option label="银行卡" value="bank_card" />
            <el-option label="余额" value="balance" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入订单描述"
          />
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="createForm.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleCreate" :loading="orderStore.loading">
          创建
        </el-button>
      </template>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="订单详情"
      width="800px"
    >
      <div v-if="selectedOrder" class="order-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ selectedOrder.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="订单类型">
            <el-tag :type="getTypeTagType(selectedOrder.type)" size="small">
              {{ getTypeLabel(selectedOrder.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusTagType(selectedOrder.status)" size="small">
              {{ getStatusLabel(selectedOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付状态">
            <el-tag :type="getPaymentStatusTagType(selectedOrder.paymentStatus)" size="small">
              {{ getPaymentStatusLabel(selectedOrder.paymentStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单金额">¥{{ (selectedOrder.finalAmount / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="支付方式">{{ getPaymentMethodLabel(selectedOrder.paymentMethod) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(selectedOrder.createdAt) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDate(selectedOrder.updatedAt) }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="order-items" v-if="selectedOrder.items.length > 0">
          <h4>订单项目</h4>
          <el-table :data="selectedOrder.items" border>
            <el-table-column prop="serviceName" label="服务/商品" />
            <el-table-column prop="quantity" label="数量" width="80" />
            <el-table-column prop="unitPrice" label="单价" width="100">
              <template #default="{ row }">
                ¥{{ (row.unitPrice / 100).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="totalPrice" label="小计" width="100">
              <template #default="{ row }">
                ¥{{ (row.totalPrice / 100).toFixed(2) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <div class="order-description" v-if="selectedOrder.description">
          <h4>订单描述</h4>
          <p>{{ selectedOrder.description }}</p>
        </div>
        
        <div class="order-remark" v-if="selectedOrder.remark">
          <h4>备注信息</h4>
          <p>{{ selectedOrder.remark }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { useOrderStore } from '@/stores/order'
import type { Order, CreateOrderForm } from '@/types/order'

const orderStore = useOrderStore()

// 响应式数据
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const selectedOrder = ref<Order | null>(null)
const dateRange = ref<[string, string] | null>(null)

// 筛选条件
const filters = reactive({
  status: '',
  type: '',
  keyword: ''
})

// 创建订单表单
const createForm = reactive<CreateOrderForm>({
  type: 'service',
  items: [],
  paymentMethod: 'alipay',
  description: '',
  remark: ''
})

const createFormRef = ref()
const createRules = {
  type: [{ required: true, message: '请选择订单类型', trigger: 'change' }],
  paymentMethod: [{ required: true, message: '请选择支付方式', trigger: 'change' }]
}

// 方法
const handleSearch = async () => {
  const params: any = { ...filters }
  
  if (dateRange.value) {
    params.startDate = dateRange.value[0]
    params.endDate = dateRange.value[1]
  }
  
  orderStore.pagination.page = 1
  await orderStore.fetchOrders(params)
}

const handleReset = async () => {
  filters.status = ''
  filters.type = ''
  filters.keyword = ''
  dateRange.value = null
  orderStore.pagination.page = 1
  await orderStore.fetchOrders()
}

const handleSizeChange = async (size: number) => {
  orderStore.pagination.pageSize = size
  await orderStore.fetchOrders()
}

const handleCurrentChange = async (page: number) => {
  orderStore.pagination.page = page
  await orderStore.fetchOrders()
}

const handleCreate = async () => {
  try {
    await createFormRef.value?.validate()
    await orderStore.createOrder(createForm)
    showCreateDialog.value = false
    resetCreateForm()
  } catch (error) {
    console.error('创建订单失败:', error)
  }
}

const resetCreateForm = () => {
  createForm.type = 'service'
  createForm.items = []
  createForm.paymentMethod = 'alipay'
  createForm.description = ''
  createForm.remark = ''
}

const viewOrder = (order: Order) => {
  selectedOrder.value = order
  showDetailDialog.value = true
}

const payOrder = async (order: Order) => {
  try {
    await ElMessageBox.confirm('确认支付此订单？', '确认支付', {
      type: 'warning'
    })
    
    await orderStore.payOrder(order.id, order.paymentMethod || 'alipay')
    await orderStore.fetchOrders()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('支付订单失败:', error)
    }
  }
}

const cancelOrder = async (order: Order) => {
  try {
    await ElMessageBox.confirm('确认取消此订单？', '确认取消', {
      type: 'warning'
    })
    
    await orderStore.cancelOrder(order.id, '用户主动取消')
    await orderStore.fetchOrders()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消订单失败:', error)
    }
  }
}

const confirmOrder = async (order: Order) => {
  try {
    await ElMessageBox.confirm('确认此订单已完成？', '确认完成', {
      type: 'success'
    })
    
    await orderStore.confirmOrder(order.id)
    await orderStore.fetchOrders()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认订单失败:', error)
    }
  }
}

// 辅助方法
const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    service: '服务',
    package: '套餐',
    recharge: '充值'
  }
  return labels[type] || type
}

const getTypeTagType = (type: string) => {
  const types: Record<string, string> = {
    service: 'primary',
    package: 'success',
    recharge: 'warning'
  }
  return types[type] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待支付',
    paid: '已支付',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消',
    refunded: '已退款'
  }
  return labels[status] || status
}

const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    paid: 'primary',
    processing: 'info',
    completed: 'success',
    cancelled: 'danger',
    refunded: 'info'
  }
  return types[status] || ''
}

const getPaymentStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待支付',
    paid: '已支付',
    failed: '支付失败',
    refunded: '已退款'
  }
  return labels[status] || status
}

const getPaymentStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    paid: 'success',
    failed: 'danger',
    refunded: 'info'
  }
  return types[status] || ''
}

const getPaymentMethodLabel = (method?: string) => {
  if (!method) return '-'
  const labels: Record<string, string> = {
    alipay: '支付宝',
    wechat: '微信支付',
    bank_card: '银行卡',
    balance: '余额'
  }
  return labels[method] || method
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 生命周期
onMounted(async () => {
  await orderStore.fetchOrders()
  await orderStore.fetchOrderStats()
})

// 监听筛选条件变化
watch([() => filters.status, () => filters.type], () => {
  handleSearch()
})
</script>

<style scoped>
.orders-page {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #1f2937;
}

.page-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-item {
  background: white;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  text-align: center;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.filter-section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.filter-left {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.orders-section {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.pagination-wrapper {
  padding: 16px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e5e7eb;
}

.order-detail {
  padding: 16px 0;
}

.order-items {
  margin-top: 24px;
}

.order-items h4,
.order-description h4,
.order-remark h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.order-description,
.order-remark {
  margin-top: 24px;
}

.order-description p,
.order-remark p {
  margin: 0;
  color: #6b7280;
  line-height: 1.6;
}
</style>