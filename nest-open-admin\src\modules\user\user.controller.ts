
import { Controller, Get, Post, Body, Put, Param, Query, ParseUUI<PERSON>ipe, ParseIntPipe, Delete, Request, UseInterceptors, ClassSerializerInterceptor } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiConsumes, ApiQuery, ApiBearerAuth, ApiProperty } from '@nestjs/swagger';
import { Roles } from '@/common/decorators/roles.decorator';
import { UserService } from './user.service';
import {
  CreateUserDto, UpdateUserDto, ChangeStatusDto, ResetPwdDto, UpdateProfileDto, UpdatePwdDto,EnterpriseVerifyDto,
} from './dto/index';

/**
 * 用户管理控制器
 * 管理开放平台的用户账户
 */
@ApiTags('用户管理')
@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @ApiOperation({
    summary: '创建用户',
  })
  @ApiBody({
    type: CreateUserDto,
    required: true,
  })
  @Post()
  @UseInterceptors(ClassSerializerInterceptor)
  async create(@Body() createUserDto: CreateUserDto):Promise<any> {
    return await this.userService.createUser(createUserDto);
  }

  @Post('certification')
  @ApiOperation({ summary: '个人实名认证' })
  async certification(@Body() certificationDto: any) {
    return await this.userService.certification(certificationDto);
  }

  @Post('enterpriseVerify')
  @ApiOperation({ summary: '企业认证' })
  async enterpriseVerify(@Body() enterpriseVerifyDto: EnterpriseVerifyDto) {
    return await this.userService.enterpriseVerify(enterpriseVerifyDto);
  }


  /**
   * 获取用户列表
   */
  @Get('list')
  @ApiOperation({ summary: '用户列表分页' })
  findList(@Query() query: any, @Request() req) {
    const user = req.user.user;
    return this.userService.findList(query, user);
  }

  @ApiOperation({
    summary: '用户信息',
  })
  @Get('/profile')
  @UseInterceptors(ClassSerializerInterceptor)
  profile(@Request() req) {
    const user = req.user;
    console.log('user::::', req.user)
    return this.userService.profile(user);
  }

  @ApiOperation({
    summary: '修改用户信息',
  })
  @Put('/profile')
  @UseInterceptors(ClassSerializerInterceptor)
  updateProfile(@Request() req, @Body() updateProfileDto: UpdateProfileDto) {
    const user = req.user;
    return this.userService.updateProfile(user, updateProfileDto);
  }

  @ApiOperation({
    summary: '修改密码',
  })
  @Put('/updatePwd')
  updatePwd(@Request() req, @Body() updatePwdDto: UpdatePwdDto) {
    const user = req.user;
    return this.userService.updateUserPassword(user, updatePwdDto);
  }

  @ApiOperation({
    summary: '用户详情byId',
  })
  @Get(':userId')
  @UseInterceptors(ClassSerializerInterceptor)
  findOne(@Param('userId') userId: string) {
    return this.userService.findOne(+userId);
  }
  
  @ApiOperation({
    summary: '启用/禁用用户',
  })
  @ApiBody({
    type: ChangeStatusDto,
    required: true,
  })
  @Roles('admin')
  @Put(':id/changeStatus')
  changeStatus(@Param('id') id: number, @Body() changeStatusDto: ChangeStatusDto) {
    return this.userService.changeStatus(id, changeStatusDto);
  }


  @ApiOperation({
    summary: '删除用户',
  })
  @Roles('admin')
  @Delete(':id')
  remove(@Param('id') ids: string) {
    const userIds = ids.split(',').map((id) => +id);
    return this.userService.remove(userIds);
  }


  @Post('/api-usage')
  @ApiOperation({ summary: '获取用户API使用情况' })
  async getUserApiUsage(
    @Body() body: {id: number,startDate?: string, endDate?: string}
  ): Promise<Record<string,any>> {
    return await this.userService.getApiUsageByParams(body);
  }

  @Get('/balance/:id')
  @ApiOperation({ summary: '获取用户余额' })
  async getUserBalance(@Param('id', ParseIntPipe) id: number): Promise<Record<string,any>> {
     return await this.userService.getBalanceById(id);
  }

  /**
   * 用户充值
   */
  @Post('/recharge/:id')
  @ApiOperation({ summary: '用户充值' })
  async rechargeUser(
    @Param('id', ParseIntPipe) id: number,
    @Body('balance') balance: number,
  ): Promise<Record<string,any>> {
    return await this.userService.rechargeById(id, balance);
  }

}