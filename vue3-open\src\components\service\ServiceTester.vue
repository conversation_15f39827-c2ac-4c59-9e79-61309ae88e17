<template>
  <div class="service-tester">
    <div class="tester-header">
      <h3>在线测试</h3>
      <p>您可以在这里直接测试API功能，无需编写代码</p>
    </div>
    
    <div class="tester-content">
      <!-- OCR 服务测试 -->
      <div v-if="isOCRService" class="ocr-tester">
        <div class="upload-section">
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            drag
            :auto-upload="false"
            :on-change="handleFileChange"
            :show-file-list="false"
            accept="image/*"
          >
            <div class="upload-content">
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                拖拽图片到此处或 <em>点击上传</em>
              </div>
              <div class="el-upload__tip">
                支持 JPG、PNG、BMP、TIFF 格式，文件大小不超过 10MB
              </div>
            </div>
          </el-upload>
          
          <!-- 图片预览 -->
          <div v-if="previewImage" class="image-preview">
            <img :src="previewImage" alt="预览图片" />
            <el-button 
              class="remove-btn" 
              type="danger" 
              size="small" 
              circle
              @click="removeImage"
            >
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </div>
        
        <!-- OCR 选项 -->
        <div class="options-section">
          <h4>识别选项</h4>
          <el-form :model="ocrOptions" label-width="120px">
            <el-form-item label="语言">
              <el-select v-model="ocrOptions.language" placeholder="选择语言">
                <el-option label="中文" value="zh-cn" />
                <el-option label="英文" value="en" />
                <el-option label="中英混合" value="zh-en" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="方向检测">
              <el-switch v-model="ocrOptions.detectDirection" />
            </el-form-item>
            
            <el-form-item label="返回位置信息">
              <el-switch v-model="ocrOptions.returnPosition" />
            </el-form-item>
          </el-form>
        </div>
      </div>
      
      <!-- NLP 服务测试 -->
      <div v-else-if="isNLPService" class="nlp-tester">
        <div class="text-input-section">
          <el-form :model="nlpOptions" label-width="120px">
            <el-form-item label="输入文本">
              <el-input
                v-model="nlpOptions.text"
                type="textarea"
                :rows="6"
                placeholder="请输入要分析的文本内容..."
                maxlength="5000"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="语言" v-if="serviceId.includes('sentiment')">
              <el-select v-model="nlpOptions.language" placeholder="选择语言">
                <el-option label="中文" value="zh" />
                <el-option label="英文" value="en" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="返回详细信息" v-if="serviceId.includes('keywords')">
              <el-switch v-model="nlpOptions.detailed" />
            </el-form-item>
          </el-form>
        </div>
      </div>
      
      <!-- AI 生成服务测试 -->
      <div v-else-if="isAIService" class="ai-tester">
        <div class="prompt-section">
          <el-form :model="aiOptions" label-width="120px">
            <el-form-item label="提示词">
              <el-input
                v-model="aiOptions.prompt"
                type="textarea"
                :rows="4"
                placeholder="请输入生成提示词..."
                maxlength="2000"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="生成长度">
              <el-slider
                v-model="aiOptions.maxLength"
                :min="50"
                :max="2000"
                :step="50"
                show-stops
                show-input
              />
            </el-form-item>
            
            <el-form-item label="创意度">
              <el-slider
                v-model="aiOptions.temperature"
                :min="0"
                :max="1"
                :step="0.1"
                show-stops
                show-input
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
      
      <!-- 测试按钮 -->
      <div class="test-actions">
        <el-button 
          type="primary" 
          size="large"
          :loading="testing"
          @click="runTest"
          :disabled="!canTest"
        >
          {{ testing ? '测试中...' : '开始测试' }}
        </el-button>
        
        <el-button 
          size="large"
          @click="clearResult"
          :disabled="!testResult"
        >
          清空结果
        </el-button>
      </div>
      
      <!-- 测试结果 -->
      <div v-if="testResult" class="result-section">
        <h4>测试结果</h4>
        <div class="result-content">
          <!-- OCR 结果 -->
          <div v-if="isOCRService" class="ocr-result">
            <div class="result-stats">
              <el-statistic title="识别文字数" :value="testResult.textCount || 0" />
              <el-statistic title="置信度" :value="testResult.confidence || 0" suffix="%" />
              <el-statistic title="处理时间" :value="testResult.processTime || 0" suffix="ms" />
            </div>
            
            <div class="recognized-text">
              <h5>识别文本</h5>
              <div class="text-content">
                {{ testResult.text || '未识别到文字' }}
              </div>
            </div>
            
            <div v-if="testResult.positions" class="position-info">
              <h5>位置信息</h5>
              <el-table :data="testResult.positions" size="small">
                <el-table-column prop="text" label="文字" />
                <el-table-column prop="x" label="X坐标" />
                <el-table-column prop="y" label="Y坐标" />
                <el-table-column prop="width" label="宽度" />
                <el-table-column prop="height" label="高度" />
              </el-table>
            </div>
          </div>
          
          <!-- NLP 结果 -->
          <div v-else-if="isNLPService" class="nlp-result">
            <div v-if="serviceId.includes('sentiment')" class="sentiment-result">
              <div class="result-stats">
                <el-statistic title="情感倾向" :value="testResult.sentiment || '中性'" />
                <el-statistic title="置信度" :value="testResult.confidence || 0" suffix="%" />
              </div>
              
              <div class="sentiment-chart">
                <h5>情感分析详情</h5>
                <div class="sentiment-bars">
                  <div class="sentiment-bar">
                    <span class="label">正面</span>
                    <el-progress :percentage="testResult.positive || 0" color="#67c23a" />
                  </div>
                  <div class="sentiment-bar">
                    <span class="label">中性</span>
                    <el-progress :percentage="testResult.neutral || 0" color="#909399" />
                  </div>
                  <div class="sentiment-bar">
                    <span class="label">负面</span>
                    <el-progress :percentage="testResult.negative || 0" color="#f56c6c" />
                  </div>
                </div>
              </div>
            </div>
            
            <div v-else-if="serviceId.includes('keywords')" class="keywords-result">
              <h5>关键词提取结果</h5>
              <div class="keywords-list">
                <el-tag 
                  v-for="keyword in testResult.keywords" 
                  :key="keyword.word"
                  class="keyword-tag"
                  :type="getKeywordType(keyword.score)"
                >
                  {{ keyword.word }} ({{ keyword.score }})
                </el-tag>
              </div>
            </div>
          </div>
          
          <!-- AI 生成结果 -->
          <div v-else-if="isAIService" class="ai-result">
            <div class="result-stats">
              <el-statistic title="生成字数" :value="testResult.length || 0" />
              <el-statistic title="处理时间" :value="testResult.processTime || 0" suffix="ms" />
            </div>
            
            <div class="generated-content">
              <h5>生成内容</h5>
              <div class="content-text">
                {{ testResult.content || '生成失败' }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 原始响应 -->
        <div class="raw-response">
          <el-collapse>
            <el-collapse-item title="查看原始响应" name="raw">
              <pre><code>{{ JSON.stringify(testResult, null, 2) }}</code></pre>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      
      <!-- 错误信息 -->
      <div v-if="testError" class="error-section">
        <el-alert
          title="测试失败"
          type="error"
          :description="testError"
          show-icon
          :closable="false"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Close } from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'
import { useServiceStore } from '@/stores/service'

interface Props {
  serviceId: string
}

const props = defineProps<Props>()
const serviceStore = useServiceStore()

const testing = ref(false)
const testResult = ref<any>(null)
const testError = ref('')
const previewImage = ref('')
const uploadedFile = ref<File | null>(null)

// 服务类型判断
const isOCRService = computed(() => props.serviceId.includes('ocr'))
const isNLPService = computed(() => props.serviceId.includes('nlp'))
const isAIService = computed(() => props.serviceId.includes('ai'))

// OCR 选项
const ocrOptions = reactive({
  language: 'zh-cn',
  detectDirection: true,
  returnPosition: false
})

// NLP 选项
const nlpOptions = reactive({
  text: '',
  language: 'zh',
  detailed: false
})

// AI 选项
const aiOptions = reactive({
  prompt: '',
  maxLength: 500,
  temperature: 0.7
})

// 是否可以测试
const canTest = computed(() => {
  if (isOCRService.value) {
    return !!uploadedFile.value
  } else if (isNLPService.value) {
    return nlpOptions.text.trim().length > 0
  } else if (isAIService.value) {
    return aiOptions.prompt.trim().length > 0
  }
  return false
})

// 处理文件上传
const handleFileChange = (file: UploadFile) => {
  if (!file.raw) return
  
  // 检查文件大小
  if (file.raw.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过 10MB')
    return
  }
  
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/png', 'image/bmp', 'image/tiff']
  if (!allowedTypes.includes(file.raw.type)) {
    ElMessage.error('不支持的文件格式')
    return
  }
  
  uploadedFile.value = file.raw
  
  // 创建预览
  const reader = new FileReader()
  reader.onload = (e) => {
    previewImage.value = e.target?.result as string
  }
  reader.readAsDataURL(file.raw)
}

// 移除图片
const removeImage = () => {
  uploadedFile.value = null
  previewImage.value = ''
}

// 运行测试
const runTest = async () => {
  try {
    testing.value = true
    testError.value = ''
    testResult.value = null
    
    if (isOCRService.value) {
      await runOCRTest()
    } else if (isNLPService.value) {
      await runNLPTest()
    } else if (isAIService.value) {
      await runAITest()
    }
  } catch (error: any) {
    testError.value = error.message || '测试失败'
    ElMessage.error('测试失败')
  } finally {
    testing.value = false
  }
}

// OCR 测试
const runOCRTest = async () => {
  if (!uploadedFile.value) return
  
  // 模拟 OCR 测试结果
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  testResult.value = {
    text: '这是识别出的文字内容示例。OCR技术能够准确识别图片中的文字信息。',
    textCount: 28,
    confidence: 98.5,
    processTime: 1850,
    positions: ocrOptions.returnPosition ? [
      { text: '这是识别出的', x: 10, y: 20, width: 120, height: 25 },
      { text: '文字内容示例', x: 140, y: 20, width: 110, height: 25 },
      { text: 'OCR技术能够', x: 10, y: 50, width: 100, height: 25 },
      { text: '准确识别图片', x: 120, y: 50, width: 100, height: 25 }
    ] : null
  }
}

// NLP 测试
const runNLPTest = async () => {
  await new Promise(resolve => setTimeout(resolve, 1500))
  
  if (props.serviceId.includes('sentiment')) {
    testResult.value = {
      sentiment: '正面',
      confidence: 85.2,
      positive: 85.2,
      neutral: 10.5,
      negative: 4.3
    }
  } else if (props.serviceId.includes('keywords')) {
    testResult.value = {
      keywords: [
        { word: '人工智能', score: 0.95 },
        { word: '机器学习', score: 0.88 },
        { word: '深度学习', score: 0.82 },
        { word: '神经网络', score: 0.75 },
        { word: '算法', score: 0.68 }
      ]
    }
  }
}

// AI 生成测试
const runAITest = async () => {
  await new Promise(resolve => setTimeout(resolve, 3000))
  
  testResult.value = {
    content: '这是AI生成的示例内容。基于您提供的提示词，AI模型生成了相关的文本内容。生成的内容具有良好的逻辑性和可读性，能够满足大多数应用场景的需求。',
    length: 68,
    processTime: 2850
  }
}

// 清空结果
const clearResult = () => {
  testResult.value = null
  testError.value = ''
}

// 获取关键词标签类型
const getKeywordType = (score: number) => {
  if (score >= 0.8) return 'danger'
  if (score >= 0.6) return 'warning'
  return 'info'
}
</script>

<style scoped>
.service-tester {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 24px;
}

.tester-header {
  margin-bottom: 24px;
}

.tester-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.tester-header p {
  color: #606266;
  margin: 0;
}

.tester-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.upload-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.upload-demo {
  width: 100%;
}

.upload-content {
  padding: 40px 20px;
  text-align: center;
}

.image-preview {
  position: relative;
  max-width: 400px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #dcdfe6;
}

.image-preview img {
  width: 100%;
  height: auto;
  display: block;
}

.remove-btn {
  position: absolute;
  top: 8px;
  right: 8px;
}

.options-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.options-section h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
}

.text-input-section,
.prompt-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.test-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.result-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.result-section h4,
.result-section h5 {
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
}

.result-section h5 {
  font-size: 1rem;
  margin: 20px 0 12px 0;
}

.result-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.text-content,
.content-text {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  line-height: 1.6;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-word;
}

.sentiment-bars {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sentiment-bar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sentiment-bar .label {
  width: 60px;
  font-size: 14px;
  color: #606266;
}

.sentiment-bar .el-progress {
  flex: 1;
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.keyword-tag {
  margin: 0;
}

.raw-response {
  margin-top: 20px;
}

.raw-response pre {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  margin: 0;
}

.error-section {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .test-actions {
    flex-direction: column;
  }
  
  .test-actions .el-button {
    width: 100%;
  }
  
  .result-stats {
    grid-template-columns: 1fr;
  }
}
</style>