<template>
  <div class="not-found-page">
    <div class="not-found-container">
      <!-- 404图标和动画 -->
      <div class="error-illustration">
        <div class="error-code">
          <span class="digit">4</span>
          <div class="zero-container">
            <div class="zero">
              <div class="zero-inner"></div>
            </div>
          </div>
          <span class="digit">4</span>
        </div>
        
        <!-- 装饰元素 -->
        <div class="floating-elements">
          <div class="element element-1"></div>
          <div class="element element-2"></div>
          <div class="element element-3"></div>
          <div class="element element-4"></div>
        </div>
      </div>
      
      <!-- 错误信息 -->
      <div class="error-content">
        <h1 class="error-title">页面未找到</h1>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移动。
          <br>
          请检查URL是否正确，或返回首页继续浏览。
        </p>
        
        <!-- 建议操作 -->
        <div class="error-suggestions">
          <h3>您可以尝试：</h3>
          <ul class="suggestions-list">
            <li>
              <el-icon><House /></el-icon>
              <span>返回首页重新开始</span>
            </li>
            <li>
              <el-icon><Search /></el-icon>
              <span>使用搜索功能查找内容</span>
            </li>
            <li>
              <el-icon><Document /></el-icon>
              <span>查看API文档了解更多</span>
            </li>
            <li>
              <el-icon><Service /></el-icon>
              <span>联系客服获取帮助</span>
            </li>
          </ul>
        </div>
        
        <!-- 操作按钮 -->
        <div class="error-actions">
          <el-button 
            type="primary" 
            size="large"
            @click="goHome"
          >
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          
          <el-button 
            size="large"
            @click="goBack"
          >
            <el-icon><ArrowLeft /></el-icon>
            返回上页
          </el-button>
          
          <el-button 
            size="large"
            @click="goToDocs"
          >
            <el-icon><Document /></el-icon>
            查看文档
          </el-button>
        </div>
      </div>
      
      <!-- 快速链接 -->
      <div class="quick-links">
        <h3>快速链接</h3>
        <div class="links-grid">
          <router-link 
            to="/" 
            class="quick-link"
          >
            <div class="link-icon">
              <el-icon><House /></el-icon>
            </div>
            <div class="link-content">
              <div class="link-title">首页</div>
              <div class="link-desc">返回网站首页</div>
            </div>
          </router-link>
          
          <router-link 
            to="/services" 
            class="quick-link"
          >
            <div class="link-icon">
              <el-icon><Grid /></el-icon>
            </div>
            <div class="link-content">
              <div class="link-title">服务列表</div>
              <div class="link-desc">浏览所有AI服务</div>
            </div>
          </router-link>
          
          <router-link 
            to="/docs" 
            class="quick-link"
          >
            <div class="link-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="link-content">
              <div class="link-title">API文档</div>
              <div class="link-desc">查看开发文档</div>
            </div>
          </router-link>
          
          <router-link 
            to="/playground" 
            class="quick-link"
          >
            <div class="link-icon">
              <el-icon><Operation /></el-icon>
            </div>
            <div class="link-content">
              <div class="link-title">API测试</div>
              <div class="link-desc">在线测试API</div>
            </div>
          </router-link>
        </div>
      </div>
      
      <!-- 联系支持 -->
      <div class="support-section">
        <div class="support-card">
          <div class="support-icon">
            <el-icon><Service /></el-icon>
          </div>
          <div class="support-content">
            <h4>需要帮助？</h4>
            <p>如果您认为这是一个错误，请联系我们的技术支持团队。</p>
            <div class="support-actions">
              <el-button 
                type="primary" 
                size="small"
                @click="contactSupport"
              >
                联系支持
              </el-button>
              <el-button 
                size="small"
                @click="reportIssue"
              >
                报告问题
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  House,
  ArrowLeft,
  Document,
  Search,
  Service,
  Grid,
  Operation
} from '@element-plus/icons-vue'

const router = useRouter()

// 返回首页
const goHome = () => {
  router.push('/')
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

// 前往文档
const goToDocs = () => {
  router.push('/docs')
}

// 联系支持
const contactSupport = () => {
  // TODO: 实现联系支持功能
  ElMessage.info('正在跳转到支持页面...')
}

// 报告问题
const reportIssue = () => {
  // TODO: 实现问题报告功能
  ElMessage.info('正在打开问题报告表单...')
}
</script>

<style scoped>
.not-found-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.not-found-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
  pointer-events: none;
}

.not-found-container {
  max-width: 800px;
  width: 100%;
  text-align: center;
  position: relative;
  z-index: 1;
}

/* 404动画效果 */
.error-illustration {
  margin-bottom: 48px;
  position: relative;
}

.error-code {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 32px;
}

.digit {
  font-size: 8rem;
  font-weight: 800;
  color: white;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  animation: bounce 2s infinite;
}

.digit:nth-child(3) {
  animation-delay: 0.2s;
}

.zero-container {
  position: relative;
  width: 120px;
  height: 120px;
}

.zero {
  width: 100%;
  height: 100%;
  border: 8px solid white;
  border-radius: 50%;
  position: relative;
  animation: rotate 3s linear infinite;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.zero-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  background: white;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* 浮动装饰元素 */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.element {
  position: absolute;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.element-1 {
  width: 20px;
  height: 20px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.element-2 {
  width: 30px;
  height: 30px;
  top: 60%;
  right: 15%;
  animation-delay: 1s;
}

.element-3 {
  width: 15px;
  height: 15px;
  top: 30%;
  right: 20%;
  animation-delay: 2s;
}

.element-4 {
  width: 25px;
  height: 25px;
  bottom: 20%;
  left: 20%;
  animation-delay: 3s;
}

/* 错误内容 */
.error-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 48px;
  margin-bottom: 32px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.error-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #303133;
  margin: 0 0 16px 0;
}

.error-description {
  font-size: 1.1rem;
  color: #606266;
  line-height: 1.6;
  margin: 0 0 32px 0;
}

.error-suggestions {
  text-align: left;
  margin-bottom: 32px;
}

.error-suggestions h3 {
  font-size: 1.2rem;
  color: #303133;
  margin: 0 0 16px 0;
}

.suggestions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.suggestions-list li {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  color: #606266;
}

.suggestions-list li .el-icon {
  color: #409eff;
  font-size: 16px;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 快速链接 */
.quick-links {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.quick-links h3 {
  font-size: 1.5rem;
  color: #303133;
  margin: 0 0 24px 0;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 16px;
}

.quick-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.link-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409eff;
  font-size: 18px;
}

.link-content {
  flex: 1;
  text-align: left;
}

.link-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.link-desc {
  font-size: 12px;
  color: #909399;
}

/* 支持部分 */
.support-section {
  display: flex;
  justify-content: center;
}

.support-card {
  display: flex;
  align-items: center;
  gap: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  max-width: 400px;
}

.support-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409eff;
  font-size: 20px;
  flex-shrink: 0;
}

.support-content {
  flex: 1;
  text-align: left;
}

.support-content h4 {
  font-size: 1.1rem;
  color: #303133;
  margin: 0 0 8px 0;
}

.support-content p {
  color: #606266;
  margin: 0 0 16px 0;
  font-size: 14px;
}

.support-actions {
  display: flex;
  gap: 8px;
}

/* 动画 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.7;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-code {
    gap: 10px;
  }
  
  .digit {
    font-size: 4rem;
  }
  
  .zero-container {
    width: 80px;
    height: 80px;
  }
  
  .error-content {
    padding: 32px 24px;
  }
  
  .error-title {
    font-size: 2rem;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-actions .el-button {
    width: 100%;
    max-width: 200px;
  }
  
  .links-grid {
    grid-template-columns: 1fr;
  }
  
  .support-card {
    flex-direction: column;
    text-align: center;
  }
  
  .support-content {
    text-align: center;
  }
  
  .support-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .not-found-page {
    padding: 16px;
  }
  
  .digit {
    font-size: 3rem;
  }
  
  .zero-container {
    width: 60px;
    height: 60px;
  }
  
  .error-content {
    padding: 24px 16px;
  }
  
  .quick-links {
    padding: 24px 16px;
  }
}
</style>