import { HttpException, HttpStatus } from '@nestjs/common';
import { ErrorCodeMap, ErrorCodeMapType } from '../constant/error-code.contants';

/**
 * Api业务异常均抛出该异常
 * 修复：使用适当的HTTP状态码，而不是统一返回200
 */
export class ApiException extends HttpException {
  /**
   * 业务类型错误代码，非Http code
   */
  public readonly errorCode: ErrorCodeMapType;
  public readonly details?: any;

  constructor(errorCode: ErrorCodeMapType, httpStatus?: HttpStatus, details?: any) {
    // 根据错误类型映射到适当的HTTP状态码
    const status = httpStatus || ApiException.mapErrorCodeToHttpStatus(errorCode);
    super(ErrorCodeMap[errorCode], status);
    this.errorCode = errorCode;
    this.details = details;
  }

  getErrCode(): ErrorCodeMapType {
    return this.errorCode;
  }

  /**
   * 将业务错误码映射到HTTP状态码
   */
  private static mapErrorCodeToHttpStatus(errorCode: ErrorCodeMapType): HttpStatus {
    // 根据错误码前缀或具体错误码映射到HTTP状态码
    const errorCodeStr = errorCode.toString();
    
    // 认证相关错误 -> 401
    if (errorCodeStr.includes('AUTH') || errorCodeStr.includes('LOGIN') || errorCodeStr.includes('TOKEN')) {
      return HttpStatus.UNAUTHORIZED;
    }
    
    // 权限相关错误 -> 403
    if (errorCodeStr.includes('PERMISSION') || errorCodeStr.includes('FORBIDDEN') || errorCodeStr.includes('ACCESS')) {
      return HttpStatus.FORBIDDEN;
    }
    
    // 资源不存在 -> 404
    if (errorCodeStr.includes('NOT_FOUND') || errorCodeStr.includes('NOT_EXIST')) {
      return HttpStatus.NOT_FOUND;
    }
    
    // 参数错误 -> 400
    if (errorCodeStr.includes('PARAM') || errorCodeStr.includes('INVALID') || errorCodeStr.includes('VALIDATION')) {
      return HttpStatus.BAD_REQUEST;
    }
    
    // 冲突错误 -> 409
    if (errorCodeStr.includes('CONFLICT') || errorCodeStr.includes('DUPLICATE') || errorCodeStr.includes('EXISTS')) {
      return HttpStatus.CONFLICT;
    }
    
    // 限流错误 -> 429
    if (errorCodeStr.includes('RATE_LIMIT') || errorCodeStr.includes('TOO_MANY')) {
      return HttpStatus.TOO_MANY_REQUESTS;
    }
    
    // 默认为400 Bad Request
    return HttpStatus.BAD_REQUEST;
  }
}
