import { PaymentMethod } from '../../order/enums/order.enum';

/**
 * 支付请求参数
 */
export interface PaymentRequest {
  /** 支付单号 */
  paymentNo: string;
  /** 订单号 */
  orderNo: string;
  /** 支付金额 */
  amount: number;
  /** 商品名称 */
  subject: string;
  /** 商品描述 */
  body?: string;
  /** 回调地址 */
  notifyUrl: string;
  /** 返回地址 */
  returnUrl?: string;
  /** 用户ID */
  userId: number;
  /** 额外参数 */
  extraParams?: Record<string, any>;
}

/**
 * 支付响应结果
 */
export interface PaymentResult {
  /** 是否成功 */
  success: boolean;
  /** 支付链接 */
  paymentUrl?: string;
  /** 二维码内容 */
  qrCode?: string;
  /** 支付参数 */
  paymentParams?: Record<string, any>;
  /** 第三方支付单号 */
  thirdPartyNo?: string;
  /** 错误信息 */
  errorMessage?: string;
  /** 过期时间 */
  expiresAt?: Date;
}

/**
 * 支付回调验证结果
 */
export interface CallbackVerifyResult {
  /** 验证是否成功 */
  success: boolean;
  /** 支付单号 */
  paymentNo?: string;
  /** 第三方支付单号 */
  thirdPartyNo?: string;
  /** 支付状态 */
  status?: 'success' | 'failed' | 'pending';
  /** 支付金额 */
  amount?: number;
  /** 支付时间 */
  paidAt?: Date;
  /** 原始回调数据 */
  rawData?: Record<string, any>;
  /** 错误信息 */
  errorMessage?: string;
}

/**
 * 支付查询结果
 */
export interface PaymentQueryResult {
  /** 查询是否成功 */
  success: boolean;
  /** 支付状态 */
  status?: 'success' | 'failed' | 'pending' | 'closed';
  /** 第三方支付单号 */
  thirdPartyNo?: string;
  /** 支付金额 */
  amount?: number;
  /** 支付时间 */
  paidAt?: Date;
  /** 错误信息 */
  errorMessage?: string;
}

/**
 * 退款请求参数
 */
export interface RefundRequest {
  /** 原支付单号 */
  paymentNo: string;
  /** 第三方支付单号 */
  thirdPartyNo: string;
  /** 退款单号 */
  refundNo: string;
  /** 退款金额 */
  refundAmount: number;
  /** 原支付金额 */
  totalAmount: number;
  /** 退款原因 */
  refundReason?: string;
}

/**
 * 退款响应结果
 */
export interface RefundResult {
  /** 是否成功 */
  success: boolean;
  /** 退款单号 */
  refundNo?: string;
  /** 第三方退款单号 */
  thirdPartyRefundNo?: string;
  /** 退款金额 */
  refundAmount?: number;
  /** 错误信息 */
  errorMessage?: string;
}

/**
 * 支付提供商接口
 */
export interface IPaymentProvider {
  /**
   * 获取支付方式
   */
  getPaymentMethod(): PaymentMethod;

  /**
   * 创建支付
   */
  createPayment(request: PaymentRequest): Promise<PaymentResult>;

  /**
   * 验证支付回调
   */
  verifyCallback(callbackData: Record<string, any>): Promise<CallbackVerifyResult>;

  /**
   * 查询支付状态
   */
  queryPayment(paymentNo: string, thirdPartyNo?: string): Promise<PaymentQueryResult>;

  /**
   * 申请退款
   */
  refund(request: RefundRequest): Promise<RefundResult>;

  /**
   * 查询退款状态
   */
  queryRefund(refundNo: string, thirdPartyRefundNo?: string): Promise<RefundResult>;
}
