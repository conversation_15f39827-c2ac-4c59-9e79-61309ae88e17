import { Injectable, Logger } from '@nestjs/common';
import { LayeredCacheService } from '../../../shared/layered-cache.service';
import { ApiKeyDto } from '../dto/api-key.dto';
import { ApiKeyEntity } from '../entities/api-key.entity';

/**
 * API密钥缓存服务
 * 负责API密钥的缓存管理
 */
@Injectable()
export class ApiKeyCacheService {
  private readonly logger = new Logger(ApiKeyCacheService.name);
  
  // 缓存键前缀
  private readonly API_KEY_ID_PREFIX = 'api-key:id:';
  private readonly API_KEY_VALUE_PREFIX = 'api-key:value:';
  private readonly API_KEY_USER_PREFIX = 'api-key:user:';
  private readonly API_KEY_SECRET_PREFIX = 'api-key:secret:';
  
  // 缓存过期时间（24小时）
  private readonly DEFAULT_TTL = 86400;
  
  constructor(private readonly cacheService: LayeredCacheService) {}
  
  /**
   * 缓存API密钥
   * @param apiKey API密钥实体
   */
  async cacheApiKey(apiKey: ApiKeyEntity): Promise<void> {
    try {
      const cacheData = this.prepareCacheData(apiKey);
      
      // 按ID缓存
      await this.cacheService.set(
        `${this.API_KEY_ID_PREFIX}${apiKey.id}`, 
        cacheData, 
        { ttl: this.DEFAULT_TTL }
      );
      
      // 按密钥值缓存
      await this.cacheService.set(
        `${this.API_KEY_VALUE_PREFIX}${apiKey.key}`, 
        cacheData, 
        { ttl: this.DEFAULT_TTL }
      );
      
      // 缓存密钥秘钥（单独存储，更短的TTL）
      if (apiKey.encryptedSecretKey) {
        await this.cacheService.set(
          `${this.API_KEY_SECRET_PREFIX}${apiKey.id}`, 
          apiKey.encryptedSecretKey, 
          { ttl: this.DEFAULT_TTL / 2 }
        );
      }
      
      this.logger.debug(`API密钥已缓存: ID=${apiKey.id}, Key=${apiKey.key}`);
    } catch (error) {
      this.logger.error(`缓存API密钥失败: ${error.message}`, error.stack);
    }
  }
  
  /**
   * 从缓存获取API密钥（按ID）
   * @param id API密钥ID
   */
  async getApiKeyById(id: number): Promise<ApiKeyDto | null> {
    try {
      return await this.cacheService.get<ApiKeyDto>(`${this.API_KEY_ID_PREFIX}${id}`);
    } catch (error) {
      this.logger.error(`从缓存获取API密钥失败: ${error.message}`, error.stack);
      return null;
    }
  }
  
  /**
   * 从缓存获取API密钥（按密钥值）
   * @param key API密钥值
   */
  async getApiKeyByValue(key: string): Promise<ApiKeyDto | null> {
    try {
      return await this.cacheService.get<ApiKeyDto>(`${this.API_KEY_VALUE_PREFIX}${key}`);
    } catch (error) {
      this.logger.error(`从缓存获取API密钥失败: ${error.message}`, error.stack);
      return null;
    }
  }
  
  /**
   * 从缓存获取API密钥秘钥
   * @param id API密钥ID
   */
  async getApiKeySecret(id: number): Promise<string | null> {
    try {
      return await this.cacheService.get<string>(`${this.API_KEY_SECRET_PREFIX}${id}`);
    } catch (error) {
      this.logger.error(`从缓存获取API密钥秘钥失败: ${error.message}`, error.stack);
      return null;
    }
  }
  
  /**
   * 从缓存删除API密钥
   * @param id API密钥ID
   * @param key API密钥值
   */
  async removeApiKeyFromCache(id: number, key: string): Promise<void> {
    try {
      await this.cacheService.delete(`${this.API_KEY_ID_PREFIX}${id}`);
      await this.cacheService.delete(`${this.API_KEY_VALUE_PREFIX}${key}`);
      await this.cacheService.delete(`${this.API_KEY_SECRET_PREFIX}${id}`);
      
      this.logger.debug(`API密钥已从缓存移除: ID=${id}, Key=${key}`);
    } catch (error) {
      this.logger.error(`从缓存删除API密钥失败: ${error.message}`, error.stack);
    }
  }
  
  /**
   * 更新API密钥最后使用时间
   * @param id API密钥ID
   * @param ip 客户端IP
   */
  async updateApiKeyLastUsed(id: number, ip: string): Promise<void> {
    try {
      const apiKey = await this.getApiKeyById(id);
      if (apiKey) {
        apiKey.lastUsedAt = new Date();
        await this.cacheService.set(
          `${this.API_KEY_ID_PREFIX}${id}`, 
          apiKey, 
          { ttl: this.DEFAULT_TTL }
        );
        
        this.logger.debug(`API密钥最后使用时间已更新: ID=${id}, IP=${ip}`);
      }
    } catch (error) {
      this.logger.error(`更新API密钥最后使用时间失败: ${error.message}`, error.stack);
    }
  }
  
  /**
   * 准备缓存数据
   * @param apiKey API密钥实体
   */
  private prepareCacheData(apiKey: ApiKeyEntity): Omit<ApiKeyDto, 'secretKey'> {
    return {
      id: apiKey.id,
      key: apiKey.key,
      name: apiKey.name,
      status: apiKey.status,
      scopes: apiKey.scopes || [],
      createdAt: apiKey.createdAt,
      updatedAt: apiKey.updatedAt,
      expiresAt: apiKey.expiresAt,
      lastUsedAt: apiKey.lastUsedAt,
      isViewed: apiKey.isViewed,
      allowedIps: apiKey.allowedIps,
      serviceId: apiKey.serviceId,
      keyType: apiKey.keyType,
      userId: apiKey.userId
    };
  }
} 