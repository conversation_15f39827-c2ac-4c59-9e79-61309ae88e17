/**
 * 地址提取处理器
 * 处理地址提取任务的具体逻辑
 */

import { Process, Processor } from '@nestjs/bull';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Job } from 'bull';
import { ModuleRef } from '@nestjs/core';
import { QUEUE_NAMES } from '../queue.constants';
import { AddressExtractionTaskData, TaskResult } from '../interfaces/queue.interface';
import { AddressExecutorService } from '../../address/services/address-executor.service';
import { ApiUsageTrackerService } from '../../call-record/services/api-usage-tracker.service';

/**
 * 地址提取处理器
 * 处理地址提取任务的具体逻辑
 */
@Injectable()
@Processor(QUEUE_NAMES.EXTRACT_ADDRESS)
export class AddressExtractionProcessor implements OnModuleInit {
  private readonly logger = new Logger(AddressExtractionProcessor.name);
  private addressExecutorService: AddressExecutorService | null = null;
  private apiUsageTrackerService: ApiUsageTrackerService | null = null;

  constructor(private readonly moduleRef: ModuleRef) {}

  /**
   * 模块初始化时，动态获取所需服务
   */
  async onModuleInit() {
    try {
      // 尝试获取地址执行器服务
      try {
        this.addressExecutorService = await this.moduleRef.resolve(AddressExecutorService, undefined, { strict: false });
        this.logger.log('成功获取地址执行器服务');
      } catch (error) {
        this.logger.error(`获取地址执行器服务失败: ${error.message}`);
        this.addressExecutorService = null;
      }
      
      // 尝试获取API使用跟踪服务
      try {
        this.apiUsageTrackerService = await this.moduleRef.resolve(ApiUsageTrackerService, undefined, { strict: false });
        this.logger.log('成功获取API使用跟踪服务');
      } catch (error) {
        this.logger.warn(`获取API使用跟踪服务失败: ${error.message}`);
        this.apiUsageTrackerService = null;
      }
    } catch (error) {
      this.logger.error(`初始化服务失败: ${error.message}`);
    }
  }

  /**
   * 处理地址提取任务
   */
  @Process('address_extraction')
  async process(job: Job<AddressExtractionTaskData>): Promise<TaskResult> {
    try {
      this.logger.log(`开始处理地址提取任务: ${job.id}`);
      await this.updateProgress(job, 0, '任务开始处理');
      
      // 验证任务数据
      this.validateJobData(job.data);
      
      // 更新进度
      await this.updateProgress(job, 20, '正在准备地址提取');
      
      // 获取地址执行器服务（如果尚未获取）
      if (!this.addressExecutorService) {
        this.addressExecutorService = await this.getServiceSafely<AddressExecutorService>(
          AddressExecutorService,
          () => this.createMockAddressExecutorService()
        );
      }
      
      // 更新进度
      await this.updateProgress(job, 40, '正在提取地址信息');
      
      // 处理地址提取
      const result = await this.addressExecutorService.extractFromText(
        job.data.text,
        job.data.mode || 'single',
        {
          userId: job.data.userId,
          apiKey: job.data.apiKeyId,
          serviceCode: job.data.serviceId?.toString(),
        }
      );
      
      // 更新进度
      await this.updateProgress(job, 80, '正在更新API使用记录');
      
      // 记录API使用情况
      if (job.data.userId && job.data.apiKeyId && job.data.serviceId) {
        try {
          // 获取API使用跟踪服务（如果尚未获取）
          if (!this.apiUsageTrackerService) {
            this.apiUsageTrackerService = await this.getServiceSafely<ApiUsageTrackerService>(
              ApiUsageTrackerService,
              () => this.createMockApiUsageTrackerService()
            );
          }
          
          if (this.apiUsageTrackerService) {
            await this.apiUsageTrackerService.trackApiCall({
              userId: job.data.userId,
              apiKeyId: job.data.apiKeyId,
              serviceId: job.data.serviceId,
              endpoint: '地址提取',
              responseTime: result.processingTime || 0,
              status: result.success ? 'completed' : 'failed',
              requestId: job.data.requestId || `req_${Date.now()}`,
              jobId: job.id.toString(),
              error: result.error,
            });
          }
        } catch (error) {
          this.logger.error(`记录API使用失败: ${error.message}`, error.stack);
          // 不中断主流程，继续返回结果
        }
      }
      
      await this.updateProgress(job, 100, '任务处理完成');
      this.logger.log(`地址提取任务处理成功: ${job.id}`);
      // 确保返回的地址数据中包含detail_address字段
      const enhancedAddresses = result.addresses.map((addr:any) => {
        // 如果有detailAddress但没有detail_address，添加detail_address
        if (addr.detailAddress && !addr.detail_address) {
          return { ...addr, detail_address: addr.detailAddress };
        }
        // 如果有detail_address但没有detailAddress，添加detailAddress
        else if (!addr.detailAddress && addr.detail_address) {
          return { ...addr, detailAddress: addr.detail_address };
        }
        return addr;
      });
      
      return {
        success: result.success,
        data: enhancedAddresses,
        error: result.error,
        duration: result.processingTime,
      };
    } catch (error) {
      this.logger.error(`地址提取任务处理失败: ${job.id}, 错误: ${error.message}`, error.stack);
      
      try {
        await this.updateProgress(job, 100, `处理失败: ${error.message}`);
      } catch (err) {
        this.logger.error(`更新任务失败状态时出错: ${err.message}`);
      }
      
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 验证任务数据
   */
  private validateJobData(data: AddressExtractionTaskData): void {
    if (!data) {
      throw new Error('任务数据不能为空');
    }
    
    if (!data.text) {
      throw new Error('文本内容不能为空');
    }

    if (data.text.length > 10000) {
      throw new Error('文本内容过长，超过10000字符限制');
    }
  }

  /**
   * 安全地获取服务实例
   */
  private async getServiceSafely<T>(
    serviceToken: any,
    fallbackFactory?: () => T
  ): Promise<T> {
    try {
      return await this.moduleRef.resolve(serviceToken, undefined, { strict: false });
    } catch (error) {
      this.logger.warn(
        `无法解析服务 ${serviceToken.name || serviceToken}，使用回退实现: ${error.message}`
      );
      
      if (fallbackFactory) {
        return fallbackFactory();
      }
      
      throw new Error(`无法获取服务 ${serviceToken.name || serviceToken}，且未提供回退实现`);
    }
  }

  /**
   * 处理任务进度更新
   */
  private async updateProgress(
    job: Job,
    progress: number,
    message?: string
  ): Promise<void> {
    try {
      await job.progress({
        progress: Math.min(100, Math.max(0, progress)),
        message: message || `处理进度: ${progress}%`,
        timestamp: Date.now(),
      });
    } catch (error) {
      this.logger.error(`更新任务进度失败: ${error.message}`);
    }
  }

  /**
   * 创建模拟地址执行器服务
   */
  private createMockAddressExecutorService(): AddressExecutorService {
    const mockService = {
      extractFromText: async () => {
        this.logger.warn('使用模拟地址执行器服务');
        return {
          success: false,
          addresses: [],
          error: '无法获取地址执行器服务，使用模拟服务',
          processingTime: 0,
        };
      },
      extractFromMultipleTexts: async () => {
        this.logger.warn('使用模拟地址执行器服务');
        return [];
      },
      reverseGeocode: async () => {
        this.logger.warn('使用模拟地址执行器服务');
        return { success: false, error: '无法获取地址执行器服务' };
      },
      batchReverseGeocode: async () => {
        this.logger.warn('使用模拟地址执行器服务');
        return { success: false, error: '无法获取地址执行器服务' };
      },
      checkPythonServiceHealth: async () => {
        this.logger.warn('使用模拟地址执行器服务');
        return false;
      },
    };
    
    return mockService as unknown as AddressExecutorService;
  }

  /**
   * 创建模拟API使用跟踪服务
   */
  private createMockApiUsageTrackerService(): ApiUsageTrackerService {
    const mockService = {
      trackApiCall: async () => {
        this.logger.warn('使用模拟API使用跟踪服务');
        return 'mock-call-record-id';
      },
      trackApiUsage: async () => {
        this.logger.warn('使用模拟API使用跟踪服务');
        return true;
      },
    };
    
    return mockService as unknown as ApiUsageTrackerService;
  }
} 