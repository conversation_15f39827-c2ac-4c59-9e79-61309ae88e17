import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OrderType, PaymentStatus } from '../entities/order.entity';

export class OrderResponseDto {
  @ApiProperty({ description: '订单ID', example: 1 })
  id: number;

  @ApiProperty({ description: '订单号', example: 'ORD202312010001' })
  orderNumber: string;

  @ApiProperty({ 
    description: '订单类型', 
    enum: OrderType,
    example: OrderType.SERVICE_PURCHASE 
  })
  type: OrderType;

  @ApiProperty({ description: '订单金额', example: 100.00 })
  amount: number;

  @ApiPropertyOptional({ description: '购买次数', example: 100 })
  purchaseCount?: number;

  @ApiProperty({ 
    description: '支付状态', 
    enum: PaymentStatus,
    example: PaymentStatus.PENDING 
  })
  status: PaymentStatus;

  @ApiProperty({ description: '用户ID', example: 1 })
  userId: number;

  @ApiPropertyOptional({ description: '用户名', example: 'john_doe' })
  username?: string;

  @ApiPropertyOptional({ description: '服务ID', example: 1 })
  serviceId?: number;

  @ApiPropertyOptional({ description: '服务名称', example: 'OCR识别服务' })
  serviceName?: string;

  @ApiProperty({ description: '创建时间', example: '2023-12-01T10:00:00Z' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间', example: '2023-12-01T10:00:00Z' })
  updatedAt: Date;
}

export class OrderListResponseDto {
  @ApiProperty({ description: '订单列表', type: [OrderResponseDto] })
  data: OrderResponseDto[];

  @ApiProperty({ description: '总数量', example: 100 })
  total: number;

  @ApiProperty({ description: '当前页码', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  limit: number;

  @ApiProperty({ description: '总页数', example: 10 })
  totalPages: number;
}