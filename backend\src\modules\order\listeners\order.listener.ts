import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { OrderService } from '../order.service';
import { UserService } from '../../user/user.service';
import { UserServiceService } from '../../user-service/user-service.service';
import { OrderType } from '../enums/order.enum';

@Injectable()
export class OrderListener {
  private readonly logger = new Logger(OrderListener.name);

  constructor(
    @Inject(forwardRef(() => OrderService))
    private readonly orderService: OrderService,
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    @Inject(forwardRef(() => UserServiceService))
    private readonly userServiceService: UserServiceService,
  ) {}

  /**
   * 处理支付成功事件
   */
  @OnEvent('payment.success')
  async handlePaymentSuccess(payload: {
    paymentId: number;
    orderId: number;
    userId: number;
    paymentMethod: string;
    amount: number;
    paidAt: Date;
  }) {
    try {
      this.logger.log(`处理支付成功事件: 订单ID ${payload.orderId}, 用户ID ${payload.userId}, 金额 ${payload.amount}`);

      // 获取订单信息
      const order = await this.orderService.findById(payload.orderId);
      
      // 如果是充值订单，增加用户余额
      if (order.orderType === OrderType.RECHARGE) {
        await this.userService.addBalance(payload.userId, payload.amount, '充值');
        this.logger.log(`用户 ${payload.userId} 充值成功，金额: ${payload.amount}`);
      }

      // 完成订单（这会触发服务次数的增加）
      await this.orderService.completeOrder(payload.orderId);
      
      this.logger.log(`订单 ${payload.orderId} 处理完成`);
    } catch (error) {
      this.logger.error(`处理支付成功事件失败: ${error.message}`, error.stack);
      // 这里可以考虑重试机制或者发送告警
    }
  }

  /**
   * 处理支付失败事件
   */
  @OnEvent('payment.failed')
  async handlePaymentFailed(payload: {
    paymentId: number;
    orderId: number;
    userId: number;
    paymentMethod: string;
    amount: number;
    failureReason?: string;
  }) {
    try {
      this.logger.log(`处理支付失败事件: 订单ID ${payload.orderId}, 失败原因: ${payload.failureReason}`);

      // 这里可以添加支付失败的处理逻辑
      // 比如发送通知、记录日志等
      
      // 可以考虑自动重试或者提醒用户重新支付
      
    } catch (error) {
      this.logger.error(`处理支付失败事件失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 处理订单创建事件
   */
  @OnEvent('order.created')
  async handleOrderCreated(payload: {
    orderId: number;
    userId: number;
    orderType: OrderType;
    totalAmount: number;
  }) {
    try {
      this.logger.log(`处理订单创建事件: 订单ID ${payload.orderId}, 类型: ${payload.orderType}`);

      // 这里可以添加订单创建后的处理逻辑
      // 比如发送通知、记录统计等
      
    } catch (error) {
      this.logger.error(`处理订单创建事件失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 处理订单完成事件
   */
  @OnEvent('order.completed')
  async handleOrderCompleted(payload: {
    orderId: number;
    userId: number;
    orderType: OrderType;
    totalAmount: number;
  }) {
    try {
      this.logger.log(`处理订单完成事件: 订单ID ${payload.orderId}, 类型: ${payload.orderType}`);

      // 这里可以添加订单完成后的处理逻辑
      // 比如发送完成通知、更新统计数据等
      
    } catch (error) {
      this.logger.error(`处理订单完成事件失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 处理订单取消事件
   */
  @OnEvent('order.cancelled')
  async handleOrderCancelled(payload: {
    orderId: number;
    userId: number;
    orderType: OrderType;
  }) {
    try {
      this.logger.log(`处理订单取消事件: 订单ID ${payload.orderId}, 类型: ${payload.orderType}`);

      // 这里可以添加订单取消后的处理逻辑
      // 比如释放库存、发送通知等
      
    } catch (error) {
      this.logger.error(`处理订单取消事件失败: ${error.message}`, error.stack);
    }
  }
}
