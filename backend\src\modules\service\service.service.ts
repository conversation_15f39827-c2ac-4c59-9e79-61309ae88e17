import { 
  Injectable, 
  NotFoundException, 
  BadRequestException, 
  ConflictException,
  Logger
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, ILike } from 'typeorm';
import { ServiceEntity } from './entities/service.entity';
import { CreateServiceDto } from './dto/create-service.dto';
import { UpdateServiceDto } from './dto/update-service.dto';
import { QueryServiceDto } from './dto/query-service.dto';
import { ServiceStatus, PricingModel } from './enums/service.enum';
import { ServiceListResponseDto } from './dto/service-response.dto';

/**
 * 服务模块核心服务
 * 负责服务的创建、查询、更新和删除等基础功能
 */
@Injectable()
export class ServiceService {
  private readonly logger = new Logger(ServiceService.name);
  
  constructor(
    @InjectRepository(ServiceEntity)
    private readonly serviceRepository: Repository<ServiceEntity>
  ) {}

  /**
   * 创建服务
   * @param createServiceDto 创建服务DTO
   * @returns 新创建的服务
   */
  async create(createServiceDto: CreateServiceDto): Promise<ServiceEntity> {
    await this.validateBeforeCreate(createServiceDto);
    
    const service = this.serviceRepository.create(createServiceDto);
    return this.serviceRepository.save(service);
  }

  /**
   * 创建前验证
   * @param createServiceDto 创建服务DTO
   */
  private async validateBeforeCreate(createServiceDto: CreateServiceDto): Promise<void> {
    // 检查代码唯一性
    const existingService = await this.serviceRepository.findOne({
      where: { code: createServiceDto.code },
    });
    
    if (existingService) {
      throw new ConflictException(`服务代码 ${createServiceDto.code} 已存在`);
    }
    
    // 验证定价模型与单价的一致性
    this.validatePricingAndPrice(createServiceDto.pricingModel, createServiceDto.unitPrice);
    
    // 验证状态变更有效性
    if (createServiceDto.status === ServiceStatus.ACTIVE) {
      this.validateRequiredFieldsForActive(createServiceDto);
    }
  }

  /**
   * 验证定价模型与单价的一致性
   * @param pricingModel 定价模型
   * @param unitPrice 单价
   */
  private validatePricingAndPrice(pricingModel: PricingModel, unitPrice: number | undefined): void {
    if (pricingModel !== PricingModel.FREE && (unitPrice === undefined || unitPrice <= 0)) {
      throw new BadRequestException('非免费服务必须设置有效的单价');
    }
    
    if (pricingModel === PricingModel.FREE && unitPrice !== undefined && unitPrice > 0) {
      throw new BadRequestException('免费服务的单价必须为0');
    }
  }

  /**
   * 验证激活服务所需的必要字段
   * @param dto 服务DTO
   */
  private validateRequiredFieldsForActive(dto: CreateServiceDto | UpdateServiceDto): void {
    const requiredFields = ['name', 'type', 'description', 'endpoint'];
    const missingFields = requiredFields.filter(field => !dto[field]);
    
    if (missingFields.length > 0) {
      throw new BadRequestException(
        `将服务状态设置为"活跃"需要提供以下字段: ${missingFields.join(', ')}`
      );
    }
  }

  /**
   * 获取所有服务（不分页）
   * @param queryDto 查询参数
   * @returns 服务列表
   */
  async findAll(queryDto?: QueryServiceDto): Promise<ServiceEntity[]> {
    const { type, status, search, sortBy = 'sortOrder', sortOrder = 'ASC' } = queryDto || {};
    
    const where: any = {};
    
    if (type) where.type = type;
    if (status) where.status = status;
    
    if (search) {
      return this.serviceRepository.find({
        where: [
          { name: ILike(`%${search}%`) },
          { code: ILike(`%${search}%`) },
          { description: ILike(`%${search}%`) }
        ],
        order: { [sortBy]: sortOrder }
      });
    }
    
    return this.serviceRepository.find({
      where,
      order: { [sortBy]: sortOrder }
    });
  }

  /**
   * 获取服务列表（带分页）
   * @param queryDto 查询参数
   * @returns 分页的服务列表
   */
  async findAllPaginated(queryDto: QueryServiceDto = {}): Promise<ServiceListResponseDto> {
    const { page = 1, limit = 10 } = queryDto;
    
    // 构建查询条件
    const queryBuilder = this.serviceRepository.createQueryBuilder('service');
    
    // 应用过滤条件
    if (queryDto.type) {
      queryBuilder.andWhere('service.type = :type', { type: queryDto.type });
    }
    
    if (queryDto.status) {
      queryBuilder.andWhere('service.status = :status', { status: queryDto.status });
    }
    
    if (queryDto.search) {
      queryBuilder.andWhere(
        '(service.name LIKE :search OR service.description LIKE :search OR service.code LIKE :search)',
        { search: `%${queryDto.search}%` }
      );
    }
    
    // 排序
    queryBuilder.orderBy(`service.${queryDto.sortBy || 'sortOrder'}`, queryDto.sortOrder as 'ASC' | 'DESC');
    
    // 分页
    queryBuilder.skip((page - 1) * limit).take(limit);
    
    // 执行查询
    const [services, total] = await queryBuilder.getManyAndCount();
    
    return new ServiceListResponseDto(services, total, page, limit);
  }

  /**
   * 根据ID获取服务
   * @param id 服务ID
   * @returns 服务实体
   */
  async findOne(id: number): Promise<ServiceEntity> {
    const service = await this.serviceRepository.findOne({ where: { id } });
    if (!service) {
      throw new NotFoundException(`服务 ID ${id} 不存在`);
    }
    return service;
  }

  /**
   * 根据代码获取服务
   * @param code 服务代码
   * @returns 服务实体
   */
  async findByCode(code: string): Promise<ServiceEntity> {
    const service = await this.serviceRepository.findOne({ where: { code } });
    if (!service) {
      throw new NotFoundException(`服务代码 ${code} 不存在`);
    }
    return service;
  }

  /**
   * 更新服务
   * @param id 服务ID
   * @param updateServiceDto 更新服务DTO
   * @returns 更新后的服务
   */
  async update(id: number, updateServiceDto: UpdateServiceDto): Promise<ServiceEntity> {
    const service = await this.findOne(id);
    
    // 验证状态变更
    if (
      updateServiceDto.status === ServiceStatus.ACTIVE &&
      service.status !== ServiceStatus.ACTIVE
    ) {
      // 合并现有服务和更新内容，检查是否满足激活条件
      const mergedData = { ...service, ...updateServiceDto };
      this.validateRequiredFieldsForActive(mergedData);
    }
    
    // 验证定价模型变更
    if (
      updateServiceDto.pricingModel !== undefined &&
      updateServiceDto.pricingModel !== service.pricingModel
    ) {
      const unitPrice = updateServiceDto.unitPrice !== undefined 
        ? updateServiceDto.unitPrice 
        : service.unitPrice;
      
      this.validatePricingAndPrice(updateServiceDto.pricingModel, unitPrice);
    }
    
    // 应用更新并保存
    Object.assign(service, updateServiceDto);
    return this.serviceRepository.save(service);
  }

  /**
   * 删除服务
   * @param id 服务ID
   * @returns 删除结果
   */
  async remove(id: number): Promise<{ id: number; message: string }> {
    const service = await this.findOne(id);
    
    // 使用软删除
    await this.serviceRepository.softDelete(id);
    this.logger.log(`服务已删除: ID=${id}, 代码=${service.code}`);
    
    return {
      id,
      message: '服务已成功删除',
    };
  }
}
