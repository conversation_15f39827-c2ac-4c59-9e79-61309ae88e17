import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { RedisService } from './redis.service';
import { AuthUser, AuthApiKey, AuthResult } from '../common/types/auth.types';
import * as bcrypt from 'bcrypt';

/**
 * 核心认证服务
 * 提供纯粹的认证功能，不涉及业务逻辑
 * 用于解耦全局守卫和业务模块的依赖关系
 */
@Injectable()
export class CoreAuthService {
  private readonly logger = new Logger(CoreAuthService.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * 验证JWT令牌
   * 纯粹的JWT验证，不涉及用户业务逻辑
   */
  async validateJwtToken(token: string): Promise<AuthResult> {
    try {
      if (!token) {
        return { isValid: false, error: 'Token is required' };
      }
      // 检查令牌是否在黑名单中
      const blacklistKey = `blacklist:${token}`;
      try {
        const isBlacklisted = await this.redisService.exists(blacklistKey);
        if (isBlacklisted) {
          return { isValid: false, error: 'Token has been revoked' };
        }
      } catch (redisError) {
        this.logger.warn('Redis黑名单检查失败，继续验证:', redisError.message);
        return { isValid: false, error: 'Token has been revoked' };
      }
      // 验证JWT令牌
      const payload = this.jwtService.verify(token, {
        secret: this.configService.get('jwt.secretkey'),
      });
      if (!payload || !payload.sub) {
        return { isValid: false, error: 'Invalid token payload' };
      }
      
      // 检查用户级别的黑名单标记（用户被删除或管理员强制下线）
      const userId = payload.sub;
      const userBlacklistKey = `user_tokens_blacklist:${userId}`;
      try {
        const isUserBlacklisted = await this.redisService.exists(userBlacklistKey);
        if (isUserBlacklisted) {
          this.logger.warn(`用户 #${userId} 的令牌已被列入黑名单，可能是用户已被删除或管理员强制下线`);
          return { isValid: false, error: 'User tokens have been revoked' };
        }
      } catch (redisError) {
        this.logger.warn(`检查用户 #${userId} 黑名单失败:`, redisError.message);
        // 继续验证，不阻止授权
      }
      
      // 构造基础用户信息（包含JWT中的所有可用信息）
      const user: AuthUser = {
        id: payload.sub,
        username: payload?.username,
        nickname: payload?.nickname,
        email: payload?.email,
        phone: payload?.phone,
        userType: payload?.userType,
        status: payload?.status,
        tier: payload?.tier,
        role: payload?.role,
        verificationStatus: payload?.verificationStatus,
        balance: payload?.balance,
      };

      return {
        isValid: true,
        authType: 'jwt',
        user,
        payload,
      };
    } catch (error) {
      this.logger.error('JWT令牌验证失败:', error.message);

      if (error.name === 'TokenExpiredError') {
        return { isValid: false, error: 'Token has expired' };
      }

      if (error.name === 'JsonWebTokenError') {
        return { isValid: false, error: 'Invalid token' };
      }

      return { isValid: false, error: 'Token validation failed' };
    }
  }

  /**
   * 验证API密钥格式
   */
  validateApiKeyFormat(apiKey: string): boolean {
    return !!(apiKey && apiKey.startsWith('ak_') && apiKey.length > 10);
  }

  /**
   * 从缓存获取API密钥信息
   */
  async getApiKeyFromCache(apiKey: string): Promise<AuthApiKey | null> {
    try {
      const cacheKey = `api_key:${apiKey}`;
      const cachedData = await this.redisService.get(cacheKey);

      if (cachedData) {
        const apiKeyData = JSON.parse(cachedData);
        this.logger.debug(
          `API密钥从缓存中获取: ${apiKey.substring(0, 10)}...`,
        );

        // 打印缓存的API密钥数据，便于调试
        this.logger.warn(`缓存的API密钥数据: ${JSON.stringify(apiKeyData)}`);

        return {
          id: apiKeyData.id,
          userId: apiKeyData.userId,
          serviceId: apiKeyData.serviceId,
          name: apiKeyData.name,
          // 修复字段名不一致问题: 优先使用keyStatus字段，兼容status字段
          keyStatus: apiKeyData.keyStatus || apiKeyData.status || 'inactive',
          keyType: apiKeyData.keyType || 'standard',
          description: apiKeyData.description,
          permissions: apiKeyData.permissions || [],
          expiresAt: apiKeyData.expiresAt
            ? new Date(apiKeyData.expiresAt)
            : undefined,
          lastUsedAt: apiKeyData.lastUsedAt
            ? new Date(apiKeyData.lastUsedAt)
            : undefined,
          createdAt: apiKeyData.createdAt
            ? new Date(apiKeyData.createdAt)
            : new Date(),
          updatedAt: apiKeyData.updatedAt
            ? new Date(apiKeyData.updatedAt)
            : new Date(),
        };
      }

      return null;
    } catch (error) {
      this.logger.warn('从缓存获取API密钥失败:', error.message);
      return null;
    }
  }

  /**
   * 缓存API密钥信息
   */
  async cacheApiKey(
    keyHash: string,
    apiKeyData: AuthApiKey,
    ttl: number = 300,
  ): Promise<void> {
    try {
      const cacheKey = `api_key:${keyHash}`;
      await this.redisService.setex(cacheKey, ttl, JSON.stringify(apiKeyData));
      this.logger.debug(`API密钥已缓存: ${keyHash.substring(0, 10)}...`);
    } catch (error) {
      this.logger.warn('缓存API密钥失败:', error.message);
    }
  }

  /**
   * 生成API密钥哈希
   * 注意：此方法需要与种子脚本中的哈希方法保持一致
   */
  async hashApiKey(apiKey: string): Promise<string> {
    // 使用固定的盐值轮数(10)，与种子脚本保持一致
    try {
      this.logger.debug(`开始生成API密钥哈希: ${apiKey}`);
      const hash = await bcrypt.hash(apiKey, 10);
      this.logger.debug(`生成API密钥哈希成功: ${apiKey} => ${hash}`);
      return hash;
    } catch (error) {
      this.logger.error(`生成API密钥哈希失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证API密钥哈希
   */
  async verifyApiKeyHash(apiKey: string, hash: string): Promise<boolean> {
    try {
      const isMatch = await bcrypt.compare(apiKey, hash);
      this.logger.debug(`验证API密钥哈希: ${apiKey} => ${isMatch ? '匹配' : '不匹配'}`);
      return isMatch;
    } catch (error) {
      this.logger.error(`验证API密钥哈希出错: ${error.message}`);
      return false;
    }
  }

  /**
   * 检查API密钥是否过期
   */
  isApiKeyExpired(apiKey: AuthApiKey): boolean {
    if (!apiKey.expiresAt) {
      return false; // 永不过期
    }
    return new Date() > apiKey.expiresAt;
  }

  /**
   * 检查API密钥状态
   */
  isApiKeyActive(apiKey: AuthApiKey): boolean {
    return apiKey.keyStatus === 'active' && !this.isApiKeyExpired(apiKey);
  }

  /**
   * 记录认证失败
   */
  async recordAuthFailure(
    identifier: string,
    reason: string,
    clientIp?: string,
  ): Promise<void> {
    try {
      const failureKey = `auth_failure:${identifier}`;
      const failureData = {
        reason,
        clientIp,
        timestamp: new Date().toISOString(),
      };

      // 记录失败次数（1小时内）
      await this.redisService.lpush(failureKey, JSON.stringify(failureData));
      await this.redisService.expire(failureKey, 3600);

      // 限制列表长度
      await this.redisService.ltrim(failureKey, 0, 99);
    } catch (error) {
      this.logger.warn('记录认证失败信息失败:', error.message);
    }
  }

  /**
   * 检查是否被限制访问
   */
  async isAccessBlocked(identifier: string, maxFailures: number = 5): Promise<boolean> {
    try {
      const failureKey = `auth_failure:${identifier}`;
      const failureCount = await this.redisService.llen(failureKey);
      return failureCount >= maxFailures;
    } catch (error) {
      this.logger.warn('检查访问限制失败:', error.message);
      return false;
    }
  }

  /**
   * 清除认证失败记录
   */
  async clearAuthFailures(identifier: string): Promise<void> {
    try {
      const failureKey = `auth_failure:${identifier}`;
      await this.redisService.del(failureKey);
    } catch (error) {
      this.logger.warn('清除认证失败记录失败:', error.message);
    }
  }

  /**
   * 从数据库加载API密钥并缓存
   * 注意：此方法需要在适当的地方使用，如在UnifiedAuthGuard中检测到缓存未命中时
   * @param apiKey 明文API密钥
   * @returns 加载的API密钥信息或null
   */
  async loadApiKeyFromDbAndCache(apiKey: string): Promise<AuthApiKey | null> {
    try {
      this.logger.debug(`尝试从数据库加载API密钥: ${apiKey}`);
      
      // 动态获取ApiKeyService
      // 注意：这里需要在实际使用时实现，因为CoreAuthService不应该直接依赖ApiKeyService
      // 这里只是提供一个接口方法，实际实现需要在使用此方法的地方提供
      
      // 示例实现:
      // const apiKeyHash = await this.hashApiKey(apiKey);
      // const apiKeyEntity = await apiKeyService.findByKeyHash(apiKeyHash);
      // if (apiKeyEntity) {
      //   const apiKeyData = {
      //     id: apiKeyEntity.id,
      //     userId: apiKeyEntity.userId,
      //     // ... 其他字段
      //   };
      //   await this.cacheApiKey(apiKeyHash, apiKeyData);
      //   return apiKeyData;
      // }
      
      return null;
    } catch (error) {
      this.logger.error(`从数据库加载API密钥失败: ${error.message}`);
      return null;
    }
  }
}
