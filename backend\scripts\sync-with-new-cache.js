const { createConnection } = require('typeorm');
const crypto = require('crypto');
const Redis = require('ioredis');

/**
 * 使用新的缓存逻辑同步API密钥
 */
async function syncWithNewCache() {
  console.log('开始使用新的缓存逻辑同步API密钥...');
  
  // 创建数据库连接
  const connection = await createConnection({
    type: 'mysql',
    host: 'localhost',
    port: 3306,
    username: 'root',
    password: '123456',
    database: 'openapidb',
    entities: [],
    synchronize: false,
  });

  // 创建Redis连接
  const redis = new Redis({
    host: 'localhost',
    port: 6379,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
  });

  try {
    // 查询所有活跃的API密钥
    const apiKeys = await connection.query(`
      SELECT id, user_id, service_id, api_key, encrypted_secret_key, name, status, key_type, scopes, expires_at, last_used_at, createdat, updatedat, is_viewed
      FROM api_keys
      WHERE status = 'active'
    `);

    console.log(`找到 ${apiKeys.length} 个活跃的API密钥`);

    let syncedCount = 0;
    const errors = [];

    for (const apiKey of apiKeys) {
      try {
        // 1. 缓存API密钥信息（JSON格式）
        const apiKeyData = {
          id: apiKey.id,
          userId: apiKey.user_id,
          serviceId: apiKey.service_id,
          name: apiKey.name,
          keyStatus: apiKey.status,
          keyType: apiKey.key_type,
          permissions: apiKey.scopes,
          expiresAt: apiKey.expires_at,
          lastUsedAt: apiKey.last_used_at,
          createdAt: apiKey.createdat,
          updatedAt: apiKey.updatedat
        };

        const apiKeyCacheKey = `api_key:${apiKey.api_key}`;
        await redis.setex(apiKeyCacheKey, 86400, JSON.stringify(apiKeyData));

        // 2. 缓存解密后的Secret Key（纯字符串）
        if (apiKey.encrypted_secret_key) {
          try {
            const algorithm = 'aes-256-cbc';
            const key = crypto.scryptSync('api-key-secret', 'salt', 32);
            
            const parts = apiKey.encrypted_secret_key.split(':');
            if (parts.length === 2) {
              const iv = Buffer.from(parts[0], 'hex');
              const encrypted = parts[1];
              
              const decipher = crypto.createDecipheriv(algorithm, key, iv);
              let decrypted = decipher.update(encrypted, 'hex', 'utf8');
              decrypted += decipher.final('utf8');
              
              // 直接缓存解密后的Secret Key（不进行JSON序列化）
              const secretKeyCacheKey = `api_key_secret:${apiKey.id}`;
              await redis.setex(secretKeyCacheKey, 86400, decrypted);
              
              console.log(`  ✅ Secret Key已解密并缓存: ${decrypted.substring(0, 5)}...`);
            } else {
              console.log(`  ❌ 加密数据格式不正确: ${apiKey.encrypted_secret_key}`);
            }
          } catch (error) {
            console.log(`  ❌ 解密Secret Key失败: ${error.message}`);
          }
        }

        console.log(`✅ 同步成功: ID=${apiKey.id}, Key=${apiKey.api_key}`);
        syncedCount++;
        
      } catch (error) {
        const errorMsg = `同步API密钥失败 (ID: ${apiKey.id}): ${error.message}`;
        console.error(`❌ ${errorMsg}`);
        errors.push(errorMsg);
      }
    }

    console.log(`\n✅ 同步完成！成功同步 ${syncedCount}/${apiKeys.length} 个密钥`);
    
    if (errors.length > 0) {
      console.log('❌ 同步过程中的错误:');
      errors.forEach(error => console.log(`  - ${error}`));
    }
    
  } catch (error) {
    console.error('❌ 同步失败:', error.message);
  } finally {
    await connection.close();
    redis.disconnect();
    console.log('连接已关闭');
  }
}

// 运行脚本
syncWithNewCache().catch(console.error);
