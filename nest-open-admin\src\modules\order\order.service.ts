import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, Between, In } from 'typeorm';
import { OrderEntity, OrderType, PaymentStatus, } from './entities/order.entity';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { QueryOrderDto } from './dto/query-order.dto';
import {
  OrderResponseDto,
  OrderListResponseDto,
} from './dto/order-response.dto';
import { UserService } from '../user/user.service';
import { ServiceService } from '../service/service.service';
import { BaseCrudService } from '../../common/base/base-crud.service';

@Injectable()
export class OrderService extends BaseCrudService<
  OrderEntity,
  CreateOrderDto,
  UpdateOrderDto,
  QueryOrderDto,
  OrderResponseDto,
  OrderListResponseDto
> {
  protected readonly repository: Repository<OrderEntity>;
  protected readonly entityName = 'Order';

  constructor(
    @InjectRepository(OrderEntity)
    orderRepository: Repository<OrderEntity>,
    private readonly userService: UserService,
    private readonly serviceService: ServiceService,
  ) {
    super();
    this.repository = orderRepository;
  }

  /**
   * 创建前验证
   */
  protected async validateBeforeCreate(createDto: CreateOrderDto): Promise<void> {
    // 验证用户是否存在
    const user = await this.userService.findById(createDto.userId);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 如果是购买服务，验证服务是否存在
    if (createDto.type === OrderType.SERVICE_PURCHASE) {
      if (!createDto.serviceId) {
        throw new BadRequestException('购买服务时必须提供服务ID');
      }
      if (!createDto.purchaseCount) {
        throw new BadRequestException('购买服务时必须提供购买次数');
      }
      
      const service = await this.serviceService.findOne(createDto.serviceId);
      if (!service) {
        throw new NotFoundException('服务不存在');
      }
    }
  }

  /**
   * 转换创建DTO
   */
  protected transformCreateDto(createDto: CreateOrderDto): Partial<OrderEntity> {
    const orderNumber = this.generateOrderNumber();
    
    return {
      orderNumber,
      type: createDto.type,
      amount: createDto.amount,
      purchaseCount: createDto.purchaseCount,
      status: PaymentStatus.PENDING,
      user: { id: createDto.userId } as any,
      service: createDto.serviceId ? { id: createDto.serviceId } as any : undefined,
    };
  }

  /**
   * 更新前验证
   */
  protected async validateBeforeUpdate(
    id: number,
    updateDto: UpdateOrderDto,
    entity: OrderEntity,
  ): Promise<void> {
    // 检查订单状态，已完成或已退款的订单不能修改
    if (entity.status === PaymentStatus.COMPLETED || entity.status === PaymentStatus.REFUNDED) {
      throw new BadRequestException('已完成或已退款的订单不能修改');
    }
  }

  /**
   * 转换更新DTO
   */
  protected transformUpdateDto(updateDto: UpdateOrderDto, entity: OrderEntity): Partial<OrderEntity> {
    return updateDto;
  }

  /**
   * 删除前验证
   */
  protected async validateBeforeDelete(id: number, entity: OrderEntity): Promise<void> {
    // 检查订单状态，只有待支付和失败的订单可以删除
    if (entity.status !== PaymentStatus.PENDING && entity.status !== PaymentStatus.FAILED) {
      throw new BadRequestException('只有待支付和失败的订单可以删除');
    }
  }

  /**
   * 转换实体为响应DTO
   */
  protected transformToResponseDto(entity: OrderEntity): OrderResponseDto {
    return {
      id: entity.id,
      orderNumber: entity.orderNumber,
      type: entity.type,
      amount: entity.amount,
      purchaseCount: entity.purchaseCount,
      status: entity.status,
      userId: entity.user?.id,
      username: entity.user?.username,
      serviceId: entity.service?.id,
      serviceName: entity.service?.name,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  /**
   * 应用查询条件
   */
  protected applyQueryConditions(
    queryBuilder: any,
    queryDto: QueryOrderDto,
  ): void {
    const { orderNumber, type, status, userId, serviceId, startDate, endDate } = queryDto;

    if (orderNumber) {
      queryBuilder.andWhere('entity.orderNumber LIKE :orderNumber', {
        orderNumber: `%${orderNumber}%`,
      });
    }

    if (type) {
      queryBuilder.andWhere('entity.type = :type', { type });
    }

    if (status) {
      queryBuilder.andWhere('entity.status = :status', { status });
    }

    if (userId) {
      queryBuilder.andWhere('entity.userId = :userId', { userId });
    }

    if (serviceId) {
      queryBuilder.andWhere('entity.serviceId = :serviceId', { serviceId });
    }

    if (startDate && endDate) {
      queryBuilder.andWhere('entity.createdAt BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }
  }

  /**
   * 转换实体列表为响应DTO
   */
  protected transformToListResponseDto(
    data: OrderResponseDto[],
    total: number,
    page: number,
    limit: number,
    totalPages: number,
  ): OrderListResponseDto {
    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * 分页查询订单列表
   */
  async findAll(queryDto: QueryOrderDto): Promise<OrderListResponseDto> {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'DESC' } = queryDto;
    const skip = (page - 1) * limit;

    const queryBuilder = this.repository
      .createQueryBuilder('entity')
      .leftJoinAndSelect('entity.user', 'user')
      .leftJoinAndSelect('entity.service', 'service');

    this.applyQueryConditions(queryBuilder, queryDto);

    // 排序
    queryBuilder.orderBy(`entity.${sortBy}`, sortOrder);

    // 分页
    const [entities, total] = await queryBuilder
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    const data = entities.map((entity) => this.transformToResponseDto(entity));
    const totalPages = Math.ceil(total / limit);

    return this.transformToListResponseDto(data, total, page, limit, totalPages);
  }

  /**
   * 获取查询关联
   */
  protected getRelations(): string[] {
    return ['user', 'service'];
  }

  /**
   * 获取删除时的关联
   */
  protected getRelationsForDelete(): string[] {
    return [];
  }

  /**
   * 生成订单号
   */
  private generateOrderNumber(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const timestamp = now.getTime().toString().slice(-6);
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    
    return `ORD${year}${month}${day}${timestamp}${random}`;
  }

  /**
   * 创建零金额奖励订单
   * @param userId 用户ID
   * @param rewardCount 奖励次数
   * @param reason 奖励原因
   */
  async createRewardOrder(
    userId: number,
    rewardCount: number,
    reason: string = '实名认证奖励',
  ): Promise<OrderEntity> {
    try {
      // 验证用户是否存在
      const user = await this.userService.findById(userId);
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      const orderNumber = this.generateOrderNumber();
      
      const orderEntity = new OrderEntity();
      orderEntity.orderNumber = orderNumber;
      orderEntity.type = OrderType.REWARD;
      orderEntity.amount = 0;
      orderEntity.purchaseCount = rewardCount;
      orderEntity.status = PaymentStatus.COMPLETED;
      orderEntity.user = { id: userId } as any;
      orderEntity.service = undefined;

      const savedOrder = await this.repository.save(orderEntity);
      
      this.logger.log(
        `创建零金额奖励订单成功: 订单号=${orderNumber}, 用户ID=${userId}, 奖励次数=${rewardCount}, 原因=${reason}`,
      );
      
      return savedOrder;
    } catch (error) {
      this.logger.error(
        `创建零金额奖励订单失败: 用户ID=${userId}, 原因=${reason}, 错误=${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 根据用户ID获取奖励订单列表
   * @param userId 用户ID
   * @param limit 限制数量
   */
  async getRewardOrdersByUserId(
    userId: number,
    limit: number = 10,
  ): Promise<OrderEntity[]> {
    return this.repository.find({
      where: {
        user: { id: userId },
        type: OrderType.REWARD,
        amount: 0,
      },
      relations: ['user'],
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  /**
   * 检查用户是否有购买或充值订单记录
   * @param userId 用户ID
   * @returns 是否有购买或充值记录
   */
  async hasUserPurchaseOrRechargeOrders(userId: number): Promise<boolean> {
    const count = await this.repository.count({
      where: {
        user: { id: userId },
        type: In([OrderType.RECHARGE, OrderType.SERVICE_PURCHASE]),
        status: PaymentStatus.COMPLETED,
      },
    });
    return count > 0;
  }

}
