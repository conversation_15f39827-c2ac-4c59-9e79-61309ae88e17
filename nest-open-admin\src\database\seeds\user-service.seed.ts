import { DataSource } from 'typeorm';
import { UserServiceEntity } from '../../modules/user-service/entities/user-service.entity';
import { ServiceEntity } from '../../modules/service/entities/service.entity';
import { UserEntity } from '../../modules/user/entities/user.entity';

export class UserServiceSeed {
  async run(dataSource: DataSource): Promise<void> {
    const userServiceRepository = dataSource.getRepository(UserServiceEntity);
    const serviceRepository = dataSource.getRepository(ServiceEntity);
    const userRepository = dataSource.getRepository(UserEntity);

    // 检查是否已存在用户服务关联数据
    const existingUserServices = await userServiceRepository.count();
    if (existingUserServices > 0) {
      console.log('用户服务关联数据已存在，跳过创建');
      return;
    }

    // 获取所有服务
    const services = await serviceRepository.find();
    if (services.length === 0) {
      console.log('没有找到服务数据，无法创建用户服务关联');
      return;
    }

    // 获取用户
    const adminUser = await userRepository.findOne({ where: { id: 1 } });
    const testUser = await userRepository.findOne({ where: { id: 2 } });

    if (!adminUser || !testUser) {
      console.log('未找到管理员或测试用户，无法创建用户服务关联');
      return;
    }

    // 为管理员用户创建所有服务的无限制访问权限
    for (const service of services) {
      const adminUserService = new UserServiceEntity();
      adminUserService.user = adminUser;
      adminUserService.service = service;
      adminUserService.totalCount = 999999999; // 无限调用次数
      adminUserService.usedCount = 0;
      adminUserService.freeUsedToday = 0;
      adminUserService.lastResetDate = new Date();
      adminUserService.alertSent = false;

      await userServiceRepository.save(adminUserService);
      console.log(`✅ 为管理员用户创建服务 ${service.name} 的使用权限`);
    }

    // 为测试用户创建有限制的服务访问权限
    for (const service of services) {
      const testUserService = new UserServiceEntity();
      testUserService.user = testUser;
      testUserService.service = service;
      testUserService.totalCount = 1000; // 有限调用次数
      testUserService.usedCount = 0;
      testUserService.freeUsedToday = 0;
      testUserService.lastResetDate = new Date();
      testUserService.alertSent = false;

      await userServiceRepository.save(testUserService);
      console.log(`✅ 为测试用户创建服务 ${service.name} 的使用权限`);
    }

    console.log('✅ 用户服务关联数据创建完成');
  }
} 