import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OnEvent } from '@nestjs/event-emitter';
import { 
  IGatewayService, 
  IGatewayRequest, 
  IGatewayResponse, 
  IProcessingModeContext,
  IRequestValidation,
  IServiceHealth,
  IFileData
} from '../interfaces/gateway.interfaces';
import { 
  ProcessingMode, 
  GATEWAY_CONFIG, 
  GATEWAY_ERROR_CODES,
  DEFAULT_ERROR_MESSAGES 
} from '../config/gateway.constants';
import { GatewayRouterService } from './gateway-router.service';
import { GatewayProxyService } from './gateway-proxy.service';
import { ProcessingModeService } from './processing-mode.service';
import { TaskResultService } from './task-result.service';
import { QueueManagerService } from '@/modules/queue/services/queue-manager.service';
import { UserServiceService } from '@/modules/user-service/user-service.service';
import { CallRecordService } from '@/modules/call-record/call-record.service';
import { CallStatus } from '@/modules/call-record/enums/call-status.enum';
import { ServiceMappingService } from './service-mapping.service';
import { TaskStatus } from '../config/gateway.constants';
/**
 * 网关核心服务
 * 负责请求的统一处理、路由分发和模式决策
 * 遵循门面模式，提供统一的网关入口
 */
@Injectable()
export class GatewayService implements IGatewayService {
  private readonly logger = new Logger(GatewayService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly routerService: GatewayRouterService,
    private readonly proxyService: GatewayProxyService,
    private readonly processingModeService: ProcessingModeService,
    private readonly taskResultService: TaskResultService,
    private readonly queueManagerService: QueueManagerService,
    private readonly userServiceService: UserServiceService,
    private readonly callRecordService: CallRecordService,
    private readonly serviceMappingService: ServiceMappingService,
  ) {}

  /**
   * 处理网关请求的主入口方法
   */
  async handleRequest(request: IGatewayRequest): Promise<IGatewayResponse> {
    const startTime = Date.now();
    const requestId = this.generateRequestId();

    try {
      this.logger.log(`处理网关请求 [${requestId}]: ${request.method} ${request.path}`);

      // 1. 验证请求
      const validation = await this.validateRequest(request);
      if (!validation.isValid) {
        throw new BadRequestException(validation.errors.join(', '));
      }

      // 2. 检查调用次数（不扣减）
      await this.checkCallLimit(request);

      // 3. 路由匹配
      const routeMatch = this.routerService.matchRoute(request.path, request.method);
      if (!routeMatch.matched || !routeMatch.config) {
        throw new BadRequestException(DEFAULT_ERROR_MESSAGES[GATEWAY_ERROR_CODES.ROUTE_NOT_FOUND]);
      }

      // 3. 处理模式决策
      const context: IProcessingModeContext = {
        request,
        routeConfig: routeMatch.config,
        requestSize: this.calculateRequestSize(request),
        estimatedProcessingTime: 0, // 将在下面计算
      };

      // 使用ProcessingModeService估算处理时间
      context.estimatedProcessingTime = this.processingModeService.estimateProcessingTime(context);

      const processingMode = this.determineProcessingMode(context);
      this.logger.debug(`请求 [${requestId}] 使用处理模式: ${processingMode}`);

      // 4. 根据处理模式执行不同逻辑
      let response: IGatewayResponse;

      switch (processingMode) {
        case ProcessingMode.SYNC:
          response = await this.handleSyncRequest(request, routeMatch, requestId);
          break;
        case ProcessingMode.ASYNC:
          response = await this.handleAsyncRequest(request, routeMatch, requestId);
          break;
        case ProcessingMode.PROXY_ASYNC:
          response = await this.handleProxyAsyncRequest(request, routeMatch, requestId);
          break;
        default:
          throw new BadRequestException(DEFAULT_ERROR_MESSAGES[GATEWAY_ERROR_CODES.INVALID_PROCESSING_MODE]);
      }

      // 5. 添加响应元数据
      response.requestId = requestId;
      response.responseTime = Date.now() - startTime;

      // 6. 根据处理模式决定是否扣减次数
      if (processingMode === 'sync' || processingMode === 'proxy-async') {
        // 同步模式和代理异步模式：业务处理成功，立即扣减调用次数
        await this.consumeCallLimit(request);
      } else if (processingMode === 'async') {
        // 纯异步模式：任务入队成功，但不扣减次数
        // 次数扣减将在队列处理成功后进行
        this.logger.debug(`异步模式任务入队成功，次数扣减将在队列处理完成后进行: ${requestId}`);
      }

      // 7. 记录API调用
      await this.recordApiCall(request, response, response.responseTime);

      this.logger.log(`请求 [${requestId}] 处理完成，耗时: ${response.responseTime}ms`);
      return response;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error(`请求 [${requestId}] 处理失败: ${error.message}`, error.stack);

      // 直接抛出异常，让GlobalExceptionFilter统一处理
      // 避免在这里包装响应，防止多层嵌套
      throw error;
    }
  }

  /**
   * 确定处理模式
   */
  determineProcessingMode(context: IProcessingModeContext): ProcessingMode {
    return this.processingModeService.determineMode(context);
  }

  /**
   * 验证请求
   */
  async validateRequest(request: IGatewayRequest): Promise<IRequestValidation> {
    const errors: string[] = [];
    let fileData: IFileData | undefined;

    try {
      // 验证基本请求结构
      if (!request.path || !request.method) {
        errors.push('请求路径和方法不能为空');
      }

      // 验证文件数据
      if (request.file) {
        const fileValidation = this.validateFile(request.file);
        if (!fileValidation.isValid) {
          errors.push(...fileValidation.errors);
        } else {
          fileData = fileValidation.fileData;
        }
      }

      // 验证Base64图片数据
      if (request.body?.imageBase64) {
        const base64Validation = this.validateBase64Image(request.body.imageBase64);
        if (!base64Validation.isValid) {
          errors.push(...base64Validation.errors);
        } else {
          fileData = base64Validation.fileData;
        }
      }

      // 验证处理模式
      if (request.mode && !Object.values(ProcessingMode).includes(request.mode)) {
        errors.push('无效的处理模式');
      }

      return {
        isValid: errors.length === 0,
        errors,
        fileData,
      };

    } catch (error) {
      this.logger.error(`请求验证失败: ${error.message}`, error.stack);
      return {
        isValid: false,
        errors: ['请求验证失败'],
      };
    }
  }

  /**
   * 获取健康状态
   */
  async getHealthStatus(): Promise<IServiceHealth[]> {
    try {
      const services = ['OCR_SERVICE', 'ADDRESS_SERVICE', 'GEO_SERVICE'];
      const healthChecks = services.map(service => 
        this.proxyService.checkServiceHealth(service)
      );

      return await Promise.all(healthChecks);
    } catch (error) {
      this.logger.error(`获取健康状态失败: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * 处理同步请求
   */
  private async handleSyncRequest(
    request: IGatewayRequest,
    routeMatch: any,
    requestId: string
  ): Promise<IGatewayResponse> {
    try {
      const targetUrl = this.routerService.buildTargetUrl(
        routeMatch.config,
        request.path,
        request.query
      );

      const result = await this.proxyService.forwardRequest(targetUrl, request, {
        timeout: routeMatch.config.timeout,
        headers: { 'X-Request-ID': requestId },
      });
console.log('forwardRequest??????>>>>>', result)
      return {
        success: true,
        code: 200,
        message: '操作成功',
        ...result,
        timestamp: new Date().toISOString(),
        requestId: requestId,
      };
    } catch (error) {
      // 直接抛出错误，让GlobalExceptionFilter统一处理
      // 避免在这里进行包装，防止多层嵌套
      throw error;
    }
  }

  /**
   * 处理异步请求（客户端自管理）
   */
  private async handleAsyncRequest(
    request: IGatewayRequest,
    routeMatch: any,
    requestId: string
  ): Promise<IGatewayResponse> {
    try {
      // 确定任务类型
      const taskType = this.getTaskTypeFromRoute(routeMatch.config);

      // 准备任务数据，包含用户和服务信息用于后续扣减次数
      const taskData = {
        path: request.path,
        method: request.method,
        headers: request.headers,
        body: request.body,
        query: request.query,
        file: request.file ? {
          buffer: request.file.buffer,
          originalname: request.file.originalname,
          mimetype: request.file.mimetype,
          size: request.file.size,
        } : undefined,
        clientInfo: request.clientInfo,
        requestId,
        // 添加用户和服务信息，用于队列处理完成后扣减次数
        userId: request.user?.id,
        serviceId: this.getServiceIdFromPath(request.path),
      };

      // 添加任务到队列
      const taskId = await this.queueManagerService.addTask(taskType, taskData);

      return {
        success: true,
        code: 200,
        message: '任务已接受',
        taskId,
        status: TaskStatus.QUEUED,
        statusUrl: `/v1/op/tasks/${taskId}`,
        eventsUrl: `/v1/op/tasks/${taskId}/events`,
        timestamp: new Date().toISOString(),
        requestId: requestId,
      };
    } catch (error) {
      this.logger.error(`异步请求处理失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 处理代理异步请求
   */
  private async handleProxyAsyncRequest(
    request: IGatewayRequest,
    routeMatch: any,
    requestId: string
  ): Promise<IGatewayResponse> {
    try {
      // 确定任务类型
      const taskType = this.getTaskTypeFromRoute(routeMatch.config);

      // 准备任务数据
      const taskData = {
        path: request.path,
        method: request.method,
        headers: request.headers,
        body: request.body,
        query: request.query,
        file: request.file ? {
          buffer: request.file.buffer,
          originalname: request.file.originalname,
          mimetype: request.file.mimetype,
          size: request.file.size,
        } : undefined,
        clientInfo: request.clientInfo,
        requestId,
      };

      // 添加任务到队列
      const taskId = await this.queueManagerService.addTask(taskType, taskData);

      // 等待任务完成
      const maxWaitTime = routeMatch.config.maxProxyWaitTime || GATEWAY_CONFIG.MAX_PROXY_WAIT_TIME;
      const taskResult = await this.taskResultService.waitForTaskResult(taskId, {
        maxWaitTime,
        checkInterval: 1000,
      });

      if (taskResult.status === 'completed') {
        const result_tmp:IGatewayResponse = {
          success: true,
          code: 200,
          message: '操作成功',
          taskId,
          status: taskResult.status as any,
          processingTime: taskResult.processingTime,
          timestamp: new Date().toISOString(),
          requestId: requestId,
        }
        if(taskResult?.result?.data){
          result_tmp.data = taskResult.result.data
        }else {
          result_tmp.data = taskResult.result
        }
        return result_tmp;
      } else if (taskResult.status === 'timeout') {
        return {
          success: true,
          code: 200,
          message: '任务处理中，请稍后查询结果',
          data: {
            taskId,
            status: taskResult.status,
            statusUrl: `/v1/op/tasks/${taskId}`,
            eventsUrl: `/v1/op/tasks/${taskId}/events`,
          },
          timestamp: new Date().toISOString(),
          requestId: requestId,
        };
      } else {
        return {
          success: false,
          code: 500,
          message: taskResult.error || '任务处理失败',
          data: {
            taskId,
            status: taskResult.status,
            error: taskResult.error,
          },
          timestamp: new Date().toISOString(),
          requestId: requestId,
        };
      }
    } catch (error) {
      this.logger.error(`代理异步请求处理失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 验证上传文件
   */
  private validateFile(file: Express.Multer.File): { isValid: boolean; errors: string[]; fileData?: IFileData } {
    const errors: string[] = [];

    // 检查文件大小
    if (file.size > GATEWAY_CONFIG.MAX_FILE_SIZE) {
      errors.push(DEFAULT_ERROR_MESSAGES[GATEWAY_ERROR_CODES.FILE_TOO_LARGE]);
    }

    // 检查文件类型
    if (!GATEWAY_CONFIG.ALLOWED_MIME_TYPES.includes(file.mimetype)) {
      errors.push(DEFAULT_ERROR_MESSAGES[GATEWAY_ERROR_CODES.UNSUPPORTED_FILE_TYPE]);
    }

    const fileData: IFileData = {
      type: 'file',
      data: {
        buffer: file.buffer,
        originalname: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
      },
      size: file.size,
      mimetype: file.mimetype,
      originalname: file.originalname,
    };

    return {
      isValid: errors.length === 0,
      errors,
      fileData: errors.length === 0 ? fileData : undefined,
    };
  }

  /**
   * 验证Base64图片
   */
  private validateBase64Image(base64String: string): { isValid: boolean; errors: string[]; fileData?: IFileData } {
    const errors: string[] = [];

    if (!base64String || typeof base64String !== 'string') {
      errors.push(DEFAULT_ERROR_MESSAGES[GATEWAY_ERROR_CODES.INVALID_BASE64_IMAGE]);
      return { isValid: false, errors };
    }

    // 检查Base64格式
    const regex = /^data:image\/(jpeg|png|gif);base64,([A-Za-z0-9+/=])+$/;
    if (!regex.test(base64String)) {
      errors.push(DEFAULT_ERROR_MESSAGES[GATEWAY_ERROR_CODES.INVALID_BASE64_IMAGE]);
      return { isValid: false, errors };
    }

    // 估算文件大小
    const base64Size = Math.ceil((base64String.length * 3) / 4);
    if (base64Size > GATEWAY_CONFIG.MAX_FILE_SIZE) {
      errors.push(DEFAULT_ERROR_MESSAGES[GATEWAY_ERROR_CODES.FILE_TOO_LARGE]);
    }

    const fileData: IFileData = {
      type: 'base64',
      data: base64String,
      size: base64Size,
    };

    return {
      isValid: errors.length === 0,
      errors,
      fileData: errors.length === 0 ? fileData : undefined,
    };
  }

  /**
   * 计算请求大小
   */
  private calculateRequestSize(request: IGatewayRequest): number {
    let size = 0;

    if (request.body) {
      size += JSON.stringify(request.body).length;
    }

    if (request.file) {
      size += request.file.size;
    }

    return size;
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  }

  /**
   * 根据路由配置获取任务类型
   */
  private getTaskTypeFromRoute(routeConfig: any): string {
    // 根据队列名称确定任务类型
    switch (routeConfig.queue) {
      case 'ocr':
        return 'ocr';
      case 'sto-ocr':
        return 'sto-ocr';
      case 'extract-address':
        return 'extract-address';
      case 'rev-geo':
        return 'rev-geo';
      default:
        return 'unknown';
    }
  }

  /**
   * 检查调用次数（不扣减）
   */
  private async checkCallLimit(request: IGatewayRequest): Promise<void> {
    try {
      // 从请求中获取用户ID（通常从认证信息中获取）
      const userId = request.user?.id;
      if (!userId) {
        throw new BadRequestException('用户身份验证失败');
      }

      // 优先使用前端传递的服务ID，避免硬编码映射
      let serviceId: number | null = request.serviceId || null;
      if (!serviceId) {
        // 如果前端没有传递serviceId，使用路径映射（兼容旧版本）
        serviceId = this.getServiceIdFromPath(request.path);
        this.logger.warn(`前端未传递服务ID，使用路径映射: ${request.path} → ${serviceId}`);
      } else {
        this.logger.debug(`使用前端传递的服务ID: ${serviceId}`);
      }

      if (!serviceId) {
        throw new BadRequestException(`无法确定服务ID: 路径=${request.path}, 前端serviceId=${request.serviceId}`);
      }

      // 检查用户服务调用次数
      try {
        const userService = await this.userServiceService.findByUserAndService(userId, serviceId);
        if (!userService.enabled) {
          throw new BadRequestException('该服务已被禁用');
        }
        if (userService.remainingCount <= 0) {
          throw new BadRequestException('调用次数不足，请充值或升级服务');
        }
      } catch (error) {
        if (error.message.includes('不存在')) {
          throw new BadRequestException('您尚未开通此服务，请先购买服务');
        }
        throw error;
      }

      this.logger.debug(`用户 ${userId} 服务 ${serviceId} 调用次数检查通过`);

    } catch (error) {
      this.logger.error(`调用次数检查失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 消费调用次数（成功时调用）
   */
  private async consumeCallLimit(request: IGatewayRequest): Promise<void> {
    try {
      // 从请求中获取用户ID
      const userId = request.user?.id;
      if (!userId) {
        this.logger.warn('无法获取用户ID，跳过次数扣减');
        return;
      }

      // 优先使用前端传递的服务ID，避免硬编码映射
      let serviceId: number | null = request.serviceId || null;
      if (!serviceId) {
        // 如果前端没有传递serviceId，使用路径映射（兼容旧版本）
        serviceId = this.getServiceIdFromPath(request.path);
        this.logger.warn(`前端未传递服务ID，使用路径映射进行扣减: ${request.path} → ${serviceId}`);
      } else {
        this.logger.debug(`使用前端传递的服务ID进行扣减: ${serviceId}`);
      }

      if (!serviceId) {
        this.logger.warn(`无法确定服务ID，跳过次数扣减: 路径=${request.path}, 前端serviceId=${request.serviceId}`);
        return;
      }

      // 扣减一次调用次数
      await this.userServiceService.updateUsedCount(userId, serviceId, 1);

      this.logger.debug(`用户 ${userId} 服务 ${serviceId} 调用次数已扣减`);

    } catch (error) {
      this.logger.error(`调用次数扣减失败: ${error.message}`, error.stack);
      // 注意：这里不抛出异常，避免影响业务流程
      // 但应该记录错误，便于后续处理
    }
  }

  /**
   * 队列任务完成后扣减次数（供队列处理器调用）
   */
  async consumeCallLimitForTask(userId: number, serviceId: number): Promise<void> {
    try {
      // 扣减一次调用次数
      await this.userServiceService.updateUsedCount(userId, serviceId, 1);

      this.logger.debug(`队列任务完成，用户 ${userId} 服务 ${serviceId} 调用次数已扣减`);

    } catch (error) {
      this.logger.error(`队列任务完成后次数扣减失败: ${error.message}`, error.stack);
      // 这里也不抛出异常，避免影响队列处理流程
    }
  }

  /**
   * 监听队列任务完成事件
   */
  @OnEvent('queue.task.completed')
  async handleQueueTaskCompleted(payload: {
    taskId: string;
    userId: number;
    serviceId: number;
    taskType: string;
    result: any;
  }): Promise<void> {
    this.logger.debug(`收到队列任务完成事件: ${payload.taskId}, 用户: ${payload.userId}, 服务: ${payload.serviceId}`);

    // 调用扣减次数方法
    await this.consumeCallLimitForTask(payload.userId, payload.serviceId);
  }

  /**
   * 记录API调用
   */
  private async recordApiCall(
    request: IGatewayRequest,
    response: IGatewayResponse,
    processingTime: number
  ): Promise<void> {
    try {
      const userId = request.user?.id;
      if (!userId) {
        return; // 如果没有用户ID，跳过记录
      }

      // 根据请求路径确定服务ID
      const serviceId = this.getServiceIdFromPath(request.path);
      if (!serviceId) {
        this.logger.warn(`无法确定服务ID，跳过记录: ${request.path}`);
        return;
      }

      // 确定调用状态
      const status = response.success ? CallStatus.SUCCESS : CallStatus.FAILED;

      await this.callRecordService.create({
        userId,
        serviceId,
        method: request.method,
        requestId: request.requestId || this.generateRequestId(),
        status,
        failReason: response.success ? undefined : response.message,
        duration: processingTime,
        ipAddress: request.clientIp,
        apiKeyId: request.headers?.['x-api-key'], // 假设API密钥在header中
      });

      this.logger.debug(`API调用记录已保存: ${request.method} ${request.path}`);

    } catch (error) {
      this.logger.error(`记录API调用失败: ${error.message}`);
      // 记录失败不应该影响主流程，所以不抛出错误
    }
  }

  /**
   * 根据请求路径确定服务ID
   */
  private getServiceIdFromPath(path: string): number | null {
    // 使用ServiceMappingService进行动态映射
    return this.serviceMappingService.getServiceIdFromPath(path);
  }
}
