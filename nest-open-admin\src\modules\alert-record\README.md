# 预警记录模块 (Alert Record Module)

## 概述

预警记录模块是一个完整的告警管理系统，用于记录、管理和处理系统中的各种告警信息。该模块支持多种告警类型、级别和状态管理，提供了完整的CRUD操作、批量处理、统计分析等功能。

## 主要功能

### 1. 告警记录管理
- 创建告警记录
- 分页查询告警记录列表（支持多种筛选条件）
- 查询单个告警记录详情
- 更新告警记录信息
- 删除告警记录（软删除）

### 2. 批量操作
- 批量确认告警
- 批量解决告警
- 批量忽略告警
- 支持批量操作备注

### 3. 统计分析
- 告警总数统计
- 按状态分组统计
- 按类型分组统计
- 按级别分组统计
- 平均处理时间统计
- 通知成功率统计

### 4. 关联管理
- 用户关联（告警所属用户）
- 服务关联（告警相关服务）
- 处理人关联（告警处理人员）

## 数据模型

### 告警类型 (AlertType)
- `quota_exceeded`: 配额超限
- `rate_limit`: 频率限制
- `error_rate`: 错误率告警
- `response_time`: 响应时间告警
- `system_error`: 系统错误
- `security`: 安全告警
- `payment`: 支付相关
- `other`: 其他类型

### 告警级别 (AlertLevel)
- `low`: 低级别
- `medium`: 中级别
- `high`: 高级别
- `critical`: 严重级别

### 告警状态 (AlertStatus)
- `pending`: 待处理
- `acknowledged`: 已确认
- `resolved`: 已解决
- `ignored`: 已忽略

## API 接口

### 基础 CRUD 操作

#### 创建告警记录
```http
POST /alert-record
Content-Type: application/json

{
  "userId": 1,
  "serviceId": 1,
  "type": "quota_exceeded",
  "level": "high",
  "title": "配额超限告警",
  "content": "用户当前配额已超过限制，请及时充值",
  "triggerCondition": "调用次数 > 1000",
  "currentValue": "1050",
  "threshold": "1000"
}
```

#### 查询告警记录列表
```http
GET /alert-record?page=1&limit=10&userId=1&type=quota_exceeded&level=high&status=pending
```

#### 查询单个告警记录
```http
GET /alert-record/1
```

#### 更新告警记录
```http
PATCH /alert-record/1
Content-Type: application/json

{
  "status": "resolved",
  "resolveNote": "已为用户充值，问题解决",
  "resolvedBy": 2
}
```

#### 删除告警记录
```http
DELETE /alert-record/1
```

### 高级功能

#### 批量处理告警
```http
POST /alert-record/batch-process
Content-Type: application/json

{
  "ids": [1, 2, 3],
  "action": "resolve",
  "note": "批量解决配额相关告警"
}
```

#### 获取告警统计
```http
GET /alert-record/stats
```

## 查询参数说明

### 分页参数
- `page`: 页码（默认: 1）
- `limit`: 每页数量（默认: 10，最大: 100）

### 筛选参数
- `userId`: 用户ID
- `serviceId`: 服务ID
- `type`: 告警类型
- `level`: 告警级别
- `status`: 告警状态
- `notified`: 是否已通知
- `title`: 标题搜索（模糊匹配）
- `content`: 内容搜索（模糊匹配）
- `startDate`: 开始时间
- `endDate`: 结束时间

### 排序参数
- `sortBy`: 排序字段（默认: createdAt）
- `sortOrder`: 排序方向（ASC/DESC，默认: DESC）

## 响应格式

### 单个告警记录响应
```json
{
  "id": 1,
  "userId": 1,
  "serviceId": 1,
  "type": "quota_exceeded",
  "level": "high",
  "title": "配额超限告警",
  "content": "用户当前配额已超过限制，请及时充值",
  "triggerCondition": "调用次数 > 1000",
  "currentValue": "1050",
  "threshold": "1000",
  "status": "pending",
  "notified": false,
  "notificationMethods": ["email", "sms"],
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z",
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>"
  },
  "service": {
    "id": 1,
    "code": "OCR",
    "name": "光学字符识别",
    "type": "OCR"
  }
}
```

### 告警记录列表响应
```json
{
  "data": [...],
  "total": 100,
  "page": 1,
  "limit": 10,
  "totalPages": 10
}
```

### 统计信息响应
```json
{
  "totalAlerts": 1000,
  "pendingAlerts": 50,
  "acknowledgedAlerts": 200,
  "resolvedAlerts": 700,
  "ignoredAlerts": 50,
  "byType": {
    "quota_exceeded": 300,
    "rate_limit": 200,
    "error_rate": 150,
    "response_time": 100,
    "system_error": 100,
    "security": 50,
    "payment": 75,
    "other": 25
  },
  "byLevel": {
    "low": 200,
    "medium": 400,
    "high": 300,
    "critical": 100
  },
  "byStatus": {
    "pending": 50,
    "acknowledged": 200,
    "resolved": 700,
    "ignored": 50
  },
  "avgResolutionTime": 2.5,
  "notificationSuccessRate": 95.5
}
```

## 数据库设计

### 表结构
```sql
CREATE TABLE alert_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  userId INT NOT NULL COMMENT '用户ID',
  serviceId INT NULL COMMENT '服务ID',
  type ENUM('quota_exceeded', 'rate_limit', 'error_rate', 'response_time', 'system_error', 'security', 'payment', 'other') NOT NULL COMMENT '告警类型',
  level ENUM('low', 'medium', 'high', 'critical') NOT NULL COMMENT '告警级别',
  title VARCHAR(200) NOT NULL COMMENT '告警标题',
  content TEXT NOT NULL COMMENT '告警内容',
  triggerCondition VARCHAR(500) NULL COMMENT '触发条件',
  currentValue VARCHAR(100) NULL COMMENT '当前值',
  threshold VARCHAR(100) NULL COMMENT '阈值',
  metadata JSON NULL COMMENT '相关数据',
  status ENUM('pending', 'acknowledged', 'resolved', 'ignored') DEFAULT 'pending' COMMENT '告警状态',
  notified BOOLEAN DEFAULT FALSE COMMENT '是否已通知',
  notificationMethods JSON NULL COMMENT '通知方式',
  resolveNote TEXT NULL COMMENT '处理备注',
  resolvedBy INT NULL COMMENT '处理人ID',
  resolvedAt TIMESTAMP NULL COMMENT '处理时间',
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deletedAt TIMESTAMP NULL COMMENT '删除时间',
  createdBy VARCHAR(50) NULL COMMENT '创建者',
  updatedBy VARCHAR(50) NULL COMMENT '更新者',
  
  INDEX idx_user_type (userId, type),
  INDEX idx_service_status (serviceId, status),
  INDEX idx_level_status (level, status),
  INDEX idx_created_at (createdAt)
);
```

## 依赖关系

### 模块依赖
- **UserModule**: 获取用户信息，验证用户存在性
- **ServiceModule**: 获取服务信息，验证服务存在性
- **TypeOrmModule**: 数据库操作

### 实体关联
- **AlertRecordEntity** -> **UserEntity**: 多对一关系（告警所属用户）
- **AlertRecordEntity** -> **ServiceEntity**: 多对一关系（告警相关服务，可选）
- **AlertRecordEntity** -> **UserEntity**: 多对一关系（告警处理人，可选）

## 最佳实践

### 1. 数据验证
- 创建告警前验证用户和服务的存在性
- 使用DTO进行请求参数验证
- 枚举类型确保数据一致性

### 2. 性能优化
- 使用数据库索引优化查询性能
- 分页查询避免大量数据加载
- 关联查询使用leftJoinAndSelect优化

### 3. 安全考虑
- 软删除保护重要数据
- 参数验证防止SQL注入
- 权限控制通过全局守卫实现

### 4. 可维护性
- 清晰的代码结构和注释
- 统一的错误处理
- 完整的API文档

## 扩展功能

### 1. 通知系统集成
- 邮件通知
- 短信通知
- 钉钉/企业微信通知
- Webhook通知

### 2. 告警规则引擎
- 动态告警规则配置
- 告警阈值管理
- 告警频率控制
- 告警升级机制

### 3. 数据分析
- 告警趋势分析
- 告警热点分析
- 告警处理效率分析
- 自定义报表生成

### 4. 集成监控
- 系统监控集成
- 业务监控集成
- 第三方监控平台对接
- 实时告警推送