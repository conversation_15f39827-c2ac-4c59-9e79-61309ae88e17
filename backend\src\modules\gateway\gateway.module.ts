import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { BullModule } from '@nestjs/bull';
import { ConfigModule, ConfigService } from '@nestjs/config';


// Controllers
import { GatewayController } from './controllers/gateway.controller';
import { OcrGatewayController } from './controllers/ocr-gateway.controller';
import { StoOcrGatewayController } from './controllers/sto-ocr-gateway.controller';
import { AddressGatewayController } from './controllers/address-gateway.controller';
import { GeoGatewayController } from './controllers/geo-gateway.controller';

// Services
import { GatewayService } from './services/gateway.service';
import { GatewayRouterService } from './services/gateway-router.service';
import { GatewayProxyService } from './services/gateway-proxy.service';
import { TaskResultService } from './services/task-result.service';
import { ProcessingModeService } from './services/processing-mode.service';
import { RouteConfigService } from './services/route-config.service';
import { ProxyAsyncLimiterService } from './services/proxy-async-limiter.service';
import { ServiceMappingService } from './services/service-mapping.service';



// Shared modules
import { SharedModule } from '@/shared/shared.module';
import { QueueModule } from '@/modules/queue/queue.module';
import { UserServiceModule } from '@/modules/user-service/user-service.module';
import { CallRecordModule } from '@/modules/call-record/call-record.module';

/**
 * 网关模块
 * 负责API请求的路由、代理和处理模式管理
 * 遵循单一职责原则，专注于请求转发和代理功能
 */
@Module({
  imports: [
    ConfigModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        timeout: configService.get<number>('gateway.httpTimeout', 30000),
        maxRedirects: configService.get<number>('gateway.maxRedirects', 5),
        retries: configService.get<number>('gateway.retries', 3),
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueueAsync({
      name: 'gateway-tasks',
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        redis: {
          host: configService.get<string>('redis.host', 'localhost'),
          port: configService.get<number>('redis.port', 6379),
          password: configService.get<string>('redis.password'),
          db: configService.get<number>('redis.db', 0),
        },
        defaultJobOptions: {
          attempts: configService.get<number>('queue.defaultAttempts', 3),
          timeout: configService.get<number>('queue.defaultTimeout', 180000),
          removeOnComplete: configService.get<number>('queue.removeOnComplete', 10),
          removeOnFail: configService.get<number>('queue.removeOnFail', 50),
        },
      }),
      inject: [ConfigService],
    }),
    SharedModule,
    QueueModule,
    UserServiceModule,
    CallRecordModule,
  ],
  controllers: [
    GatewayController,
    OcrGatewayController,
    StoOcrGatewayController,
    AddressGatewayController,
    GeoGatewayController,
  ],
  providers: [
    GatewayService,
    GatewayRouterService,
    GatewayProxyService,
    TaskResultService,
    ProcessingModeService,
    RouteConfigService,
    ProxyAsyncLimiterService,
    ServiceMappingService,
  ],
  exports: [
    GatewayService,
    GatewayRouterService,
    GatewayProxyService,
    TaskResultService,
  ],
})
export class GatewayModule {}
