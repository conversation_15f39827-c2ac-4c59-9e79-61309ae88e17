import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  ParseArrayPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { CallRecordService } from './call-record.service';
import {
  CreateCallRecordDto,
  UpdateCallRecordDto,
  QueryCallRecordDto,
  CallRecordResponseDto,
  CallRecordListResponseDto,
  CallRecordStatsDto,
  ExportCallRecordDto,
} from './dto/call-record.dto';

@ApiTags('调用记录管理')
@Controller('call-record')
export class CallRecordController {
  constructor(private readonly callRecordService: CallRecordService) {}

  @Post()
  @ApiOperation({ summary: '创建调用记录' })
  create(@Body() createCallRecordDto: CreateCallRecordDto): Promise<CallRecordResponseDto> {
    return this.callRecordService.create(createCallRecordDto);
  }

  @Get()
  @ApiOperation({ summary: '查询调用记录列表' })
  findAll(@Query() queryDto: QueryCallRecordDto): Promise<CallRecordListResponseDto> {
    return this.callRecordService.findAll(queryDto);
  }

  @Get('stats')
  @ApiOperation({ summary: '获取调用记录统计' })
  getStats(@Query() queryDto: QueryCallRecordDto): Promise<CallRecordStatsDto> {
    return this.callRecordService.getStats(queryDto);
  }

  @Get('request/:requestId')
  @ApiOperation({ summary: '根据请求ID查询调用记录' })
  @ApiParam({ name: 'requestId', description: '请求ID' })
  @ApiResponse({ status: 404, description: '调用记录不存在' })
  findByRequestId(@Param('requestId') requestId: string): Promise<CallRecordResponseDto> {
    return this.callRecordService.findByRequestId(requestId);
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID查询调用记录' })
  @ApiParam({ name: 'id', description: '记录ID' })
  findOne(@Param('id', ParseIntPipe) id: number): Promise<CallRecordResponseDto> {
    return this.callRecordService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新调用记录' })
  @ApiParam({ name: 'id', description: '记录ID' })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCallRecordDto: UpdateCallRecordDto,
  ): Promise<CallRecordResponseDto> {
    return this.callRecordService.update(id, updateCallRecordDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除调用记录' })
  @ApiParam({ name: 'id', description: '记录ID' })
  remove(@Param('id', ParseIntPipe) id: number): Promise<{ message: string }> {
    return this.callRecordService.remove(id);
  }

  @Delete('batch/remove')
  @ApiOperation({ summary: '批量删除调用记录' })
  @ApiBody({
    description: '要删除的记录ID数组',
    schema: {
      type: 'object',
      properties: {
        ids: {
          type: 'array',
          items: { type: 'number' },
          description: '记录ID数组',
        },
      },
    },
  })
  batchRemove(
    @Body('ids', new ParseArrayPipe({ items: Number, separator: ',' }))
    ids: number[],
  ): Promise<void> {
    return this.callRecordService.batchRemove(ids);
  }
}
