import { TaskStatus } from './task-status.enum';

/**
 * 任务状态更新接口
 * 描述任务状态更新时的数据结构
 */
export interface TaskStatusUpdate {
  status: TaskStatus;
  progress?: number;
  message?: string;
  result?: any;
  error?: string;
}

/**
 * 任务详情接口
 * 描述获取任务状态时的数据结构
 */
export interface TaskDetails extends TaskStatusUpdate {
  taskId: string;
  type: string;
  createdAt: number;
  updatedAt?: number;
}

/**
 * 创建任务选项接口
 * 描述创建任务时的可选配置
 */
export interface CreateTaskOptions {
  priority?: number;
  delay?: number;
} 