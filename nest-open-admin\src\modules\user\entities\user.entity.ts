import {
  Entity,
  Column,
  OneToMany,
  BeforeInsert,
  BeforeUpdate,
  Index,
  OneToOne,
  JoinColumn,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { BaseEntity } from '../../../common/entities/base.entity';
import { UserVerificationEntity } from './user-verification.entity';
import { UserPaymentStatusEntity } from './user-payment-status.entity';

// 移除直接导入其他模块的Entity，使用字符串关系定义避免循环依赖
// import { ApiKeyEntity } from '../../api-key/entities/api-key.entity';
// import { CallRecordEntity } from '../../call-record/entities/call-record.entity';
// import { OrderEntity } from '../../order/entities/order.entity';
// import { AlertRecordEntity } from '../../alert-record/entities/alert-record.entity';
// import { UserServiceEntity } from '../../user-service/entities/user-service.entity';
import * as bcrypt from 'bcrypt';


/**
 * 用户类型枚举
 */
export enum UserType {
  INDIVIDUAL = 'individual', // 个人用户
  ENTERPRISE = 'enterprise', // 企业用户
  ORGANIZATION = 'organization', // 机构用户
}
/**
 * 用户认证状态枚举
 */
export enum UserVerificationStatus {
  UNVERIFIED = 'unverified', // 未认证
  PERSONAL_VERIFIED = 'personal_verified', // 个人实名认证
  ENTERPRISE_VERIFIED = 'enterprise_verified', // 企业实名认证
}

/**
 * 用户状态枚举
 */
export enum UserStatus {
  ACTIVE = 'active', // 活跃
  SUSPENDED = 'suspended', // 暂停
  BANNED = 'banned', // 封禁
  PENDING = 'pending', // 待审核
  LOCKED = 'locked', // 锁定
}

export enum RoleEnum {
  USER = 'user', // 普通用户
  ADMIN = 'admin', // admin
  SUPER = 'super', // 超管
}
export enum TierEnum {
  BASIC = 'basic', //  基础
  PREMIUM = 'premium', // 高级
  ENTERPRISE = 'enterprise', // 企业
}
/**
 * 用户实体
 */
@Entity('open_user', {
  comment: '用户信息表',
})
@Index(['phone']) // 如设置了unique: true则不再需要手动设置索引Index()否则会导致重复
export class UserEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
    comment: '用户名',
  })
  username: string;

  @Column({
    type: 'varchar',
    length: 200,
    unique: true,
    nullable: true,
    comment: '邮箱',
  })
  email: string;

  @Exclude({ toPlainOnly: true }) // 输出屏蔽密码
  @Column({
    type: 'varchar',
    length: 255,
    comment: '密码哈希',
  })
  password: string;

  @Column({
    type: 'varchar',
    length: 30,
    nullable: false,
    comment: '用户昵称',
  })
  public nickname: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '真实姓名',
  })
  realName?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: '个人简介',
  })
  bio?: string;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '手机号',
  })
  phone?: string;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '头像URL',
  })
  avatar?: string;

  @Column({
    type: 'enum',
    enum: ['male', 'female', 'unknown'],
    default: 'unknown',
    comment: '性别',
  })
  gender: string;

  @Column({
    type: 'enum',
    enum: UserType,
    default: UserType.INDIVIDUAL,
    comment: '用户类型',
  })
  userType: UserType;

  @Column({
    type: 'enum',
    enum: UserVerificationStatus,
    default: UserVerificationStatus.UNVERIFIED,
    comment: '认证状态',
  })
  verificationStatus: UserVerificationStatus;

  @Column({
    type: 'enum',
    enum: RoleEnum,
    default: RoleEnum.USER,
    comment: '角色',
  })
  role: RoleEnum;

  @Column({ default: null })
  openid: string;
  
  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE,
    comment: '账户状态：active-活跃，suspended-暂停，banned-封禁，pending-待审核，locked-锁定',
  })
  userStatus: UserStatus;

  @Column({
    type: 'boolean',
    default: false,
    comment: '邮箱是否验证：false-未验证，true-已验证',
  })
  emailVerified: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: '手机是否验证：false-未验证，true-已验证',
  })
  phoneVerified: boolean;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '最后登录时间',
  })
  lastLoginAt?: Date;

  @Column({
    type: 'varchar',
    length: 45,
    nullable: true,
    comment: '最后登录IP',
  })
  lastLoginIp?: string;

  @Column({
    type: 'int',
    default: 0,
    comment: '登录失败次数',
  })
  loginFailCount: number;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '账户锁定截止时间',
  })
  lockedUntil?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: '用户偏好设置',
  })
  preferences?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: '用户设置',
  })
  settings?: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '每日免费使用次数重置日期',
  })
  lastDailyResetDate?: Date;

  @Column({
    type: 'enum',
    enum: TierEnum,
    default: TierEnum.BASIC,
    comment: '用户等级：basic-基础，premium-高级，enterprise-企业',
  })
  tier: TierEnum;

  @Column({ type: 'int', default: 0, comment: '剩余使用次数' })
  remainingUsage: number;

  @Column({ type: 'int', default: 0, comment: '总使用次数' })
  totalUsage: number;

  @Column({ type: 'int', default: 0, comment: '每日免费使用次数剩余' })
  dailyFreeUsageRemaining: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0.0,
    comment: '账户余额',
  })
  balance: number;

  // 新增字段
  @Column({ default: true, comment: '是否有资格获取免费额度' })
  isFreeQuotaEligible: boolean;

  // 关联关系
  // 关联关系 - 使用字符串关系定义，避免循环引用
  @OneToMany('ApiKeyEntity', 'user')
  apiKeys: any[];

  @OneToMany('UserServiceEntity', 'user')
  userServices: any[];

  @OneToMany('OrderEntity', 'user')
  orders: any[];

  @OneToOne(() => UserVerificationEntity, (profile) => profile.user)
  verificationInfo: UserVerificationEntity;

  @OneToMany('CallRecordEntity', 'user')
  callRecords: any[];

  @OneToMany('AlertRecordEntity', 'user')
  alertRecords: any[];

  @OneToMany(() => UserPaymentStatusEntity, (status) => status.user)
  paymentStatus: UserPaymentStatusEntity[];

  // 通过生命周期把密码加密再存入库
  @BeforeInsert()
  @BeforeUpdate()
  async encryptPwd() {
    try {
      const salt = await bcrypt.genSaltSync(10);
      if (!this.password) return;
      this.password = await bcrypt.hashSync(this.password, salt);
    } catch (e) {
      console.log(e);
      throw e;
    }
  }
}
