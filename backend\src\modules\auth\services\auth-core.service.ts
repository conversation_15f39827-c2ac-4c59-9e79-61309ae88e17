import {
  Injectable,
  BadRequestException,
  UnauthorizedException,
  ForbiddenException,
  NotFoundException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { UserService } from '@/modules/user/user.service';
import { UserStatus, UserType } from '@/modules/user/entities/user.entity';
import { ApiKeyService } from '@/modules/api-key/services/api-key.service';
import { RegisterDto } from '../dto/register.dto';
import { LoginDto } from '../dto/login.dto';
import { ResetPasswordDto } from '../dto/reset-password.dto';
import { ChangePasswordDto } from '../dto/change-password.dto';
import { RefreshTokenDto } from '../dto/refresh-token.dto';
import { TokenResponseDto, RegisterResponseDto, LoginResponseDto } from '../dto/auth-response.dto';
import { getClientIp } from '../../../common/utils/ip.utils';
import { AuthErrorHandlerService } from './auth-error-handler.service';
import { AuthSecurityService } from './auth-security.service';
import { PasswordPolicyService } from './password-policy.service';
import { VerificationCodeService } from './verification-code.service';
import { TokenManagementService } from './token-management.service';
import { CaptchaService } from '@/shared/captcha.service';

/**
 * 认证核心服务
 * 专门负责核心的认证逻辑，符合单一职责原则
 */
@Injectable()
export class AuthCoreService {
  private readonly logger = new Logger(AuthCoreService.name);

  constructor(
    private readonly userService: UserService,
    private readonly apiKeyService: ApiKeyService,
    private readonly authErrorHandler: AuthErrorHandlerService,
    private readonly authSecurity: AuthSecurityService,
    private readonly passwordPolicy: PasswordPolicyService,
    private readonly verificationCode: VerificationCodeService,
    private readonly tokenManagement: TokenManagementService,
    private readonly captchaService: CaptchaService,
  ) {}

  /**
   * 用户注册
   */
  async register(registerDto: RegisterDto): Promise<RegisterResponseDto> {
    const { email, phone, password, confirmPassword, emailCode, smsCode, userType, nickname } = registerDto;

    try {
      // 验证密码一致性
      this.passwordPolicy.validatePasswordConfirmation(password, confirmPassword);

      // 验证密码复杂度
      const passwordValidation = this.passwordPolicy.validatePasswordComplexity(password);
      if (!passwordValidation.isValid) {
        throw new BadRequestException(`密码不符合安全要求: ${passwordValidation.errors.join(', ')}`);
      }

      // 验证验证码
      if (email && emailCode) {
        await this.verificationCode.verifyCode(email, emailCode, 'register' as any, true);
      } else if (phone && smsCode) {
        await this.verificationCode.verifyCode(phone, smsCode, 'register' as any, false);
      } else {
        throw new BadRequestException('请提供有效的邮箱验证码或短信验证码');
      }

      // 哈希密码
      const hashedPassword = await this.passwordPolicy.hashPassword(password);

      // 生成用户名
      const username = this.generateUsername(email || phone!);

      // 创建用户
      const user = await this.userService.create({
        username,
        email,
        phone,
        password: hashedPassword,
        nickname: nickname || username,
        userType: userType || UserType.INDIVIDUAL,
      });

      // 创建默认API密钥
      const apiKey = await this.apiKeyService.createApiKey(user.id, {
        name: '默认密钥',
      } as any);

      // 生成令牌
      const { accessToken, refreshToken, expiresIn } = await this.tokenManagement.generateTokens(user);

      this.logger.log(`用户注册成功: ${user.email || user.phone}`);

      return {
        userId: user.id,
        email: user.email,
        phone: user.phone,
        nickname: user.nickname,
        token: accessToken,
        refreshToken,
        expiresIn,
        apiKeys: [
          {
            id: apiKey.id,
            name: apiKey.name,
            createdAt: apiKey.createdAt,
          },
        ],
      };
    } catch (error) {
      if (error instanceof ConflictException || error instanceof BadRequestException) {
        throw error;
      }
      
      const baseContext = this.authErrorHandler.extractContext();
      const context = {
        ...baseContext,
        email: registerDto.email,
        phone: registerDto.phone,
      };
      
      this.authErrorHandler.handleRegisterError(error, context);
    }
  }

  /**
   * 用户登录
   */
  async login(loginDto: LoginDto, request?: any): Promise<LoginResponseDto> {
    try {
      const { email, phone, username, password, emailCode, smsCode, securityVerification } = loginDto;

      // 安全验证检查（必需）
      if (!securityVerification) {
        throw new BadRequestException('缺少安全验证数据');
      }

      try {
        // 确保 type 字段存在
        const securityData = {
          ...securityVerification,
          type: securityVerification.type || 'slide' // 默认为滑动验证
        };

        const isSecurityValid = await this.captchaService.validateSecurityVerification(securityData);
        if (!isSecurityValid) {
          throw new BadRequestException('安全验证失败');
        }
      } catch (error) {
        this.logger.warn(`安全验证失败: ${error.message}`, {
          identifier: email || phone,
          ip: request ? getClientIp(request) : 'unknown',
        });
        throw new BadRequestException('安全验证失败');
      }

      // IP安全检查
      const identifier = email || phone || username || 'unknown';
      const ipSecurity = await this.authSecurity.checkIpSecurity(request, identifier);

      if (!ipSecurity.isAllowed) {
        await this.authSecurity.recordSecurityEvent('ip_blocked', {
          ip: request ? getClientIp(request) : 'unknown',
          identifier,
          reason: ipSecurity.reason,
        });
        throw new BadRequestException(ipSecurity.reason);
      }

      // 查找用户（使用安全方法，不抛出异常）
      let user: any = null;
      if (email) {
        user = await this.userService.findByEmailSafe(email);
      } else if (phone) {
        user = await this.userService.findByPhoneSafe(phone);
      } else if (username) {
        user = await this.userService.findByUsernameSafe(username);
      }

      // 如果用户不存在且是短信验证码登录且启用了静默注册，执行静默注册
      if (!user && smsCode && phone && loginDto.isSilentRegister) {
        this.logger.log(`用户不存在，开始静默注册: ${phone}`);

        // 先验证短信验证码
        await this.verificationCode.verifyCode(phone, smsCode, 'login' as any, false);

        // 创建新用户（静默注册）
        const username = `user_${Date.now()}`;
        const nickname = `用户_${phone.slice(-4)}`;

        user = await this.userService.create({
          username,
          phone,
          nickname,
          userType: 'individual' as any,
          password: '', // 静默注册不需要密码
        });

        // 创建默认API密钥
        const apiKey = await this.apiKeyService.createApiKey(user.id, {
          name: '默认密钥',
        } as any);

        this.logger.log(`静默注册成功: ${phone}, 用户ID: ${user.id}`);

        // 生成令牌并返回
        const { accessToken, refreshToken, expiresIn } = await this.tokenManagement.generateTokens(user);

        return {
          userId: user.id,
          email: user.email,
          phone: user.phone,
          nickname: user.nickname,
          token: accessToken,
          refreshToken,
          expiresIn,
          apiKeys: [
            {
              id: apiKey.id,
              name: apiKey.name,
              createdAt: apiKey.createdAt,
            },
          ],
        };
      } else if (!user) {
        throw new UnauthorizedException('用户不存在');
      }

      // 验证登录方式
      if (password) {
        // 密码登录
        const isPasswordValid = await this.passwordPolicy.validateCurrentPassword(password, user.password);
        if (!isPasswordValid) {
          await this.incrementLoginFailCount(user.id);
          throw new UnauthorizedException('密码错误');
        }
      } else if (emailCode && email) {
        // 邮箱验证码登录
        await this.verificationCode.verifyCode(email, emailCode, 'login' as any, true);
      } else if (smsCode && phone) {
        // 短信验证码登录
        await this.verificationCode.verifyCode(phone, smsCode, 'login' as any, false);
      } else {
        throw new BadRequestException('请提供密码或验证码');
      }

      // 检查用户状态
      this.checkUserStatus(user);

      // 设备指纹检查
      if (request) {
        const deviceCheck = await this.authSecurity.checkDeviceFingerprint(request, user.id);
        
        if (deviceCheck.isNewDevice) {
          await this.authSecurity.recordSecurityEvent('new_device_login', {
            userId: user.id,
            deviceId: deviceCheck.deviceId,
            ip: getClientIp(request),
            userAgent: request.headers?.['user-agent'],
            riskLevel: deviceCheck.riskLevel,
          });

          if (deviceCheck.riskLevel === 'high') {
            this.logger.warn(`高风险设备登录: 用户${user.id}, 设备${deviceCheck.deviceId}`);
          }
        }
      }

      // 重置登录失败计数
      await this.resetLoginFailCount(user.id);

      // 更新最后登录信息
      const clientIp = request ? getClientIp(request) : '127.0.0.1';
      await this.updateLastLogin(user.id, clientIp);

      // 生成令牌
      const { accessToken, refreshToken, expiresIn } = await this.tokenManagement.generateTokens(user);

      return {
        userId: user.id,
        email: user.email,
        phone: user.phone,
        nickname: user.nickname,
        token: accessToken,
        refreshToken,
        expiresIn,
      };
    } catch (error) {
      if (error instanceof UnauthorizedException || 
          error instanceof BadRequestException || 
          error instanceof ForbiddenException) {
        throw error;
      }

      const baseContext = this.authErrorHandler.extractContext(request);
      const context = {
        ...baseContext,
        identifier: loginDto.email || loginDto.phone || loginDto.username,
        loginType: (loginDto.emailCode || loginDto.smsCode) ?
          (loginDto.emailCode ? 'email_code' : 'sms_code') : 'password' as any,
      };

      this.authErrorHandler.handleLoginError(error, context);
    }
  }

  /**
   * 刷新令牌
   */
  async refreshToken(refreshTokenDto: RefreshTokenDto): Promise<TokenResponseDto> {
    try {
      const result = await this.tokenManagement.refreshAccessToken(refreshTokenDto.refreshToken);
      return {
        token: result.accessToken,
        refreshToken: result.refreshToken,
        expiresIn: result.expiresIn,
      };
    } catch (error) {
      this.authErrorHandler.handleTokenError(error, { tokenType: 'refresh' });
    }
  }

  /**
   * 登出
   */
  async logout(accessToken: string, refreshToken?: string): Promise<{ message: string }> {
    try {
      await this.tokenManagement.revokeTokens(accessToken, refreshToken || '');
      return { message: '登出成功' };
    } catch (error) {
      this.logger.error(`登出失败: ${error.message}`, error.stack);
      // 即使撤销失败，也返回成功，因为从客户端角度看登出已完成
      return { message: '登出成功' };
    }
  }

  /**
   * 修改密码
   */
  async changePassword(userId: number, changePasswordDto: ChangePasswordDto): Promise<{ message: string }> {
    const { currentPassword, newPassword, confirmPassword } = changePasswordDto;

    try {
      // 验证新密码一致性
      this.passwordPolicy.validatePasswordConfirmation(newPassword, confirmPassword);

      // 验证新密码复杂度
      const passwordValidation = this.passwordPolicy.validatePasswordComplexity(newPassword);
      if (!passwordValidation.isValid) {
        throw new BadRequestException(`新密码不符合安全要求: ${passwordValidation.errors.join(', ')}`);
      }

      // 获取用户信息
      const user = await this.userService.findById(userId);

      // 验证当前密码
      const isCurrentPasswordValid = await this.passwordPolicy.validateCurrentPassword(currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        throw new BadRequestException('当前密码错误');
      }

      // 哈希新密码
      const hashedNewPassword = await this.passwordPolicy.hashPassword(newPassword);

      // 更新密码
      await this.userService.update(userId, { password: hashedNewPassword });

      // 撤销所有令牌（强制重新登录）
      await this.tokenManagement.revokeAllUserTokens(userId);

      this.logger.log(`用户${userId}密码修改成功`);

      return { message: '密码修改成功，请重新登录' };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      
      this.logger.error(`修改密码失败: ${error.message}`, error.stack);
      throw new BadRequestException('修改密码失败');
    }
  }

  /**
   * 重置密码
   */
  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<{ message: string }> {
    const { email, phone, code, newPassword, confirmPassword } = resetPasswordDto;

    try {
      // 验证新密码一致性
      this.passwordPolicy.validatePasswordConfirmation(newPassword, confirmPassword);

      // 验证新密码复杂度
      const passwordValidation = this.passwordPolicy.validatePasswordComplexity(newPassword);
      if (!passwordValidation.isValid) {
        throw new BadRequestException(`新密码不符合安全要求: ${passwordValidation.errors.join(', ')}`);
      }

      // 验证验证码
      if (email) {
        await this.verificationCode.verifyCode(email, code, 'reset' as any, true);
      } else if (phone) {
        await this.verificationCode.verifyCode(phone, code, 'reset' as any, false);
      } else {
        throw new BadRequestException('请提供邮箱或手机号');
      }

      // 查找用户
      let user: any = null;
      if (email) {
        user = await this.userService.findByEmail(email);
      } else if (phone) {
        user = await this.userService.findByPhone(phone);
      }

      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      // 哈希新密码
      const hashedNewPassword = await this.passwordPolicy.hashPassword(newPassword);

      // 更新密码
      await this.userService.update(user.id, { password: hashedNewPassword });

      // 撤销所有令牌
      await this.tokenManagement.revokeAllUserTokens(user.id);

      this.logger.log(`用户${user.id}密码重置成功`);

      return { message: '密码重置成功，请重新登录' };
    } catch (error) {
      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }
      
      this.logger.error(`重置密码失败: ${error.message}`, error.stack);
      throw new BadRequestException('重置密码失败');
    }
  }

  /**
   * 生成用户名
   */
  private generateUsername(identifier: string): string {
    const timestamp = Date.now().toString().slice(-6);
    const prefix = identifier.includes('@') ? 'user' : 'mobile';
    return `${prefix}_${timestamp}`;
  }

  /**
   * 检查用户状态
   */
  private checkUserStatus(user: any): void {
    if (user.userStatus === UserStatus.INACTIVE) {
      throw new ForbiddenException('账户未激活');
    }

    if (user.userStatus === UserStatus.LOCKED) {
      if (user.lockedUntil && new Date() < user.lockedUntil) {
        const remainingTime = Math.ceil((user.lockedUntil.getTime() - Date.now()) / 60000);
        throw new ForbiddenException(`账户已被锁定，请${remainingTime}分钟后再试`);
      }
    }

    if (user.userStatus === UserStatus.BANNED) {
      throw new ForbiddenException('账户已被封禁');
    }
  }

  /**
   * 更新用户最后登录信息
   */
  private async updateLastLogin(userId: number, ip: string): Promise<void> {
    await this.userService.updateLastLogin(userId, ip);
  }

  /**
   * 增加登录失败计数
   */
  private async incrementLoginFailCount(userId: number): Promise<void> {
    await this.userService.incrementLoginFailCount(userId);
  }

  /**
   * 重置登录失败计数
   */
  private async resetLoginFailCount(userId: number): Promise<void> {
    await this.userService.resetLoginFailCount(userId);
  }
}
