import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToOne,
  JoinC<PERSON>um<PERSON>,
} from 'typeorm';
import { UserEntity } from './user.entity';
import { BaseEntity } from '../../../common/entities/base.entity';

/**
 * 个人实名认证实体
 */
@Entity('user_verification', {
  comment: '实名认证信息表',
})
export class UserVerificationEntity extends BaseEntity {
  @Column({ name: 'user_id', type: 'int', })
  userId: number;

  @Column({ name: 'real_name', type: 'varchar', length: 30, comment: '真实姓名' })
  realName: string;

  @Column({ name: 'id_card', type: 'varchar', length: 18, comment: '身份证号' })
  idCard: string;

  @Column({ name: 'id_card_front', type: 'text', comment: '身份证正面照片URL' })
  idCardFront: string;

  @Column({ name: 'id_card_back', type: 'text', comment: '身份证背面照片URL' })
  idCardBack: string;

  @Column({
    name: 'review_status',
    type: 'enum',
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending',
    comment: '审核状态',
  })
  reviewStatus: 'pending' | 'approved' | 'rejected';

  @Column({ name: 'review_remark', type: 'text', nullable: true, comment: '审核备注' })
  reviewRemark: string;

  @Column({ name: 'reviewed_at', type: 'datetime', nullable: true, comment: '审核时间' })
  reviewedAt: Date;

  @Column({ name: 'reviewer_id', type: 'int', nullable: true, comment: '审核人ID' })
  reviewerId: number;

  // 关联用户
  @OneToOne(() => UserEntity, user => user.verificationInfo)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;
}