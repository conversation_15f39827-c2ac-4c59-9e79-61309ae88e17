import { request } from '@/utils/request'
import type { Package, PackageDetail, PackageStats } from '@/types/package'

/**
 * 套餐包相关API
 */
export const packageApi = {
  /**
   * 获取套餐包列表
   * @param params 查询参数
   */
  getPackages(params?: {
    page?: number
    pageSize?: number
    category?: string
    status?: string
    priceRange?: [number, number]
    keyword?: string
  }): Promise<{
    data: Package[]
    total: number
    page: number
    pageSize: number
  }> {
    return request.get('/op/packages', { params })
  },

  /**
   * 获取套餐包详情
   * @param packageId 套餐包ID
   */
  getPackageDetail(packageId: string): Promise<PackageDetail> {
    return request.get(`/op/packages/${packageId}`)
  },

  /**
   * 获取推荐套餐包
   * @param limit 数量限制
   */
  getRecommendedPackages(limit?: number): Promise<Package[]> {
    return request.get('/op/packages/recommended', {
      params: { limit }
    })
  },

  /**
   * 获取热门套餐包
   * @param limit 数量限制
   */
  getPopularPackages(limit?: number): Promise<Package[]> {
    return request.get('/op/packages/popular', {
      params: { limit }
    })
  },

  /**
   * 搜索套餐包
   * @param keyword 关键词
   */
  searchPackages(keyword: string): Promise<Package[]> {
    return request.get('/op/packages/search', {
      params: { keyword }
    })
  },

  /**
   * 获取套餐包分类
   */
  getPackageCategories(): Promise<{
    id: string
    name: string
    description?: string
    packageCount: number
  }[]> {
    return request.get('/op/packages/categories')
  },

  /**
   * 获取套餐包统计信息
   * @param packageId 套餐包ID
   */
  getPackageStats(packageId: string): Promise<PackageStats> {
    return request.get(`/op/packages/${packageId}/stats`)
  },

  /**
   * 获取套餐包评价
   * @param packageId 套餐包ID
   * @param params 查询参数
   */
  getPackageReviews(packageId: string, params?: {
    page?: number
    pageSize?: number
    rating?: number
  }): Promise<{
    data: {
      id: string
      userId: string
      userName: string
      rating: number
      comment: string
      createdAt: string
    }[]
    total: number
    averageRating: number
    ratingDistribution: Record<string, number>
  }> {
    return request.get(`/op/packages/${packageId}/reviews`, { params })
  },

  /**
   * 添加套餐包评价
   * @param packageId 套餐包ID
   * @param data 评价数据
   */
  addPackageReview(packageId: string, data: {
    rating: number
    comment: string
  }): Promise<void> {
    return request.post(`/op/packages/${packageId}/reviews`, data)
  },

  /**
   * 收藏套餐包
   * @param packageId 套餐包ID
   */
  favoritePackage(packageId: string): Promise<void> {
    return request.post(`/op/packages/${packageId}/favorite`)
  },

  /**
   * 取消收藏套餐包
   * @param packageId 套餐包ID
   */
  unfavoritePackage(packageId: string): Promise<void> {
    return request.delete(`/op/packages/${packageId}/favorite`)
  },

  /**
   * 获取用户收藏的套餐包
   */
  getFavoritePackages(): Promise<Package[]> {
    return request.get('/op/packages/favorites')
  },

  /**
   * 比较套餐包
   * @param packageIds 套餐包ID列表
   */
  comparePackages(packageIds: string[]): Promise<{
    packages: PackageDetail[]
    comparison: {
      feature: string
      values: (string | number | boolean)[]
    }[]
  }> {
    return request.post('/op/packages/compare', { packageIds })
  }
}