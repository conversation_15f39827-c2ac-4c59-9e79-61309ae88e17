import {
  Controller,
  Post,
  Body,
  Get,
  Req,
  Headers,
  Query,
  Patch,
  BadRequestException,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiBearerAuth, ApiQuery, ApiParam } from '@nestjs/swagger';
import { Request } from 'express';
import { AuthService } from './services/auth.service';
import { OAuthService } from './services/oauth.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { SendEmailCodeDto } from './dto/captcha/send-email-code.dto';
import { SendSmsCodeDto } from './dto/captcha/send-sms-code.dto';
import { VerifyCodeDto } from './dto/verify-code.dto';
import { WechatLoginDto } from './dto/oauth/wechat-login.dto';
import { AlipayLoginDto } from './dto/oauth/alipay-login.dto';
import { GithubLoginDto } from './dto/oauth/github-login.dto';
import { GoogleLoginDto } from './dto/oauth/google-login.dto';
import { OAuthBindDto } from './dto/oauth/oauth-bind.dto';
import { OAuthUnbindDto } from './dto/oauth/oauth-unbind.dto';
import { Public } from '@/common/decorators/public.decorator';
import { AuthPermission } from '@/common/decorators/auth-permission.decorator';
import { UseAuthStrategy, AuthStrategy } from '@/common/decorators/auth-strategy.decorator';
import { ApiResult } from '@/common/decorators/api-result.decorator';
import { AuthUser } from '@/common/types/auth.types';

// 扩展 Express Request 类型，包含用户信息
interface RequestWithUser extends Request {
  user: AuthUser;
}

@ApiTags('认证')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly oauthService: OAuthService,
  ) {}

  @Public()
  @Post('register')
  @ApiOperation({ summary: '用户注册' })
  @ApiBody({ type: RegisterDto })
  @ApiResult()
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @Public()
  @Post('login')
  @ApiOperation({ summary: '用户登录' })
  @ApiBody({ type: LoginDto })
  @ApiResult()
  async login(@Body() loginDto: LoginDto, @Req() req: Request) {
    return this.authService.login(loginDto, req);
  }

  @Public()
  @Post('refresh')
  @ApiOperation({ summary: '刷新令牌' })
  @ApiBody({ type: RefreshTokenDto })
  @ApiResult()
  async refresh(@Body() refreshTokenDto: RefreshTokenDto) {
    return this.authService.refreshToken(refreshTokenDto);
  }

  @Post('logout')
  @ApiOperation({ summary: '用户登出' })
  @ApiBearerAuth()
  @ApiResult()
  async logout(@Headers('authorization') authHeader: string, @Body() body: { refreshToken?: string } = {}) {
    if (!authHeader) {
      throw new BadRequestException('令牌不能为空');
    }

    const token = authHeader.replace('Bearer ', '');
    if (!token) {
      throw new BadRequestException('令牌格式错误');
    }

    // refreshToken 是可选的，如果没有提供则只撤销访问令牌
    const refreshToken = body.refreshToken || '';

    return this.authService.logout(token, refreshToken);
  }

  @Public()
  @Post('send-email-code')
  @ApiOperation({ summary: '发送邮箱验证码' })
  @ApiBody({ type: SendEmailCodeDto })
  @ApiResult()
  async sendEmailCode(@Body() dto: SendEmailCodeDto) {
    return this.authService.sendEmailCode(dto);
  }

  @Public()
  @Post('send-sms-code')
  @HttpCode(200)
  @ApiOperation({ summary: '发送短信验证码' })
  @ApiBody({ type: SendSmsCodeDto })
  @ApiResult()
  async sendSmsCode(@Body() dto: SendSmsCodeDto) {
    return this.authService.sendSmsCode(dto);
  }

  @Public()
  @Post('verify-code')
  @ApiOperation({ summary: '验证验证码' })
  @ApiBody({ type: VerifyCodeDto })
  @ApiResult()
  async verifyCode(@Body() dto: VerifyCodeDto) {
    return this.authService.verifyCode(dto);
  }

  @Public()
  @Post('slide/verify')
  @ApiOperation({ summary: '验证滑动拼图验证码' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        moveDistance: { type: 'number', description: '移动距离' },
        duration: { type: 'number', description: '移动耗时(毫秒)' },
        deviceFingerprint: { type: 'object', description: '设备指纹' },
        behaviorPattern: { type: 'object', description: '行为模式' },
        userAgent: { type: 'string', description: '用户代理' },
        screenResolution: { type: 'string', description: '屏幕分辨率' },
        timezone: { type: 'string', description: '时区' },
        language: { type: 'string', description: '语言' },
        timestamp: { type: 'number', description: '时间戳' },
        sessionId: { type: 'string', description: '会话ID' },
        clientIP: { type: 'string', description: '客户端IP' },
        browserFingerprint: { type: 'string', description: '浏览器指纹' },
        mouseTrajectory: { type: 'array', description: '鼠标轨迹' },
        keyboardPattern: { type: 'array', description: '键盘模式' },
        touchPattern: { type: 'array', description: '触摸模式' }
      },
      required: ['moveDistance', 'duration']
    }
  })
  @ApiResult()
  async verifySlideVerify(@Body() verifySlideVerifyDto: any) {
    const {
      moveDistance,
      duration,
      deviceFingerprint,
      behaviorPattern,
      userAgent,
      screenResolution,
      timezone,
      language,
      timestamp,
      sessionId,
      clientIP,
      browserFingerprint,
      mouseTrajectory,
      keyboardPattern,
      touchPattern,
    } = verifySlideVerifyDto;

    const securityData = {
      deviceFingerprint,
      behaviorPattern,
      userAgent,
      screenResolution,
      timezone,
      language,
      timestamp,
      sessionId,
      clientIP,
      browserFingerprint,
      mouseTrajectory,
      keyboardPattern,
      touchPattern,
    };

    return this.authService.verifySlideVerify(moveDistance, duration, securityData);
  }

  @Public()
  @Post('reset-password')
  @ApiOperation({ summary: '重置密码' })
  @ApiBody({ type: ResetPasswordDto })
  @ApiResult()
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(resetPasswordDto);
  }

  @Patch('change-password')
  @ApiOperation({ summary: '修改密码' })
  @ApiBearerAuth()
  @ApiBody({ type: ChangePasswordDto })
  @ApiResult()
  async changePassword(@Req() req: RequestWithUser, @Body() changePasswordDto: ChangePasswordDto) {
    return this.authService.changePassword(req.user.id, changePasswordDto);
  }

  @Get('profile')
  @ApiOperation({ summary: '获取用户信息' })
  @ApiBearerAuth()
  @ApiResult()
  async getProfile(@Req() req: RequestWithUser) {
    return req.user;
  }

  // 第三方登录相关接口
  @Public()
  @Post('wechat/login')
  @ApiOperation({ summary: '微信登录' })
  @ApiBody({ type: WechatLoginDto })
  @ApiResult()
  async wechatLogin(@Body() dto: WechatLoginDto) {
    return this.oauthService.wechatLogin(dto);
  }

  @Public()
  @Post('alipay/login')
  @ApiOperation({ summary: '支付宝登录' })
  @ApiBody({ type: AlipayLoginDto })
  @ApiResult()
  async alipayLogin(@Body() dto: AlipayLoginDto) {
    return this.oauthService.alipayLogin(dto);
  }

  @Public()
  @Post('github/login')
  @ApiOperation({ summary: 'GitHub登录' })
  @ApiBody({ type: GithubLoginDto })
  @ApiResult()
  async githubLogin(@Body() dto: GithubLoginDto) {
    return this.oauthService.githubLogin(dto);
  }

  @Public()
  @Post('google/login')
  @ApiOperation({ summary: 'Google登录' })
  @ApiBody({ type: GoogleLoginDto })
  @ApiResult()
  async googleLogin(@Body() dto: GoogleLoginDto) {
    return this.oauthService.googleLogin(dto);
  }

  @Post('oauth/bind')
  @ApiOperation({ summary: '绑定第三方账号' })
  @ApiBearerAuth()
  @ApiBody({ type: OAuthBindDto })
  @ApiResult()
  async bindOAuthAccount(@Req() req: RequestWithUser, @Body() bindDto: OAuthBindDto) {
    return this.oauthService.bindOAuthAccount(req.user.id, bindDto);
  }

  @Post('oauth/unbind')
  @ApiOperation({ summary: '解绑第三方账号' })
  @ApiBearerAuth()
  @ApiBody({ type: OAuthUnbindDto })
  @ApiResult()
  async unbindOAuthAccount(@Req() req: RequestWithUser, @Body() unbindDto: OAuthUnbindDto) {
    return this.oauthService.unbindOAuthAccount(req.user.id, unbindDto);
  }

  @Get('oauth/accounts')
  @ApiOperation({ summary: '获取已绑定的第三方账号列表' })
  @ApiBearerAuth()
  @ApiResult()
  async getOAuthAccounts(@Req() req: RequestWithUser) {
    return this.oauthService.getUserOAuthAccounts(req.user.id);
  }
}
