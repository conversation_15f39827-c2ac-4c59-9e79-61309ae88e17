import { Controller, Get, Post, Delete, Query, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery, ApiBody, ApiResponse } from '@nestjs/swagger';
import { QueueManagerService } from '../services/queue-manager.service';
import { QueueStatusDto, AllQueuesStatusDto } from '../dto/queue-status.dto';
import { StructuredLogger } from '../../../common/logging/structured-logger';
import { AuthPermission } from '../../../common/decorators/auth-permission.decorator';

/**
 * 队列管理控制器
 * 提供队列状态查询和管理功能（仅内部使用）
 */
@ApiTags('队列管理')
@Controller('queues')
export class QueueAdminController {
  constructor(
    private queueManagerService: QueueManagerService,
    private logger: StructuredLogger,
  ) {}

  /**
   * 获取队列状态
   */
  @Get('status')
  @AuthPermission('queue.view')
  @ApiOperation({
    summary: '获取队列状态',
    description: '返回指定队列或所有队列的状态信息',
  })
  @ApiQuery({
    name: 'name',
    description: '队列名称（可选）',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: '操作成功',
    type: AllQueuesStatusDto,
  })
  async getQueueStatus(@Query('name') queueName?: string): Promise<any> {
    const status = await this.queueManagerService.getQueueStatus(queueName);
    
    this.logger.debug(
      `查询队列状态${queueName ? queueName : '所有队列'}`,
      { module: 'QueueAdminController' }
    );
    
    return {
      code: 200,
      message: '操作成功',
      data: status,
    };
  }

  /**
   * 暂停队列
   */
  @Post('pause')
  @AuthPermission('queue.manage')
  @ApiOperation({
    summary: '暂停队列',
    description: '暂停指定的队列，不再处理新任务',
  })
  @ApiQuery({
    name: 'name',
    description: '队列名称',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: '操作成功',
  })
  async pauseQueue(@Query('name') queueName: string): Promise<any> {
    await this.queueManagerService.pauseQueue(queueName);
    
    this.logger.log(
      `暂停队列${queueName}`,
      { module: 'QueueAdminController' }
    );
    
    return {
      code: 200,
      message: `队列${queueName}已暂停`,
    };
  }

  /**
   * 恢复队列
   */
  @Post('resume')
  @AuthPermission('queue.manage')
  @ApiOperation({
    summary: '恢复队列',
    description: '恢复指定的队列，继续处理任务',
  })
  @ApiQuery({
    name: 'name',
    description: '队列名称',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: '操作成功',
  })
  async resumeQueue(@Query('name') queueName: string): Promise<any> {
    await this.queueManagerService.resumeQueue(queueName);
    
    this.logger.log(
      `恢复队列${queueName}`,
      { module: 'QueueAdminController' }
    );
    
    return {
      code: 200,
      message: `队列${queueName}已恢复`,
    };
  }

  /**
   * 清空队列
   */
  @Delete('clean')
  @AuthPermission('queue.manage')
  @ApiOperation({
    summary: '清空队列',
    description: '清空指定队列的已完成和失败的任务',
  })
  @ApiQuery({
    name: 'name',
    description: '队列名称',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: '操作成功',
  })
  async cleanQueue(@Query('name') queueName: string): Promise<any> {
    await this.queueManagerService.cleanQueue(queueName);
    
    this.logger.log(
      `清空队列${queueName}`,
      { module: 'QueueAdminController' }
    );
    
    return {
      code: 200,
      message: `队列${queueName}已清空已完成和失败的任务`,
    };
  }
} 