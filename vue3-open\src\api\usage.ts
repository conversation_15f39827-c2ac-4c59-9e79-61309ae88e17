import { request } from '@/utils/request'
import type {
  UsageStats,
  UsageRecord,
  UsageTrend,
  ServiceUsageDistribution,
  TimeRange,
} from '@/types/usage'

// 使用统计API接口
export const usageApi = {
  // 获取使用记录列表
  getUsageRecords: (params?: {
    page?: number
    pageSize?: number
    userId?: string
    serviceId?: string
  }) => {
    return request.get<{
      data: Record<string, any>
      total: number
      page: number
      limit: number
    }>('user-service', {
      params,
    })
  },
}
