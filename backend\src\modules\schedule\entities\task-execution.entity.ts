import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 任务执行状态枚举
 */
export enum TaskExecutionStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  SUCCESS = 'success',
  FAILED = 'failed',
  RETRYING = 'retrying',
}

/**
 * 任务执行记录实体
 * 用于记录定时任务的执行历史和状态
 */
@Entity('task_executions')
export class TaskExecution {
  @ApiProperty({ description: '主键ID' })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: '任务名称' })
  @Column({ length: 100, comment: '任务名称' })
  taskName: string;

  @ApiProperty({ description: '任务类型' })
  @Column({ length: 50, comment: '任务类型' })
  taskType: string;

  @ApiProperty({ description: '执行状态', enum: TaskExecutionStatus })
  @Column({
    type: 'enum',
    enum: TaskExecutionStatus,
    default: TaskExecutionStatus.PENDING,
    comment: '执行状态'
  })
  status: TaskExecutionStatus;

  @ApiProperty({ description: '开始时间' })
  @Column({ type: 'datetime', nullable: true, comment: '开始时间' })
  startTime: Date | null;

  @ApiProperty({ description: '结束时间' })
  @Column({ type: 'datetime', nullable: true, comment: '结束时间' })
  endTime: Date | null;

  @ApiProperty({ description: '执行耗时(毫秒)' })
  @Column({ type: 'int', nullable: true, comment: '执行耗时(毫秒)' })
  duration: number | null;

  @ApiProperty({ description: '重试次数' })
  @Column({ type: 'int', default: 0, comment: '重试次数' })
  retryCount: number;

  @ApiProperty({ description: '最大重试次数' })
  @Column({ type: 'int', default: 3, comment: '最大重试次数' })
  maxRetries: number;

  @ApiProperty({ description: '执行结果' })
  @Column({ type: 'text', nullable: true, comment: '执行结果' })
  result: string | null;

  @ApiProperty({ description: '错误信息' })
  @Column({ type: 'text', nullable: true, comment: '错误信息' })
  errorMessage: string | null;

  @ApiProperty({ description: '错误堆栈' })
  @Column({ type: 'text', nullable: true, comment: '错误堆栈' })
  errorStack: string | null;

  @ApiProperty({ description: '执行参数' })
  @Column({ type: 'json', nullable: true, comment: '执行参数' })
  parameters: any;

  @ApiProperty({ description: '下次重试时间' })
  @Column({ type: 'datetime', nullable: true, comment: '下次重试时间' })
  nextRetryTime: Date | null;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  /**
   * 计算执行耗时
   */
  calculateDuration(): void {
    if (this.startTime && this.endTime) {
      this.duration = this.endTime.getTime() - this.startTime.getTime();
    }
  }

  /**
   * 检查是否可以重试
   */
  canRetry(): boolean {
    return this.retryCount < this.maxRetries && this.status === TaskExecutionStatus.FAILED;
  }

  /**
   * 增加重试次数
   */
  incrementRetryCount(): void {
    this.retryCount++;
  }

  /**
   * 设置下次重试时间（指数退避策略）
   */
  setNextRetryTime(): void {
    if (this.canRetry()) {
      const backoffMinutes = Math.pow(2, this.retryCount) * 5; // 5分钟、10分钟、20分钟
      this.nextRetryTime = new Date(Date.now() + backoffMinutes * 60 * 1000);
    }
  }
}
