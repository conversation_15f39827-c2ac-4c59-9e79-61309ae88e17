{"name": "vue3-open", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fingerprintjs/fingerprintjs": "^4.6.2", "@types/event-source-polyfill": "^1.0.5", "@types/markdown-it": "^14.1.2", "@vueuse/core": "^13.3.0", "axios": "^1.9.0", "cropperjs": "^2.0.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.11", "event-source-polyfill": "^1.0.31", "highlight.js": "^11.11.1", "markdown-it": "^14.1.0", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue3-slide-verify": "^1.1.7"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/crypto-js": "^4.2.2", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}