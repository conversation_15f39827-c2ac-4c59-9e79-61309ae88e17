import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { UserServiceService } from '../user-service.service';
import { ServiceService } from '../../service/service.service';
import { UserEntity } from '../../user/entities/user.entity';
import { ServiceStatus } from '../../service/enums/service.enum';

/**
 * 用户服务事件监听器
 * 处理用户创建时自动分配免费服务次数
 */
@Injectable()
export class UserServiceListener {
  private readonly logger = new Logger(UserServiceListener.name);

  constructor(
    private readonly userServiceService: UserServiceService,
    private readonly serviceService: ServiceService,
  ) {}

  /**
   * 监听用户创建事件，为新用户自动创建免费服务记录
   */
  @OnEvent('user.created')
  async handleUserCreated(user: UserEntity): Promise<void> {
    try {
      this.logger.log(`处理用户创建事件: 用户ID=${user.id}, 用户名=${user.username}`);

      // 检查用户是否有资格获取免费额度
      if (!user.isFreeQuotaEligible) {
        this.logger.log(`用户 ${user.id} 不符合免费额度资格，跳过创建免费服务记录`);
        return;
      }

      // 获取所有活跃服务
      const services = await this.serviceService.findAll({
        status: ServiceStatus.ACTIVE,
      });

      if (!services || services.length === 0) {
        this.logger.warn('没有找到活跃的服务，跳过创建免费服务记录');
        return;
      }

      this.logger.log(`找到 ${services.length} 个活跃服务，开始为用户创建免费记录`);

      // 根据需求文档，新用户默认每日免费调用次数：5次
      const freeCount = 5;
      let successCount = 0;
      let skipCount = 0;

      for (const service of services) {
        try {
          // 检查是否已存在记录
          const existing = await this.userServiceService.findByUserAndService(user.id, service.id);
          if (existing) {
            this.logger.debug(`用户 ${user.id} 的服务 ${service.id} 记录已存在，跳过`);
            skipCount++;
            continue;
          }
        } catch (error) {
          // 如果记录不存在，会抛出NotFoundException，这是正常的
          if (!error.message.includes('不存在')) {
            this.logger.error(`检查用户服务记录时出错: ${error.message}`);
            continue;
          }
        }

        try {
          // 创建免费服务记录
          await this.userServiceService.addServiceCount(
            user.id,
            service.id,
            freeCount,
            'new_user_free_quota'
          );

          this.logger.debug(`✅ 为用户 ${user.id} 创建服务 ${service.name} 的免费记录，次数: ${freeCount}`);
          successCount++;
        } catch (error) {
          this.logger.error(`为用户 ${user.id} 创建服务 ${service.id} 免费记录失败: ${error.message}`);
        }
      }

      this.logger.log(
        `用户 ${user.id} 免费服务记录创建完成: 成功=${successCount}, 跳过=${skipCount}, 总计=${services.length}`
      );

    } catch (error) {
      this.logger.error(`处理用户创建事件失败: 用户ID=${user.id}, 错误=${error.message}`, error.stack);
      // 不抛出异常，避免影响用户注册流程
    }
  }

  /**
   * 监听用户验证状态变更事件，为实名认证用户增加奖励次数
   */
  @OnEvent('user.verification.approved')
  async handleUserVerificationApproved(event: { userId: number; verificationType: 'personal' | 'enterprise' }): Promise<void> {
    try {
      const { userId, verificationType } = event;
      this.logger.log(`处理用户实名认证通过事件: 用户ID=${userId}, 认证类型=${verificationType}`);

      // 根据需求文档，实名认证奖励次数
      const rewardCount = verificationType === 'personal' ? 10 : 20;

      // 获取所有活跃服务
      const services = await this.serviceService.findAll({
        status: ServiceStatus.ACTIVE,
      });

      if (!services || services.length === 0) {
        this.logger.warn('没有找到活跃的服务，跳过发放认证奖励');
        return;
      }

      let successCount = 0;

      for (const service of services) {
        try {
          await this.userServiceService.addServiceCount(
            userId,
            service.id,
            rewardCount,
            `${verificationType}_verification_reward`
          );

          this.logger.debug(`✅ 为用户 ${userId} 的服务 ${service.name} 增加认证奖励次数: ${rewardCount}`);
          successCount++;
        } catch (error) {
          this.logger.error(`为用户 ${userId} 的服务 ${service.id} 增加认证奖励失败: ${error.message}`);
        }
      }

      this.logger.log(
        `用户 ${userId} ${verificationType}认证奖励发放完成: 成功=${successCount}, 每服务奖励=${rewardCount}次`
      );

    } catch (error) {
      this.logger.error(`处理用户认证奖励事件失败: ${error.message}`, error.stack);
    }
  }
}
