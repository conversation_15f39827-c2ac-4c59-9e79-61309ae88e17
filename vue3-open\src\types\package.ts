// 套餐包状态
export type PackageStatus = 'active' | 'inactive' | 'expired' | 'sold_out'

// 套餐包类型
export type PackageType = 'basic' | 'standard' | 'premium' | 'enterprise' | 'custom'

// 计费方式
export type BillingType = 'one_time' | 'monthly' | 'yearly' | 'usage_based'

// 套餐包特性
export interface PackageFeature {
  id: string
  name: string
  description?: string
  included: boolean
  limit?: number
  unit?: string
  unlimited?: boolean
}

// 套餐包服务配额
export interface ServiceQuota {
  serviceId: string
  serviceName: string
  quota: number // -1 表示无限制
  limit?: number // 限制数量
  unit: string
  description?: string
  unlimited?: boolean // 是否无限制
}

// 套餐包信息
export interface Package {
  id: string
  name: string
  description: string
  type: PackageType
  status: PackageStatus
  billingType: BillingType
  price: number // 分为单位
  originalPrice?: number // 原价
  currency: string
  duration?: number // 有效期（天）
  validDays?: number // 有效期天数
  features: PackageFeature[]
  quotas: ServiceQuota[]
  serviceQuotas: ServiceQuota[] // 服务配额（别名）
  maxUsers?: number
  supportLevel: string
  isPopular?: boolean
  isRecommended?: boolean
  tags: string[]
  thumbnail?: string
  images?: string[]
  createdAt: string
  updatedAt: string
  validFrom?: string
  validTo?: string
  salesCount: number
  remainingCount?: number // 剩余数量，-1表示无限制
  discount?: number // 折扣
  rating?: number // 评分
}

// 用户套餐包
export interface UserPackage {
  id: string
  userId: string
  packageId: string
  package: Package
  status: 'active' | 'expired' | 'cancelled'
  purchasedAt: string
  activatedAt?: string
  expiredAt?: string
  cancelledAt?: string
  remainingQuotas: Record<string, number> // serviceId -> remaining quota
  usedQuotas: Record<string, number> // serviceId -> used quota
  autoRenew: boolean
  renewalPrice?: number
}

// 套餐包详情（扩展套餐包信息）
export interface PackageDetail extends Package {
  reviews?: {
    id: string
    userId: string
    userName: string
    rating: number
    comment: string
    createdAt: string
  }[]
  averageRating?: number
  totalReviews?: number
  ratingDistribution?: Record<string, number>
  relatedPackages?: Package[]
  faq?: {
    question: string
    answer: string
  }[]
  changelog?: {
    version: string
    changes: string[]
    date: string
  }[]
}

// 套餐包统计
export interface PackageStats {
  totalPackages: number
  activePackages: number
  soldPackages: number
  totalRevenue: number
  monthlyRevenue: number
  popularPackages: Package[]
  recentSales: UserPackage[]
}

// 套餐包查询参数
export interface PackageQueryParams {
  page?: number
  pageSize?: number
  type?: PackageType
  status?: PackageStatus
  billingType?: BillingType
  priceMin?: number
  priceMax?: number
  keyword?: string
  tags?: string[]
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 创建套餐包表单
export interface CreatePackageForm {
  name: string
  description: string
  type: PackageType
  billingType: BillingType
  price: number
  originalPrice?: number
  duration?: number
  features: Omit<PackageFeature, 'id'>[]
  quotas: ServiceQuota[]
  maxUsers?: number
  supportLevel: string
  tags: string[]
  thumbnail?: string
  images?: string[]
  validFrom?: string
  validTo?: string
  remainingCount?: number
}

// 购买套餐包表单
export interface PurchasePackageForm {
  packageId: string
  quantity?: number
  paymentMethod: string
  autoRenew?: boolean
  couponCode?: string
}

// 套餐包推荐
export interface PackageRecommendation {
  packageId: string
  package: Package
  score: number
  reasons: string[]
  matchedFeatures: string[]
}

// 套餐包比较
export interface PackageComparison {
  packages: Package[]
  features: {
    name: string
    values: (string | number | boolean)[]
  }[]
}

// 套餐包使用情况
export interface PackageUsage {
  packageId: string
  serviceName: string
  totalQuota: number
  usedQuota: number
  remainingQuota: number
  usagePercentage: number
  lastUsedAt?: string
}