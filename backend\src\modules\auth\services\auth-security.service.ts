import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '@/shared/redis.service';
import { getClientIp, isPrivateIp } from '@/common/utils/ip.utils';
import * as bcrypt from 'bcrypt';

/**
 * 认证安全增强服务
 * 提供密码策略验证、IP限制、设备指纹等安全功能
 */
@Injectable()
export class AuthSecurityService {
  private readonly logger = new Logger(AuthSecurityService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * 验证密码复杂度
   */
  validatePasswordPolicy(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    const minLength = this.configService.get<number>('auth.passwordPolicy.minLength', 8);
    const requireLowercase = this.configService.get<boolean>('auth.passwordPolicy.requireLowercase', true);
    const requireUppercase = this.configService.get<boolean>('auth.passwordPolicy.requireUppercase', true);
    const requireNumbers = this.configService.get<boolean>('auth.passwordPolicy.requireNumbers', true);
    const requireSpecialChars = this.configService.get<boolean>('auth.passwordPolicy.requireSpecialChars', true);

    // 长度检查
    if (password.length < minLength) {
      errors.push(`密码长度至少需要${minLength}位`);
    }

    // 小写字母检查
    if (requireLowercase && !/[a-z]/.test(password)) {
      errors.push('密码必须包含小写字母');
    }

    // 大写字母检查
    if (requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('密码必须包含大写字母');
    }

    // 数字检查
    if (requireNumbers && !/\d/.test(password)) {
      errors.push('密码必须包含数字');
    }

    // 特殊字符检查
    if (requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('密码必须包含特殊字符');
    }

    // 常见弱密码检查
    const weakPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', '12345678', '111111', '123123', 'admin'
    ];
    
    if (weakPasswords.includes(password.toLowerCase())) {
      errors.push('密码过于简单，请使用更复杂的密码');
    }

    // 连续字符检查
    if (this.hasConsecutiveChars(password)) {
      errors.push('密码不能包含连续的字符或数字');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 检查是否有连续字符
   */
  private hasConsecutiveChars(password: string): boolean {
    for (let i = 0; i < password.length - 2; i++) {
      const char1 = password.charCodeAt(i);
      const char2 = password.charCodeAt(i + 1);
      const char3 = password.charCodeAt(i + 2);
      
      // 检查连续递增或递减
      if ((char2 === char1 + 1 && char3 === char2 + 1) ||
          (char2 === char1 - 1 && char3 === char2 - 1)) {
        return true;
      }
    }
    return false;
  }

  /**
   * IP地址安全检查
   */
  async checkIpSecurity(request: any, identifier: string): Promise<{
    isAllowed: boolean;
    reason?: string;
    riskLevel: 'low' | 'medium' | 'high';
  }> {
    const ip = getClientIp(request);
    
    // 检查IP是否在黑名单中
    const isBlacklisted = await this.isIpBlacklisted(ip);
    if (isBlacklisted) {
      return {
        isAllowed: false,
        reason: 'IP地址已被列入黑名单',
        riskLevel: 'high',
      };
    }

    // 检查IP频率限制
    const isRateLimited = await this.checkIpRateLimit(ip);
    if (isRateLimited) {
      return {
        isAllowed: false,
        reason: 'IP地址请求过于频繁',
        riskLevel: 'medium',
      };
    }

    // 检查地理位置异常
    const riskLevel = await this.assessIpRisk(ip, identifier);
    
    return {
      isAllowed: true,
      riskLevel,
    };
  }

  /**
   * 检查IP是否在黑名单中
   */
  private async isIpBlacklisted(ip: string): Promise<boolean> {
    try {
      const blacklistKey = `ip_blacklist:${ip}`;
      return await this.redisService.exists(blacklistKey);
    } catch (error) {
      this.logger.error(`检查IP黑名单失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 检查IP频率限制
   */
  private async checkIpRateLimit(ip: string): Promise<boolean> {
    try {
      const rateLimitKey = `ip_rate_limit:${ip}`;
      const maxRequests = this.configService.get<number>('auth.security.ipRateLimit.maxRequests', 100);
      const windowSeconds = this.configService.get<number>('auth.security.ipRateLimit.windowSeconds', 3600);
      
      const currentCount = await this.redisService.get(rateLimitKey);
      
      if (!currentCount) {
        await this.redisService.set(rateLimitKey, '1', windowSeconds);
        return false;
      }
      
      const count = parseInt(currentCount, 10);
      if (count >= maxRequests) {
        return true;
      }
      
      await this.redisService.incr(rateLimitKey);
      return false;
    } catch (error) {
      this.logger.error(`检查IP频率限制失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 评估IP风险等级
   */
  private async assessIpRisk(ip: string, identifier: string): Promise<'low' | 'medium' | 'high'> {
    try {
      // 内网IP风险较低
      if (isPrivateIp(ip)) {
        return 'low';
      }

      // 检查该IP是否有多个账户登录
      const ipAccountsKey = `ip_accounts:${ip}`;
      const accounts = await this.redisService.smembers(ipAccountsKey);
      
      if (accounts.length > 5) {
        return 'high';
      } else if (accounts.length > 2) {
        return 'medium';
      }

      // 记录当前账户使用此IP
      await this.redisService.sadd(ipAccountsKey, identifier);
      await this.redisService.expire(ipAccountsKey, 86400); // 24小时过期

      return 'low';
    } catch (error) {
      this.logger.error(`评估IP风险失败: ${error.message}`);
      return 'medium';
    }
  }

  /**
   * 设备指纹检查
   */
  async checkDeviceFingerprint(request: any, userId: number): Promise<{
    isNewDevice: boolean;
    deviceId: string;
    riskLevel: 'low' | 'medium' | 'high';
  }> {
    const userAgent = request.headers?.['user-agent'] || '';
    const acceptLanguage = request.headers?.['accept-language'] || '';
    const acceptEncoding = request.headers?.['accept-encoding'] || '';
    
    // 生成设备指纹
    const deviceFingerprint = this.generateDeviceFingerprint({
      userAgent,
      acceptLanguage,
      acceptEncoding,
      ip: getClientIp(request),
    });

    // 检查是否为已知设备
    const userDevicesKey = `user_devices:${userId}`;
    const knownDevices = await this.redisService.smembers(userDevicesKey);
    
    const isNewDevice = !knownDevices.includes(deviceFingerprint);
    
    if (isNewDevice) {
      // 记录新设备
      await this.redisService.sadd(userDevicesKey, deviceFingerprint);
      await this.redisService.expire(userDevicesKey, 86400 * 30); // 30天过期
      
      this.logger.warn(`检测到新设备登录: 用户${userId}, 设备指纹${deviceFingerprint}`);
    }

    // 评估风险等级
    let riskLevel: 'low' | 'medium' | 'high' = 'low';
    
    if (isNewDevice) {
      riskLevel = 'medium';
      
      // 如果用户代理异常，提高风险等级
      if (this.isAbnormalUserAgent(userAgent)) {
        riskLevel = 'high';
      }
    }

    return {
      isNewDevice,
      deviceId: deviceFingerprint,
      riskLevel,
    };
  }

  /**
   * 生成设备指纹
   */
  private generateDeviceFingerprint(data: {
    userAgent: string;
    acceptLanguage: string;
    acceptEncoding: string;
    ip: string;
  }): string {
    const { userAgent, acceptLanguage, acceptEncoding, ip } = data;
    const fingerprint = `${userAgent}|${acceptLanguage}|${acceptEncoding}|${ip}`;
    
    // 使用简单的哈希算法生成指纹
    return bcrypt.hashSync(fingerprint, 5).substring(0, 16);
  }

  /**
   * 检查是否为异常用户代理
   */
  private isAbnormalUserAgent(userAgent: string): boolean {
    // 检查是否为空或过短
    if (!userAgent || userAgent.length < 10) {
      return true;
    }

    // 检查是否包含常见的爬虫标识
    const botPatterns = [
      /bot/i, /crawler/i, /spider/i, /scraper/i,
      /curl/i, /wget/i, /python/i, /java/i,
    ];

    return botPatterns.some(pattern => pattern.test(userAgent));
  }

  /**
   * 记录安全事件
   */
  async recordSecurityEvent(eventType: string, data: any): Promise<void> {
    try {
      const securityEvent = {
        type: eventType,
        timestamp: new Date().toISOString(),
        data,
      };

      const eventKey = `security_events:${eventType}`;
      await this.redisService.lpush(eventKey, JSON.stringify(securityEvent));
      await this.redisService.ltrim(eventKey, 0, 999); // 保留最近1000条记录
      await this.redisService.expire(eventKey, 86400 * 7); // 7天过期

      this.logger.warn(`安全事件记录: ${eventType}`, securityEvent);
    } catch (error) {
      this.logger.error(`记录安全事件失败: ${error.message}`);
    }
  }

  /**
   * 生成安全的随机密码
   */
  generateSecurePassword(length: number = 12): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    const allChars = lowercase + uppercase + numbers + symbols;
    
    let password = '';
    
    // 确保包含每种类型的字符
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];
    
    // 填充剩余长度
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }
    
    // 打乱字符顺序
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }
}
