import { Entity, Column, BeforeInsert } from 'typeorm';
import { BaseEntity } from '@/common/entities/base.entity';
import * as bcrypt from 'bcrypt';

// 用户类型枚举
export enum UserType {
  INDIVIDUAL = 'individual',
  ENTERPRISE = 'enterprise'
}

// 用户认证状态枚举
export enum UserVerificationStatus {
  UNVERIFIED = 'unverified',
  PERSONAL_VERIFIED = 'personal_verified',
  ENTERPRISE_VERIFIED = 'enterprise_verified',
  VERIFICATION_REJECTED = 'verification_rejected',
  VERIFICATION_PENDING = 'verification_pending'
}

// 用户状态枚举
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  LOCKED = 'locked',
  BANNED = 'banned'
}

// 用户角色枚举
export enum RoleEnum {
  ADMIN = 'admin',
  USER = 'user'
}

// 用户等级枚举
export enum TierEnum {
  BASIC = 'basic',
  PREMIUM = 'premium',
  VIP = 'vip',
  ENTERPRISE = 'enterprise'
}

// 第三方平台类型枚举
export enum ThirdPartyType {
  WECHAT = 'wechat',
  ALIPAY = 'alipay',
  GITHUB = 'github',
  GOOGLE = 'google',
}

@Entity('open_user')
export class UserEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 100, unique: true, comment: '用户名' })
  username: string;

  @Column({ type: 'varchar', length: 200, unique: true, nullable: true, comment: '邮箱' })
  email: string;

  @Column({ type: 'varchar', length: 255, comment: '密码哈希' })
  password: string;

  @Column({ type: 'varchar', length: 30, nullable: false, comment: '用户昵称' })
  nickname: string;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: '真实姓名' })
  realName?: string;

  @Column({ type: 'varchar', length: 20, nullable: true, comment: '手机号' })
  phone?: string;

  @Column({ type: 'varchar', length: 500, nullable: true, comment: '头像URL' })
  avatar?: string;

  @Column({
    type: 'enum',
    enum: UserType,
    default: UserType.INDIVIDUAL,
    comment: '用户类型'
  })
  userType: UserType;

  @Column({
    type: 'enum',
    enum: UserVerificationStatus,
    default: UserVerificationStatus.UNVERIFIED,
    comment: '认证状态'
  })
  verificationStatus: UserVerificationStatus;
  
  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE,
    comment: '账户状态'
  })
  userStatus: UserStatus;

  @Column({
    type: 'enum',
    enum: RoleEnum,
    default: RoleEnum.USER,
    comment: '角色'
  })
  role: RoleEnum;

  @Column({ type: 'boolean', default: false, comment: '邮箱是否验证' })
  emailVerified: boolean;

  @Column({ type: 'boolean', default: false, comment: '手机是否验证' })
  phoneVerified: boolean;

  @Column({ type: 'timestamp', nullable: true, comment: '最后登录时间' })
  lastLoginAt?: Date;

  @Column({ type: 'varchar', length: 45, nullable: true, comment: '最后登录IP' })
  lastLoginIp?: string;

  @Column({ type: 'int', default: 0, comment: '登录失败次数' })
  loginFailCount: number;

  @Column({ type: 'timestamp', nullable: true, comment: '账户锁定截止时间' })
  lockedUntil?: Date;

  @Column({ type: 'timestamp', nullable: true, comment: '每日免费使用次数重置日期' })
  lastDailyResetDate?: Date;

  @Column({
    type: 'enum',
    enum: TierEnum,
    default: TierEnum.BASIC,
    comment: '用户等级'
  })
  tier: TierEnum;

  @Column({ default: true, comment: '是否有资格获取免费额度' })
  isFreeQuotaEligible: boolean;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    comment: '账户余额'
  })
  balance: number;

  // 第三方账号关联字段
  @Column({ type: 'varchar', length: 100, nullable: true, comment: '微信OpenID' })
  wechatOpenid?: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '微信UnionID' })
  wechatUnionid?: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '支付宝用户ID' })
  alipayUserId?: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: 'GitHub用户ID' })
  githubId?: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: 'Google用户ID' })
  googleId?: string;

  @BeforeInsert()
  async hashPassword() {
    if (this.password) {
      this.password = await bcrypt.hash(this.password, 10);
    }
  }
}
