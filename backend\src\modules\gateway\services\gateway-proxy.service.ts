import { Injectable, Logger, ServiceUnavailableException, RequestTimeoutException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { ProxyAsyncLimiterService } from './proxy-async-limiter.service';
import { firstValueFrom, timeout, retry, catchError } from 'rxjs';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { 
  IGatewayProxyService, 
  IGatewayRequest, 
  IProxyOptions, 
  IServiceHealth 
} from '../interfaces/gateway.interfaces';
import { 
  GATEWAY_CONFIG, 
  SERVICE_ENDPOINTS,
  GATEWAY_ERROR_CODES,
  DEFAULT_ERROR_MESSAGES 
} from '../config/gateway.constants';

/**
 * 网关代理服务
 * 负责将请求转发到目标服务并处理响应
 * 遵循单一职责原则，专注于HTTP代理功能
 */
@Injectable()
export class GatewayProxyService implements IGatewayProxyService {
  private readonly logger = new Logger(GatewayProxyService.name);
  private readonly serviceHealthCache = new Map<string, IServiceHealth>();
  private readonly healthCheckInterval: NodeJS.Timeout;
  private lastHealthLogTime = 0; // 用于控制健康检查日志频率

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly proxyLimiter: ProxyAsyncLimiterService,
  ) {
    // 只在启用健康检查时才设置定时器
    if (GATEWAY_CONFIG.ENABLE_HEALTH_CHECK) {
      this.healthCheckInterval = setInterval(
        () => this.updateServiceHealthCache(),
        GATEWAY_CONFIG.HEALTH_CHECK_INTERVAL
      );
      this.logger.log(`健康检查已启用，间隔: ${GATEWAY_CONFIG.HEALTH_CHECK_INTERVAL}ms`);
    } else {
      this.logger.log('健康检查已禁用');
    }
  }

  /**
   * 转发请求到目标服务
   */
  async forwardRequest(
    targetUrl: string,
    request: IGatewayRequest,
    options?: IProxyOptions
  ): Promise<any> {
    const startTime = Date.now();
    const requestId = request.headers?.['x-request-id'] || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const taskType = this.extractTaskType(targetUrl);
    const userId = (request as any).user?.id;
    const apiKeyId = (request as any).apiKey?.id;

    try {
      this.logger.debug(`转发请求到: ${targetUrl}，请求ID: ${requestId}`);

      // 请求执行权限（并发控制）
      await this.proxyLimiter.requestExecution(requestId, taskType, userId, apiKeyId);

      try {
        // 构建请求配置
        const config = this.buildRequestConfig(request, options);

        // 发送请求
        const response = await this.sendRequest(targetUrl, config);

        const responseTime = Date.now() - startTime;
        this.logger.debug(`请求完成，耗时: ${responseTime}ms`);

        // 释放执行权限（成功）
        await this.proxyLimiter.releaseExecution(requestId, true, responseTime);

        return this.processResponse(response);

      } catch (requestError) {
        // 释放执行权限（失败）
        const responseTime = Date.now() - startTime;
        await this.proxyLimiter.releaseExecution(requestId, false, responseTime);
        throw requestError;
      }

    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error(`请求转发失败 [${targetUrl}]: ${error.message}，耗时: ${responseTime}ms`);

      throw this.handleProxyError(error, targetUrl);
    }
  }

  /**
   * 检查服务健康状态
   */
  async checkServiceHealth(serviceKey: string): Promise<IServiceHealth> {
    const startTime = Date.now();
    
    try {
      const serviceUrl = SERVICE_ENDPOINTS[serviceKey as keyof typeof SERVICE_ENDPOINTS];
      if (!serviceUrl) {
        throw new Error(`未知的服务: ${serviceKey}`);
      }

      // 发送健康检查请求
      const healthUrl = `${serviceUrl}/health`;
      const response = await firstValueFrom(
        this.httpService.get(healthUrl, {
          timeout: 5000,
          validateStatus: (status) => status < 500,
        }).pipe(
          timeout(5000),
          catchError((error) => {
            throw error;
          })
        )
      );

      const responseTime = Date.now() - startTime;
      const health: IServiceHealth = {
        service: serviceKey,
        status: response.status === 200 ? 'healthy' : 'degraded',
        responseTime,
        lastCheck: new Date(),
        details: response.data,
      };

      // 更新缓存
      this.serviceHealthCache.set(serviceKey, health);
      
      return health;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      const health: IServiceHealth = {
        service: serviceKey,
        status: 'unhealthy',
        responseTime,
        lastCheck: new Date(),
        errorCount: 1,
        details: { error: error.message },
      };

      // 更新缓存
      this.serviceHealthCache.set(serviceKey, health);
      
      return health;
    }
  }

  /**
   * 获取缓存的服务健康状态
   */
  getCachedServiceHealth(serviceKey: string): IServiceHealth | undefined {
    return this.serviceHealthCache.get(serviceKey);
  }

  /**
   * 获取所有服务健康状态
   */
  getAllServiceHealth(): IServiceHealth[] {
    return Array.from(this.serviceHealthCache.values());
  }

  /**
   * 构建请求配置
   */
  private buildRequestConfig(request: IGatewayRequest, options?: IProxyOptions): AxiosRequestConfig {
    const config: AxiosRequestConfig = {
      method: request.method.toLowerCase() as any,
      headers: {
        ...request.headers,
        ...options?.headers,
      },
      timeout: options?.timeout || GATEWAY_CONFIG.DEFAULT_TIMEOUT,
      validateStatus: options?.validateStatus || ((status) => status < 400),
    };

    // 处理请求体
    if (request.body) {
      if (request.file) {
        // 文件上传
        config.data = this.buildFormData(request);
        if (config.headers) {
          config.headers['Content-Type'] = 'multipart/form-data';
        }
      } else {
        // JSON数据
        config.data = request.body;
        if (config.headers) {
          config.headers['Content-Type'] = 'application/json';
        }
      }
    }

    // 移除可能导致问题的头部
    if (config.headers) {
      delete config.headers['host'];
      delete config.headers['content-length'];
    }

    return config;
  }

  /**
   * 构建FormData
   */
  private buildFormData(request: IGatewayRequest): FormData {
    const formData = new FormData();

    // 添加文件
    if (request.file) {
      formData.append('file', new Blob([request.file.buffer]), request.file.originalname);
    }

    // 添加其他字段
    if (request.body) {
      Object.keys(request.body).forEach(key => {
        if (key !== 'file') {
          const value = request.body[key];
          if (typeof value === 'object') {
            formData.append(key, JSON.stringify(value));
          } else {
            formData.append(key, String(value));
          }
        }
      });
    }

    return formData;
  }

  /**
   * 发送HTTP请求
   */
  private async sendRequest(url: string, config: AxiosRequestConfig): Promise<AxiosResponse> {
    const maxRetries = config.timeout && config.timeout > 10000 ? 
      GATEWAY_CONFIG.DEFAULT_RETRY_ATTEMPTS : 1;

    return await firstValueFrom(
      this.httpService.request({ ...config, url }).pipe(
        timeout(config.timeout || GATEWAY_CONFIG.DEFAULT_TIMEOUT),
        retry({
          count: maxRetries,
          delay: GATEWAY_CONFIG.DEFAULT_RETRY_DELAY,
        }),
        catchError((error) => {
          throw error;
        })
      )
    );
  }

  /**
   * 处理响应
   */
  private processResponse(response: AxiosResponse): any {
    // 记录响应信息
    this.logger.debug(`收到响应: ${response.status} ${response.statusText}`);

    // 返回响应数据
    return response.data;
  }

  /**
   * 处理代理错误
   */
  private handleProxyError(error: any, targetUrl: string): Error {
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      this.logger.error(`服务不可用: ${targetUrl}`);
      return new ServiceUnavailableException(
        DEFAULT_ERROR_MESSAGES[GATEWAY_ERROR_CODES.SERVICE_UNAVAILABLE]
      );
    }

    if (error.code === 'ECONNABORTED' || error.name === 'TimeoutError') {
      this.logger.error(`请求超时: ${targetUrl}`);
      return new RequestTimeoutException(
        DEFAULT_ERROR_MESSAGES[GATEWAY_ERROR_CODES.REQUEST_TIMEOUT]
      );
    }

    if (error.response) {
      // 服务返回了错误响应
      const status = error.response.status;
      const message = error.response.data?.message || error.response.statusText;
      
      this.logger.error(`服务返回错误: ${status} ${message}`);
      
      // 保持原始错误状态码和消息
      const proxyError = new Error(message);
      (proxyError as any).status = status;
      return proxyError;
    }

    // 其他未知错误
    this.logger.error(`代理请求失败: ${error.message}`);
    return error;
  }

  /**
   * 更新服务健康状态缓存
   */
  private async updateServiceHealthCache(): Promise<void> {
    try {
      const services = Object.keys(SERVICE_ENDPOINTS);
      const healthChecks = services.map(service => 
        this.checkServiceHealth(service).catch(error => {
          this.logger.error(`健康检查失败 [${service}]: ${error.message}`);
          return null;
        })
      );

      await Promise.all(healthChecks);

      // 只在必要时记录日志（每小时最多一次）
      const now = Date.now();
      if (now - this.lastHealthLogTime > 3600000) { // 1小时
        this.logger.log('服务健康状态缓存已更新');
        this.lastHealthLogTime = now;
      }
    } catch (error) {
      this.logger.error(`更新健康状态缓存失败: ${error.message}`);
    }
  }

  /**
   * 检查目标服务是否可用
   */
  isServiceAvailable(serviceKey: string): boolean {
    const health = this.serviceHealthCache.get(serviceKey);
    return health ? health.status === 'healthy' : true; // 默认认为可用
  }

  /**
   * 获取服务响应时间
   */
  getServiceResponseTime(serviceKey: string): number | undefined {
    const health = this.serviceHealthCache.get(serviceKey);
    return health?.responseTime;
  }

  /**
   * 清理资源
   */
  onModuleDestroy(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
  }

  /**
   * 批量转发请求
   */
  async forwardBatchRequests(
    requests: Array<{ url: string; request: IGatewayRequest; options?: IProxyOptions }>
  ): Promise<Array<{ success: boolean; data?: any; error?: string }>> {
    const results = await Promise.allSettled(
      requests.map(({ url, request, options }) => 
        this.forwardRequest(url, request, options)
      )
    );

    return results.map(result => {
      if (result.status === 'fulfilled') {
        return { success: true, data: result.value };
      } else {
        return { success: false, error: result.reason.message };
      }
    });
  }

  /**
   * 获取代理统计信息
   */
  getProxyStats(): {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    avgResponseTime: number;
    serviceHealth: Record<string, string>;
  } {
    // 这里可以添加实际的统计逻辑
    // 暂时返回模拟数据
    const serviceHealth: Record<string, string> = {};
    this.serviceHealthCache.forEach((health, service) => {
      serviceHealth[service] = health.status;
    });

    return {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      avgResponseTime: 0,
      serviceHealth,
    };
  }

  /**
   * 从URL提取任务类型
   */
  private extractTaskType(url: string): string {
    if (url.includes('/ocr/')) return 'ocr';
    if (url.includes('/address/')) return 'address';
    if (url.includes('/geo/')) return 'geo';
    return 'default';
  }

  /**
   * 清理资源
   */
  onDestroy(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.logger.log('健康检查定时器已清理');
    }
  }
}
