import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { TaskService } from '@/modules/queue/services/task.service';
import { TaskStatus as QueueTaskStatus } from '@/modules/queue/interfaces/task-status.enum';
import { 
  ITaskResultService, 
  ITaskResult, 
  ITaskWaitOptions 
} from '../interfaces/gateway.interfaces';
import { 
  TaskStatus, 
  GATEWAY_CONFIG,
  GATEWAY_ERROR_CODES,
  DEFAULT_ERROR_MESSAGES 
} from '../config/gateway.constants';

/**
 * 任务结果服务
 * 负责管理异步任务的状态、进度和结果
 * 遵循单一职责原则，专注于任务生命周期管理
 */
@Injectable()
export class TaskResultService implements ITaskResultService {
  private readonly logger = new Logger(TaskResultService.name);

  constructor(
    private readonly taskService: TaskService,
  ) {}

  /**
   * 等待任务完成（增强版，支持自适应超时策略）
   */
  async waitForTaskResult(taskId: string, options?: ITaskWaitOptions): Promise<ITaskResult> {
    const adaptiveTimeout = await this.calculateAdaptiveTimeout(taskId, options);
    const maxWaitTime = options?.maxWaitTime || adaptiveTimeout;
    const checkInterval = this.calculateCheckInterval(maxWaitTime);
    const startTime = Date.now();

    this.logger.debug(`开始等待任务 ${taskId}，自适应超时时间: ${maxWaitTime}ms，检查间隔: ${checkInterval}ms`);

    return new Promise((resolve, reject) => {
      let consecutiveFailures = 0;
      const maxConsecutiveFailures = 3;

      const checkTask = async () => {
        try {
          const task = await this.getTaskStatus(taskId);
          consecutiveFailures = 0; // 重置失败计数

          // 调用进度回调
          if (options?.progressCallback && task.progress !== undefined) {
            options.progressCallback(task.progress);
          }

          // 检查任务是否完成
          if (task.status === TaskStatus.COMPLETED) {
            this.logger.debug(`任务 ${taskId} 已完成，耗时: ${Date.now() - startTime}ms`);
            resolve(task);
            return;
          }

          if (task.status === TaskStatus.FAILED) {
            this.logger.error(`任务 ${taskId} 执行失败: ${task.error}`);
            resolve(task); // 返回失败结果而不是抛出异常
            return;
          }

          // 检查超时
          const elapsedTime = Date.now() - startTime;
          if (elapsedTime >= maxWaitTime) {
            this.logger.warn(`任务 ${taskId} 等待超时，已等待 ${elapsedTime}ms`);

            const timeoutTask: ITaskResult = {
              ...task,
              status: TaskStatus.TIMEOUT,
              error: '任务等待超时',
              updatedAt: new Date(),
            };

            resolve(timeoutTask);
            return;
          }

          // 动态调整检查间隔
          const dynamicInterval = this.calculateDynamicInterval(elapsedTime, maxWaitTime, task.progress);
          setTimeout(checkTask, dynamicInterval);

        } catch (error) {
          consecutiveFailures++;
          this.logger.error(`检查任务状态失败 (${consecutiveFailures}/${maxConsecutiveFailures}): ${error.message}`);

          if (consecutiveFailures >= maxConsecutiveFailures) {
            reject(new Error(`连续 ${maxConsecutiveFailures} 次检查任务状态失败`));
            return;
          }

          // 指数退避重试
          const retryDelay = Math.min(checkInterval * Math.pow(2, consecutiveFailures - 1), 5000);
          setTimeout(checkTask, retryDelay);
        }
      };

      // 开始检查
      checkTask();
    });
  }

  /**
   * 计算自适应超时时间
   */
  private async calculateAdaptiveTimeout(taskId: string, options?: ITaskWaitOptions): Promise<number> {
    try {
      // 获取任务类型和历史数据
      const task = await this.getTaskStatus(taskId);
      const taskType = this.extractTaskType(taskId);

      // 基础超时时间配置
      const baseTimeouts = {
        ocr: 30000,        // OCR任务基础30秒
        address: 15000,    // 地址提取基础15秒
        geo: 10000,        // 地理坐标基础10秒
        default: 20000,    // 默认20秒
      };

      let baseTimeout = baseTimeouts[taskType] || baseTimeouts.default;

      // 根据队列长度调整超时时间
      const queueLength = await this.getQueueLength(taskType);
      if (queueLength > 10) {
        baseTimeout *= Math.min(1 + (queueLength - 10) * 0.1, 3); // 最多增加3倍
      }

      // 根据历史平均处理时间调整
      const avgProcessingTime = await this.getAverageProcessingTime(taskType);
      if (avgProcessingTime > 0) {
        const adjustedTimeout = avgProcessingTime * 2.5; // 给予2.5倍的缓冲时间
        baseTimeout = Math.max(baseTimeout, adjustedTimeout);
      }

      // 应用用户配置的最大值
      if (options?.maxWaitTime) {
        baseTimeout = Math.min(baseTimeout, options.maxWaitTime);
      }

      // 确保在合理范围内
      return Math.max(5000, Math.min(baseTimeout, 300000)); // 5秒到5分钟之间
    } catch (error) {
      this.logger.warn(`计算自适应超时时间失败: ${error.message}`);
      return GATEWAY_CONFIG.MAX_PROXY_WAIT_TIME;
    }
  }

  /**
   * 计算检查间隔
   */
  private calculateCheckInterval(maxWaitTime: number): number {
    // 根据总等待时间动态调整检查间隔
    if (maxWaitTime <= 10000) return 500;   // 10秒内，每500ms检查一次
    if (maxWaitTime <= 30000) return 1000;  // 30秒内，每1秒检查一次
    if (maxWaitTime <= 60000) return 2000;  // 1分钟内，每2秒检查一次
    return 3000; // 超过1分钟，每3秒检查一次
  }

  /**
   * 计算动态检查间隔
   */
  private calculateDynamicInterval(elapsedTime: number, maxWaitTime: number, progress?: number): number {
    const baseInterval = this.calculateCheckInterval(maxWaitTime);

    // 如果有进度信息，根据进度调整间隔
    if (progress !== undefined) {
      if (progress > 0.8) return baseInterval * 0.5; // 接近完成时更频繁检查
      if (progress > 0.5) return baseInterval * 0.8; // 进度过半时稍微频繁
    }

    // 根据已等待时间调整
    const timeRatio = elapsedTime / maxWaitTime;
    if (timeRatio > 0.8) return baseInterval * 0.7; // 接近超时时更频繁检查
    if (timeRatio > 0.5) return baseInterval * 0.9; // 过半时稍微频繁

    return baseInterval;
  }

  /**
   * 从任务ID提取任务类型
   */
  private extractTaskType(taskId: string): string {
    if (taskId.includes('ocr')) return 'ocr';
    if (taskId.includes('address')) return 'address';
    if (taskId.includes('geo')) return 'geo';
    return 'default';
  }

  /**
   * 获取队列长度
   */
  private async getQueueLength(taskType: string): Promise<number> {
    try {
      // 这里应该调用队列服务获取实际队列长度
      // 暂时返回模拟数据
      return 0;
    } catch (error) {
      this.logger.warn(`获取队列长度失败: ${error.message}`);
      return 0;
    }
  }

  /**
   * 获取平均处理时间
   */
  private async getAverageProcessingTime(taskType: string): Promise<number> {
    try {
      // 这里应该从历史数据中计算平均处理时间
      // 暂时返回模拟数据
      const avgTimes = {
        ocr: 15000,
        address: 8000,
        geo: 5000,
        default: 10000,
      };
      return avgTimes[taskType] || avgTimes.default;
    } catch (error) {
      this.logger.warn(`获取平均处理时间失败: ${error.message}`);
      return 0;
    }
  }

  /**
   * 获取任务状态
   */
  async getTaskStatus(taskId: string): Promise<ITaskResult> {
    try {
      // 使用队列模块的TaskService获取任务状态
      const queueTask = await this.taskService.getTaskStatus(taskId);

      // 转换为网关模块的任务结果格式
      const task: ITaskResult = {
        taskId: queueTask.taskId,
        status: this.convertQueueStatusToGatewayStatus(queueTask.status),
        result: queueTask.result,
        error: queueTask.error,
        progress: queueTask.progress,
        message: queueTask.message,
        createdAt: new Date(queueTask.createdAt),
        updatedAt: queueTask.updatedAt ? new Date(queueTask.updatedAt) : undefined,
        processingTime: queueTask.updatedAt && queueTask.createdAt ?
          queueTask.updatedAt - queueTask.createdAt : undefined,
      };

      return task;

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new NotFoundException(DEFAULT_ERROR_MESSAGES[GATEWAY_ERROR_CODES.TASK_NOT_FOUND]);
      }

      this.logger.error(`获取任务状态失败: ${error.message}`);
      throw new Error('获取任务状态失败');
    }
  }

  /**
   * 转换队列状态到网关状态
   */
  private convertQueueStatusToGatewayStatus(queueStatus: QueueTaskStatus): TaskStatus {
    switch (queueStatus) {
      case QueueTaskStatus.QUEUED:
        return TaskStatus.QUEUED;
      case QueueTaskStatus.ACTIVE:
      case QueueTaskStatus.PROCESSING:
        return TaskStatus.PROCESSING;
      case QueueTaskStatus.COMPLETED:
        return TaskStatus.COMPLETED;
      case QueueTaskStatus.FAILED:
        return TaskStatus.FAILED;
      case QueueTaskStatus.WAITING:
      case QueueTaskStatus.PAUSED:
      case QueueTaskStatus.DELAYED:
        return TaskStatus.QUEUED;
      default:
        return TaskStatus.QUEUED;
    }
  }

  /**
   * 转换网关状态到队列状态
   */
  private convertGatewayStatusToQueueStatus(gatewayStatus: TaskStatus): QueueTaskStatus {
    switch (gatewayStatus) {
      case TaskStatus.QUEUED:
        return QueueTaskStatus.QUEUED;
      case TaskStatus.PROCESSING:
        return QueueTaskStatus.PROCESSING;
      case TaskStatus.COMPLETED:
        return QueueTaskStatus.COMPLETED;
      case TaskStatus.FAILED:
        return QueueTaskStatus.FAILED;
      case TaskStatus.TIMEOUT:
        return QueueTaskStatus.FAILED; // 超时视为失败
      default:
        return QueueTaskStatus.QUEUED;
    }
  }

  /**
   * 创建新任务
   */
  async createTask(taskId: string, initialData?: Partial<ITaskResult>): Promise<ITaskResult> {
    // 这个方法现在主要用于兼容性，实际任务创建由队列模块处理
    const task: ITaskResult = {
      taskId,
      status: TaskStatus.QUEUED,
      progress: 0,
      createdAt: new Date(),
      ...initialData,
    };

    this.logger.debug(`任务已创建: ${taskId}`);

    return task;
  }

  /**
   * 更新任务状态
   */
  async updateTaskStatus(taskId: string, status: TaskStatus, data?: Partial<ITaskResult>): Promise<void> {
    try {
      // 转换网关状态到队列状态
      const queueStatus = this.convertGatewayStatusToQueueStatus(status);

      // 准备更新数据
      const updateData: any = {};
      if (data?.result) updateData.result = data.result;
      if (data?.error) updateData.error = data.error;
      if (data?.progress !== undefined) updateData.progress = data.progress;

      // 使用队列模块的TaskService更新状态
      await this.taskService.updateTaskStatus(taskId, queueStatus, updateData);

      this.logger.debug(`更新任务状态: ${taskId} -> ${status}`);

    } catch (error) {
      this.logger.error(`更新任务状态失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新任务进度
   */
  async updateTaskProgress(taskId: string, progress: number): Promise<void> {
    try {
      // 使用队列模块的TaskService更新进度
      await this.taskService.updateTaskStatus(taskId, QueueTaskStatus.PROCESSING, {
        progress,
      });

      this.logger.debug(`更新任务进度: ${taskId} -> ${progress}`);

    } catch (error) {
      this.logger.error(`更新任务进度失败: ${error.message}`);
    }
  }

  /**
   * 设置任务结果
   */
  async setTaskResult(taskId: string, result: any, error?: string): Promise<void> {
    const status = error ? TaskStatus.FAILED : TaskStatus.COMPLETED;

    await this.updateTaskStatus(taskId, status, {
      result: error ? undefined : result,
      error,
      progress: error ? undefined : 1.0,
    });
  }

  /**
   * 估算剩余时间
   */
  estimateRemainingTime(taskType: string, progress: number): number {
    // 基于任务类型的平均处理时间
    const avgTimes: Record<string, number> = {
      'ocr': 20000,           // 20秒
      'extract-address': 8000, // 8秒
      'rev-geo': 3000,        // 3秒
    };

    const avgTime = avgTimes[taskType] || 10000; // 默认10秒
    const remainingProgress = Math.max(0, 1 - progress);

    return Math.round(avgTime * remainingProgress);
  }

  /**
   * 获取任务列表
   */
  async getTaskList(
    status?: TaskStatus,
    limit: number = 50,
    offset: number = 0
  ): Promise<{ tasks: ITaskResult[]; total: number }> {
    try {
      // 这里应该使用队列模块的方法获取任务列表
      // 暂时返回空列表，实际实现需要队列模块提供相应接口
      this.logger.warn('getTaskList方法需要队列模块提供相应接口');
      return { tasks: [], total: 0 };

    } catch (error) {
      this.logger.error(`获取任务列表失败: ${error.message}`);
      return { tasks: [], total: 0 };
    }
  }

  /**
   * 清理过期任务
   */
  async cleanupExpiredTasks(): Promise<number> {
    try {
      // 这里应该使用队列模块的方法清理过期任务
      // 暂时返回0，实际实现需要队列模块提供相应接口
      this.logger.warn('cleanupExpiredTasks方法需要队列模块提供相应接口');
      return 0;

    } catch (error) {
      this.logger.error(`清理过期任务失败: ${error.message}`);
      return 0;
    }
  }

  /**
   * 获取任务统计信息
   */
  async getTaskStats(): Promise<{
    total: number;
    byStatus: Record<TaskStatus, number>;
    avgProcessingTime: number;
    successRate: number;
  }> {
    try {
      // 这里应该使用队列模块的方法获取统计信息
      // 暂时返回空统计，实际实现需要队列模块提供相应接口
      this.logger.warn('getTaskStats方法需要队列模块提供相应接口');

      return {
        total: 0,
        byStatus: {
          [TaskStatus.QUEUED]: 0,
          [TaskStatus.PENDING]: 0,
          [TaskStatus.PROCESSING]: 0,
          [TaskStatus.COMPLETED]: 0,
          [TaskStatus.FAILED]: 0,
          [TaskStatus.TIMEOUT]: 0,
          [TaskStatus.CANCELLED]: 0,
        },
        avgProcessingTime: 0,
        successRate: 0,
      };

    } catch (error) {
      this.logger.error(`获取任务统计失败: ${error.message}`);
      return {
        total: 0,
        byStatus: {
          [TaskStatus.QUEUED]: 0,
          [TaskStatus.PENDING]: 0,
          [TaskStatus.PROCESSING]: 0,
          [TaskStatus.COMPLETED]: 0,
          [TaskStatus.FAILED]: 0,
          [TaskStatus.TIMEOUT]: 0,
          [TaskStatus.CANCELLED]: 0,
        },
        avgProcessingTime: 0,
        successRate: 0,
      };
    }
  }
}
