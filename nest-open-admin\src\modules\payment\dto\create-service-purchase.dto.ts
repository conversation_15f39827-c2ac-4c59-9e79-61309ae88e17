import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString, IsOptional, IsIn, Min, Max, IsInt } from 'class-validator';

export class CreateServicePurchaseDto {
  @ApiProperty({
    description: '服务ID',
    example: 1,
  })
  @IsNumber({}, { message: '服务ID必须是数字' })
  @IsInt({ message: '服务ID必须是整数' })
  @Min(1, { message: '服务ID必须大于0' })
  serviceId: number;

  @ApiProperty({
    description: '购买数量（调用次数）',
    example: 100,
    minimum: 1,
    maximum: 100000,
  })
  @IsNumber({}, { message: '购买数量必须是数字' })
  @IsInt({ message: '购买数量必须是整数' })
  @Min(1, { message: '购买数量最少1次' })
  @Max(100000, { message: '购买数量最多100000次' })
  quantity: number;

  @ApiProperty({
    description: '支付方式',
    example: 'alipay',
    enum: ['alipay', 'wechat', 'bank'],
  })
  @IsString({ message: '支付方式必须是字符串' })
  @IsIn(['alipay', 'wechat', 'bank'], { message: '支付方式必须是 alipay、wechat 或 bank' })
  paymentMethod: 'alipay' | 'wechat' | 'bank';

  @ApiProperty({
    description: '支付成功后的返回地址',
    example: 'https://example.com/payment/success',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '返回地址必须是字符串' })
  returnUrl?: string;

  @ApiProperty({
    description: '支付结果通知地址',
    example: 'https://example.com/api/payment/notify',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '通知地址必须是字符串' })
  notifyUrl?: string;
}