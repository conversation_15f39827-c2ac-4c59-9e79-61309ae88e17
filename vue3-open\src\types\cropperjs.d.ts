// Cropper.js 类型扩展
declare module 'cropperjs' {
  interface CropperOptions {
    aspectRatio?: number
    viewMode?: number
    dragMode?: string
    autoCropArea?: number
    restore?: boolean
    guides?: boolean
    center?: boolean
    highlight?: boolean
    cropBoxMovable?: boolean
    cropBoxResizable?: boolean
    toggleDragModeOnDblclick?: boolean
    preview?: any
    crop?: (event: any) => void
    ready?: () => void
    cropstart?: (event: any) => void
    cropmove?: (event: any) => void
    cropend?: (event: any) => void
    zoom?: (event: any) => void
  }

  class Cropper {
    constructor(element: HTMLImageElement | HTMLCanvasElement, options?: CropperOptions)
    destroy(): void
    zoom(ratio: number): Cropper
    rotate(degree: number): Cropper
    scaleX(scaleX: number): Cropper
    scaleY(scaleY: number): Cropper
    getCroppedCanvas(options?: any): HTMLCanvasElement
    getData(rounded?: boolean): any
    setAspectRatio(aspectRatio: number): Cropper
    setDragMode(mode: string): Cropper
    reset(): Cropper
  }

  export { Cropper, CropperOptions }
}