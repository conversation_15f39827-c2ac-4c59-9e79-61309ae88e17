import { request } from '@/utils/request'
import type {
  SystemStatus,
  SystemMetrics,
  MetricsHistory,
  Alert,
  AlertHistory,
  MonitoringDashboard,
  HealthCheck
} from '@/types/monitoring'

/**
 * 监控相关API
 */
export const monitoringApi = {
  /**
   * 获取系统状态
   */
  getSystemStatus(): Promise<SystemStatus> {
    return request.get('/op/monitoring/status')
  },

  /**
   * 获取系统指标
   */
  getSystemMetrics(): Promise<SystemMetrics> {
    return request.get('/op/monitoring/metrics')
  },

  /**
   * 获取历史指标数据
   * @param timeRange 时间范围
   */
  getMetricsHistory(timeRange: string): Promise<MetricsHistory> {
    return request.get('/op/monitoring/metrics/history', {
      params: { timeRange }
    })
  },

  /**
   * 获取活跃告警
   */
  getActiveAlerts(): Promise<Alert[]> {
    return request.get('/op/monitoring/alerts')
  },

  /**
   * 获取告警历史
   * @param params 查询参数
   */
  getAlertHistory(params?: {
    page?: number
    pageSize?: number
    startTime?: string
    endTime?: string
    level?: string
  }): Promise<{
    data: AlertHistory[]
    total: number
    page: number
    pageSize: number
  }> {
    return request.get('/op/monitoring/alerts/history', { params })
  },

  /**
   * 获取监控面板数据
   */
  getMonitoringDashboard(): Promise<MonitoringDashboard> {
    return request.get('/op/monitoring/dashboard')
  },

  /**
   * 健康检查
   */
  healthCheck(): Promise<HealthCheck> {
    return request.get('/op/monitoring/health')
  },

  /**
   * 解除告警
   * @param alertId 告警ID
   */
  resolveAlert(alertId: string): Promise<void> {
    return request.post(`/op/monitoring/alerts/${alertId}/resolve`)
  },

  /**
   * 批量解除告警
   * @param alertIds 告警ID列表
   */
  batchResolveAlerts(alertIds: string[]): Promise<void> {
    return request.post('/op/monitoring/alerts/batch-resolve', {
      alertIds
    })
  }
}