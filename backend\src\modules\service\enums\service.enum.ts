export enum ServiceType {
  AI_SERVICE = 'ai_service',
  OCR = 'ocr',
  NLP = 'nlp',
  CV = 'cv',
  GEO = 'geo',
  DATA = 'data',
  OTHER = 'other'
}

export enum ServiceStatus {
  DRAFT = 'draft',         // 草稿
  ACTIVE = 'active',       // 已上线
  INACTIVE = 'inactive',   // 已下线
  MAINTENANCE = 'maintenance', // 维护中
  DEPRECATED = 'deprecated',   // 已废弃
  COMING_SOON = 'coming_soon'  // 即将推出
}

export enum PricingModel {
  FREE = 'free',                  // 免费
  PER_REQUEST = 'per_request',    // 按请求次数
  PER_TOKEN = 'per_token',        // 按令牌数量
  PER_CHARACTER = 'per_character',// 按字符数量
  SUBSCRIPTION = 'subscription'   // 订阅制
} 