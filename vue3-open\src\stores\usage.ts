import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { usageApi } from '@/api/usage'
import type {
  UsageStats,
  UsageRecord,
  UsageTrend,
  ServiceUsageDistribution,
  TimeRange,
  UsageQueryParams,
  ApiKeyUsageStats,
} from '@/types/usage'

export const useUsageStore = defineStore('usage', () => {
  // 状态
  const loading = ref(false)
  const stats = ref<UsageStats | null>(null)
  const records = ref<UsageRecord[]>([])
  const trends = ref<UsageTrend[]>([])
  const serviceDistribution = ref<ServiceUsageDistribution[]>([])
  const apiKeyStats = ref<ApiKeyUsageStats[]>([])
  const currentTimeRange = ref<TimeRange>('30d')
  const pagination = ref({
    page: 1,
    pageSize: 20,
    total: 0,
  })
  const lastUpdated = ref<string>('')

  // 计算属性
  const totalCalls = computed(() => stats.value?.totalCalls || 0)
  const totalCost = computed(() => stats.value?.totalCost || 0)
  const errorRate = computed(() => stats.value?.errorRate || 0)
  const averageResponseTime = computed(() => stats.value?.averageResponseTime || 0)

  const topServices = computed(() => stats.value?.topServices || [])
  const hasData = computed(() => records.value.length > 0)

  // 获取使用记录列表
  const fetchUsageRecords = async (params?: Record<string, any>) => {
    try {
      loading.value = true
      const queryParams = {
        page: pagination.value.page,
        limit: pagination.value.pageSize,
        ...params,
      }

      const response: Record<string, any> = await usageApi.getUsageRecords(queryParams)
      records.value = response?.data || []
      pagination.value = {
        page: response?.page,
        pageSize: response?.limit,
        total: response?.total,
      }
      return response?.data || []
    } catch (error) {
      console.error('获取使用记录失败:', error)
      ElMessage.error('获取使用记录失败')
    } finally {
      loading.value = false
    }
  }

  // 设置时间范围
  const setTimeRange = (range: TimeRange) => {
    currentTimeRange.value = range
  }

  // 设置分页
  const setPagination = (page: number, pageSize?: number) => {
    pagination.value.page = page
    if (pageSize) {
      pagination.value.pageSize = pageSize
    }
  }

  // 重置状态
  const reset = () => {
    stats.value = null
    records.value = []
    trends.value = []
    serviceDistribution.value = []
    apiKeyStats.value = []
    pagination.value = {
      page: 1,
      pageSize: 20,
      total: 0,
    }
    lastUpdated.value = ''
  }

  return {
    // 状态
    loading,
    stats,
    records,
    trends,
    serviceDistribution,
    apiKeyStats,
    currentTimeRange,
    pagination,
    lastUpdated,

    // 计算属性
    totalCalls,
    totalCost,
    errorRate,
    averageResponseTime,
    topServices,
    hasData,

    // 方法
    fetchUsageRecords,
    setTimeRange,
    setPagination,
    reset,
  }
})
