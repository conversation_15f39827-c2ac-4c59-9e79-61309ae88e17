# 定时任务管理需求文档

## 1. 文档概述

本文档描述了开放平台定时任务管理模块的需求规范，该模块基于单一职责原则设计，专注于系统中各类定时任务的集中管理和调度。定时任务管理模块负责执行周期性任务，如数据清理、缓存同步、额度重置等，确保系统各组件能够按照预定的时间间隔自动执行维护操作，提高系统稳定性和数据一致性。

## 2. 功能需求

### 2.1 核心功能

- **定时任务调度**
  - 基于Cron表达式的任务调度
  - 支持多种执行周期（每日、每小时、每月等）
  - 任务执行状态跟踪
  - 任务执行结果记录

- **系统维护任务**
  - 新用户每日免费使用次数重置
  - API密钥定时缓存同步
  - 过期数据定期清理
  - 调用记录定期清理（每两个月）
  - 每日调用统计生成（每天凌晨2:00）

- **管理接口**
  - 手动触发定时任务执行
  - 查看任务执行状态和历史
  - 任务执行统计信息

### 2.2 非功能需求

- **高可靠性**：确保任务按时执行，避免遗漏
- **容错能力**：单个任务失败不影响其他任务执行
- **低资源占用**：避免定时任务影响系统正常业务处理
- **可监控性**：提供任务执行状态和结果的监控能力
- **可扩展性**：支持轻松添加新的定时任务

## 3. 技术规范

### 3.1 定时任务配置

```typescript
// 简化示意
@Injectable()
export class ScheduleService {
  private readonly logger = new Logger(ScheduleService.name);
  private readonly DAILY_FREE_QUOTA = 5; // 每日免费额度
  private readonly ALERT_THRESHOLD = 0.2; // 20%预警阈值

  constructor(
    private readonly redisService: RedisService,
    private readonly apiKeyService: ApiKeyService,
    private readonly userServiceService: UserServiceService,
    private readonly callRecordService: CallRecordService
  ) {}

  /**
   * 重置每日免费额度
   * 只为新用户（未有过购买或充值记录的用户）重置免费额度
   */
  @Cron('0 0 * * *') // 每天凌晨执行
  async resetDailyFreeQuota(): Promise<void> {
    try {
      this.logger.log('开始执行每日免费额度重置');
      // 查找符合条件的新用户
      const newUsers = await this.userService.findNewUsers();
      
      // 为每个新用户重置免费额度
      for (const user of newUsers) {
        await this.userServiceService.addFreeCountForAllServices(
          user.id, 
          this.DAILY_FREE_QUOTA
        );
      }
      
      this.logger.log(`每日免费额度重置完成，共处理 ${newUsers.length} 个用户`);
    } catch (error) {
      this.logger.error(`每日免费额度重置失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 定期同步API密钥到缓存
   * 确保缓存中的API密钥数据始终是最新的
   */
  @Cron('0 */30 * * * *') // 每30分钟执行一次
  async syncApiKeysToCache(): Promise<void> {
    try {
      this.logger.log('开始同步API密钥到缓存');
      await this.apiKeyService.syncAllKeysToCache();
      this.logger.log('API密钥同步到缓存完成');
    } catch (error) {
      this.logger.error(`API密钥同步到缓存失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 清理过期数据
   * 包括过期缓存、过期会话等
   */
  @Cron('0 2 * * *') // 每天凌晨2点执行
  async cleanupExpiredData(): Promise<void> {
    try {
      this.logger.log('开始清理过期数据');
      
      // 清理过期缓存
      await this.cleanupExpiredCache();
      
      // 清理过期会话
      await this.cleanupExpiredSessions();
      
      // 清理过期文件
      await this.cleanupExpiredFiles();
      
      this.logger.log('过期数据清理完成');
    } catch (error) {
      this.logger.error(`过期数据清理失败: ${error.message}`, error.stack);
    }
  }
  
  /**
   * 生成每日调用统计
   */
  @Cron('0 0 2 * * *') // 每天凌晨2点执行
  async generateDailyStatistics(): Promise<void> {
    try {
      this.logger.log('开始生成每日调用统计');
      await this.callRecordService.generateDailyStatistics();
      this.logger.log('每日调用统计生成完成');
    } catch (error) {
      this.logger.error(`每日调用统计生成失败: ${error.message}`, error.stack);
    }
  }
  
  /**
   * 清理过期调用记录
   */
  @Cron('0 30 3 1 1,3,5,7,9,11 *') // 每两个月执行一次
  async cleanupCallRecords(): Promise<void> {
    try {
      this.logger.log('开始清理过期调用记录');
      await this.callRecordService.cleanupCallRecords();
      this.logger.log('过期调用记录清理完成');
    } catch (error) {
      this.logger.error(`过期调用记录清理失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取调度任务统计信息
   */
  async getScheduleStats(): Promise<any> {
    // 返回各类定时任务的执行统计信息
    return {
      lastExecutions: {
        resetDailyFreeQuota: await this.getLastExecutionTime('resetDailyFreeQuota'),
        syncApiKeysToCache: await this.getLastExecutionTime('syncApiKeysToCache'),
        cleanupExpiredData: await this.getLastExecutionTime('cleanupExpiredData'),
        generateDailyStatistics: await this.getLastExecutionTime('generateDailyStatistics'),
        cleanupCallRecords: await this.getLastExecutionTime('cleanupCallRecords')
      },
      executionCounts: {
        // 各任务执行次数统计
      },
      status: {
        // 当前任务状态
      }
    };
  }
}
```

### 3.2 管理接口定义

```typescript
// 简化示意
@ApiTags('定时任务管理')
@Controller('schedule')
@ApiBearerAuth()
export class ScheduleController {
  constructor(private readonly scheduleService: ScheduleService) {}

  @Post('reset-free-quota')
  @Roles(RoleEnum.ADMIN, RoleEnum.SUPER)
  @ApiOperation({ summary: '手动重置每日免费次数' })
  async manualResetFreeQuota() {
    await this.scheduleService.resetDailyFreeQuota();
    return { success: true, message: '每日免费次数重置成功' };
  }

  @Post('sync-api-keys')
  @Roles(RoleEnum.ADMIN, RoleEnum.SUPER)
  @ApiOperation({ summary: '手动同步API密钥到缓存' })
  async manualSyncApiKeys() {
    await this.scheduleService.syncApiKeysToCache();
    return { success: true, message: 'API密钥同步成功' };
  }

  @Post('cleanup-data')
  @Roles(RoleEnum.ADMIN, RoleEnum.SUPER)
  @ApiOperation({ summary: '手动清理过期数据' })
  async manualCleanupData() {
    await this.scheduleService.cleanupExpiredData();
    return { success: true, message: '过期数据清理成功' };
  }

  @Get('stats')
  @Roles(RoleEnum.ADMIN, RoleEnum.SUPER)
  @ApiOperation({ summary: '获取定时任务统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getScheduleStats() {
    return await this.scheduleService.getScheduleStats();
  }
}
```

## 4. 系统架构

### 4.1 模块结构

```
schedule/
├── dto/
│   ├── schedule-stats.dto.ts       # 任务统计DTO
│   └── task-execution.dto.ts       # 任务执行DTO
├── entities/
│   └── task-execution.entity.ts    # 任务执行记录实体
├── services/
│   ├── schedule.service.ts         # 定时任务核心服务
│   └── task-history.service.ts     # 任务历史记录服务
├── schedule.controller.ts          # 定时任务控制器
└── schedule.module.ts              # 定时任务模块定义
```

### 4.2 与其他模块的关系

```
graph TD
    A[Schedule模块] -->|调用| B[User模块]
    A -->|调用| C[API Key模块]
    A -->|调用| D[User Service模块]
    A -->|调用| E[Call Record模块]
    A -->|使用| F[Redis服务]
    G[管理员] -->|通过API| A
```

## 5. 任务执行计划

| 任务名称 | 执行周期 | Cron表达式 | 描述 |
|---------|---------|-----------|------|
| 重置每日免费额度 | 每天凌晨 | 0 0 * * * | 为新用户重置每日免费调用次数 |
| API密钥缓存同步 | 每30分钟 | 0 */30 * * * * | 将数据库中的API密钥同步到缓存 |
| 过期数据清理 | 每天凌晨2点 | 0 2 * * * | 清理过期缓存、会话和临时文件 |
| 每日调用统计生成 | 每天凌晨2点 | 0 0 2 * * * | 生成前一天的调用统计数据 |
| 调用记录清理 | 每两个月 | 0 30 3 1 1,3,5,7,9,11 * | 清理两个月前的调用记录 |

## 6. 实现要点

### 6.1 任务隔离与错误处理

- **任务隔离**：每个定时任务独立执行，互不影响
- **错误处理**：任务执行错误被捕获并记录，不影响其他任务
- **重试机制**：关键任务支持失败重试
- **日志记录**：详细记录任务执行过程和结果

### 6.2 性能考虑

- **资源控制**：大型任务分批处理，避免资源占用过高
- **执行时间选择**：选择系统负载较低的时间执行耗资源任务
- **并发限制**：控制并发执行的任务数量
- **数据库优化**：批量操作使用事务和优化的查询

### 6.3 可扩展设计

- **模块化任务**：每个任务独立实现，便于添加新任务
- **配置驱动**：任务执行周期和参数可配置
- **事件通知**：任务执行状态变更通过事件系统通知
- **插件架构**：支持通过插件方式扩展新的定时任务

## 7. 监控和可观测性

- **执行日志**：记录任务执行开始、完成和失败事件
- **性能指标**：监控任务执行时间和资源占用
- **告警机制**：任务失败或超时时发送告警
- **统计面板**：提供任务执行统计和历史查看界面

## 8. 安全考虑

- **权限控制**：只有管理员可手动触发任务执行
- **资源限制**：防止恶意触发导致资源耗尽
- **审计日志**：记录手动触发操作的用户和时间
- **敏感操作保护**：数据清理等敏感操作需要额外确认

## 9. 后续优化方向

- **分布式调度**：支持在集群环境中协调任务执行
- **动态调度**：根据系统负载动态调整任务执行时间
- **任务优先级**：实现任务优先级机制
- **可视化管理**：提供图形化的任务管理界面
- **智能调度**：基于历史数据优化任务执行计划 