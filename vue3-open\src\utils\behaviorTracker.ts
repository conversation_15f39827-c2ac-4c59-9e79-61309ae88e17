/**
 * 行为轨迹记录工具
 * 用于记录用户的鼠标移动、点击、键盘等行为模式
 */

export interface MouseEvent {
  type: 'mousedown' | 'mouseup' | 'mousemove' | 'click'
  x: number
  y: number
  timestamp: number
  button?: number
  pressure?: number
  tiltX?: number
  tiltY?: number
}

export interface KeyboardEvent {
  type: 'keydown' | 'keyup'
  key: string
  code: string
  timestamp: number
  duration?: number
}

export interface TouchEvent {
  type: 'touchstart' | 'touchmove' | 'touchend'
  x: number
  y: number
  timestamp: number
  pressure?: number
  radiusX?: number
  radiusY?: number
}

export interface ScrollEvent {
  type: 'scroll'
  x: number
  y: number
  timestamp: number
}

export interface BehaviorPattern {
  mouseEvents: MouseEvent[]
  keyboardEvents: KeyboardEvent[]
  touchEvents: TouchEvent[]
  scrollEvents: ScrollEvent[]
  startTime: number
  endTime: number
  duration: number
  statistics: BehaviorStatistics
}

export interface BehaviorStatistics {
  totalMouseMoves: number
  averageMouseSpeed: number
  maxMouseSpeed: number
  mouseAcceleration: number[]
  clickPattern: {
    totalClicks: number
    averageClickInterval: number
    doubleClickCount: number
  }
  keyboardPattern: {
    totalKeystrokes: number
    averageTypingSpeed: number
    pausePattern: number[]
  }
  touchPattern: {
    totalTouches: number
    averagePressure: number
    gestureComplexity: number
  }
  scrollPattern: {
    totalScrolls: number
    scrollVelocity: number[]
    scrollDirection: string
  }
  humanLikelihood: number // 0-1, 1表示最像人类行为
}

class BehaviorTracker {
  private mouseEvents: MouseEvent[] = []
  private keyboardEvents: KeyboardEvent[] = []
  private touchEvents: TouchEvent[] = []
  private scrollEvents: ScrollEvent[] = []
  private startTime: number = 0
  private isTracking: boolean = false
  private element: HTMLElement | null = null
  private keyDownTimes: Map<string, number> = new Map()

  /**
   * 开始记录行为轨迹
   */
  startTracking(element?: HTMLElement): void {
    if (this.isTracking) {
      this.stopTracking()
    }

    this.element = element || document.body
    this.startTime = Date.now()
    this.isTracking = true
    this.clearEvents()

    // 添加事件监听器
    this.addEventListeners()
  }

  /**
   * 停止记录行为轨迹
   */
  stopTracking(): BehaviorPattern {
    if (!this.isTracking) {
      throw new Error('Tracking is not started')
    }

    this.isTracking = false
    this.removeEventListeners()

    const endTime = Date.now()
    const duration = endTime - this.startTime
    const statistics = this.calculateStatistics()

    return {
      mouseEvents: [...this.mouseEvents],
      keyboardEvents: [...this.keyboardEvents],
      touchEvents: [...this.touchEvents],
      scrollEvents: [...this.scrollEvents],
      startTime: this.startTime,
      endTime,
      duration,
      statistics
    }
  }

  /**
   * 清空事件记录
   */
  private clearEvents(): void {
    this.mouseEvents = []
    this.keyboardEvents = []
    this.touchEvents = []
    this.scrollEvents = []
    this.keyDownTimes.clear()
  }

  /**
   * 添加事件监听器
   */
  private addEventListeners(): void {
    if (!this.element) return

    // 鼠标事件
    this.element.addEventListener('mousedown', this.handleMouseEvent)
    this.element.addEventListener('mouseup', this.handleMouseEvent)
    this.element.addEventListener('mousemove', this.handleMouseEvent)
    this.element.addEventListener('click', this.handleMouseEvent)

    // 键盘事件
    this.element.addEventListener('keydown', this.handleKeyboardEvent)
    this.element.addEventListener('keyup', this.handleKeyboardEvent)

    // 触摸事件
    this.element.addEventListener('touchstart', this.handleTouchEvent)
    this.element.addEventListener('touchmove', this.handleTouchEvent)
    this.element.addEventListener('touchend', this.handleTouchEvent)

    // 滚动事件
    this.element.addEventListener('scroll', this.handleScrollEvent)
  }

  /**
   * 移除事件监听器
   */
  private removeEventListeners(): void {
    if (!this.element) return

    // 鼠标事件
    this.element.removeEventListener('mousedown', this.handleMouseEvent)
    this.element.removeEventListener('mouseup', this.handleMouseEvent)
    this.element.removeEventListener('mousemove', this.handleMouseEvent)
    this.element.removeEventListener('click', this.handleMouseEvent)

    // 键盘事件
    this.element.removeEventListener('keydown', this.handleKeyboardEvent)
    this.element.removeEventListener('keyup', this.handleKeyboardEvent)

    // 触摸事件
    this.element.removeEventListener('touchstart', this.handleTouchEvent)
    this.element.removeEventListener('touchmove', this.handleTouchEvent)
    this.element.removeEventListener('touchend', this.handleTouchEvent)

    // 滚动事件
    this.element.removeEventListener('scroll', this.handleScrollEvent)
  }

  /**
   * 处理鼠标事件
   */
  private handleMouseEvent = (event: globalThis.MouseEvent): void => {
    if (!this.isTracking) return

    const mouseEvent: MouseEvent = {
      type: event.type as MouseEvent['type'],
      x: event.clientX,
      y: event.clientY,
      timestamp: Date.now(),
      button: event.button
    }

    // 添加压感信息（如果支持）
    if ('pressure' in event) {
      mouseEvent.pressure = (event as any).pressure
    }
    if ('tiltX' in event) {
      mouseEvent.tiltX = (event as any).tiltX
    }
    if ('tiltY' in event) {
      mouseEvent.tiltY = (event as any).tiltY
    }

    this.mouseEvents.push(mouseEvent)
  }

  /**
   * 处理键盘事件
   */
  private handleKeyboardEvent = (event: globalThis.KeyboardEvent): void => {
    if (!this.isTracking) return

    const timestamp = Date.now()
    const keyboardEvent: KeyboardEvent = {
      type: event.type as KeyboardEvent['type'],
      key: event.key,
      code: event.code,
      timestamp
    }

    if (event.type === 'keydown') {
      this.keyDownTimes.set(event.code, timestamp)
    } else if (event.type === 'keyup') {
      const keyDownTime = this.keyDownTimes.get(event.code)
      if (keyDownTime) {
        keyboardEvent.duration = timestamp - keyDownTime
        this.keyDownTimes.delete(event.code)
      }
    }

    this.keyboardEvents.push(keyboardEvent)
  }

  /**
   * 处理触摸事件
   */
  private handleTouchEvent = (event: globalThis.TouchEvent): void => {
    if (!this.isTracking) return

    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i]
      const touchEvent: TouchEvent = {
        type: event.type as TouchEvent['type'],
        x: touch.clientX,
        y: touch.clientY,
        timestamp: Date.now()
      }

      // 添加压感和半径信息（如果支持）
      if ('force' in touch) {
        touchEvent.pressure = touch.force
      }
      if ('radiusX' in touch) {
        touchEvent.radiusX = touch.radiusX
      }
      if ('radiusY' in touch) {
        touchEvent.radiusY = touch.radiusY
      }

      this.touchEvents.push(touchEvent)
    }
  }

  /**
   * 处理滚动事件
   */
  private handleScrollEvent = (event: Event): void => {
    if (!this.isTracking) return

    const target = event.target as HTMLElement
    const scrollEvent: ScrollEvent = {
      type: 'scroll',
      x: target.scrollLeft || window.scrollX,
      y: target.scrollTop || window.scrollY,
      timestamp: Date.now()
    }

    this.scrollEvents.push(scrollEvent)
  }

  /**
   * 计算行为统计信息
   */
  private calculateStatistics(): BehaviorStatistics {
    const mouseStats = this.calculateMouseStatistics()
    const keyboardStats = this.calculateKeyboardStatistics()
    const touchStats = this.calculateTouchStatistics()
    const scrollStats = this.calculateScrollStatistics()
    const humanLikelihood = this.calculateHumanLikelihood(mouseStats, keyboardStats, touchStats)

    return {
      totalMouseMoves: mouseStats.totalMoves,
      averageMouseSpeed: mouseStats.averageSpeed,
      maxMouseSpeed: mouseStats.maxSpeed,
      mouseAcceleration: mouseStats.acceleration,
      clickPattern: mouseStats.clickPattern,
      keyboardPattern: keyboardStats,
      touchPattern: touchStats,
      scrollPattern: scrollStats,
      humanLikelihood
    }
  }

  /**
   * 计算鼠标统计信息
   */
  private calculateMouseStatistics() {
    const mouseMoves = this.mouseEvents.filter(e => e.type === 'mousemove')
    const clicks = this.mouseEvents.filter(e => e.type === 'click')
    
    let totalDistance = 0
    const speeds: number[] = []
    const acceleration: number[] = []
    
    for (let i = 1; i < mouseMoves.length; i++) {
      const prev = mouseMoves[i - 1]
      const curr = mouseMoves[i]
      
      const distance = Math.sqrt(
        Math.pow(curr.x - prev.x, 2) + Math.pow(curr.y - prev.y, 2)
      )
      const timeDiff = curr.timestamp - prev.timestamp
      const speed = timeDiff > 0 ? distance / timeDiff : 0
      
      totalDistance += distance
      speeds.push(speed)
      
      if (i > 1) {
        const prevSpeed = speeds[speeds.length - 2]
        const accel = (speed - prevSpeed) / timeDiff
        acceleration.push(accel)
      }
    }

    // 计算点击模式
    let doubleClickCount = 0
    const clickIntervals: number[] = []
    
    for (let i = 1; i < clicks.length; i++) {
      const interval = clicks[i].timestamp - clicks[i - 1].timestamp
      clickIntervals.push(interval)
      
      if (interval < 500) { // 500ms内的点击认为是双击
        doubleClickCount++
      }
    }

    return {
      totalMoves: mouseMoves.length,
      averageSpeed: speeds.length > 0 ? speeds.reduce((a, b) => a + b, 0) / speeds.length : 0,
      maxSpeed: speeds.length > 0 ? Math.max(...speeds) : 0,
      acceleration,
      clickPattern: {
        totalClicks: clicks.length,
        averageClickInterval: clickIntervals.length > 0 ? clickIntervals.reduce((a, b) => a + b, 0) / clickIntervals.length : 0,
        doubleClickCount
      }
    }
  }

  /**
   * 计算键盘统计信息
   */
  private calculateKeyboardStatistics() {
    const keyDowns = this.keyboardEvents.filter(e => e.type === 'keydown')
    const keyUps = this.keyboardEvents.filter(e => e.type === 'keyup' && e.duration)
    
    let totalTypingTime = 0
    const pausePattern: number[] = []
    
    for (let i = 1; i < keyDowns.length; i++) {
      const interval = keyDowns[i].timestamp - keyDowns[i - 1].timestamp
      pausePattern.push(interval)
    }
    
    const durations = keyUps.map(e => e.duration!).filter(d => d > 0)
    if (durations.length > 0) {
      totalTypingTime = durations.reduce((a, b) => a + b, 0)
    }

    return {
      totalKeystrokes: keyDowns.length,
      averageTypingSpeed: keyDowns.length > 0 && totalTypingTime > 0 ? keyDowns.length / (totalTypingTime / 1000) : 0,
      pausePattern
    }
  }

  /**
   * 计算触摸统计信息
   */
  private calculateTouchStatistics() {
    const touches = this.touchEvents
    const pressures = touches.map(t => t.pressure).filter(p => p !== undefined) as number[]
    
    // 计算手势复杂度（基于触摸点的变化）
    let gestureComplexity = 0
    for (let i = 1; i < touches.length; i++) {
      const prev = touches[i - 1]
      const curr = touches[i]
      const distance = Math.sqrt(
        Math.pow(curr.x - prev.x, 2) + Math.pow(curr.y - prev.y, 2)
      )
      gestureComplexity += distance
    }

    return {
      totalTouches: touches.length,
      averagePressure: pressures.length > 0 ? pressures.reduce((a, b) => a + b, 0) / pressures.length : 0,
      gestureComplexity
    }
  }

  /**
   * 计算滚动统计信息
   */
  private calculateScrollStatistics() {
    const scrolls = this.scrollEvents
    const velocities: number[] = []
    const directions: string[] = []
    
    for (let i = 1; i < scrolls.length; i++) {
      const prev = scrolls[i - 1]
      const curr = scrolls[i]
      
      const deltaX = curr.x - prev.x
      const deltaY = curr.y - prev.y
      const timeDiff = curr.timestamp - prev.timestamp
      
      const velocity = timeDiff > 0 ? Math.sqrt(deltaX * deltaX + deltaY * deltaY) / timeDiff : 0
      velocities.push(velocity)
      
      if (Math.abs(deltaY) > Math.abs(deltaX)) {
        directions.push(deltaY > 0 ? 'down' : 'up')
      } else {
        directions.push(deltaX > 0 ? 'right' : 'left')
      }
    }
    
    const mainDirection = directions.length > 0 ? 
      directions.reduce((a, b, i, arr) => 
        arr.filter(v => v === a).length >= arr.filter(v => v === b).length ? a : b
      ) : 'none'

    return {
      totalScrolls: scrolls.length,
      scrollVelocity: velocities,
      scrollDirection: mainDirection
    }
  }

  /**
   * 计算人类行为相似度
   */
  private calculateHumanLikelihood(mouseStats: any, keyboardStats: any, touchStats: any): number {
    let score = 0
    let factors = 0

    // 鼠标移动的自然性（速度变化、加速度变化）
    if (mouseStats.acceleration.length > 0) {
      const accelVariance = this.calculateVariance(mouseStats.acceleration)
      const naturalAccel = Math.min(accelVariance / 1000, 1) // 归一化
      score += naturalAccel * 0.3
      factors += 0.3
    }

    // 点击模式的自然性
    if (mouseStats.clickPattern.totalClicks > 0) {
      const clickNaturalness = mouseStats.clickPattern.averageClickInterval > 100 && 
                              mouseStats.clickPattern.averageClickInterval < 2000 ? 1 : 0.5
      score += clickNaturalness * 0.2
      factors += 0.2
    }

    // 键盘输入的自然性
    if (keyboardStats.pausePattern.length > 0) {
      const pauseVariance = this.calculateVariance(keyboardStats.pausePattern)
      const naturalPause = Math.min(pauseVariance / 10000, 1) // 归一化
      score += naturalPause * 0.2
      factors += 0.2
    }

    // 触摸行为的自然性
    if (touchStats.totalTouches > 0) {
      const touchNaturalness = touchStats.averagePressure > 0 && touchStats.averagePressure < 1 ? 1 : 0.7
      score += touchNaturalness * 0.15
      factors += 0.15
    }

    // 整体行为的连贯性
    const totalEvents = mouseStats.totalMoves + keyboardStats.totalKeystrokes + touchStats.totalTouches
    if (totalEvents > 10) {
      score += 0.15
      factors += 0.15
    }

    return factors > 0 ? Math.min(score / factors, 1) : 0.5
  }

  /**
   * 计算方差
   */
  private calculateVariance(values: number[]): number {
    if (values.length === 0) return 0
    
    const mean = values.reduce((a, b) => a + b, 0) / values.length
    const variance = values.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / values.length
    
    return variance
  }

  /**
   * 获取当前状态
   */
  isCurrentlyTracking(): boolean {
    return this.isTracking
  }

  /**
   * 获取当前记录的事件数量
   */
  getEventCounts(): { mouse: number; keyboard: number; touch: number; scroll: number } {
    return {
      mouse: this.mouseEvents.length,
      keyboard: this.keyboardEvents.length,
      touch: this.touchEvents.length,
      scroll: this.scrollEvents.length
    }
  }
}

// 导出单例
export const behaviorTracker = new BehaviorTracker()

// 导出便捷方法
export function startBehaviorTracking(element?: HTMLElement): void {
  behaviorTracker.startTracking(element)
}

export function stopBehaviorTracking(): BehaviorPattern {
  return behaviorTracker.stopTracking()
}