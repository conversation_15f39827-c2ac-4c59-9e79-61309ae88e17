<template>
  <div class="forgot-password-page">
    <div class="forgot-password-container">
      <div class="forgot-password-header">
        <h1 class="forgot-password-title">找回密码</h1>
        <p class="forgot-password-subtitle">请选择找回密码的方式</p>
      </div>
      
      <!-- 找回方式切换 -->
      <div class="reset-type-switch">
        <el-radio-group v-model="resetMode" class="reset-type-group">
          <el-radio-button label="email">邮箱找回</el-radio-button>
          <el-radio-button label="phone">手机找回</el-radio-button>
        </el-radio-group>
      </div>
      
      <el-form
        ref="resetFormRef"
        :model="resetForm"
        :rules="resetRules"
        class="reset-form"
        size="large"
        @submit.prevent="handleReset"
      >
        <!-- 邮箱找回表单 -->
        <template v-if="resetMode === 'email'">
          <el-form-item prop="email">
            <el-input
              v-model="resetForm.email"
              placeholder="请输入邮箱地址"
              :prefix-icon="Message"
              clearable
            />
          </el-form-item>
          
          <el-form-item prop="emailCode">
            <div class="code-container">
              <el-input
                v-model="resetForm.emailCode"
                placeholder="请输入邮箱验证码"
                :prefix-icon="Key"
                clearable
                maxlength="6"
                @keyup.enter="handleReset"
              />
              <el-button
                type="primary"
                class="send-code-btn"
                :disabled="!canSendEmailCode || emailCountdown > 0"
                :loading="emailSending"
                @click="sendEmailCodeHandler"
              >
                {{ emailCountdown > 0 ? `${emailCountdown}s` : '发送验证码' }}
              </el-button>
            </div>
          </el-form-item>
        </template>
        
        <!-- 手机找回表单 -->
        <template v-else>
          <el-form-item prop="phone">
            <el-input
              v-model="resetForm.phone"
              placeholder="请输入手机号"
              :prefix-icon="Phone"
              clearable
              maxlength="11"
            />
          </el-form-item>
          
          <el-form-item prop="smsCode">
            <div class="code-container">
              <el-input
                v-model="resetForm.smsCode"
                placeholder="请输入短信验证码"
                :prefix-icon="Key"
                clearable
                maxlength="6"
                @keyup.enter="handleReset"
              />
              <el-button
                type="primary"
                class="send-code-btn"
                :disabled="!canSendSmsCode || smsCountdown > 0"
                :loading="smsSending"
                @click="sendSmsCodeHandler"
              >
                {{ smsCountdown > 0 ? `${smsCountdown}s` : '发送验证码' }}
              </el-button>
            </div>
          </el-form-item>
        </template>
        
        <!-- 新密码输入 -->
        <template v-if="showPasswordForm">
          <el-form-item prop="newPassword">
            <el-input
              v-model="resetForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              :prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>
          
          <el-form-item prop="confirmPassword">
            <el-input
              v-model="resetForm.confirmPassword"
              type="password"
              placeholder="请确认新密码"
              :prefix-icon="Lock"
              show-password
              clearable
              @keyup.enter="handleReset"
            />
          </el-form-item>
        </template>
        
        <el-form-item>
          <el-button
            type="primary"
            class="reset-button"
            :loading="loading"
            :disabled="loading"
            @click="handleReset"
          >
            {{ loading ? '处理中...' : (showPasswordForm ? '重置密码' : '验证身份') }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="reset-footer">
        <p class="back-link">
          <router-link to="/login" class="link">
            ← 返回登录
          </router-link>
        </p>
        
        <p class="register-link">
          还没有账号？
          <router-link to="/register" class="link">
            立即注册
          </router-link>
        </p>
      </div>
    </div>
    
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Message, Phone, Lock, Key } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const resetFormRef = ref<FormInstance>()
const showPasswordForm = ref(false)

// 重置模式：email（邮箱找回）或 phone（手机找回）
const resetMode = ref<'email' | 'phone'>('email')

// 验证码相关
const emailSending = ref(false)
const smsSending = ref(false)
const emailCountdown = ref(0)
const smsCountdown = ref(0)
let emailTimer: number | null = null
let smsTimer: number | null = null

// 重置表单
const resetForm = reactive({
  email: '',
  phone: '',
  emailCode: '',
  smsCode: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const resetRules = computed((): FormRules => {
  const rules: FormRules = {}
  
  if (resetMode.value === 'email') {
    rules.email = [
      { required: true, message: '请输入邮箱地址', trigger: 'blur' },
      { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
    ]
    rules.emailCode = [
      { required: true, message: '请输入邮箱验证码', trigger: 'blur' },
      { pattern: /^\d{6}$/, message: '验证码必须是6位数字', trigger: 'blur' }
    ]
  } else {
    rules.phone = [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号', trigger: 'blur' }
    ]
    rules.smsCode = [
      { required: true, message: '请输入短信验证码', trigger: 'blur' },
      { pattern: /^\d{6}$/, message: '验证码必须是6位数字', trigger: 'blur' }
    ]
  }
  
  if (showPasswordForm.value) {
    rules.newPassword = [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
    ]
    rules.confirmPassword = [
      { required: true, message: '请确认新密码', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value !== resetForm.newPassword) {
            callback(new Error('两次输入的密码不一致'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  }
  
  return rules
})

// 是否可以发送邮箱验证码
const canSendEmailCode = computed(() => {
  return resetForm.email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(resetForm.email)
})

// 是否可以发送短信验证码
const canSendSmsCode = computed(() => {
  return resetForm.phone && /^1[3-9]\d{9}$/.test(resetForm.phone)
})

// 发送邮箱验证码
const sendEmailCodeHandler = async () => {
  if (!canSendEmailCode.value) {
    ElMessage.warning('请输入有效的邮箱地址')
    return
  }
  
  try {
    emailSending.value = true
    const securityVerification = {
      level: 2,
      behaviorPattern: { verified: true },
      verificationTimestamp: Date.now(),
      type: 'behavior'
    }
    await userStore.sendEmailCode(resetForm.email, 'reset', securityVerification)
    ElMessage.success('验证码已发送到您的邮箱')
    
    // 开始倒计时
    emailCountdown.value = 60
    emailTimer = setInterval(() => {
      emailCountdown.value--
      if (emailCountdown.value <= 0) {
        if (emailTimer) {
          clearInterval(emailTimer)
        }
        emailTimer = null
      }
    }, 1000)
  } catch (error: any) {
    ElMessage.error(error.message || '发送验证码失败')
  } finally {
    emailSending.value = false
  }
}

// 发送短信验证码
const sendSmsCodeHandler = async () => {
  if (!canSendSmsCode.value) {
    ElMessage.warning('请输入有效的手机号')
    return
  }
  
  try {
    smsSending.value = true
    const securityVerification = {
      level: 2,
      behaviorPattern: { verified: true },
      verificationTimestamp: Date.now(),
      type: 'behavior'
    }
    await userStore.sendSmsCode(resetForm.phone, 'reset', securityVerification)
    ElMessage.success('验证码已发送')
    
    // 开始倒计时
    smsCountdown.value = 60
    smsTimer = setInterval(() => {
      smsCountdown.value--
      if (smsCountdown.value <= 0) {
        if (smsTimer) {
          clearInterval(smsTimer)
        }
        smsTimer = null
      }
    }, 1000)
  } catch (error: any) {
    ElMessage.error(error.message || '发送验证码失败')
  } finally {
    smsSending.value = false
  }
}

// 处理重置密码
const handleReset = async () => {
  if (!resetFormRef.value) return
  
  try {
    await resetFormRef.value.validate()
    
    if (!showPasswordForm.value) {
      // 第一步：验证身份
      await verifyIdentity()
    } else {
      // 第二步：重置密码
      await resetPassword()
    }
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

// 验证身份
const verifyIdentity = async () => {
  try {
    loading.value = true
    
    const verifyData = resetMode.value === 'email' 
      ? {
          email: resetForm.email,
          code: resetForm.emailCode,
          type: 'email' as const
        }
      : {
          phone: resetForm.phone,
          code: resetForm.smsCode,
          type: 'phone' as const
        }
    
    const response = await userStore.verifyResetCode(verifyData)
    
    if (response) {
      ElMessage.success('身份验证成功，请设置新密码')
      showPasswordForm.value = true
    }
  } catch (error: any) {
    ElMessage.error(error.message || '验证失败，请检查验证码')
  } finally {
    loading.value = false
  }
}

// 重置密码
const resetPassword = async () => {
  try {
    loading.value = true
    
    const resetData = {
      newPassword: resetForm.newPassword,
      confirmPassword: resetForm.confirmPassword,
      ...(resetMode.value === 'email' 
        ? {
            email: resetForm.email,
            emailCode: resetForm.emailCode,
            type: 'email' as const
          }
        : {
            phone: resetForm.phone,
            smsCode: resetForm.smsCode,
            type: 'phone' as const
          }
      )
    }
    
    const response = await userStore.resetPasswordWithCode(resetData)
    
    if (response) {
      ElMessage.success('密码重置成功，请使用新密码登录')
      router.push('/login')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '密码重置失败')
  } finally {
    loading.value = false
  }
}

// 组件销毁时清理定时器
onUnmounted(() => {
  if (emailTimer) {
    clearInterval(emailTimer)
  }
  if (smsTimer) {
    clearInterval(smsTimer)
  }
})
</script>

<style scoped>
.forgot-password-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  padding: 20px;
  box-sizing: border-box;
}

.forgot-password-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  width: 100%;
  max-width: 420px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
  margin: auto;
}

.forgot-password-header {
  text-align: center;
  margin-bottom: 32px;
}

.forgot-password-title {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.forgot-password-subtitle {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0;
}

/* 重置方式切换样式 */
.reset-type-switch {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
}

.reset-type-group {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.reset-type-group :deep(.el-radio-button__inner) {
  border: none;
  background: transparent;
  color: #606266;
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.reset-type-group :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background: #409eff;
  color: white;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.reset-form {
  margin-bottom: 24px;
}

.reset-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.reset-form :deep(.el-input__wrapper) {
  height: 48px;
  border-radius: 12px;
  border: 2px solid #e1e8ed;
  font-size: 16px;
  transition: all 0.3s ease;
  padding: 0 16px;
  box-shadow: none;
}

.reset-form :deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc;
}

.reset-form :deep(.el-input.is-focus .el-input__wrapper) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.reset-form :deep(.el-input__prefix) {
  left: 16px;
}

.reset-form :deep(.el-input__prefix .el-icon) {
  font-size: 18px;
  color: #909399;
}

.reset-form :deep(.el-input__inner) {
  height: 44px;
  line-height: 44px;
  padding: 0 40px 0 0;
}

.code-container {
  display: flex;
  gap: 12px;
  width: 100%;
}

.code-container .el-input {
  flex: 1;
}

.send-code-btn {
  height: 48px;
  padding: 0 20px;
  border-radius: 12px;
  font-weight: 500;
  white-space: nowrap;
  min-width: 120px;
  transition: all 0.3s ease;
}

.send-code-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.send-code-btn:disabled {
  background-color: #c0c4cc !important;
  border-color: #c0c4cc !important;
  color: #ffffff !important;
  cursor: not-allowed;
  opacity: 0.8;
}

.reset-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.reset-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.reset-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.reset-footer {
  text-align: center;
}

.back-link,
.register-link {
  color: #606266;
  font-size: 14px;
  margin: 0 0 12px 0;
}

.link {
  color: #409eff;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.link:hover {
  color: #66b1ff;
  text-decoration: underline;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 120px;
  height: 120px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 80px;
  height: 80px;
  top: 70%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .forgot-password-page {
    padding: 10px;
  }
  
  .forgot-password-container {
    padding: 24px;
    border-radius: 16px;
    max-width: 100%;
  }
  
  .forgot-password-title {
    font-size: 24px;
  }
  
  .forgot-password-subtitle {
    font-size: 14px;
  }
  
  .reset-type-group :deep(.el-radio-button__inner) {
    padding: 10px 16px;
    font-size: 14px;
  }
  
  .code-container {
    flex-direction: column;
    gap: 12px;
  }
  
  .send-code-btn {
    width: 100%;
    min-width: auto;
    height: 44px;
  }
  
  .reset-form :deep(.el-input__wrapper) {
    height: 44px;
  }
  
  .reset-form :deep(.el-input__inner) {
    height: 40px;
    line-height: 40px;
  }
  
  .reset-button {
    height: 44px;
  }
  
  .circle-1 {
    width: 80px;
    height: 80px;
  }
  
  .circle-2 {
    width: 60px;
    height: 60px;
  }
  
  .circle-3 {
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 480px) {
  .forgot-password-page {
    padding: 5px;
  }
  
  .forgot-password-container {
    padding: 20px;
    border-radius: 12px;
  }
  
  .forgot-password-header {
    margin-bottom: 24px;
  }
  
  .forgot-password-title {
    font-size: 22px;
  }
  
  .reset-type-switch {
    margin-bottom: 20px;
  }
  
  .reset-form {
    margin-bottom: 20px;
  }
  
  .reset-form :deep(.el-form-item) {
    margin-bottom: 16px;
  }
}
</style>