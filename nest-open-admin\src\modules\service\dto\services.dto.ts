import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsObject,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsBoolean,
  IsUUID,
  IsDateString,
  Min,
  Max,
} from 'class-validator';

/**
 * 服务类型枚举
 */
export enum ServiceType {
  OCR = 'OCR',       // 光学字符识别（文字识别服务）
  NLP = 'NLP',       // 自然语言处理（文本分析、语义理解等）
  CV = 'CV',         // 计算机视觉（图像/视频分析）
  AI = 'AI',         // 人工智能（通用AI服务）
  DATA = 'DATA',     // 数据处理（数据存储、清洗、分析等）
  OTHER = 'OTHER',   // 其他（未分类的服务类型）
}

/**
 * 服务状态枚举
 */
export enum ServiceStatus {
  ACTIVE = 'active',       // 活跃的（服务正常运行）
  INACTIVE = 'inactive',   // 未激活的（服务未启用）
  MAINTENANCE = 'maintenance', // 维护中（服务暂时不可用）
  DEPRECATED = 'deprecated',   // 已弃用（服务不再支持或即将下线）
}
/**
 * 定价模式枚举
 */
export enum PricingModel {
  FREE = 'free',       // 免费
  PAY_PER_USE = 'pay_per_use',   // 按次付费
  SUBSCRIPTION = 'subscription', // 订阅制
  TIERED = 'tiered',   // 分层定价
}
/**
 * 创建服务DTO
 */
export class CreateServiceDto {
  @ApiProperty({ description: '服务代码', example: 'OCR' })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({ description: '服务名称', example: '光学字符识别' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({ description: '服务描述', example: '图像文字识别服务' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '服务类型', enum: ServiceType, example: ServiceType.OCR })
  @IsEnum(ServiceType)
  type: ServiceType;

  @ApiProperty({ description: '服务版本', example: 'v1.0.0' })
  @IsString()
  @IsNotEmpty()
  version: string;

  @ApiProperty({ description: '服务端点', example: 'http://localhost:3001/api/ocr' })
  @IsString()
  @IsNotEmpty()
  endpoint: string;

  @ApiPropertyOptional({ description: '服务配置', example: { timeout: 30000, retries: 3 } })
  @IsOptional()
  @IsObject()
  configuration?: Record<string, any>;

  @ApiPropertyOptional({ description: '服务价格（每次调用）', example: 0.01, minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  price?: number;

  @ApiPropertyOptional({ description: '每日调用限制', example: 1000, minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  dailyLimit?: number;

  @ApiPropertyOptional({ description: '每分钟调用限制', example: 100, minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  minuteLimit?: number;

  @ApiPropertyOptional({ description: '是否需要认证', example: true, default: true })
  @IsOptional()
  @IsBoolean()
  requireAuth?: boolean;

  @ApiPropertyOptional({ description: '是否异步处理', example: true, default: false })
  @IsOptional()
  @IsBoolean()
  isAsync?: boolean;

  @ApiPropertyOptional({ description: '超时时间（毫秒）', example: 30000, minimum: 1000, maximum: 300000 })
  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(300000)
  timeout?: number;
}

/**
 * 更新服务DTO
 */
export class UpdateServiceDto {
  @ApiPropertyOptional({ description: '服务名称', example: '光学字符识别' })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiPropertyOptional({ description: '服务描述', example: '图像文字识别服务' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: '服务类型', enum: ServiceType })
  @IsOptional()
  @IsEnum(ServiceType)
  type?: ServiceType;

  @ApiPropertyOptional({ description: '服务版本', example: 'v1.0.1' })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  version?: string;

  @ApiPropertyOptional({ description: '服务端点', example: 'http://localhost:3001/api/ocr' })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  endpoint?: string;

  @ApiPropertyOptional({ description: '服务配置' })
  @IsOptional()
  @IsObject()
  configuration?: Record<string, any>;

  @ApiPropertyOptional({ description: '服务价格（每次调用）', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  price?: number;

  @ApiPropertyOptional({ description: '每日调用限制', minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  dailyLimit?: number;

  @ApiPropertyOptional({ description: '每分钟调用限制', minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  minuteLimit?: number;

  @ApiPropertyOptional({ description: '是否需要认证' })
  @IsOptional()
  @IsBoolean()
  requireAuth?: boolean;

  @ApiPropertyOptional({ description: '是否异步处理' })
  @IsOptional()
  @IsBoolean()
  isAsync?: boolean;

  @ApiPropertyOptional({ description: '超时时间（毫秒）', minimum: 1000, maximum: 300000 })
  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(300000)
  timeout?: number;
}

/**
 * 服务响应DTO
 */
export class ServiceResponseDto {
  @ApiProperty({ description: '服务ID', example: 1 })
  id: number;

  @ApiProperty({ description: '服务代码', example: 'OCR' })
  code: string;

  @ApiProperty({ description: '服务名称', example: '光学字符识别' })
  name: string;

  @ApiPropertyOptional({ description: '服务描述', example: '图像文字识别服务' })
  description?: string;

  @ApiProperty({ description: '服务类型', enum: ServiceType })
  type: ServiceType;

  @ApiProperty({ description: '服务版本', example: 'v1.0.0' })
  version: string;

  @ApiProperty({ description: '服务端点', example: 'http://localhost:3001/api/ocr' })
  endpoint: string;

  @ApiPropertyOptional({ description: '服务配置' })
  configuration?: Record<string, any>;

  @ApiProperty({ description: '调用次数', example: 1500 })
  callCount: number;

  @ApiPropertyOptional({ description: '服务价格（每次调用）', example: 0.01 })
  price?: number;

  @ApiPropertyOptional({ description: '每日调用限制', example: 1000 })
  dailyLimit?: number;

  @ApiPropertyOptional({ description: '每分钟调用限制', example: 100 })
  minuteLimit?: number;

  @ApiProperty({ description: '是否需要认证', example: true })
  requireAuth: boolean;

  @ApiProperty({ description: '是否异步处理', example: true })
  isAsync: boolean;

  @ApiPropertyOptional({ description: '超时时间（毫秒）', example: 30000 })
  timeout?: number;

  @ApiProperty({ description: '服务状态', enum: ServiceStatus })
  status: ServiceStatus;

  @ApiProperty({ description: '创建时间', example: '2024-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间', example: '2024-01-01T00:00:00.000Z' })
  updatedAt: Date;
}

/**
 * 服务列表响应DTO
 */
export class ServicesListResponseDto {
  @ApiProperty({ description: '服务列表', type: [ServiceResponseDto] })
  services: ServiceResponseDto[];

  @ApiProperty({ description: '总数', example: 50 })
  total: number;

  @ApiProperty({ description: '当前页', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  limit: number;
}

// ==================== 服务版本相关DTO ====================

/**
 * 创建服务版本DTO
 */
export class CreateServiceVersionDto {
  @ApiProperty({ description: '版本号', example: 'v1.1.0' })
  @IsString()
  @IsNotEmpty()
  version: string;

  @ApiPropertyOptional({ description: '版本描述', example: '新增图像预处理功能' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '端点URL', example: 'http://localhost:3001/api/ocr/v1.1' })
  @IsString()
  @IsNotEmpty()
  endpointUrl: string;

  @ApiPropertyOptional({ description: 'API模式定义' })
  @IsOptional()
  @IsObject()
  apiSchema?: Record<string, any>;

  @ApiPropertyOptional({ description: '版本配置' })
  @IsOptional()
  @IsObject()
  configuration?: Record<string, any>;

  @ApiPropertyOptional({ description: '是否兼容旧版本', default: true })
  @IsOptional()
  @IsBoolean()
  isCompatible?: boolean;

  @ApiPropertyOptional({ description: '发布日期' })
  @IsOptional()
  @IsDateString()
  releaseDate?: Date;

  @ApiPropertyOptional({ description: '弃用日期' })
  @IsOptional()
  @IsDateString()
  deprecationDate?: Date;
}

/**
 * 更新服务版本DTO
 */
export class UpdateServiceVersionDto {
  @ApiPropertyOptional({ description: '版本描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: '端点URL' })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  endpointUrl?: string;

  @ApiPropertyOptional({ description: 'API模式定义' })
  @IsOptional()
  @IsObject()
  apiSchema?: Record<string, any>;

  @ApiPropertyOptional({ description: '版本配置' })
  @IsOptional()
  @IsObject()
  configuration?: Record<string, any>;

  @ApiPropertyOptional({ description: '是否兼容旧版本' })
  @IsOptional()
  @IsBoolean()
  isCompatible?: boolean;

  @ApiPropertyOptional({ description: '发布日期' })
  @IsOptional()
  @IsDateString()
  releaseDate?: Date;

  @ApiPropertyOptional({ description: '弃用日期' })
  @IsOptional()
  @IsDateString()
  deprecationDate?: Date;
}

/**
 * 服务版本响应DTO
 */
export class ServiceVersionResponseDto {
  @ApiProperty({ description: '版本ID', example: 1 })
  id: number;

  @ApiProperty({ description: '服务ID', example: 1 })
  serviceId: number;

  @ApiProperty({ description: '版本号', example: 'v1.1.0' })
  version: string;

  @ApiPropertyOptional({ description: '版本描述', example: '新增图像预处理功能' })
  description?: string;

  @ApiProperty({ description: '端点URL', example: 'http://localhost:3001/api/ocr/v1.1' })
  endpointUrl: string;

  @ApiPropertyOptional({ description: 'API模式定义' })
  apiSchema?: Record<string, any>;

  @ApiPropertyOptional({ description: '版本配置' })
  configuration?: Record<string, any>;

  @ApiProperty({ description: '是否为当前版本', example: true })
  isCurrent: boolean;

  @ApiProperty({ description: '是否兼容旧版本', example: true })
  isCompatible: boolean;

  @ApiPropertyOptional({ description: '发布日期', example: '2024-01-01T00:00:00.000Z' })
  releaseDate?: Date;

  @ApiPropertyOptional({ description: '弃用日期', example: '2024-12-31T00:00:00.000Z' })
  deprecationDate?: Date;

  @ApiProperty({ description: '创建时间', example: '2024-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间', example: '2024-01-01T00:00:00.000Z' })
  updatedAt: Date;
}

/**
 * 服务版本列表响应DTO
 */
export class ServiceVersionsListResponseDto {
  @ApiProperty({ description: '版本列表', type: [ServiceVersionResponseDto] })
  versions: ServiceVersionResponseDto[];

  @ApiProperty({ description: '总数', example: 5 })
  total: number;
}