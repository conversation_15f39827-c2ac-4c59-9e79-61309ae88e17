/**
 * 队列配置文件
 * 包含Redis连接和队列配置信息
 */
export const QUEUE_CONFIG = {
  // Redis连接配置
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    db: 0,
  },
  
  // 队列配置
  queues: {
    OCR_QUEUE: {
      name: 'ocr',  // 更新为与网关和Python服务一致的队列名称
      concurrency: 5,               // 并发处理任务数
      attempts: 3,                  // 失败重试次数
      backoff: {                    // 重试延迟策略
        type: 'exponential',
        delay: 5000,                // 初始延迟时间(ms)
      },
      removeOnComplete: true,       // 完成后删除任务
      removeOnFail: false,          // 失败后保留任务
      defaultJobOptions: {
        timeout: 180000,            // 3分钟超时
        priority: 1,                // 默认优先级
      }
    },

    STO_OCR_QUEUE: {
      name: 'sto-ocr',                 // 申通OCR专用队列
      concurrency: 3,                 // 并发数较低以保证质量
      attempts: 3,                    // 失败重试次数
      backoff: {                      // 重试延迟策略
        type: 'exponential',
        delay: 5000,                  // 初始延迟时间(ms)
      },
      removeOnComplete: true,         // 完成后删除任务
      removeOnFail: false,            // 失败后保留任务
      defaultJobOptions: {
        timeout: 200000,              // 申通OCR可能需要更长处理时间
        priority: 2,                  // 稍高优先级
      }
    },

    ADDRESS_QUEUE: {
      name: 'extract-address',  // 更新为与网关和Python服务一致的队列名称
      concurrency: 8,
      attempts: 2,
      backoff: {
        type: 'exponential',
        delay: 3000,
      },
      removeOnComplete: true,
      removeOnFail: false,
      defaultJobOptions: {
        timeout: 60000,             // 1分钟超时
        priority: 1,
      }
    },
    
    GEO_QUEUE: {
      name: 'rev-geo',  // 更新为与网关和Python服务一致的队列名称
      concurrency: 10,              // 并发处理任务数
      attempts: 2,                  // 失败重试次数
      backoff: {                    // 重试延迟策略
        type: 'exponential',
        delay: 3000,                // 初始延迟时间(ms)
      },
      removeOnComplete: true,       // 完成后删除任务
      removeOnFail: false,          // 失败后保留任务
      defaultJobOptions: {
        timeout: 30000,             // 30秒超时
        priority: 1,                // 默认优先级
      }
    },
  },
};

// 队列优先级定义
export enum QueuePriority {
  LOW = 10,
  NORMAL = 5,
  HIGH = 1,
  CRITICAL = 0,
} 