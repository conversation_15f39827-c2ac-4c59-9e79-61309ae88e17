import { <PERSON>, <PERSON>, Para<PERSON>, <PERSON><PERSON>, Header, NotFoundException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import { TaskService } from '../services/task.service';
import { StructuredLogger } from '../../../common/logging/structured-logger';
import { TaskStatus } from '../interfaces/task-status.enum';

/**
 * 任务事件控制器
 * 提供任务状态的SSE推送服务
 */
@ApiTags('任务事件')
@Controller('tasks')
export class TaskEventsController {
  constructor(
    private taskService: TaskService,
    private logger: StructuredLogger,
  ) {}

  /**
   * 获取任务状态的SSE流
   */
  @Get(':taskId/events')
  @Header('Content-Type', 'text/event-stream')
  @Header('Cache-Control', 'no-cache')
  @Header('Connection', 'keep-alive')
  @ApiOperation({
    summary: '获取任务状态的SSE流',
    description: '通过SSE接收任务状态的实时更新',
  })
  @ApiParam({
    name: 'taskId',
    description: '任务ID',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'SSE事件流',
  })
  @ApiResponse({
    status: 404,
    description: '任务不存在',
  })
  async streamTaskEvents(
    @Param('taskId') taskId: string,
    @Res() response: Response,
  ): Promise<void> {
    try {
      // 验证任务ID
      const taskExists = await this.taskService.checkTaskExists(taskId);
      if (!taskExists) {
        response.status(404).json({
          code: 404,
          message: `任务${taskId}不存在`,
        });
        return;
      }
      
      // 设置SSE头部
      response.flushHeaders();
      
      // 初始化SSE连接
      const sendEvent = (data: any) => {
        response.write(`data: ${JSON.stringify(data)}\n\n`);
      };
      
      // 发送初始状态
      const initialStatus = await this.taskService.getTaskStatus(taskId);
      sendEvent({
        ...initialStatus,
        timestamp: Date.now(),
      });
      
      // 如果任务已完成或失败，直接结束连接
      if (
        initialStatus.status === TaskStatus.COMPLETED ||
        initialStatus.status === TaskStatus.FAILED
      ) {
        response.end();
        return;
      }
      
      // 订阅任务更新
      const subscription = this.taskService.subscribeToTaskUpdates(
        taskId,
        (update) => {
          sendEvent({
            ...update,
            timestamp: Date.now(),
          });
          
          // 如果任务已完成或失败，关闭连接
          if (
            update.status === TaskStatus.COMPLETED ||
            update.status === TaskStatus.FAILED
          ) {
            this.logger.debug(
              `任务${taskId}已${update.status}，关闭SSE连接`,
              { module: 'TaskEventsController' }
            );
            response.end();
          }
        }
      );
      
      // 处理客户端断开连接
      response.on('close', () => {
        this.logger.debug(
          `客户端断开了与任务${taskId}的SSE连接`,
          { module: 'TaskEventsController' }
        );
        subscription.unsubscribe();
      });
      
      // 设置连接超时保护（1小时）
      setTimeout(() => {
        if (!response.writableEnded) {
          sendEvent({
            taskId,
            status: TaskStatus.FAILED,
            error: '连接超时',
            timestamp: Date.now(),
          });
          response.end();
          subscription.unsubscribe();
        }
      }, 3600000);
      
    } catch (error) {
      this.logger.error(
        `为任务${taskId}创建SSE流时发生错误`,
        error,
        { module: 'TaskEventsController' }
      );
      
      if (!response.writableEnded) {
        response.status(500).json({
          code: 500,
          message: '服务器内部错误',
          error: error.message,
        });
      }
    }
  }
} 