const mysql = require('mysql2/promise');

async function checkTables() {
  const connection = await mysql.createConnection({
    host: 'localhost', 
    user: 'root', 
    password: '123456', 
    database: 'openapidb'
  });
  
  try {
    console.log('=== 查询数据库中的所有表 ===');
    const [tables] = await connection.execute('SHOW TABLES');
    
    console.log('数据库中的表:');
    tables.forEach(table => {
      console.log(`- ${Object.values(table)[0]}`);
    });
    
    // 查看user_service表的结构，了解服务是如何定义的
    console.log('\n=== user_service表结构 ===');
    const [userServiceColumns] = await connection.execute('DESCRIBE user_service');
    userServiceColumns.forEach(col => {
      console.log(`${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Key} ${col.Default || ''}`);
    });
    
    // 查看user_service表中的service_id都有哪些
    console.log('\n=== user_service表中的服务ID ===');
    const [serviceIds] = await connection.execute('SELECT DISTINCT service_id FROM user_service ORDER BY service_id');
    serviceIds.forEach(row => {
      console.log(`服务ID: ${row.service_id}`);
    });
    
    // 查看用户4的所有服务
    console.log('\n=== 用户4的服务详情 ===');
    const [userServices] = await connection.execute('SELECT * FROM user_service WHERE user_id = 4 ORDER BY service_id');
    userServices.forEach(service => {
      console.log(`服务ID: ${service.service_id}, 总次数: ${service.total_count}, 已用: ${service.used_count}, 剩余: ${service.remaining_count}`);
    });
    
  } catch (error) {
    console.error('查询失败:', error.message);
  } finally {
    await connection.end();
  }
}

checkTables();
