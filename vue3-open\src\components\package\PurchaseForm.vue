<template>
  <div class="purchase-form">
    <!-- 套餐包信息 -->
    <div class="package-summary">
      <div class="package-info">
        <h3 class="package-name">{{ package.name }}</h3>
        <p class="package-description">{{ package.description }}</p>
      </div>
      
      <div class="package-pricing">
        <div class="price-main">
          <span class="price">¥{{ (package.price / 100).toFixed(2) }}</span>
          <span v-if="package.originalPrice" class="original-price">
            ¥{{ (package.originalPrice / 100).toFixed(2) }}
          </span>
          <span class="billing-type">{{ getBillingTypeLabel(package.billingType) }}</span>
        </div>
        
        <div v-if="package.discount" class="discount-info">
          <el-tag type="warning" size="small">
            限时优惠 {{ package.discount }}折
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 购买表单 -->
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="purchase-form-content"
    >
      <!-- 购买数量 -->
      <el-form-item label="购买数量" prop="quantity">
        <el-input-number
          v-model="form.quantity"
          :min="1"
          :max="maxQuantity"
          style="width: 100%;"
          @change="updateTotalAmount"
        />
        <div class="form-tip">
          单次最多购买 {{ maxQuantity }} 个
        </div>
      </el-form-item>

      <!-- 支付方式 -->
      <el-form-item label="支付方式" prop="paymentMethod">
        <el-radio-group v-model="form.paymentMethod" class="payment-methods">
          <el-radio value="alipay" class="payment-option">
            <div class="payment-item">
              <el-icon class="payment-icon alipay"><CreditCard /></el-icon>
              <span>支付宝</span>
            </div>
          </el-radio>
          
          <el-radio value="wechat" class="payment-option">
            <div class="payment-item">
              <el-icon class="payment-icon wechat"><Wallet /></el-icon>
              <span>微信支付</span>
            </div>
          </el-radio>
          
          <el-radio value="bank_card" class="payment-option">
            <div class="payment-item">
              <el-icon class="payment-icon bank"><CreditCard /></el-icon>
              <span>银行卡</span>
            </div>
          </el-radio>
          
          <el-radio value="balance" class="payment-option">
            <div class="payment-item">
              <el-icon class="payment-icon balance"><Coin /></el-icon>
              <span>账户余额</span>
              <span class="balance-amount">(¥{{ (userBalance / 100).toFixed(2) }})</span>
            </div>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 自动续费 -->
      <el-form-item v-if="package.billingType !== 'one_time'" label="自动续费">
        <div class="auto-renew-section">
          <el-switch v-model="form.autoRenew" />
          <div class="auto-renew-info">
            <span class="auto-renew-text">开启后将在到期前自动续费</span>
            <div class="auto-renew-tip">
              可在个人中心随时关闭自动续费
            </div>
          </div>
        </div>
      </el-form-item>

      <!-- 优惠券 -->
      <el-form-item label="优惠券" prop="couponCode">
        <div class="coupon-section">
          <el-input
            v-model="form.couponCode"
            placeholder="请输入优惠券代码"
            class="coupon-input"
          >
            <template #append>
              <el-button @click="applyCoupon" :loading="applyingCoupon">
                {{ appliedCoupon ? '已应用' : '使用' }}
              </el-button>
            </template>
          </el-input>
          
          <div v-if="availableCoupons.length > 0" class="available-coupons">
            <div class="coupons-title">可用优惠券：</div>
            <div class="coupons-list">
              <div 
                v-for="coupon in availableCoupons" 
                :key="coupon.id"
                class="coupon-item"
                :class="{ selected: form.couponCode === coupon.code }"
                @click="selectCoupon(coupon)"
              >
                <div class="coupon-info">
                  <span class="coupon-name">{{ coupon.name }}</span>
                  <span class="coupon-desc">{{ coupon.description }}</span>
                </div>
                <div class="coupon-value">
                  {{ coupon.type === 'percentage' ? `${coupon.value}%` : `¥${(coupon.value / 100).toFixed(2)}` }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-form-item>

      <!-- 发票信息 -->
      <el-form-item label="发票">
        <el-checkbox v-model="form.needInvoice">需要发票</el-checkbox>
        
        <div v-if="form.needInvoice" class="invoice-section">
          <el-form-item label="发票类型" prop="invoiceType">
            <el-radio-group v-model="form.invoiceType">
              <el-radio value="personal">个人</el-radio>
              <el-radio value="company">企业</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item 
            v-if="form.invoiceType === 'company'" 
            label="公司名称" 
            prop="companyName"
          >
            <el-input v-model="form.companyName" placeholder="请输入公司名称" />
          </el-form-item>
          
          <el-form-item 
            v-if="form.invoiceType === 'company'" 
            label="税号" 
            prop="taxNumber"
          >
            <el-input v-model="form.taxNumber" placeholder="请输入税号" />
          </el-form-item>
        </div>
      </el-form-item>
    </el-form>

    <!-- 费用明细 -->
    <div class="cost-breakdown">
      <div class="breakdown-title">费用明细</div>
      
      <div class="breakdown-item">
        <span>套餐包单价</span>
        <span>¥{{ (package.price / 100).toFixed(2) }}</span>
      </div>
      
      <div class="breakdown-item">
        <span>购买数量</span>
        <span>× {{ form.quantity }}</span>
      </div>
      
      <div class="breakdown-item">
        <span>小计</span>
        <span>¥{{ (subtotal / 100).toFixed(2) }}</span>
      </div>
      
      <div v-if="couponDiscount > 0" class="breakdown-item discount">
        <span>优惠券减免</span>
        <span>-¥{{ (couponDiscount / 100).toFixed(2) }}</span>
      </div>
      
      <div class="breakdown-divider"></div>
      
      <div class="breakdown-item total">
        <span>总计</span>
        <span>¥{{ (totalAmount / 100).toFixed(2) }}</span>
      </div>
    </div>

    <!-- 服务条款 -->
    <div class="terms-section">
      <el-checkbox v-model="form.agreeTerms" :required="true">
        我已阅读并同意
        <el-link type="primary" @click="showTerms">《服务条款》</el-link>
        和
        <el-link type="primary" @click="showPrivacy">《隐私政策》</el-link>
      </el-checkbox>
    </div>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button size="large" @click="handleCancel">
        取消
      </el-button>
      
      <el-button 
        type="primary" 
        size="large" 
        @click="handleSubmit"
        :loading="submitting"
        :disabled="!form.agreeTerms || (form.paymentMethod === 'balance' && totalAmount > userBalance)"
      >
        {{ getSubmitButtonText() }}
      </el-button>
    </div>

    <!-- 余额不足提示 -->
    <div v-if="form.paymentMethod === 'balance' && totalAmount > userBalance" class="insufficient-balance">
      <el-alert
        title="账户余额不足"
        type="warning"
        :description="`当前余额：¥${(userBalance / 100).toFixed(2)}，还需：¥${((totalAmount - userBalance) / 100).toFixed(2)}`"
        show-icon
        :closable="false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { CreditCard, Wallet, Coin } from '@element-plus/icons-vue'
import { usePackageStore } from '../../stores/package'
import type { Package, PurchasePackageForm } from '../../types/package'

interface Props {
  package: Package
}

interface Emits {
  success: []
  cancel: []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const packageStore = usePackageStore()

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const applyingCoupon = ref(false)
const appliedCoupon = ref<any>(null)
const maxQuantity = 10
const userBalance = ref(50000) // 模拟用户余额，单位：分

// 表单数据
const form = reactive<PurchasePackageForm & {
  needInvoice: boolean
  invoiceType: string
  companyName: string
  taxNumber: string
  agreeTerms: boolean
}>({
  packageId: props.package.id,
  quantity: 1,
  paymentMethod: 'alipay',
  autoRenew: false,
  couponCode: '',
  needInvoice: false,
  invoiceType: 'personal',
  companyName: '',
  taxNumber: '',
  agreeTerms: false
})

// 表单验证规则
const rules = {
  quantity: [
    { required: true, message: '请输入购买数量', trigger: 'blur' },
    { type: 'number', min: 1, max: maxQuantity, message: `数量必须在1-${maxQuantity}之间`, trigger: 'blur' }
  ],
  paymentMethod: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ],
  companyName: [
    { required: true, message: '请输入公司名称', trigger: 'blur' }
  ],
  taxNumber: [
    { required: true, message: '请输入税号', trigger: 'blur' }
  ]
}

// 模拟可用优惠券
const availableCoupons = ref([
  {
    id: '1',
    code: 'WELCOME10',
    name: '新用户优惠券',
    description: '首次购买立减',
    type: 'percentage',
    value: 10
  },
  {
    id: '2',
    code: 'SAVE50',
    name: '满减优惠券',
    description: '满100减50',
    type: 'fixed',
    value: 5000
  }
])

// 计算属性
const subtotal = computed(() => props.package.price * (form.quantity || 1))

const couponDiscount = computed(() => {
  if (!appliedCoupon.value) return 0
  
  if (appliedCoupon.value.type === 'percentage') {
    return Math.floor(subtotal.value * appliedCoupon.value.value / 100)
  } else {
    return Math.min(appliedCoupon.value.value, subtotal.value)
  }
})

const totalAmount = computed(() => Math.max(0, subtotal.value - couponDiscount.value))

// 方法
const getBillingTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    one_time: '一次性',
    monthly: '/ 月',
    yearly: '/ 年',
    usage_based: '按量付费'
  }
  return labels[type] || type
}

const updateTotalAmount = () => {
  // 数量变化时重新计算总金额
}

const applyCoupon = async () => {
  if (!form.couponCode?.trim()) {
    ElMessage.warning('请输入优惠券代码')
    return
  }
  
  applyingCoupon.value = true
  
  try {
    // 模拟验证优惠券
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const coupon = availableCoupons.value.find(c => c.code === form.couponCode)
    if (coupon) {
      appliedCoupon.value = coupon
      ElMessage.success('优惠券应用成功')
    } else {
      ElMessage.error('优惠券无效或已过期')
    }
  } catch (error) {
    console.error('优惠券验证失败:', error)
    ElMessage.error('优惠券验证失败')
  } finally {
    applyingCoupon.value = false
  }
}

const selectCoupon = (coupon: any) => {
  form.couponCode = coupon.code
  appliedCoupon.value = coupon
  ElMessage.success('优惠券已选择')
}

const showTerms = () => {
  window.open('/terms', '_blank')
}

const showPrivacy = () => {
  window.open('/privacy', '_blank')
}

const getSubmitButtonText = () => {
  if (form.paymentMethod === 'balance') {
    return totalAmount.value > userBalance.value ? '余额不足' : '确认购买'
  }
  return '确认购买'
}

const handleCancel = () => {
  emit('cancel')
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    if (!form.agreeTerms) {
      ElMessage.warning('请先同意服务条款和隐私政策')
      return
    }
    
    if (form.paymentMethod === 'balance' && totalAmount.value > userBalance.value) {
      ElMessage.error('账户余额不足')
      return
    }
    
    submitting.value = true
    
    // 构建购买数据
    const purchaseData: PurchasePackageForm = {
      packageId: form.packageId,
      quantity: form.quantity,
      paymentMethod: form.paymentMethod,
      autoRenew: form.autoRenew,
      couponCode: appliedCoupon.value?.code || ''
    }
    
    await packageStore.purchasePackage(purchaseData)
    
    ElMessage.success('购买成功！')
    emit('success')
    
  } catch (error) {
    console.error('购买失败:', error)
    ElMessage.error('购买失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 生命周期
onMounted(() => {
  // 获取用户余额等信息
})
</script>

<style scoped>
.purchase-form {
  max-width: 100%;
}

.package-summary {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
}

.package-info {
  margin-bottom: 16px;
}

.package-name {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1f2937;
}

.package-description {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.package-pricing {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-main {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.price {
  font-size: 24px;
  font-weight: 700;
  color: #3b82f6;
}

.original-price {
  font-size: 16px;
  color: #9ca3af;
  text-decoration: line-through;
}

.billing-type {
  font-size: 14px;
  color: #6b7280;
}

.discount-info {
  flex-shrink: 0;
}

.purchase-form-content {
  margin-bottom: 24px;
}

.form-tip {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.payment-methods {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  width: 100%;
}

.payment-option {
  margin: 0;
  width: 100%;
}

.payment-option :deep(.el-radio__input) {
  display: none;
}

.payment-option :deep(.el-radio__label) {
  padding: 0;
  width: 100%;
}

.payment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
}

.payment-option.is-checked .payment-item {
  border-color: #3b82f6;
  background: #eff6ff;
}

.payment-icon {
  width: 20px;
  height: 20px;
}

.payment-icon.alipay {
  color: #1677ff;
}

.payment-icon.wechat {
  color: #07c160;
}

.payment-icon.bank {
  color: #f56565;
}

.payment-icon.balance {
  color: #f59e0b;
}

.balance-amount {
  font-size: 12px;
  color: #6b7280;
  margin-left: 4px;
}

.auto-renew-section {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.auto-renew-info {
  flex: 1;
}

.auto-renew-text {
  display: block;
  color: #374151;
  font-size: 14px;
  margin-bottom: 4px;
}

.auto-renew-tip {
  font-size: 12px;
  color: #6b7280;
}

.coupon-section {
  width: 100%;
}

.coupon-input {
  margin-bottom: 16px;
}

.available-coupons {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background: #fafbfc;
}

.coupons-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.coupons-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.coupon-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.coupon-item:hover {
  border-color: #3b82f6;
}

.coupon-item.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.coupon-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.coupon-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.coupon-desc {
  font-size: 12px;
  color: #6b7280;
}

.coupon-value {
  font-weight: 700;
  color: #3b82f6;
  font-size: 16px;
}

.invoice-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.cost-breakdown {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
}

.breakdown-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #374151;
}

.breakdown-item:last-child {
  margin-bottom: 0;
}

.breakdown-item.discount {
  color: #10b981;
}

.breakdown-item.total {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  padding-top: 8px;
}

.breakdown-divider {
  height: 1px;
  background: #e5e7eb;
  margin: 12px 0;
}

.terms-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #fef3c7;
  border-radius: 8px;
  border: 1px solid #fbbf24;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.form-actions .el-button {
  min-width: 100px;
}

.insufficient-balance {
  margin-top: 16px;
}

@media (max-width: 768px) {
  .payment-methods {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions .el-button {
    width: 100%;
  }
}
</style>