# Cursor编码规范与限制规则

> 新建模块或者文件包括方法或者类型时候需要检查是否已经存在，存在不再新增，不能一味新增代码，需要结合整体进行处理，避免代码冗余堆积,新产生的代码需要自动处理解决类型报错。


## 1. 架构分层规范

### 1.1 控制器与服务层分离

- **控制器职责**：
  - 处理HTTP请求和响应
  - 参数验证和转换
  - 调用相应的服务层方法
  - 返回适当的HTTP响应

- **控制器规范**：
  - 控制器方法应保持简洁，通常不超过5-10行代码
  - 不在控制器中处理业务逻辑
  - 不直接访问数据库或外部服务
  - 使用依赖注入获取服务实例

- **实体规范**：
  - 无特殊情况，实体需要从基础实体BaseEntity继承
  - 实体命名需要有‘Entity’，列如：UserEntity这样代码阅读更容易理解，语义化
  - 跨模块引入时候，对于实体的类型声明不能直接通过实体类来使用，需要通过DTO来限制

```typescript
// ✅ 正确示例
@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return await this.userService.findOne(id);
  }
}

// ❌ 错误示例
@Controller('users')
export class UserController {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>
  ) {}

  @Get(':id')
  async findOne(@Param('id') id: string) {
    // 控制器直接访问数据库
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }
    // 控制器中处理业务逻辑
    if (user.status === 'inactive') {
      throw new ForbiddenException('用户已禁用');
    }
    return user;
  }
}
```

- **服务层职责**：
  - 实现业务逻辑
  - 数据处理和转换
  - 数据库操作
  - 调用其他服务或外部API

- **服务层规范**：
  - 方法应具有明确的单一职责
  - 使用依赖注入获取所需依赖
  - 处理业务异常并转换为适当的HTTP异常
  - 返回处理后的数据，不关心HTTP状态码

```typescript
// ✅ 正确示例
@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly profileService: ProfileService,
  ) {}

  async findOne(id: string): Promise<UserDto> {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }
    if (user.status === 'inactive') {
      throw new ForbiddenException('用户已禁用');
    }
    
    const profile = await this.profileService.findByUserId(id);
    return this.mapToUserDto(user, profile);
  }
  
  private mapToUserDto(user: User, profile: Profile): UserDto {
    // 数据转换逻辑
    return {
      id: user.id,
      username: user.username,
      email: user.email,
      profile: {
        fullName: profile.fullName,
        avatar: profile.avatarUrl
      }
    };
  }
}
```

## 2. 数据验证与类型限制

### 2.1 DTO规范

- **DTO目录结构**：
  ```
  src/modules/user/dto/
  ├── create-user.dto.ts
  ├── update-user.dto.ts
  ├── user-query.dto.ts
  └── user.dto.ts
  ```

- **DTO类型定义规范**：
  - 使用类而非接口，以支持运行时验证
  - 使用class-validator装饰器进行验证
  - 使用class-transformer装饰器进行转换
  - 为每个字段添加详细的API文档

```typescript
// ✅ 正确示例
export class CreateUserDto {
  @ApiProperty({
    description: '用户名',
    example: 'johndoe',
    minLength: 4,
    maxLength: 20
  })
  @IsString()
  @MinLength(4)
  @MaxLength(20)
  @Matches(/^[a-zA-Z0-9_-]+$/, {
    message: '用户名只能包含字母、数字、下划线和连字符'
  })
  username: string;

  @ApiProperty({
    description: '电子邮箱',
    example: '<EMAIL>'
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: '密码',
    minLength: 8,
    maxLength: 100
  })
  @IsString()
  @MinLength(8)
  @MaxLength(100)
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]+$/, {
    message: '密码必须包含至少一个大写字母、一个小写字母和一个数字'
  })
  password: string;
}
```

- **验证管道使用**：
  - 在控制器方法中使用ValidationPipe
  - 或在全局配置ValidationPipe

```typescript
// 方法级别使用
@Post()
async create(@Body(new ValidationPipe()) createUserDto: CreateUserDto) {
  return this.userService.create(createUserDto);
}

// 全局配置
// main.ts
async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,           // 剔除未定义的属性
    forbidNonWhitelisted: true, // 存在未定义属性时抛出错误
    transform: true,           // 自动转换类型
    transformOptions: {
      enableImplicitConversion: true // 启用隐式转换
    }
  }));
  
  await app.listen(3000);
}
```

## 3. 包管理与项目启动

### 3.1 包管理规范

- **使用pnpm作为包管理工具**：
  - 使用`pnpm add <package>`安装依赖
  - 使用`pnpm add -D <package>`安装开发依赖
  - 使用`pnpm install`安装所有依赖

- **脚本命令规范**：
  - 使用`pnpm run start:dev`启动开发服务器
  - 使用`pnpm run build`构建项目
  - 使用`pnpm run test`运行测试

```bash
# ✅ 正确的启动命令
pnpm run start:dev

# ❌ 除非特别说明，否则不使用以下命令
npm run start:dev
yarn start:dev
node dist/main.js
```

### 3.2 项目配置规范

- **环境变量**：
  - 使用`/scr/config/`下对应的环境文件存储环境变量
  - 不在代码中硬编码敏感信息
  - 使用ConfigService获取配置值

```typescript
// ✅ 正确示例
@Injectable()
export class DatabaseService {
  constructor(private configService: ConfigService) {
    const host = this.configService.get<string>('DATABASE_HOST');
    const port = this.configService.get<number>('DATABASE_PORT');
    // ...
  }
}

// ❌ 错误示例
@Injectable()
export class DatabaseService {
  private readonly host = 'localhost';
  private readonly port = 5432;
  // ...
}
```

## 4. 跨模块调用规范

### 4.1 模块间依赖

- **通过服务层调用**：
  - 模块间调用必须通过服务层进行
  - 不直接引用其他模块的实体
  - 在模块的`exports`数组中导出需要被其他模块使用的服务

```typescript
// ✅ 正确示例 - 模块定义
@Module({
  imports: [TypeOrmModule.forFeature([User])],
  controllers: [UserController],
  providers: [UserService],
  exports: [UserService] // 导出服务以供其他模块使用
})
export class UserModule {}

// ✅ 正确示例 - 服务调用
@Injectable()
export class AuthService {
  constructor(private readonly userService: UserService) {}
  
  async validateUser(username: string, password: string) {
    const user = await this.userService.findByUsername(username);
    // ...验证逻辑
  }
}

// ❌ 错误示例 - 直接引用实体
@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>
  ) {}
  
  async validateUser(username: string, password: string) {
    const user = await this.userRepository.findOne({ where: { username } });
    // ...验证逻辑
  }
}
```

### 4.2 例外情况

在以下特殊场景中，允许直接引用其他模块的实体：

- **事务处理**：需要在同一事务中操作多个实体
- **原子性操作**：需要保证多个实体操作的原子性
- **公共共享的服务**：例如redis服务，验证码服务等共享服务
- **特殊性能优化**：经过评估，直接访问实体可显著提升性能

```typescript
// 事务处理的例外情况
@Injectable()
export class PaymentService {
  constructor(
    private connection: Connection,
    private userService: UserService // 仍然注入服务用于非事务操作
  ) {}
  
  async processPayment(userId: string, amount: number) {
    // 使用事务同时操作多个实体
    await this.connection.transaction(async manager => {
      // 在事务中直接使用实体管理器
      const user = await manager.findOne(User, { where: { id: userId } });
      if (!user) {
        throw new NotFoundException('用户不存在');
      }
      
      // 更新用户余额
      user.balance -= amount;
      await manager.save(user);
      
      // 创建交易记录
      const transaction = new Transaction();
      transaction.userId = userId;
      transaction.amount = amount;
      transaction.type = 'payment';
      transaction.status = 'completed';
      await manager.save(transaction);
    });
  }
}
```

## 5. 代码审查检查点

在使用Cursor进行代码审查时，请检查以下关键点：

### 5.1 控制器检查

- [ ] 控制器方法是否简洁，只负责请求处理和响应返回
- [ ] 是否避免在控制器中包含业务逻辑
- [ ] 是否正确使用DTO进行参数验证
- [ ] 是否通过依赖注入获取服务实例

### 5.2 服务层检查

- [ ] 服务方法是否具有单一职责
- [ ] 是否包含所有业务逻辑处理
- [ ] 是否正确处理异常并转换为HTTP异常
- [ ] 是否避免处理HTTP请求/响应细节

### 5.3 DTO检查

- [ ] 是否为所有输入/输出创建了DTO类
- [ ] 是否使用class-validator进行验证
- [ ] 是否添加了完整的API文档
- [ ] 是否定义了合理的验证规则

### 5.4 跨模块调用检查

- [ ] 是否通过服务层进行模块间调用
- [ ] 是否避免直接引用其他模块的实体
- [ ] 如有直接引用实体，是否属于允许的例外情况
- [ ] 被依赖的服务是否正确导出

## 6. 最佳实践示例

### 完整的控制器示例

```typescript
@Controller('api-keys')
@ApiTags('API密钥管理')
export class ApiKeyController {
  constructor(private readonly apiKeyService: ApiKeyService) {}

  @Post()
  @ApiOperation({ summary: '创建API密钥' })
  @ApiCreatedResponse({ type: ApiKeyDto })
  async create(@Body() createApiKeyDto: CreateApiKeyDto, @Req() req) {
    return await this.apiKeyService.createApiKey(req.user.id, createApiKeyDto);
  }

  @Get()
  @ApiOperation({ summary: '获取API密钥列表' })
  @ApiOkResponse({ type: PaginatedApiKeyDto })
  async findAll(@Query() queryDto: ApiKeyQueryDto, @Req() req) {
    return await this.apiKeyService.findAll(req.user.id, queryDto);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取单个API密钥' })
  @ApiOkResponse({ type: ApiKeyDto })
  async findOne(@Param('id') id: string, @Req() req) {
    return await this.apiKeyService.findOne(id, req.user.id);
  }

  @Patch(':id/regenerate')
  @ApiOperation({ summary: '重新生成API密钥' })
  @ApiOkResponse({ type: ApiKeyDto })
  async regenerate(@Param('id') id: string, @Req() req) {
    return await this.apiKeyService.regenerateApiKey(id, req.user.id);
  }

  @Patch(':id/mark-viewed')
  @ApiOperation({ summary: '标记API密钥已查看' })
  @ApiNoContentResponse()
  async markViewed(@Param('id') id: string, @Req() req) {
    await this.apiKeyService.markAsViewed(id, req.user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除API密钥' })
  @ApiNoContentResponse()
  async remove(@Param('id') id: string, @Req() req) {
    await this.apiKeyService.removeApiKey(id, req.user.id);
  }
}
```

### 完整的服务示例

```typescript
@Injectable()
export class ApiKeyService {
  constructor(
    @InjectRepository(ApiKey)
    private apiKeyRepository: Repository<ApiKey>,
    private cryptoService: CryptoService,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
    private userService: UserService // 跨模块调用通过服务
  ) {}

  async createApiKey(userId: number, createApiKeyDto: CreateApiKeyDto): Promise<ApiKeyDto> {
    // 验证用户存在
    await this.userService.findById(userId);
    
    // 生成API密钥
    const apiKey = this.generateApiKey();
    const secretKey = this.generateSecretKey();
    
    // 加密密钥
    const encryptedSecretKey = await this.cryptoService.encrypt(secretKey);
    
    // 创建密钥记录
    const newApiKey = this.apiKeyRepository.create({
      key: apiKey,
      userId,
      name: createApiKeyDto.name || `API Key ${format(new Date(), 'yyyy-MM-dd')}`,
      status: ApiKeyStatus.ACTIVE,
      scopes: createApiKeyDto.scopes || ['*'],
      createdAt: new Date(),
      expiresAt: createApiKeyDto.expiresAt ? new Date(createApiKeyDto.expiresAt) : undefined,
      allowedIps: createApiKeyDto.allowedIps,
      encryptedSecretKey,
    });
    
    // 保存到数据库
    await this.apiKeyRepository.save(newApiKey);
    
    // 同步到缓存
    await this.syncApiKeyToCache(newApiKey.id);
    
    // 返回密钥信息（仅在创建时返回明文密钥）
    return {
      id: newApiKey.id,
      key: apiKey,
      secretKey, // 明文密钥，仅创建时返回
      name: newApiKey.name,
      status: newApiKey.status,
      scopes: newApiKey.scopes,
      createdAt: newApiKey.createdAt,
      expiresAt: newApiKey.expiresAt,
      allowedIps: newApiKey.allowedIps,
      isViewed: false,
    };
  }
  
  // ... 其他服务方法
  
  private generateApiKey(): string {
    return 'ak-' + crypto.randomBytes(16).toString('hex');
  }
  
  private generateSecretKey(): string {
    return 'sk-' + crypto.randomBytes(32).toString('hex');
  }
}
```

## 7. 总结

使用Cursor进行NestJS开发时，请遵循以下核心原则：

1. **职责分离**：控制器处理HTTP请求/响应，服务层处理业务逻辑
2. **类型安全**：使用DTO和验证装饰器确保类型安全和数据验证
3. **包管理一致性**：默认使用pnpm作为包管理工具
4. **模块隔离**：跨模块调用通过服务层进行，避免直接引用实体
5. **遵循全局权限守卫和响应拦截过滤**：全局守卫和响应拦截过滤不单独额外处理
6. **公共复杂非业务的逻辑可以考虑封装装饰器**：装饰器便于复用
7. **尽量使用redis等共享模块不单独额外处理**：避免代码重复冗余

遵循这些规则将帮助团队维护一个结构清晰、易于扩展的NestJS应用程序。 