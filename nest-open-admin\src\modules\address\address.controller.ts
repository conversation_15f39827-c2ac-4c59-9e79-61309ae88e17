import {
  Controller,
  Post,
  Body,
  Get,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
} from '@nestjs/swagger';
import { AddressService } from './address.service';
import {
  ExtractAddressDto,
  ExtractMultipleAddressDto,
  ExtractionResult,
  ReverseGeocodeDto,
  BatchReverseGeocodeDto,
  ReverseGeocodeResult,
  BatchReverseGeocodeResult,
} from './dto/address-extraction.dto';
import { UseAuthStrategy, AuthStrategy } from '../../common/decorators/auth-strategy.decorator';

import { Public } from '@/common/decorators/public.decorator';

@ApiTags('物流地址文本提取、地理坐标逆解析')
@Controller('address')
@UseAuthStrategy(AuthStrategy.API_KEY)
export class AddressController {
  private readonly logger = new Logger(AddressController.name);
  
  constructor(
    private readonly addressService: AddressService,
  ) {}

  /**
   * 输入文本预处理（控制器层面的基础清洗）
   */
  private preprocessInputText(text: string): string {
    if (!text || typeof text !== 'string') {
      return '';
    }

    try {
      // 确保文本是有效的UTF-8
      let processedText = Buffer.from(text, 'utf8').toString('utf8');
      
      // 基础清洗：移除明显的垃圾字符
      return processedText
        // 移除BOM标记
        .replace(/^\uFEFF/, '')
        // 统一换行符
        .replace(/\r\n|\r/g, '\n')
        // 移除过多的连续换行
        .replace(/\n{3,}/g, '\n\n')
        // 移除行首行尾空白
        .split('\n')
        .map(line => line.trim())
        .join('\n')
        // 移除零宽字符
        .replace(/[\u200B-\u200D\uFEFF]/g, '')
        // 基础trim
        .trim();
    } catch (error) {
      this.logger.warn('输入文本预处理失败:', error.message);
      return text.trim();
    }
  }

  /**
   * 检测可疑内容（仅检测真正危险的内容）
   */
  private containsSuspiciousContent(text: string): boolean {
    // 检测明显的脚本注入攻击
    const dangerousPatterns = [
      /<script[^>]*>.*?<\/script>/i,
      /javascript:\s*[^\s]/i,
      /on(load|click|error|focus)\s*=/i,
      /eval\s*\([^)]*\)/i,
      /document\.write/i,
      /window\.location/i,
      /<iframe[^>]*>/i,
      /<object[^>]*>/i,
      /<embed[^>]*>/i
    ];
    
    // 检测过长的单行内容（可能是恶意payload）
    const lines = text.split('\n');
    const hasExtremelyLongLine = lines.some(line => line.length > 2000);
    
    // 检测过多的控制字符（可能是二进制数据）
    const controlCharCount = (text.match(/[\u0000-\u0008\u000E-\u001F\u007F]/g) || []).length;
    const controlCharRatio = controlCharCount / text.length;
    
    return dangerousPatterns.some(pattern => pattern.test(text)) || 
           hasExtremelyLongLine || 
           controlCharRatio > 0.1;
  }

  @Post('extract')
  @ApiOperation({
    summary: '从文本中提取地址信息',
    description: '使用规则引擎+NLP模型三层架构提取寄件人/收件人的姓名、手机号和详细地址信息',
  })
  @ApiBody({ type: ExtractAddressDto })
  async extractAddress(
    @Body() extractAddressDto: ExtractAddressDto,
  ): Promise<ExtractionResult> {
    try {
      const { text, mode = 'single' } = extractAddressDto;
      
      // 基础验证
      if (!text || typeof text !== 'string') {
        throw new HttpException(
          '文本内容不能为空且必须为字符串类型',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      // 预处理文本以移除潜在的问题字符
      let processedText = this.preprocessInputText(text);
      
      if (processedText.trim().length === 0) {
        throw new HttpException(
          '处理后的文本内容为空，请检查输入内容',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      // 验证文本长度（处理后）
      if (processedText.length > 10000) {
        throw new HttpException(
          '文本长度不能超过10000个字符',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      // 检测可能的恶意内容
      if (this.containsSuspiciousContent(processedText)) {
        throw new HttpException(
          '输入内容包含不支持的格式或字符',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      this.logger.log(`接收到地址提取请求，原始长度: ${text.length}，处理后长度: ${processedText.length}`);
      
      return await this.addressService.extractFromText(processedText, mode);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      // 特殊处理JSON解析错误
      if (error.message && error.message.includes('Unexpected token')) {
        throw new HttpException(
          '请求数据格式错误，请检查JSON格式和字符编码',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      // 处理编码相关错误
      if (error.message && (error.message.includes('encoding') || error.message.includes('UTF-8'))) {
        throw new HttpException(
          '文本编码格式不支持，请使用UTF-8编码',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      this.logger.error('地址提取服务异常:', error.message, error.stack);
      
      throw new HttpException(
        '地址提取失败: ' + error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('extract-multiple')
  @ApiOperation({
    summary: '从多个文本中批量提取地址信息',
    description: '批量处理多个文本，提取其中的地址信息',
  })
  @ApiBody({ type: ExtractMultipleAddressDto })
  async extractMultipleAddresses(
    @Body() extractMultipleDto: ExtractMultipleAddressDto,
  ): Promise<ExtractionResult[]> {
    try {
      const { texts } = extractMultipleDto;
      
      if (!texts || texts.length === 0) {
        throw new HttpException(
          '文本数组不能为空',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      if (texts.length > 100) {
        throw new HttpException(
          '单次最多处理100个文本',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      return await this.addressService.extractFromMultipleTexts(texts);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        '批量地址提取失败: ' + error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('test')
  @ApiOperation({
    summary: '测试接口',
    description: '使用预设的测试数据验证地址提取功能',
  })
  async testExtraction(): Promise<ExtractionResult[]> {
    const testTexts = [
      '张三 13812345678 北京市朝阳区建国门外大街1号国贸大厦A座1001室',
      '李四先生 13987654321 上海市浦东新区陆家嘴环路1000号上海中心大厦50楼',
      '王五 15612345678 广东省深圳市南山区科技园南区深圳湾科技生态园10栋A座2001',
      '赵六女士 18712345678 四川省成都市武侯区天府大道中段666号天府软件园E区5栋3楼',
      '收件人：刘七 电话：13512345678 地址：新疆维吾尔自治区乌鲁木齐市天山区解放南路123号',
      '寄件人信息：陈八 手机号码：13612345678 详细地址：内蒙古自治区呼和浩特市赛罕区大学东街110号内蒙古大学',
    ];
    
    return await this.addressService.extractFromMultipleTexts(testTexts);
  }

  @Post('rev-geo')
  @ApiOperation({
    summary: '地理坐标逆解析',
    description: '将经纬度坐标转换为详细地址信息，采用分层解析策略确保高准确率',
  })
  @ApiBody({ type: ReverseGeocodeDto })
  async reverseGeocode(
    @Body() reverseGeocodeDto: ReverseGeocodeDto,
  ): Promise<ReverseGeocodeResult> {
    try {
      const { latitude, longitude } = reverseGeocodeDto;
      
      // 验证坐标有效性
      if (latitude < -90 || latitude > 90) {
        throw new HttpException(
          '纬度必须在-90到90之间',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      if (longitude < -180 || longitude > 180) {
        throw new HttpException(
          '经度必须在-180到180之间',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      this.logger.log(`接收到地理坐标逆解析请求: ${latitude}, ${longitude}`);
      
      return await this.addressService.reverseGeocode(reverseGeocodeDto);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      this.logger.error('地理坐标逆解析失败:', error.message, error.stack);
      
      throw new HttpException(
        '地理坐标逆解析失败: ' + error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('batch-rev-geo')
  @ApiOperation({
    summary: '批量地理坐标逆解析',
    description: '批量将多个经纬度坐标转换为详细地址信息，最多支持50个坐标点',
  })
  @ApiBody({ type: BatchReverseGeocodeDto })
  async batchReverseGeocode(
    @Body() batchReverseGeocodeDto: BatchReverseGeocodeDto,
  ): Promise<BatchReverseGeocodeResult> {
    try {
      const { coordinates } = batchReverseGeocodeDto;
      
      if (!coordinates || coordinates.length === 0) {
        throw new HttpException(
          '坐标数组不能为空',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      if (coordinates.length > 50) {
        throw new HttpException(
          '单次最多处理50个坐标点',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      // 验证每个坐标的有效性
      for (let i = 0; i < coordinates.length; i++) {
        const { latitude, longitude } = coordinates[i];
        
        if (latitude < -90 || latitude > 90) {
          throw new HttpException(
            `第${i + 1}个坐标的纬度无效: ${latitude}`,
            HttpStatus.BAD_REQUEST,
          );
        }
        
        if (longitude < -180 || longitude > 180) {
          throw new HttpException(
            `第${i + 1}个坐标的经度无效: ${longitude}`,
            HttpStatus.BAD_REQUEST,
          );
        }
      }
      
      this.logger.log(`接收到批量地理坐标逆解析请求，数量: ${coordinates.length}`);
      
      return await this.addressService.batchReverseGeocode(batchReverseGeocodeDto);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        '批量地理坐标逆解析失败: ' + error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('test-geocode')
  @ApiOperation({
    summary: '测试地理坐标逆解析',
    description: '使用预设的测试坐标验证地理坐标逆解析功能',
  })
  async testReverseGeocode(): Promise<ReverseGeocodeResult> {
    return await this.addressService.testReverseGeocode();
  }
}