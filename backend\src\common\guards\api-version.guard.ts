import { Injectable, CanActivate, ExecutionContext, BadRequestException, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request, Response } from 'express';
import { API_VERSION_KEY, API_DEPRECATED_KEY } from '../decorators/api-version.decorator';
import { API_VERSIONS } from '@/shared/constants/api.constants';

/**
 * API版本控制守卫
 * 负责验证API版本兼容性和处理弃用警告
 */
@Injectable()
export class ApiVersionGuard implements CanActivate {
  private readonly logger = new Logger(ApiVersionGuard.name);

  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();

    // 获取请求的API版本
    const requestedVersion = this.extractApiVersion(request);
    
    // 获取端点要求的版本
    const requiredVersion = this.reflector.get<string>(API_VERSION_KEY, context.getHandler());
    
    // 获取弃用信息
    const deprecationInfo = this.reflector.get(API_DEPRECATED_KEY, context.getHandler());

    // 验证版本兼容性
    if (requiredVersion && !this.isVersionCompatible(requestedVersion, requiredVersion)) {
      throw new BadRequestException(
        `API版本不兼容。请求版本: ${requestedVersion}, 要求版本: ${requiredVersion}`,
      );
    }

    // 处理弃用警告
    if (deprecationInfo) {
      this.handleDeprecationWarning(response, deprecationInfo, requestedVersion);
    }

    // 添加版本信息到响应头
    response.setHeader('X-API-Version', requestedVersion);
    response.setHeader('X-API-Supported-Versions', API_VERSIONS.SUPPORTED.join(', '));

    // 记录API版本使用情况
    this.logVersionUsage(request, requestedVersion, requiredVersion);

    return true;
  }

  /**
   * 从请求中提取API版本
   */
  private extractApiVersion(request: Request): string {
    // 1. 从URL路径中提取版本 (优先级最高)
    const pathMatch = request.path.match(/^\/api\/(v\d+)\//);
    if (pathMatch) {
      return pathMatch[1];
    }

    // 2. 从Accept头中提取版本
    const acceptHeader = request.headers.accept;
    if (acceptHeader) {
      const versionMatch = acceptHeader.match(/application\/vnd\.api\+(v\d+)\+json/);
      if (versionMatch) {
        return versionMatch[1];
      }
    }

    // 3. 从自定义头中提取版本
    const versionHeader = request.headers['x-api-version'] as string;
    if (versionHeader) {
      return versionHeader;
    }

    // 4. 从查询参数中提取版本
    const versionQuery = request.query.version as string;
    if (versionQuery) {
      return versionQuery;
    }

    // 5. 默认使用当前版本
    return API_VERSIONS.CURRENT;
  }

  /**
   * 检查版本兼容性
   */
  private isVersionCompatible(requestedVersion: string, requiredVersion: string): boolean {
    // 检查是否为支持的版本
    if (!API_VERSIONS.SUPPORTED.includes(requestedVersion)) {
      return false;
    }

    // 检查是否为弃用版本
    if (API_VERSIONS.DEPRECATED.includes(requestedVersion)) {
      // 弃用版本仍然可以使用，但会收到警告
      return true;
    }

    // 简单的版本匹配（可以扩展为更复杂的语义版本比较）
    return requestedVersion === requiredVersion || requiredVersion === '*';
  }

  /**
   * 处理弃用警告
   */
  private handleDeprecationWarning(
    response: Response,
    deprecationInfo: any,
    requestedVersion: string,
  ): void {
    const { deprecatedSince, removeIn, alternative } = deprecationInfo;

    // 添加弃用警告头
    response.setHeader('X-API-Deprecated', 'true');
    response.setHeader('X-API-Deprecated-Since', deprecatedSince);
    
    if (removeIn) {
      response.setHeader('X-API-Remove-In', removeIn);
    }
    
    if (alternative) {
      response.setHeader('X-API-Alternative', alternative);
    }

    // 构建弃用警告消息
    let warningMessage = `API已弃用，自版本 ${deprecatedSince}`;
    if (removeIn) {
      warningMessage += `，将在版本 ${removeIn} 中移除`;
    }
    if (alternative) {
      warningMessage += `，请使用 ${alternative}`;
    }

    response.setHeader('Warning', `299 - "${warningMessage}"`);

    // 记录弃用API的使用
    this.logger.warn(`弃用API被调用`, {
      version: requestedVersion,
      deprecatedSince,
      removeIn,
      alternative,
      userAgent: response.req.headers['user-agent'],
      ip: this.getClientIp(response.req),
    });
  }

  /**
   * 记录版本使用情况
   */
  private logVersionUsage(request: Request, requestedVersion: string, requiredVersion?: string): void {
    this.logger.debug(`API版本使用`, {
      path: request.path,
      method: request.method,
      requestedVersion,
      requiredVersion,
      userAgent: request.headers['user-agent'],
      ip: this.getClientIp(request),
    });
  }

  /**
   * 获取客户端IP
   */
  private getClientIp(request: any): string {
    return (
      request.headers['x-forwarded-for'] ||
      request.headers['x-real-ip'] ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      request.ip ||
      'unknown'
    );
  }
}
