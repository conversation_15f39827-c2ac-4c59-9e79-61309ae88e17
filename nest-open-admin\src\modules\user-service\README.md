# 用户服务关联模块 (User Service Module)

## 概述

用户服务关联模块负责管理用户与服务之间的关联关系，包括服务次数管理、使用统计、权限控制等功能。

## 主要功能

### 1. 用户服务关联管理
- 创建用户与服务的关联关系
- 查询用户服务关联列表（支持多种筛选条件）
- 查询单个关联详情
- 更新关联信息
- 删除关联关系

### 2. 批量操作
- 批量分配服务给多个用户
- 批量更新服务次数
- 重置用户服务次数

### 3. 统计功能
- 获取用户服务关联统计信息
- 按服务、用户类型、用户等级分组统计
- 使用情况分析

## API 接口

### 基础 CRUD 操作

#### 创建用户服务关联
```http
POST /user-service
Content-Type: application/json

{
  "userId": 1,
  "serviceId": 1,
  "freeCount": 100,
  "purchasedCount": 1000,
  "enabled": true,
  "expiresAt": "2024-12-31T23:59:59.000Z",
  "config": {
    "maxDailyUsage": 50
  },
  "notes": "测试用户服务关联"
}
```

#### 查询关联列表
```http
GET /user-service?page=1&limit=10&userId=1&serviceId=1&enabled=true
```

#### 查询单个关联
```http
GET /user-service/1
```

#### 根据用户和服务查询关联
```http
GET /user-service/user/1/service/1
```

#### 更新关联
```http
PATCH /user-service/1
Content-Type: application/json

{
  "freeCount": 200,
  "purchasedCount": 2000,
  "enabled": false
}
```

#### 删除关联
```http
DELETE /user-service/1
```

### 批量操作

#### 批量分配服务
```http
POST /user-service/batch-assign
Content-Type: application/json

{
  "userIds": [1, 2, 3],
  "serviceId": 1,
  "freeCount": 100,
  "purchasedCount": 1000,
  "expiresAt": "2024-12-31T23:59:59.000Z"
}
```

#### 批量更新次数
```http
POST /user-service/batch-update-count
Content-Type: application/json

{
  "ids": [1, 2, 3],
  "operation": "add",
  "countType": "purchased",
  "amount": 500,
  "reason": "充值赠送"
}
```

#### 重置次数
```http
POST /user-service/reset-count
Content-Type: application/json

{
  "userId": 1,
  "serviceId": 1,
  "resetType": "call_count",
  "reason": "月度重置"
}
```

### 统计信息

#### 获取统计信息
```http
GET /user-service/stats
```

## 数据模型

### UserServiceEntity

| 字段 | 类型 | 描述 |
|------|------|------|
| id | number | 主键ID |
| totalCount | number | 总调用次数 |
| usedCount | number | 已用次数 |
| freeUsedToday | number | 今日已用免费次数 |
| lastResetDate | Date | 上次免费额度重置日期 |
| alertSent | boolean | 是否已发送预警 |
| user | UserEntity | 关联用户 |
| service | ServiceEntity | 关联服务 |
| createdAt | Date | 创建时间 |
| updatedAt | Date | 更新时间 |

## 业务规则

### 1. 关联创建规则
- 用户和服务必须存在
- 同一用户和服务只能有一个关联关系
- 总次数 = 免费次数 + 购买次数
- 剩余次数 = 总次数 - 已用次数

### 2. 次数管理规则
- 已用次数不能超过总次数
- 免费次数每日重置（根据配置）
- 购买次数不会自动重置
- 支持手动重置次数

### 3. 删除规则
- 有调用记录的关联不能删除
- 删除采用软删除方式

### 4. 批量操作规则
- 批量分配时跳过已存在的关联
- 批量更新使用事务保证数据一致性
- 操作失败时记录日志但不中断整个流程

## 依赖关系

- **UserModule**: 用于验证用户信息
- **ServiceModule**: 用于验证服务信息
- **TypeORM**: 数据库操作

## 错误处理

模块使用统一的异常处理机制：

- `NotFoundException`: 资源不存在
- `ConflictException`: 资源冲突（如重复创建关联）
- `BadRequestException`: 请求参数错误或业务规则违反

## 日志记录

模块记录以下关键操作的日志：

- 创建/更新/删除关联
- 批量操作结果
- 次数重置操作
- 错误和警告信息

## 使用示例

```typescript
// 注入服务
constructor(
  private readonly userServiceService: UserServiceService,
) {}

// 创建关联
const association = await this.userServiceService.create({
  userId: 1,
  serviceId: 1,
  freeCount: 100,
  purchasedCount: 1000,
});

// 查询用户的所有服务
const userServices = await this.userServiceService.findAll({
  userId: 1,
  page: 1,
  limit: 10,
});

// 批量分配服务
const results = await this.userServiceService.batchAssignService({
  userIds: [1, 2, 3],
  serviceId: 1,
  freeCount: 100,
});
```

## 注意事项

1. 所有涉及次数的操作都需要考虑并发安全
2. 批量操作可能耗时较长，建议异步处理
3. 统计信息可能存在缓存，实时性要求高的场景需要注意
4. 删除操作不可逆，建议在UI层增加二次确认