import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from './redis.service';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as crypto from 'crypto';

interface SmsConfig {
  provider: string;
  qiniu?: {
    accessKey: string;
    secretKey: string;
    signatureId: string;
    templates: Record<string, string>;
  };
  aliyun?: {
    accessKeyId: string;
    accessKeySecret: string;
    signName: string;
    templates: Record<string, string>;
  };
  tencent?: {
    secretId: string;
    secretKey: string;
    appId: string;
    signName: string;
    templates: Record<string, string>;
  };
}

@Injectable()
export class SmsService {
  private readonly logger = new Logger(SmsService.name);
  private readonly SMS_RATE_LIMIT_PREFIX = 'sms:rate:';
  private readonly SMS_DAILY_LIMIT_PREFIX = 'sms:daily:';
  private readonly MAX_DAILY_SENDS = 10; // 每日最大发送次数
  private readonly RATE_LIMIT_WINDOW = 60; // 频率限制窗口（秒）
  private readonly MAX_RATE_SENDS = 1; // 频率限制窗口内最大发送次数

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly httpService: HttpService,
  ) {}

  /**
   * 发送短信验证码
   */
  async sendVerificationCode(
    phone: string,
    code: string,
    type: 'register' | 'login' | 'reset' = 'register',
  ): Promise<{ success: boolean; message?: string; provider?: string }> {
    try {
      // 验证手机号格式
      if (!this.isValidPhoneNumber(phone)) {
        return {
          success: false,
          message: '手机号格式不正确',
        };
      }

      // 检查发送频率限制
      const rateLimitCheck = await this.checkRateLimit(phone);
      if (!rateLimitCheck.canSend) {
        return {
          success: false,
          message: rateLimitCheck.message,
        };
      }

      // 检查日发送限制
      const dailyLimitCheck = await this.checkDailyLimit(phone);
      if (!dailyLimitCheck.canSend) {
        return {
          success: false,
          message: dailyLimitCheck.message,
        };
      }

      // 获取短信配置
      const smsConfig = this.configService.get<SmsConfig>('sms');
      if (!smsConfig) {
        return {
          success: false,
          message: '短信服务配置错误',
        };
      }

      let result = false;
      let provider = '';

      // 根据配置的提供商发送短信
      switch (smsConfig.provider) {
        case 'qiniu':
          result = await this.sendQiniuSms(phone, code, type, smsConfig.qiniu);
          provider = 'qiniu';
          break;
        case 'aliyun':
          result = await this.sendAliyunSms(phone, code, type, smsConfig.aliyun);
          provider = 'aliyun';
          break;
        case 'tencent':
          result = await this.sendTencentSms(phone, code, type, smsConfig.tencent);
          provider = 'tencent';
          break;
        default:
          return {
            success: false,
            message: `不支持的短信服务商: ${smsConfig.provider}`,
          };
      }

      if (result) {
        // 更新发送计数
        await this.updateSendCounts(phone);
        this.logger.log(`短信发送成功: ${phone},${code}, 提供商: ${provider}`);
        return {
          success: true,
          message: '短信发送成功',
          provider,
        };
      } else {
        return {
          success: false,
          message: '短信发送失败，请稍后重试',
        };
      }
    } catch (error) {
      this.logger.error('发送短信失败:', error);
      return {
        success: false,
        message: '短信发送失败，请稍后重试',
      };
    }
  }

  /**
   * 发送模板短信
   */
  async sendTemplateSms(
    phone: string,
    template: string,
    data: Record<string, any>,
  ): Promise<{ success: boolean; message?: string; provider?: string }> {
    try {
      // 验证手机号格式
      if (!this.isValidPhoneNumber(phone)) {
        return {
          success: false,
          message: '手机号格式不正确',
        };
      }

      // 获取短信配置
      const smsConfig = this.configService.get<SmsConfig>('sms');
      if (!smsConfig) {
        return {
          success: false,
          message: '短信服务配置错误',
        };
      }

      let result = false;
      let provider = '';

      // 根据配置的提供商发送短信
      switch (smsConfig.provider) {
        case 'qiniu':
          result = await this.sendQiniuTemplateSms(phone, template, data, smsConfig.qiniu);
          provider = 'qiniu';
          break;
        case 'aliyun':
          result = await this.sendAliyunTemplateSms(phone, template, data, smsConfig.aliyun);
          provider = 'aliyun';
          break;
        case 'tencent':
          result = await this.sendTencentTemplateSms(phone, template, data, smsConfig.tencent);
          provider = 'tencent';
          break;
        default:
          return {
            success: false,
            message: `不支持的短信服务商: ${smsConfig.provider}`,
          };
      }

      if (result) {
        this.logger.log(`模板短信发送成功: ${phone}, 模板: ${template}, 提供商: ${provider}`);
        return {
          success: true,
          message: '短信发送成功',
          provider,
        };
      } else {
        return {
          success: false,
          message: '短信发送失败，请稍后重试',
        };
      }
    } catch (error) {
      this.logger.error('发送模板短信失败:', error);
      return {
        success: false,
        message: '短信发送失败，请稍后重试',
      };
    }
  }

  /**
   * 验证手机号格式
   */
  private isValidPhoneNumber(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }

  /**
   * 检查发送频率限制
   */
  private async checkRateLimit(phone: string): Promise<{ canSend: boolean; message?: string }> {
    const key = `${this.SMS_RATE_LIMIT_PREFIX}${phone}`;
    const count = await this.redisService.get(key);
    const currentCount = count ? parseInt(count, 10) : 0;

    if (currentCount >= this.MAX_RATE_SENDS) {
      const ttl = await this.redisService.getClient().ttl(key);
      return {
        canSend: false,
        message: `发送过于频繁，请${ttl}秒后重试`,
      };
    }

    return { canSend: true };
  }

  /**
   * 检查日发送限制
   */
  private async checkDailyLimit(phone: string): Promise<{ canSend: boolean; message?: string }> {
    const today = new Date().toISOString().split('T')[0];
    const key = `${this.SMS_DAILY_LIMIT_PREFIX}${phone}:${today}`;
    const count = await this.redisService.get(key);
    const currentCount = count ? parseInt(count, 10) : 0;

    if (currentCount >= this.MAX_DAILY_SENDS) {
      return {
        canSend: false,
        message: '今日发送次数已达上限，请明天再试',
      };
    }

    return { canSend: true };
  }

  /**
   * 更新发送计数
   */
  private async updateSendCounts(phone: string): Promise<void> {
    // 更新频率限制计数
    const rateLimitKey = `${this.SMS_RATE_LIMIT_PREFIX}${phone}`;
    await this.redisService.getClient().incr(rateLimitKey);
    await this.redisService.getClient().expire(rateLimitKey, this.RATE_LIMIT_WINDOW);

    // 更新日发送计数
    const today = new Date().toISOString().split('T')[0];
    const dailyLimitKey = `${this.SMS_DAILY_LIMIT_PREFIX}${phone}:${today}`;
    await this.redisService.getClient().incr(dailyLimitKey);
    await this.redisService.getClient().expire(dailyLimitKey, 86400); // 24小时过期
  }

  /**
   * 七牛云短信发送
   */
  private async sendQiniuSms(
    phone: string,
    code: string,
    type: string,
    config: any,
  ): Promise<boolean> {
    try {
      if (!config || !config.accessKey || !config.secretKey || !config.signatureId) {
        this.logger.error('七牛云短信配置不完整');
        return false;
      }

      const templateId = config.templates[type] || config.templates.register;
      if (!templateId) {
        this.logger.error(`未找到短信模板: ${type}`);
        return false;
      }

      // 构建正确的请求体
      const requestBody = {
        template_id: templateId,
        mobile: phone,
        parameters: {
          code: code,
        }
      };

      // 如果签名ID存在，则添加到请求体中
      if (config.signatureId) {
        requestBody['signature_id'] = config.signatureId;
      }

      const token = this.generateQiniuToken(config.accessKey, config.secretKey, requestBody);

      this.logger.log(`七牛云短信请求参数: ${JSON.stringify(requestBody)}, token: ${token}`);

      const response = await firstValueFrom(
        this.httpService.post(
          'https://sms.qiniuapi.com/v1/message/single',
          requestBody,
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Qiniu ${token}`,
            },
            timeout: 10000,
          },
        ),
      );

      // 检查HTTP状态码和响应体
      if (response.status === 200 && response.data) {
        // 七牛云API可能返回200状态码但响应体中包含错误信息
        if (response.data.error) {
          this.logger.error(`七牛云短信发送失败: ${response.data.error}`, response.data);
          return false;
        }
        
        // 记录完整响应以便调试
        this.logger.log(`七牛云短信发送成功: ${phone},${code}, 响应: ${JSON.stringify(response.data)}`);
        return true;
      } else {
        const errorMsg = response.data ? JSON.stringify(response.data) : '未知错误';
        this.logger.error(`七牛云短信发送失败: HTTP状态 ${response.status}, 错误: ${errorMsg}`);
        return false;
      }
    } catch (error) {
      this.logger.error('七牛云短信发送异常:', error);
      return false;
    }
  }

  /**
   * 七牛云模板短信发送
   */
  private async sendQiniuTemplateSms(
    phone: string,
    template: string,
    data: Record<string, any>,
    config: any,
  ): Promise<boolean> {
    try {
      if (!config || !config.accessKey || !config.secretKey || !config.signatureId) {
        this.logger.error('七牛云短信配置不完整');
        return false;
      }

      // 构建正确的请求体
      const requestBody = {
        template_id: template,
        mobile: phone,
        parameters: data
      };

      // 如果签名ID存在，则添加到请求体中
      if (config.signatureId) {
        requestBody['signature_id'] = config.signatureId;
      }

      const token = this.generateQiniuToken(config.accessKey, config.secretKey, requestBody);

      this.logger.log(`七牛云模板短信请求参数: ${JSON.stringify(requestBody)}, token: ${token}`);

      const response = await firstValueFrom(
        this.httpService.post(
          'https://sms.qiniuapi.com/v1/message/single',
          requestBody,
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Qiniu ${token}`,
            },
            timeout: 10000,
          },
        ),
      );

      // 检查HTTP状态码和响应体
      if (response.status === 200 && response.data) {
        // 七牛云API可能返回200状态码但响应体中包含错误信息
        if (response.data.error) {
          this.logger.error(`七牛云模板短信发送失败: ${response.data.error}`, response.data);
          return false;
        }
        
        // 记录完整响应以便调试
        this.logger.log(`七牛云模板短信发送成功: ${phone}, 模板: ${template}, 响应: ${JSON.stringify(response.data)}`);
        return true;
      } else {
        const errorMsg = response.data ? JSON.stringify(response.data) : '未知错误';
        this.logger.error(`七牛云模板短信发送失败: HTTP状态 ${response.status}, 错误: ${errorMsg}`);
        return false;
      }
    } catch (error) {
      this.logger.error('七牛云模板短信发送异常:', error);
      return false;
    }
  }

  /**
   * 生成七牛云签名token
   */
  private generateQiniuToken(accessKey: string, secretKey: string, requestBody: any): string {
    const method = 'POST';
    const path = '/v1/message/single';
    const host = 'sms.qiniuapi.com';
    const contentType = 'application/json';
    const body = JSON.stringify(requestBody);

    // 正确的签名字符串构造格式
    const signingStr = `${method} ${path}\nHost: ${host}\nContent-Type: ${contentType}\n\n${body}`;

    // 使用HMAC-SHA1算法计算签名
    const hmac = crypto.createHmac('sha1', secretKey);
    hmac.update(Buffer.from(signingStr, 'utf8'));
    const signature = hmac.digest('base64');

    // URL安全的Base64编码
    const urlSafeSignature = signature
      .replace(/\+/g, '-')
      .replace(/\//g, '_');

    return `${accessKey}:${urlSafeSignature}`;
  }

  /**
   * 阿里云短信发送
   */
  private async sendAliyunSms(
    phone: string,
    code: string,
    type: string,
    config: any,
  ): Promise<boolean> {
    try {
      if (!config || !config.accessKeyId || !config.accessKeySecret || !config.signName) {
        this.logger.error('阿里云短信配置不完整');
        return false;
      }

      const templateCode = config.templates[type] || config.templates.register;
      if (!templateCode) {
        this.logger.error(`未找到短信模板: ${type}`);
        return false;
      }

      // 开发环境下模拟发送成功
      if (this.configService.get('NODE_ENV') === 'development') {
        this.logger.log(`阿里云模拟发送验证码到 ${phone}: ${code}`);
        return true;
      }

      // 实际实现需要集成阿里云SDK
      // 这里提供基本的参数结构
      const params = {
        PhoneNumbers: phone,
        SignName: config.signName,
        TemplateCode: templateCode,
        TemplateParam: JSON.stringify({ code }),
      };

      this.logger.log(`阿里云短信发送参数:`, params);
      return true;
    } catch (error) {
      this.logger.error('阿里云短信发送异常:', error);
      return false;
    }
  }

  /**
   * 阿里云模板短信发送
   */
  private async sendAliyunTemplateSms(
    phone: string,
    template: string,
    data: Record<string, any>,
    config: any,
  ): Promise<boolean> {
    try {
      if (!config || !config.accessKeyId || !config.accessKeySecret || !config.signName) {
        this.logger.error('阿里云短信配置不完整');
        return false;
      }

      // 开发环境下模拟发送成功
      if (this.configService.get('NODE_ENV') === 'development') {
        this.logger.log(`阿里云模拟发送模板短信到 ${phone}, 模板: ${template}`);
        return true;
      }

      const params = {
        PhoneNumbers: phone,
        SignName: config.signName,
        TemplateCode: template,
        TemplateParam: JSON.stringify(data),
      };

      this.logger.log(`阿里云模板短信发送参数:`, params);
      return true;
    } catch (error) {
      this.logger.error('阿里云模板短信发送异常:', error);
      return false;
    }
  }

  /**
   * 腾讯云短信发送
   */
  private async sendTencentSms(
    phone: string,
    code: string,
    type: string,
    config: any,
  ): Promise<boolean> {
    try {
      if (!config || !config.secretId || !config.secretKey || !config.appId || !config.signName) {
        this.logger.error('腾讯云短信配置不完整');
        return false;
      }

      const templateId = config.templates[type] || config.templates.register;
      if (!templateId) {
        this.logger.error(`未找到短信模板: ${type}`);
        return false;
      }

      // 开发环境下模拟发送成功
      if (this.configService.get('NODE_ENV') === 'development') {
        this.logger.log(`腾讯云模拟发送验证码到 ${phone}: ${code}`);
        return true;
      }

      const params = {
        PhoneNumberSet: [phone],
        SmsSdkAppId: config.appId,
        SignName: config.signName,
        TemplateId: templateId,
        TemplateParamSet: [code],
      };

      this.logger.log(`腾讯云短信发送参数:`, params);
      return true;
    } catch (error) {
      this.logger.error('腾讯云短信发送异常:', error);
      return false;
    }
  }

  /**
   * 腾讯云模板短信发送
   */
  private async sendTencentTemplateSms(
    phone: string,
    template: string,
    data: Record<string, any>,
    config: any,
  ): Promise<boolean> {
    try {
      if (!config || !config.secretId || !config.secretKey || !config.appId || !config.signName) {
        this.logger.error('腾讯云短信配置不完整');
        return false;
      }

      // 开发环境下模拟发送成功
      if (this.configService.get('NODE_ENV') === 'development') {
        this.logger.log(`腾讯云模拟发送模板短信到 ${phone}, 模板: ${template}`);
        return true;
      }

      const params = {
        PhoneNumberSet: [phone],
        SmsSdkAppId: config.appId,
        SignName: config.signName,
        TemplateId: template,
        TemplateParamSet: Object.values(data),
      };

      this.logger.log(`腾讯云模板短信发送参数:`, params);
      return true;
    } catch (error) {
      this.logger.error('腾讯云模板短信发送异常:', error);
      return false;
    }
  }

  /**
   * 获取短信发送状态
   */
  async getSmsStatus(phone: string): Promise<{
    canSend: boolean;
    remainingDaily: number;
    nextAvailableTime?: Date;
  }> {
    const rateLimitCheck = await this.checkRateLimit(phone);
    const dailyLimitCheck = await this.checkDailyLimit(phone);

    const today = new Date().toISOString().split('T')[0];
    const dailyKey = `${this.SMS_DAILY_LIMIT_PREFIX}${phone}:${today}`;
    const dailyCount = await this.redisService.get(dailyKey);
    const currentDailyCount = dailyCount ? parseInt(dailyCount, 10) : 0;

    let nextAvailableTime: Date | undefined;
    if (!rateLimitCheck.canSend) {
      const rateLimitKey = `${this.SMS_RATE_LIMIT_PREFIX}${phone}`;
      const ttl = await this.redisService.getClient().ttl(rateLimitKey);
      nextAvailableTime = new Date(Date.now() + ttl * 1000);
    }

    return {
      canSend: rateLimitCheck.canSend && dailyLimitCheck.canSend,
      remainingDaily: Math.max(0, this.MAX_DAILY_SENDS - currentDailyCount),
      nextAvailableTime,
    };
  }
}