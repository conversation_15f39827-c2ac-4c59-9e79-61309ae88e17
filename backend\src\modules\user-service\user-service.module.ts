import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { UserServiceService } from './user-service.service';
import { UserServiceController } from './user-service.controller';
import { UserServiceEntity } from './entities/user-service.entity';
import { UserModule } from '../user/user.module';
import { ServiceModule } from '../service/service.module';
import { UserEntity } from '../user/entities/user.entity';
import { UserServiceListener } from './listeners/user-service.listener';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserServiceEntity, UserEntity]),
    forwardRef(() => UserModule),
    ServiceModule,
    EventEmitterModule.forRoot(),
  ],
  controllers: [UserServiceController],
  providers: [UserServiceService, UserServiceListener],
  exports: [UserServiceService],
})
export class UserServiceModule {}
