import { DataSource } from 'typeorm';
import { BaseSeed } from './base/base.seed';
import { SeedConfig, SeedResult } from './base/seed.interface';
import { UserServiceEntity } from '../../modules/user-service/entities/user-service.entity';
import { UserEntity, RoleEnum, TierEnum } from '../../modules/user/entities/user.entity';
import { ServiceEntity } from '../../modules/service/entities/service.entity';

/**
 * 用户服务关联种子数据
 * 为用户分配服务使用次数
 */
export class UserServicesSeed extends BaseSeed {
  config: SeedConfig = {
    name: 'user-services',
    description: '为用户分配服务使用次数',
    environments: ['development', 'test', 'production'],
    dependencies: ['users', 'services'],
    version: '1.0.0',
    priority: 4,
  };

  protected async execute(dataSource: DataSource): Promise<SeedResult> {
    const userServiceRepository = this.getRepository(dataSource, UserServiceEntity);
    const userRepository = this.getRepository(dataSource, UserEntity);
    const serviceRepository = this.getRepository(dataSource, ServiceEntity);
    
    // 获取所有用户和服务
    const users = await userRepository.find();
    const services = await serviceRepository.find();
    
    if (users.length === 0 || services.length === 0) {
      return {
        success: false,
        message: '未找到用户或服务数据',
      };
    }
    
    let affectedRows = 0;
    
    for (const user of users) {
      for (const service of services) {
        try {
          // 检查用户服务关联是否已存在
          const existingUserService = await userServiceRepository.findOne({
            where: {
              userId: user.id,
              serviceId: service.id,
            }
          });
          
          if (existingUserService && !this.config.force) {
            continue;
          }
          
          // 根据用户等级分配不同的使用次数
          const allocation = this.calculateServiceAllocation(user, service);
          
          const userServiceData = {
            userId: user.id,
            serviceId: service.id,
            totalCount: allocation.totalCount,
            usedCount: 0,
            remainingCount: allocation.totalCount,
            freeCount: allocation.freeCount,
            purchasedCount: allocation.purchasedCount,
            freeUsedToday: 0,
            lastResetDate: new Date(),
            alertSent: false,
            enabled: true,
            notes: allocation.notes,
          };
          
          if (existingUserService) {
            // 更新现有关联（保留已使用次数）
            const updateData = {
              ...userServiceData,
              usedCount: existingUserService.usedCount,
              remainingCount: allocation.totalCount - existingUserService.usedCount,
              freeUsedToday: existingUserService.freeUsedToday,
            };
            await userServiceRepository.update({ id: existingUserService.id }, updateData);
            this.logger.log(`更新用户 ${user.username} 的服务 ${service.name} 配额`);
          } else {
            // 创建新关联
            const newUserService = userServiceRepository.create(userServiceData);
            await userServiceRepository.save(newUserService);
            this.logger.log(`为用户 ${user.username} 分配服务 ${service.name}: ${allocation.totalCount} 次`);
          }
          
          affectedRows++;
          
        } catch (error) {
          this.logger.error(`为用户 ${user.username} 分配服务 ${service.name} 失败: ${error.message}`);
        }
      }
    }
    
    return {
      success: true,
      message: `成功创建/更新 ${affectedRows} 个用户服务关联`,
      affectedRows,
    };
  }
  
  /**
   * 计算服务分配
   */
  private calculateServiceAllocation(user: UserEntity, service: ServiceEntity): {
    totalCount: number;
    freeCount: number;
    purchasedCount: number;
    notes: string;
  } {
    let freeCount = 0;
    let purchasedCount = 0;
    let notes = '';
    
    // 根据用户角色和等级分配
    if (user.role === RoleEnum.ADMIN) {
      // 管理员获得大量免费次数
      freeCount = 10000;
      notes = '管理员账户，免费使用';
    } else {
      // 普通用户根据等级分配
      const baseFreeQuota = this.getBaseFreeQuota(service.code);
      
      switch (user.tier) {
        case TierEnum.BASIC:
          freeCount = baseFreeQuota;
          purchasedCount = 500; // 基础用户赠送500次
          notes = '基础用户，包含免费额度和赠送次数';
          break;
        case TierEnum.PREMIUM:
          freeCount = baseFreeQuota * 2;
          purchasedCount = 2000; // 高级用户赠送2000次
          notes = '高级用户，双倍免费额度和更多赠送次数';
          break;
        case TierEnum.VIP:
          freeCount = baseFreeQuota * 5;
          purchasedCount = 5000; // VIP用户赠送5000次
          notes = 'VIP用户，五倍免费额度和大量赠送次数';
          break;
        case TierEnum.ENTERPRISE:
          freeCount = baseFreeQuota * 10;
          purchasedCount = 20000; // 企业用户赠送20000次
          notes = '企业用户，十倍免费额度和海量赠送次数';
          break;
        default:
          freeCount = baseFreeQuota;
          purchasedCount = 100;
          notes = '默认用户配额';
      }
    }
    
    const totalCount = freeCount + purchasedCount;
    
    return {
      totalCount,
      freeCount,
      purchasedCount,
      notes,
    };
  }
  
  /**
   * 获取基础免费配额
   */
  private getBaseFreeQuota(serviceCode: string): number {
    const quotaMap = {
      'logistics-ocr': 100,        // 物流面单OCR识别
      'sto-ocr': 50,              // 申通面单OCR识别
      'address-extraction': 200,   // 地址文字块提取
      'geo-coordinate': 500,       // 地理坐标处理
    };

    return quotaMap[serviceCode] || 100;
  }
  
  /**
   * 检查是否需要执行
   */
  async shouldRun(dataSource: DataSource): Promise<boolean> {
    const userServiceRepository = this.getRepository(dataSource, UserServiceEntity);
    
    // 检查是否存在任何用户服务关联
    const count = await userServiceRepository.count();
    
    return count === 0;
  }
}
