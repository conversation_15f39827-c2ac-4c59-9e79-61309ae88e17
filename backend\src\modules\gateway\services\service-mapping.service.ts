import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { StructuredLogger } from '../../../common/logging/structured-logger';

/**
 * 服务映射接口
 */
interface ServiceMapping {
  id: number;
  name: string;
  code: string;
  pathPatterns: string[];
  description?: string;
}

/**
 * 服务映射服务
 * 负责维护API路径到服务ID的映射关系
 */
@Injectable()
export class ServiceMappingService implements OnModuleInit {
  private serviceMappings: Map<string, ServiceMapping> = new Map();
  private pathToServiceMap: Map<string, number> = new Map();

  constructor(
    private readonly logger: StructuredLogger,
  ) {}

  async onModuleInit() {
    await this.initializeServiceMappings();
  }

  /**
   * 初始化服务映射
   */
  private async initializeServiceMappings() {
    try {
      // 基于数据库中的实际服务定义创建映射
      const mappings: ServiceMapping[] = [
        {
          id: 1,
          name: 'OCR文件上传识别',
          code: 'ocr_file_upload',
          pathPatterns: ['/v1/op/ocr/file', '/ocr/file'],
          description: '支持图片文件上传进行OCR文字识别'
        },
        {
          id: 2,
          name: 'OCR Base64图片识别',
          code: 'ocr_base64',
          pathPatterns: ['/v1/op/ocr/base64', '/ocr/base64', '/ocr'],
          description: '支持Base64编码的图片进行OCR文字识别'
        },
        {
          id: 3,
          name: '申通面单OCR文件上传识别',
          code: 'ocr_shentong_file',
          pathPatterns: ['/v1/op/ocr/shentong/file', '/ocr/shentong/file'],
          description: '专门针对申通物流面单的文件上传OCR识别'
        },
        {
          id: 4,
          name: '申通面单OCR Base64识别',
          code: 'ocr_shentong_base64',
          pathPatterns: ['/v1/op/ocr/shentong/base64', '/ocr/shentong'],
          description: '专门针对申通物流面单的Base64图片OCR识别'
        },
        {
          id: 5,
          name: '从文本中提取地址信息',
          code: 'address_extract',
          pathPatterns: ['/v1/op/address/extract', '/address/extract', '/address'],
          description: '从文本中提取省市区、详细地址、手机号、姓名等地址相关信息'
        },
        {
          id: 6,
          name: '地址标准化',
          code: 'address_normalize',
          pathPatterns: ['/v1/op/address/normalize', '/address/normalize'],
          description: '将非标准地址格式转换为标准地址格式'
        },
        {
          id: 7,
          name: '逆地理编码（坐标转地址）',
          code: 'geo_reverse',
          pathPatterns: ['/v1/op/geo/reverse', '/geo/reverse', '/geo/decode'],
          description: '根据地理坐标（经纬度）获取对应的详细地址信息'
        },
        {
          id: 8,
          name: '正地理编码（地址转坐标）',
          code: 'geo_forward',
          pathPatterns: ['/v1/op/geo/forward', '/geo/forward', '/geo/encode', '/geo'],
          description: '根据地址信息获取对应的地理坐标（经纬度）'
        }
      ];

      // 构建映射表
      mappings.forEach(mapping => {
        this.serviceMappings.set(mapping.code, mapping);
        
        // 为每个路径模式创建映射
        mapping.pathPatterns.forEach(pattern => {
          this.pathToServiceMap.set(pattern, mapping.id);
        });
      });

      this.logger.log(`服务映射初始化完成，加载了 ${mappings.length} 个服务映射`);
      
      // 输出映射详情用于调试
      this.logger.debug('服务路径映射详情:');
      this.pathToServiceMap.forEach((serviceId, path) => {
        const mapping = Array.from(this.serviceMappings.values()).find(m => m.id === serviceId);
        this.logger.debug(`  ${path} → 服务ID ${serviceId} (${mapping?.name})`);
      });

    } catch (error) {
      this.logger.error('服务映射初始化失败', error);
    }
  }

  /**
   * 根据请求路径获取服务ID
   */
  getServiceIdFromPath(path: string): number | null {
    // 精确匹配
    if (this.pathToServiceMap.has(path)) {
      return this.pathToServiceMap.get(path)!;
    }

    // 模糊匹配 - 检查路径是否包含映射的模式
    for (const [pattern, serviceId] of this.pathToServiceMap.entries()) {
      if (path.includes(pattern)) {
        this.logger.debug(`路径 ${path} 匹配到模式 ${pattern}，服务ID: ${serviceId}`);
        return serviceId;
      }
    }

    this.logger.warn(`无法为路径 ${path} 找到对应的服务ID`);
    return null;
  }

  /**
   * 获取服务信息
   */
  getServiceInfo(serviceId: number): ServiceMapping | null {
    const mapping = Array.from(this.serviceMappings.values()).find(m => m.id === serviceId);
    return mapping || null;
  }

  /**
   * 获取所有服务映射
   */
  getAllServiceMappings(): ServiceMapping[] {
    return Array.from(this.serviceMappings.values());
  }

  /**
   * 刷新服务映射（用于动态更新）
   */
  async refreshServiceMappings(): Promise<void> {
    this.serviceMappings.clear();
    this.pathToServiceMap.clear();
    await this.initializeServiceMappings();
  }
}
