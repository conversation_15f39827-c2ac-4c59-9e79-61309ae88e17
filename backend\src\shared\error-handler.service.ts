import { Injectable, Logger, HttpStatus, HttpException } from '@nestjs/common';
import { ApiException } from '@/common/exceptions/api.exception';
import { ErrorCodeMapType } from '@/common/constant/error-code.contants';

/**
 * 错误处理服务
 * 提供统一的错误处理、状态码映射和错误分类
 * 解决错误处理不一致的问题
 */
@Injectable()
export class ErrorHandlerService {
  private readonly logger = new Logger(ErrorHandlerService.name);
  
  // 错误码到HTTP状态码的映射
  private readonly errorCodeMappings = new Map<string, HttpStatus>([
    // 认证相关错误
    ['AUTH_FAILED', HttpStatus.UNAUTHORIZED],
    ['AUTH_TOKEN_INVALID', HttpStatus.UNAUTHORIZED],
    ['AUTH_TOKEN_EXPIRED', HttpStatus.UNAUTHORIZED],
    ['AUTH_CREDENTIALS_INVALID', HttpStatus.UNAUTHORIZED],
    ['AUTH_REQUIRED', HttpStatus.UNAUTHORIZED],
    
    // 权限相关错误
    ['PERMISSION_DENIED', HttpStatus.FORBIDDEN],
    ['PERMISSION_INSUFFICIENT', HttpStatus.FORBIDDEN],
    ['ACCESS_FORBIDDEN', HttpStatus.FORBIDDEN],
    ['ROLE_REQUIRED', HttpStatus.FORBIDDEN],
    
    // 资源相关错误
    ['NOT_FOUND', HttpStatus.NOT_FOUND],
    ['USER_NOT_FOUND', HttpStatus.NOT_FOUND],
    ['RESOURCE_NOT_FOUND', HttpStatus.NOT_FOUND],
    ['SERVICE_NOT_FOUND', HttpStatus.NOT_FOUND],
    
    // 请求相关错误
    ['BAD_REQUEST', HttpStatus.BAD_REQUEST],
    ['INVALID_PARAMETER', HttpStatus.BAD_REQUEST],
    ['VALIDATION_FAILED', HttpStatus.BAD_REQUEST],
    ['MISSING_PARAMETER', HttpStatus.BAD_REQUEST],
    
    // 冲突相关错误
    ['CONFLICT', HttpStatus.CONFLICT],
    ['DUPLICATE_ENTRY', HttpStatus.CONFLICT],
    ['RESOURCE_EXISTS', HttpStatus.CONFLICT],
    
    // 服务器错误
    ['INTERNAL_ERROR', HttpStatus.INTERNAL_SERVER_ERROR],
    ['DATABASE_ERROR', HttpStatus.INTERNAL_SERVER_ERROR],
    ['SERVICE_UNAVAILABLE', HttpStatus.SERVICE_UNAVAILABLE],
    
    // 限流相关错误
    ['RATE_LIMIT_EXCEEDED', HttpStatus.TOO_MANY_REQUESTS],
    ['QUOTA_EXCEEDED', HttpStatus.TOO_MANY_REQUESTS],
    
    // 请求实体过大
    ['PAYLOAD_TOO_LARGE', HttpStatus.PAYLOAD_TOO_LARGE],
    
    // 不支持的媒体类型
    ['UNSUPPORTED_MEDIA_TYPE', HttpStatus.UNSUPPORTED_MEDIA_TYPE],
  ]);

  /**
   * 创建API异常
   */
  createApiException(
    errorCode: ErrorCodeMapType,
    message?: string,
    details?: any,
    httpStatus?: HttpStatus,
  ): ApiException {
    const status = httpStatus || this.mapErrorCodeToHttpStatus(errorCode);
    const finalMessage = message || this.getDefaultMessage(errorCode);
    
    return new ApiException(errorCode, status, details);
  }

  /**
   * 处理未知错误
   */
  handleUnknownError(error: any, context?: string): ApiException {
    this.logger.error(`Unknown error${context ? ` in ${context}` : ''}: ${error.message}`, error.stack);

    // 如果已经是ApiException，直接返回
    if (error instanceof ApiException) {
      return error;
    }

    // 处理 NestJS HTTP 异常
    if (error instanceof HttpException) {
      const status = error.getStatus();
      const response = error.getResponse();

      // 处理验证错误
      if (status === 400 && (error.constructor.name === 'BadRequestException' ||
          (typeof response === 'object' && response['message']))) {
        const message = typeof response === 'object' ?
          (Array.isArray(response['message']) ? response['message'].join(', ') : response['message']) :
          response;

        // 在开发环境下记录详细的验证错误
        if (process.env.NODE_ENV === 'development') {
          this.logger.error(`Validation error details: ${JSON.stringify(response, null, 2)}`);
        }

        return this.createApiException(10000, message);
      }

      // 处理其他HTTP异常
      const errorCodeMap = {
        401: 40001, // 身份验证失败
        403: 40002, // 权限不足
        404: 10017, // 资源不存在
        409: 10001, // 资源冲突
        429: 60005, // 请求频率超限
      };

      const errorCode = errorCodeMap[status] || 10000;
      const message = typeof response === 'string' ? response : error.message;
      return this.createApiException(errorCode, message);
    }

    // 根据错误类型进行分类
    if (error.name === 'ValidationError') {
      return this.createApiException(10000, error.message, error.details);
    }
    
    if (error.name === 'CastError') {
      return this.createApiException(10000, 'Invalid parameter format', error.path);
    }
    
    if (error.code === 11000) { // MongoDB duplicate key error
      return this.createApiException(10001, 'Resource already exists');
    }
    
    if (error.name === 'TimeoutError') {
      return this.createApiException(60001, 'Request timeout');
    }
    
    // 默认为内部服务器错误
    return this.createApiException(
      60001,
      'An unexpected error occurred',
      process.env.NODE_ENV === 'development' ? error.message : undefined,
    );
  }

  /**
   * 处理认证错误
   */
  handleAuthError(reason: string, details?: any): ApiException {
    const errorMappings = {
      'token_expired': 40007,
      'token_invalid': 40008,
      'credentials_invalid': 50005,
      'auth_required': 40001,
    };
    
    const errorCode = errorMappings[reason] || 40001;
    return this.createApiException(errorCode, undefined, details);
  }

  /**
   * 处理权限错误
   */
  handlePermissionError(reason: string, details?: any): ApiException {
    const errorMappings = {
      'insufficient_permissions': 40002,
      'role_required': 40003,
      'access_denied': 40004,
    };
    
    const errorCode = errorMappings[reason] || 40002;
    return this.createApiException(errorCode, undefined, details);
  }

  /**
   * 处理验证错误
   */
  handleValidationError(errors: any[]): ApiException {
    const details = errors.map(error => ({
      field: error.property || error.field,
      message: error.message || error.constraints,
      value: error.value,
    }));
    
    return this.createApiException(
      10000,
      'Request validation failed',
      details,
    );
  }

  /**
   * 处理资源未找到错误
   */
  handleNotFoundError(resourceType: string, identifier?: string | number): ApiException {
    const message = identifier 
      ? `${resourceType} with identifier '${identifier}' not found`
      : `${resourceType} not found`;
    
    return this.createApiException(10017, message, {
      resourceType,
      identifier,
    });
  }

  /**
   * 处理限流错误
   */
  handleRateLimitError(limit: number, windowMs: number, retryAfter?: number): ApiException {
    const message = `Rate limit exceeded. Maximum ${limit} requests per ${windowMs / 1000} seconds.`;
    
    return this.createApiException(60005, message, {
      limit,
      windowMs,
      retryAfter,
    });
  }

  /**
   * 处理数据库错误
   */
  handleDatabaseError(error: any): ApiException {
    this.logger.error(`Database error: ${error.message}`, error.stack);
    
    // 根据数据库错误类型进行分类
    if (error.code === 'ER_DUP_ENTRY' || error.code === 11000) {
      return this.createApiException(10001, 'Resource already exists');
    }
    
    if (error.code === 'ER_NO_REFERENCED_ROW_2') {
      return this.createApiException(10000, 'Referenced resource does not exist');
    }
    
    if (error.code === 'ECONNREFUSED') {
      return this.createApiException(60001, 'Database connection failed');
    }
    
    return this.createApiException(
      60001,
      'Database operation failed',
      process.env.NODE_ENV === 'development' ? error.message : undefined,
    );
  }

  /**
   * 映射错误码到HTTP状态码
   */
  mapErrorCodeToHttpStatus(errorCode: ErrorCodeMapType): HttpStatus {
    // 精确匹配
    const errorCodeStr = String(errorCode);
    if (this.errorCodeMappings.has(errorCodeStr)) {
      return this.errorCodeMappings.get(errorCodeStr)!;
    }
    
    // 模糊匹配
    const upperErrorCode = errorCodeStr.toUpperCase();
    
    if (upperErrorCode.includes('AUTH')) {
      return HttpStatus.UNAUTHORIZED;
    }
    
    if (upperErrorCode.includes('PERMISSION') || upperErrorCode.includes('FORBIDDEN')) {
      return HttpStatus.FORBIDDEN;
    }
    
    if (upperErrorCode.includes('NOT_FOUND') || upperErrorCode.includes('NOTFOUND')) {
      return HttpStatus.NOT_FOUND;
    }
    
    if (upperErrorCode.includes('VALIDATION') || upperErrorCode.includes('INVALID')) {
      return HttpStatus.BAD_REQUEST;
    }
    
    if (upperErrorCode.includes('CONFLICT') || upperErrorCode.includes('DUPLICATE')) {
      return HttpStatus.CONFLICT;
    }
    
    if (upperErrorCode.includes('RATE_LIMIT') || upperErrorCode.includes('QUOTA')) {
      return HttpStatus.TOO_MANY_REQUESTS;
    }
    
    if (upperErrorCode.includes('UNAVAILABLE') || upperErrorCode.includes('TIMEOUT')) {
      return HttpStatus.SERVICE_UNAVAILABLE;
    }
    
    // 默认为400 Bad Request
    return HttpStatus.BAD_REQUEST;
  }

  /**
   * 获取默认错误消息
   */
  private getDefaultMessage(errorCode: ErrorCodeMapType): string {
    const errorCodeStr = String(errorCode);
    const defaultMessages = {
      'AUTH_FAILED': 'Authentication failed',
      'AUTH_TOKEN_INVALID': 'Invalid authentication token',
      'AUTH_TOKEN_EXPIRED': 'Authentication token has expired',
      'AUTH_CREDENTIALS_INVALID': 'Invalid credentials',
      'AUTH_REQUIRED': 'Authentication required',
      
      'PERMISSION_DENIED': 'Permission denied',
      'PERMISSION_INSUFFICIENT': 'Insufficient permissions',
      'ACCESS_FORBIDDEN': 'Access forbidden',
      'ROLE_REQUIRED': 'Required role not found',
      
      'NOT_FOUND': 'Resource not found',
      'USER_NOT_FOUND': 'User not found',
      'RESOURCE_NOT_FOUND': 'Resource not found',
      'SERVICE_NOT_FOUND': 'Service not found',
      
      'BAD_REQUEST': 'Bad request',
      'INVALID_PARAMETER': 'Invalid parameter',
      'VALIDATION_FAILED': 'Validation failed',
      'MISSING_PARAMETER': 'Missing required parameter',
      
      'CONFLICT': 'Resource conflict',
      'DUPLICATE_ENTRY': 'Resource already exists',
      'RESOURCE_EXISTS': 'Resource already exists',
      
      'INTERNAL_ERROR': 'Internal server error',
      'DATABASE_ERROR': 'Database error',
      'SERVICE_UNAVAILABLE': 'Service unavailable',
      
      'RATE_LIMIT_EXCEEDED': 'Rate limit exceeded',
      'QUOTA_EXCEEDED': 'Quota exceeded',
      
      'PAYLOAD_TOO_LARGE': 'Request payload too large',
      'UNSUPPORTED_MEDIA_TYPE': 'Unsupported media type',
    };
    
    return defaultMessages[errorCodeStr] || 'An error occurred';
  }

  /**
   * 记录错误
   */
  logError(error: any, context?: string, userId?: string | number): void {
    const logContext = {
      context,
      userId,
      timestamp: new Date().toISOString(),
      errorCode: error.errorCode || 'UNKNOWN',
      message: error.message,
      stack: error.stack,
    };
    
    if (error instanceof ApiException) {
      // 根据错误级别记录日志
      if (error.getStatus() >= 500) {
        this.logger.error('Server error occurred', logContext);
      } else if (error.getStatus() >= 400) {
        this.logger.warn('Client error occurred', logContext);
      } else {
        this.logger.log('Request processed with error', logContext);
      }
    } else {
      this.logger.error('Unexpected error occurred', logContext);
    }
  }

  /**
   * 检查错误是否为客户端错误
   */
  isClientError(error: any): boolean {
    if (error instanceof ApiException) {
      const status = error.getStatus();
      return status >= 400 && status < 500;
    }
    return false;
  }

  /**
   * 检查错误是否为服务器错误
   */
  isServerError(error: any): boolean {
    if (error instanceof ApiException) {
      const status = error.getStatus();
      return status >= 500;
    }
    return true; // 未知错误默认为服务器错误
  }
}