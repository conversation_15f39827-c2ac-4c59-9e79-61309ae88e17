import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';
import { Exclude } from 'class-transformer';
import { GenerateUUID } from '@/common/utils/index';
// 移除直接导入其他模块的Entity，使用字符串关系定义避免循环依赖
// import { ServiceEntity } from '../../service/entities/service.entity';
// import { UserEntity } from '../../user/entities/user.entity';

/**
 * API密钥状态枚举
 */
export enum ApiKeyStatus {
  ACTIVE = 'active', // 激活
  INACTIVE = 'inactive', // 未激活
  EXPIRED = 'expired', // 已过期
  REVOKED = 'revoked', // 已撤销
}

// API密钥实体类，查看secretHash字段定义
@Entity('open_api_key', {
  comment: 'API密钥表',
})
@Index(['userId'])
@Index(['keyStatus'])
export class ApiKeyEntity extends BaseEntity {
  @Column({ type: 'int', name: 'user_id', comment: '用户ID' })
  public userId: number;

  @Column({ type: 'int', name: 'service_id', comment: '服务ID' })
  public serviceId: number;

  @Column({
    type: 'varchar',
    length: 100,
    comment: '密钥名称',
  })
  name: string;

  @Column({
    type: 'varchar',
    length: 64,
    unique: true,
    comment: 'API密钥（明文）',
  })
  apiKey: string;

  @Column({
    type: 'varchar',
    length: 64,
    unique: true,
    comment: 'API密钥秘钥哈希',
  })
  secretHash: string;

  @Column({
    type: 'enum',
    enum: ['trial', 'basic', 'premium', 'enterprise'],
    default: 'trial',
    comment: '密钥类型',
  })
  keyType: string;

  @Column({
    type: 'enum',
    enum: ApiKeyStatus,
    default: ApiKeyStatus.ACTIVE,
    comment: '密钥状态',
    name: 'key-status',
  })
  keyStatus: ApiKeyStatus;

  @Column({ type: 'text', nullable: true, comment: '密钥描述' })
  description: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: '权限范围',
    transformer: {
      to: (value: string[] | string): string => {
        if (value === null || value === undefined) {
          return '[]';
        }
        // 如果已经是字符串，检查是否是有效的JSON
        if (typeof value === 'string') {
          try {
            // 尝试解析，如果是有效的JSON，直接返回
            JSON.parse(value);
            return value;
          } catch (e) {
            // 如果不是有效的JSON，将其作为单个元素的数组处理
            return JSON.stringify([value]);
          }
        }
        // 否则转换为JSON字符串
        return JSON.stringify(value);
      },
      from: (value: string): string[] => {
        if (!value) {
          return [];
        }
        try {
          const parsed = JSON.parse(value);
          return Array.isArray(parsed) ? parsed : [parsed];
        } catch (e) {
          console.error('解析permissions字段失败:', e, '原始值:', value);
          return [];
        }
      },
    }
  })
  permissions: string[];

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '过期时间',
  })
  expiresAt?: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '最后使用时间',
  })
  lastUsedAt?: Date;

  @Column({
    type: 'boolean',
    name: 'is_secret_viewed',
    default: false,
    comment: '密钥是否已被查看过（用于仅显示一次）',
  })
  isSecretViewed: boolean;

  @Column({
    type: 'varchar',
    length: 45,
    nullable: true,
    comment: '最后使用IP',
  })
  lastUsedIp?: string;

  @Column({
    type: 'varchar',
    length: 128,
    nullable: true,
    comment: '临时明文密钥，仅未查看时保存'
  })
  tempSecretKey?: string;

  @Column({
    type: 'varchar',
    length: 512,
    nullable: true,
    comment: '加密存储的密钥，用于长期保存和恢复，通过系统密钥加密'
  })
  encryptedSecretKey?: string;

  // 关联关系 - 使用字符串关系定义，避免循环引用
  @ManyToOne('UserEntity', 'apiKeys')
  @JoinColumn({ name: 'userId' })
  user: any;

  @ManyToOne('ServiceEntity', 'apiKeys')
  @JoinColumn({ name: 'serviceId' })
  service: any;
}
