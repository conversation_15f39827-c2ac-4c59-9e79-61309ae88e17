/**
 * API调用处理器
 * 处理API调用任务的具体逻辑
 */

import { Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bull';
import axios, { AxiosResponse, AxiosError } from 'axios';
import { QUEUE_NAMES } from '../queue.constants';
import { ApiCallTaskData, TaskResult } from '../interfaces/queue.interface';

@Injectable()
@Processor(QUEUE_NAMES.API_CALL)
export class ApiProcessor {
  private readonly logger = new Logger(ApiProcessor.name);

  @Process('api_call')
  async handleApiCall(job: Job<ApiCallTaskData>): Promise<TaskResult> {
    const { data } = job;
    const startTime = Date.now();

    this.logger.log(`开始处理API调用任务: ${job.id}, URL: ${data.url}`);

    try {
      // 更新任务进度
      await job.progress(10);

      // 验证任务数据
      this.validateTaskData(data);
      await job.progress(20);

      // 准备请求配置
      const requestConfig = this.prepareRequestConfig(data);
      await job.progress(30);

      // 执行API调用
      const response = await this.executeApiCall(requestConfig);
      await job.progress(80);

      // 处理响应
      const result = this.processResponse(response);
      await job.progress(100);

      const duration = Date.now() - startTime;
      this.logger.log(`API调用任务完成: ${job.id}, 耗时: ${duration}ms`);

      return {
        success: true,
        data: result,
        duration,
        retryCount: job.attemptsMade,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`API调用任务失败: ${job.id}, 错误: ${error.message}`, error.stack);

      return {
        success: false,
        error: error.message,
        duration,
        retryCount: job.attemptsMade,
      };
    }
  }

  /**
   * 验证任务数据
   */
  private validateTaskData(data: ApiCallTaskData): void {
    if (!data.url) {
      throw new Error('URL不能为空');
    }

    if (!data.method) {
      throw new Error('HTTP方法不能为空');
    }

    const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
    if (!validMethods.includes(data.method)) {
      throw new Error(`无效的HTTP方法: ${data.method}`);
    }

    // 验证URL格式
    try {
      new URL(data.url);
    } catch {
      throw new Error(`无效的URL格式: ${data.url}`);
    }
  }

  /**
   * 准备请求配置
   */
  private prepareRequestConfig(data: ApiCallTaskData) {
    const config = {
      method: data.method,
      url: data.url,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'NestJS-Queue-Service/1.0',
        ...data.headers,
      },
      timeout: data.timeout || 30000,
      validateStatus: (status: number) => status < 500, // 只有5xx错误才重试
    };

    // 添加请求体（如果有）
    if (data.body && ['POST', 'PUT', 'PATCH'].includes(data.method)) {
      config['data'] = data.body;
    }

    return config;
  }

  /**
   * 执行API调用
   */
  private async executeApiCall(config: any): Promise<AxiosResponse> {
    try {
      const response = await axios(config);
      return response;
    } catch (error) {
      if (error.response) {
        // 服务器响应了错误状态码
        const { status, statusText, data } = error.response;
        throw new Error(`API调用失败: ${status} ${statusText}, 响应: ${JSON.stringify(data)}`);
      } else if (error.request) {
        // 请求发出但没有收到响应
        throw new Error('API调用超时或网络错误');
      } else {
        // 其他错误
        throw new Error(`请求配置错误: ${error.message}`);
      }
    }
  }

  /**
   * 处理响应
   */
  private processResponse(response: AxiosResponse) {
    return {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 判断是否为可重试的错误
   */
  private isRetryableError(error: AxiosError): boolean {
    // 网络错误或5xx服务器错误可以重试
    if (!error.response) {
      return true;
    }

    const status = error.response.status;
    return status >= 500 || status === 429; // 5xx错误或限流错误
  }
}