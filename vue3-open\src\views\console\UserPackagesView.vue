<template>
  <div class="user-packages-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">我的套餐包</h1>
      <p class="page-subtitle">管理您购买的套餐包和服务</p>
      
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        
        <el-button type="primary" @click="$router.push('/packages')">
          <el-icon><Plus /></el-icon>
          购买套餐包
        </el-button>
      </div>
    </div>

    <!-- 套餐包统计 -->
    <div class="stats-overview">
      <div class="stats-cards">
        <div class="stats-card">
          <div class="stats-icon">
            <el-icon :size="32" color="#3b82f6">
              <Box />
            </el-icon>
          </div>
          <div class="stats-info">
            <h3>总套餐包</h3>
            <p class="stats-number">{{ packageStats.total }}</p>
            <span class="stats-desc">已购买套餐包数量</span>
          </div>
        </div>
        
        <div class="stats-card">
          <div class="stats-icon">
            <el-icon :size="32" color="#10b981">
              <CircleCheck />
            </el-icon>
          </div>
          <div class="stats-info">
            <h3>活跃套餐包</h3>
            <p class="stats-number">{{ packageStats.active }}</p>
            <span class="stats-desc">正在使用的套餐包</span>
          </div>
        </div>
        
        <div class="stats-card">
          <div class="stats-icon">
            <el-icon :size="32" color="#f59e0b">
              <Clock />
            </el-icon>
          </div>
          <div class="stats-info">
            <h3>即将到期</h3>
            <p class="stats-number">{{ packageStats.expiringSoon }}</p>
            <span class="stats-desc">7天内到期</span>
          </div>
        </div>
        
        <div class="stats-card">
          <div class="stats-icon">
            <el-icon :size="32" color="#8b5cf6">
              <Money />
            </el-icon>
          </div>
          <div class="stats-info">
            <h3>总消费</h3>
            <p class="stats-number">¥{{ formatNumber(packageStats.totalSpent) }}</p>
            <span class="stats-desc">累计消费金额</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-select v-model="filters.status" @change="handleFilterChange" placeholder="套餐包状态" style="width: 150px;">
          <el-option label="全部状态" value="" />
          <el-option label="活跃" value="active" />
          <el-option label="已暂停" value="paused" />
          <el-option label="已过期" value="expired" />
          <el-option label="已取消" value="cancelled" />
        </el-select>
        
        <el-select v-model="filters.type" @change="handleFilterChange" placeholder="套餐包类型" style="width: 150px;">
          <el-option label="全部类型" value="" />
          <el-option label="基础版" value="basic" />
          <el-option label="专业版" value="professional" />
          <el-option label="企业版" value="enterprise" />
          <el-option label="定制版" value="custom" />
        </el-select>
        
        <el-date-picker
          v-model="filters.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleFilterChange"
          style="width: 240px;"
        />
      </div>
      
      <div class="filter-right">
        <el-input
          v-model="filters.keyword"
          placeholder="搜索套餐包名称或描述"
          @input="handleSearch"
          style="width: 300px;"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 套餐包列表 -->
    <div class="packages-section">
      <div class="packages-grid" v-if="filteredPackages.length > 0">
        <div 
          v-for="userPackage in paginatedPackages" 
          :key="userPackage.id"
          class="package-card"
          :class="{ 
            'expired': userPackage.status === 'expired',
            'expiring-soon': isExpiringSoon(userPackage.expiredAt || '')
          }"
        >
          <div class="package-header">
            <div class="package-info">
              <div class="package-icon">
                <el-icon :size="24">
                  <component :is="getPackageIcon(userPackage.package.type)" />
                </el-icon>
              </div>
              <div class="package-details">
                <h3 class="package-name">{{ userPackage.package.name }}</h3>
                <p class="package-description">{{ userPackage.package.description }}</p>
              </div>
            </div>
            
            <div class="package-status">
              <el-tag :type="getStatusType(userPackage.status)" size="small">
                {{ getStatusText(userPackage.status) }}
              </el-tag>
            </div>
          </div>
          
          <div class="package-content">
            <!-- 套餐包信息 -->
            <div class="package-meta">
              <div class="meta-item">
                <span class="meta-label">购买时间</span>
                <span class="meta-value">{{ formatDate(userPackage.purchasedAt) }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">有效期至</span>
                <span class="meta-value" :class="{ 'text-warning': isExpiringSoon(userPackage.expiredAt || ''), 'text-danger': userPackage.status === 'expired' }">
                  {{ formatDate(userPackage.expiredAt || '') }}
                </span>
              </div>
              <div class="meta-item">
                <span class="meta-label">剩余天数</span>
                <span class="meta-value" :class="{ 'text-warning': getRemainingDays(userPackage.expiredAt || '') <= 7, 'text-danger': getRemainingDays(userPackage.expiredAt || '') <= 0 }">
                  {{ getRemainingDays(userPackage.expiredAt || '') }}天
                </span>
              </div>
              <div class="meta-item">
                <span class="meta-label">自动续费</span>
                <span class="meta-value">
                  <el-switch 
                    v-model="userPackage.autoRenew" 
                    @change="toggleAutoRenew(userPackage)"
                    :disabled="userPackage.status === 'expired' || userPackage.status === 'cancelled'"
                  />
                </span>
              </div>
            </div>
            
            <!-- 使用情况 -->
            <div class="usage-section" v-if="userPackage.usage">
              <h4 class="usage-title">使用情况</h4>
              <div class="usage-items">
                <div 
                  v-for="(usage, key) in userPackage.usage" 
                  :key="key"
                  class="usage-item"
                >
                  <div class="usage-info">
                    <span class="usage-name">{{ getUsageName(key) }}</span>
                    <span class="usage-value">{{ usage.used }}/{{ usage.limit === -1 ? '无限' : usage.limit }}</span>
                  </div>
                  <div class="usage-progress">
                    <el-progress 
                      :percentage="usage.limit === -1 ? 0 : Math.min((usage.used / usage.limit) * 100, 100)" 
                      :color="getUsageColor(usage.used, usage.limit)"
                      :stroke-width="6"
                      :show-text="false"
                    />
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 套餐包特性 -->
            <div class="features-section">
              <h4 class="features-title">套餐包特性</h4>
              <div class="features-list">
                <div 
                  v-for="feature in userPackage.package.features.slice(0, 3)" 
                  :key="feature.name"
                  class="feature-item"
                >
                  <el-icon class="feature-icon" :color="feature.included ? '#10b981' : '#ef4444'">
                    <component :is="feature.included ? 'CircleCheck' : 'CircleClose'" />
                  </el-icon>
                  <span class="feature-name">{{ feature.name }}</span>
                </div>
                <div v-if="userPackage.package.features.length > 3" class="feature-more">
                  +{{ userPackage.package.features.length - 3 }}个特性
                </div>
              </div>
            </div>
          </div>
          
          <div class="package-actions">
            <el-button size="small" @click="viewPackageDetail(userPackage)">
              查看详情
            </el-button>
            
            <el-button 
              size="small" 
              type="primary" 
              @click="renewPackage(userPackage)"
              v-if="userPackage.status === 'active' || userPackage.status === 'expired'"
            >
              续费
            </el-button>
            
            <el-button 
              size="small" 
              @click="pausePackage(userPackage)"
              v-if="userPackage.status === 'active'"
            >
              暂停
            </el-button>
            
            <el-button 
              size="small" 
              type="success" 
              @click="resumePackage(userPackage)"
              v-if="userPackage.status === 'paused'"
            >
              恢复
            </el-button>
            
            <el-button 
              size="small" 
              type="danger" 
              @click="cancelPackage(userPackage)"
              v-if="userPackage.status === 'active' || userPackage.status === 'paused'"
            >
              取消
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="empty-state">
        <el-empty description="暂无套餐包">
          <el-button type="primary" @click="$router.push('/packages')">
            去购买套餐包
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-section" v-if="filteredPackages.length > 0">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="filteredPackages.length"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 套餐包详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="套餐包详情"
      width="800px"
      :before-close="handleDetailDialogClose"
    >
      <div v-if="selectedPackage" class="package-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="detail-label">套餐包名称</span>
              <span class="detail-value">{{ selectedPackage.package.name }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">套餐包类型</span>
              <span class="detail-value">{{ getPackageTypeText(selectedPackage.package.type) }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">购买时间</span>
              <span class="detail-value">{{ formatDateTime(selectedPackage.purchasedAt) }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">有效期至</span>
              <span class="detail-value">{{ formatDateTime(selectedPackage.expiredAt || '') }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">状态</span>
              <span class="detail-value">
                <el-tag :type="getStatusType(selectedPackage.status)">
                  {{ getStatusText(selectedPackage.status) }}
                </el-tag>
              </span>
            </div>
            <div class="detail-item">
              <span class="detail-label">自动续费</span>
              <span class="detail-value">{{ selectedPackage.autoRenew ? '已开启' : '已关闭' }}</span>
            </div>
          </div>
        </div>
        
        <!-- 使用统计 -->
        <div class="detail-section" v-if="selectedPackage?.usage">
          <h3 class="section-title">使用统计</h3>
          <div class="usage-detail">
            <div 
              v-for="(usage, key) in selectedPackage?.usage" 
              :key="key"
              class="usage-detail-item"
            >
              <div class="usage-header">
                <span class="usage-name">{{ getUsageName(key) }}</span>
                <span class="usage-value">{{ usage.used }}/{{ usage.limit === -1 ? '无限' : usage.limit }}</span>
              </div>
              <el-progress 
                :percentage="usage.limit === -1 ? 0 : Math.min((usage.used / usage.limit) * 100, 100)" 
                :color="getUsageColor(usage.used, usage.limit)"
              />
            </div>
          </div>
        </div>
        
        <!-- 套餐包特性 -->
        <div class="detail-section">
          <h3 class="section-title">套餐包特性</h3>
          <div class="features-detail">
            <div 
              v-for="feature in selectedPackage.package.features" 
              :key="feature.name"
              class="feature-detail-item"
            >
              <el-icon class="feature-icon" :color="feature.included ? '#10b981' : '#ef4444'">
                <component :is="feature.included ? 'CircleCheck' : 'CircleClose'" />
              </el-icon>
              <div class="feature-content">
                <span class="feature-name">{{ feature.name }}</span>
                <span class="feature-description">{{ feature.description }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="renewPackage(selectedPackage)" v-if="selectedPackage && (selectedPackage.status === 'active' || selectedPackage.status === 'expired')">
            续费套餐包
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Plus,
  Box,
  CircleCheck,
  CircleClose, // eslint-disable-line @typescript-eslint/no-unused-vars
  Clock,
  Money,
  Search,
  Setting,
  Star,
  Trophy
} from '@element-plus/icons-vue'
import { usePackageStore } from '@/stores/package'
import type { UserPackage } from '@/types/package'

// 使用 store
const packageStore: any = usePackageStore()

// 响应式数据
const loading = ref(false)
const detailDialogVisible = ref(false)
const selectedPackage:any = ref<UserPackage | null>(null)

// 筛选条件
const filters = reactive({
  status: '',
  type: '',
  keyword: '',
  dateRange: null as [Date, Date] | null
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10
})

// 套餐包统计
const packageStats = computed(() => {
  const packages: any = packageStore.userPackages
  return {
    total: packages.length,
    active: packages.filter((p:any) => p.status === 'active').length,
    expiringSoon: packages.filter((p:any) => isExpiringSoon(p.expiredAt || '')).length,
    totalSpent: packages.reduce((sum:any, p:any) => sum + (p.renewalPrice || 0), 0)
  }
})

// 筛选后的套餐包
const filteredPackages = computed(() => {
  let packages = packageStore.userPackages
  
  // 状态筛选
  if (filters.status) {
    packages = packages.filter((p:any) => p.status === filters.status)
  }
  
  // 类型筛选
  if (filters.type) {
    packages = packages.filter((p:any) => p.package.type === filters.type)
  }
  
  // 关键词搜索
  if (filters.keyword) {
    const keyword = filters.keyword.toLowerCase()
    packages = packages.filter((p:any) => 
      p.package.name.toLowerCase().includes(keyword) ||
      p.package.description.toLowerCase().includes(keyword)
    )
  }
  
  // 日期范围筛选
  if (filters.dateRange) {
    const [start, end] = filters.dateRange
    packages = packages.filter((p:any) => {
      const purchaseDate = new Date(p.purchasedAt)
      return purchaseDate >= start && purchaseDate <= end
    })
  }
  
  return packages
})

// 分页后的套餐包
const paginatedPackages = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredPackages.value.slice(start, end)
})

// 方法
const getPackageIcon = (type: string) => {
  const icons: Record<string, any> = {
    basic: Setting,
    professional: Star,
    enterprise: Trophy,
    custom: Setting
  }
  return icons[type] || Setting
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    active: 'success',
    paused: 'warning',
    expired: 'danger',
    cancelled: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    active: '活跃',
    paused: '已暂停',
    expired: '已过期',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getPackageTypeText = (type: string) => {
  const texts: Record<string, string> = {
    basic: '基础版',
    professional: '专业版',
    enterprise: '企业版',
    custom: '定制版'
  }
  return texts[type] || type
}

const getUsageName = (key: string | number) => {
  const names: Record<string, string> = {
    apiCalls: 'API调用次数',
    storage: '存储空间',
    bandwidth: '带宽使用',
    users: '用户数量',
    projects: '项目数量'
  }
  return names[key] || key
}

const getUsageColor = (used: number, limit: number) => {
  if (limit === -1) return '#10b981'
  const percentage = (used / limit) * 100
  if (percentage >= 90) return '#ef4444'
  if (percentage >= 70) return '#f59e0b'
  return '#10b981'
}

const isExpiringSoon = (expiresAt: string) => {
  const expireDate = new Date(expiresAt)
  const now = new Date()
  const diffTime = expireDate.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays <= 7 && diffDays > 0
}

const getRemainingDays = (expiresAt: string) => {
  const expireDate = new Date(expiresAt)
  const now = new Date()
  const diffTime = expireDate.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
}

const formatNumber = (num: number) => {
  return num.toLocaleString()
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatDateTime = (date: string) => {
  return new Date(date).toLocaleString('zh-CN')
}

const refreshData = async () => {
  loading.value = true
  try {
    await packageStore.fetchUserPackages()
    ElMessage.success('数据已刷新')
  } catch (error) {
    console.error('刷新失败:', error)
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const handleFilterChange = () => {
  pagination.currentPage = 1
}

const handleSearch = () => {
  pagination.currentPage = 1
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
}

const viewPackageDetail = (userPackage: UserPackage) => {
  selectedPackage.value = userPackage
  detailDialogVisible.value = true
}

const handleDetailDialogClose = () => {
  detailDialogVisible.value = false
  selectedPackage.value = null
}

const toggleAutoRenew = async (userPackage: UserPackage) => {
  try {
    await packageStore.setAutoRenew(userPackage.id, userPackage.autoRenew)
    ElMessage.success(`自动续费已${userPackage.autoRenew ? '开启' : '关闭'}`)
  } catch (error) {
    console.error('设置自动续费失败:', error)
    userPackage.autoRenew = !userPackage.autoRenew // 回滚
    ElMessage.error('设置失败')
  }
}

const renewPackage = async (userPackage: UserPackage) => {
  try {
    await ElMessageBox.confirm(
      `确定要续费套餐包「${userPackage.package.name}」吗？`,
      '续费确认',
      {
        confirmButtonText: '确定续费',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await packageStore.renewPackage(userPackage.id)
    ElMessage.success('续费成功')
    await refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('续费失败')
    }
  }
}

const pausePackage = async (userPackage: UserPackage) => {
  try {
    await ElMessageBox.confirm(
      `确定要暂停套餐包「${userPackage.package.name}」吗？暂停期间将无法使用相关服务。`,
      '暂停确认',
      {
        confirmButtonText: '确定暂停',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 实现暂停套餐包功能
    ElMessage.info('暂停功能暂未实现')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('暂停失败')
    }
  }
}

const resumePackage = async (userPackage: UserPackage) => {
  try {
    // TODO: 实现恢复套餐包功能
    ElMessage.info('恢复功能暂未实现')
  } catch (error) {
    console.error('恢复套餐包失败:', error)
    ElMessage.error('恢复失败')
  }
}

const cancelPackage = async (userPackage: UserPackage) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消套餐包「${userPackage.package.name}」吗？取消后将无法恢复，剩余时间不予退款。`,
      '取消确认',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '我再想想',
        type: 'error'
      }
    )
    
    await packageStore.cancelPackage(userPackage.id)
    ElMessage.success('套餐包已取消')
    await refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消失败')
    }
  }
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.user-packages-page {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: #1f2937;
}

.page-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 16px;
}

.stats-overview {
  margin-bottom: 32px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.stats-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stats-info h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #374151;
}

.stats-number {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 4px 0;
  color: #3b82f6;
}

.stats-desc {
  font-size: 12px;
  color: #9ca3af;
  margin: 0;
}

.filter-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.filter-left {
  display: flex;
  gap: 16px;
  align-items: center;
}

.packages-section {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.packages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
}

.package-card {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  background: white;
  transition: all 0.3s ease;
  position: relative;
}

.package-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.package-card.expired {
  border-color: #ef4444;
  background: #fef2f2;
}

.package-card.expiring-soon {
  border-color: #f59e0b;
  background: #fffbeb;
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.package-info {
  display: flex;
  gap: 12px;
  flex: 1;
}

.package-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.package-details {
  flex: 1;
  min-width: 0;
}

.package-name {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #1f2937;
}

.package-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.package-content {
  margin-bottom: 20px;
}

.package-meta {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.meta-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.meta-value {
  font-size: 13px;
  color: #374151;
  font-weight: 600;
}

.text-warning {
  color: #f59e0b !important;
}

.text-danger {
  color: #ef4444 !important;
}

.usage-section {
  margin-bottom: 20px;
}

.usage-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #374151;
}

.usage-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.usage-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.usage-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 120px;
  flex-shrink: 0;
}

.usage-name {
  font-size: 12px;
  color: #6b7280;
}

.usage-value {
  font-size: 12px;
  color: #374151;
  font-weight: 600;
}

.usage-progress {
  flex: 1;
}

.features-section {
  margin-bottom: 20px;
}

.features-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #374151;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #374151;
}

.feature-icon {
  flex-shrink: 0;
}

.feature-name {
  flex: 1;
}

.feature-more {
  font-size: 12px;
  color: #9ca3af;
  font-style: italic;
  margin-left: 24px;
}

.package-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

/* 对话框样式 */
.package-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: #1f2937;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.detail-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: #374151;
  font-weight: 600;
}

.usage-detail {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.usage-detail-item {
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.usage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.features-detail {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-detail-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.feature-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.feature-description {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .filter-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-left {
    flex-direction: column;
    align-items: stretch;
  }
  
  .packages-grid {
    grid-template-columns: 1fr;
  }
  
  .package-meta {
    grid-template-columns: 1fr;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
}
</style>