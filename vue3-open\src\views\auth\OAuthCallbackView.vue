<template>
  <div class="oauth-callback-page">
    <div class="callback-container">
      <div class="loading-content">
        <el-icon class="loading-icon" :size="48">
          <Loading />
        </el-icon>
        <h2>正在处理登录...</h2>
        <p>{{ statusMessage }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { useOAuthStore } from '@/stores/oauth'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const route = useRoute()
const oauthStore = useOAuthStore()
const userStore = useUserStore()

const statusMessage = ref('正在验证授权信息...')

onMounted(async () => {
  try {
    // 获取URL参数
    const code = route.query.code as string
    const state = route.query.state as string
    const error = route.query.error as string
    
    // 检查是否有错误
    if (error) {
      throw new Error(`授权失败: ${error}`)
    }
    
    if (!code) {
      throw new Error('未获取到授权码')
    }
    
    // 获取保存的provider信息
    const provider = localStorage.getItem('oauth_provider')
    if (!provider) {
      throw new Error('未找到第三方平台信息')
    }
    
    statusMessage.value = `正在处理${getProviderName(provider)}登录...`
    
    // 处理第三方登录回调
    const response = await oauthStore.handleOAuthCallback(provider, code)
    
    if (response.success && response.data) {
      // 登录成功，保存用户信息和token
      const { token, refreshToken, user, apiKeys } = response.data
      
      // 更新用户store
      userStore.setToken(token)
      userStore.setRefreshToken(refreshToken)
      userStore.setUserInfo(user)
      
      // 保存到localStorage
      localStorage.setItem('token', token)
      localStorage.setItem('refreshToken', refreshToken)
      localStorage.setItem('userInfo', JSON.stringify(user))
      
      ElMessage.success(`${getProviderName(provider)}登录成功`)
      
      // 清理临时数据
      localStorage.removeItem('oauth_provider')
      
      // 跳转到目标页面
      const redirectUrl = localStorage.getItem('oauth_redirect') || '/console'
      localStorage.removeItem('oauth_redirect')
      
      router.replace(redirectUrl)
    } else {
      throw new Error(response.message || '登录失败')
    }
    
  } catch (error: any) {
    console.error('OAuth回调处理失败:', error)
    ElMessage.error(error.message || '登录失败')
    
    // 清理临时数据
    localStorage.removeItem('oauth_provider')
    localStorage.removeItem('oauth_redirect')
    
    // 跳转回登录页面
    setTimeout(() => {
      router.replace('/login')
    }, 2000)
  }
})

// 获取第三方平台名称
const getProviderName = (provider: string): string => {
  const names: Record<string, string> = {
    github: 'GitHub',
    google: 'Google',
    wechat: '微信',
    alipay: '支付宝'
  }
  return names[provider] || provider
}
</script>

<style scoped>
.oauth-callback-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.callback-container {
  background: white;
  border-radius: 12px;
  padding: 48px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 400px;
  width: 100%;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-icon {
  color: #409eff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

h2 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

p {
  margin: 0;
  color: #606266;
  font-size: 16px;
}
</style>
