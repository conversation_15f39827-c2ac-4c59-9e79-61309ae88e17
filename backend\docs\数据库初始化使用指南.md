# 数据库初始化使用指南

## 🎯 概述

本项目提供了完整的数据库初始化和种子数据管理系统，支持开发、测试、生产环境的数据库快速初始化。

## 🚀 快速开始

### 1. 环境准备

确保已安装并启动MySQL数据库，然后配置数据库连接信息：

```bash
# 复制环境变量配置文件
cp .env.example .env

# 编辑 .env 文件，配置数据库连接信息
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=123456
DB_DATABASE=openapidb
```

### 2. 基础数据库初始化

```bash
# 简单的数据库初始化（推荐首次使用）
npm run db:simple-init

# 完整的数据库初始化（包含迁移和种子数据）
npm run db:init
```

### 3. 种子数据管理

```bash
# 测试种子数据功能
npm run seed:test

# 强制重新执行种子数据
npm run seed:force-test

# 查看种子数据列表
npm run seed:list

# 查看种子数据状态
npm run seed:status

# 执行种子数据
npm run seed:run

# 回滚种子数据
npm run seed:revert -- --names system-config
```

## 📋 可用命令

### 数据库管理命令

| 命令 | 描述 | 示例 |
|------|------|------|
| `npm run db:simple-init` | 简单数据库初始化 | 创建基础表和配置 |
| `npm run db:init` | 完整数据库初始化 | 迁移+种子数据 |
| `npm run db:migrate` | 执行数据库迁移 | 更新数据库结构 |
| `npm run db:reset` | 重置数据库 | 清空并重新初始化 |

### 种子数据命令

| 命令 | 描述 | 示例 |
|------|------|------|
| `npm run seed:test` | 测试种子数据 | 验证功能是否正常 |
| `npm run seed:force-test` | 强制执行测试 | 忽略已执行状态 |
| `npm run seed:run` | 执行种子数据 | 正常执行流程 |
| `npm run seed:status` | 查看执行状态 | 检查哪些已执行 |
| `npm run seed:list` | 列出所有种子数据 | 查看可用的种子数据 |

### 迁移管理命令

| 命令 | 描述 | 示例 |
|------|------|------|
| `npm run migration:generate` | 生成迁移文件 | 基于实体变更生成 |
| `npm run migration:create` | 创建空迁移文件 | 手动创建迁移 |
| `npm run migration:run` | 执行迁移 | 应用数据库变更 |
| `npm run migration:revert` | 回滚迁移 | 撤销最后一次迁移 |
| `npm run migration:show` | 显示迁移状态 | 查看待执行的迁移 |

## 🔧 高级用法

### 1. 环境特定初始化

```bash
# 开发环境初始化
NODE_ENV=development npm run db:init

# 测试环境初始化
NODE_ENV=test npm run db:init

# 生产环境初始化（需要确认）
NODE_ENV=production npm run db:init
```

### 2. 指定种子数据执行

```bash
# 执行特定的种子数据
npm run seed:run -- --names system-config

# 强制执行特定种子数据
npm run seed:run -- --names system-config --force

# 详细日志模式
npm run seed:run -- --verbose
```

### 3. 数据库重置选项

```bash
# 清空数据（保留表结构）
npm run db:reset

# 删除所有表
npm run db:reset -- --drop-tables

# 重置测试数据库
npm run db:reset -- --env test
```

## 📊 当前可用的种子数据

### 1. 系统配置种子数据 (`simple-system-config`)

**描述**: 创建系统基础配置

**包含配置**:
- `system.name`: 系统名称
- `system.version`: 系统版本  
- `system.initialized`: 系统初始化状态
- `api.rate_limit.default`: 默认API限流
- `queue.ocr.concurrency`: OCR队列并发数
- `queue.sto_ocr.concurrency`: 申通OCR队列并发数

**适用环境**: development, test, production

## 🛠️ 故障排除

### 1. 数据库连接失败

**错误**: `Access denied for user 'root'@'localhost'`

**解决方案**:
```bash
# 检查 .env 文件中的数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=你的密码
DB_DATABASE=openapidb

# 确保MySQL服务已启动
# 确保数据库已创建
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS openapidb;"
```

### 2. 种子数据执行失败

**错误**: 种子数据执行时出现错误

**解决方案**:
```bash
# 查看详细错误信息
npm run seed:run -- --verbose

# 强制重新执行
npm run seed:run -- --force

# 检查数据库连接
npm run db:simple-init
```

### 3. 迁移执行失败

**错误**: 迁移文件执行失败

**解决方案**:
```bash
# 查看迁移状态
npm run migration:show

# 手动回滚有问题的迁移
npm run migration:revert

# 重新执行迁移
npm run migration:run
```

## 📈 最佳实践

### 1. 开发环境

```bash
# 首次设置
npm run db:simple-init

# 日常开发
npm run seed:test  # 验证种子数据
npm run db:migrate # 应用新的迁移
```

### 2. 测试环境

```bash
# CI/CD 中的自动化测试
npm run db:reset -- --env test
npm run db:init -- --env test
```

### 3. 生产环境

```bash
# 生产环境部署（谨慎操作）
# 1. 备份数据库
mysqldump -u root -p openapidb > backup.sql

# 2. 执行迁移
npm run migration:run

# 3. 执行必要的种子数据
npm run seed:run -- --env production --names system-config
```

## 🔍 监控和日志

### 1. 查看执行日志

种子数据的执行记录保存在 `seed_execution_log` 表中：

```sql
SELECT * FROM seed_execution_log ORDER BY executed_at DESC;
```

### 2. 查看系统配置

系统配置保存在 `system_config` 表中：

```sql
SELECT * FROM system_config ORDER BY category, `key`;
```

## 🚨 注意事项

1. **生产环境操作**: 生产环境的数据库操作需要特别谨慎，建议先在测试环境验证
2. **数据备份**: 执行任何数据库变更前，请先备份数据
3. **环境隔离**: 不同环境使用不同的数据库，避免数据混乱
4. **权限控制**: 生产环境的数据库操作应该有适当的权限控制和审批流程

## 📞 技术支持

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查控制台输出的详细错误信息
3. 查看数据库日志
4. 联系开发团队获取支持

---

**版本**: 1.0.0  
**更新时间**: 2025-08-03  
**维护者**: 开发团队
