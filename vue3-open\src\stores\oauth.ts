import { defineStore } from 'pinia'
import { ref } from 'vue'
import { request } from '@/utils/request'
import { ElMessage } from 'element-plus'

export interface OAuthAccount {
  id: number
  providerType: 'wechat' | 'github' | 'google' | 'alipay'
  providerId: string
  providerName: string
  avatar?: string
  nickname?: string
  email?: string
  bindTime: string
}

export interface WechatLoginDto {
  code: string
}

export interface GithubLoginDto {
  code: string
}

export interface GoogleLoginDto {
  code: string
}

export interface AlipayLoginDto {
  code: string
}

export interface OAuthBindDto {
  providerType: 'wechat' | 'github' | 'google' | 'alipay'
  code: string
}

export interface OAuthUnbindDto {
  providerType: 'wechat' | 'github' | 'google' | 'alipay'
}

export const useOAuthStore = defineStore('oauth', () => {
  // 状态
  const oauthAccounts = ref<OAuthAccount[]>([])
  const loading = ref(false)

  // 微信登录
  const wechatLogin = async (dto: WechatLoginDto) => {
    try {
      loading.value = true
      const response = await request.post('/auth/wechat/login', dto)
      return response
    } catch (error) {
      console.error('微信登录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // GitHub登录
  const githubLogin = async (dto: GithubLoginDto) => {
    try {
      loading.value = true
      const response = await request.post('/auth/github/login', dto)
      return response
    } catch (error) {
      console.error('GitHub登录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // Google登录
  const googleLogin = async (dto: GoogleLoginDto) => {
    try {
      loading.value = true
      const response = await request.post('/auth/google/login', dto)
      return response
    } catch (error) {
      console.error('Google登录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 支付宝登录
  const alipayLogin = async (dto: AlipayLoginDto) => {
    try {
      loading.value = true
      const response = await request.post('/auth/alipay/login', dto)
      return response
    } catch (error) {
      console.error('支付宝登录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 绑定第三方账号
  const bindOAuthAccount = async (dto: OAuthBindDto) => {
    try {
      loading.value = true
      const response = await request.post('/auth/oauth/bind', dto)
      ElMessage.success('绑定成功')
      await getOAuthAccounts() // 刷新绑定列表
      return response
    } catch (error) {
      console.error('绑定第三方账号失败:', error)
      ElMessage.error('绑定失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 解绑第三方账号
  const unbindOAuthAccount = async (dto: OAuthUnbindDto) => {
    try {
      loading.value = true
      const response = await request.post('/auth/oauth/unbind', dto)
      ElMessage.success('解绑成功')
      await getOAuthAccounts() // 刷新绑定列表
      return response
    } catch (error) {
      console.error('解绑第三方账号失败:', error)
      ElMessage.error('解绑失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取已绑定的第三方账号列表
  const getOAuthAccounts = async () => {
    try {
      loading.value = true
      const response: any = await request.get('/auth/oauth/accounts')
      oauthAccounts.value = response.data || response || []
      return response
    } catch (error) {
      console.error('获取第三方账号列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取第三方登录授权URL
  const getOAuthUrl = (provider: string, redirectUri?: string) => {
    const baseUrl = import.meta.env.VITE_APP_BASE_API
    const currentDomain = window.location.origin
    const redirect = redirectUri || `${currentDomain}/auth/oauth/callback`
    
    switch (provider) {
      case 'github':
        const githubClientId = import.meta.env.VITE_GITHUB_CLIENT_ID || 'your_github_client_id'
        return `https://github.com/login/oauth/authorize?client_id=${githubClientId}&redirect_uri=${encodeURIComponent(redirect)}&scope=user:email`
      
      case 'google':
        const googleClientId = import.meta.env.VITE_GOOGLE_CLIENT_ID || 'your_google_client_id'
        return `https://accounts.google.com/oauth/authorize?client_id=${googleClientId}&redirect_uri=${encodeURIComponent(redirect)}&response_type=code&scope=openid%20email%20profile`
      
      case 'wechat':
        const wechatAppId = import.meta.env.VITE_WECHAT_APP_ID || 'your_wechat_app_id'
        return `https://open.weixin.qq.com/connect/qrconnect?appid=${wechatAppId}&redirect_uri=${encodeURIComponent(redirect)}&response_type=code&scope=snsapi_login`
      
      default:
        throw new Error(`不支持的第三方平台: ${provider}`)
    }
  }

  // 处理第三方登录回调
  const handleOAuthCallback = async (provider: string, code: string) => {
    try {
      let response
      switch (provider) {
        case 'github':
          response = await githubLogin({ code })
          break
        case 'google':
          response = await googleLogin({ code })
          break
        case 'wechat':
          response = await wechatLogin({ code })
          break
        case 'alipay':
          response = await alipayLogin({ code })
          break
        default:
          throw new Error(`不支持的第三方平台: ${provider}`)
      }
      return response
    } catch (error) {
      console.error(`${provider}登录回调处理失败:`, error)
      throw error
    }
  }

  return {
    // 状态
    oauthAccounts,
    loading,

    // 方法
    wechatLogin,
    githubLogin,
    googleLogin,
    alipayLogin,
    bindOAuthAccount,
    unbindOAuthAccount,
    getOAuthAccounts,
    getOAuthUrl,
    handleOAuthCallback,
  }
})
