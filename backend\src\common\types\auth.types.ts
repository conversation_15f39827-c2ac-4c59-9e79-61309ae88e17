/**
 * 认证相关的类型定义
 * 用于守卫和拦截器中替代实体引用
 */

/**
 * 用户基本信息接口
 * 用于替代UserEntity在守卫和拦截器中的使用
 */
export interface AuthUser {
  /** 用户ID */
  id: number;
  /** 用户名 */
  username?: string;
  nickname?: string;
  /** 邮箱 */
  email?: string;
  /** 手机号 */
  phone?: string;
  /** 用户类型 */
  userType?: string;
  /** 用户状态 */
  status?: string;
  /** 用户等级 */
  tier?: string;
  /** 用户角色 */
  role?: string;
  /** 认证状态 */
  verificationStatus?: string;
  /** 余额 */
  balance?: number;
  /** 最后登录IP */
  lastLoginIp?: string;
  /** 最后登录时间 */
  lastLoginAt?: Date;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

/**
 * API密钥基本信息接口
 * 用于替代ApiKeyEntity在守卫和拦截器中的使用
 */
export interface AuthApiKey {
  /** API密钥ID */
  id: number;
  /** 用户ID */
  userId: number;
  /** 服务ID */
  serviceId: number;
  /** 密钥名称 */
  name: string;
  /** 密钥状态 */
  keyStatus: string;
  /** 密钥类型 */
  keyType: string;
  /** 密钥描述 */
  description?: string;
  /** 权限范围 */
  permissions?: string[];
  /** 过期时间 */
  expiresAt?: Date;
  /** 最后使用时间 */
  lastUsedAt?: Date;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
  /** 密钥是否已被查看过 */
  isSecretViewed?: boolean;
}

/**
 * 认证结果接口
 */
export interface AuthResult {
  /** 是否验证成功 */
  isValid: boolean;
  /** 认证类型 */
  authType?: 'jwt' | 'api-key' | 'api-key-signature';
  /** 用户信息 */
  user?: AuthUser;
  /** API密钥信息 */
  apiKey?: AuthApiKey;
  /** 载荷数据 */
  payload?: Record<string, any>;
  /** 错误信息 */
  error?: string;
}

/**
 * 请求日志数据接口
 */
export interface RequestLogData {
  /** 请求ID */
  requestId: string;
  /** 请求方法 */
  method: string;
  /** 请求URL */
  url: string;
  /** 用户代理 */
  userAgent?: string;
  /** 客户端IP */
  ip: string;
  /** 认证类型 */
  authType?: string;
  /** 用户ID */
  userId?: number;
  /** API密钥ID */
  apiKeyId?: number;
  /** 时间戳 */
  timestamp: string;
  /** 请求持续时间 */
  duration?: number;
  /** 请求状态 */
  status?: string;
  /** 响应大小 */
  responseSize?: number;
}

/**
 * API调用计费数据接口
 */
export interface BillingData {
  /** 用户ID */
  userId: number;
  /** API密钥ID */
  apiKeyId: number;
  /** 服务ID */
  serviceId: number;
  /** 请求方法 */
  method: string;
  /** 请求URL */
  url: string;
  /** 是否成功 */
  isSuccess: boolean;
  /** 时间戳 */
  timestamp: Date;
}