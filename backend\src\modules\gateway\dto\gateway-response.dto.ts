import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TaskStatus } from '../config/gateway.constants';

/**
 * 网关响应基础DTO
 */
export class GatewayResponseDto {
  @ApiProperty({
    description: '请求是否成功',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'HTTP状态码',
    example: 200,
  })
  code: number;

  @ApiPropertyOptional({
    description: '响应消息',
    example: '操作成功',
  })
  message?: string;

  @ApiPropertyOptional({
    description: '请求ID',
    example: 'req_1234567890',
  })
  requestId?: string;

  @ApiPropertyOptional({
    description: '响应时间（毫秒）',
    example: 1203,
  })
  responseTime?: number;
}

/**
 * 同步响应DTO
 */
export class SyncResponseDto extends GatewayResponseDto {
  @ApiPropertyOptional({
    description: '响应数据',
    example: {
      sender: {
        name: '张三',
        phone: '13800138000',
        address: '北京市海淀区中关村大街1号'
      }
    },
  })
  data?: any;
}

/**
 * 异步响应DTO
 */
export class AsyncResponseDto extends GatewayResponseDto {
  @ApiProperty({
    description: '任务ID',
    example: 'job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a',
  })
  taskId: string;

  @ApiProperty({
    description: '任务状态',
    enum: TaskStatus,
    example: TaskStatus.QUEUED,
  })
  status: TaskStatus;

  @ApiPropertyOptional({
    description: '任务进度（0-1）',
    example: 0.45,
    minimum: 0,
    maximum: 1,
  })
  progress?: number;

  @ApiPropertyOptional({
    description: '预估完成时间（毫秒）',
    example: 25000,
  })
  estimatedCompletion?: number;

  @ApiPropertyOptional({
    description: '任务状态查询URL',
    example: '/v1/op/tasks/job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a',
  })
  statusUrl?: string;

  @ApiPropertyOptional({
    description: 'SSE事件流URL',
    example: '/v1/op/tasks/job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a/events',
  })
  eventsUrl?: string;
}

/**
 * 代理异步响应DTO（成功）
 */
export class ProxyAsyncSuccessResponseDto extends GatewayResponseDto {
  @ApiProperty({
    description: '任务ID',
    example: 'job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a',
  })
  taskId: string;

  @ApiProperty({
    description: '任务状态',
    enum: TaskStatus,
    example: TaskStatus.COMPLETED,
  })
  status: TaskStatus;

  @ApiPropertyOptional({
    description: '处理结果',
    example: {
      sender: {
        name: '张三',
        phone: '13800138000',
        address: '北京市海淀区中关村大街1号'
      }
    },
  })
  data?: any;

  @ApiPropertyOptional({
    description: '处理耗时（毫秒）',
    example: 18543,
  })
  processingTime?: number;
}

/**
 * 代理异步响应DTO（超时）
 */
export class ProxyAsyncTimeoutResponseDto extends AsyncResponseDto {
  @ApiPropertyOptional({
    description: '已处理时间（毫秒）',
    example: 60120,
  })
  elapsedTime?: number;
}

/**
 * OCR识别响应DTO
 */
export class OcrResponseDto {
  @ApiPropertyOptional({
    description: '发件人信息',
    example: {
      name: '张三',
      phone: '13800138000',
      address: '北京市海淀区中关村大街1号'
    },
  })
  sender?: {
    name?: string;
    phone?: string;
    address?: string;
  };

  @ApiPropertyOptional({
    description: '收件人信息',
    example: {
      name: '李四',
      phone: '13900139000',
      address: '上海市浦东新区张江高科技园区'
    },
  })
  receiver?: {
    name?: string;
    phone?: string;
    address?: string;
  };

  @ApiPropertyOptional({
    description: '快递信息',
    example: {
      company: '顺丰速运',
      trackingNumber: 'SF1234567890'
    },
  })
  expressInfo?: {
    company?: string;
    trackingNumber?: string;
  };

  @ApiPropertyOptional({
    description: '识别置信度',
    example: 0.95,
    minimum: 0,
    maximum: 1,
  })
  confidence?: number;
}

/**
 * 申通面单OCR识别响应DTO
 */
export class StoOcrResponseDto {
  @ApiPropertyOptional({
    description: '发件人信息',
    example: {
      name: '张三',
      phone: '13800138000',
      address: '北京市海淀区中关村大街1号'
    },
  })
  sender?: {
    name?: string;
    phone?: string;
    address?: string;
  };

  @ApiPropertyOptional({
    description: '收件人信息',
    example: {
      name: '李四',
      phone: '13900139000',
      address: '上海市浦东新区张江高科技园区'
    },
  })
  receiver?: {
    name?: string;
    phone?: string;
    address?: string;
  };

  @ApiPropertyOptional({
    description: '快递信息',
    example: {
      company: '申通快递',
      trackingNumber: 'STO1234567890',
      serviceType: '标准快递',
      weight: '1.2kg'
    },
  })
  expressInfo?: {
    company?: string;
    trackingNumber?: string;
    serviceType?: string;
    weight?: string;
  };

  @ApiPropertyOptional({
    description: '申通特有信息',
    example: {
      sortingCode: '021-PD-001',
      routeCode: 'SH-BJ-001',
      packageType: '标准包装'
    },
  })
  stoSpecific?: {
    sortingCode?: string;
    routeCode?: string;
    packageType?: string;
  };

  @ApiPropertyOptional({
    description: '识别置信度',
    example: 0.95,
    minimum: 0,
    maximum: 1,
  })
  confidence?: number;
}

/**
 * 地址提取响应DTO
 */
export class AddressExtractionResponseDto {
  @ApiPropertyOptional({
    description: '姓名',
    example: '李四',
  })
  name?: string;

  @ApiPropertyOptional({
    description: '电话号码',
    example: '13900139000',
  })
  phone?: string;

  @ApiPropertyOptional({
    description: '地址信息',
    example: {
      province: '上海市',
      city: '上海市',
      district: '浦东新区',
      street: '',
      detail: '张江高科技园区1号楼3层',
      full: '上海市浦东新区张江高科技园区1号楼3层'
    },
  })
  address?: {
    province?: string;
    city?: string;
    district?: string;
    street?: string;
    detail?: string;
    full?: string;
  };

  @ApiPropertyOptional({
    description: '邮政编码',
    example: '200120',
  })
  postalCode?: string;
}

/**
 * 地理坐标响应DTO
 */
export class GeoCoordinateResponseDto {
  @ApiPropertyOptional({
    description: '纬度',
    example: 31.230416,
  })
  lat?: number;

  @ApiPropertyOptional({
    description: '经度',
    example: 121.473701,
  })
  lng?: number;

  @ApiPropertyOptional({
    description: '地址',
    example: '上海市黄浦区南京东路1号',
  })
  address?: string;

  @ApiPropertyOptional({
    description: '省份',
    example: '上海市',
  })
  province?: string;

  @ApiPropertyOptional({
    description: '城市',
    example: '上海市',
  })
  city?: string;

  @ApiPropertyOptional({
    description: '区县',
    example: '黄浦区',
  })
  district?: string;

  @ApiPropertyOptional({
    description: '街道',
    example: '南京东路',
  })
  street?: string;

  @ApiPropertyOptional({
    description: '门牌号',
    example: '1号',
  })
  streetNumber?: string;

  @ApiPropertyOptional({
    description: 'POI信息',
    example: '人民广场',
  })
  poi?: string;

  @ApiPropertyOptional({
    description: '置信度',
    example: 0.95,
    minimum: 0,
    maximum: 1,
  })
  confidence?: number;

  @ApiPropertyOptional({
    description: '精度级别',
    example: '门牌号',
  })
  level?: string;
}

/**
 * 任务状态响应DTO
 */
export class TaskStatusResponseDto extends GatewayResponseDto {
  @ApiProperty({
    description: '任务ID',
    example: 'job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a',
  })
  taskId: string;

  @ApiProperty({
    description: '任务状态',
    enum: TaskStatus,
    example: TaskStatus.COMPLETED,
  })
  status: TaskStatus;

  @ApiPropertyOptional({
    description: '任务进度',
    example: 1.0,
    minimum: 0,
    maximum: 1,
  })
  progress?: number;

  @ApiPropertyOptional({
    description: '任务结果',
    example: { data: 'result' },
  })
  result?: any;

  @ApiPropertyOptional({
    description: '错误信息',
    example: '处理失败：图片格式不支持',
  })
  error?: string;

  @ApiPropertyOptional({
    description: '创建时间',
    example: '2023-10-01T10:00:00Z',
  })
  createdAt?: string;

  @ApiPropertyOptional({
    description: '更新时间',
    example: '2023-10-01T10:05:00Z',
  })
  updatedAt?: string;

  @ApiPropertyOptional({
    description: '处理耗时（毫秒）',
    example: 18543,
  })
  processingTime?: number;
}

/**
 * 错误响应DTO
 */
export class ErrorResponseDto {
  @ApiProperty({
    description: 'HTTP状态码',
    example: 400,
  })
  code: number;

  @ApiProperty({
    description: '错误码',
    example: 'GATEWAY_INVALID_PROCESSING_MODE',
  })
  errorCode: string;

  @ApiProperty({
    description: '错误消息',
    example: '无效的处理模式',
  })
  message: string;

  @ApiPropertyOptional({
    description: '错误详情',
    example: ['mode字段必须是sync、async或proxy-async之一'],
  })
  details?: string[];

  @ApiProperty({
    description: '时间戳',
    example: '2023-10-01T10:00:00Z',
  })
  timestamp: string;

  @ApiPropertyOptional({
    description: '请求ID',
    example: 'req_1234567890',
  })
  requestId?: string;
}
