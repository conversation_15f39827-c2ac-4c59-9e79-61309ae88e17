<template>
  <div class="file-uploader">
    <!-- 上传区域 -->
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      :action="uploadAction"
      :headers="uploadHeaders"
      :data="uploadData"
      :multiple="multiple"
      :accept="accept"
      :limit="maxCount"
      :auto-upload="autoUpload"
      :disabled="disabled"
      :drag="drag"
      :show-file-list="false"
      :before-upload="beforeUpload"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      :on-progress="handleUploadProgress"
      :on-change="handleChange"
      :on-remove="handleRemove"
      :on-exceed="handleExceed"
      class="upload-area"
    >
      <div class="upload-content">
        <el-icon class="upload-icon"><Upload /></el-icon>
        <div class="upload-text">
          <p class="upload-title">{{ drag ? '拖拽文件到此处或' : '' }}点击上传文件</p>
          <p class="upload-hint">
            支持 {{ acceptText }}，单个文件不超过 {{ maxSizeText }}
          </p>
          <p v-if="tip" class="upload-tip">{{ tip }}</p>
        </div>
      </div>
    </el-upload>

    <!-- 文件列表 -->
    <div v-if="showFileList && hasFiles" class="file-list">
      <div class="file-list-header">
        <div class="file-count">
          已选择 {{ fileList.length }} 个文件，共 {{ formattedTotalSize }}
        </div>
        <div class="file-actions">
          <el-button
            v-if="!autoUpload && canUpload"
            type="primary"
            size="small"
            :loading="uploading"
            @click="submitUpload"
          >
            开始上传
          </el-button>
          <el-button
            size="small"
            @click="clearFiles"
          >
            清空文件
          </el-button>
        </div>
      </div>

      <div class="file-items">
        <div
          v-for="(file, index) in fileList"
          :key="file.uid"
          class="file-item"
        >
          <!-- 文件图标和预览 -->
          <div class="file-icon">
            <el-image
              v-if="getFilePreview(file.name)"
              :src="getFilePreview(file.name)"
              class="file-preview"
              fit="cover"
            />
            <el-icon v-else class="file-type-icon">
              <component :is="getFileIconComponent(file)" />
            </el-icon>
          </div>

          <!-- 文件信息 -->
          <div class="file-info">
            <div class="file-name" :title="file.name">{{ file.name }}</div>
            <div class="file-meta">
              <span class="file-size">{{ formatFileSize(file.size || 0) }}</span>
              <span class="file-status" :class="`status-${file.status}`">
                {{ getStatusText(file.status) }}
              </span>
            </div>
            
            <!-- 上传进度 -->
            <el-progress
              v-if="file.status === 'uploading'"
              :percentage="file.percentage || 0"
              :stroke-width="4"
              class="file-progress"
            />
          </div>

          <!-- 操作按钮 -->
          <div class="file-actions">
            <el-button
              v-if="file.status === 'success'"
              type="primary"
              size="small"
              text
              @click="handlePreview(file)"
            >
              预览
            </el-button>
            <el-button
              type="danger"
              size="small"
              text
              @click="removeFile(index)"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传进度 -->
    <div v-if="uploading && uploadProgress > 0" class="upload-progress">
      <el-progress
        :percentage="uploadProgress"
        :stroke-width="6"
        status="success"
      >
        <template #default="{ percentage }">
          <span class="progress-text">上传中 {{ percentage }}%</span>
        </template>
      </el-progress>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Document, Picture, VideoPlay, Microphone, FolderOpened } from '@element-plus/icons-vue'
import type { UploadFile, UploadRawFile, UploadInstance } from 'element-plus'
import {
  formatFileSize,
  getFileIcon,
  getFileType,
  beforeUploadValidate,
  createImagePreview,
  compressImage,
  UploadManager,
  getUploadStatusText
} from '../../utils/upload'

// Props
interface Props {
  modelValue?: File[]
  accept?: string
  multiple?: boolean
  maxSize?: number // MB
  maxCount?: number
  disabled?: boolean
  autoUpload?: boolean
  action?: string
  headers?: Record<string, string>
  data?: Record<string, any>
  listType?: 'text' | 'picture' | 'picture-card'
  showFileList?: boolean
  drag?: boolean
  tip?: string
  allowedTypes?: string[] // 允许的文件类型
  enableCompress?: boolean // 是否启用图片压缩
  compressOptions?: {
    maxWidth?: number
    maxHeight?: number
    quality?: number
  }
}

const props = withDefaults(defineProps<Props>(), {
  accept: '*',
  multiple: true,
  maxSize: 10,
  maxCount: 10,
  disabled: false,
  autoUpload: false,
  listType: 'text',
  showFileList: true,
  drag: true,
  tip: '',
  allowedTypes: () => ['all'],
  enableCompress: false,
  compressOptions: () => ({
    maxWidth: 1920,
    maxHeight: 1080,
    quality: 0.8
  })
})

// Emits
interface Emits {
  'update:modelValue': [files: File[]]
  'change': [files: File[]]
  'upload-success': [response: any, file: File]
  'upload-error': [error: Error, file: File]
  'upload-progress': [progress: number, file: File]
  'remove': [file: File]
  'preview': [file: File]
}

const emit = defineEmits<Emits>()

// State
const uploadRef = ref<UploadInstance>()
const fileList = ref<UploadFile[]>([])
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadManager = new UploadManager(3)
const previewImages = ref<Map<string, string>>(new Map())

// Computed
const uploadAction = computed(() => props.action || '/api/upload')
const uploadHeaders = computed(() => props.headers || {})
const uploadData = computed(() => props.data || {})

const totalSize = computed(() => {
  return fileList.value.reduce((total, file) => {
    return total + (file.size || 0)
  }, 0)
})

const formattedTotalSize = computed(() => {
  return formatFileSize(totalSize.value)
})

const hasFiles = computed(() => {
  return fileList.value.length > 0
})

const canUpload = computed(() => {
  return fileList.value.some(file => file.status === 'ready')
})

const acceptText = computed(() => {
  if (props.accept === '*') return '所有格式'
  return props.accept.split(',').map(type => type.trim()).join(', ')
})

const maxSizeText = computed(() => {
  return props.maxSize >= 1024 
    ? `${(props.maxSize / 1024).toFixed(1)}GB`
    : `${props.maxSize}MB`
})

// Methods
function getFileIconComponent(file: UploadFile) {
  const iconName = getFileIcon(file.name)
  const iconMap: Record<string, any> = {
    Picture,
    VideoPlay,
    Document,
    Microphone,
    FolderOpened
  }
  return iconMap[iconName] || Document
}

function getStatusText(status?: string): string {
  return getUploadStatusText(status || 'ready')
}

async function beforeUpload(rawFile: UploadRawFile) {
  // 使用工具类进行验证
  const isValid = beforeUploadValidate(rawFile, {
    allowedTypes: props.allowedTypes,
    maxSize: props.maxSize
  })

  if (!isValid) {
    return false
  }

  // 文件数量检查
  if (fileList.value.length >= props.maxCount) {
    ElMessage.error(`最多只能上传 ${props.maxCount} 个文件`)
    return false
  }

  // 图片压缩处理
  if (props.enableCompress && getFileType(rawFile.name) === 'image') {
    try {
      const compressedFile = await compressImage(rawFile, props.compressOptions)
      // 替换原文件内容
      const buffer = await compressedFile.arrayBuffer()
      Object.defineProperty(rawFile, 'size', { value: compressedFile.size, writable: true })
      Object.defineProperty(rawFile, 'type', { value: compressedFile.type, writable: true })
    } catch (error) {
      console.warn('图片压缩失败，使用原文件:', error)
    }
  }

  // 生成图片预览
  if (getFileType(rawFile.name) === 'image') {
    try {
      const preview = await createImagePreview(rawFile)
      previewImages.value.set(rawFile.name, preview)
    } catch (error) {
      console.warn('生成图片预览失败:', error)
    }
  }

  return true
}

function handleChange(uploadFile: UploadFile, uploadFiles: UploadFile[]) {
  fileList.value = uploadFiles
  const files = uploadFiles.map(file => file.raw!).filter(Boolean)
  emit('update:modelValue', files)
  emit('change', files)
}

function handleRemove(uploadFile: UploadFile) {
  // 清理预览图片
  if (uploadFile.name) {
    previewImages.value.delete(uploadFile.name)
  }
  
  const files = fileList.value.map(file => file.raw!).filter(Boolean)
  emit('update:modelValue', files)
  emit('change', files)
  if (uploadFile.raw) {
    emit('remove', uploadFile.raw)
  }
}

function handlePreview(uploadFile: UploadFile) {
  if (uploadFile.raw) {
    emit('preview', uploadFile.raw)
  }
}

function handleUploadSuccess(response: any, uploadFile: UploadFile) {
  if (uploadFile.raw) {
    emit('upload-success', response, uploadFile.raw)
  }
}

function handleUploadError(error: Error, uploadFile: UploadFile) {
  if (uploadFile.raw) {
    emit('upload-error', error, uploadFile.raw)
  }
}

function handleUploadProgress(event: any, uploadFile: UploadFile) {
  const progress = Math.round((event.loaded / event.total) * 100)
  if (uploadFile.raw) {
    emit('upload-progress', progress, uploadFile.raw)
  }
}

function handleExceed() {
  ElMessage.warning(`最多只能上传 ${props.maxCount} 个文件`)
}

function removeFile(index: number) {
  const file = fileList.value[index]
  if (file) {
    // 清理预览图片
    if (file.name) {
      previewImages.value.delete(file.name)
    }
    
    fileList.value.splice(index, 1)
    const files = fileList.value.map(file => file.raw!).filter(Boolean)
    emit('update:modelValue', files)
    emit('change', files)
    
    if (file.raw) {
      emit('remove', file.raw)
    }
  }
}

function clearFiles() {
  uploadRef.value?.clearFiles()
  fileList.value = []
  previewImages.value.clear()
  emit('update:modelValue', [])
  emit('change', [])
}

function submitUpload() {
  if (!props.action) {
    ElMessage.warning('请设置上传地址')
    return
  }
  uploadRef.value?.submit()
}

// 获取文件预览图片
function getFilePreview(fileName: string): string | undefined {
  return previewImages.value.get(fileName)
}

// 批量上传处理
async function batchUpload(customUploadFn?: (file: File) => Promise<any>) {
  if (!customUploadFn && !props.action) {
    ElMessage.warning('请提供上传函数或设置上传地址')
    return
  }

  const files = fileList.value.map(file => file.raw!).filter(Boolean)
  if (files.length === 0) {
    ElMessage.warning('请先选择文件')
    return
  }

  uploading.value = true
  uploadProgress.value = 0

  try {
    let completed = 0
    const total = files.length

    for (const file of files) {
      uploadManager.addTask(file)
    }

    await uploadManager.startUpload(async (file, onProgress) => {
      if (customUploadFn) {
        return await customUploadFn(file)
      } else {
        // 使用默认上传逻辑
        return new Promise((resolve, reject) => {
          const formData = new FormData()
          formData.append('file', file)
          
          // 添加额外数据
          if (props.data) {
            Object.entries(props.data).forEach(([key, value]) => {
              formData.append(key, value)
            })
          }

          const xhr = new XMLHttpRequest()
          
          xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable) {
              const progress = Math.round((e.loaded / e.total) * 100)
              onProgress(progress)
              
              // 更新总体进度
              const fileProgress = progress / 100
              const totalProgress = ((completed + fileProgress) / total) * 100
              uploadProgress.value = Math.round(totalProgress)
            }
          })

          xhr.addEventListener('load', () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              completed++
              uploadProgress.value = Math.round((completed / total) * 100)
              
              try {
                const response = JSON.parse(xhr.responseText)
                resolve(response)
              } catch {
                resolve(xhr.responseText)
              }
            } else {
              reject(new Error(`上传失败: ${xhr.statusText}`))
            }
          })

          xhr.addEventListener('error', () => {
            reject(new Error('网络错误'))
          })

          xhr.open('POST', props.action!)
          
          // 设置请求头
          if (props.headers) {
            Object.entries(props.headers).forEach(([key, value]) => {
              xhr.setRequestHeader(key, value)
            })
          }

          xhr.send(formData)
        })
      }
    })

    ElMessage.success('所有文件上传完成')
  } catch (error) {
    ElMessage.error('批量上传失败')
    console.error('批量上传错误:', error)
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}

// Watch
watch(
  () => props.modelValue,
  (newFiles) => {
    if (newFiles && newFiles.length > 0) {
      fileList.value = newFiles.map((file, index) => ({
        name: file.name,
        size: file.size,
        raw: file,
        uid: Date.now() + index,
        status: 'ready'
      } as any))
    } else {
      fileList.value = []
      previewImages.value.clear()
    }
  },
  { immediate: true }
)

// 暴露方法给父组件
defineExpose({
  clearFiles,
  submitUpload,
  batchUpload,
  getFilePreview
})
</script>

<style scoped>
.file-uploader {
  width: 100%;
}

.upload-area {
  width: 100%;
}

.upload-area :deep(.el-upload) {
  width: 100%;
}

.upload-area :deep(.el-upload-dragger) {
  width: 100%;
  height: 180px;
  border: 2px dashed var(--el-border-color);
  border-radius: 8px;
  background-color: var(--el-fill-color-lighter);
  transition: all 0.3s;
}

.upload-area :deep(.el-upload-dragger:hover) {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
}

.upload-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-bottom: 16px;
}

.upload-text {
  text-align: center;
}

.upload-title {
  font-size: 16px;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.upload-hint {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin: 0 0 4px 0;
}

.upload-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin: 0;
}

.file-list {
  margin-top: 16px;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  overflow: hidden;
}

.file-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color);
}

.file-count {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.file-actions {
  display: flex;
  gap: 8px;
}

.file-items {
  max-height: 300px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  transition: background-color 0.3s;
}

.file-item:hover {
  background-color: var(--el-fill-color-lighter);
}

.file-item:last-child {
  border-bottom: none;
}

.file-icon {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background-color: var(--el-fill-color-light);
}

.file-preview {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.file-type-icon {
  font-size: 20px;
  color: var(--el-color-primary);
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
}

.file-size {
  color: var(--el-text-color-regular);
}

.file-status {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
}

.status-ready {
  background-color: var(--el-color-info-light-8);
  color: var(--el-color-info);
}

.status-uploading {
  background-color: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
}

.status-success {
  background-color: var(--el-color-success-light-8);
  color: var(--el-color-success);
}

.status-error {
  background-color: var(--el-color-danger-light-8);
  color: var(--el-color-danger);
}

.file-progress {
  margin-top: 8px;
}

.file-actions {
  display: flex;
  gap: 4px;
  margin-left: 12px;
}

.upload-progress {
  margin-top: 16px;
  padding: 16px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 8px;
}

.progress-text {
  font-size: 14px;
  color: var(--el-text-color-primary);
}
</style>