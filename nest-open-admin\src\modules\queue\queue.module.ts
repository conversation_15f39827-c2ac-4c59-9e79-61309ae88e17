/**
 * 队列模块
 * 提供统一的队列管理和任务处理功能
 */

import { Module, DynamicModule, Provider, Global, forwardRef } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { QueueController } from './queue.controller';
import { EnhancedQueueManagerService } from './services/enhanced-queue-manager.service';
import { QUEUE_NAMES } from './queue.constants';
import { OcrProcessor } from './processors/ocr.processor';
import { AddressExtractionProcessor } from './processors/address-extraction.processor';
import { ReverseGeocodingProcessor } from './processors/reverse-geocoding.processor';
import { AlertProcessor } from './processors/alert.processor';
import { ApiProcessor } from './processors/api.processor';
import { AddressModule } from '../address/address.module';
import { CallRecordModule } from '../call-record/call-record.module';
import { SharedModule } from '../../shared/shared.module';
import { createBullConfig } from './config/bull.config';

/**
 * 队列模块
 * 提供统一的队列管理和任务处理功能
 */
@Global()
@Module({})
export class QueueModule {
  /**
   * 根模块配置
   * 注册所有队列和基础服务
   */
  static forRoot(): DynamicModule {
    return {
      module: QueueModule,
      imports: [
        BullModule.forRootAsync({
          imports: [ConfigModule],
          useFactory: createBullConfig,
          inject: [ConfigService],
        }),
        BullModule.registerQueue(
          { 
            name: QUEUE_NAMES.OCR,
            defaultJobOptions: {
              removeOnComplete: false, // 不自动删除已完成的任务
              removeOnFail: false,     // 不自动删除失败的任务
              attempts: 3,
              backoff: {
                type: 'exponential',
                delay: 2000,
              },
              timeout: 180000, // 增加到3分钟
            }
          },
          { name: QUEUE_NAMES.EXTRACT_ADDRESS,
            defaultJobOptions: {
              attempts: 3,
              backoff: {
                type: 'exponential',
                delay: 1500,
              },
              timeout: 60000, // 增加到1分钟
            }
          },
          { name: QUEUE_NAMES.REV_GEO,
            defaultJobOptions: {
              attempts: 3,
              backoff: {
                type: 'exponential',
                delay: 1500,
              },
              timeout: 60000, // 增加到1分钟
            }
          },
          { name: QUEUE_NAMES.ALERT },
          { name: QUEUE_NAMES.API_CALL },
        ),
        SharedModule,
        forwardRef(() => CallRecordModule),
      ],
      providers: [
        EnhancedQueueManagerService,
        OcrProcessor,
        AddressExtractionProcessor,
        ReverseGeocodingProcessor,
      ],
      exports: [
        BullModule,
        EnhancedQueueManagerService,
        OcrProcessor,
        AddressExtractionProcessor,
        ReverseGeocodingProcessor,
      ],
    };
  }

  /**
   * 特性模块配置
   * 注册队列处理器和控制器
   */
  static forFeature(): DynamicModule {
    return {
      module: QueueModule,
      imports: [
        forwardRef(() => AddressModule),
        forwardRef(() => CallRecordModule),
      ],
      controllers: [QueueController],
      providers: [
        AddressExtractionProcessor,
        ReverseGeocodingProcessor,
        AlertProcessor,
        ApiProcessor,
      ],
      exports: [
        AddressExtractionProcessor,
        ReverseGeocodingProcessor,
        AlertProcessor,
        ApiProcessor,
      ],
    };
  }

  /**
   * 注册OCR处理器
   */
  static registerOcrProcessor(): DynamicModule {
    return {
      module: QueueModule,
      imports: [
        BullModule.registerQueue({ name: QUEUE_NAMES.OCR }),
      ],
      providers: [
        {
          provide: 'OcrService',
          useValue: {
            processOcrRequestFromBase64: async () => {
              return { success: true, data: {} };
            }
          }
        },
        {
          provide: 'EnhancedQueueManagerService',
          useValue: {
            addJob: async (queueName, taskType, data, options = {}) => {
              return { id: `mock-job-${Date.now()}` };
            },
            getJobStatus: async (queueName, jobId) => {
              return { 
                id: jobId,
                state: 'completed',
                progress: 100,
                result: { success: true },
                data: {},
                timestamp: {
                  created: Date.now(),
                  processed: Date.now(),
                  finished: Date.now(),
                }
              };
            },
            jobExists: async (queueName, jobId) => true,
            removeJob: async (queueName, jobId) => true,
            getQueueNames: () => Object.values(QUEUE_NAMES),
            getQueueStatus: async (queueName) => ({
              name: queueName,
              counts: { waiting: 0, active: 0, completed: 0, failed: 0 },
              isPaused: false,
              workerCount: 1
            }),
            pauseQueue: async (queueName) => true,
            resumeQueue: async (queueName) => true
          }
        },
        OcrProcessor
      ],
      exports: [OcrProcessor],
    };
  }

  /**
   * 注册地址提取处理器
   */
  static registerAddressProcessor(): DynamicModule {
    return {
      module: QueueModule,
      imports: [
        BullModule.registerQueue({ name: QUEUE_NAMES.EXTRACT_ADDRESS }),
      ],
      providers: [
        {
          provide: 'EnhancedQueueManagerService',
          useValue: {
            addJob: async (queueName, taskType, data, options = {}) => {
              return { id: `mock-job-${Date.now()}` };
            },
            getJobStatus: async (queueName, jobId) => {
              return { 
                id: jobId,
                state: 'completed',
                progress: 100,
                result: { success: true },
                data: {},
                timestamp: {
                  created: Date.now(),
                  processed: Date.now(),
                  finished: Date.now(),
                }
              };
            },
            jobExists: async (queueName, jobId) => true,
            removeJob: async (queueName, jobId) => true,
            getQueueNames: () => Object.values(QUEUE_NAMES),
            getQueueStatus: async (queueName) => ({
              name: queueName,
              counts: { waiting: 0, active: 0, completed: 0, failed: 0 },
              isPaused: false,
              workerCount: 1
            }),
            pauseQueue: async (queueName) => true,
            resumeQueue: async (queueName) => true
          }
        },
        AddressExtractionProcessor
      ],
      exports: [AddressExtractionProcessor],
    };
  }

  /**
   * 注册坐标逆解析处理器
   */
  static registerGeoProcessor(): DynamicModule {
    return {
      module: QueueModule,
      imports: [
        BullModule.registerQueue({ name: QUEUE_NAMES.REV_GEO }),
      ],
      providers: [
        {
          provide: 'EnhancedQueueManagerService',
          useValue: {
            addJob: async (queueName, taskType, data, options = {}) => {
              return { id: `mock-job-${Date.now()}` };
            },
            getJobStatus: async (queueName, jobId) => {
              return { 
                id: jobId,
                state: 'completed',
                progress: 100,
                result: { success: true },
                data: {},
                timestamp: {
                  created: Date.now(),
                  processed: Date.now(),
                  finished: Date.now(),
                }
              };
            },
            jobExists: async (queueName, jobId) => true,
            removeJob: async (queueName, jobId) => true,
            getQueueNames: () => Object.values(QUEUE_NAMES),
            getQueueStatus: async (queueName) => ({
              name: queueName,
              counts: { waiting: 0, active: 0, completed: 0, failed: 0 },
              isPaused: false,
              workerCount: 1
            }),
            pauseQueue: async (queueName) => true,
            resumeQueue: async (queueName) => true
          }
        },
        ReverseGeocodingProcessor
      ],
      exports: [ReverseGeocodingProcessor],
    };
  }
}