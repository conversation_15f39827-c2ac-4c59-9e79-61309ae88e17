import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  Req,
  UploadedFile,
  UseInterceptors,
  HttpCode,
  HttpStatus,
  BadRequestException,
  ServiceUnavailableException,
  Res,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiConsumes,
  ApiBody,
  ApiParam,
  ApiQuery,
  ApiResponse,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { CurrentUser } from '../../../common/decorators/current-user.decorator';
import { CurrentApiKey } from '../../../common/decorators/current-api-key.decorator';
import { AuthUser, AuthApiKey } from '../../../common/types/auth.types';
import { ImprovedGatewayProxyService } from '../services/improved-gateway-proxy.service';
import { ProcessingModeDecider } from '../services/processing-mode-decider.service';
import { GatewayService } from '../gateway.service';
import { Logger } from '@nestjs/common';
import * as fs from 'fs';
import { interval, firstValueFrom } from 'rxjs';
import { take, takeWhile } from 'rxjs/operators';
import { SkipResponseFormat } from '../../../common/decorators/public.decorator';
import { UseAuthStrategy, AuthStrategy } from '../../../common/decorators/auth-strategy.decorator';

import { Public } from '@/common/decorators/public.decorator';

/**
 * 统一网关控制器
 * 提供统一的API入口，支持多种服务的代理和路由
 */
@ApiTags('统一网关')
@UseAuthStrategy(AuthStrategy.API_KEY)
@Controller('api')
export class UnifiedGatewayController {
  private readonly logger = new Logger(UnifiedGatewayController.name);

  constructor(
    private readonly gatewayService: GatewayService,
    private readonly gatewayProxyService: ImprovedGatewayProxyService,
    private readonly processingModeDecider: ProcessingModeDecider,
  ) {}

  /**
   * OCR识别服务 - 文件上传方式
   */
  @ApiOperation({ summary: 'OCR识别服务 - 文件上传方式' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: '要识别的图片文件',
        },
        mode: {
          type: 'string',
          enum: ['sync', 'async'],
          description: '处理模式: sync(同步) 或 async(异步)',
        },
        callbackUrl: {
          type: 'string',
          description: '异步处理回调URL (仅异步模式有效)',
        },
      },
      required: ['file'],
    },
  })
  @Post('ocr/upload')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads/temp',
        filename: (req, file, cb) => {
          const randomName = Array(32)
            .fill(null)
            .map(() => Math.round(Math.random() * 16).toString(16))
            .join('');
          return cb(null, `${randomName}${extname(file.originalname)}`);
        },
      }),
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
  )
  async ocrRecognizeFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: any,
    @Query() query: any,
    @Req() req: Request,
    @CurrentUser() user: AuthUser,
    @CurrentApiKey() apiKey: AuthApiKey,
  ) {
    if (!file) {
      throw new BadRequestException('未上传文件');
    }

    this.logger.debug(`收到OCR文件上传请求: ${file.originalname}, 处理模式: ${body.mode || '未指定'}`);
    
    // 检查文件是否存在
    try {
      const fileExists = fs.existsSync(file.path);
      this.logger.debug(`文件检查: 路径=${file.path}, 存在=${fileExists}`);
      
      if (fileExists) {
        const stats = fs.statSync(file.path);
        this.logger.debug(`文件状态: 大小=${stats.size}, 是否文件=${stats.isFile()}`);
      } else {
        throw new BadRequestException('文件不存在或已被删除');
      }
    } catch (error) {
      this.logger.error(`检查文件失败: ${error.message}`);
      throw new BadRequestException(`文件检查失败: ${error.message}`);
    }

    // 如果是异步模式，直接使用网关代理服务处理
    if (body.mode === 'async') {
      try {
        // 调用网关代理服务处理请求，强制使用异步模式
        const response = await this.gatewayProxyService.proxyRequest({
          method: 'POST',
          serviceCode: 'ocr',
          path: 'upload',
          body: {
            ...body,
            file,
          },
          query,
          headers: req.headers as Record<string, string>,
          user,
          apiKey,
          originalRequest: req,
          forceProcessingMode: 'async',
        });

        // 如果是异步处理模式，简化返回结构
        if (response.metadata?.processingMode === 'async') {
          return {
            success: true,
            jobId: response.jobId || '',
            status: 'queued',
            statusUrl: `/v1/op/api/tasks/${response.jobId}`,
            message: '任务已提交到队列，请通过statusUrl查询结果',
            requestId: response.metadata.requestId,
            responseTime: response.responseTime
          };
        }
        
        return response;
      } catch (error) {
        this.logger.error(`处理异步OCR请求失败: ${error.message}`, error.stack);
        throw new ServiceUnavailableException(`处理异步OCR请求失败: ${error.message}`);
      }
    }

    // 添加日志，帮助调试
    this.logger.log(`处理OCR文件上传请求: ${file.originalname}, 大小: ${file.size}字节, 类型: ${file.mimetype}`);

    // 同步模式使用原有逻辑
    const response = await this.gatewayProxyService.proxyRequest({
      method: 'POST',
      serviceCode: 'ocr',
      path: 'upload',
      body: {
        ...body,
        file,
      },
      query,
      headers: req.headers as Record<string, string>,
      user,
      apiKey,
      originalRequest: req,
      forceProcessingMode: 'sync',
    });

    // 记录返回数据
    this.logger.debug(
      `OCR识别完成，原始返回数据长度: ${JSON.stringify(response).length}`,
    );
    
    // 处理返回结果，确保格式正确
    if (response?.data?.rawData?.results) {
      try {
        // 调用本地解析服务处理OCR结果
        const localResponse = await this.gatewayService.localParseOcrResult(
          response.data.rawData.results,
        );

        // 直接返回简化的结构，只包含必要数据
        if (localResponse && localResponse.data) {
          const simplifiedResponse = {
            success: true,
            ...localResponse.data,
            process_time: response.data.process_time || 0,
            image_info: response.data.image_info || {},
            requestId: response.metadata.requestId,
            responseTime: response.responseTime,
          };

          return simplifiedResponse;
        }
      } catch (error) {
        this.logger.error(`处理OCR结果失败: ${error.message}`, error.stack);
      }
    }
    
    return response;
  }

  /**
   * OCR识别服务 - Base64方式
   */
  @ApiOperation({ summary: 'OCR识别服务 - Base64方式' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        image_base64: {
          type: 'string',
          format: 'text',
          description: 'Base64编码的图片数据 (可能很长，Swagger UI可能会有限制)',
          example: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEASABIAAD/...(省略)',
        },
        filename: {
          type: 'string',
          description: '文件名',
          example: 'test.jpg',
        },
        mode: {
          type: 'string',
          enum: ['sync', 'async'],
          description: '处理模式: sync(同步) 或 async(异步)',
          example: 'sync',
        },
        callbackUrl: {
          type: 'string',
          description: '异步处理回调URL (仅异步模式有效)',
          example: 'https://example.com/callback',
        },
      },
      required: ['image_base64'],
    },
  })
  @Post('ocr/recognize')
  async ocrRecognizeBase64(
    @Body() body: any,
    @Query() query: any,
    @Req() req: Request,
    @CurrentUser() user: AuthUser,
    @CurrentApiKey() apiKey: AuthApiKey,
  ) {
    if (!body.image_base64) {
      throw new BadRequestException('缺少图片数据');
    }

    // 修复Base64字符串 - 删除前缀并处理填充
    if (body.image_base64) {
      // 完全删除前缀，支持所有图像类型
      const base64Data = body.image_base64.replace(/^data:image\/[^;]+;base64,/, '');
      this.logger.debug(`处理Base64图像数据: 原长度=${body.image_base64.length}, 移除前缀后长度=${base64Data.length}`);
      
      // 修复填充问题
      const padding = base64Data.length % 4;
      if (padding) {
        const paddedBase64 = base64Data + '='.repeat(4 - padding);
        this.logger.debug(`修复Base64填充: 添加了${4 - padding}个填充字符`);
        
        // 清理非法字符
        const cleanBase64 = paddedBase64.replace(/[^A-Za-z0-9+/=]/g, '');
        
        // 替换原始数据为清理后的纯Base64字符串，不再添加前缀
        body.image_base64 = cleanBase64;
        this.logger.debug(`最终Base64数据长度: ${cleanBase64.length}`);
      } else {
        // 即使不需要填充，也要清理非法字符并移除前缀
        body.image_base64 = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');
      }
    }

    // 调用网关代理服务处理请求
    const response = await this.gatewayProxyService.proxyRequest({
      method: 'POST',
      serviceCode: 'ocr',
      path: 'recognize',
      body,
      query,
      headers: req.headers as Record<string, string>,
      user,
      apiKey,
      originalRequest: req,
      forceProcessingMode: body.mode as 'sync' | 'async',
    });

    // 如果是异步处理模式，简化返回结构
    if (response.metadata?.processingMode === 'async') {
      // 简化异步任务响应
      return {
        success: true,
        jobId: response.jobId || '',
        status: 'queued',
        statusUrl: `/v1/op/api/tasks/${response.jobId}`,
        message: '任务已提交到队列，请通过statusUrl查询结果',
        requestId: response.metadata.requestId,
        responseTime: response.responseTime
      };
    }

    // 处理返回结果，确保格式正确并移除原始OCR数据
    if (response?.data?.rawData?.results) {
      try {
        // 调用本地解析服务处理OCR结果
        const localResponse = await this.gatewayService.localParseOcrResult(
          response.data.rawData.results,
        );

        // 直接返回简化的结构，只包含必要数据
        if (localResponse && localResponse.data) {
          const simplifiedResponse = {
            success: true,
            ...localResponse.data,
            process_time: response.data.process_time || 0,
            image_info: response.data.image_info || {},
            requestId: response.metadata.requestId,
            responseTime: response.responseTime,
          };

          return simplifiedResponse;
        }
      } catch (error) {
        this.logger.error(`处理OCR结果失败: ${error.message}`, error.stack);
      }
    }
    
    return response;
  }

  /**
   * 地址提取服务 - 单个地址
   */
  @ApiOperation({ summary: '地址提取服务 - 单个地址' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        text: {
          type: 'string',
          description: '包含地址的文本',
        },
        mode: {
          type: 'string',
          enum: ['sync', 'async'],
          description: '处理模式: sync(同步) 或 async(异步)',
        },
        callbackUrl: {
          type: 'string',
          description: '异步处理回调URL (仅异步模式有效)',
        },
      },
      required: ['text'],
    },
  })
  @Post('address/extract')
  async extractAddress(
    @Body() body: any,
    @Query() query: any,
    @Req() req: Request,
    @CurrentUser() user: AuthUser,
    @CurrentApiKey() apiKey: AuthApiKey,
  ) {
    if (!body.text) {
      throw new BadRequestException('缺少文本数据');
    }

    // 调用网关代理服务
    const response = await this.gatewayProxyService.proxyRequest({
      method: 'POST',
      serviceCode: 'address',
      path: 'extract',
      body,
      query,
      headers: req.headers as Record<string, string>,
      user,
      apiKey,
      originalRequest: req,
      forceProcessingMode: body.mode as 'sync' | 'async',
    });

    // 如果是同步模式，简化返回结构
    if (response.metadata?.processingMode === 'sync' && response.data) {
      // 提取地址数据
      const addressData = response.data;
      
      // 构建简化的返回结构
      return {
        success: addressData.success !== false,
        addresses: addressData.addresses || [],
        processingTime: addressData.processingTime || addressData.processing_time,
        originalText: addressData.originalText || body.text,
        requestId: response.metadata.requestId,
        responseTime: response.responseTime
      };
    }
    
    // 如果是异步模式，简化返回结构
    if (response.metadata?.processingMode === 'async') {
      return {
        success: true,
        jobId: response.jobId || '',
        status: 'queued',
        statusUrl: `/v1/op/api/tasks/${response.jobId}`,
        message: '任务已提交到队列，请通过statusUrl查询结果',
        requestId: response.metadata.requestId,
        responseTime: response.responseTime
      };
    }
    
    // 其他情况，返回原始响应
    return response;
  }

  /**
   * 地址提取服务 - 批量地址
   */
  @ApiOperation({ summary: '地址提取服务 - 批量地址' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        texts: {
          type: 'array',
          items: {
            type: 'string',
          },
          description: '包含地址的文本数组',
        },
        mode: {
          type: 'string',
          enum: ['sync', 'async'],
          description: '处理模式: sync(同步) 或 async(异步)',
        },
        callbackUrl: {
          type: 'string',
          description: '异步处理回调URL (仅异步模式有效)',
        },
      },
      required: ['texts'],
    },
  })
  @Post('address/extract-batch')
  async extractAddressBatch(
    @Body() body: any,
    @Query() query: any,
    @Req() req: Request,
    @CurrentUser() user: AuthUser,
    @CurrentApiKey() apiKey: AuthApiKey,
  ) {
    if (!body.texts || !Array.isArray(body.texts) || body.texts.length === 0) {
      throw new BadRequestException('缺少文本数组或数组为空');
    }

    // 调用网关代理服务
    const response = await this.gatewayProxyService.proxyRequest({
      method: 'POST',
      serviceCode: 'address',
      path: 'extract-batch',
      body,
      query,
      headers: req.headers as Record<string, string>,
      user,
      apiKey,
      originalRequest: req,
      forceProcessingMode: body.mode as 'sync' | 'async',
    });

    // 如果是同步模式，简化返回结构
    if (response.metadata?.processingMode === 'sync' && response.data) {
      // 提取地址数据
      const batchData = response.data;
      
      // 构建简化的返回结构
      return {
        success: batchData.success !== false,
        results: batchData.results || [],
        total: body.texts.length,
        processed: batchData.results?.length || 0,
        processingTime: batchData.processingTime || batchData.processing_time,
        requestId: response.metadata.requestId,
        responseTime: response.responseTime
      };
    }
    
    // 如果是异步模式，简化返回结构
    if (response.metadata?.processingMode === 'async') {
      return {
        success: true,
        jobId: response.jobId || '',
        status: 'queued',
        statusUrl: `/v1/op/api/tasks/${response.jobId}`,
        message: '任务已提交到队列，请通过statusUrl查询结果',
        requestId: response.metadata.requestId,
        responseTime: response.responseTime
      };
    }
    
    // 其他情况，返回原始响应
    return response;
  }

  /**
   * 坐标逆解析服务 - 单个坐标
   */
  @ApiOperation({ summary: '坐标逆解析服务 - 单个坐标' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        latitude: {
          type: 'number',
          description: '纬度',
        },
        longitude: {
          type: 'number',
          description: '经度',
        },
        mode: {
          type: 'string',
          enum: ['sync', 'async'],
          description: '处理模式: sync(同步) 或 async(异步)',
        },
        callbackUrl: {
          type: 'string',
          description: '异步处理回调URL (仅异步模式有效)',
        },
      },
      required: ['latitude', 'longitude'],
    },
  })

  @Post('geo/reverse')
  async reverseGeocode(
    @Body() body: any,
    @Query() query: any,
    @Req() req: Request,
    @CurrentUser() user: AuthUser,
    @CurrentApiKey() apiKey: AuthApiKey,
  ) {
    if (body.latitude === undefined || body.longitude === undefined) {
      throw new BadRequestException('缺少坐标数据');
    }

    // 确保坐标数据格式正确，转换为数字类型
    const formattedBody = {
      ...body,
      latitude: Number(body.latitude),
      longitude: Number(body.longitude),
      coordinates: [{ lat: Number(body.latitude), lng: Number(body.longitude) }]
    };

    this.logger.debug(`坐标逆解析请求: ${JSON.stringify(formattedBody)}`);

    // 调用网关代理服务
    const response = await this.gatewayProxyService.proxyRequest({
      method: 'POST',
      serviceCode: 'geo',
      path: 'reverse',
      body: formattedBody,
      query,
      headers: req.headers as Record<string, string>,
      user,
      apiKey,
      originalRequest: req,
      forceProcessingMode: body.mode as 'sync' | 'async',
    });

    // 如果是同步模式，简化返回结构
    if (response.metadata?.processingMode === 'sync' && response.data) {
      // 提取地理数据
      const geoData = response.data;
      
      // 构建简化的返回结构
      return {
        success: geoData.success !== false,
        address: geoData.address || {},
        coordinates: geoData.coordinates || { latitude: body.latitude, longitude: body.longitude },
        confidence: geoData.confidence,
        processing_time: geoData.processing_time,
        resolution_method: geoData.resolution_method,
        requestId: response.metadata.requestId,
        responseTime: response.responseTime
      };
    }
    
    // 如果是异步模式，简化返回结构
    if (response.metadata?.processingMode === 'async') {
      return {
        success: true,
        jobId: response.jobId || '',
        status: 'queued',
        statusUrl: `/v1/op/api/tasks/${response.jobId}`,
        message: '任务已提交到队列，请通过statusUrl查询结果',
        requestId: response.metadata.requestId,
        responseTime: response.responseTime
      };
    }
    
    // 如果是异步模式或其他情况，返回原始响应
    return response;
  }

  /**
   * 坐标逆解析服务 - 批量坐标
   */
  @ApiOperation({ summary: '坐标逆解析服务 - 批量坐标' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        coordinates: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              latitude: {
                type: 'number',
                description: '纬度',
              },
              longitude: {
                type: 'number',
                description: '经度',
              },
            },
          },
          description: '坐标数组',
        },
        mode: {
          type: 'string',
          enum: ['sync', 'async'],
          description: '处理模式: sync(同步) 或 async(异步)',
        },
        callbackUrl: {
          type: 'string',
          description: '异步处理回调URL (仅异步模式有效)',
        },
      },
      required: ['coordinates'],
    },
  })
  @Post('geo/reverse-batch')
  async reverseGeocodeBatch(
    @Body() body: any,
    @Query() query: any,
    @Req() req: Request,
    @CurrentUser() user: AuthUser,
    @CurrentApiKey() apiKey: AuthApiKey,
  ) {
    if (
      !body.coordinates ||
      !Array.isArray(body.coordinates) ||
      body.coordinates.length === 0
    ) {
      throw new BadRequestException('缺少坐标数组或数组为空');
    }

    // 调用网关代理服务
    const response = await this.gatewayProxyService.proxyRequest({
      method: 'POST',
      serviceCode: 'geo',
      path: 'reverse-batch',
      body,
      query,
      headers: req.headers as Record<string, string>,
      user,
      apiKey,
      originalRequest: req,
      forceProcessingMode: body.mode as 'sync' | 'async',
    });

    // 如果是同步模式，简化返回结构
    if (response.metadata?.processingMode === 'sync' && response.data) {
      // 提取地理数据
      const geoData = response.data;
      
      // 构建简化的返回结构
      return {
        success: geoData.success !== false,
        results: geoData.results || [],
        total_count: geoData.total_count || body.coordinates.length,
        processing_time: geoData.processing_time,
        requestId: response.metadata.requestId,
        responseTime: response.responseTime
      };
    }
    
    // 如果是异步模式，简化返回结构
    if (response.metadata?.processingMode === 'async') {
      return {
        success: true,
        jobId: response.jobId || '',
        status: 'queued',
        statusUrl: `/v1/op/api/tasks/${response.jobId}`,
        message: '任务已提交到队列，请通过statusUrl查询结果',
        requestId: response.metadata.requestId,
        responseTime: response.responseTime
      };
    }
    
    // 如果是异步模式或其他情况，返回原始响应
    return response;
  }

  /**
   * 获取任务状态
   */
  @ApiOperation({ summary: '获取任务状态', description: '通过任务ID查询任务的详细状态' })
  @ApiParam({ name: 'jobId', description: '任务ID' })
  @ApiQuery({ name: 'service', description: '服务代码', required: false })
  @ApiResponse({ status: 200, description: '任务状态信息' })
  @Get('tasks/:jobId')
  @Public()
  async getTaskStatus(
    @Param('jobId') jobId: string,
    @Query('service') service?: string,
  ): Promise<any> {
    if (!jobId) {
      throw new BadRequestException('缺少任务ID');
    }

    this.logger.debug(`获取任务状态: jobId=${jobId}, service=${service || '未指定'}`);
    const result = await this.gatewayProxyService.getTaskStatus(jobId, service);
    this.logger.debug(`获取到任务状态: ${JSON.stringify(result)}`);
    return result;
  }

  /**
   * 批量获取任务状态
   */
  @ApiOperation({ summary: '批量获取任务状态' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        jobIds: {
          type: 'array',
          items: {
            type: 'string',
          },
          description: '任务ID数组',
        },
      },
      required: ['jobIds'],
    },
  })
  @ApiResponse({ status: 200, description: '批量任务状态信息' })
  // @SkipResponseFormat()
  @Post('tasks/batch-status')
  async getBatchTaskStatus(
    @Body() body: { jobIds: string[] },
    @CurrentUser() user: AuthUser,
  ) {
    if (
      !body.jobIds ||
      !Array.isArray(body.jobIds) ||
      body.jobIds.length === 0
    ) {
      throw new BadRequestException('缺少任务ID数组或数组为空');
    }

    const response = await this.gatewayProxyService.getBatchTaskStatus(body.jobIds);
    
    // 简化批量任务状态的返回结构
    if (response && response.results) {
      return {
        success: true,
        results: response.results.map(item => ({
          jobId: item.jobId,
          status: item.status || 'unknown',
          progress: item.progress,
          error: item.error || item.failedReason,
          completedAt: item.finishedOn
        }))
      };
    }
    
    return response;
  }

  /**
   * 取消任务
   */
  @ApiOperation({ summary: '取消任务' })
  @ApiParam({ name: 'jobId', description: '任务ID' })
  @HttpCode(HttpStatus.OK)
  @Post('tasks/:jobId/cancel')
  async cancelTask(
    @Param('jobId') jobId: string,
    @CurrentUser() user: AuthUser,
  ) {
    if (!jobId) {
      throw new BadRequestException('缺少任务ID');
    }

    return this.gatewayProxyService.cancelTask(jobId, user);
  }

  /**
   * 获取网关健康状态
   */
  @ApiOperation({ summary: '获取网关健康状态' })
  @Get('health')
  async getGatewayHealth(): Promise<any> {
    return this.gatewayService.getHealthStatus();
  }

  /**
   * 获取网关统计信息
   */
  @ApiOperation({ summary: '获取网关统计信息' })
  @Get('stats')
  async getGatewayStats(): Promise<any> {
    return this.gatewayService.getStatistics();
  }

  /**
   * 获取服务统计信息
   */
  @ApiOperation({ summary: '获取服务统计信息' })
  @ApiParam({ name: 'serviceCode', description: '服务代码' })
  @Get('stats/:serviceCode')
  async getServiceStats(
    @Param('serviceCode') serviceCode: string,
  ): Promise<any> {
    return this.gatewayService.getServiceStatistics(serviceCode);
  }

  /**
   * 获取请求指标
   */
  @ApiOperation({ summary: '获取请求指标' })
  @ApiParam({ name: 'requestId', description: '请求ID' })
  @Get('metrics/:requestId')
  async getRequestMetrics(@Param('requestId') requestId: string): Promise<any> {
    return this.gatewayService.getRequestMetrics(requestId);
  }

  /**
   * 获取任务状态 - SSE方式
   */
  @ApiOperation({ 
    summary: '获取任务状态 - SSE方式', 
    description: '使用Server-Sent Events (SSE)方式获取任务状态的实时更新。注意：此接口不适合在Swagger UI中直接测试，请使用浏览器、Postman或其他支持SSE的客户端工具测试。'
  })
  @ApiParam({ name: 'jobId', description: '任务ID' })
  @ApiQuery({ name: 'service', description: '服务代码', required: false })
  @ApiQuery({ name: 'timeout', description: '最大等待时间(毫秒)，默认60000', required: false })
  @ApiQuery({ name: 'interval', description: '轮询间隔(毫秒)，默认1000', required: false })
  @Get('tasks/:jobId/stream')
  async getTaskStatusStream(
    @Param('jobId') jobId: string,
    @Query('service') service: string,
    @Query('timeout') timeoutStr: string,
    @Query('interval') intervalStr: string,
    @CurrentUser() user: AuthUser,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    if (!jobId) {
      res.status(400).send({ error: '缺少任务ID' });
      return;
    }

    // 设置SSE响应头
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('X-Accel-Buffering', 'no'); // 禁用Nginx缓冲

    // 解析超时和间隔参数
    const timeout = parseInt(timeoutStr) || 60000; // 默认60秒超时
    const intervalMs = parseInt(intervalStr) || 1000; // 默认1秒查询一次
    
    let lastState = null;
    let lastProgress = 0;
    const startTime = Date.now();

    try {
      // 创建一个定时器，定期查询任务状态
      const subscription = interval(intervalMs).pipe(
        takeWhile(() => Date.now() - startTime < timeout)
      ).subscribe(async () => {
        try {
          const response = await this.gatewayProxyService.getTaskStatus(jobId, service);
          
          // 状态变化或进度变化时才发送更新
          if (response && (lastState !== response.state || lastProgress !== response.progress)) {
            lastState = response.state;
            lastProgress = response.progress || 0;
            const status = response?.status || response?.state || 'unknown'
            // 发送状态更新
            const statusUpdate = {
              jobId,
              status,
              progress: response.progress || 0,
              timestamp: new Date().toISOString()
            };
            
            res.write(`data: ${JSON.stringify(statusUpdate)}\n\n`);
            
            // 如果任务已完成或失败，发送完整结果并结束流
            if (status === 'completed' || status === 'failed') {
              let finalResult;
              
              // 如果是OCR任务且已完成，处理结果
              if ((service === 'ocr' || !service) && status === 'completed' && response.result) {
                try {
                  // 处理不同的结果结构
                  let ocrResults;
                  
                  // 检查各种可能的结果结构
                  if (response.result.data?.rawData?.results) {
                    ocrResults = response.result.data.rawData.results;
                  } else if (response.result.data?.results) {
                    ocrResults = response.result.data.results;
                  } else if (Array.isArray(response.result)) {
                    ocrResults = response.result;
                  } else if (response.result.success && response.result.data?.rawData?.results) {
                    ocrResults = response.result.data.rawData.results;
                  } else if (typeof response.result === 'object') {
                    // 尝试深度搜索结果对象
                    const findResults = (obj: any, depth = 0): any[] | null => {
                      if (depth > 3) return null; // 限制搜索深度
                      
                      if (!obj || typeof obj !== 'object') return null;
                      
                      // 直接检查是否是OCR结果数组
                      if (Array.isArray(obj) && obj.length > 0 && 
                          Array.isArray(obj[0]) && obj[0].length >= 3 && 
                          typeof obj[0][1] === 'string' && 
                          typeof obj[0][2] === 'number') {
                        return obj;
                      }
                      
                      // 搜索对象的所有属性
                      for (const key in obj) {
                        if (key === 'results' && Array.isArray(obj[key]) && obj[key].length > 0) {
                          return obj[key];
                        }
                        
                        if (typeof obj[key] === 'object') {
                          const found = findResults(obj[key], depth + 1);
                          if (found) return found;
                        }
                      }
                      
                      return null;
                    };
                    
                    const foundResults = findResults(response.result);
                    if (foundResults) {
                      ocrResults = foundResults;
                    } else {
                      finalResult = {
                        success: true,
                        status: 'completed',
                        result: response.result,
                        error: '无法解析OCR结果格式',
                      };
                    }
                  } else {
                    finalResult = {
                      success: true,
                      status: 'completed',
                      result: response.result,
                      error: '无法解析OCR结果格式',
                    };
                  }
                  
                  if (!finalResult && ocrResults) {
                    // 解析OCR任务结果
                    const localResponse = await this.gatewayService.localParseOcrResult(ocrResults);
                    
                    if (localResponse?.data) {
                      // 提取process_time和image_info
                      let processTime = 0;
                      let imageInfo = {};
                      
                      if (response.result?.data?.process_time) {
                        processTime = response.result.data.process_time;
                      } else if (response.result?.process_time) {
                        processTime = response.result.process_time;
                      }
                      
                      if (response.result?.data?.image_info) {
                        imageInfo = response.result.data.image_info;
                      } else if (response.result?.image_info) {
                        imageInfo = response.result.image_info;
                      }
                      
                      // 与同步请求保持一致的返回格式
                      finalResult = {
                        success: true,
                        status,
                        ...localResponse.data,
                        process_time: processTime,
                        image_info: imageInfo,
                        requestId: jobId,
                        responseTime: response.timestamp?.finished ? 
                          new Date(response.timestamp.finished).getTime() - new Date(response.timestamp.created).getTime() : 0,
                      };
                    }
                  }
                } catch (error) {
                  this.logger.error(`处理OCR任务结果失败: ${error.message}`, error.stack);
                  finalResult = {
                    success: false,
                    error: `处理OCR任务结果失败: ${error.message}`,
                  };
                }
              } else {
                // 非OCR任务或未完成的任务
                finalResult = {
                  success: status === 'completed',
                  status: status,
                  result: response.result,
                  error: response.failedReason,
                };
              }
              
              // 发送最终结果
              res.write(`event: complete\ndata: ${JSON.stringify(finalResult)}\n\n`);
              
              // 结束订阅和响应
              subscription.unsubscribe();
              res.end();
            }
          }
        } catch (error) {
          // 发送错误信息
          this.logger.error(`SSE获取任务状态失败: ${error.message}`, error.stack);
          res.write(`event: error\ndata: ${JSON.stringify({ error: error.message })}\n\n`);
          
          // 结束订阅和响应
          subscription.unsubscribe();
          res.end();
        }
      });
      
      // 客户端断开连接时清理订阅
      req.on('close', () => {
        subscription.unsubscribe();
        res.end();
      });
    } catch (error) {
      // 发送错误信息并结束响应
      this.logger.error(`SSE初始化失败: ${error.message}`, error.stack);
      res.write(`event: error\ndata: ${JSON.stringify({ error: error.message })}\n\n`);
      res.end();
    }
  }

}
