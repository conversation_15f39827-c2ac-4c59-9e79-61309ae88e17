import { ProcessingMode, TaskStatus } from '../config/gateway.constants';

/**
 * 网关请求接口
 */
export interface IGatewayRequest {
  path: string;
  method: string;
  headers: Record<string, string>;
  body?: any;
  query?: Record<string, any>;
  file?: Express.Multer.File;
  mode?: ProcessingMode;
  serviceId?: number; // 服务ID，由前端传递，避免后端硬编码映射
  clientInfo?: IClientInfo;
  user?: {
    id: number;
    username?: string;
    email?: string;
  };
  requestId?: string;
  clientIp?: string;
}

/**
 * 客户端信息接口
 */
export interface IClientInfo {
  ip: string;
  userAgent?: string;
  referer?: string;
  apiKey?: string;
  userId?: number;
}

/**
 * 路由配置接口
 */
export interface IRouteConfig {
  prefix: string;
  target: string;
  methods: string[];
  allowAsync: boolean;
  allowProxyAsync: boolean;
  defaultMode: ProcessingMode;
  timeout: number;
  maxProxyWaitTime?: number;
  avgProcessingTime?: number;
  queue: string;
  paths?: Record<string, IPathConfig>;
}

/**
 * 路径配置接口
 */
export interface IPathConfig {
  path: string;
  target: string;
  methods: string[];
  allowAsync: boolean;
  allowProxyAsync: boolean;
  defaultMode: ProcessingMode;
  timeout: number;
  queue: string;
}

/**
 * 代理选项接口
 */
export interface IProxyOptions {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  headers?: Record<string, string>;
  validateStatus?: (status: number) => boolean;
}

/**
 * 任务结果接口
 */
export interface ITaskResult {
  taskId: string;
  status: TaskStatus;
  result?: any;
  error?: string;
  progress?: number;
  message?: string;
  createdAt: Date;
  updatedAt?: Date;
  processingTime?: number;
}

/**
 * 任务等待选项接口
 */
export interface ITaskWaitOptions {
  maxWaitTime?: number;
  checkInterval?: number;
  usePollingFallback?: boolean;
  progressCallback?: (progress: number) => void;
}

/**
 * 网关响应接口
 */
export interface IGatewayResponse {
  success: boolean;
  code: number;
  message?: string;
  data?: any;
  taskId?: string;
  status?: TaskStatus;
  progress?: number;
  estimatedCompletion?: number;
  processingTime?: number;
  eventsUrl?: string;
  statusUrl?: string;
  requestId?: string;
  responseTime?: number;
  [key:string]: any
}

/**
 * 文件数据接口
 */
export interface IFileData {
  type: 'file' | 'base64';
  data: any;
  size?: number;
  mimetype?: string;
  originalname?: string;
}

/**
 * 处理模式决策上下文接口
 */
export interface IProcessingModeContext {
  request: IGatewayRequest;
  routeConfig: IRouteConfig;
  requestSize: number;
  estimatedProcessingTime: number;
  systemLoad?: number;
  clientLimits?: IClientLimits;
}

/**
 * 客户端限制接口
 */
export interface IClientLimits {
  maxConcurrentRequests: number;
  currentConcurrentRequests: number;
  allowProxyAsync: boolean;
  priorityLevel: number;
}

/**
 * 服务健康状态接口
 */
export interface IServiceHealth {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime?: number;
  lastCheck: Date;
  errorCount?: number;
  details?: any;
}

/**
 * 网关指标接口
 */
export interface IGatewayMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  avgResponseTime: number;
  activeConcurrentRequests: number;
  queuedTasks: number;
  processingTasks: number;
  completedTasks: number;
  failedTasks: number;
  timeoutTasks: number;
}

/**
 * 路由匹配结果接口
 */
export interface IRouteMatch {
  matched: boolean;
  config?: IRouteConfig;
  pathConfig?: IPathConfig;
  params?: Record<string, string>;
  targetUrl?: string;
}

/**
 * 请求验证结果接口
 */
export interface IRequestValidation {
  isValid: boolean;
  errors: string[];
  fileData?: IFileData;
}

/**
 * 网关服务接口
 */
export interface IGatewayService {
  /**
   * 处理网关请求
   */
  handleRequest(request: IGatewayRequest): Promise<IGatewayResponse>;
  
  /**
   * 确定处理模式
   */
  determineProcessingMode(context: IProcessingModeContext): ProcessingMode;
  
  /**
   * 验证请求
   */
  validateRequest(request: IGatewayRequest): Promise<IRequestValidation>;
  
  /**
   * 获取健康状态
   */
  getHealthStatus(): Promise<IServiceHealth[]>;
}

/**
 * 路由服务接口
 */
export interface IGatewayRouterService {
  /**
   * 匹配路由
   */
  matchRoute(path: string, method: string): IRouteMatch;
  
  /**
   * 获取所有路由配置
   */
  getAllRoutes(): IRouteConfig[];
  
  /**
   * 构建目标URL
   */
  buildTargetUrl(config: IRouteConfig | IPathConfig, path: string, query?: Record<string, any>): string;
}

/**
 * 代理服务接口
 */
export interface IGatewayProxyService {
  /**
   * 转发请求到目标服务
   */
  forwardRequest(
    targetUrl: string,
    request: IGatewayRequest,
    options?: IProxyOptions
  ): Promise<any>;
  
  /**
   * 检查服务健康状态
   */
  checkServiceHealth(serviceUrl: string): Promise<IServiceHealth>;
}

/**
 * 任务结果服务接口
 */
export interface ITaskResultService {
  /**
   * 等待任务完成
   */
  waitForTaskResult(taskId: string, options?: ITaskWaitOptions): Promise<ITaskResult>;
  
  /**
   * 获取任务状态
   */
  getTaskStatus(taskId: string): Promise<ITaskResult>;
  
  /**
   * 估算剩余时间
   */
  estimateRemainingTime(taskType: string, progress: number): number;
}
