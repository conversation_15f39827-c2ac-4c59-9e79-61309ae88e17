# 服务模块需求文档

## 1. 文档概述

本文档描述了开放平台服务模块的需求规范，该模块基于单一职责原则设计，专注于业务服务接口的管理。服务模块负责处理服务的创建、更新、查询和删除等操作，提供对外展示的服务目录，包括服务详情、功能介绍、使用方法和价格等信息。

## 2. 功能需求

### 2.1 核心功能

- **服务生命周期管理**
  - 创建服务：定义新的业务服务接口及其属性
  - 更新服务：修改服务的详情、状态和定价等信息
  - 查询服务：获取服务列表和详细信息
  - 删除服务：软删除不再提供的服务（保留历史记录）

- **服务详情管理**
  - 基础信息：服务名称、类型、代码、状态
  - 功能描述：服务功能的详细介绍和技术规格
  - 调用指南：服务接口的使用方法和示例代码
  - 定价信息：计费模型、单价和套餐选项

- **服务分类与展示**
  - 服务分类管理：按功能类型对服务进行分类
  - 服务状态管理：通过状态字段控制服务的可见性和可用性
  - 服务排序规则：控制服务在官网的展示顺序
  - 服务版本管理：支持服务的多版本并存

### 2.2 非功能需求

- **高可用性**：确保服务目录始终可访问
- **数据一致性**：服务信息在多系统间保持一致
- **可扩展性**：支持新增服务类型和属性
- **性能要求**：服务查询响应时间<200ms

## 3. 技术规范

### 3.1 服务实体模型

```typescript
// 与代码实现一致
@Entity('open_service', {
  comment: 'API服务表',
})
export class ServiceEntity extends BaseEntity {
  @Column({ 
    type: 'varchar', 
    length: 50, 
    unique: true,
    comment: '服务唯一代码' 
  })
  code: string;

  @Column({
    type: 'varchar',
    length: 100,
    comment: '服务名称',
  })
  name: string;

  @Column({
    type: 'enum',
    enum: ServiceType,
    comment: '服务类型：OCR、NLP、CV、AI、DATA、OTHER等',
  })
  type: ServiceType;

  @Column({
    type: 'enum',
    enum: ServiceStatus,
    default: ServiceStatus.DRAFT,
    comment: '服务状态枚举：draft、active、inactive、maintenance、deprecated等',
  })
  status: ServiceStatus;

  @Column({
    type: 'text',
    nullable: true,
    comment: '服务描述',
  })
  description?: string;

  @Column({
    type: 'enum',
    enum: PricingModel,
    default: PricingModel.PER_REQUEST,
    comment: '定价模式枚举：free、per_request、per_token、per_character、subscription等',
  })
  pricingModel: PricingModel;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    comment: '单价（元）'
  })
  unitPrice: number;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '服务端点URL',
  })
  endpoint?: string;

  @Column({
    type: 'int',
    default: 0,
    comment: '排序顺序'
  })
  sortOrder: number;
}

export enum ServiceType {
  AI_SERVICE = 'ai_service',
  OCR = 'ocr',
  NLP = 'nlp',
  CV = 'cv',
  DATA = 'data',
  OTHER = 'other'
}

export enum ServiceStatus {
  DRAFT = 'draft',         // 草稿
  ACTIVE = 'active',       // 已上线
  INACTIVE = 'inactive',   // 不可用
  MAINTENANCE = 'maintenance', // 维护中
  DEPRECATED = 'deprecated',   // 已废弃
  COMING_SOON = 'coming_soon'  // 即将推出
}

export enum PricingModel {
  FREE = 'free',                  // 免费
  PER_REQUEST = 'per_request',    // 按请求次数
  PER_TOKEN = 'per_token',        // 按令牌数量
  PER_CHARACTER = 'per_character',// 按字符数量
  SUBSCRIPTION = 'subscription'   // 订阅制
}
```

### 3.2 API接口定义

#### 创建服务

```
POST /services
Content-Type: application/json
Authorization: Bearer <admin_token>

Request:
{
  "code": "logistics-ocr",
  "name": "物流面单识别",
  "type": "OCR",
  "description": "识别物流快递面单上的文字信息，提取收件人、寄件人等数据",
  "pricingModel": "per_request",
  "unitPrice": 0.1,
  "endpoint": "/v1/op/ocr/logistics",
  "sortOrder": 100
}

// 申通面单OCR服务示例
{
  "code": "sto-ocr",
  "name": "申通面单识别",
  "type": "OCR",
  "description": "专门针对申通物流面单的OCR识别服务，优化识别准确率和字段提取，支持申通特有字段识别",
  "pricingModel": "per_request",
  "unitPrice": 0.12,
  "endpoint": "/v1/op/ocr/sto",
  "sortOrder": 101
}

Response:
{
  "id": 1,
  "code": "logistics-ocr",
  "name": "物流面单识别",
  // 其他返回字段
  "createdAt": "2023-10-01T08:00:00Z",
  "updatedAt": "2023-10-01T08:00:00Z"
}
```

#### 更新服务

```
PATCH /services/{id}
Content-Type: application/json
Authorization: Bearer <admin_token>

Request:
{
  "name": "物流面单识别增强版",
  "description": "更新后的描述信息",
  "unitPrice": 0.08,
  "status": "active"
}

Response:
{
  "id": 1,
  "code": "logistics-ocr",
  "name": "物流面单识别增强版",
  // 其他更新后的字段
  "updatedAt": "2023-10-10T14:30:00Z"
}
```

#### 查询服务列表

```
GET /services?type=ai_service&status=active&page=1&limit=10
Authorization: Bearer <token>

Response:
{
  "data": [
    {
      "id": 1,
      "code": "logistics-ocr",
      "name": "物流面单识别增强版",
      // 其他字段
    },
    // 更多服务
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 42,
    "totalPages": 5
  }
}
```

#### 查询服务详情

```
GET /services/{id}
Authorization: Bearer <token>

Response:
{
  "id": 1,
  "code": "logistics-ocr",
  "name": "物流面单识别增强版",
  "type": "ai_service",
  "status": "active",
  "description": "识别物流快递面单上的文字信息，提取收件人、寄件人等数据...",
  // 完整服务详情
  "createdAt": "2023-10-01T08:00:00Z",
  "updatedAt": "2023-10-10T14:30:00Z"
}
```

#### 删除服务

```
DELETE /services/{id}
Authorization: Bearer <admin_token>

Response:
{
  "message": "服务已成功删除",
  "id": 1
}
```

## 4. 系统架构

### 4.1 模块结构

```
service/
├── dto/
│   ├── create-service.dto.ts      # 创建服务DTO
│   ├── update-service.dto.ts      # 更新服务DTO
│   ├── query-service.dto.ts       # 查询服务DTO
│   └── service-response.dto.ts    # 服务响应DTO
├── entities/
│   └── service.entity.ts          # 服务实体
├── enums/
│   └── service.enum.ts            # 服务相关枚举（状态、类型、定价模型）
├── service.controller.ts          # 服务接口控制器
├── service.service.ts             # 服务核心服务
└── service.module.ts              # 服务模块定义
```

### 4.2 服务生命周期

```
1. 创建服务（草稿状态）
2. 完善服务信息（文档、示例等）
3. 上线服务（转为活跃状态）
4. 更新服务信息（按需）
5. 状态变更（按需：维护中、不可用、废弃等）
6. 软删除服务（保留历史记录）
```

## 5. 实现要点

### 5.1 数据验证与一致性

- 服务代码唯一性检查
- 状态转换验证（如草稿→活跃需要满足必要字段）
- 服务信息变更的版本控制

```typescript
// 简化示意
async validateBeforeCreate(createServiceDto: CreateServiceDto): Promise<void> {
  // 检查代码唯一性
  const existingService = await this.serviceRepository.findOne({
    where: { code: createServiceDto.code }
  });
  
  if (existingService) {
    throw new ConflictException(`服务代码 ${createServiceDto.code} 已存在`);
  }
  
  // 验证定价模型与单价的一致性
  if (createServiceDto.pricingModel !== PricingModel.FREE && 
      (createServiceDto.unitPrice === undefined || createServiceDto.unitPrice <= 0)) {
    throw new BadRequestException('非免费服务必须设置有效的单价');
  }
}
```

### 5.2 软删除机制

服务模块使用TypeORM的软删除特性，而不是物理删除记录：

```typescript
// 软删除实现示例
async remove(id: number): Promise<{ id: number; message: string }> {
  const service = await this.findOne(id);
  
  // 使用软删除
  await this.serviceRepository.softDelete(id);
  this.logger.log(`服务已删除: ID=${id}, 代码=${service.code}`);
  
  return {
    id,
    message: '服务已成功删除',
  };
}
```

### 5.3 服务搜索优化

- 全文检索支持：基于服务名称和描述
- 标签索引：根据服务类型和标签快速过滤
- 排序机制：根据多种条件灵活排序

```typescript
// 简化示意
async searchServices(query: QueryServiceDto): Promise<ServiceListResponseDto> {
  const queryBuilder = this.serviceRepository.createQueryBuilder('service');
  
  // 基础筛选条件
  if (query.type) {
    queryBuilder.andWhere('service.type = :type', { type: query.type });
  }
  
  if (query.status) {
    queryBuilder.andWhere('service.status = :status', { status: query.status });
  }
  
  // 全文搜索
  if (query.search) {
    queryBuilder.andWhere(
      '(service.name ILIKE :search OR service.description ILIKE :search)',
      { search: `%${query.search}%` }
    );
  }
  
  // 标签过滤
  if (query.tags && query.tags.length) {
    queryBuilder.andWhere(
      'service.tags @> :tags',
      { tags: query.tags }
    );
  }
  
  // 可见性筛选
  if (query.isPublic !== undefined) {
    queryBuilder.andWhere('service.isPublic = :isPublic', { isPublic: query.isPublic });
  }
  
  // 排序
  const sortField = this.validateSortField(query.sortBy || 'sortOrder');
  const sortOrder = query.sortOrder === 'ASC' ? 'ASC' : 'DESC';
  queryBuilder.orderBy(`service.${sortField}`, sortOrder);
  
  // 分页
  queryBuilder
    .skip((query.page - 1) * query.limit)
    .take(query.limit);
  
  // 执行查询
  const [services, total] = await queryBuilder.getManyAndCount();
  
  // 转换为响应DTO
  return this.transformToListResponseDto(
    services,
    total,
    query.page,
    query.limit
  );
}
```

## 6. 后续优化方向

- **服务使用统计**：集成调用记录，提供使用情况分析
- **缓存机制**：实现服务列表和服务详情的缓存机制
- **服务公开控制**：增加公开性字段，控制服务对外可见性
- **服务图标**：支持服务图标上传和显示
- **服务文档和示例**：提供更丰富的服务文档和调用示例
- **服务标签系统**：通过标签更灵活地分类和搜索服务