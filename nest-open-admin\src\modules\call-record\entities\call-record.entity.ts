import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';
import { Exclude } from 'class-transformer';
import { GenerateUUID } from '@/common/utils/index';
// 移除直接导入其他模块的Entity，使用字符串关系定义避免循环依赖
// import { ServiceEntity } from '../../service/entities/service.entity';
// import { UserEntity } from '../../user/entities/user.entity';

@Entity('call_record', {
  comment: '调用记录表',
})
export class CallRecordEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  requestId: string; // 唯一请求ID

  @Column({ type: 'text' })
  input: string; // 请求输入

  @Column({ type: 'text', nullable: true })
  output?: string; // 响应输出

  @Column({ type: 'int' })
  cost: number; // 本次调用消耗次数

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number; // 本次调用金额

  @Column({ type: 'boolean', default: false })
  success: boolean; // 是否成功

  @Column({ type: 'varchar', length: 255, nullable: true })
  apiKey?: string; // API密钥ID

  @Column({ type: 'varchar', length: 255, nullable: true })
  endpoint?: string; // 调用的接口路径

  @Column({ type: 'int', nullable: true })
  responseTime?: number; // 响应时间(毫秒)

  // 关系 - 使用字符串关系定义，避免循环引用
  @ManyToOne('UserEntity', 'callRecords')
  @JoinColumn({ name: 'userId' })
  user: any;

  @ManyToOne('ServiceEntity', 'callRecords')
  @JoinColumn({ name: 'serviceId' })
  service: any;
}
