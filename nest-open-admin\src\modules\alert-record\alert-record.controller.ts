import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { AlertRecordService } from './alert-record.service';
import {
  CreateAlertRecordDto,
  UpdateAlertRecordDto,
  QueryAlertRecordDto,
  BatchProcessAlertDto,
  AlertRecordResponseDto,
  AlertRecordListResponseDto,
  AlertStatsDto,
} from './dto/alert-record.dto';

@ApiTags('告警记录管理')
@Controller('alert-record')
export class AlertRecordController {
  constructor(private readonly alertRecordService: AlertRecordService) {}

  @Post()
  @ApiOperation({ summary: '创建告警记录' })
  @ApiResponse({
    status: 201,
    description: '告警记录创建成功',
    type: AlertRecordResponseDto,
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '用户或服务不存在' })
  async create(@Body() createAlertRecordDto: CreateAlertRecordDto): Promise<AlertRecordResponseDto> {
    return await this.alertRecordService.create(createAlertRecordDto);
  }

  @Get()
  @ApiOperation({ summary: '分页查询告警记录列表' })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: AlertRecordListResponseDto,
  })
  async findAll(@Query() queryDto: QueryAlertRecordDto): Promise<AlertRecordListResponseDto> {
    return await this.alertRecordService.findAll(queryDto);
  }

  @Get('stats')
  @ApiOperation({ summary: '获取告警统计信息' })
  @ApiResponse({
    status: 200,
    description: '统计信息获取成功',
    type: AlertStatsDto,
  })
  async getStats(): Promise<AlertStatsDto> {
    return await this.alertRecordService.getStats();
  }

  @Get(':id')
  @ApiOperation({ summary: '查询单个告警记录' })
  @ApiParam({ name: 'id', description: '告警记录ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: AlertRecordResponseDto,
  })
  @ApiResponse({ status: 404, description: '告警记录不存在' })
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<AlertRecordResponseDto> {
    return await this.alertRecordService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新告警记录' })
  @ApiParam({ name: 'id', description: '告警记录ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: AlertRecordResponseDto,
  })
  @ApiResponse({ status: 404, description: '告警记录不存在' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateAlertRecordDto: UpdateAlertRecordDto,
  ): Promise<AlertRecordResponseDto> {
    return await this.alertRecordService.update(id, updateAlertRecordDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除告警记录' })
  @ApiParam({ name: 'id', description: '告警记录ID', type: 'number' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '告警记录不存在' })
  @HttpCode(HttpStatus.OK)
  async remove(@Param('id', ParseIntPipe) id: number): Promise<{ message: string }> {
    return await this.alertRecordService.remove(id);
  }

  @Post('batch-process')
  @ApiOperation({ summary: '批量处理告警记录' })
  @ApiResponse({
    status: 200,
    description: '批量处理成功',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', description: '处理结果消息' },
        processedCount: { type: 'number', description: '处理的记录数量' },
      },
    },
  })
  @ApiResponse({ status: 400, description: '请求参数错误或部分记录不存在' })
  @HttpCode(HttpStatus.OK)
  async batchProcess(
    @Body() batchProcessDto: BatchProcessAlertDto,
  ): Promise<{ message: string; processedCount: number }> {
    return await this.alertRecordService.batchProcess(batchProcessDto);
  }
}
