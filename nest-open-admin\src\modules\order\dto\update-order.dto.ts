import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsOptional,
  IsEnum,
  IsNumber,
  IsPositive,
  Min,
  IsInt,
} from 'class-validator';
import { PaymentStatus } from '../entities/order.entity';

export class UpdateOrderDto {
  @ApiPropertyOptional({ 
    description: '订单金额', 
    example: 100.00,
    minimum: 0.01 
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  amount?: number;

  @ApiPropertyOptional({ 
    description: '购买次数', 
    example: 100,
    minimum: 1 
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  purchaseCount?: number;

  @ApiPropertyOptional({ 
    description: '支付状态', 
    enum: PaymentStatus 
  })
  @IsOptional()
  @IsEnum(PaymentStatus)
  status?: PaymentStatus;
}
