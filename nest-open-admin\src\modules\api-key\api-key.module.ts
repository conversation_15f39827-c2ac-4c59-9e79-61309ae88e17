import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { ApiKeyService } from './api-key.service';
import { <PERSON><PERSON><PERSON>ey<PERSON>ontroller } from './api-key.controller';
import { ApiCallService } from './services/api-call.service';
import { ApiKeyEntity } from './entities/api-key.entity';
import { UserModule } from '../user/user.module';
import { ServiceModule } from '../service/service.module';
import { UserServiceModule } from '../user-service/user-service.module';
import { KeyManagementService } from './services/key-management.service';
import { CallRecordModule } from '../call-record/call-record.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ApiKeyEntity]),
    forwardRef(() => UserModule),
    forwardRef(() => ServiceModule),
    forwardRef(() => UserServiceModule),
    HttpModule,
    forwardRef(() => CallRecordModule),
  ],
  controllers: [ApiKeyController],
  providers: [
    ApiKeyService,
    KeyManagementService,
    ApiCallService,
  ],
  exports: [
    ApiKeyService,
    KeyManagementService,
    ApiCallService,
    TypeOrmModule,
  ],
})
export class ApiKeyModule {}
