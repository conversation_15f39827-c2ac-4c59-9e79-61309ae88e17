import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConfigService } from '@nestjs/config';

/**
 * 事件类型枚举
 */
export enum EventType {
  USER_CREATED = 'user.created',
  USER_UPDATED = 'user.updated',
  USER_DELETED = 'user.deleted',
  API_KEY_CREATED = 'api_key.created',
  API_KEY_UPDATED = 'api_key.updated',
  API_KEY_DELETED = 'api_key.deleted',
  SERVICE_CREATED = 'service.created',
  SERVICE_UPDATED = 'service.updated',
  SERVICE_DELETED = 'service.deleted',
  ORDER_CREATED = 'order.created',
  ORDER_UPDATED = 'order.updated',
  ORDER_COMPLETED = 'order.completed',
  BILLING_SUCCESS = 'billing.success',
  BILLING_FAILED = 'billing.failed',
  QUOTA_WARNING = 'quota.warning',
  QUOTA_EXCEEDED = 'quota.exceeded',
  CALL_RECORD_CREATED = 'call_record.created',
  ALERT_CREATED = 'alert.created',
  ALERT_RESOLVED = 'alert.resolved',
  CACHE_INVALIDATED = 'cache.invalidated',
  PERFORMANCE_SLOW = 'performance.slow',
  PERFORMANCE_WARNING = 'performance.warning',
}

/**
 * 基础事件数据接口
 */
export interface BaseEventData {
  entityId?: number;
  entityType?: string;
  userId?: number;
  serviceId?: number;
  timestamp?: string;
  metadata?: Record<string, any>;
}

/**
 * 基础事件服务抽象类
 * 提供统一的事件发送接口，减少事件相关代码重复
 */
@Injectable()
export abstract class BaseEventService {
  protected readonly logger: Logger;
  protected readonly isEventEnabled: boolean;

  constructor(
    protected readonly eventEmitter: EventEmitter2,
    protected readonly configService: ConfigService,
  ) {
    this.logger = new Logger(this.constructor.name);
    this.isEventEnabled = this.configService.get('events.enabled', true);
  }

  /**
   * 发送事件
   */
  protected async emitEvent<T extends BaseEventData>(
    eventType: EventType | string,
    eventData: T,
    options?: {
      async?: boolean;
      delay?: number;
      retries?: number;
    }
  ): Promise<void> {
    if (!this.isEventEnabled) {
      this.logger.debug(`事件发送已禁用: ${eventType}`);
      return;
    }

    try {
      const enrichedData = this.enrichEventData(eventData);
      
      if (options?.async === false) {
        // 同步发送
        this.eventEmitter.emit(eventType, enrichedData);
      } else {
        // 异步发送（默认）
        setImmediate(() => {
          this.eventEmitter.emit(eventType, enrichedData);
        });
      }
      
      this.logger.debug(`事件发送成功: ${eventType}`, { entityId: eventData.entityId });
    } catch (error) {
      this.logger.error(`事件发送失败: ${eventType}`, error);
      
      // 重试机制
      if (options?.retries && options.retries > 0) {
        setTimeout(() => {
          this.emitEvent(eventType, eventData, {
            ...options,
            retries: options.retries! - 1,
          });
        }, options.delay || 1000);
      }
    }
  }

  /**
   * 批量发送事件
   */
  protected async emitBatchEvents<T extends BaseEventData>(
    events: Array<{ type: EventType | string; data: T }>
  ): Promise<void> {
    if (!this.isEventEnabled || events.length === 0) {
      return;
    }

    try {
      const promises = events.map(event => 
        this.emitEvent(event.type, event.data, { async: true })
      );
      
      await Promise.allSettled(promises);
      this.logger.debug(`批量事件发送完成: ${events.length}个事件`);
    } catch (error) {
      this.logger.error('批量事件发送失败', error);
    }
  }

  /**
   * 发送实体创建事件
   */
  protected async emitEntityCreated(
    entityType: string,
    entityId: number,
    userId?: number,
    metadata?: Record<string, any>
  ): Promise<void> {
    const eventType = `${entityType}.created` as EventType;
    await this.emitEvent(eventType, {
      entityId,
      entityType,
      userId,
      metadata,
    });
  }

  /**
   * 发送实体更新事件
   */
  protected async emitEntityUpdated(
    entityType: string,
    entityId: number,
    userId?: number,
    changes?: Record<string, any>,
    metadata?: Record<string, any>
  ): Promise<void> {
    const eventType = `${entityType}.updated` as EventType;
    await this.emitEvent(eventType, {
      entityId,
      entityType,
      userId,
      metadata: {
        ...metadata,
        changes,
      },
    });
  }

  /**
   * 发送实体删除事件
   */
  protected async emitEntityDeleted(
    entityType: string,
    entityId: number,
    userId?: number,
    metadata?: Record<string, any>
  ): Promise<void> {
    const eventType = `${entityType}.deleted` as EventType;
    await this.emitEvent(eventType, {
      entityId,
      entityType,
      userId,
      metadata,
    });
  }

  /**
   * 发送缓存失效事件
   */
  protected async emitCacheInvalidated(
    cacheKey: string,
    entityType?: string,
    entityId?: number
  ): Promise<void> {
    await this.emitEvent(EventType.CACHE_INVALIDATED, {
      entityType,
      entityId,
      metadata: {
        cacheKey,
      },
    });
  }

  /**
   * 发送性能警告事件
   */
  protected async emitPerformanceWarning(
    operation: string,
    duration: number,
    threshold: number,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.emitEvent(EventType.PERFORMANCE_SLOW, {
      metadata: {
        operation,
        duration,
        threshold,
        ...metadata,
      },
    });
  }

  /**
   * 发送配额警告事件
   */
  protected async emitQuotaWarning(
    userId: number,
    serviceId: number,
    currentUsage: number,
    totalQuota: number,
    warningThreshold: number
  ): Promise<void> {
    await this.emitEvent(EventType.QUOTA_WARNING, {
      userId,
      serviceId,
      metadata: {
        currentUsage,
        totalQuota,
        warningThreshold,
        usagePercentage: (currentUsage / totalQuota) * 100,
      },
    });
  }

  /**
   * 发送配额超限事件
   */
  protected async emitQuotaExceeded(
    userId: number,
    serviceId: number,
    currentUsage: number,
    totalQuota: number
  ): Promise<void> {
    await this.emitEvent(EventType.QUOTA_EXCEEDED, {
      userId,
      serviceId,
      metadata: {
        currentUsage,
        totalQuota,
        excessUsage: currentUsage - totalQuota,
      },
    });
  }

  /**
   * 丰富事件数据
   */
  private enrichEventData<T extends BaseEventData>(eventData: T): T {
    return {
      ...eventData,
      timestamp: eventData.timestamp || new Date().toISOString(),
    };
  }

  /**
   * 获取事件统计信息
   */
  async getEventStats(): Promise<{
    totalEmitted: number;
    totalFailed: number;
    isEnabled: boolean;
  }> {
    // 这里可以实现事件统计逻辑
    // 目前返回基础信息
    return {
      totalEmitted: 0, // 可以从Redis或内存中获取
      totalFailed: 0,  // 可以从Redis或内存中获取
      isEnabled: this.isEventEnabled,
    };
  }
}