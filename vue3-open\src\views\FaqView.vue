<template>
  <div class="faq-page">
      <!-- 页面头部 -->
      <div class="page-hero">
        <div class="hero-content">
          <h1 class="hero-title">常见问题</h1>
          <p class="hero-subtitle">
            为您解答使用过程中的常见疑问
          </p>
        </div>
        <div class="hero-decoration">
          <div class="decoration-circle circle-1"></div>
          <div class="decoration-circle circle-2"></div>
          <div class="decoration-circle circle-3"></div>
        </div>
      </div>
      
      <!-- 搜索框 -->
      <div class="search-section">
        <div class="container">
          <el-input
            v-model="searchQuery"
            placeholder="搜索问题..."
            size="large"
            class="search-input"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>
      
      <!-- 问题分类 -->
      <div class="categories-section">
        <div class="container">
          <div class="category-tabs">
            <el-button
              v-for="category in categories"
              :key="category.key"
              :type="activeCategory === category.key ? 'primary' : 'default'"
              @click="activeCategory = category.key"
              class="category-btn"
            >
              <el-icon><component :is="category.icon" /></el-icon>
              {{ category.name }}
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- FAQ列表 -->
      <div class="faq-section">
        <div class="container">
          <div class="faq-list">
            <el-collapse v-model="activeNames" accordion>
              <el-collapse-item
                v-for="(faq, index) in filteredFaqs"
                :key="index"
                :name="index"
                class="faq-item"
              >
                <template #title>
                  <div class="faq-title">
                    <el-icon class="faq-icon"><QuestionFilled /></el-icon>
                    <span>{{ faq.question }}</span>
                  </div>
                </template>
                <div class="faq-content">
                  <div v-html="faq.answer"></div>
                  <div v-if="faq.tags" class="faq-tags">
                    <el-tag
                      v-for="tag in faq.tags"
                      :key="tag"
                      size="small"
                      type="info"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>
      
      <!-- 联系支持 -->
      <div class="contact-section">
        <div class="container">
          <div class="contact-card">
            <div class="contact-content">
              <h3>没有找到答案？</h3>
              <p>如果您的问题没有在上面找到答案，请联系我们的技术支持团队</p>
              <div class="contact-actions">
                <el-button type="primary" @click="contactSupport">
                  <el-icon><Service /></el-icon>
                  联系技术支持
                </el-button>
                <el-button @click="viewDocs">
                  <el-icon><Document /></el-icon>
                  查看文档
                </el-button>
              </div>
            </div>
            <div class="contact-image">
              <el-icon size="80" color="#409eff"></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Search,
  QuestionFilled,
  Service,
  Document,
  Setting,
  Key,
  CreditCard,
  DataAnalysis,
  Lock
} from '@element-plus/icons-vue'


const router = useRouter()

// 搜索查询
const searchQuery = ref('')

// 当前激活的分类
const activeCategory = ref('all')

// 当前展开的FAQ项
const activeNames = ref([])

// 问题分类
const categories = ref([
  { key: 'all', name: '全部', icon: 'Grid' },
  { key: 'account', name: '账户相关', icon: 'User' },
  { key: 'api', name: 'API使用', icon: 'Setting' },
  { key: 'billing', name: '计费问题', icon: 'CreditCard' },
  { key: 'security', name: '安全相关', icon: 'Lock' }
])

// FAQ数据
const faqs = ref([
  {
    category: 'account',
    question: '如何注册账户？',
    answer: `
      <p>注册账户非常简单：</p>
      <ol>
        <li>点击页面右上角的"注册"按钮</li>
        <li>填写您的邮箱地址和密码</li>
        <li>验证邮箱（查看邮件中的验证链接）</li>
        <li>完成注册，即可开始使用我们的服务</li>
      </ol>
    `,
    tags: ['注册', '账户']
  },
  {
    category: 'account',
    question: '忘记密码怎么办？',
    answer: `
      <p>如果您忘记了密码，可以通过以下步骤重置：</p>
      <ol>
        <li>在登录页面点击"忘记密码"</li>
        <li>输入您的注册邮箱</li>
        <li>查看邮件中的重置链接</li>
        <li>设置新密码</li>
      </ol>
    `,
    tags: ['密码', '重置']
  },
  {
    category: 'api',
    question: '如何获取API密钥？',
    answer: `
      <p>获取API密钥的步骤：</p>
      <ol>
        <li>登录您的账户</li>
        <li>进入控制台页面</li>
        <li>点击"API密钥"菜单</li>
        <li>点击"创建新密钥"按钮</li>
        <li>为密钥设置名称和权限</li>
        <li>保存并复制您的API密钥</li>
      </ol>
      <p><strong>注意：</strong>请妥善保管您的API密钥，不要在公开场所泄露。</p>
    `,
    tags: ['API', '密钥', '安全']
  },
  {
    category: 'api',
    question: 'API调用频率限制是多少？',
    answer: `
      <p>我们的API调用频率限制如下：</p>
      <ul>
        <li><strong>免费版：</strong>每分钟100次请求</li>
        <li><strong>基础版：</strong>每分钟500次请求</li>
        <li><strong>专业版：</strong>每分钟2000次请求</li>
        <li><strong>企业版：</strong>可定制限制</li>
      </ul>
      <p>如果超出限制，API将返回429状态码。建议在代码中实现重试机制。</p>
    `,
    tags: ['API', '限制', '频率']
  },
  {
    category: 'billing',
    question: '如何查看账单和使用情况？',
    answer: `
      <p>查看账单和使用情况：</p>
      <ol>
        <li>登录控制台</li>
        <li>点击"使用统计"查看详细的API调用记录</li>
        <li>点击"账单管理"查看费用明细</li>
        <li>可以按时间范围筛选数据</li>
        <li>支持导出CSV格式的报表</li>
      </ol>
    `,
    tags: ['账单', '统计', '费用']
  },
  {
    category: 'billing',
    question: '支持哪些付款方式？',
    answer: `
      <p>我们支持多种付款方式：</p>
      <ul>
        <li>支付宝</li>
        <li>微信支付</li>
        <li>银行卡支付</li>
        <li>企业转账（企业用户）</li>
      </ul>
      <p>所有支付都采用SSL加密，确保您的支付安全。</p>
    `,
    tags: ['支付', '付款', '安全']
  },
  {
    category: 'security',
    question: '数据安全如何保障？',
    answer: `
      <p>我们采用多重安全措施保护您的数据：</p>
      <ul>
        <li><strong>传输加密：</strong>所有API调用都使用HTTPS加密</li>
        <li><strong>数据加密：</strong>敏感数据在数据库中加密存储</li>
        <li><strong>访问控制：</strong>严格的权限管理和身份验证</li>
        <li><strong>审计日志：</strong>完整的操作日志记录</li>
        <li><strong>定期备份：</strong>数据定期备份，确保不丢失</li>
      </ul>
    `,
    tags: ['安全', '加密', '隐私']
  },
  {
    category: 'api',
    question: 'OCR识别支持哪些图片格式？',
    answer: `
      <p>我们的OCR服务支持以下图片格式：</p>
      <ul>
        <li>JPEG / JPG</li>
        <li>PNG</li>
        <li>BMP</li>
        <li>TIFF</li>
        <li>WEBP</li>
      </ul>
      <p><strong>建议：</strong></p>
      <ul>
        <li>图片大小不超过10MB</li>
        <li>分辨率建议在300DPI以上</li>
        <li>文字清晰，避免模糊或倾斜</li>
      </ul>
    `,
    tags: ['OCR', '图片', '格式']
  }
])

// 过滤后的FAQ列表
const filteredFaqs = computed(() => {
  let filtered = faqs.value
  
  // 按分类过滤
  if (activeCategory.value !== 'all') {
    filtered = filtered.filter(faq => faq.category === activeCategory.value)
  }
  
  // 按搜索关键词过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(faq => 
      faq.question.toLowerCase().includes(query) ||
      faq.answer.toLowerCase().includes(query) ||
      (faq.tags && faq.tags.some(tag => tag.toLowerCase().includes(query)))
    )
  }
  
  return filtered
})

// 联系技术支持
const contactSupport = () => {
  ElMessage.info('技术支持功能开发中，请发送邮件至 <EMAIL>')
}

// 查看文档
const viewDocs = () => {
  router.push('/docs')
}
</script>

<style scoped>
.faq-page {
  min-height: calc(100vh - 70px);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.page-hero {
  position: relative;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 60px 0;
  overflow: hidden;
}

.page-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 2;
  padding: 0 20px;
}

.hero-title {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  font-weight: 400;
}

.hero-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  top: 60%;
  right: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.search-section {
  padding: 40px 0;
  background: #ffffff;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.search-input {
  max-width: 500px;
  margin: 0 auto;
}

.search-input .el-input__wrapper {
  border-radius: 12px;
  border: 2px solid rgba(59, 130, 246, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.search-input .el-input__wrapper:hover {
  border-color: rgba(59, 130, 246, 0.4);
}

.search-input .el-input__wrapper.is-focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.categories-section {
  padding: 30px 0;
  background: #ffffff;
  border-bottom: 1px solid #e2e8f0;
}

.category-tabs {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.category-btn {
  border-radius: 25px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.category-btn .el-icon {
  margin-right: 6px;
}

.faq-section {
  padding: 60px 0;
  background: #f8fafc;
}

.faq-list {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  margin-bottom: 20px;
  border: 1px solid rgba(59, 130, 246, 0.15);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.05);
}

.faq-item:hover {
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

.faq-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #1e293b;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.faq-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.faq-title:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 197, 253, 0.05) 100%);
  color: #3b82f6;
}

.faq-title:hover::before {
  opacity: 1;
}

.faq-item.is-active .faq-title {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%);
  color: #3b82f6;
}

.faq-item.is-active .faq-title::before {
  opacity: 1;
}

.faq-icon {
  margin-right: 12px;
  color: #3b82f6;
  font-size: 1.1rem;
}

.faq-content {
  padding: 24px;
  background: #ffffff;
  border-top: 1px solid rgba(59, 130, 246, 0.1);
}

.faq-content p {
  margin-bottom: 18px;
  line-height: 1.7;
  color: #475569;
  font-size: 1.05rem;
}

.faq-content ol,
.faq-content ul {
  margin-bottom: 18px;
  padding-left: 24px;
}

.faq-content li {
  margin-bottom: 10px;
  line-height: 1.7;
  color: #475569;
  font-size: 1.05rem;
}

.faq-content li::marker {
  color: #3b82f6;
}

.faq-tags {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.faq-tags .el-tag {
  background: rgba(59, 130, 246, 0.1) !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  color: #3b82f6 !important;
  font-weight: 500;
  border-radius: 6px;
}

.contact-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.contact-card {
  background: #ffffff;
  border-radius: 20px;
  padding: 40px;
  display: flex;
  align-items: center;
  gap: 40px;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12);
  border: 1px solid rgba(59, 130, 246, 0.1);
  max-width: 800px;
  margin: 0 auto;
  transition: all 0.3s ease;
}

.contact-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(59, 130, 246, 0.18);
}

.contact-content {
  flex: 1;
}

.contact-content h3 {
  font-size: 1.6rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 16px;
}

.contact-content p {
  color: #64748b;
  margin-bottom: 28px;
  line-height: 1.7;
  font-size: 1.05rem;
}

.contact-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.contact-actions .el-button {
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.contact-actions .el-button--primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.contact-actions .el-button--primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.contact-actions .el-button:not(.el-button--primary) {
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #3b82f6;
}

.contact-actions .el-button:not(.el-button--primary):hover {
  background: rgba(59, 130, 246, 0.05);
  border-color: #3b82f6;
}

.contact-image {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .category-tabs {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 10px;
  }
  
  .contact-card {
    flex-direction: column;
    text-align: center;
    padding: 30px 20px;
  }
  
  .contact-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .page-hero {
    padding: 60px 0;
  }
  
  .faq-section {
    padding: 40px 0;
  }
  
  .contact-section {
    padding: 60px 0;
  }
  
  .category-btn {
    padding: 8px 16px;
    font-size: 14px;
  }
}
</style>