import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, Join<PERSON>olumn } from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';
import { UserEntity } from './user.entity';

@Entity('user_company', {
  comment: '企业认证用户',
})
export class UserCompanyEntity extends BaseEntity {
  @Column({ type: 'int', name: 'user_id', comment: '用户ID' })
  public userId: number;

  @Column({
    type: 'enum',
    enum: ['pending', 'approved', 'rejected'],
    name: 'reviewed_status',
    default: 'pending',
    comment: '审核状态'
  })
  public reviewedStatus: 'pending' | 'approved' | 'rejected';

  @Column({ type: 'timestamp', name: 'submitted_at', nullable: true, comment: '提交时间' })
  public submittedAt: Date;

  @Column({ type: 'timestamp', name: 'reviewed_at', nullable: true, comment: '审核时间' })
  public reviewedAt: Date;

  @Column({ type: 'int', name: 'reviewer_id', nullable: true, comment: '审核人ID' })
  public reviewerId: number;

  @Column({ type: 'text', name: 'review_remark', nullable: true, comment: '审核备注' })
  public reviewRemark: string;

  @Column({
    type: 'varchar',
    comment: '企业名称',
    nullable: true,
  })
  enterprise: string;

  @Column({ type: 'varchar', name: 'license_no', length: 50, default: '', comment: '营业执照号' })
  public licenseNo: string;

  @Column({ type: 'varchar', name: 'license_image', default: '', comment: '营业执照照片' })
  public licenseImage: string;

  @Column({ type: 'varchar', name: 'legal_person', length: 30, default: '', comment: '法人姓名' })
  public legalPerson: string;

  @Column({ type: 'varchar', name: 'address', length: 200, default: '', comment: '企业地址' })
  public address: string;

  @Column({ type: 'varchar', name: 'contact_name', length: 30, default: '', comment: '联系人姓名' })
  public contactName: string;

  @Column({ type: 'varchar', name: 'contact_number', length: 30, default: '', comment: '联系人电话' })
  public contactNumber: string;

  @Column({ type: 'varchar', name: 'contact_phone', length: 20, default: '', comment: '联系人手机' })
  public contactPhone: string;

  // 关联用户
  @ManyToOne(() => UserEntity, user => user.id)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;
}
