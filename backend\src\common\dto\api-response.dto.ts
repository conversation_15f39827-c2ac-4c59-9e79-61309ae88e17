import { ApiProperty } from '@nestjs/swagger';

/**
 * 统一API响应格式
 */
export class ApiResponseDto<T = any> {
  @ApiProperty({
    description: '操作是否成功',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: '业务状态码',
    example: 0,
  })
  code: number;

  @ApiProperty({
    description: '响应消息',
    example: '操作成功',
  })
  message: string;

  @ApiProperty({
    description: '响应数据',
    example: {},
  })
  data?: T;

  @ApiProperty({
    description: '响应时间戳',
    example: '2023-10-01T12:00:00Z',
  })
  timestamp: string;

  @ApiProperty({
    description: '请求ID（用于追踪）',
    example: 'req_abcdef123456',
    required: false,
  })
  requestId?: string;

  constructor(success: boolean, code: number, message: string, data?: T, requestId?: string) {
    this.success = success;
    this.code = code;
    this.message = message;
    this.data = data;
    this.timestamp = new Date().toISOString();
    this.requestId = requestId;
  }

  /**
   * 创建成功响应
   */
  static success<T>(data?: T, message = '操作成功', requestId?: string): ApiResponseDto<T> {
    return new ApiResponseDto(true, 0, message, data, requestId);
  }

  /**
   * 创建错误响应
   */
  static error(code: number, message: string, requestId?: string): ApiResponseDto {
    return new ApiResponseDto(false, code, message, undefined, requestId);
  }
}

/**
 * 分页响应数据结构
 */
export class PaginationMeta {
  @ApiProperty({
    description: '当前页码',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: '每页数量',
    example: 20,
  })
  limit: number;

  @ApiProperty({
    description: '总记录数',
    example: 100,
  })
  totalItems: number;

  @ApiProperty({
    description: '总页数',
    example: 5,
  })
  totalPages: number;

  @ApiProperty({
    description: '是否有下一页',
    example: true,
  })
  hasNext: boolean;

  @ApiProperty({
    description: '是否有上一页',
    example: false,
  })
  hasPrev: boolean;

  constructor(page: number, limit: number, totalItems: number) {
    this.page = page;
    this.limit = limit;
    this.totalItems = totalItems;
    this.totalPages = Math.ceil(totalItems / limit);
    this.hasNext = page < this.totalPages;
    this.hasPrev = page > 1;
  }
}

/**
 * 分页响应格式
 */
export class PaginatedResponseDto<T> {
  @ApiProperty({
    description: '数据列表',
    isArray: true,
  })
  items: T[];

  @ApiProperty({
    description: '分页信息',
    type: PaginationMeta,
  })
  meta: PaginationMeta;

  constructor(items: T[], page: number, limit: number, totalItems: number) {
    this.items = items;
    this.meta = new PaginationMeta(page, limit, totalItems);
  }
}

/**
 * 分页API响应格式
 */
export class PaginatedApiResponseDto<T> extends ApiResponseDto<PaginatedResponseDto<T>> {
  constructor(
    items: T[],
    page: number,
    limit: number,
    totalItems: number,
    message = '查询成功',
    requestId?: string,
  ) {
    const paginatedData = new PaginatedResponseDto(items, page, limit, totalItems);
    super(true, 0, message, paginatedData, requestId);
  }
}

/**
 * 错误响应格式
 */
export class ErrorResponseDto {
  @ApiProperty({
    description: '错误码',
    example: 'BAD_REQUEST',
  })
  code: string;

  @ApiProperty({
    description: '错误消息',
    example: '请求参数错误',
  })
  message: string;

  @ApiProperty({
    description: '错误详情',
    example: ['name字段不能为空'],
    required: false,
  })
  details?: string[];

  @ApiProperty({
    description: '时间戳',
    example: '2023-10-01T12:00:00Z',
  })
  timestamp: string;

  @ApiProperty({
    description: '请求路径',
    example: '/api/v1/users',
    required: false,
  })
  path?: string;

  @ApiProperty({
    description: '请求ID',
    example: 'req_abcdef123456',
    required: false,
  })
  requestId?: string;

  constructor(
    code: string,
    message: string,
    details?: string[],
    path?: string,
    requestId?: string,
  ) {
    this.code = code;
    this.message = message;
    this.details = details;
    this.timestamp = new Date().toISOString();
    this.path = path;
    this.requestId = requestId;
  }
}

/**
 * 任务响应格式（用于异步任务）
 */
export class TaskResponseDto {
  @ApiProperty({
    description: '任务ID',
    example: 'task_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a',
  })
  taskId: string;

  @ApiProperty({
    description: '任务状态',
    example: 'queued',
    enum: ['queued', 'active', 'processing', 'completed', 'failed', 'waiting', 'paused', 'delayed'],
  })
  status: string;

  @ApiProperty({
    description: 'SSE事件流URL',
    example: '/v1/op/tasks/task_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a/events',
  })
  eventsUrl: string;

  @ApiProperty({
    description: '任务状态查询URL',
    example: '/v1/op/tasks/task_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a',
  })
  statusUrl: string;

  @ApiProperty({
    description: '预估完成时间（毫秒）',
    example: 30000,
    required: false,
  })
  estimatedDuration?: number;

  constructor(
    taskId: string,
    status: string,
    estimatedDuration?: number,
  ) {
    this.taskId = taskId;
    this.status = status;
    this.eventsUrl = `/v1/op/tasks/${taskId}/events`;
    this.statusUrl = `/v1/op/tasks/${taskId}`;
    this.estimatedDuration = estimatedDuration;
  }
}

/**
 * 任务状态响应格式
 */
export class TaskStatusResponseDto {
  @ApiProperty({
    description: '任务ID',
    example: 'task_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a',
  })
  taskId: string;

  @ApiProperty({
    description: '任务状态',
    example: 'completed',
  })
  status: string;

  @ApiProperty({
    description: '任务类型',
    example: 'ocr',
  })
  type: string;

  @ApiProperty({
    description: '创建时间',
    example: 1633456789000,
  })
  createdAt: number;

  @ApiProperty({
    description: '更新时间',
    example: 1633456792000,
    required: false,
  })
  updatedAt?: number;

  @ApiProperty({
    description: '任务进度（0-1）',
    example: 1,
    required: false,
  })
  progress?: number;

  @ApiProperty({
    description: '任务结果',
    example: {},
    required: false,
  })
  result?: any;

  @ApiProperty({
    description: '错误信息',
    example: '处理失败：文件格式不支持',
    required: false,
  })
  error?: string;

  @ApiProperty({
    description: '处理时长（毫秒）',
    example: 3000,
    required: false,
  })
  duration?: number;
}
