import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PaymentMethod } from '../../order/enums/order.enum';
import {
  IPaymentProvider,
  PaymentRequest,
  PaymentResult,
  CallbackVerifyResult,
  PaymentQueryResult,
  RefundRequest,
  RefundResult,
} from '../interfaces/payment-provider.interface';

@Injectable()
export class WechatPayService implements IPaymentProvider {
  private readonly logger = new Logger(WechatPayService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * 获取支付方式
   */
  getPaymentMethod(): PaymentMethod {
    return PaymentMethod.WECHAT;
  }

  /**
   * 创建微信支付
   */
  async createPayment(request: PaymentRequest): Promise<PaymentResult> {
    try {
      this.logger.log(`创建微信支付: ${request.paymentNo}, 金额: ${request.amount}`);

      const config = this.getWechatConfig();
      
      // 构建支付参数
      const params = {
        appid: config.appId,
        mch_id: config.mchId,
        nonce_str: this.generateNonceStr(),
        body: request.subject,
        detail: request.body,
        out_trade_no: request.paymentNo,
        total_fee: Math.round(request.amount * 100), // 微信支付金额单位为分
        spbill_create_ip: '127.0.0.1',
        notify_url: request.notifyUrl,
        trade_type: 'NATIVE', // 扫码支付
        time_expire: this.formatExpireTime(new Date(Date.now() + 30 * 60 * 1000)),
      };

      // 生成签名
      const sign = this.generateSign(params, config.key!);
      params['sign'] = sign;

      // 构建XML请求体
      const xmlData = this.buildXml(params);

      // 发送统一下单请求
      const response = await this.sendRequest(config.unifiedOrderUrl, xmlData);
      const result = this.parseXml(response);

      if (result.return_code !== 'SUCCESS' || result.result_code !== 'SUCCESS') {
        return {
          success: false,
          errorMessage: result.return_msg || result.err_code_des,
        };
      }

      return {
        success: true,
        qrCode: result.code_url, // 微信支付二维码
        thirdPartyNo: result.prepay_id,
        expiresAt: new Date(Date.now() + 30 * 60 * 1000),
      };
    } catch (error) {
      this.logger.error(`创建微信支付失败: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message,
      };
    }
  }

  /**
   * 验证微信支付回调
   */
  async verifyCallback(callbackData: Record<string, any>): Promise<CallbackVerifyResult> {
    try {
      this.logger.log(`验证微信支付回调: ${JSON.stringify(callbackData)}`);

      const config = this.getWechatConfig();
      
      // 验证签名
      const isValid = this.verifySign(callbackData, config.key!);
      
      if (!isValid) {
        return {
          success: false,
          errorMessage: '签名验证失败',
        };
      }

      // 验证返回码
      if (callbackData.return_code !== 'SUCCESS' || callbackData.result_code !== 'SUCCESS') {
        return {
          success: false,
          errorMessage: callbackData.return_msg || callbackData.err_code_des,
        };
      }

      return {
        success: true,
        paymentNo: callbackData.out_trade_no,
        thirdPartyNo: callbackData.transaction_id,
        status: 'success',
        amount: parseFloat(callbackData.total_fee) / 100, // 微信金额单位为分，需要转换
        paidAt: callbackData.time_end ? this.parseWechatTime(callbackData.time_end) : new Date(),
        rawData: callbackData,
      };
    } catch (error) {
      this.logger.error(`验证微信支付回调失败: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message,
      };
    }
  }

  /**
   * 查询微信支付状态
   */
  async queryPayment(paymentNo: string, thirdPartyNo?: string): Promise<PaymentQueryResult> {
    try {
      this.logger.log(`查询微信支付状态: ${paymentNo}`);

      const config = this.getWechatConfig();
      
      // 构建查询参数
      const params = {
        appid: config.appId,
        mch_id: config.mchId,
        out_trade_no: paymentNo,
        transaction_id: thirdPartyNo,
        nonce_str: this.generateNonceStr(),
      };

      // 生成签名
      const sign = this.generateSign(params, config.key!);
      params['sign'] = sign;

      // 构建XML请求体
      const xmlData = this.buildXml(params);

      // 发送查询请求
      const response = await this.sendRequest(config.orderQueryUrl, xmlData);
      const result = this.parseXml(response);

      if (result.return_code !== 'SUCCESS' || result.result_code !== 'SUCCESS') {
        return {
          success: false,
          errorMessage: result.return_msg || result.err_code_des,
        };
      }

      // 解析支付状态
      let status: 'success' | 'failed' | 'pending' | 'closed' = 'pending';
      switch (result.trade_state) {
        case 'SUCCESS':
          status = 'success';
          break;
        case 'CLOSED':
        case 'REVOKED':
        case 'PAYERROR':
          status = 'failed';
          break;
        case 'NOTPAY':
        case 'USERPAYING':
          status = 'pending';
          break;
        default:
          status = 'pending';
      }

      return {
        success: true,
        status,
        thirdPartyNo: result.transaction_id,
        amount: parseFloat(result.total_fee) / 100,
        paidAt: result.time_end ? this.parseWechatTime(result.time_end) : undefined,
      };
    } catch (error) {
      this.logger.error(`查询微信支付状态失败: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message,
      };
    }
  }

  /**
   * 微信支付退款
   */
  async refund(request: RefundRequest): Promise<RefundResult> {
    try {
      this.logger.log(`申请微信退款: ${request.refundNo}, 金额: ${request.refundAmount}`);

      const config = this.getWechatConfig();
      
      // 构建退款参数
      const params = {
        appid: config.appId,
        mch_id: config.mchId,
        nonce_str: this.generateNonceStr(),
        out_trade_no: request.paymentNo,
        transaction_id: request.thirdPartyNo,
        out_refund_no: request.refundNo,
        total_fee: Math.round(request.totalAmount * 100),
        refund_fee: Math.round(request.refundAmount * 100),
        refund_desc: request.refundReason || '用户申请退款',
      };

      // 生成签名
      const sign = this.generateSign(params, config.key!);
      params['sign'] = sign;

      // 构建XML请求体
      const xmlData = this.buildXml(params);

      // 发送退款请求（需要证书）
      const response = await this.sendSecureRequest(config.refundUrl, xmlData, config.certPath!);
      const result = this.parseXml(response);

      if (result.return_code !== 'SUCCESS' || result.result_code !== 'SUCCESS') {
        return {
          success: false,
          errorMessage: result.return_msg || result.err_code_des,
        };
      }

      return {
        success: true,
        refundNo: request.refundNo,
        thirdPartyRefundNo: result.refund_id,
        refundAmount: parseFloat(result.refund_fee) / 100,
      };
    } catch (error) {
      this.logger.error(`微信退款失败: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message,
      };
    }
  }

  /**
   * 查询微信退款状态
   */
  async queryRefund(refundNo: string, thirdPartyRefundNo?: string): Promise<RefundResult> {
    try {
      this.logger.log(`查询微信退款状态: ${refundNo}`);

      // 微信退款查询接口实现
      // 这里简化处理，实际应该调用退款查询接口
      
      return {
        success: true,
        refundNo,
        thirdPartyRefundNo,
      };
    } catch (error) {
      this.logger.error(`查询微信退款状态失败: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message,
      };
    }
  }

  /**
   * 获取微信配置
   */
  private getWechatConfig() {
    return {
      appId: this.configService.get<string>('wechat.appId'),
      mchId: this.configService.get<string>('wechat.mchId'),
      key: this.configService.get<string>('wechat.key'),
      certPath: this.configService.get<string>('wechat.certPath'),
      unifiedOrderUrl: 'https://api.mch.weixin.qq.com/pay/unifiedorder',
      orderQueryUrl: 'https://api.mch.weixin.qq.com/pay/orderquery',
      refundUrl: 'https://api.mch.weixin.qq.com/secapi/pay/refund',
    };
  }

  /**
   * 生成随机字符串
   */
  private generateNonceStr(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  /**
   * 生成签名
   */
  private generateSign(params: Record<string, any>, key: string): string {
    // 实际实现中需要使用 MD5 签名算法
    // 这里简化处理
    return 'mock_signature';
  }

  /**
   * 验证签名
   */
  private verifySign(params: Record<string, any>, key: string): boolean {
    // 实际实现中需要使用 MD5 验签算法
    // 这里简化处理
    return true;
  }

  /**
   * 构建XML
   */
  private buildXml(params: Record<string, any>): string {
    let xml = '<xml>';
    for (const key in params) {
      xml += `<${key}><![CDATA[${params[key]}]]></${key}>`;
    }
    xml += '</xml>';
    return xml;
  }

  /**
   * 解析XML
   */
  private parseXml(xml: string): Record<string, any> {
    // 实际实现中需要使用 XML 解析器
    // 这里简化处理
    return {
      return_code: 'SUCCESS',
      result_code: 'SUCCESS',
      code_url: 'weixin://wxpay/bizpayurl?pr=mock',
      prepay_id: 'mock_prepay_id',
      transaction_id: 'mock_transaction_id',
      total_fee: '10000',
      time_end: '20231201103000',
      trade_state: 'SUCCESS',
      refund_id: 'mock_refund_id',
      refund_fee: '10000',
    };
  }

  /**
   * 格式化过期时间
   */
  private formatExpireTime(date: Date): string {
    return date.toISOString().replace(/[-:]/g, '').replace('T', '').substring(0, 14);
  }

  /**
   * 解析微信时间格式
   */
  private parseWechatTime(timeStr: string): Date {
    // 微信时间格式: 20231201103000
    const year = parseInt(timeStr.substring(0, 4));
    const month = parseInt(timeStr.substring(4, 6)) - 1;
    const day = parseInt(timeStr.substring(6, 8));
    const hour = parseInt(timeStr.substring(8, 10));
    const minute = parseInt(timeStr.substring(10, 12));
    const second = parseInt(timeStr.substring(12, 14));
    
    return new Date(year, month, day, hour, minute, second);
  }

  /**
   * 发送HTTP请求
   */
  private async sendRequest(url: string, data: string): Promise<string> {
    // 实际实现中需要使用 HTTP 客户端发送请求
    // 这里简化处理
    return '<xml><return_code><![CDATA[SUCCESS]]></return_code><result_code><![CDATA[SUCCESS]]></result_code></xml>';
  }

  /**
   * 发送HTTPS请求（带证书）
   */
  private async sendSecureRequest(url: string, data: string, certPath: string): Promise<string> {
    // 实际实现中需要使用 HTTPS 客户端发送请求，并携带证书
    // 这里简化处理
    return '<xml><return_code><![CDATA[SUCCESS]]></return_code><result_code><![CDATA[SUCCESS]]></result_code></xml>';
  }
}
