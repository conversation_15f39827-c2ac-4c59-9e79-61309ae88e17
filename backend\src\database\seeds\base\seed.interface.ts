import { DataSource } from 'typeorm';

/**
 * 种子数据配置接口
 */
export interface SeedConfig {
  /** 种子数据名称 */
  name: string;
  
  /** 种子数据描述 */
  description: string;
  
  /** 适用环境 */
  environments: string[];
  
  /** 依赖的种子数据 */
  dependencies: string[];
  
  /** 版本号 */
  version: string;
  
  /** 是否强制执行（忽略已执行状态） */
  force?: boolean;
  
  /** 执行优先级（数字越小优先级越高） */
  priority?: number;
}

/**
 * 种子数据执行结果
 */
export interface SeedResult {
  /** 是否成功 */
  success: boolean;
  
  /** 执行消息 */
  message: string;
  
  /** 影响的记录数 */
  affectedRows?: number;
  
  /** 执行时间（毫秒） */
  executionTime?: number;
  
  /** 错误信息 */
  error?: string;
}

/**
 * 种子数据执行状态
 */
export interface SeedStatus {
  /** 种子数据名称 */
  name: string;
  
  /** 是否已执行 */
  executed: boolean;
  
  /** 执行时间 */
  executedAt?: Date;
  
  /** 执行版本 */
  version?: string;
  
  /** 执行结果 */
  result?: SeedResult;
}

/**
 * 种子数据基础接口
 */
export interface ISeed {
  /** 种子数据配置 */
  config: SeedConfig;
  
  /**
   * 执行种子数据
   * @param dataSource 数据源
   * @returns 执行结果
   */
  run(dataSource: DataSource): Promise<SeedResult>;
  
  /**
   * 回滚种子数据（可选）
   * @param dataSource 数据源
   * @returns 回滚结果
   */
  revert?(dataSource: DataSource): Promise<SeedResult>;
  
  /**
   * 检查种子数据是否需要执行
   * @param dataSource 数据源
   * @returns 是否需要执行
   */
  shouldRun?(dataSource: DataSource): Promise<boolean>;
}

/**
 * 种子数据执行选项
 */
export interface SeedRunOptions {
  /** 指定执行的种子数据名称 */
  names?: string[];
  
  /** 目标环境 */
  environment?: string;
  
  /** 是否强制执行 */
  force?: boolean;
  
  /** 是否跳过依赖检查 */
  skipDependencies?: boolean;
  
  /** 是否并行执行 */
  parallel?: boolean;
  
  /** 是否显示详细日志 */
  verbose?: boolean;
}
