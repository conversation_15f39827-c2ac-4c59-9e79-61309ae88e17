import 'tsconfig-paths/register';
import { DataSource } from 'typeorm';
import { config } from 'dotenv';
import * as bcrypt from 'bcrypt';
import * as path from 'path';

// 加载环境变量
config();

// 导入配置
const configData = require('../../config/index').default();

async function checkApiKeys() {
  console.log('开始检查API密钥...');

  // 创建数据库连接
  const dataSource = new DataSource({
    type: 'mysql',
    host: configData.database.host,
    port: configData.database.port,
    username: configData.database.username,
    password: configData.database.password,
    database: configData.database.database,
    synchronize: false,
  });

  try {
    // 初始化数据库连接
    await dataSource.initialize();
    console.log('✅ 数据库连接成功');

    // 直接使用SQL查询API密钥表
    const apiKeys = await dataSource.query(`
      SELECT * FROM open_api_key WHERE deletedAt IS NULL
    `);

    console.log(`找到 ${apiKeys.length} 个API密钥`);

    // 打印每个API密钥的信息
    for (const key of apiKeys) {
      console.log('\n------------------------------');
      console.log(`API密钥 #${key.id}`);
      console.log(`名称: ${key.name}`);
      console.log(`用户ID: ${key.user_id}`);
      console.log(`服务ID: ${key.service_id}`);
      console.log(`状态: ${key['key-status']}`);
      console.log(`类型: ${key.keyType}`);
      console.log(`密钥哈希: ${key.keyHash}`);
      console.log(`密钥秘钥哈希: ${key.secretHash}`);
      console.log(`权限: ${key.permissions}`);
      console.log('------------------------------');
    }

    // 测试哈希匹配
    console.log('\n开始测试API密钥哈希匹配...');
    
    // 测试种子数据中的API密钥
    const testApiKeys = [
      { name: '管理员密钥', key: 'ak_admin_test_key_12345678901234567890', secret: 'sk_admin_test_secret_12345678901234567890' },
      { name: '测试用户密钥', key: 'ak_a1fb793ecce140d3851dbae3e5568dbf', secret: 'sk_2ed1efaf41f14c3d9dafd63aac35cadf' }
    ];

    for (const testKey of testApiKeys) {
      console.log(`\n测试 ${testKey.name}:`);
      const keyHash = await bcrypt.hash(testKey.key, 10);
      console.log(`生成的密钥哈希: ${keyHash}`);
      
      // 检查是否有匹配的密钥哈希
      let found = false;
      for (const key of apiKeys) {
        try {
          const isMatch = await bcrypt.compare(testKey.key, key.keyHash);
          if (isMatch) {
            console.log(`✅ 找到匹配的API密钥 #${key.id}, 名称: ${key.name}`);
            found = true;
            break;
          }
        } catch (error) {
          console.error(`比较失败: ${error.message}`);
        }
      }
      
      if (!found) {
        console.log(`❌ 未找到匹配的API密钥`);
        
        // 尝试直接使用bcrypt.compare比较每个密钥哈希
        console.log('尝试直接比较每个密钥哈希...');
        for (const key of apiKeys) {
          try {
            console.log(`比较 ${testKey.key} 与 ${key.keyHash}`);
            const isMatch = await bcrypt.compare(testKey.key, key.keyHash);
            console.log(`比较结果: ${isMatch}`);
          } catch (error) {
            console.error(`比较失败: ${error.message}`);
          }
        }
      }

    }

  } catch (error) {
    console.error('❌ 检查API密钥失败:', error);
  } finally {
    // 关闭数据库连接
    if (dataSource.isInitialized) {
      await dataSource.destroy();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行检查
checkApiKeys().catch(console.error); 