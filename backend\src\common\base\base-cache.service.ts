import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../../shared/redis.service';

/**
 * 基础缓存服务抽象类
 * 提供统一的缓存操作接口，减少缓存相关代码重复
 */
@Injectable()
export abstract class BaseCacheService<T = any> {
  protected readonly logger: Logger;
  protected abstract readonly cachePrefix: string;
  protected abstract readonly defaultTTL: number;

  constructor(protected readonly redisService: RedisService) {
    this.logger = new Logger(this.constructor.name);
  }

  /**
   * 生成缓存键
   */
  protected getCacheKey(identifier: string | number, suffix?: string): string {
    const key = `${this.cachePrefix}${identifier}`;
    return suffix ? `${key}:${suffix}` : key;
  }

  /**
   * 设置缓存
   */
  async setCache(identifier: string | number, data: T, ttl?: number, suffix?: string): Promise<void> {
    try {
      const key = this.getCacheKey(identifier, suffix);
      const value = this.serializeData(data);
      const cacheTTL = ttl || this.defaultTTL;
      
      await this.redisService.setex(key, cacheTTL, value);
      this.logger.debug(`缓存设置成功: ${key}`);
    } catch (error) {
      this.logger.error(`设置缓存失败: ${identifier}`, error);
      // 缓存失败不应该影响业务逻辑
    }
  }

  /**
   * 获取缓存
   */
  async getCache(identifier: string | number, suffix?: string): Promise<T | null> {
    try {
      const key = this.getCacheKey(identifier, suffix);
      const value = await this.redisService.get(key);
      
      if (!value) {
        this.logger.debug(`缓存未命中: ${key}`);
        return null;
      }
      
      this.logger.debug(`缓存命中: ${key}`);
      return this.deserializeData(value);
    } catch (error) {
      this.logger.error(`获取缓存失败: ${identifier}`, error);
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async deleteCache(identifier: string | number, suffix?: string): Promise<void> {
    try {
      const key = this.getCacheKey(identifier, suffix);
      await this.redisService.del(key);
      this.logger.debug(`缓存删除成功: ${key}`);
    } catch (error) {
      this.logger.error(`删除缓存失败: ${identifier}`, error);
    }
  }

  /**
   * 刷新缓存
   */
  async refreshCache(identifier: string | number, data: T, ttl?: number, suffix?: string): Promise<void> {
    await this.deleteCache(identifier, suffix);
    await this.setCache(identifier, data, ttl, suffix);
  }

  /**
   * 批量删除缓存
   */
  async deleteCachePattern(pattern: string): Promise<void> {
    try {
      const searchPattern = `${this.cachePrefix}${pattern}`;
      const keys = await this.redisService.keys(searchPattern);
      
      if (keys.length > 0) {
        await this.redisService.delMany(keys);
        this.logger.debug(`批量删除缓存成功: ${keys.length}个键`);
      }
    } catch (error) {
      this.logger.error(`批量删除缓存失败: ${pattern}`, error);
    }
  }

  /**
   * 检查缓存是否存在
   */
  async existsCache(identifier: string | number, suffix?: string): Promise<boolean> {
    try {
      const key = this.getCacheKey(identifier, suffix);
      return await this.redisService.exists(key);
    } catch (error) {
      this.logger.error(`检查缓存存在性失败: ${identifier}`, error);
      return false;
    }
  }

  /**
   * 设置缓存过期时间
   */
  async expireCache(identifier: string | number, ttl: number, suffix?: string): Promise<void> {
    try {
      const key = this.getCacheKey(identifier, suffix);
      await this.redisService.expire(key, ttl);
      this.logger.debug(`设置缓存过期时间成功: ${key}, TTL: ${ttl}`);
    } catch (error) {
      this.logger.error(`设置缓存过期时间失败: ${identifier}`, error);
    }
  }

  /**
   * 获取缓存剩余过期时间
   */
  async getTTL(identifier: string | number, suffix?: string): Promise<number> {
    try {
      const key = this.getCacheKey(identifier, suffix);
      return await this.redisService.ttl(key);
    } catch (error) {
      this.logger.error(`获取缓存TTL失败: ${identifier}`, error);
      return -1;
    }
  }

  /**
   * 原子性增加数值
   */
  async incrementCache(identifier: string | number, increment: number = 1, suffix?: string): Promise<number> {
    try {
      const key = this.getCacheKey(identifier, suffix);
      return await this.redisService.incr(key, increment);
    } catch (error) {
      this.logger.error(`缓存增量操作失败: ${identifier}`, error);
      throw error;
    }
  }

  /**
   * 原子性减少数值
   */
  async decrementCache(identifier: string | number, decrement: number = 1, suffix?: string): Promise<number> {
    try {
      const key = this.getCacheKey(identifier, suffix);
      return await this.redisService.decr(key, decrement);
    } catch (error) {
      this.logger.error(`缓存减量操作失败: ${identifier}`, error);
      throw error;
    }
  }

  /**
   * 序列化数据
   */
  protected serializeData(data: T): string {
    return typeof data === 'object' ? JSON.stringify(data) : String(data);
  }

  /**
   * 反序列化数据
   */
  protected deserializeData(value: string): T {
    try {
      return JSON.parse(value) as T;
    } catch {
      return value as unknown as T;
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats(): Promise<{
    totalKeys: number;
    memoryUsage: string;
    hitRate?: number;
  }> {
    try {
      const keys = await this.redisService.keys(`${this.cachePrefix}*`);
      const info = await this.redisService.info();
      
      // 解析内存使用情况
      const memoryMatch = info.match(/used_memory_human:([^\r\n]+)/);
      const memoryUsage = memoryMatch ? memoryMatch[1] : 'Unknown';
      
      return {
        totalKeys: keys.length,
        memoryUsage,
      };
    } catch (error) {
      this.logger.error('获取缓存统计信息失败', error);
      return {
        totalKeys: 0,
        memoryUsage: 'Unknown',
      };
    }
  }
}