import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  UseGuards,
  ForbiddenException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiSecurity,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { UserServiceService } from './user-service.service';
import {
  CreateUserServiceDto,
  UpdateUserServiceDto,
  QueryUserServiceDto,
  BatchAssignServiceDto,
  BatchUpdateCountDto,
  ResetCountDto,
  UserServiceResponseDto,
  UserServiceListResponseDto,
  UserServiceStatsDto,
  UpdateTotalCountDto,
} from './dto/user-service.dto';
import { Roles } from '@/common/decorators/roles.decorator';
import { Public } from '../../common/decorators/public.decorator';
import { CurrentUser } from '@/common/decorators/current-user.decorator';
import { AuthUser } from '@/common/types/auth.types';

@ApiTags('用户服务关联管理')
@Controller('user-service')
export class UserServiceController {
  constructor(private readonly userServiceService: UserServiceService) {}

  @Post()
  @ApiOperation({ summary: '创建用户服务关联' })
  create(@Body() createUserServiceDto: CreateUserServiceDto): Promise<UserServiceResponseDto> {
    return this.userServiceService.create(createUserServiceDto);
  }

  @Get()
  // @Public()
  @ApiOperation({ summary: '查询用户服务关联列表' })
  findAll(@Query() queryDto: QueryUserServiceDto): Promise<UserServiceListResponseDto> {
    return this.userServiceService.findAll(queryDto);
  }

  @Get('stats')
  @ApiOperation({ summary: '获取用户服务统计信息' })
  getStats(): Promise<UserServiceStatsDto> {
    return this.userServiceService.getStats();
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID查询用户服务关联' })
  @ApiParam({ name: 'id', description: '关联ID' })
  findOne(@Param('id', ParseIntPipe) id: number): Promise<UserServiceResponseDto> {
    return this.userServiceService.findOne(id);
  }

  @Get('user/:userId/service/:serviceId')
  @ApiOperation({ summary: '根据用户ID和服务ID查询关联' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiParam({ name: 'serviceId', description: '服务ID' })
  findByUserAndService(
    @Param('userId', ParseIntPipe) userId: number,
    @Param('serviceId', ParseIntPipe) serviceId: number,
  ): Promise<UserServiceResponseDto> {
    return this.userServiceService.findByUserAndService(userId, serviceId);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新用户服务关联' })
  @ApiParam({ name: 'id', description: '关联ID' })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateUserServiceDto: UpdateUserServiceDto,
  ): Promise<UserServiceResponseDto> {
    return this.userServiceService.update(id, updateUserServiceDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除用户服务关联' })
  @ApiParam({ name: 'id', description: '关联ID' })
  remove(@Param('id', ParseIntPipe) id: number): Promise<{ message: string }> {
    return this.userServiceService.remove(id);
  }

  @Post('batch-assign')
  @ApiOperation({ summary: '批量分配服务' })
  batchAssignService(@Body() batchAssignDto: BatchAssignServiceDto): Promise<UserServiceResponseDto[]> {
    return this.userServiceService.batchAssignService(batchAssignDto);
  }

  @Post('batch-update-count')
  @ApiOperation({ summary: '批量更新次数' })
  batchUpdateCount(@Body() batchUpdateDto: BatchUpdateCountDto): Promise<void> {
    return this.userServiceService.batchUpdateCount(batchUpdateDto);
  }

  @Post('reset-count')
  @ApiOperation({ summary: '重置次数' })
  resetCount(@Body() resetDto: ResetCountDto): Promise<UserServiceResponseDto> {
    return this.userServiceService.resetCount(resetDto);
  }

  @ApiBearerAuth()
  @Post('update-total')
  @ApiOperation({ summary: '更新调用总次数（适用于用户购买或充值场景）' })
  @ApiResponse({ 
    status: 200, 
    description: '调用次数更新成功', 
    type: UserServiceResponseDto 
  })
  @ApiResponse({ 
    status: 403, 
    description: '权限不足，只有管理员或用户本人可以调用此接口' 
  })
  async updateTotalCount(
    @Body() updateDto: UpdateTotalCountDto,
    @CurrentUser() currentUser: AuthUser
  ): Promise<UserServiceResponseDto> {
    // 检查权限：只有管理员或用户本人可以调用
    const isAdmin = currentUser.role === 'admin';
    const isSelf = currentUser.id === updateDto.userId;
    
    if (!isAdmin) {
      throw new ForbiddenException('权限不足，只有管理员可以调用此接口');
    }
    // if (!isAdmin && !isSelf) {
    //   throw new ForbiddenException('权限不足，只有管理员或用户本人可以调用此接口');
    // }
    
    // 传递当前用户ID给服务方法，记录操作者身份
    return this.userServiceService.updateTotalCount(updateDto, currentUser.id);
  }
}
