import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { dashboardApi } from '@/api/dashboard'
import type { 
  DashboardOverview, 
  StatCard, 
  UsageTrendData, 
  ServiceDistributionItem, 
  QuickAction, 
  RecentActivity, 
  DashboardSettings, 
  TimeRange 
} from '@/types/dashboard'

// 保持向后兼容的旧接口
export interface DashboardStats {
  totalCalls: number
  totalSpent: number
  activeApiKeys: number
  errorRate: number
  callsGrowth: number
  spentGrowth: number
  activeKeys: number
  totalKeys: number
  errorChange: number
}

export interface UsageTrend {
  date: string
  calls: number
  cost: number
}

export interface ServiceDistribution {
  service: string
  calls: number
  percentage: number
}

export interface SystemNotification {
  id: string
  title: string
  content: string
  type: 'info' | 'warning' | 'error' | 'success'
  timestamp: string
  read: boolean
}

export const useDashboardStore = defineStore('dashboard', () => {
  // 状态
  const loading = ref(false)
  const overview = ref<DashboardOverview | null>(null)
  const settings = ref<DashboardSettings>({
    autoRefresh: true,
    refreshInterval: 30,
    defaultTimeRange: '7d',
    showQuickActions: true,
    showRecentActivities: true,
    theme: 'auto'
  })
  const currentTimeRange = ref<TimeRange>('7d')
  const lastUpdated = ref<Date | null>(null)
  
  // 向后兼容的旧状态
  const stats = ref<DashboardStats>({
    totalCalls: 0,
    totalSpent: 0,
    activeApiKeys: 0,
    errorRate: 0,
    callsGrowth: 0,
    spentGrowth: 0,
    activeKeys: 0,
    totalKeys: 0,
    errorChange: 0
  })
  
  const usageTrend = ref<UsageTrend[]>([])
  const serviceDistribution = ref<ServiceDistribution[]>([])
  const recentActivities = ref<RecentActivity[]>([])
  const notifications = ref<SystemNotification[]>([])
  
  // 计算属性
  const statsCards = computed(() => overview.value?.stats || null)
  const usageTrendData = computed(() => overview.value?.usageTrend || null)
  const serviceDistributionData = computed(() => overview.value?.serviceDistribution || [])
  const quickActions = computed(() => overview.value?.quickActions || [])
  const recentActivitiesData = computed(() => overview.value?.recentActivities || [])

  // 获取仪表盘统计数据
  const getDashboardStats = async () => {
    try {
      loading.value = true
      const response: Record<string,any> = await dashboardApi.getStats(currentTimeRange.value)
      
      if (response.data) {
        stats.value = response.data
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
      ElMessage.error('获取统计数据失败')
    } finally {
      loading.value = false
    }
  }

  // 获取使用趋势数据
  const getUsageTrend = async () => {
    try {
      const response: Record<string,any> = await dashboardApi.getUsageTrend(currentTimeRange.value)
      
      if (response.data) {
        usageTrend.value = response.data
      }
    } catch (error) {
      console.error('获取使用趋势失败:', error)
      ElMessage.error('获取使用趋势失败')
    }
  }

  // 获取服务分布数据
  const getServiceDistribution = async () => {
    try {
      const response: Record<string,any> = await dashboardApi.getServiceDistribution(currentTimeRange.value)
      
      if (response.data) {
        serviceDistribution.value = response.data
      }
    } catch (error) {
      console.error('获取服务分布失败:', error)
      ElMessage.error('获取服务分布失败')
    }
  }

  // 获取最近活动
  const getRecentActivities = async () => {
    try {
      const response: Record<string,any> = await dashboardApi.getRecentActivities(10)
      
      if (response.data) {
        recentActivities.value = response.data
      }
    } catch (error) {
      console.error('获取最近活动失败:', error)
      ElMessage.error('获取最近活动失败')
    }
  }

  // 获取系统通知
  const getNotifications = async (limit: number = 5) => {
    try {
      const response: Record<string,any> = await dashboardApi.getNotifications(limit)
      
      if (response.data) {
        notifications.value = response.data
      }
    } catch (error) {
      console.error('获取系统通知失败:', error)
      ElMessage.error('获取系统通知失败')
    }
  }

  // 标记通知为已读
  const markNotificationAsRead = async (notificationId: string) => {
    try {
      await dashboardApi.markNotificationAsRead(notificationId)
      
      // 更新本地状态
      const notification = notifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.read = true
      }
      
      ElMessage.success('通知已标记为已读')
    } catch (error) {
      console.error('标记通知已读失败:', error)
      ElMessage.error('操作失败，请稍后重试')
      throw error
    }
  }

  // 批量标记通知为已读
  const markAllNotificationsAsRead = async () => {
    try {
      await dashboardApi.markAllNotificationsAsRead()
      
      // 更新本地状态
      notifications.value.forEach(notification => {
        notification.read = true
      })
      
      ElMessage.success('所有通知已标记为已读')
    } catch (error) {
      console.error('批量标记通知已读失败:', error)
      ElMessage.error('操作失败，请稍后重试')
      throw error
    }
  }

  // 删除通知
  const deleteNotification = async (notificationId: string) => {
    try {
      await dashboardApi.deleteNotification(notificationId)
      
      // 更新本地状态
      const index = notifications.value.findIndex(n => n.id === notificationId)
      if (index > -1) {
        notifications.value.splice(index, 1)
      }
      
      ElMessage.success('通知已删除')
    } catch (error) {
      console.error('删除通知失败:', error)
      ElMessage.error('操作失败，请稍后重试')
      throw error
    }
  }

  // 获取仪表盘概览数据（新方法）
  const fetchOverview = async (timeRange: TimeRange = currentTimeRange.value) => {
    try {
      loading.value = true
      currentTimeRange.value = timeRange
      
      const response:any = await dashboardApi.getOverview(timeRange)
      
      if (response.data) {
        overview.value = response.data
        lastUpdated.value = new Date()
      } else {
        throw new Error('获取仪表盘数据失败')
      }
    } catch (error: unknown) {
      console.error('获取仪表盘概览失败:', error)
      ElMessage.error((error as Error).message || '获取仪表盘数据失败')
      
      // 设置默认数据
      overview.value = getDefaultOverview()
    } finally {
      loading.value = false
    }
  }

  // 获取默认概览数据（用于错误时的回退）
  const getDefaultOverview = (): DashboardOverview => {
    return {
      stats: {
        totalCalls: {
          title: '总调用次数',
          value: 0,
          unit: '次',
          trend: { value: 0, type: 'up', period: '较昨日' },
          icon: 'DataAnalysis',
          color: '#409EFF'
        },
        totalAmount: {
          title: '总消费金额',
          value: '0.00',
          unit: '元',
          trend: { value: 0, type: 'up', period: '较昨日' },
          icon: 'Money',
          color: '#67C23A'
        },
        activeKeys: {
          title: '活跃API密钥',
          value: 0,
          unit: '个',
          icon: 'Key',
          color: '#E6A23C'
        },
        errorRate: {
          title: '错误率',
          value: '0.00',
          unit: '%',
          trend: { value: 0, type: 'down', period: '较昨日' },
          icon: 'Warning',
          color: '#F56C6C'
        }
      },
      usageTrend: {
        dates: [],
        series: []
      },
      serviceDistribution: [],
      quickActions: [
        {
          id: 'create-api-key',
          title: '创建API密钥',
          description: '快速创建新的API密钥',
          icon: 'Plus',
          route: '/console/api-keys',
          color: '#409EFF'
        },
        {
          id: 'view-services',
          title: '浏览服务',
          description: '查看可用的API服务',
          icon: 'Service',
          route: '/services',
          color: '#67C23A'
        },
        {
          id: 'api-playground',
          title: 'API测试',
          description: '在线测试API接口',
          icon: 'Monitor',
          route: '/playground',
          color: '#E6A23C'
        },
        {
          id: 'view-docs',
          title: '查看文档',
          description: '阅读API使用文档',
          icon: 'Document',
          route: '/docs',
          color: '#909399'
        }
      ],
      recentActivities: []
    }
  }

  // 更新设置
  const updateSettings = (newSettings: Partial<DashboardSettings>) => {
    settings.value = { ...settings.value, ...newSettings }
    localStorage.setItem('dashboard-settings', JSON.stringify(settings.value))
  }

  // 从本地存储加载设置
  const loadSettings = () => {
    const saved = localStorage.getItem('dashboard-settings')
    if (saved) {
      try {
        settings.value = { ...settings.value, ...JSON.parse(saved) }
      } catch (error) {
        console.warn('加载仪表盘设置失败:', error)
      }
    }
  }

  // 自动刷新定时器
  let refreshTimer: number | null = null

  // 启动自动刷新
  const startAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
    }
    
    if (settings.value.autoRefresh) {
      refreshTimer = setInterval(() => {
        fetchOverview()
      }, settings.value.refreshInterval * 1000)
    }
  }

  // 停止自动刷新
  const stopAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }

  // 刷新所有数据
  const refreshAll = async () => {
    await Promise.all([
      fetchOverview(),
      getNotifications(5)
    ])
  }

  // 加载所有仪表盘数据（保持向后兼容）
  const loadDashboardData = async () => {
    try {
      await Promise.all([
        getDashboardStats(),
        getUsageTrend(),
        getServiceDistribution(),
        getRecentActivities(),
        getNotifications(5)
      ])
    } catch (error) {
      console.error('加载仪表盘数据失败:', error)
      throw error
    }
  }

  // 初始化
  const init = async () => {
    loadSettings()
    await fetchOverview()
    startAutoRefresh()
  }

  return {
    // 状态
    loading,
    overview,
    settings,
    currentTimeRange,
    lastUpdated,
    
    // 向后兼容的旧状态
    stats,
    usageTrend,
    serviceDistribution,
    recentActivities,
    notifications,
    
    // 计算属性
    statsCards,
    usageTrendData,
    serviceDistributionData,
    quickActions,
    recentActivitiesData,
    
    // 新方法
    fetchOverview,
    updateSettings,
    loadSettings,
    startAutoRefresh,
    stopAutoRefresh,
    refreshAll,
    init,
    
    // 向后兼容的旧方法
    getDashboardStats,
    getUsageTrend,
    getServiceDistribution,
    getRecentActivities,
    getNotifications,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    deleteNotification,
    loadDashboardData
  }
})