import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from './redis.service';
import { UserEntity } from '../modules/user/entities/user.entity';
import { BaseCacheService } from '../common/base/base-cache.service';

/**
 * 用户缓存服务
 * 提供用户相关的缓存操作
 */
@Injectable()
export class UserCacheService extends BaseCacheService<Partial<UserEntity>> {
  protected readonly cachePrefix = 'user:';
  protected readonly defaultTTL = 3600; // 1小时

  constructor(redisService: RedisService) {
    super(redisService);
  }

  /**
   * 设置用户缓存
   */
  async setUserCache(userId: string | number, userData: Partial<UserEntity>): Promise<void> {
    await this.setCache(userId, userData);
  }

  /**
   * 获取用户缓存
   */
  async getUserCache(userId: string | number): Promise<Partial<UserEntity> | null> {
    return await this.getCache(userId);
  }

  /**
   * 删除用户缓存
   */
  async deleteUserCache(userId: string | number): Promise<void> {
    await this.deleteCache(userId);
  }

  /**
   * 刷新用户缓存
   */
  async refreshUserCache(userId: string | number, userData: Partial<UserEntity>): Promise<void> {
    await this.refreshCache(userId, userData);
  }

  /**
   * 批量删除用户缓存
   */
  async deleteUserCachePattern(pattern: string): Promise<void> {
    await this.deleteCachePattern(pattern);
  }
}