import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { GatewayService } from '../services/gateway.service';
import { GatewayRouterService } from '../services/gateway-router.service';
import { GatewayProxyService } from '../services/gateway-proxy.service';
import { ProcessingModeService } from '../services/processing-mode.service';
import { TaskResultService } from '../services/task-result.service';
import { ProcessingMode } from '../config/gateway.constants';
import { IGatewayRequest } from '../interfaces/gateway.interfaces';

describe('GatewayService', () => {
  let service: GatewayService;
  let routerService: jest.Mocked<GatewayRouterService>;
  let proxyService: jest.Mocked<GatewayProxyService>;
  let processingModeService: jest.Mocked<ProcessingModeService>;
  let taskResultService: jest.Mocked<TaskResultService>;

  beforeEach(async () => {
    const mockRouterService = {
      matchRoute: jest.fn(),
      buildTargetUrl: jest.fn(),
    };

    const mockProxyService = {
      forwardRequest: jest.fn(),
      checkServiceHealth: jest.fn(),
    };

    const mockProcessingModeService = {
      determineMode: jest.fn(),
    };

    const mockTaskResultService = {
      waitForTaskResult: jest.fn(),
      getTaskStatus: jest.fn(),
      createTask: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GatewayService,
        {
          provide: GatewayRouterService,
          useValue: mockRouterService,
        },
        {
          provide: GatewayProxyService,
          useValue: mockProxyService,
        },
        {
          provide: ProcessingModeService,
          useValue: mockProcessingModeService,
        },
        {
          provide: TaskResultService,
          useValue: mockTaskResultService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<GatewayService>(GatewayService);
    routerService = module.get(GatewayRouterService);
    proxyService = module.get(GatewayProxyService);
    processingModeService = module.get(ProcessingModeService);
    taskResultService = module.get(TaskResultService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('handleRequest', () => {
    it('should handle sync request successfully', async () => {
      const mockRequest: IGatewayRequest = {
        path: '/v1/op/ocr/upload',
        method: 'POST',
        headers: { 'content-type': 'application/json' },
        body: { test: 'data' },
        clientInfo: {
          ip: '127.0.0.1',
          userAgent: 'test-agent',
        },
      };

      const mockRouteMatch = {
        matched: true,
        config: {
          prefix: '/v1/op/ocr',
          target: 'OCR_SERVICE',
          methods: ['POST'],
          allowAsync: true,
          allowProxyAsync: true,
          defaultMode: ProcessingMode.SYNC,
          timeout: 30000,
          queue: 'ocr',
        },
      };

      const mockProxyResult = {
        result: 'success',
        data: { processed: true },
      };

      routerService.matchRoute.mockReturnValue(mockRouteMatch);
      routerService.buildTargetUrl.mockReturnValue('http://localhost:8866/v1/op/ocr/upload');
      processingModeService.determineMode.mockReturnValue(ProcessingMode.SYNC);
      proxyService.forwardRequest.mockResolvedValue(mockProxyResult);

      const result = await service.handleRequest(mockRequest);

      expect(result.success).toBe(true);
      expect(result.code).toBe(200);
      expect(result.data).toEqual(mockProxyResult);
      expect(result.requestId).toBeDefined();
      expect(result.responseTime).toBeDefined();
    });

    it('should handle route not found', async () => {
      const mockRequest: IGatewayRequest = {
        path: '/v1/op/unknown',
        method: 'POST',
        headers: { 'content-type': 'application/json' },
        body: { test: 'data' },
        clientInfo: {
          ip: '127.0.0.1',
          userAgent: 'test-agent',
        },
      };

      routerService.matchRoute.mockReturnValue({ matched: false });

      const result = await service.handleRequest(mockRequest);

      expect(result.success).toBe(false);
      expect(result.code).toBe(400);
      expect(result.message).toContain('请求的路由不存在');
    });

    it('should validate file upload correctly', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 1024 * 1024, // 1MB
        buffer: Buffer.from('test'),
        destination: '',
        filename: '',
        path: '',
        stream: null as any,
      };

      const mockRequest: IGatewayRequest = {
        path: '/v1/op/ocr/upload',
        method: 'POST',
        headers: { 'content-type': 'multipart/form-data' },
        body: { type: 'logistics' },
        file: mockFile,
        clientInfo: {
          ip: '127.0.0.1',
          userAgent: 'test-agent',
        },
      };

      const validation = await service.validateRequest(mockRequest);

      expect(validation.isValid).toBe(true);
      expect(validation.fileData).toBeDefined();
      expect(validation.fileData?.type).toBe('file');
      expect(validation.fileData?.size).toBe(1024 * 1024);
    });

    it('should reject oversized files', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 10 * 1024 * 1024, // 10MB (over limit)
        buffer: Buffer.from('test'),
        destination: '',
        filename: '',
        path: '',
        stream: null as any,
      };

      const mockRequest: IGatewayRequest = {
        path: '/v1/op/ocr/upload',
        method: 'POST',
        headers: { 'content-type': 'multipart/form-data' },
        body: { type: 'logistics' },
        file: mockFile,
        clientInfo: {
          ip: '127.0.0.1',
          userAgent: 'test-agent',
        },
      };

      const validation = await service.validateRequest(mockRequest);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('文件大小超过限制');
    });

    it('should validate base64 image correctly', async () => {
      const validBase64 = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD//gA7Q1JFQVR';

      const mockRequest: IGatewayRequest = {
        path: '/v1/op/ocr/recognize',
        method: 'POST',
        headers: { 'content-type': 'application/json' },
        body: { 
          imageBase64: validBase64,
          type: 'logistics' 
        },
        clientInfo: {
          ip: '127.0.0.1',
          userAgent: 'test-agent',
        },
      };

      const validation = await service.validateRequest(mockRequest);

      expect(validation.isValid).toBe(true);
      expect(validation.fileData).toBeDefined();
      expect(validation.fileData?.type).toBe('base64');
    });

    it('should reject invalid base64 image', async () => {
      const invalidBase64 = 'invalid-base64-string';

      const mockRequest: IGatewayRequest = {
        path: '/v1/op/ocr/recognize',
        method: 'POST',
        headers: { 'content-type': 'application/json' },
        body: { 
          imageBase64: invalidBase64,
          type: 'logistics' 
        },
        clientInfo: {
          ip: '127.0.0.1',
          userAgent: 'test-agent',
        },
      };

      const validation = await service.validateRequest(mockRequest);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('无效的Base64图片格式');
    });
  });

  describe('determineProcessingMode', () => {
    it('should delegate to ProcessingModeService', () => {
      const mockContext = {
        request: {} as IGatewayRequest,
        routeConfig: {} as any,
        requestSize: 1024,
        estimatedProcessingTime: 5000,
      };

      processingModeService.determineMode.mockReturnValue(ProcessingMode.SYNC);

      const result = service.determineProcessingMode(mockContext);

      expect(result).toBe(ProcessingMode.SYNC);
      expect(processingModeService.determineMode).toHaveBeenCalledWith(mockContext);
    });
  });

  describe('getHealthStatus', () => {
    it('should return health status from proxy service', async () => {
      const mockHealthStatus = [
        {
          service: 'OCR_SERVICE',
          status: 'healthy' as const,
          responseTime: 100,
          lastCheck: new Date(),
        },
      ];

      proxyService.checkServiceHealth.mockResolvedValue(mockHealthStatus[0]);

      const result = await service.getHealthStatus();

      expect(result).toEqual(mockHealthStatus);
    });
  });
});
