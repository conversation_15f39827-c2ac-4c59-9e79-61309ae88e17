import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { ModuleRef } from '@nestjs/core';

/**
 * 基础处理器类
 * 所有队列处理器的基类，提供通用功能和错误处理
 */
export abstract class BaseProcessor {
  protected readonly logger: Logger;
  protected readonly moduleRef: ModuleRef;

  constructor(moduleRef: ModuleRef, processorName: string) {
    this.logger = new Logger(processorName);
    this.moduleRef = moduleRef;
  }

  /**
   * 处理任务的主方法
   * 子类需要实现此方法
   * @param job 任务对象
   */
  abstract process(job: Job): Promise<any>;

  /**
   * 安全地获取服务实例
   * 用于解决循环依赖问题，动态获取服务
   * @param serviceToken 服务的令牌（类或字符串）
   * @param fallbackFactory 如果服务不可用时的回退工厂函数
   * @returns 服务实例
   */
  protected async getServiceSafely<T>(
    serviceToken: any,
    fallbackFactory?: () => T
  ): Promise<T> {
    try {
      return await this.moduleRef.resolve(serviceToken, undefined, { strict: false });
    } catch (error) {
      this.logger.warn(
        `无法解析服务 ${serviceToken.name || serviceToken}，使用回退实现: ${error.message}`
      );
      
      if (fallbackFactory) {
        return fallbackFactory();
      }
      
      throw new Error(`无法获取服务 ${serviceToken.name || serviceToken}，且未提供回退实现`);
    }
  }

  /**
   * 处理任务进度更新
   * @param job 任务对象
   * @param progress 进度值（0-100）
   * @param message 可选的进度消息
   */
  protected async updateProgress(
    job: Job,
    progress: number,
    message?: string
  ): Promise<void> {
    try {
      await job.progress({
        progress: Math.min(100, Math.max(0, progress)),
        message: message || `处理进度: ${progress}%`,
        timestamp: Date.now(),
      });
    } catch (error) {
      this.logger.error(`更新任务进度失败: ${error.message}`);
    }
  }

  /**
   * 处理任务开始
   * @param job 任务对象
   */
  protected async handleJobStart(job: Job): Promise<void> {
    this.logger.log(`开始处理任务 ${job.id}, 类型: ${job.name}`);
    await this.updateProgress(job, 0, '任务开始处理');
  }

  /**
   * 处理任务成功完成
   * @param job 任务对象
   * @param result 处理结果
   */
  protected async handleJobSuccess(job: Job, result: any): Promise<any> {
    this.logger.log(`任务 ${job.id} 处理成功`);
    await this.updateProgress(job, 100, '任务处理完成');
    return result;
  }

  /**
   * 处理任务失败
   * @param job 任务对象
   * @param error 错误对象
   */
  protected async handleJobFailure(job: Job, error: Error): Promise<void> {
    this.logger.error(
      `任务 ${job.id} 处理失败: ${error.message}`,
      error.stack
    );
    
    try {
      await job.progress({
        progress: 100,
        status: 'failed',
        error: error.message,
        timestamp: Date.now(),
      });
    } catch (err) {
      this.logger.error(`更新任务失败状态时出错: ${err.message}`);
    }
    
    throw error; // 重新抛出错误，让Bull处理重试逻辑
  }
} 