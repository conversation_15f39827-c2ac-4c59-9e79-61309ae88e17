# 开发平台前端功能需求文档

## 1. 项目概述
本项目为开放平台的前端部分，基于 Vue3 + Element Plus + Pinia + TypeScript 实现，主要面向开发者用户，提供注册、登录、API密钥管理、调用记录、服务订购、订单支付等功能。所有数据均通过后端API获取，页面已实现，需与后端接口串联。

## 2. 用户体系
### 2.1 注册与登录
- 支持邮箱/手机号注册、登录
- 登录后获取用户信息、权限、API密钥等
- 登录态通过 Pinia 全局管理，支持自动续期
- 未登录用户仅可访问平台介绍、文档等公开路由

### 2.2 用户信息管理
- 支持查看和编辑个人信息
- 支持实名认证、企业认证等扩展

## 3. 平台介绍与文档
- 未登录用户可访问平台首页、服务介绍、API文档、隐私政策、服务条款等
- 首页展示平台能力、典型场景、接入流程、价格说明等
- 文档页面支持多语言切换、代码示例

## 4. API密钥管理
- 登录后可生成、查看、禁用、删除 API Key
- 支持为密钥设置名称、权限范围、IP白名单、有效期等
- 支持密钥状态（启用/禁用/过期）展示
- 支持密钥用量统计、最近调用时间等信息
- 密钥相关操作均通过后端 `/v1/api-key` 系列接口实现

## 5. 服务管理
- 展示平台所有可用API服务（如OCR、地址解析、地理编码等）
- 支持服务详情查看、价格/计费模式说明
- 支持服务订购、试用、续费等操作
- 服务数据通过 `/v1/service` 相关接口获取

## 6. 调用记录
- 展示用户所有API调用记录，支持按服务、时间、状态筛选
- 支持查看调用详情、错误信息、消耗额度等
- 支持导出调用记录
- 记录数据通过 `/v1/call-record` 相关接口获取

## 7. 订单与支付
- 支持服务订购、套餐购买、余额充值等
- 展示订单列表、订单详情、支付状态
- 支持多种支付方式（如支付宝、微信、余额等）
- 订单与支付数据通过 `/v1/order`、`/v1/payment` 相关接口获取

## 8. 队列与异步任务
- 支持异步任务（如大批量OCR、地址批量解析等）提交与进度跟踪
- 展示任务状态、结果、失败原因等
- 任务相关数据通过 `/v1/op/tasks` 相关接口获取

## 9. 其他功能
- 支持站内消息、公告、通知等
- 支持平台公告、服务变更、接口升级等信息展示
- 支持多端适配（PC/移动端）

## 10. 技术与规范
- 遵循项目 `documents/rules` 下的开发规范
- 组件分层、命名一致、接口统一、异常处理完善
- 严禁页面内写死测试数据，所有数据均通过API获取
- 代码风格、目录结构、命名规范等详见 `rules/global.md`、`rules/vue.md`

---

如需详细API对接说明，请参考后端接口文档和 `src/api` 目录下的接口实现。 