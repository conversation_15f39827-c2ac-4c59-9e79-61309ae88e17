import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';
import { ServiceStatus, ServiceType, PricingModel } from '../enums/service.enum';

/**
 * API服务实体
 */
@Entity('open_service', {
  comment: 'API服务表',
})
export class ServiceEntity extends BaseEntity {
  @Column({ 
    type: 'varchar', 
    length: 50, 
    unique: true,
    comment: '服务唯一代码' 
  })
  code: string;

  @Column({
    type: 'varchar',
    length: 100,
    comment: '服务名称',
  })
  name: string;

  @Column({
    type: 'enum',
    enum: ServiceType,
    comment: '服务类型：OCR、NLP、CV、AI、DATA、OTHER等',
  })
  type: ServiceType;

  @Column({
    type: 'enum',
    enum: ServiceStatus,
    default: ServiceStatus.DRAFT,
    comment: '服务状态枚举：draft、active、inactive、maintenance、deprecated等',
  })
  status: ServiceStatus;

  @Column({
    type: 'text',
    nullable: true,
    comment: '服务描述',
  })
  description?: string;

  @Column({
    type: 'enum',
    enum: PricingModel,
    default: PricingModel.PER_REQUEST,
    comment: '定价模式枚举：free、per_request、per_token、per_character、subscription等',
  })
  pricingModel: PricingModel;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    comment: '单价（元）'
  })
  unitPrice: number;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '服务端点URL',
  })
  endpoint?: string;

  @Column({
    type: 'int',
    default: 0,
    comment: '排序顺序'
  })
  sortOrder: number;
}
