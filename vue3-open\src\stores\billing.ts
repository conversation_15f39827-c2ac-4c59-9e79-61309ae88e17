import { defineStore } from 'pinia'
import { ref } from 'vue'
import { request } from '@/utils/request'
import { ElMessage } from 'element-plus'

export interface BillRecord {
  id: string
  date: string
  service: string
  amount: number
  usage: number
  status: 'paid' | 'pending' | 'failed'
  description: string
}

export interface AccountInfo {
  balance: number
  monthlySpending: number
  totalSpending: number
  currency: string
}

export interface UsageStats {
  totalCalls: number
  successCalls: number
  errorCalls: number
  totalCost: number
  avgResponseTime: number
  peakCalls: number
  peakTime: string
  costTrend: any[]
  ocr: {
    calls: number
    cost: number
  }
  nlp: {
    calls: number
    cost: number
  }
  aiGeneration: {
    calls: number
    cost: number
  }
}

export interface TopUpForm {
  amount: number
  paymentMethod: string
}

export const useBillingStore = defineStore('billing', () => {
  // 状态
  const accountInfo = ref<AccountInfo>({
    balance: 0,
    monthlySpending: 0,
    totalSpending: 0,
    currency: 'CNY'
  })
  
  const usageStats = ref<UsageStats>({
    totalCalls: 0,
    successCalls: 0,
    errorCalls: 0,
    totalCost: 0,
    avgResponseTime: 0,
    peakCalls: 0,
    peakTime: '',
    costTrend: [],
    ocr: {
      calls: 0,
      cost: 0
    },
    nlp: {
      calls: 0,
      cost: 0
    },
    aiGeneration: {
      calls: 0,
      cost: 0
    }
  })
  
  const callRecords = ref<any[]>([])
  
  const billRecords = ref<BillRecord[]>([])
  const loading = ref(false)
  const consumptionTrend = ref<any[]>([])

  // 获取账户信息
  const getAccountInfo = async () => {
    try {
      loading.value = true
      const response:Record<string,any> = await request.get('/op/billing/account')
      accountInfo.value = response as any
      return response
    } catch (error) {
      console.error('获取账户信息失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取使用统计
  const getUsageStats = async (params?: any) => {
    try {
      loading.value = true
      const response = await request.get('/op/billing/usage-stats', { params })
      usageStats.value = response as any
      return response
    } catch (error) {
      console.error('获取使用统计失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取账单记录
  const getBillRecords = async (params?: any) => {
    try {
      loading.value = true
      const response:Record<string,any> = await request.get('/op/billing/records', { params })
      billRecords.value = response.records || response
      return response
    } catch (error) {
      console.error('获取账单记录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取消费趋势
  const getConsumptionTrend = async (params?: any) => {
    try {
      loading.value = true
      const response:Record<string,any> = await request.get('/op/billing/consumption-trend', { params })
      consumptionTrend.value = response as any
      return response
    } catch (error) {
      console.error('获取消费趋势失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 账户充值
  const topUpAccount = async (form: TopUpForm) => {
    try {
      loading.value = true
      const response = await request.post('/op/billing/topup', form)
      ElMessage.success('充值成功')
      await getAccountInfo() // 刷新账户信息
      return response
    } catch (error) {
      console.error('充值失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 下载账单
  const downloadBill = async (billId: string) => {
    try {
      const response:any = await request.get(`/op/billing/download/${billId}`, {
        responseType: 'blob'
      })
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `bill_${billId}.pdf`)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      ElMessage.success('账单下载成功')
    } catch (error) {
      console.error('下载账单失败:', error)
      throw error
    }
  }

  // 批量下载账单
  const batchDownloadBills = async (billIds: string[]) => {
    try {
      loading.value = true
      // TODO: 实现批量下载账单
      await new Promise(resolve => setTimeout(resolve, 1000))
      ElMessage.success('批量下载成功')
    } catch (error) {
      console.error('批量下载失败:', error)
      throw error
    }
  }

  const getCallRecords = async (params?: any) => {
    try {
      loading.value = true
      // TODO: 实现获取调用记录
      await new Promise(resolve => setTimeout(resolve, 1000))
      callRecords.value = []
    } catch (error) {
      console.error('获取调用记录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const exportCallRecords = async (params?: any) => {
    try {
      loading.value = true
      // TODO: 实现导出调用记录
      await new Promise(resolve => setTimeout(resolve, 1000))
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出调用记录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    accountInfo,
    usageStats,
    callRecords,
    billRecords,
    loading,
    consumptionTrend,
    
    // 方法
    getAccountInfo,
    getUsageStats,
    getCallRecords,
    exportCallRecords,
    getBillRecords,
    getConsumptionTrend,
    topUpAccount,
    downloadBill,
    batchDownloadBills
  }
})