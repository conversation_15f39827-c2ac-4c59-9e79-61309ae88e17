import {
  Controller,
  Get,
  Post,
  Param,
  Query,
  Body,
  Req,
  Res,
  UseGuards,
  UseInterceptors,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { UseAuthStrategy, AuthStrategy } from '@/common/decorators/auth-strategy.decorator';
import { GatewayService } from '../services/gateway.service';
import { TaskResultService } from '../services/task-result.service';
import { RouteConfigService } from '../services/route-config.service';
import { 
  TaskQueryDto,
} from '../dto/gateway-request.dto';
import {
  GatewayResponseDto,
  TaskStatusResponseDto,
  ErrorResponseDto,
} from '../dto/gateway-response.dto';
import { IGatewayRequest } from '../interfaces/gateway.interfaces';
import { getClientIp } from '@/common/utils/ip.utils';

/**
 * 网关主控制器
 * 负责任务管理、健康检查和配置管理等通用功能
 * 遵循控制器职责，专注于HTTP请求处理
 */
@ApiTags('网关管理')
@Controller()
@UseAuthStrategy(AuthStrategy.API_KEY)
export class GatewayController {
  private readonly logger = new Logger(GatewayController.name);

  constructor(
    private readonly gatewayService: GatewayService,
    private readonly taskResultService: TaskResultService,
    private readonly routeConfigService: RouteConfigService,
  ) {}

  /**
   * 获取任务状态
   */
  @Get('tasks/:taskId')
  @ApiOperation({ summary: '获取任务状态' })
  @ApiResponse({ 
    status: 200, 
    description: '任务状态信息',
    type: TaskStatusResponseDto,
  })
  @ApiResponse({ 
    status: 404, 
    description: '任务不存在',
    type: ErrorResponseDto,
  })
  async getTaskStatus(@Param('taskId') taskId: string): Promise<TaskStatusResponseDto> {
    this.logger.log(`获取任务状态: ${taskId}`);

    const task = await this.taskResultService.getTaskStatus(taskId);
    
    return {
      success: true,
      code: 200,
      taskId: task.taskId,
      status: task.status,
      progress: task.progress,
      result: task.result,
      error: task.error,
      createdAt: task.createdAt.toISOString(),
      updatedAt: task.updatedAt?.toISOString(),
      processingTime: task.processingTime,
    };
  }

  /**
   * 获取任务事件流（SSE）
   * 注意：SSE接口不需要API Key认证，因为EventSource无法发送自定义头
   */
  @Get('tasks/:taskId/events')
  @UseAuthStrategy(AuthStrategy.NONE) // SSE接口不需要认证
  @ApiOperation({ summary: '获取任务事件流' })
  @ApiResponse({
    status: 200,
    description: 'Server-Sent Events流',
  })
  async getTaskEvents(
    @Param('taskId') taskId: string,
    @Res() res: Response
  ): Promise<void> {
    this.logger.log(`开始任务事件流: ${taskId}`);

    // 设置SSE响应头
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');

    // 发送初始连接事件
    res.write(`data: ${JSON.stringify({ type: 'connected', taskId })}\n\n`);

    // 定期检查任务状态
    const checkInterval = setInterval(async () => {
      try {
        this.logger.debug(`检查任务状态: ${taskId}`);
        const task = await this.taskResultService.getTaskStatus(taskId);
        this.logger.debug(`任务状态查询结果: ${JSON.stringify(task)}`);

        const eventData = {
          type: 'status',
          taskId: task.taskId,
          status: task.status,
          progress: task.progress,
          message: task.message,
          timestamp: new Date().toISOString(),
        };

        this.logger.debug(`发送SSE事件: ${JSON.stringify(eventData)}`);
        res.write(`data: ${JSON.stringify(eventData)}\n\n`);

        // 如果任务完成，发送结果并关闭连接
        if (task.status === 'completed' || task.status === 'failed' || task.status === 'timeout') {
          this.logger.log(`任务${taskId}已完成，状态: ${task.status}`);

          const finalData = {
            type: 'final',
            taskId: task.taskId,
            status: task.status,
            result: task.result,
            error: task.error,
            processingTime: task.processingTime,
            timestamp: new Date().toISOString(),
          };

          this.logger.debug(`发送最终SSE事件: ${JSON.stringify(finalData)}`);
          res.write(`data: ${JSON.stringify(finalData)}\n\n`);
          clearInterval(checkInterval);
          res.end();
          this.logger.log(`任务事件流结束: ${taskId}`);
        }

      } catch (error) {
        this.logger.error(`任务事件流错误: ${error.message}`, error.stack);

        const errorData = {
          type: 'error',
          taskId,
          error: error.message,
          timestamp: new Date().toISOString(),
        };

        res.write(`data: ${JSON.stringify(errorData)}\n\n`);
        clearInterval(checkInterval);
        res.end();
      }
    }, 1000); // 每秒检查一次

    // 处理客户端断开连接
    res.on('close', () => {
      this.logger.log(`客户端断开任务事件流: ${taskId}`);
      clearInterval(checkInterval);
    });
  }

  /**
   * 获取任务列表
   */
  @Get('tasks')
  @ApiOperation({ summary: '获取任务列表' })
  @ApiResponse({ 
    status: 200, 
    description: '任务列表',
  })
  async getTaskList(@Query() query: TaskQueryDto) {
    this.logger.log('获取任务列表');

    const { status, page = 1, limit = 20 } = query;
    const offset = (page - 1) * limit;

    const result = await this.taskResultService.getTaskList(
      status as any,
      limit,
      offset
    );

    return {
      success: true,
      code: 200,
      data: {
        tasks: result.tasks,
        pagination: {
          page,
          limit,
          total: result.total,
          pages: Math.ceil(result.total / limit),
        },
      },
    };
  }

  /**
   * 获取网关健康状态
   */
  @Get('health')
  @ApiOperation({ summary: '获取网关健康状态' })
  @ApiResponse({ 
    status: 200, 
    description: '健康状态信息',
  })
  async getHealthStatus() {
    this.logger.log('检查网关健康状态');

    const serviceHealth = await this.gatewayService.getHealthStatus();
    const taskStats = await this.taskResultService.getTaskStats();
    const configStats = await this.routeConfigService.getConfigStats();

    const overallHealth = serviceHealth.every(service => service.status === 'healthy');

    return {
      success: true,
      code: 200,
      data: {
        status: overallHealth ? 'healthy' : 'degraded',
        timestamp: new Date().toISOString(),
        services: serviceHealth,
        tasks: taskStats,
        routes: configStats,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
      },
    };
  }

  /**
   * 获取网关统计信息
   */
  @Get('stats')
  @ApiOperation({ summary: '获取网关统计信息' })
  @ApiResponse({ 
    status: 200, 
    description: '统计信息',
  })
  async getGatewayStats() {
    this.logger.log('获取网关统计信息');

    const taskStats = await this.taskResultService.getTaskStats();
    const configStats = await this.routeConfigService.getConfigStats();

    return {
      success: true,
      code: 200,
      data: {
        tasks: taskStats,
        routes: configStats,
        system: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          cpu: process.cpuUsage(),
          version: process.version,
        },
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * 清理过期任务
   */
  @Post('tasks/cleanup')
  @ApiOperation({ summary: '清理过期任务' })
  @ApiResponse({ 
    status: 200, 
    description: '清理结果',
  })
  async cleanupTasks() {
    this.logger.log('开始清理过期任务');

    const cleanedCount = await this.taskResultService.cleanupExpiredTasks();

    return {
      success: true,
      code: 200,
      message: `已清理 ${cleanedCount} 个过期任务`,
      data: { cleanedCount },
    };
  }

  /**
   * 获取路由配置
   */
  @Get('routes')
  @ApiOperation({ summary: '获取路由配置' })
  @ApiResponse({ 
    status: 200, 
    description: '路由配置列表',
  })
  async getRouteConfigs() {
    this.logger.log('获取路由配置');

    const configs = await this.routeConfigService.getAllRouteConfigs();

    return {
      success: true,
      code: 200,
      data: configs,
    };
  }

  /**
   * 更新路由配置
   */
  @Post('routes/:prefix/config')
  @ApiOperation({ summary: '更新路由配置' })
  @ApiResponse({ 
    status: 200, 
    description: '更新成功',
  })
  async updateRouteConfig(
    @Param('prefix') prefix: string,
    @Body() updates: any
  ) {
    this.logger.log(`更新路由配置: ${prefix}`);

    // URL解码前缀
    const decodedPrefix = decodeURIComponent(prefix);
    
    await this.routeConfigService.updateRouteConfig(decodedPrefix, updates);

    return {
      success: true,
      code: 200,
      message: '路由配置更新成功',
    };
  }

  /**
   * 重置路由配置
   */
  @Post('routes/reset')
  @ApiOperation({ summary: '重置为默认路由配置' })
  @ApiResponse({ 
    status: 200, 
    description: '重置成功',
  })
  async resetRouteConfigs() {
    this.logger.log('重置路由配置');

    await this.routeConfigService.resetToDefaultConfigs();

    return {
      success: true,
      code: 200,
      message: '路由配置已重置为默认值',
    };
  }

  /**
   * 导出路由配置
   */
  @Get('routes/export')
  @ApiOperation({ summary: '导出路由配置' })
  @ApiResponse({ 
    status: 200, 
    description: '配置文件',
  })
  async exportRouteConfigs(@Res() res: Response) {
    this.logger.log('导出路由配置');

    const configJson = await this.routeConfigService.exportConfigs();
    
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', 'attachment; filename="gateway-routes.json"');
    res.send(configJson);
  }

  /**
   * 导入路由配置
   */
  @Post('routes/import')
  @ApiOperation({ summary: '导入路由配置' })
  @ApiResponse({ 
    status: 200, 
    description: '导入成功',
  })
  async importRouteConfigs(@Body() body: { config: string }) {
    this.logger.log('导入路由配置');

    await this.routeConfigService.importConfigs(body.config);

    return {
      success: true,
      code: 200,
      message: '路由配置导入成功',
    };
  }
}
