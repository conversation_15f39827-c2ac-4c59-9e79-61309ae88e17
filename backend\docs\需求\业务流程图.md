# 开放平台业务流程图

## 0. 流程图可视化说明

本文档中的流程图使用 [Mermaid](https://mermaid-js.github.io/) 标记语言编写，可以在支持 Mermaid 的环境中自动渲染为可视化图形。

### 查看可视化流程图的方法

1. **支持 Mermaid 的 Markdown 查看器**：
   - VS Code + Markdown Preview Enhanced 插件
   - Typora 编辑器
   - GitLab/GitHub 的 Markdown 渲染

2. **在线 Mermaid 编辑器**：
   - [Mermaid Live Editor](https://mermaid-js.github.io/mermaid-live-editor/)
   - [Mermaid.ink](https://mermaid.ink/)

3. **导出静态图像**：
   - 使用 Mermaid CLI 工具导出为 PNG/SVG/PDF
   - 在线编辑器中导出图像

### 渲染示例

将文档中的 Mermaid 代码块复制到在线编辑器，即可获得如下效果：

![处理模式决策流程图示例](https://mermaid.ink/img/pako:eNp1kk1rwzAMhv-K8blb_QN2KIWxwWDssg67GGM4tnIw8Qcc2VMp-e-Tm-5jUHYSevVIPpScoyysJg45l0Y75VUBF9SJ-2BBgR69JSVXlRHKoCYHiXEQAdeRdV0HDCvjkJwdCUvfnEXRHUje2YN2o8leAoFFChrYX6bT-_kjzG5gGq1u_GCy3nsMKKY5W6b5KwxLIubXiRmzRLx0_4R4zLHEaOtmGEkvEQtRHGJ3UFZr69DTsnZCHWrlFBYq1caDxqMSHsrON4RlQrfS9q6rr4JckGbYOPF-HzWDptFW4eOsv-Z9GPx-EbLbL7bpLk13i_U-3We7ZJOl-81yl6a7zyGBlSs6X6GzZzqpg5LNqhzZBe9NUdupXzJX4Hvi5zFxbxVl79Yf5_MlVdl_qCv7I3UNtxv8i3w1zA7eMRFHHrnpmPPK2Q7J-QckFse9?type=png)

## 1. 用户完整旅程

以下流程图展示了用户从注册到使用服务的完整旅程。

```mermaid
graph TD
    Start([开始]) --> Register[用户注册]
    Register --> EmailVerify[邮箱验证]
    EmailVerify --> CreateApiKey[创建API密钥]
    CreateApiKey --> ViewServices[浏览服务目录]
    ViewServices --> SelectService[选择服务]
    
    SelectService --> CheckBalance{检查余额}
    CheckBalance -->|余额不足| RechargeAccount[账户充值]
    RechargeAccount --> PayRecharge[支付充值]
    PayRecharge --> CheckBalance
    
    CheckBalance -->|余额充足| BuyService[购买服务次数]
    BuyService --> UseService[调用服务API]
    
    UseService --> CheckQuota{检查剩余次数}
    CheckQuota -->|次数不足| BuyService
    CheckQuota -->|次数充足| ProcessRequest[处理请求]
    
    ProcessRequest --> GetResult[获取结果]
    GetResult --> ViewRecords[查看调用记录]
    ViewRecords --> UseService
    
    style Start fill:#f9f,stroke:#333,stroke-width:2px
    style Register fill:#bbf,stroke:#333,stroke-width:1px
    style BuyService fill:#bfb,stroke:#333,stroke-width:1px
    style UseService fill:#fbf,stroke:#333,stroke-width:1px
    style RechargeAccount fill:#fbb,stroke:#333,stroke-width:1px
```

## 2. 充值-购买-消费闭环

以下流程图展示了用户资金和服务次数的完整流转过程。

```mermaid
graph TD
    Start([用户]) --> Choose{选择操作}
    Choose -->|充值账户| CreateRechargeOrder[创建充值订单]
    Choose -->|直接购买服务次数| CreatePurchaseOrder[创建购买订单]
    
    CreateRechargeOrder --> SelectPayMethod1[选择支付方式]
    CreatePurchaseOrder --> SelectPayMethod2[选择支付方式]
    
    SelectPayMethod1 -->|第三方支付| ThirdPartyPay1[支付宝/微信支付]
    SelectPayMethod2 -->|第三方支付| ThirdPartyPay2[支付宝/微信支付]
    SelectPayMethod2 -->|余额支付| BalancePay[余额支付]
    
    ThirdPartyPay1 --> PaymentCallback1[支付回调]
    ThirdPartyPay2 --> PaymentCallback2[支付回调]
    
    PaymentCallback1 -->|支付成功| IncreaseBalance[增加账户余额]
    PaymentCallback2 -->|支付成功| UpdateOrder2[更新订单状态]
    BalancePay -->|余额充足| DeductBalance[扣减账户余额]
    BalancePay -->|余额不足| PaymentFailed[支付失败]
    
    DeductBalance --> UpdateOrder2
    UpdateOrder2 --> IncreaseServiceCount[增加服务调用次数]
    
    IncreaseBalance --> UserBalance[(用户余额)]
    UserBalance --> DeductBalance
    
    IncreaseServiceCount --> ServiceQuota[(服务次数)]
    
    ServiceQuota --> CallService[调用服务]
    CallService -->|调用成功| DeductServiceCount[扣减服务次数]
    CallService -->|调用失败| RecordFailure[记录失败]
    
    DeductServiceCount --> ServiceQuota
    DeductServiceCount --> RecordSuccess[记录成功调用]
    
    RecordSuccess --> CallRecords[(调用记录)]
    RecordFailure --> CallRecords
    
    style Start fill:#f9f,stroke:#333,stroke-width:2px
    style UserBalance fill:#bbf,stroke:#333,stroke-width:1px
    style ServiceQuota fill:#bfb,stroke:#333,stroke-width:1px
    style CallRecords fill:#fbf,stroke:#333,stroke-width:1px
```

## 3. 总体业务流程

以下流程图展示了开放平台的核心请求处理流程，包括认证、路由、处理和结果返回等环节。

```mermaid
graph TD
    Client([客户端]) -->|API请求| Auth{UnifiedAuthGuard}
    Auth -->|密钥无效| Error401[返回401错误]
    Auth -->|密钥有效| Gateway[网关模块]
    
    Gateway -->|路由匹配| DecideMode{处理模式决策}
    DecideMode -->|同步模式| SyncProcess[同步处理]
    DecideMode -->|异步模式| AsyncProcess[异步处理]
    
    %% 同步处理流程
    SyncProcess -->|代理请求| BizModule[业务模块]
    BizModule -->|执行业务逻辑| BizProcess[具体业务执行]
    BizProcess --> BizResult[业务结果]
    BizResult -->|返回结果| Client
    
    %% 异步处理 - 客户端自管理模式
    AsyncProcess -->|客户端自管理模式| DirectMode[生成任务ID]
    DirectMode -->|添加到队列| Queue[(Redis队列)]
    DirectMode -->|返回任务ID和SSE地址| Client
    Client -->|SSE连接| TaskEvents[任务事件API]
    Queue --> QueueProcessor[队列处理器]
    QueueProcessor -->|任务完成| TaskEvents
    TaskEvents -->|推送结果| Client
    
    %% 异步处理 - 网关代理模式
    AsyncProcess -->|网关代理模式| ProxyMode[生成任务ID]
    ProxyMode -->|添加到队列| Queue
    ProxyMode -->|内部订阅结果| WaitResult[网关等待结果]
    WaitResult -->|轮询/订阅| QueueProcessor
    QueueProcessor -->|结果返回| WaitResult
    WaitResult -->|任务结果+ID| Client
    
    %% 子系统关系
    subgraph "认证层"
        Auth
    end
    
    subgraph "网关层"
        Gateway
        DecideMode
        SyncProcess
        AsyncProcess
        DirectMode
        ProxyMode
        WaitResult
    end
    
    subgraph "队列系统"
        Queue
        QueueProcessor
        TaskEvents
    end
    
    subgraph "业务服务"
        BizModule
        BizProcess
        BizResult
    end
```

## 4. 处理模式决策流程

以下流程图展示了网关如何决定请求的处理模式（同步/异步/代理异步）。

```mermaid
graph TD
    Start([请求进入]) --> CheckMode{请求指定处理模式?}
    CheckMode -->|是| ValidMode{模式有效?}
    CheckMode -->|否| CheckSize{检查请求大小}
    
    ValidMode -->|是| UseSpecified[使用指定模式]
    ValidMode -->|否| UseDefault[使用默认模式]
    
    CheckSize -->|小请求| EstimateTime{预估处理时间}
    CheckSize -->|大请求| CheckSystem{系统负载检查}
    
    EstimateTime -->|处理时间短| SyncMode[同步模式]
    EstimateTime -->|处理时间长| CheckSystem
    
    CheckSystem -->|负载低| ProxyAsyncMode[网关代理异步模式]
    CheckSystem -->|负载高| DirectAsyncMode[客户端自管理异步模式]
    
    UseSpecified --> Return([返回决策结果])
    UseDefault --> Return
    SyncMode --> Return
    ProxyAsyncMode --> Return
    DirectAsyncMode --> Return
```

## 5. 认证与密钥验证流程

以下流程图展示了API密钥的验证过程。

```mermaid
graph TD
    Start([API请求]) --> ExtractKeys[提取API Key和Secret Key]
    ExtractKeys --> CheckCache{检查缓存}
    
    CheckCache -->|命中缓存| ValidateKey{验证密钥}
    CheckCache -->|缓存未命中| QueryDB[查询数据库]
    
    QueryDB -->|找到密钥| UpdateCache[更新缓存]
    QueryDB -->|未找到密钥| Error401[返回401错误]
    
    UpdateCache --> ValidateKey
    
    ValidateKey -->|密钥有效| CheckStatus{检查密钥状态}
    ValidateKey -->|密钥无效| Error401
    
    CheckStatus -->|状态正常| CheckScope{检查权限范围}
    CheckStatus -->|状态异常| Error401
    
    CheckScope -->|权限足够| Success[认证成功]
    CheckScope -->|权限不足| Error403[返回403错误]
    
    Success --> RecordUsage[记录使用情况]
    RecordUsage --> NextStep([继续处理请求])
```

## 6. 队列处理与任务状态流程

以下流程图展示了队列如何处理任务并通过SSE推送更新。

```mermaid
graph TD
    Start([任务入队]) --> CreateTask[创建任务记录]
    CreateTask --> AddQueue[添加到Redis队列]
    
    AddQueue -->|任务等待中| TaskQueued[任务状态:排队中]
    TaskQueued --> Processor{队列处理器}
    
    Processor -->|开始处理| TaskActive[任务状态:处理中]
    TaskActive --> BizExec[执行业务逻辑]
    
    BizExec -->|处理进行中| UpdateProgress[更新进度]
    UpdateProgress -->|发布更新事件| PubSub[(Redis PubSub)]
    PubSub -->|推送更新| SSE[SSE连接]
    SSE -->|发送进度| Client([客户端])
    UpdateProgress --> BizExec
    
    BizExec -->|处理完成| TaskCompleted[任务状态:已完成]
    BizExec -->|处理失败| TaskFailed[任务状态:失败]
    
    TaskCompleted -->|发布完成事件| PubSub
    TaskFailed -->|发布失败事件| PubSub
    
    PubSub -->|推送结果| SSEClose[关闭SSE连接]
    SSEClose --> ClientComplete([客户端处理结果])
```

## 7. 异步处理详细流程

### 7.1 客户端自管理模式

以下序列图展示了客户端自管理异步模式的详细流程。

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as 网关模块
    participant Auth as 认证模块
    participant CallMgmt as 调用管理模块
    participant Queue as 队列模块
    participant Service as 业务服务
    
    Client->>Gateway: 发送请求(mode=async)
    Gateway->>Auth: 验证API密钥
    Auth-->>Gateway: 返回验证结果
    
    Gateway->>CallMgmt: 检查调用次数
    CallMgmt-->>Gateway: 返回次数检查结果
    
    Gateway->>Queue: 创建任务并入队
    Queue-->>Gateway: 返回任务ID
    
    Gateway->>CallMgmt: 记录请求(不扣减次数)
    Gateway-->>Client: 返回任务ID和SSE URL
    
    Client->>Queue: 建立SSE连接(taskId)
    Queue->>Queue: 任务排队中
    Queue-->>Client: 推送排队状态
    
    Queue->>Service: 开始处理任务
    Queue-->>Client: 推送处理中状态
    
    Service->>Service: 执行业务逻辑
    Service-->>Queue: 返回中间进度
    Queue-->>Client: 推送进度更新
    
    Service-->>Queue: 返回处理结果
    Queue->>CallMgmt: 记录结果并扣减次数
    Queue-->>Client: 推送最终结果
    Queue-->>Client: 关闭SSE连接
```

### 7.2 网关代理模式

以下序列图展示了网关代理异步模式的详细流程。

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as 网关模块
    participant Auth as 认证模块
    participant CallMgmt as 调用管理模块
    participant Queue as 队列模块
    participant Service as 业务服务
    
    Client->>Gateway: 发送请求(mode=proxy-async)
    Gateway->>Auth: 验证API密钥
    Auth-->>Gateway: 返回验证结果
    
    Gateway->>CallMgmt: 检查调用次数
    CallMgmt-->>Gateway: 返回次数检查结果
    
    Gateway->>Queue: 创建任务并入队
    Queue-->>Gateway: 返回任务ID
    
    Gateway->>Gateway: 建立内部SSE连接(taskId)
    Queue->>Queue: 任务排队中
    Queue-->>Gateway: 推送排队状态
    
    Queue->>Service: 开始处理任务
    Queue-->>Gateway: 推送处理中状态
    
    Service->>Service: 执行业务逻辑
    Service-->>Queue: 返回中间进度
    Queue-->>Gateway: 推送进度更新
    
    Service-->>Queue: 返回处理结果
    Queue-->>Gateway: 推送最终结果
    
    Gateway->>CallMgmt: 记录调用并扣减次数
    Gateway-->>Client: 返回结果和任务ID
```

## 8. 错误处理流程

以下流程图展示了系统中各种错误的处理流程。

```mermaid
graph TD
    Start([错误发生]) --> Classify{错误类型分类}
    
    Classify -->|认证错误| AuthError[认证错误处理]
    Classify -->|业务错误| BizError[业务错误处理]
    Classify -->|系统错误| SysError[系统错误处理]
    Classify -->|网络错误| NetError[网络错误处理]
    
    AuthError --> AuthActions{认证错误类型}
    AuthActions -->|密钥无效| Return401[返回401错误]
    AuthActions -->|权限不足| Return403[返回403错误]
    AuthActions -->|次数不足| Return402[返回402错误]
    
    BizError --> BizActions{业务错误类型}
    BizActions -->|参数错误| Return400[返回400错误]
    BizActions -->|资源不存在| Return404[返回404错误]
    BizActions -->|业务规则冲突| Return409[返回409错误]
    
    SysError --> SysActions{系统错误类型}
    SysActions -->|内部错误| Return500[返回500错误]
    SysActions -->|依赖服务错误| Return502[返回502错误]
    SysActions -->|服务不可用| Return503[返回503错误]
    
    NetError --> NetActions{网络错误类型}
    NetActions -->|请求超时| Return504[返回504错误]
    NetActions -->|连接断开| RetryConnect[重试连接]
    
    Return401 --> LogError[记录错误日志]
    Return402 --> LogError
    Return403 --> LogError
    Return400 --> LogError
    Return404 --> LogError
    Return409 --> LogError
    
    Return500 --> AlertAdmin[告警管理员]
    Return502 --> AlertAdmin
    Return503 --> AlertAdmin
    Return504 --> AlertAdmin
    
    AlertAdmin --> LogError
    RetryConnect -->|重试成功| ContinueProcess[继续处理]
    RetryConnect -->|重试失败| Return504
    
    LogError --> ReturnToClient[返回错误响应给客户端]
    ContinueProcess --> NextStep[继续执行流程]
    
    subgraph "客户端错误 (4xx)"
        Return400
        Return401
        Return402
        Return403
        Return404
        Return409
    end
    
    subgraph "服务器错误 (5xx)"
        Return500
        Return502
        Return503
        Return504
    end
```

### 8.1 异步任务错误处理

以下流程图展示了异步任务执行过程中的错误处理流程。

```mermaid
graph TD
    Start([任务执行错误]) --> Classify{错误类型}
    
    Classify -->|临时错误| TempError[临时错误处理]
    Classify -->|永久错误| PermError[永久错误处理]
    
    TempError --> RetryCheck{重试次数检查}
    RetryCheck -->|未超过重试上限| ScheduleRetry[安排重试]
    RetryCheck -->|超过重试上限| MarkFailed1[标记任务失败]
    
    PermError --> MarkFailed2[标记任务失败]
    
    ScheduleRetry --> Backoff[计算退避时间]
    Backoff --> DelayRetry[延迟重试]
    DelayRetry --> RequeueTask[重新入队]
    
    MarkFailed1 --> NotifyError[通知错误]
    MarkFailed2 --> NotifyError
    
    NotifyError --> UpdateStatus[更新任务状态]
    UpdateStatus --> PublishEvent[发布任务失败事件]
    PublishEvent --> SaveErrorDetails[保存错误详情]
    
    RequeueTask --> IncrRetryCount[增加重试计数]
    IncrRetryCount --> LogRetry[记录重试日志]
    
    SaveErrorDetails --> End([结束])
    LogRetry --> End
```

## 9. 系统组件关系

```mermaid
graph TD
    Client([客户端]) --> AuthGuard[全局认证守卫]
    AuthGuard --> ApiKeyCacheService[密钥缓存服务]
    ApiKeyCacheService --> ApiKeyService[密钥服务]
    
    AuthGuard -->|认证通过| GatewayController[网关控制器]
    GatewayController --> GatewayService[网关服务]
    GatewayService --> ProxyService[代理服务]
    GatewayService --> QueueManager[队列管理服务]
    
    ProxyService --> BusinessServices[业务服务]
    QueueManager --> RedisQueue[(Redis队列)]
    
    RedisQueue --> TaskProcessor[任务处理器]
    TaskProcessor --> ExecutorServices[执行器服务]
    ExecutorServices --> BusinessServices
    
    TaskProcessor --> TaskService[任务服务]
    TaskService --> RedisPubSub[(Redis PubSub)]
    
    Client -->|SSE连接| TaskEventsController[任务事件控制器]
    TaskEventsController --> RedisPubSub
    
    GatewayService -->|网关代理模式| TaskService
    
    Client --> OrderController[订单控制器]
    OrderController --> OrderService[订单服务]
    OrderService --> PaymentService[支付服务]
    PaymentService --> ThirdPartyPayment[第三方支付]
    
    OrderService --> UserServiceManager[用户服务管理器]
    UserServiceManager --> CallRecordService[调用记录服务]
```

## 10. 如何修改和导出流程图

要更新或修改这些流程图，可以：

1. 编辑文档中的 Mermaid 代码块
2. 使用 Mermaid Live Editor 预览和调整图表
3. 使用以下命令导出为静态图像：

```bash
# 安装 Mermaid CLI
npm install -g @mermaid-js/mermaid-cli

# 导出为 PNG
mmdc -i input.mmd -o output.png

# 导出为 SVG
mmdc -i input.mmd -o output.svg -t dark
```

在团队协作中，可以考虑将关键流程图导出为静态图像，并存储在项目的 `docs/images` 目录中，方便没有 Mermaid 渲染环境的团队成员查看。
