import { Injectable, Logger, NotFoundException, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, LessThan } from 'typeorm';
import { CallRecordEntity } from '../entities/call-record.entity';
import { UserServiceEntity } from '../../user-service/entities/user-service.entity';
import { AuthUser, AuthApiKey } from '../../../common/types/auth.types';

/**
 * API使用跟踪服务
 * 负责创建API调用记录并扣减使用次数
 */
@Injectable()
export class ApiUsageTrackerService {
  private readonly logger = new Logger(ApiUsageTrackerService.name);
  private readonly ALERT_THRESHOLD = 0.8; // 80%预警阈值

  constructor(
    @InjectRepository(CallRecordEntity)
    private readonly callRecordRepository: Repository<CallRecordEntity>,
    @InjectRepository(UserServiceEntity)
    private readonly userServiceRepository: Repository<UserServiceEntity>,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * 记录API调用
   * 简化版的API调用记录，主要用于处理器和网关调用
   * @param params 调用参数
   * @returns 调用记录ID
   */
  async trackApiCall(params: {
    userId: number;
    apiKeyId: string;
    serviceId: number;
    endpoint: string;
    responseTime?: number;
    status: 'completed' | 'queued' | 'failed';
    requestId?: string;
    jobId?: string;
    error?: string;
  }): Promise<string> {
    try {
      const callRecord = new CallRecordEntity();
      callRecord.requestId = params.requestId || `req_${Date.now()}`;
      callRecord.user = { id: params.userId } as any;
      callRecord.service = { id: params.serviceId } as any;
      callRecord.apiKey = String(params.apiKeyId); // 确保apiKeyId是字符串
      callRecord.endpoint = params.endpoint;
      callRecord.responseTime = params.responseTime || 0;
      callRecord.success = params.status !== 'failed';
      callRecord.cost = 1; // 默认消耗1次
      callRecord.amount = 0.00; // 添加amount字段的默认值为0.00
      
      // 设置输入/输出
      const inputOutput: any = {
        status: params.status
      };
      
      if (params.jobId) {
        inputOutput.jobId = params.jobId;
      }
      
      if (params.error) {
        inputOutput.error = params.error;
      }
      
      callRecord.input = JSON.stringify(inputOutput);
      callRecord.output = JSON.stringify(inputOutput);
      
      // 如果状态是已完成，则扣减使用次数
      if (params.status === 'completed') {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        
        try {
          // 保存调用记录
          const savedRecord = await queryRunner.manager.save(callRecord);
          
          // 扣减使用次数
          const userService = await queryRunner.manager.findOne(UserServiceEntity, {
            where: { 
              user: { id: params.userId }, 
              service: { id: params.serviceId } 
            },
            lock: { mode: 'pessimistic_write' }
          });
          
          if (userService) {
            // 扣减使用次数
            userService.usedCount += callRecord.cost;
            await queryRunner.manager.save(userService);
            
            // 检查是否需要发送预警
            await this.checkUsageAlertThreshold(userService, queryRunner);
          } else {
            this.logger.warn(`用户(${params.userId})没有服务(${params.serviceId})的使用权限记录`);
          }
          
          await queryRunner.commitTransaction();
          return String(savedRecord.id);
        } catch (error) {
          await queryRunner.rollbackTransaction();
          this.logger.error(`扣减使用次数失败: ${error.message}`, error.stack);
          throw error;
        } finally {
          await queryRunner.release();
        }
      } else {
        // 如果不是已完成状态，只保存记录不扣减次数
        const savedRecord = await this.callRecordRepository.save(callRecord);
        return String(savedRecord.id);
      }
    } catch (error) {
      this.logger.error(`记录API调用失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 记录API调用并扣减使用次数
   * @param requestId 请求ID
   * @param userId 用户ID
   * @param serviceId 服务ID
   * @param input 输入数据
   * @param output 输出数据
   * @param success 是否成功
   * @param cost 消耗的调用次数
   * @param transactionCallback 可选的事务回调（用于外部事务）
   */
  async trackApiUsage(
    requestId: string,
    userId: number,
    serviceId: number,
    input: any,
    output: any,
    success: boolean,
    cost: number = 1,
    transactionCallback?: (queryRunner: any) => Promise<void>
  ): Promise<boolean> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1. 创建API调用记录
      const callRecord = new CallRecordEntity();
      callRecord.requestId = requestId;
      callRecord.input = typeof input === 'string' ? input : JSON.stringify(input);
      callRecord.output = typeof output === 'string' ? output.substring(0, 65000) : JSON.stringify(output).substring(0, 65000);
      callRecord.success = success;
      callRecord.cost = cost;
      callRecord.amount = 0; // 此处可以根据实际业务添加金额计算
      callRecord.user = { id: userId } as any;
      callRecord.service = { id: serviceId } as any;

      // 保存API调用记录
      await queryRunner.manager.save(callRecord);
      
      // 2. 扣减用户服务使用次数
      // 获取用户服务关联
      const userService = await queryRunner.manager.findOne(UserServiceEntity, {
        where: { 
          user: { id: userId }, 
          service: { id: serviceId } 
        },
        lock: { mode: 'pessimistic_write' }
      });

      if (userService) {
        // 扣减使用次数
        userService.usedCount += cost;
        await queryRunner.manager.save(userService);
        this.logger.log(`成功扣减用户(${userId})服务(${serviceId})使用次数: ${cost}`);
        
        // 在扣减后检查是否需要发送预警
        await this.checkUsageAlertThreshold(userService, queryRunner);
      } else {
        this.logger.warn(`用户(${userId})没有服务(${serviceId})的使用权限记录`);
      }
      
      // 执行额外的事务操作（如果有）
      if (transactionCallback) {
        await transactionCallback(queryRunner);
      }

      await queryRunner.commitTransaction();
      this.logger.log(`成功创建API调用记录: ${requestId}`);
      return true;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`创建API调用记录失败: ${error.message}`, error.stack);
      return false;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 记录异步任务的API调用（任务开始时）
   * @param requestId 请求ID
   * @param userId 用户ID
   * @param serviceId 服务ID
   * @param jobId 任务ID
   * @param input 输入数据
   * @param cost 消耗的调用次数
   */
  async trackAsyncTaskStart(
    requestId: string,
    userId: number,
    serviceId: number,
    jobId: string,
    input: any,
    cost: number = 1
  ): Promise<boolean> {
    return await this.trackApiUsage(
      requestId,
      userId,
      serviceId,
      input,
      { status: 'queued', jobId },
      true,
      cost
    );
  }

  /**
   * 记录异步任务的API调用（任务完成时）
   * @param jobId 任务ID
   * @param serviceId 服务ID
   * @param output 输出数据
   * @param success 是否成功
   */
  async trackAsyncTaskComplete(
    jobId: string,
    serviceId: number,
    output: any,
    success: boolean
  ): Promise<boolean> {
    // 查找相关的调用记录
    const callRecord = await this.callRecordRepository.findOne({
      where: { output: { jobId } } as any, // 简化，实际应该使用更精确的查询
    });

    if (!callRecord) {
      this.logger.warn(`未找到任务对应的API调用记录: ${jobId}`);
      return false;
    }

    // 更新调用记录
    callRecord.output = typeof output === 'string' ? output.substring(0, 65000) : JSON.stringify(output).substring(0, 65000);
    callRecord.success = success;
    
    try {
      await this.callRecordRepository.save(callRecord);
      this.logger.log(`成功更新异步任务API调用记录: ${jobId}`);
      return true;
    } catch (error) {
      this.logger.error(`更新异步任务API调用记录失败: ${error.message}`, error.stack);
      return false;
    }
  }
  
  /**
   * 检查并发送用户服务使用量预警
   * 当用户服务使用量超过阈值时发送预警
   * @param userId 用户ID
   * @param serviceId 服务ID
   */
  async checkAndSendAlert(userId: number, serviceId: number): Promise<void> {
    const userService = await this.userServiceRepository.findOne({
      where: { 
        user: { id: userId }, 
        service: { id: serviceId } 
      },
      relations: ['user', 'service']
    });
    
    if (!userService) {
      throw new NotFoundException('用户服务关联不存在');
    }
    
    await this.checkUsageAlertThreshold(userService);
  }
  
  /**
   * 检查用户服务使用量是否超过预警阈值
   * @param userService 用户服务实体
   * @param queryRunner 可选事务查询运行器
   */
  private async checkUsageAlertThreshold(
    userService: UserServiceEntity, 
    queryRunner?: any
  ): Promise<void> {
    if (!userService.totalCount) return;
    
    // 计算已使用比例
    const usedRatio = userService.usedCount / userService.totalCount;
    
    // 如果已用超过阈值(80%)且尚未发送预警
    if (usedRatio >= this.ALERT_THRESHOLD && !userService.alertSent) {
      // 更新预警状态
      userService.alertSent = true;
      
      if (queryRunner) {
        await queryRunner.manager.save(userService);
      } else {
        await this.userServiceRepository.save(userService);
      }
      
      // 发送预警
      await this.sendUsageAlert(userService);
    }
  }
  
  /**
   * 发送使用量预警
   * @param userService 用户服务实体
   */
  private async sendUsageAlert(userService: UserServiceEntity): Promise<void> {
    try {
      // 这里实现实际的预警发送逻辑
      // 可以是发送邮件、短信、站内信等
      
      const userId = userService.user?.id;
      const serviceId = userService.service?.id;
      const serviceName = userService.service?.name || '未知服务';
      const usedCount = userService.usedCount;
      const totalCount = userService.totalCount;
      const remainingCount = totalCount - usedCount;
      const usedPercentage = Math.round((usedCount / totalCount) * 100);
      
      this.logger.log(`向用户 #${userId} 发送服务 #${serviceId} (${serviceName}) 使用量预警: ${usedCount}/${totalCount} (${usedPercentage}%)`);
      
      // TODO: 调用实际的通知服务发送预警
      // await this.notificationService.sendAlert({
      //   userId,
      //   serviceId,
      //   subject: `服务使用量预警 - ${serviceName}`,
      //   content: `您的服务"${serviceName}"使用量已达到${usedPercentage}%，剩余可用次数: ${remainingCount}`
      // });
    } catch (error) {
      this.logger.error(`发送使用量预警失败: ${error.message}`, error.stack);
      // 失败不抛出异常，避免阻止主流程
    }
  }
  
  /**
   * 重置预警状态(当用户购买新次数后调用)
   * @param userId 用户ID
   * @param serviceId 服务ID
   */
  async resetAlertStatus(userId: number, serviceId: number): Promise<void> {
    const userService = await this.userServiceRepository.findOne({
      where: { 
        user: { id: userId }, 
        service: { id: serviceId } 
      }
    });
    
    if (!userService) {
      throw new NotFoundException('用户服务关联不存在');
    }
    
    userService.alertSent = false;
    await this.userServiceRepository.save(userService);
    this.logger.log(`重置用户 #${userId} 服务 #${serviceId} 的预警状态成功`);
  }
  
  /**
   * 重置每日免费使用次数
   * 定时任务，每天凌晨执行
   */
  async resetDailyFreeQuota(): Promise<void> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    try {
      // 查找需要重置的记录，这里需要联表查询用户是否有免费额度资格
      const queryBuilder = this.userServiceRepository.createQueryBuilder('userService')
        .innerJoin('userService.user', 'user')
        .where('userService.lastResetDate < :today', { today })
        .andWhere('user.isFreeQuotaEligible = :isEligible', { isEligible: true });
        
      const userServices = await queryBuilder.getMany();
      
      if (userServices.length === 0) {
        this.logger.log('没有需要重置的每日免费额度');
        return;
      }
      
      this.logger.log(`找到 ${userServices.length} 条记录需要重置每日免费额度`);
      
      // 批量更新
      for (const userService of userServices) {
        userService.freeUsedToday = 0;
        userService.lastResetDate = new Date();
        await this.userServiceRepository.save(userService);
      }
      
      this.logger.log(`成功重置 ${userServices.length} 条记录的每日免费额度`);
    } catch (error) {
      this.logger.error(`重置每日免费额度失败: ${error.message}`, error.stack);
      throw error;
    }
  }
  
  /**
   * 批量检查所有用户服务的使用量阈值
   * 定时任务，每小时执行一次
   */
  async checkAllUsageAlerts(): Promise<void> {
    try {
      // 查找可能需要预警的记录
      const queryBuilder = this.userServiceRepository.createQueryBuilder('userService')
        .leftJoinAndSelect('userService.user', 'user')
        .leftJoinAndSelect('userService.service', 'service')
        .where('userService.alertSent = :alertSent', { alertSent: false })
        .andWhere('userService.totalCount > 0')
        .andWhere(`(userService.usedCount * 1.0 / userService.totalCount) >= :threshold`, { threshold: this.ALERT_THRESHOLD });
      
      const needAlertServices = await queryBuilder.getMany();
      
      if (needAlertServices.length === 0) {
        this.logger.log('没有需要发送预警的服务');
        return;
      }
      
      this.logger.log(`找到 ${needAlertServices.length} 条记录需要发送使用量预警`);
      
      // 发送预警
      for (const userService of needAlertServices) {
        await this.sendUsageAlert(userService);
        userService.alertSent = true;
        await this.userServiceRepository.save(userService);
      }
      
      this.logger.log(`成功发送 ${needAlertServices.length} 条使用量预警`);
    } catch (error) {
      this.logger.error(`批量检查使用量预警失败: ${error.message}`, error.stack);
    }
  }
} 