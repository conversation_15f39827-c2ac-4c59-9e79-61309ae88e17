# 队列模块集成总结

## 改进内容

我们对队列模块进行了全面改进，使其能够支持物流OCR识别、地址提取和地理坐标逆解析的异步调用需求。主要改进包括：

### 1. 队列定义扩展

- 启用了三个专用队列：
  - `OCR`: 用于面单OCR识别
  - `EXTRACT_ADDRESS`: 用于提取地址
  - `REV_GEO`: 用于坐标逆解析地址

- 添加了对应的任务类型：
  - `OCR_RECOGNITION`: OCR识别任务
  - `ADDRESS_EXTRACTION`: 地址提取任务
  - `REVERSE_GEOCODING`: 地理坐标逆解析任务

### 2. 处理器实现

创建了三个专用任务处理器：

- **OcrProcessor**: 处理OCR识别任务
  - 支持Base64图片处理
  - 集成现有OCR服务
  - 支持回调通知

- **AddressExtractionProcessor**: 处理地址提取任务
  - 支持单文本和多文本处理
  - 集成NLP服务
  - 支持回调通知

- **ReverseGeocodingProcessor**: 处理地理坐标逆解析任务
  - 支持单点和批量坐标处理
  - 集成地理编码服务
  - 支持回调通知

### 3. 队列服务扩展

扩展了BullQueueService，添加了新的队列服务方法：

- `addOcrTask`: 添加OCR识别任务
- `addAddressExtractionTask`: 添加地址提取任务
- `addReverseGeocodingTask`: 添加地理坐标逆解析任务

### 4. 队列管理器扩展

扩展了QueueManagerService，添加了新的任务管理方法：

- `addOcrTask`: 添加OCR识别任务
- `addAddressExtractionTask`: 添加地址提取任务
- `addReverseGeocodingTask`: 添加地理坐标逆解析任务
- `addBatchOcrTasks`: 批量添加OCR识别任务
- `addBatchAddressExtractionTasks`: 批量添加地址提取任务
- `addBatchReverseGeocodingTasks`: 批量添加地理坐标逆解析任务

### 5. 业务模块集成

#### OCR模块集成

- 添加了异步处理方法：
  - `processOcrRequestAsync`: 异步处理OCR识别请求（文件上传方式）
  - `processOcrRequestFromBase64Async`: 异步处理OCR识别请求（Base64方式）
  - `getOcrTaskStatus`: 获取OCR任务状态

#### 地址模块集成

- 添加了异步处理方法：
  - `extractFromTextAsync`: 异步从单个文本中提取地址信息
  - `extractFromMultipleTextsAsync`: 异步从多个文本中提取地址信息
  - `getAddressExtractionTaskStatus`: 获取地址提取任务状态
  - `reverseGeocodeAsync`: 异步地理坐标逆解析
  - `batchReverseGeocodeAsync`: 异步批量地理坐标逆解析
  - `getReverseGeocodeTaskStatus`: 获取地理坐标逆解析任务状态

## 技术实现

### 队列配置

为每个队列设置了合适的配置：

```typescript
{
  name: QUEUE_NAMES.OCR,
  defaultJobOptions: {
    removeOnComplete: 100,
    removeOnFail: 50,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
  },
}
```

### 任务数据接口

定义了专用的任务数据接口：

```typescript
export interface OcrTaskData extends BaseTask {
  type: 'ocr_recognition';
  imageData: string; // Base64编码的图片数据
  filename?: string;
  callbackUrl?: string; // 回调URL，用于通知任务完成
}

export interface AddressExtractionTaskData extends BaseTask {
  type: 'address_extraction';
  text: string;
  mode?: 'single' | 'multiple';
  callbackUrl?: string;
}

export interface ReverseGeocodingTaskData extends BaseTask {
  type: 'reverse_geocoding';
  coordinates: Array<{
    lat: number;
    lng: number;
    id?: string;
  }>;
  callbackUrl?: string;
}
```

### 回调机制

实现了统一的回调通知机制：

```typescript
private async sendCallback(url: string, data: any): Promise<void> {
  try {
    await firstValueFrom(
      this.httpService.post(url, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      })
    );
  } catch (error) {
    this.logger.error(`回调通知失败: ${error.message}`);
    throw error;
  }
}
```

## 使用示例

### OCR识别异步调用

```typescript
// 异步处理OCR识别请求
const result = await ocrService.processOcrRequestAsync(file, 'http://your-callback-url.com/webhook');

// 获取任务状态
const status = await ocrService.getOcrTaskStatus(result.jobId);
```

### 地址提取异步调用

```typescript
// 异步提取地址
const result = await addressService.extractFromTextAsync(text, 'single', 'http://your-callback-url.com/webhook');

// 获取任务状态
const status = await addressService.getAddressExtractionTaskStatus(result.jobId);
```

### 地理坐标逆解析异步调用

```typescript
// 异步地理坐标逆解析
const result = await addressService.reverseGeocodeAsync(coordinates, 'http://your-callback-url.com/webhook');

// 获取任务状态
const status = await addressService.getReverseGeocodeTaskStatus(result.jobId);
```

## 结论

通过这些改进，队列模块现在能够满足物流OCR识别、地址提取和地理坐标逆解析的异步调用需求，避免了请求阻塞问题，提高了系统的响应性能和可扩展性。

队列模块的设计符合单一职责原则，各组件职责明确，功能完整，可以满足业务需求。同时，通过回调机制和任务状态查询，提供了灵活的异步处理方式。