
/**
 * 认证模块
 * 处理用户注册、登录、令牌管理和第三方登录
 * 使用全局的UnifiedAuthGuard和CoreAuthService
 */
import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserModule } from '../user/user.module';
import { UserOauthEntity } from '../user/entities/user-oauth.entity';
import { AuthController } from './auth.controller';
import { AuthService } from './services/auth.service';
import { OAuthService } from './services/oauth.service';
import { AuthErrorHandlerService } from './services/auth-error-handler.service';
import { AuthSecurityService } from './services/auth-security.service';
import { PasswordPolicyService } from './services/password-policy.service';
import { VerificationCodeService } from './services/verification-code.service';
import { TokenManagementService } from './services/token-management.service';
import { AuthCoreService } from './services/auth-core.service';
import { SharedModule } from '@/shared/shared.module';
import { ApiKeyModule } from '../api-key/api-key.module';

@Module({
  imports: [
    // 导入TypeORM实体
    TypeOrmModule.forFeature([
      UserOauthEntity,
    ]),
    
    // 导入其他模块
    forwardRef(() => UserModule),
    forwardRef(() => ApiKeyModule),
    SharedModule,
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    OAuthService,
    AuthErrorHandlerService,
    AuthSecurityService,
    PasswordPolicyService,
    VerificationCodeService,
    TokenManagementService,
    AuthCoreService,
  ],
  exports: [
    AuthService,
    OAuthService,
  ],
})
export class AuthModule {}