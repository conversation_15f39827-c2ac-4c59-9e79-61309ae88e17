const { createConnection } = require('typeorm');
const crypto = require('crypto');

/**
 * 获取解密后的Secret Key脚本
 */
async function getDecryptedSecret() {
  console.log('获取解密后的Secret Key...');
  
  // 创建数据库连接
  const connection = await createConnection({
    type: 'mysql',
    host: 'localhost',
    port: 3306,
    username: 'root',
    password: '123456',
    database: 'openapidb',
    entities: [],
    synchronize: false,
  });

  try {
    const apiKey = 'ak-579b27759508f152525a4e5a567efd5a';
    
    // 查询API密钥的加密Secret Key
    const result = await connection.query(`
      SELECT id, api_key, encrypted_secret_key 
      FROM api_keys 
      WHERE api_key = ?
    `, [apiKey]);

    if (result.length === 0) {
      console.log('❌ 未找到API密钥');
      return;
    }

    const keyData = result[0];
    console.log(`找到API密钥: ID=${keyData.id}, Key=${keyData.api_key}`);
    console.log(`加密的Secret Key: ${keyData.encrypted_secret_key}`);
    
    // 解密Secret Key
    try {
      const algorithm = 'aes-256-cbc';
      const key = crypto.scryptSync('api-key-secret', 'salt', 32);
      
      const parts = keyData.encrypted_secret_key.split(':');
      if (parts.length === 2) {
        const iv = Buffer.from(parts[0], 'hex');
        const encrypted = parts[1];
        
        const decipher = crypto.createDecipheriv(algorithm, key, iv);
        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        
        console.log(`\n✅ 解密成功！`);
        console.log(`完整的Secret Key: ${decrypted}`);
        
        // 测试签名
        console.log('\n===== 测试签名 =====');
        const testData = {
          method: 'POST',
          path: '/v1/op/address/extract',
          queryParams: { mode: 'async' },
          bodyData: {
            text: '张三，13800138000，广东省深圳市南山区科技园',
            mode: 'async'
          },
          timestamp: '1754749990',
          expectedSignature: '/iovhoyT4F+UMDc/TyVY3BOxXzBAeL9ovMZxBgzLAI8='
        };
        
        // 构建规范化请求字符串
        const method = testData.method.toUpperCase();
        const path = testData.path;
        
        const sortedQuery = Object.keys(testData.queryParams)
          .sort()
          .map(key => `${key}=${encodeURIComponent(testData.queryParams[key])}`)
          .join('&');
        
        const bodyString = JSON.stringify(testData.bodyData);
        
        const canonicalRequest = [
          method,
          path,
          sortedQuery,
          bodyString,
          testData.timestamp
        ].join('\n');
        
        console.log('规范化请求字符串:');
        console.log(canonicalRequest);
        
        // 生成签名
        const hmac = crypto.createHmac('sha256', decrypted);
        hmac.update(canonicalRequest);
        const calculatedSignature = hmac.digest('base64');
        
        console.log('\n签名结果:');
        console.log('- 计算的签名:', calculatedSignature);
        console.log('- 期望的签名:', testData.expectedSignature);
        console.log('- 签名匹配:', calculatedSignature === testData.expectedSignature ? '✅ 是' : '❌ 否');
        
      } else {
        console.log('❌ 加密数据格式不正确');
      }
    } catch (error) {
      console.log(`❌ 解密失败: ${error.message}`);
    }
    
  } catch (error) {
    console.error('❌ 获取失败:', error.message);
  } finally {
    await connection.close();
    console.log('\n连接已关闭');
  }
}

// 运行脚本
getDecryptedSecret().catch(console.error);
