// 测试Redis连接
const Redis = require('ioredis');

async function testRedisConnection() {
  console.log('=== 测试Redis连接 ===');
  
  // 使用与配置文件相同的连接参数
  const redis = new Redis({
    host: 'localhost',
    port: 6379,
    password: '', // 本地开发环境Redis无密码
    db: 0,
    keyPrefix: '',
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: null,
  });

  try {
    // 测试连接
    console.log('1. 测试ping...');
    const pingResult = await redis.ping();
    console.log(`   Ping结果: ${pingResult}`);

    // 测试设置和获取
    console.log('2. 测试set/get...');
    await redis.set('test:connection', 'success');
    const getValue = await redis.get('test:connection');
    console.log(`   Set/Get结果: ${getValue}`);

    // 测试哈希操作
    console.log('3. 测试hash操作...');
    const taskId = '05d6de1d-c760-4d1a-8345-b47b327e6e89';
    const taskKey = `task:${taskId}`;
    
    // 检查任务是否存在
    const taskExists = await redis.exists(taskKey);
    console.log(`   任务${taskId}是否存在: ${taskExists ? '是' : '否'}`);
    
    if (taskExists) {
      const taskData = await redis.hgetall(taskKey);
      console.log('   任务数据:', taskData);
    } else {
      console.log('   任务不存在，创建测试任务...');
      await redis.hmset(taskKey, {
        status: 'processing',
        type: 'address',
        createdAt: Date.now(),
        progress: '0.5',
        message: '正在处理中...'
      });
      
      const newTaskData = await redis.hgetall(taskKey);
      console.log('   创建的任务数据:', newTaskData);
    }

    // 测试队列相关的键
    console.log('4. 检查队列相关的键...');
    const queueKeys = await redis.keys('bull:*');
    console.log(`   队列相关键数量: ${queueKeys.length}`);
    if (queueKeys.length > 0) {
      console.log('   前5个队列键:', queueKeys.slice(0, 5));
    }

    // 测试任务相关的键
    console.log('5. 检查任务相关的键...');
    const taskKeys = await redis.keys('task:*');
    console.log(`   任务相关键数量: ${taskKeys.length}`);
    if (taskKeys.length > 0) {
      console.log('   任务键:', taskKeys);
    }

    console.log('\n✅ Redis连接测试成功！');

  } catch (error) {
    console.error('\n❌ Redis连接测试失败:', error.message);
    console.error('错误详情:', error);
  } finally {
    await redis.quit();
  }
}

testRedisConnection().catch(console.error);
