/**
 * 统一错误代码定义
 */
export const ErrorCodeMap = {
  // 10000 - 99999 业务操作错误
  10000: '参数校验异常',
  10001: '系统用户已存在',
  10002: '填写验证码有误',
  10003: '用户名密码有误',
  10004: '节点路由已存在',
  10005: '权限必须包含父节点',
  10006: '非法操作：该节点仅支持目录类型父节点',
  10007: '非法操作：节点类型无法直接转换',
  10008: '该角色存在关联用户，请先删除关联用户',
  10009: '该部门存在关联用户，请先删除关联用户',
  10010: '该部门存在关联角色，请先删除关联角色',
  10015: '该部门存在子部门，请先删除子部门',
  10011: '旧密码与原密码不一致',
  10012: '如想下线自身可右上角退出',
  10013: '不允许下线该用户',
  10014: '父级菜单不存在',
  10016: '系统内置功能不允许操作',
  10017: '用户不存在',
  10018: '无法查找当前用户所属部门',
  10019: '部门不存在',
  10020: '任务不存在',
  10024: '验证码错误或已过期',
  10021: '参数配置键值对已存在',
  10101: '不安全的任务，确保执行的加入@Mission注解',
  10102: '所执行的任务不存在',

  // token相关
  11001: '登录无效或无权限访问',
  11002: '登录身份已过期',
  11003: '无权限，请联系管理员申请权限',

  // OSS相关 (21000-21999)
  21001: '当前创建的文件或目录已存在',
  21002: '无需操作',
  21003: '已超出支持的最大处理数量',

  // 用户相关错误
  30001: '手机号格式错误',
  30002: '邮箱格式错误',
  30003: '请提供邮箱验证码',
  30004: '帐号已存在，请调整后重新注册',
  30005: '当前手机号已存在，请调整后重新注册',
  30006: '当前邮箱已存在，请调整后重新注册',
  30007: '请提供邮箱或手机号',
  30008: '请提供短信验证码',
  30009: '短信验证码错误或已过期',
  30010: '用户不存在',
  30011: '邮箱已存在',
  30012: '余额不足',
  30013: '使用次数不足',
  30014: '两次输入的密码不一致',
  30015: '验证码无效或已过期',
  30016: '该手机号已注册',
  30017: '原密码错误',
  30018: '两次输入的新密码不一致',
  30019: '充值失败，请稍后重试',
  30020: '未找到要删除的用户',

  // 验证码相关错误 (20000-20999)
  20001: '图像验证码错误或已过期',
  20002: '发送过于频繁，请稍后再试',
  20003: '短信发送失败，请稍后重试',
  20004: '邮箱验证码发送失败',
  20005: '邮箱验证码验证失败',
  20006: '滑动验证码验证失败',
  20007: '邮箱验证码错误或已过期',
  20008: 'reCAPTCHA验证失败',
  20009: '手机号格式不正确',
  20010: '邮箱格式不正确',
  20011: '验证码发送失败',
  20012: '验证失败',

  // 服务相关错误
  32001: '服务代码已存在',
  32002: '服务不存在',
  32003: '服务不可用',
  32004: '服务版本已存在',
  32005: '服务版本不存在',
  32006: '不能删除当前版本，请先设置其他版本为当前版本',

  // 认证授权相关错误 (40000-49999)
  40001: '身份验证失败',
  40002: '权限不足',
  40003: '角色权限不足',
  40004: '资源访问被拒绝',
  40005: '用户不存在',
  40006: '用户账户已被禁用',
  40007: 'Token已过期',
  40008: '无效的Token',
  40009: '无效或未激活的API密钥',
  40010: 'API密钥已过期',
  40011: 'API密钥认证失败',

  // 登录认证相关错误 (50000-59999)
  50001: '请提供邮箱、用户名或手机号',
  50002: '安全验证失败',
  50003: '手机号未注册',
  50004: '请提供密码或短信验证码',
  50005: '用户名或密码错误',
  50006: '用户验证失败',
  50007: '登出失败',
  50008: '无效的令牌类型',
  50009: '用户账户未激活',
  50010: '无效的刷新令牌',

  // 网关服务相关错误 (60000-69999)
  60001: '服务不可用',
  60002: '同步请求处理失败',
  60003: '任务不存在',
  60004: '查询任务状态失败',
  60005: 'API Key请求频率超限',
  60006: 'IP请求频率超限',
  60007: '服务请求频率超限',
  60008: '服务熔断器已开启',
  60009: '需要API密钥',
  60010: '服务不存在',
  60011: '获取队列统计失败',

  // 服务管理相关错误 (70000-79999)
  70001: '服务代码已存在',
  70002: '服务ID不存在',
  70003: '服务代码不存在',
  70004: '服务版本已存在',
  70005: '服务版本ID不存在',
  70006: '服务版本不存在或不属于指定服务',
  70007: '不能删除当前版本',

  // 订单相关错误
  33001: '订单不存在',
  33002: '订单状态不正确',
  33003: '支付金额与订单金额不符',
  33004: '不支持的支付方式',
  33005: '支付记录不存在',
  33006: '只能对成功的支付申请退款',
  33007: '退款金额超过可退款金额',
  33008: '只有已支付的订单才能申请退款',
  33009: '只有待支付的订单才能取消',

  // 套餐相关错误
  34001: '套餐不存在或已下架',
  34002: '该服务下已存在相同类型的套餐包',
  34003: '套餐包不存在',
  34004: '该套餐包正在被用户使用，无法删除',
  34005: '用户套餐包不存在',
  34006: '只能暂停激活状态的套餐包',
  34007: '只能恢复暂停状态的套餐包',
  34008: '套餐包已过期，无法恢复',
  34009: '套餐包使用失败',

  // API Key相关错误
  35001: '不支持的密钥类型',
  35002: '密钥不存在',
  35003: 'API密钥不存在',
  35004: '无效的权限格式',

  // 计费相关错误
  36001: '无法调用API',
  36002: '余额扣费失败',

  // 网关相关错误
  37001: '任务不存在',
  37002: '查询任务状态失败',
  37003: '请求频率超限',
  37004: '熔断器已开启，请稍后重试',
  37005: '获取队列统计失败',

  // 认证相关错误（已迁移到40000-49999和50000-59999段）
  38001: '请提供邮箱、用户名或手机号',
  38002: '图像验证码错误',
  38003: '安全验证失败',
  38004: '手机号未注册',
  38005: '请提供密码或短信验证码',
  38006: '用户名或密码错误',
  38007: '用户验证失败',
  38008: '登出失败',
  38009: '无效的令牌类型',
  38010: '用户账户未激活',
  38011: '无效的刷新令牌'
} as const

export type ErrorCodeMapType = keyof typeof ErrorCodeMap
