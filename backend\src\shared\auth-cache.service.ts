import { Injectable, Logger } from '@nestjs/common';
import { LayeredCacheService } from './layered-cache.service';
import { ApiKeyDto } from '../common/dto/auth.dto';

/**
 * 认证缓存服务
 * 作为认证服务和缓存之间的中间层
 */
@Injectable()
export class AuthCacheService {
  private readonly logger = new Logger(AuthCacheService.name);
  
  // 缓存键前缀
  private readonly API_KEY_PREFIX = 'api_key:';
  private readonly API_KEY_SECRET_PREFIX = 'api_key_secret:';
  private readonly API_KEY_LAST_USED_PREFIX = 'api_key_last_used:';
  private readonly JWT_BLACKLIST_PREFIX = 'jwt_blacklist:';
  private readonly USER_SESSION_PREFIX = 'user_session:';
  
  constructor(private readonly cacheService: LayeredCacheService) {}
  
  /**
   * 获取API密钥信息
   * @param key API密钥
   * @returns API密钥信息，不存在则返回null
   */
  async getApiKey(key: string): Promise<ApiKeyDto | null> {
    try {
      const cacheKey = `${this.API_KEY_PREFIX}${key}`;
      
      // 使用分层缓存获取API密钥信息
      const cachedData = await this.cacheService.get<ApiKeyDto>(cacheKey);
      
      if (!cachedData) {
        this.logger.debug(`API密钥缓存未命中: ${key}`);
        return null;
      }
      
      this.logger.debug(`API密钥缓存命中: ${key}`);
      return cachedData;
    } catch (error) {
      this.logger.error(`获取API密钥缓存失败: ${key}, ${error.message}`, error.stack);
      return null;
    }
  }
  
  /**
   * 设置API密钥信息
   * @param key API密钥
   * @param apiKeyData API密钥信息
   * @param ttl 过期时间（秒）
   */
  async setApiKey(key: string, apiKeyData: ApiKeyDto, ttl: number = 86400): Promise<void> {
    try {
      const cacheKey = `${this.API_KEY_PREFIX}${key}`;
      
      // 使用分层缓存设置API密钥信息
      await this.cacheService.set(cacheKey, apiKeyData, { ttl });
      
      this.logger.debug(`API密钥缓存已设置: ${key}, TTL=${ttl}秒`);
    } catch (error) {
      this.logger.error(`设置API密钥缓存失败: ${key}, ${error.message}`, error.stack);
    }
  }
  
  /**
   * 获取API密钥密文
   * @param id API密钥ID
   * @returns 密文，不存在则返回null
   */
  async getApiKeySecret(id: string | number): Promise<string | null> {
    try {
      const cacheKey = `${this.API_KEY_SECRET_PREFIX}${id}`;

      // 直接从Redis获取字符串值（Secret Key是纯字符串，不是JSON）
      const cachedData = await this.cacheService.getRedisString(cacheKey);

      if (!cachedData) {
        this.logger.debug(`API密钥密文缓存未命中: ${id}`);
        return null;
      }

      this.logger.debug(`API密钥密文缓存命中: ${id}`);
      return cachedData;
    } catch (error) {
      this.logger.error(`获取API密钥密文缓存失败: ${id}, ${error.message}`, error.stack);
      return null;
    }
  }
  
  /**
   * 设置API密钥密文
   * @param id API密钥ID
   * @param secret 密文
   * @param ttl 过期时间（秒）
   */
  async setApiKeySecret(id: string | number, secret: string, ttl: number = 86400): Promise<void> {
    try {
      const cacheKey = `${this.API_KEY_SECRET_PREFIX}${id}`;

      // 直接设置Redis字符串值（Secret Key是纯字符串，不需要JSON序列化）
      await this.cacheService.setRedisString(cacheKey, secret, ttl);

      this.logger.debug(`API密钥密文缓存已设置: ${id}, TTL=${ttl}秒`);
    } catch (error) {
      this.logger.error(`设置API密钥密文缓存失败: ${id}, ${error.message}`, error.stack);
    }
  }
  
  /**
   * 更新API密钥最后使用时间
   * @param id API密钥ID
   * @param ip 客户端IP
   */
  async updateApiKeyLastUsed(id: string | number, ip: string): Promise<void> {
    try {
      const cacheKey = `${this.API_KEY_LAST_USED_PREFIX}${id}`;
      
      // 使用分层缓存设置API密钥最后使用时间
      await this.cacheService.set(cacheKey, {
        time: new Date().toISOString(),
        ip
      }, { ttl: 86400, localOnly: true }); // 只使用本地缓存，不写入Redis
      
      this.logger.debug(`API密钥最后使用时间已更新: ${id}, IP=${ip}`);
    } catch (error) {
      this.logger.error(`更新API密钥最后使用时间失败: ${id}, ${error.message}`, error.stack);
    }
  }
  
  /**
   * 检查是否为黑名单JWT
   * @param jwtId JWT ID
   * @returns 是否在黑名单中
   */
  async isJwtBlacklisted(jwtId: string): Promise<boolean> {
    try {
      const cacheKey = `${this.JWT_BLACKLIST_PREFIX}${jwtId}`;
      
      // 使用分层缓存检查JWT是否在黑名单中
      return await this.cacheService.exists(cacheKey);
    } catch (error) {
      this.logger.error(`检查JWT黑名单失败: ${jwtId}, ${error.message}`, error.stack);
      return false;
    }
  }
  
  /**
   * 将JWT加入黑名单
   * @param jwtId JWT ID
   * @param ttl 过期时间（秒）
   */
  async blacklistJwt(jwtId: string, ttl: number = 86400): Promise<void> {
    try {
      const cacheKey = `${this.JWT_BLACKLIST_PREFIX}${jwtId}`;
      
      // 使用分层缓存将JWT加入黑名单
      await this.cacheService.set(cacheKey, '1', { ttl });
      
      this.logger.debug(`JWT已加入黑名单: ${jwtId}, TTL=${ttl}秒`);
    } catch (error) {
      this.logger.error(`将JWT加入黑名单失败: ${jwtId}, ${error.message}`, error.stack);
    }
  }
  
  /**
   * 获取用户会话信息
   * @param userId 用户ID
   * @returns 会话信息，不存在则返回null
   */
  async getUserSession(userId: string | number): Promise<any | null> {
    try {
      const cacheKey = `${this.USER_SESSION_PREFIX}${userId}`;
      
      // 使用分层缓存获取用户会话信息
      return await this.cacheService.get(cacheKey);
    } catch (error) {
      this.logger.error(`获取用户会话信息失败: ${userId}, ${error.message}`, error.stack);
      return null;
    }
  }
  
  /**
   * 设置用户会话信息
   * @param userId 用户ID
   * @param sessionData 会话信息
   * @param ttl 过期时间（秒）
   */
  async setUserSession(userId: string | number, sessionData: any, ttl: number = 86400): Promise<void> {
    try {
      const cacheKey = `${this.USER_SESSION_PREFIX}${userId}`;
      
      // 使用分层缓存设置用户会话信息
      await this.cacheService.set(cacheKey, sessionData, { ttl });
      
      this.logger.debug(`用户会话信息已设置: ${userId}, TTL=${ttl}秒`);
    } catch (error) {
      this.logger.error(`设置用户会话信息失败: ${userId}, ${error.message}`, error.stack);
    }
  }
  
  /**
   * 删除API密钥缓存
   * @param key API密钥
   */
  async deleteApiKey(key: string): Promise<void> {
    try {
      const cacheKey = `${this.API_KEY_PREFIX}${key}`;
      
      // 使用分层缓存删除API密钥信息
      await this.cacheService.delete(cacheKey);
      
      this.logger.debug(`API密钥缓存已删除: ${key}`);
    } catch (error) {
      this.logger.error(`删除API密钥缓存失败: ${key}, ${error.message}`, error.stack);
    }
  }
  
  /**
   * 删除API密钥密文缓存
   * @param id API密钥ID
   */
  async deleteApiKeySecret(id: string | number): Promise<void> {
    try {
      const cacheKey = `${this.API_KEY_SECRET_PREFIX}${id}`;
      
      // 使用分层缓存删除API密钥密文
      await this.cacheService.delete(cacheKey);
      
      this.logger.debug(`API密钥密文缓存已删除: ${id}`);
    } catch (error) {
      this.logger.error(`删除API密钥密文缓存失败: ${id}, ${error.message}`, error.stack);
    }
  }
  
  /**
   * 记录认证失败
   * @param userId 用户ID
   * @param reason 失败原因
   */
  async recordAuthFailure(userId: string | number, reason: string): Promise<void> {
    try {
      const cacheKey = `auth_failures:${userId}`;
      
      // 使用分层缓存增加认证失败计数
      const count = await this.cacheService.increment(cacheKey);
      
      // 设置过期时间（1小时）
      await this.cacheService.expire(cacheKey, 3600);
      
      this.logger.warn(`用户认证失败: ${userId}, 原因=${reason}, 计数=${count}`);
      
      // 如果失败次数过多，可以触发安全警报
      if (count >= 5) {
        this.logger.warn(`用户认证失败次数过多: ${userId}, 计数=${count}, 可能存在安全风险`);
        // 这里可以添加触发安全警报的逻辑
      }
    } catch (error) {
      this.logger.error(`记录认证失败失败: ${userId}, ${error.message}`, error.stack);
    }
  }
  
  /**
   * 重置认证失败计数
   * @param userId 用户ID
   */
  async resetAuthFailures(userId: string | number): Promise<void> {
    try {
      const cacheKey = `auth_failures:${userId}`;
      
      // 使用分层缓存删除认证失败计数
      await this.cacheService.delete(cacheKey);
      
      this.logger.debug(`用户认证失败计数已重置: ${userId}`);
    } catch (error) {
      this.logger.error(`重置认证失败计数失败: ${userId}, ${error.message}`, error.stack);
    }
  }
} 