import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
  BeforeInsert,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { UserEntity } from '../../user/entities/user.entity';
import { OrderItemEntity } from './order-item.entity';
import { OrderStatus, OrderType, PaymentMethod } from '../enums/order.enum';

@Entity('orders')
@Index(['userId', 'status'])
@Index(['createdAt'])
export class OrderEntity {
  @ApiProperty({ description: '订单ID', example: 1 })
  @PrimaryGeneratedColumn({ comment: '订单ID' })
  id: number;

  @ApiProperty({ description: '用户ID', example: 1 })
  @Column({ type: 'int', name: 'user_id', comment: '用户ID' })
  userId: number;

  @ApiProperty({ description: '订单号', example: 'ORD202312010001' })
  @Column({ type: 'varchar', length: 32, name: 'order_no', unique: true, comment: '订单号' })
  orderNo: string;

  @ApiProperty({ description: '订单类型', enum: OrderType, example: OrderType.SERVICE })
  @Column({
    type: 'enum',
    enum: OrderType,
    name: 'order_type',
    comment: '订单类型'
  })
  orderType: OrderType;

  @ApiProperty({ description: '订单状态', enum: OrderStatus, example: OrderStatus.PENDING })
  @Column({
    type: 'enum',
    enum: OrderStatus,
    default: OrderStatus.PENDING,
    comment: '订单状态'
  })
  status: OrderStatus;

  @ApiProperty({ description: '订单总金额', example: 100.00 })
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    name: 'total_amount',
    comment: '订单总金额'
  })
  totalAmount: number;

  @ApiProperty({ description: '已支付金额', example: 100.00 })
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    name: 'paid_amount',
    default: 0,
    comment: '已支付金额'
  })
  paidAmount: number;

  @ApiProperty({ description: '优惠金额', example: 10.00 })
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    name: 'discount_amount',
    default: 0,
    comment: '优惠金额'
  })
  discountAmount: number;

  @ApiProperty({ description: '支付方式', enum: PaymentMethod, example: PaymentMethod.ALIPAY })
  @Column({
    type: 'enum',
    enum: PaymentMethod,
    name: 'payment_method',
    nullable: true,
    comment: '支付方式'
  })
  paymentMethod?: PaymentMethod;

  @ApiProperty({ description: '订单过期时间', example: '2023-12-01T12:00:00Z' })
  @Column({
    type: 'timestamp',
    name: 'expires_at',
    nullable: true,
    comment: '订单过期时间'
  })
  expiresAt?: Date;

  @ApiProperty({ description: '备注', example: '购买OCR服务' })
  @Column({
    type: 'text',
    nullable: true,
    comment: '备注'
  })
  remark?: string;

  @ApiProperty({ description: '创建时间', example: '2023-12-01T10:00:00Z' })
  @CreateDateColumn({ name: 'created_at', comment: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间', example: '2023-12-01T10:30:00Z' })
  @UpdateDateColumn({ name: 'updated_at', comment: '更新时间' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => UserEntity, { lazy: true })
  @JoinColumn({ name: 'user_id' })
  user: Promise<UserEntity>;

  @OneToMany(() => OrderItemEntity, orderItem => orderItem.order, { cascade: true })
  orderItems: OrderItemEntity[];

  /**
   * 生成订单号
   */
  @BeforeInsert()
  generateOrderNo() {
    if (!this.orderNo) {
      const timestamp = Date.now().toString();
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      this.orderNo = `ORD${timestamp}${random}`;
    }
  }

  /**
   * 检查订单是否已过期
   */
  isExpired(): boolean {
    if (!this.expiresAt) return false;
    return new Date() > this.expiresAt;
  }

  /**
   * 检查订单是否可以支付
   */
  canPay(): boolean {
    return this.status === OrderStatus.PENDING && !this.isExpired();
  }

  /**
   * 检查订单是否可以取消
   */
  canCancel(): boolean {
    return [OrderStatus.PENDING].includes(this.status);
  }

  /**
   * 检查订单是否可以退款
   */
  canRefund(): boolean {
    return [OrderStatus.PAID, OrderStatus.COMPLETED].includes(this.status);
  }
}
