import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { request } from '@/utils/request'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'

export interface UserInfo {
  id: string
  email?: string
  username?: string
  nickName?: string
  type: 'individual' | 'enterprise'
  balance: number
  verificationStatus: string
  remainingUsage: number
  totalUsage: number
  createdAt: string
  joinDate?: string
  avatar?: string
  emailVerified?: boolean
  twoFactorEnabled?: boolean
  loginNotification?: boolean
  phone?: string
  realName?: string
  company?: string
  position?: string
  bio?: string
  settings?: Record<string, any>
}

export interface LoginForm {
  username?: string
  password?: string
  phone?: string
  smsCode?: string
  isSilentRegister?: boolean
  securityVerification?: {
    type: string
    level: number
    behaviorPattern: any
    verificationTimestamp: number
  }
  rememberMe: boolean
}

export interface RegisterForm {
  email?: string
  phone?: string
  password: string
  confirmPassword?: string
  userType?: 'individual' | 'enterprise' | 'organization'
  captchaId?: string
  captchaCode?: string
  smsCode?: string
  emailCode?: string
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '')
  const refreshToken = ref<string>(localStorage.getItem('refreshToken') || '')
  const userInfo = ref<UserInfo | null>(null)
  const loading = ref<boolean>(false)
  const isLoggedIn = computed(() => !!token.value)
  const router = useRouter()

  // 登录 - 支持用户名密码登录和短信验证码登录
  const login = async (loginData: LoginForm) => {
    try {
      loading.value = true
      const response: Record<string, any> = await request.post('/auth/login', loginData)

      // 由于响应拦截器已经处理了成功响应，直接使用返回的数据
      token.value = response.accessToken || response.token
      refreshToken.value = response.refreshToken
      userInfo.value = response.user

      // 保存到 localStorage
      localStorage.setItem('token', token.value)
      localStorage.setItem('refreshToken', refreshToken.value || '')
      localStorage.setItem('userInfo', JSON.stringify(userInfo.value))

      return response
    } catch (error: unknown) {
      console.error('登录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册 - 支持两种注册方式
  const register = async (registerData: RegisterForm) => {
    try {
      loading.value = true
      return await request.post('/auth/register', registerData)
    } catch (error: unknown) {
      console.error('注册失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const response = await request.get('/auth/profile')
      userInfo.value = response as any
      localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  // 获取用户API密钥信息
  const getUserApiKeysInfo = async () => {
    try {
      // 使用已存在的getUserApiKeysInfoById方法，传入当前用户ID
      if (userInfo.value && userInfo.value.id) {
        return await getUserApiKeysInfoById(parseInt(userInfo.value.id));
      } else {
        throw new Error('用户未登录或ID不存在');
      }
    } catch (error) {
      console.error('获取用户API密钥信息失败:', error);
      throw error;
    }
  }

  // 获取用户的apikeys信息
  const getUserApiKeysInfoById = async (userId: number) => {
    try {
      const response = await request.get(`/api-keys/user/${userId}`)
      return response
    } catch (error) {
      console.error('获取用户apikeys信息失败:', error)
      throw error
    }
  }

  // 设置token的方法
  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
    return true
  }

  // 设置refreshToken的方法
  const setRefreshToken = (newRefreshToken: string) => {
    refreshToken.value = newRefreshToken
    localStorage.setItem('refreshToken', newRefreshToken)
    return true
  }

  // 刷新token
  const refreshAccessToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('没有刷新令牌')
      }

      console.log('用户store开始刷新token...')
      const response: Record<string, any> = await request.post('/auth/refresh', {
        refresh_token: refreshToken.value,
      })

      if (!response.accessToken) {
        throw new Error('刷新响应中缺少accessToken')
      }

      // 更新token
      token.value = response.accessToken
      if (response.refreshToken) {
        refreshToken.value = response.refreshToken
      }

      // 持久化存储
      localStorage.setItem('token', token.value)
      localStorage.setItem('refreshToken', refreshToken.value)

      console.log('用户store token刷新成功')
      return response
    } catch (error) {
      console.error('用户store刷新token失败:', error)
      // 刷新失败，清除所有token
      clearLocalData()
      throw error
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 尝试调用服务器登出接口
      const response: Record<string, any> = await request.post('/auth/logout')

      // 无论服务器响应如何，都清除本地数据
      clearLocalData()

      ElMessage.success(response?.message || '已退出登录')
    } catch (error) {
      // 即使服务器请求失败，也要清除本地数据
      console.warn('服务器登出请求失败，但仍清除本地数据:', error)
      clearLocalData()
      ElMessage.success('已退出登录')
    }
  }

  // 清除本地数据的辅助函数
  const clearLocalData = () => {
    // 清除 store 中的状态
    token.value = ''
    refreshToken.value = ''
    userInfo.value = null
    loading.value = false

    // 清除 localStorage 中的所有相关数据
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('userInfo')

    // 清除可能存在的其他相关缓存数据
    localStorage.removeItem('lastLoginTime')
    localStorage.removeItem('userPreferences')

    // 清除 sessionStorage 中的相关数据（如果有的话）
    sessionStorage.removeItem('token')
    sessionStorage.removeItem('userInfo')
  }

  // 初始化用户信息（从本地存储恢复）
  const initUserInfo = () => {
    const savedUserInfo = localStorage.getItem('userInfo')
    if (savedUserInfo) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        localStorage.removeItem('userInfo')
      }
    }
  }

  // 检查并获取用户信息的兜底逻辑
  const checkAndFetchUserInfo = async () => {
    // 如果有token但没有用户信息，主动获取用户信息
    if (token.value && !userInfo.value) {
      try {
        console.log('检测到有token但无用户信息，正在获取用户信息...')
        await getUserInfo()
        console.log('用户信息获取成功')
        return true
      } catch (error) {
        console.error('获取用户信息失败，可能token已过期:', error)
        // 如果获取用户信息失败，清除无效的token
        clearLocalData()
        return false
      }
    } else {
      return true
    }
  }

  // 应用初始化时的完整检查逻辑
  const initializeUserState = async () => {
    // 首先从本地存储恢复用户信息
    await initUserInfo()

    // 然后检查是否需要获取用户信息
    const res = await checkAndFetchUserInfo()
    if (!res) {
      router.push('/')
    }
  }

  // 更新用户信息
  const updateUserInfo = async (updateData: Partial<UserInfo>) => {
    try {
      const response: Record<string, any> = await request.put('/user/profile', updateData)
      userInfo.value = { ...userInfo.value, ...response } as any
      localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
      ElMessage.success('更新成功')
      return response
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    }
  }

  const uploadAvatar = async (file: File) => {
    try {
      loading.value = true
      const formData = new FormData()
      formData.append('file', file)

      // 这里假设有文件上传接口，如果没有可以先返回一个模拟的URL
      // const response = await request.post('/op/upload/avatar', formData, {
      //   headers: {
      //     'Content-Type': 'multipart/form-data'
      //   }
      // })

      // 临时方案：生成一个本地预览URL
      const avatarUrl = URL.createObjectURL(file)

      // 更新用户头像信息
      if (userInfo.value) {
        userInfo.value.avatar = avatarUrl
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
      }

      ElMessage.success('头像上传成功')
      return { url: avatarUrl }
    } catch (error: unknown) {
      console.error('头像上传失败:', error)
      ElMessage.error((error as Error).message || '头像上传失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateSettings = async (settings: Record<string, unknown>) => {
    try {
      loading.value = true

      // 更新用户偏好设置
      const response = await request.put('/user/profile', {
        settings: settings,
      })

      // 更新本地用户信息
      if (userInfo.value) {
        userInfo.value.settings = { ...userInfo.value.settings, ...settings }
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
      }

      ElMessage.success('设置更新成功')
      return response
    } catch (error: unknown) {
      console.error('设置更新失败:', error)
      ElMessage.error((error as Error).message || '设置更新失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const verifyEmail = async () => {
    try {
      if (!userInfo.value?.email) {
        throw new Error('用户邮箱信息不存在')
      }

      const response: Record<string, any> = await request.post('/auth/send-email-code', {
        email: userInfo.value.email,
        type: 'verify',
      })

      if (response.code === 200) {
        return { success: true, message: '验证邮件已发送' }
      } else {
        throw new Error(response.message || '发送验证邮件失败')
      }
    } catch (error) {
      console.error('邮箱验证失败:', error)
      throw error
    }
  }

  const enableTwoFactor = async () => {
    try {
      loading.value = true

      // 这里应该调用后端API启用两步验证
      // const response = await request.post('/op/user/enable-2fa')

      // 临时方案：更新本地设置
      if (userInfo.value) {
        userInfo.value.settings = {
          ...userInfo.value.settings,
          twoFactorEnabled: true,
        }
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
      }

      ElMessage.success('两步验证已启用')
      return { success: true }
    } catch (error: unknown) {
      console.error('启用两步验证失败:', error)
      ElMessage.error((error as Error).message || '启用两步验证失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const disableTwoFactor = async () => {
    try {
      loading.value = true

      // 这里应该调用后端API禁用两步验证
      // const response = await request.post('/op/user/disable-2fa')

      // 临时方案：更新本地设置
      if (userInfo.value) {
        userInfo.value.settings = {
          ...userInfo.value.settings,
          twoFactorEnabled: false,
        }
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
      }

      ElMessage.success('两步验证已禁用')
      return { success: true }
    } catch (error: unknown) {
      console.error('禁用两步验证失败:', error)
      ElMessage.error((error as Error).message || '禁用两步验证失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const updatePassword = async (passwordData: { oldPassword: string; newPassword: string }) => {
    try {
      loading.value = true
      const response = await request.patch('/auth/change-password', {
        oldPassword: passwordData.oldPassword,
        newPassword: passwordData.newPassword,
        confirmPassword: passwordData.newPassword, // 确认密码与新密码相同
      })

      ElMessage.success('密码修改成功')
      return response
    } catch (error: unknown) {
      console.error('修改密码失败:', error)
      ElMessage.error((error as Error).message || '修改密码失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateProfile = async (profileData: Record<string, any>) => {
    try {
      loading.value = true
      const response = await request.put('/user/profile', {
        nickName: profileData.username, // 前端使用username，后端期望nickName
        realName: profileData.realName,
        bio: profileData.bio,
        settings: {
          company: profileData.company,
          position: profileData.position,
          phone: profileData.phone,
        },
      })

      // 更新本地用户信息
      if (userInfo.value) {
        userInfo.value = {
          ...userInfo.value,
          username: profileData.username,
          realName: profileData.realName,
          bio: profileData.bio,
          company: profileData.company,
          position: profileData.position,
          phone: profileData.phone,
        }
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
      }

      ElMessage.success('个人资料更新成功')
      return response
    } catch (error: unknown) {
      console.error('更新个人资料失败:', error)
      ElMessage.error((error as Error).message || '更新个人资料失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const resetPassword = async (email: string) => {
    try {
      const response = await request.post('/auth/reset-password', { email })
      return response
    } catch (error) {
      console.error('重置密码失败:', error)
      throw error
    }
  }

  // 验证重置密码验证码
  const verifyResetCode = async (data: {
    email?: string
    phone?: string
    code: string
    type: 'email' | 'phone'
  }) => {
    try {
      const response = await request.post('/auth/verify-reset-code', data)
      return response
    } catch (error) {
      console.error('验证重置码失败:', error)
      throw error
    }
  }

  // 使用验证码重置密码
  const resetPasswordWithCode = async (data: {
    newPassword: string
    confirmPassword: string
    email?: string
    phone?: string
    emailCode?: string
    smsCode?: string
    type: 'email' | 'phone'
  }) => {
    try {
      const response = await request.post('/auth/reset-password-with-code', data)
      return response
    } catch (error) {
      console.error('重置密码失败:', error)
      throw error
    }
  }

  // 发送邮箱验证码
  const sendEmailCode = async (
    email: string,
    type: 'register' | 'login' | 'reset' = 'register',
    securityVerification?: {
      level: number;
      behaviorPattern: any;
      verificationTimestamp: number;
    }
  ) => {
    try {
      // 如果没有提供安全验证数据，使用默认值
      const defaultSecurityVerification = {
        level: 2,
        behaviorPattern: { verified: true },
        verificationTimestamp: Date.now(),
        type: 'behavior'
      };

      const response = await request.post('/auth/send-email-code', {
        email,
        type,
        securityVerification: securityVerification || defaultSecurityVerification
      })
      return response
    } catch (error) {
      console.error('发送邮箱验证码失败:', error)
      throw error
    }
  }

  // 发送短信验证码
  const sendSmsCode = async (
    phone: string,
    type: 'register' | 'login' | 'reset' = 'register',
    securityVerification?: {
      level: number;
      behaviorPattern: any;
      verificationTimestamp: number;
    }
  ) => {
    try {
      // 如果没有提供安全验证数据，使用默认值
      const defaultSecurityVerification = {
        level: 2,
        behaviorPattern: { verified: true },
        verificationTimestamp: Date.now(),
        type: 'behavior'
      };

      const response = await request.post('/auth/send-sms-code', {
        phone,
        type,
        securityVerification: securityVerification || defaultSecurityVerification
      })
      return response
    } catch (error) {
      console.error('发送短信验证码失败:', error)
      throw error
    }
  }

  return {
    // 状态
    token,
    refreshToken,
    userInfo,
    loading,
    isLoggedIn,

    // 方法
    login,
    register,
    logout,
    getUserInfo,
    getUserApiKeysInfo,
    getUserApiKeysInfoById,
    refreshAccessToken,
    setToken,
    setRefreshToken,
    clearLocalData,
    initUserInfo,
    checkAndFetchUserInfo,
    initializeUserState,
    updateUserInfo,
    uploadAvatar,
    updateSettings,
    verifyEmail,
    enableTwoFactor,
    disableTwoFactor,
    updatePassword,
    updateProfile,
    resetPassword,
    verifyResetCode,
    resetPasswordWithCode,
    sendEmailCode,
    sendSmsCode,
  }
})
