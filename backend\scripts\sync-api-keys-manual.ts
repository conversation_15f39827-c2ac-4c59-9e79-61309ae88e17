import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { ApiKeyService } from '../src/modules/api-key/services/api-key.service';

/**
 * 手动同步API密钥到缓存脚本
 * 用于修复缓存数据结构问题
 */
async function syncApiKeysManual() {
  console.log('开始手动同步API密钥到缓存...');
  
  const app = await NestFactory.createApplicationContext(AppModule);
  
  try {
    const apiKeyService = app.get(ApiKeyService);
    
    console.log('正在同步API密钥...');
    const result = await apiKeyService.syncAllKeysToCache();
    
    console.log(`✅ 同步完成！成功同步 ${result.syncedCount} 个密钥`);
    
    if (result.errors.length > 0) {
      console.log('❌ 同步过程中的错误:');
      result.errors.forEach(error => console.log(`  - ${error}`));
    }
    
  } catch (error) {
    console.error('❌ 同步失败:', error.message);
  } finally {
    await app.close();
    console.log('应用已关闭');
  }
}

// 运行脚本
syncApiKeysManual().catch(console.error);
