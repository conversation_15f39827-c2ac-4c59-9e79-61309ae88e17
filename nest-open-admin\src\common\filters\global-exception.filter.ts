import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
  BadRequestException,
  UnauthorizedException,
  ForbiddenException,
  NotFoundException,
  ConflictException,
  InternalServerErrorException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { QueryFailedError, EntityNotFoundError } from 'typeorm';
import { ValidationError } from 'class-validator';
import { ApiException } from '../exceptions/api.exception';
import { ErrorCodeMap } from '../constant/error-code.contants';
import { ResponseBuilder } from '../utils/response-builder';
import { ConfigService } from '@nestjs/config';
import { ErrorHandlerService } from '@/shared/error-handler.service';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);
  private readonly isDevelopment: boolean;
  private readonly enableDetailedErrors: boolean;

  constructor(
    private readonly configService?: ConfigService,
    private readonly errorHandler?: ErrorHandlerService,
  ) {
    this.isDevelopment = this.configService?.get('NODE_ENV') !== 'production';
    this.enableDetailedErrors = !!this.configService?.get(
      'app.enableDetailedErrors',
      false,
    );
  }

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // 检查响应是否已经发送
    if (response.headersSent) {
      this.logger.warn('Response already sent, skipping exception handling');
      return;
    }

    const requestId = (request as any).requestId || this.generateRequestId();
    const startTime = (request as any).startTime || Date.now();
    const duration = Date.now() - startTime;

    // 使用ErrorHandlerService处理异常（如果可用）
    let exceptionInfo;
    if (this.errorHandler && exception instanceof Error) {
      try {
        const handledException = this.errorHandler.handleUnknownError(
          exception,
          'GlobalExceptionFilter',
        );
        exceptionInfo = {
          status: handledException.getStatus(),
          errorCode: handledException.errorCode,
          message: handledException.message,
          details: handledException.details,
        };
      } catch (handlerError) {
        this.logger.error(`ErrorHandler failed: ${handlerError.message}`);
        exceptionInfo = this.parseException(exception);
      }
    } else {
      exceptionInfo = this.parseException(exception);
    }

    // 记录错误日志（使用ErrorHandlerService如果可用）
    if (this.errorHandler) {
      this.errorHandler.logError(
        exception,
        'GlobalExceptionFilter',
        (request as any).user?.id,
      );
    } else {
      this.logException(exception, exceptionInfo, request, requestId);
    }

    // 构建错误响应
    const errorResponse = ResponseBuilder.error(
      exceptionInfo.message,
      exceptionInfo.code,
      {
        requestId,
        duration,
        meta: {
          path: request.url,
          method: request.method,
          timestamp: new Date().toISOString(),
          ...(this.isDevelopment && exceptionInfo.details
            ? { details: exceptionInfo.details }
            : {}),
        },
      },
    );

    // 记录异常日志
    this.logException(exception, exceptionInfo, request, requestId);

    // 设置响应头
    response.setHeader('X-Request-ID', requestId);
    response.setHeader('X-Error-Code', exceptionInfo.code);

    // 发送响应
    response.status(exceptionInfo.httpStatus).json(errorResponse);
  }

  /**
   * 解析异常信息
   */
  private parseException(exception: unknown): {
    httpStatus: number;
    code: number;
    message: string;
    details?: any;
  } {
    // 业务异常
    if (exception instanceof ApiException) {
      return {
        httpStatus: HttpStatus.OK, // 业务异常统一返回200
        code: exception.getErrCode(),
        message: ErrorCodeMap[exception.getErrCode()] || '业务处理失败',
      };
    }

    // 数据库异常
    if (exception instanceof QueryFailedError) {
      return this.handleDatabaseException(exception);
    }

    if (exception instanceof EntityNotFoundError) {
      return {
        httpStatus: HttpStatus.NOT_FOUND,
        code: 404,
        message: '请求的资源不存在',
        details: this.enableDetailedErrors ? exception.message : undefined,
      };
    }

    // HTTP异常
    if (exception instanceof HttpException) {
      return this.handleHttpException(exception);
    }

    // 系统异常
    return {
      httpStatus: HttpStatus.INTERNAL_SERVER_ERROR,
      code: 500,
      message: this.isDevelopment
        ? (exception as Error)?.message || '系统内部错误'
        : '系统内部错误',
      details:
        this.enableDetailedErrors && exception instanceof Error
          ? {
              stack: exception.stack,
              name: exception.name,
            }
          : undefined,
    };
  }

  /**
   * 处理数据库异常
   */
  private handleDatabaseException(exception: QueryFailedError): {
    httpStatus: number;
    code: number;
    message: string;
    details?: any;
  } {
    const error = exception as any;

    // MySQL错误码处理
    switch (error.errno) {
      case 1062: // 重复键错误
        return {
          httpStatus: HttpStatus.CONFLICT,
          code: 409,
          message: '数据已存在，请检查唯一性约束',
          details: this.enableDetailedErrors ? error.sqlMessage : undefined,
        };
      case 1452: // 外键约束错误
        return {
          httpStatus: HttpStatus.BAD_REQUEST,
          code: 400,
          message: '关联数据不存在，请检查数据完整性',
          details: this.enableDetailedErrors ? error.sqlMessage : undefined,
        };
      case 1451: // 外键约束删除错误
        return {
          httpStatus: HttpStatus.CONFLICT,
          code: 409,
          message: '存在关联数据，无法删除',
          details: this.enableDetailedErrors ? error.sqlMessage : undefined,
        };
      default:
        return {
          httpStatus: HttpStatus.INTERNAL_SERVER_ERROR,
          code: 500,
          message: this.isDevelopment ? '数据库操作失败' : '系统内部错误',
          details: this.enableDetailedErrors ? error.sqlMessage : undefined,
        };
    }
  }

  /**
   * 处理HTTP异常
   */
  private handleHttpException(exception: HttpException): {
    httpStatus: number;
    code: number;
    message: string;
    details?: any;
  } {
    const status = exception.getStatus();
    const exceptionResponse = exception.getResponse();

    let message: string;
    let details: any;

    if (typeof exceptionResponse === 'object') {
      const responseObj = exceptionResponse as any;
      message = responseObj.message;

      // 处理验证错误
      if (Array.isArray(message)) {
        details = this.enableDetailedErrors
          ? { validationErrors: message }
          : undefined;
        message = message[0] || '请求参数验证失败';
      }

      // 处理其他详细信息
      if (responseObj.error && this.enableDetailedErrors) {
        details = { ...details, error: responseObj.error };
      }
    } else {
      message = exceptionResponse as string;
    }

    // 根据HTTP状态码映射业务错误码
    let code: number;
    switch (status) {
      case HttpStatus.BAD_REQUEST:
        code = 400;
        message = message || '请求参数错误';
        break;
      case HttpStatus.UNAUTHORIZED:
        code = 401;
        message = message || '身份验证失败';
        break;
      case HttpStatus.FORBIDDEN:
        code = 403;
        message = message || '权限不足';
        break;
      case HttpStatus.NOT_FOUND:
        code = 404;
        message = message || '请求的资源不存在';
        break;
      case HttpStatus.CONFLICT:
        code = 409;
        message = message || '资源冲突';
        break;
      case HttpStatus.TOO_MANY_REQUESTS:
        code = 429;
        message = message || '请求过于频繁';
        break;
      default:
        code = status;
        message = message || '请求处理失败';
    }

    return {
      httpStatus: status,
      code,
      message,
      details,
    };
  }

  /**
   * 记录异常日志
   */
  private logException(
    exception: unknown,
    exceptionInfo: any,
    request: Request,
    requestId: string,
  ): void {
    const logContext = {
      requestId,
      method: request.method,
      url: request.url,
      userAgent: request.get('User-Agent'),
      ip: request.ip || request.connection.remoteAddress,
      userId: (request as any).user?.id,
      apiKeyId: (request as any).apiKey?.id,
      code: exceptionInfo.code,
      httpStatus: exceptionInfo.httpStatus,
    };

    // 业务异常记录为debug级别
    if (exception instanceof ApiException) {
      this.logger.debug(
        `Business exception: ${exceptionInfo.message}`,
        logContext,
      );
      return;
    }

    // 客户端错误记录为warn级别
    if (exceptionInfo.httpStatus >= 400 && exceptionInfo.httpStatus < 500) {
      this.logger.warn(`Client error: ${exceptionInfo.message}`, {
        ...logContext,
        exception:
          exception instanceof Error ? exception.message : String(exception),
      });
      return;
    }

    // 服务器错误记录为error级别
    this.logger.error(
      `Server error: ${exceptionInfo.message}`,
      exception instanceof Error ? exception.stack : String(exception),
      {
        ...logContext,
        exception:
          exception instanceof Error
            ? {
                name: exception.name,
                message: exception.message,
                stack: exception.stack,
              }
            : String(exception),
      },
    );
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }
}
