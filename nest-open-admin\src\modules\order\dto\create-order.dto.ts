import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsPositive,
  Min,
  IsInt,
} from 'class-validator';
import { OrderType } from '../entities/order.entity';

export class CreateOrderDto {
  @ApiProperty({ 
    description: '订单类型', 
    example: OrderType.SERVICE_PURCHASE,
    enum: OrderType 
  })
  @IsEnum(OrderType)
  @IsNotEmpty()
  type: OrderType;

  @ApiProperty({ 
    description: '订单金额', 
    example: 100.00,
    minimum: 0.01 
  })
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  amount: number;

  @ApiProperty({ 
    description: '用户ID', 
    example: 1 
  })
  @Type(() => Number)
  @IsInt()
  @IsPositive()
  userId: number;

  @ApiPropertyOptional({ 
    description: '服务ID（购买服务时必填）', 
    example: 1 
  })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @IsPositive()
  serviceId?: number;

  @ApiPropertyOptional({ 
    description: '购买次数（购买服务时必填）', 
    example: 100,
    minimum: 1 
  })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  purchaseCount?: number;
}