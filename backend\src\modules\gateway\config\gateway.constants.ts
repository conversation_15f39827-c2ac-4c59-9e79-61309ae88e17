/**
 * 网关模块常量定义
 * 集中管理API路径、队列名称、服务端点等配置
 * 确保多模块之间的一致性
 */



// 导入统一的API常量
import { ProcessingMode } from '@/shared/constants/api.constants';
export {
  API_PATHS,
  SERVICE_ENDPOINTS,
  QUEUE_CONFIGS,
  ProcessingMode,
  QueuePriority,
  TaskStatus,
  SUPPORTED_MIME_TYPES,
  FILE_SIZE_LIMITS,
  RATE_LIMIT_CONFIG,
  ERROR_CODES
} from '@/shared/constants/api.constants';

/**
 * API路由配置
 */
export const API_ROUTES = {
  // OCR服务路由配置
  LOGISTICS_OCR: {
    prefix: '/v1/op/ocr',
    target: 'OCR_SERVICE',
    methods: ['POST'],
    allowAsync: true,
    allowProxyAsync: true,
    defaultMode: ProcessingMode.PROXY_ASYNC,
    timeout: 30000, // ms
    maxProxyWaitTime: 60000, // 最大网关代理等待时间 (60秒)
    avgProcessingTime: 20000, // 平均处理时间估计 (20秒)
    queue: 'ocr',
  },

  // 申通面单OCR服务路由配置
  STO_OCR: {
    prefix: '/v1/op/ocr/sto',
    target: 'STO_OCR_SERVICE',
    methods: ['POST'],
    allowAsync: true,
    allowProxyAsync: true,
    defaultMode: ProcessingMode.PROXY_ASYNC,
    timeout: 35000, // 申通OCR可能需要更长处理时间
    maxProxyWaitTime: 70000, // 最大网关代理等待时间 (70秒)
    avgProcessingTime: 25000, // 平均处理时间估计 (25秒)
    queue: 'sto-ocr',
  },

  // 地址提取服务路由配置
  ADDRESS_EXTRACTION: {
    prefix: '/v1/op/address/extract',
    target: 'ADDRESS_SERVICE',
    methods: ['POST'],
    allowAsync: true,
    allowProxyAsync: true,
    defaultMode: ProcessingMode.PROXY_ASYNC,
    timeout: 10000, // ms
    maxProxyWaitTime: 30000, // 30秒
    avgProcessingTime: 8000, // 8秒
    queue: 'extract-address',
  },

  // 地址标准化服务路由配置
  ADDRESS_NORMALIZE: {
    prefix: '/v1/op/address/normalize',
    target: 'ADDRESS_SERVICE',
    methods: ['POST'],
    allowAsync: false,
    allowProxyAsync: false,
    defaultMode: ProcessingMode.SYNC,
    timeout: 5000, // ms
    avgProcessingTime: 2000, // 2秒
    queue: 'extract-address', // 使用相同的队列
    status: 'coming_soon', // 标记为即将推出
  },
  
  // 地理坐标服务配置
  GEO_COORDINATE: {
    prefix: '/v1/op/geo',
    paths: {
      REVERSE: {
        path: '/reverse',
        target: 'GEO_SERVICE',
        methods: ['GET', 'POST'],
        allowAsync: true,
        allowProxyAsync: true,
        defaultMode: ProcessingMode.SYNC,
        timeout: 5000, // ms
        queue: 'rev-geo',
      },
      FORWARD: {
        path: '/forward',
        target: 'GEO_SERVICE',
        methods: ['GET', 'POST'],
        allowAsync: true,
        allowProxyAsync: true,
        defaultMode: ProcessingMode.SYNC,
        timeout: 5000, // ms
        queue: 'rev-geo',
      }
    }
  }
};

/**
 * 网关配置常量
 */
export const GATEWAY_CONFIG = {
  // 请求大小阈值，超过此大小自动使用异步模式
  ASYNC_SIZE_THRESHOLD: 1024 * 1024, // 1MB
  
  // 最大代理等待时间
  MAX_PROXY_WAIT_TIME: 120000, // 2分钟
  
  // 文件上传限制
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  
  // 支持的文件类型
  ALLOWED_MIME_TYPES: [
    'image/jpeg',
    'image/png', 
    'image/gif',
    'application/pdf'
  ],
  
  // 并发限制
  MAX_CONCURRENT_PROXY_REQUESTS: 100,
  MAX_PROXY_REQUESTS_PER_CLIENT: 5,
  
  // 超时配置
  DEFAULT_TIMEOUT: 30000,
  DEFAULT_RETRY_ATTEMPTS: 3,
  DEFAULT_RETRY_DELAY: 1000,
  
  // 监控配置
  METRICS_COLLECTION_INTERVAL: 300000, // 5分钟
  HEALTH_CHECK_INTERVAL: 300000, // 5分钟（减少频繁的健康检查）
  ENABLE_HEALTH_CHECK: true, // 是否启用健康检查
};

/**
 * 错误码常量
 */
export const GATEWAY_ERROR_CODES = {
  ROUTE_NOT_FOUND: 'GATEWAY_ROUTE_NOT_FOUND',
  SERVICE_UNAVAILABLE: 'GATEWAY_SERVICE_UNAVAILABLE',
  REQUEST_TIMEOUT: 'GATEWAY_REQUEST_TIMEOUT',
  INVALID_PROCESSING_MODE: 'GATEWAY_INVALID_PROCESSING_MODE',
  FILE_TOO_LARGE: 'GATEWAY_FILE_TOO_LARGE',
  UNSUPPORTED_FILE_TYPE: 'GATEWAY_UNSUPPORTED_FILE_TYPE',
  PROXY_LIMIT_EXCEEDED: 'GATEWAY_PROXY_LIMIT_EXCEEDED',
  TASK_NOT_FOUND: 'GATEWAY_TASK_NOT_FOUND',
  INVALID_BASE64_IMAGE: 'GATEWAY_INVALID_BASE64_IMAGE',
};

/**
 * HTTP状态码映射
 */
export const HTTP_STATUS_MAPPING = {
  [GATEWAY_ERROR_CODES.ROUTE_NOT_FOUND]: 404,
  [GATEWAY_ERROR_CODES.SERVICE_UNAVAILABLE]: 503,
  [GATEWAY_ERROR_CODES.REQUEST_TIMEOUT]: 408,
  [GATEWAY_ERROR_CODES.INVALID_PROCESSING_MODE]: 400,
  [GATEWAY_ERROR_CODES.FILE_TOO_LARGE]: 413,
  [GATEWAY_ERROR_CODES.UNSUPPORTED_FILE_TYPE]: 415,
  [GATEWAY_ERROR_CODES.PROXY_LIMIT_EXCEEDED]: 429,
  [GATEWAY_ERROR_CODES.TASK_NOT_FOUND]: 404,
  [GATEWAY_ERROR_CODES.INVALID_BASE64_IMAGE]: 400,
};

/**
 * 默认响应消息
 */
export const DEFAULT_ERROR_MESSAGES = {
  [GATEWAY_ERROR_CODES.ROUTE_NOT_FOUND]: '请求的路由不存在',
  [GATEWAY_ERROR_CODES.SERVICE_UNAVAILABLE]: '服务暂时不可用',
  [GATEWAY_ERROR_CODES.REQUEST_TIMEOUT]: '请求超时',
  [GATEWAY_ERROR_CODES.INVALID_PROCESSING_MODE]: '无效的处理模式',
  [GATEWAY_ERROR_CODES.FILE_TOO_LARGE]: '文件大小超过限制',
  [GATEWAY_ERROR_CODES.UNSUPPORTED_FILE_TYPE]: '不支持的文件类型',
  [GATEWAY_ERROR_CODES.PROXY_LIMIT_EXCEEDED]: '代理请求数量超过限制',
  [GATEWAY_ERROR_CODES.TASK_NOT_FOUND]: '任务不存在',
  [GATEWAY_ERROR_CODES.INVALID_BASE64_IMAGE]: '无效的Base64图片格式',
};
