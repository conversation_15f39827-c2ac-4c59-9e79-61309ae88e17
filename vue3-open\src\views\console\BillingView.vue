<template>
  <div class="billing-page">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">账单管理</h1>
        <p class="page-subtitle">查看您的消费记录和账单详情</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="downloadBillBatch">
          <el-icon><Download /></el-icon>
          下载账单
        </el-button>
      </div>
    </div>
    
    <!-- 账户概览 -->
    <div class="account-overview">
      <div class="balance-card">
        <div class="card-header">
          <h3>账户余额</h3>
          <el-button type="primary" size="small" @click="showRechargeDialog = true">
            充值
          </el-button>
        </div>
        <div class="balance-amount">
          <span class="currency">¥</span>
          <span class="amount">{{ accountInfo.balance }}</span>
        </div>
        <div class="balance-info">
          <div class="info-item">
            <span class="label">本月消费:</span>
            <span class="value">¥{{ accountInfo.monthlySpending }}</span>
          </div>
          <div class="info-item">
            <span class="label">预计月消费:</span>
            <span class="value">¥{{ accountInfo.totalSpending }}</span>
          </div>
        </div>
      </div>
      
      <div class="usage-card">
        <h3>本月使用情况</h3>
        <div class="usage-stats">
          <div class="stat-item">
            <div class="stat-icon ocr">
              <el-icon><Picture /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ usageStats.ocr.calls }}</div>
                <div class="stat-label">OCR调用次数</div>
                <div class="stat-cost">¥{{ usageStats.ocr.cost }}</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon nlp">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ usageStats.nlp.calls }}</div>
                <div class="stat-label">NLP调用次数</div>
                <div class="stat-cost">¥{{ usageStats.nlp.cost }}</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon ai">
              <el-icon><MagicStick /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ usageStats.aiGeneration.calls }}</div>
                <div class="stat-label">AI生成调用次数</div>
                <div class="stat-cost">¥{{ usageStats.aiGeneration.cost }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 消费趋势图 -->
    <div class="chart-section">
      <div class="chart-card">
        <div class="chart-header">
          <h3>消费趋势</h3>
          <el-radio-group v-model="chartPeriod" size="small">
            <el-radio-button label="7d">7天</el-radio-button>
            <el-radio-button label="30d">30天</el-radio-button>
            <el-radio-button label="90d">90天</el-radio-button>
          </el-radio-group>
        </div>
        <div class="chart-content">
          <!-- 这里应该集成图表库 -->
          <div class="mock-chart">
            <div class="chart-area">
              <div class="chart-line">
                <div class="point" style="left: 10%; bottom: 30%"></div>
                <div class="point" style="left: 25%; bottom: 45%"></div>
                <div class="point" style="left: 40%; bottom: 60%"></div>
                <div class="point" style="left: 55%; bottom: 40%"></div>
                <div class="point" style="left: 70%; bottom: 75%"></div>
                <div class="point" style="left: 85%; bottom: 55%"></div>
              </div>
            </div>
            <div class="chart-labels">
              <span>1月</span>
              <span>2月</span>
              <span>3月</span>
              <span>4月</span>
              <span>5月</span>
              <span>6月</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 账单列表 -->
    <div class="bills-section">
      <div class="section-header">
        <h2>账单记录</h2>
        <div class="filters">
          <el-date-picker
            v-model="dateRange"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
          />
          
          <el-select v-model="statusFilter" placeholder="状态" style="width: 120px;">
            <el-option label="全部" value="" />
            <el-option label="已支付" value="paid" />
            <el-option label="未支付" value="unpaid" />
            <el-option label="已退款" value="refunded" />
          </el-select>
        </div>
      </div>
      
      <div class="bills-table">
        <el-table 
          :data="filteredBills" 
          v-loading="loading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="billNo" label="账单号" width="180" />
          
          <el-table-column prop="period" label="账单周期" width="120">
            <template #default="{ row }">
              {{ formatPeriod(row.period) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="amount" label="金额" width="100">
            <template #default="{ row }">
              <span class="amount-text">¥{{ row.amount }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getBillStatusType(row.status)" size="small">
                {{ formatBillStatus(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="paymentMethod" label="支付方式" width="120" />
          
          <el-table-column prop="createdAt" label="生成时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.createdAt) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="paidAt" label="支付时间" width="180">
            <template #default="{ row }">
              {{ row.paidAt ? formatDateTime(row.paidAt) : '-' }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button 
                text 
                size="small" 
                @click="viewBillDetail(row)"
              >
                详情
              </el-button>
              
              <el-button 
                text 
                size="small" 
                @click="downloadBillPdf(row)"
              >
                下载
              </el-button>
              
              <el-button 
                v-if="row.status === 'unpaid'"
                text 
                size="small" 
                type="primary"
                @click="payBill(row)"
              >
                支付
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50]"
            :total="totalBills"
            layout="total, sizes, prev, pager, next, jumper"
          />
        </div>
      </div>
    </div>
    
    <!-- 充值对话框 -->
    <el-dialog
      v-model="showRechargeDialog"
      title="账户充值"
      width="500px"
    >
      <div class="recharge-form">
        <div class="amount-selection">
          <h4>选择充值金额</h4>
          <div class="amount-options">
            <div 
              class="amount-option"
              v-for="amount in rechargeAmounts"
              :key="amount"
              :class="{ active: selectedAmount === amount }"
              @click="selectedAmount = amount"
            >
              ¥{{ amount }}
            </div>
          </div>
          
          <div class="custom-amount">
            <el-input
              v-model="customAmount"
              placeholder="自定义金额"
              type="number"
              :min="1"
              :max="10000"
              @input="selectedAmount = null"
            >
              <template #prepend>¥</template>
            </el-input>
          </div>
        </div>
        
        <div class="payment-method">
          <h4>支付方式</h4>
          <el-radio-group v-model="paymentMethod">
            <el-radio label="alipay">
              <div class="payment-option">
                <el-icon><CreditCard /></el-icon>
                <span>支付宝</span>
              </div>
            </el-radio>
            <el-radio label="wechat">
              <div class="payment-option">
                <el-icon><CreditCard /></el-icon>
                <span>微信支付</span>
              </div>
            </el-radio>
            <el-radio label="bank">
              <div class="payment-option">
                <el-icon><CreditCard /></el-icon>
                <span>银行卡</span>
              </div>
            </el-radio>
          </el-radio-group>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showRechargeDialog = false">取消</el-button>
        <el-button 
          type="primary" 
          :loading="recharging"
          @click="submitRecharge"
        >
          确认充值
        </el-button>
      </template>
    </el-dialog>
    
    <!-- 账单详情对话框 -->
    <el-dialog
      v-model="showBillDialog"
      title="账单详情"
      width="600px"
    >
      <div v-if="selectedBill" class="bill-detail">
        <div class="bill-header">
          <div class="bill-info">
            <h3>{{ selectedBill.billNo }}</h3>
            <p>{{ formatPeriod(selectedBill.period) }}</p>
          </div>
          <div class="bill-amount">
            <span class="amount">¥{{ selectedBill.amount }}</span>
            <el-tag :type="getBillStatusType(selectedBill.status)" size="small">
              {{ formatBillStatus(selectedBill.status) }}
            </el-tag>
          </div>
        </div>
        
        <div class="bill-breakdown">
          <h4>费用明细</h4>
          <div class="breakdown-list">
            <div class="breakdown-item" v-for="item in selectedBill.breakdown" :key="item.service">
              <div class="item-info">
                <span class="service">{{ item.service }}</span>
                <span class="usage">{{ item.calls }}次调用</span>
              </div>
              <div class="item-cost">¥{{ item.cost }}</div>
            </div>
          </div>
        </div>
        
        <div class="bill-summary">
          <div class="summary-row">
            <span class="label">小计:</span>
            <span class="value">¥{{ selectedBill.subtotal }}</span>
          </div>
          <div class="summary-row">
            <span class="label">税费:</span>
            <span class="value">¥{{ selectedBill.tax }}</span>
          </div>
          <div class="summary-row total">
            <span class="label">总计:</span>
            <span class="value">¥{{ selectedBill.amount }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Download,
  Picture,
  ChatDotRound,
  MagicStick,
  CreditCard
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { useBillingStore } from '@/stores/billing'

const recharging = ref(false)
const showRechargeDialog = ref(false)
const showBillDialog = ref(false)
const selectedBill = ref<any>(null)
const chartPeriod = ref('30d')
const dateRange = ref<[string, string]>([dayjs().subtract(6, 'month').format('YYYY-MM'), dayjs().format('YYYY-MM')])
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalBills = ref(0)

const billingStore = useBillingStore()

// 响应式引用
const { 
  accountInfo,
  usageStats,
  billRecords,
  loading,
  getAccountInfo,
  getUsageStats,
  getBillRecords,
  topUpAccount,
  downloadBill,
  batchDownloadBills
} = billingStore

// 充值相关
const selectedAmount = ref<number | null>(100)
const customAmount = ref('')
const paymentMethod = ref('alipay')
const rechargeAmounts = [50, 100, 200, 500, 1000, 2000]



// 计算属性
const filteredBills = computed(() => {
  let filtered = billRecords
  
  if (statusFilter.value) {
    filtered = filtered.filter((bill: any) => bill.status === statusFilter.value)
  }
  
  return filtered
})

// 格式化账单周期
const formatPeriod = (period: string) => {
  return dayjs(period).format('YYYY年MM月')
}

// 格式化日期时间
const formatDateTime = (date: Date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 获取账单状态类型
const getBillStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    paid: 'success',
    unpaid: 'warning',
    refunded: 'info'
  }
  return typeMap[status] || 'info'
}

// 格式化账单状态
const formatBillStatus = (status: string) => {
  const statusMap: Record<string, string> = {
    paid: '已支付',
    unpaid: '未支付',
    refunded: '已退款'
  }
  return statusMap[status] || status
}

// 查看账单详情
const viewBillDetail = (bill: any) => {
  selectedBill.value = bill
  showBillDialog.value = true
}

// 下载账单PDF
const downloadBillPdf = async (bill: any) => {
  try {
    await downloadBill(bill.id)
  } catch (error) {
    ElMessage.error('下载失败，请稍后重试')
  }
}

// 支付账单
const payBill = async (bill: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要支付账单 ${bill.billNo} (¥${bill.amount}) 吗？`,
      '确认支付',
      {
        confirmButtonText: '确认支付',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用支付API
    bill.status = 'paid'
    bill.paidAt = new Date()
    bill.paymentMethod = '支付宝'
    
    ElMessage.success('支付成功')
  } catch {
    // 用户取消
  }
}

// 下载账单
const downloadBillBatch = async () => {
  try {
    const billIds = filteredBills.value.map((bill: any) => bill.id)
    await batchDownloadBills(billIds)
  } catch (error) {
    ElMessage.error('批量下载失败，请稍后重试')
  }
}

// 提交充值
const submitRecharge = async () => {
  const amount = selectedAmount.value || parseFloat(customAmount.value)
  
  if (!amount || amount <= 0) {
    ElMessage.error('请选择或输入有效的充值金额')
    return
  }
  
  if (!paymentMethod.value) {
    ElMessage.error('请选择支付方式')
    return
  }
  
  try {
    recharging.value = true
    
    await topUpAccount({
      amount: amount,
      paymentMethod: paymentMethod.value
    })
    
    showRechargeDialog.value = false
    
    // 重置表单
    selectedAmount.value = 100
    customAmount.value = ''
    paymentMethod.value = 'alipay'
  } catch (error) {
    ElMessage.error('充值失败，请重试')
  } finally {
    recharging.value = false
  }
}

// 加载账单数据
const loadBills = async () => {
  try {
    await Promise.all([
      getAccountInfo(),
      getUsageStats(),
      getBillRecords({
        page: currentPage.value,
        pageSize: pageSize.value,
        dateRange: dateRange.value,
        status: statusFilter.value
      })
    ])
    totalBills.value = billRecords.length
  } catch (error) {
    ElMessage.error('加载账单数据失败')
  }
}

onMounted(() => {
  loadBills()
})
</script>

<style scoped>
.billing-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #606266;
  margin: 0;
  font-size: 1rem;
}

.header-right {
  flex-shrink: 0;
}

.account-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.balance-card,
.usage-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.balance-amount {
  display: flex;
  align-items: baseline;
  margin-bottom: 20px;
}

.currency {
  font-size: 1.5rem;
  color: #409eff;
  margin-right: 4px;
}

.amount {
  font-size: 3rem;
  font-weight: 600;
  color: #409eff;
}

.balance-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item .label {
  color: #606266;
  font-size: 14px;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
}

.usage-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.stat-icon.ocr {
  background: #e3f2fd;
  color: #1976d2;
}

.stat-icon.nlp {
  background: #e8f5e8;
  color: #388e3c;
}

.stat-icon.ai {
  background: #fff3e0;
  color: #f57c00;
}

.stat-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.stat-cost {
  color: #409eff;
  font-weight: 500;
}

.chart-section {
  margin-bottom: 32px;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.chart-content {
  height: 300px;
}

.mock-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-area {
  flex: 1;
  position: relative;
  background: linear-gradient(to right, #f5f7fa 0%, #f5f7fa 100%);
  border-radius: 8px;
  margin-bottom: 16px;
}

.chart-line {
  position: relative;
  width: 100%;
  height: 100%;
}

.point {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #409eff;
  border-radius: 50%;
  transform: translate(-50%, 50%);
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #606266;
}

.bills-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.filters {
  display: flex;
  gap: 12px;
}

.bills-table {
  margin-bottom: 24px;
}

.amount-text {
  font-weight: 600;
  color: #409eff;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.recharge-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.amount-selection h4,
.payment-method h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.amount-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.amount-option {
  padding: 12px;
  text-align: center;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
}

.amount-option:hover {
  border-color: #409eff;
}

.amount-option.active {
  border-color: #409eff;
  background: #ecf5ff;
  color: #409eff;
}

.payment-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bill-detail {
  max-height: 60vh;
  overflow-y: auto;
}

.bill-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.bill-info h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.bill-info p {
  color: #606266;
  margin: 0;
}

.bill-amount {
  text-align: right;
}

.bill-amount .amount {
  display: block;
  font-size: 1.5rem;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 8px;
}

.bill-breakdown {
  margin-bottom: 24px;
}

.bill-breakdown h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.breakdown-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.service {
  font-weight: 500;
  color: #303133;
}

.usage {
  font-size: 12px;
  color: #606266;
}

.item-cost {
  font-weight: 600;
  color: #409eff;
}

.bill-summary {
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.summary-row.total {
  font-size: 1.125rem;
  font-weight: 600;
  color: #303133;
  border-top: 1px solid #e4e7ed;
  padding-top: 8px;
  margin-top: 8px;
}

.summary-row .label {
  color: #606266;
}

.summary-row .value {
  color: #303133;
  font-weight: 500;
}

.summary-row.total .value {
  color: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .account-overview {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .filters {
    flex-direction: column;
  }
  
  .amount-options {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .bill-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .bill-amount {
    text-align: left;
  }
}
</style>