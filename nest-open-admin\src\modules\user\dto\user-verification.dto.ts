import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsBoolean,
  <PERSON><PERSON>ength,
  MaxLength,
  Min,
  IsInt,
  Max,
  IsIn,
  IsDateString,
  IsUrl,
  Length,
  Matches,
  IsArray,
} from 'class-validator';

/**
 * 审核状态枚举
 */
export enum VerificationStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

/**
 * 证件类型枚举
 */
export enum IdType {
  ID_CARD = 'id_card',
  PASSPORT = 'passport',
  OTHER = 'other',
}

/**
 * 创建实名认证DTO
 */
export class CreateUserVerificationDto {
  @ApiProperty({ description: '用户ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  userId: number;

  @ApiProperty({ description: '真实姓名', example: '张三' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  realName: string;

  @ApiProperty({ description: '证件类型', enum: IdType, example: IdType.ID_CARD })
  @IsEnum(IdType)
  @IsNotEmpty()
  idType: IdType;

  @ApiProperty({ description: '证件号码', example: '110101199001011234' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(30)
  idNumber: string;

  @ApiProperty({ description: '证件正面照片URL', example: 'https://example.com/id_front.jpg' })
  @IsString()
  @IsNotEmpty()
  @IsUrl()
  idFrontImage: string;

  @ApiProperty({ description: '证件背面照片URL', example: 'https://example.com/id_back.jpg' })
  @IsString()
  @IsNotEmpty()
  @IsUrl()
  idBackImage: string;

  @ApiPropertyOptional({ description: '手持证件照片URL', example: 'https://example.com/id_hold.jpg' })
  @IsOptional()
  @IsString()
  @IsUrl()
  idHoldImage?: string;

  @ApiPropertyOptional({ description: '备注信息', example: '补充说明' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  remark?: string;
}

/**
 * 更新实名认证DTO
 */
export class UpdateUserVerificationDto {
  @ApiPropertyOptional({ description: '真实姓名' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  realName?: string;

  @ApiPropertyOptional({ description: '证件类型', enum: IdType })
  @IsOptional()
  @IsEnum(IdType)
  idType?: IdType;

  @ApiPropertyOptional({ description: '证件号码' })
  @IsOptional()
  @IsString()
  @MaxLength(30)
  idNumber?: string;

  @ApiPropertyOptional({ description: '证件正面照片URL' })
  @IsOptional()
  @IsString()
  @IsUrl()
  idFrontImage?: string;

  @ApiPropertyOptional({ description: '证件背面照片URL' })
  @IsOptional()
  @IsString()
  @IsUrl()
  idBackImage?: string;

  @ApiPropertyOptional({ description: '手持证件照片URL' })
  @IsOptional()
  @IsString()
  @IsUrl()
  idHoldImage?: string;

  @ApiPropertyOptional({ description: '备注信息' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  remark?: string;
}

/**
 * 审核实名认证DTO
 */
export class ReviewUserVerificationDto {
  @ApiProperty({ description: '审核状态', enum: VerificationStatus, example: VerificationStatus.APPROVED })
  @IsEnum(VerificationStatus)
  @IsNotEmpty()
  status: VerificationStatus;

  @ApiProperty({ description: '审核人ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  reviewerId: number;

  @ApiPropertyOptional({ description: '审核备注', example: '身份信息核实无误，审核通过' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  reviewRemark?: string;
}

/**
 * 实名认证查询DTO
 */
export class QueryUserVerificationDto {
  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', example: 10, minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: '用户ID', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  userId?: number;

  @ApiPropertyOptional({ description: '审核状态', enum: VerificationStatus })
  @IsOptional()
  @IsEnum(VerificationStatus)
  status?: VerificationStatus;

  @ApiPropertyOptional({ description: '证件类型', enum: IdType })
  @IsOptional()
  @IsEnum(IdType)
  idType?: IdType;

  @ApiPropertyOptional({ description: '审核人ID', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  reviewerId?: number;

  @ApiPropertyOptional({ description: '真实姓名搜索', example: '张' })
  @IsOptional()
  @IsString()
  realName?: string;

  @ApiPropertyOptional({ description: '证件号码搜索', example: '110101' })
  @IsOptional()
  @IsString()
  idNumber?: string;

  @ApiPropertyOptional({ description: '用户名搜索', example: 'john' })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiPropertyOptional({ description: '用户邮箱搜索', example: '<EMAIL>' })
  @IsOptional()
  @IsString()
  userEmail?: string;

  @ApiPropertyOptional({ description: '提交开始时间' })
  @IsOptional()
  @IsDateString()
  submittedStartDate?: string;

  @ApiPropertyOptional({ description: '提交结束时间' })
  @IsOptional()
  @IsDateString()
  submittedEndDate?: string;

  @ApiPropertyOptional({ description: '审核开始时间' })
  @IsOptional()
  @IsDateString()
  reviewedStartDate?: string;

  @ApiPropertyOptional({ description: '审核结束时间' })
  @IsOptional()
  @IsDateString()
  reviewedEndDate?: string;

  @ApiPropertyOptional({ description: '排序字段', example: 'submittedAt' })
  @IsOptional()
  @IsString()
  @IsIn(['id', 'status', 'idType', 'submittedAt', 'reviewedAt', 'realName', 'createdAt'])
  sortBy?: string = 'submittedAt';

  @ApiPropertyOptional({ description: '排序方向', example: 'DESC' })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}

/**
 * 实名认证响应DTO
 */
export class UserVerificationResponseDto {
  @ApiProperty({ description: '认证ID', example: 1 })
  id: number;

  @ApiProperty({ description: '用户ID', example: 1 })
  userId: number;

  @ApiProperty({ description: '真实姓名' })
  realName: string;

  @ApiProperty({ description: '证件类型', enum: IdType })
  idType: IdType;

  @ApiProperty({ description: '证件号码' })
  idNumber: string;

  @ApiProperty({ description: '证件正面照片URL' })
  idFrontImage: string;

  @ApiProperty({ description: '证件背面照片URL' })
  idBackImage: string;

  @ApiPropertyOptional({ description: '手持证件照片URL' })
  idHoldImage?: string;

  @ApiProperty({ description: '审核状态', enum: VerificationStatus })
  status: VerificationStatus;

  @ApiPropertyOptional({ description: '提交时间' })
  submittedAt?: Date;

  @ApiPropertyOptional({ description: '审核时间' })
  reviewedAt?: Date;

  @ApiPropertyOptional({ description: '审核人ID' })
  reviewerId?: number;

  @ApiPropertyOptional({ description: '审核备注' })
  reviewRemark?: string;

  @ApiPropertyOptional({ description: '备注信息' })
  remark?: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 关联信息
  @ApiPropertyOptional({ description: '用户信息' })
  user?: {
    id: number;
    username: string;
    email: string;
    phone?: string;
    type: string;
    status: string;
    tier: string;
  };

  @ApiPropertyOptional({ description: '审核人信息' })
  reviewer?: {
    id: number;
    username: string;
    email: string;
    realName?: string;
  };

  // 脱敏信息（用于列表显示）
  @ApiProperty({ description: '脱敏证件号码', example: '110101****1234' })
  maskedIdNumber: string;
}

/**
 * 实名认证列表响应DTO
 */
export class UserVerificationListResponseDto {
  @ApiProperty({ description: '实名认证列表', type: [UserVerificationResponseDto] })
  data: UserVerificationResponseDto[];

  @ApiProperty({ description: '总数', example: 200 })
  total: number;

  @ApiProperty({ description: '当前页', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  limit: number;

  @ApiProperty({ description: '总页数', example: 20 })
  totalPages: number;
}

/**
 * 实名认证统计DTO
 */
export class UserVerificationStatsDto {
  @ApiProperty({ description: '总申请数', example: 1000 })
  totalApplications: number;

  @ApiProperty({ description: '待审核数', example: 100 })
  pendingApplications: number;

  @ApiProperty({ description: '已通过数', example: 800 })
  approvedApplications: number;

  @ApiProperty({ description: '已拒绝数', example: 100 })
  rejectedApplications: number;

  @ApiProperty({ description: '通过率', example: 88.9 })
  approvalRate: number;

  @ApiProperty({ description: '平均审核时间（小时）', example: 12.5 })
  avgReviewTime: number;

  @ApiProperty({ description: '按审核状态分组统计' })
  byStatus: Record<VerificationStatus, number>;

  @ApiProperty({ description: '按证件类型分组统计' })
  byIdType: Record<IdType, number>;

  @ApiProperty({ description: '按月份分组统计' })
  byMonth: Record<string, {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
  }>;

  @ApiProperty({ description: '按审核人分组统计' })
  byReviewer: Record<string, {
    total: number;
    approved: number;
    rejected: number;
    avgReviewTime: number;
  }>;

  @ApiProperty({ description: '今日新增申请数', example: 10 })
  todayApplications: number;

  @ApiProperty({ description: '本周新增申请数', example: 50 })
  weekApplications: number;

  @ApiProperty({ description: '本月新增申请数', example: 200 })
  monthApplications: number;

  @ApiProperty({ description: '待审核超过24小时的数量', example: 5 })
  overdueApplications: number;
}

/**
 * 批量审核实名认证DTO
 */
export class BatchReviewUserVerificationDto {
  @ApiProperty({ description: '实名认证ID列表', example: [1, 2, 3] })
  @IsArray()
  @IsInt({ each: true })
  @IsNotEmpty()
  ids: number[];

  @ApiProperty({ description: '审核状态', enum: VerificationStatus })
  @IsEnum(VerificationStatus)
  @IsNotEmpty()
  status: VerificationStatus;

  @ApiProperty({ description: '审核人ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  reviewerId: number;

  @ApiPropertyOptional({ description: '审核备注' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  reviewRemark?: string;
}

/**
 * 身份证OCR识别结果DTO
 */
export class IdCardOcrResultDto {
  @ApiProperty({ description: '姓名', example: '张三' })
  name: string;

  @ApiProperty({ description: '身份证号', example: '110101199001011234' })
  idNumber: string;

  @ApiProperty({ description: '性别', example: '男' })
  gender: string;

  @ApiProperty({ description: '民族', example: '汉' })
  nation: string;

  @ApiProperty({ description: '出生日期', example: '1990-01-01' })
  birth: string;

  @ApiProperty({ description: '地址', example: '北京市朝阳区xxx街道xxx号' })
  address: string;

  @ApiPropertyOptional({ description: '签发机关', example: 'xx市公安局' })
  authority?: string;

  @ApiPropertyOptional({ description: '有效期限', example: '2020-01-01-2030-01-01' })
  validPeriod?: string;

  @ApiProperty({ description: '识别置信度', example: 0.95 })
  confidence: number;
}

/**
 * OCR识别请求DTO
 */
export class OcrRecognitionDto {
  @ApiProperty({ description: '图片URL或Base64', example: 'https://example.com/id_card.jpg' })
  @IsString()
  @IsNotEmpty()
  image: string;

  @ApiProperty({ description: '证件类型', enum: IdType, example: IdType.ID_CARD })
  @IsEnum(IdType)
  @IsNotEmpty()
  idType: IdType;

  @ApiProperty({ description: '证件面（正面/背面）', example: 'front' })
  @IsString()
  @IsIn(['front', 'back'])
  side: 'front' | 'back';
}