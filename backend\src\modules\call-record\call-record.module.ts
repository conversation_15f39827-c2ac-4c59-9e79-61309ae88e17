import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CallRecordController } from './call-record.controller';
import { CallRecordService } from './call-record.service';
import { CallRecordEntity } from './entities/call-record.entity';
import { CallStatisticsEntity } from './entities/call-statistics.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([CallRecordEntity, CallStatisticsEntity]),
  ],
  controllers: [CallRecordController],
  providers: [CallRecordService],
  exports: [CallRecordService],
})
export class CallRecordModule {}
