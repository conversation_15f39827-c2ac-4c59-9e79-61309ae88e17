import { ApiProperty } from '@nestjs/swagger';

/**
 * 单个队列状态DTO
 */
export class QueueStatusDto {
  @ApiProperty({
    description: '队列名称',
    example: 'ocr-tasks',
  })
  name: string;

  @ApiProperty({
    description: '等待中的任务数',
    example: 5,
  })
  waiting: number;

  @ApiProperty({
    description: '处理中的任务数',
    example: 3,
  })
  active: number;

  @ApiProperty({
    description: '已完成的任务数',
    example: 150,
  })
  completed: number;

  @ApiProperty({
    description: '失败的任务数',
    example: 2,
  })
  failed: number;
}

/**
 * 所有队列状态DTO
 */
export class AllQueuesStatusDto {
  @ApiProperty({
    description: '所有队列的状态',
    type: [QueueStatusDto],
  })
  queues: QueueStatusDto[];
} 