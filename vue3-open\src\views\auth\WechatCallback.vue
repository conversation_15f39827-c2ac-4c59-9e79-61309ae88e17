<template>
  <div class="wechat-callback">
    <div class="callback-container">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <h2>{{ isProcessing ? '正在处理微信授权...' : '授权完成' }}</h2>
        <p v-if="isProcessing">请稍候，我们正在验证您的微信信息</p>
        <p v-else-if="error" class="error-message">{{ error }}</p>
        <p v-else class="success-message">授权成功，即将跳转...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const isProcessing = ref(true)
const error = ref('')

// 处理微信授权回调
const handleWechatCallback = async () => {
  try {
    const { code, state, type } = route.query
    
    // 验证参数
    if (!code) {
      throw new Error('授权失败：缺少授权码')
    }
    
    if (!state) {
      throw new Error('授权失败：缺少状态参数')
    }
    
    // 验证state参数
    const savedState = localStorage.getItem('wechat_auth_state')
    if (state !== savedState) {
      throw new Error('授权失败：状态参数不匹配')
    }
    
    // 清除保存的state
    localStorage.removeItem('wechat_auth_state')
    
    // 调用后端API处理微信授权
    const response = await fetch('/open/v1/auth/wechat/callback', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        code,
        state,
        type: type || 'login'
      })
    })
    
    const data = await response.json()
    
    if (data.success) {
      isProcessing.value = false
      
      if (type === 'register') {
        ElMessage.success('微信注册成功！')
        
        // 如果返回了token，直接登录
        if (data.token) {
          // userStore.setToken(data.token)
          // userStore.setUserInfo(data.userInfo)
          setTimeout(() => {
            router.push('/console')
          }, 1500)
        } else {
          // 跳转到登录页面
          setTimeout(() => {
            router.push('/login')
          }, 1500)
        }
      } else {
        ElMessage.success('微信登录成功！')
        
        // 设置用户信息并跳转
        // userStore.setToken(data.token)
        // userStore.setUserInfo(data.userInfo)
        
        setTimeout(() => {
          router.push('/console')
        }, 1500)
      }
    } else {
      throw new Error(data.message || '授权处理失败')
    }
  } catch (error: any) {
    isProcessing.value = false
    error.value = error.message || '处理微信授权时发生错误'
    ElMessage.error(error.value)
    
    // 3秒后跳转回登录页面
    setTimeout(() => {
      router.push('/login')
    }, 3000)
  }
}

onMounted(() => {
  handleWechatCallback()
})
</script>

<style scoped>
.wechat-callback {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.callback-container {
  width: 100%;
  max-width: 400px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  text-align: center;
}

.loading-content h2 {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 20px 0 10px;
}

.loading-content p {
  font-size: 14px;
  color: #606266;
  margin: 10px 0;
  line-height: 1.5;
}

.error-message {
  color: #f56c6c !important;
}

.success-message {
  color: #67c23a !important;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .callback-container {
    margin: 20px;
    padding: 30px 20px;
  }
  
  .loading-content h2 {
    font-size: 20px;
  }
  
  .loading-content p {
    font-size: 13px;
  }
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    animation: none;
    border-top-color: #409eff;
  }
}
</style>