import { Module, forwardRef } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { AddressController } from './address.controller';
import { AddressService } from './address.service';
import { QueueModule } from '../queue/queue.module';
import { AddressExecutorService } from './services/address-executor.service';

@Module({
  imports: [
    HttpModule,
    ConfigModule,
    forwardRef(() => QueueModule),
  ],
  controllers: [AddressController],
  providers: [
    AddressService,
    AddressExecutorService
  ],
  exports: [
    AddressService,
    AddressExecutorService
  ],
})
export class AddressModule {}