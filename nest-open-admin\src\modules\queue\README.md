# 队列模块 (Queue Module)

基于Bull队列的异步任务处理系统，专注于API调用和调用次数预警功能。

## 功能特性

- ✅ **API调用任务**: 支持HTTP请求的异步执行
- ✅ **调用次数预警**: 当使用量达到阈值时自动发送告警
- ✅ **任务重试**: 支持失败任务的自动重试
- ✅ **任务监控**: 实时监控任务状态和队列健康
- ✅ **事件驱动**: 支持任务生命周期事件监听
- ✅ **RESTful API**: 提供完整的HTTP接口

## 快速开始

### 1. 模块导入

```typescript
import { Module } from '@nestjs/common';
import { QueueModule } from './modules/queue';

@Module({
  imports: [
    QueueModule.forRoot(), // 根模块导入
    // 或者
    QueueModule.forFeature(), // 功能模块导入
  ],
})
export class AppModule {}
```

### 2. 环境变量配置

```bash
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_password
REDIS_DB=0

# Bull队列配置
BULL_REMOVE_ON_COMPLETE=10
BULL_REMOVE_ON_FAIL=5
BULL_ATTEMPTS=3
```

### 3. 基本使用

#### 添加API调用任务

```typescript
import { Injectable } from '@nestjs/common';
import { QueueManagerService } from './modules/queue';

@Injectable()
export class YourService {
  constructor(
    private readonly queueManager: QueueManagerService,
  ) {}

  async callExternalApi() {
    const job = await this.queueManager.addApiCallTask({
      url: 'https://api.example.com/data',
      method: 'GET',
      headers: {
        'Authorization': 'Bearer token',
      },
      timeout: 30000,
      priority: 5,
      userId: 1,
    });

    console.log(`API调用任务已创建: ${job.id}`);
    return job;
  }
}
```

#### 添加使用量预警

```typescript
async checkUsageAndAlert(userId: number, currentUsage: number, totalLimit: number) {
  const usagePercent = (currentUsage / totalLimit) * 100;
  
  // 当使用量超过80%时发送预警
  if (usagePercent >= 80) {
    const job = await this.queueManager.addUsageWarningTask(userId, usagePercent);
    console.log(`使用量预警已发送: ${usagePercent}%`);
    return job;
  }
}
```

## API接口

### 任务管理

```bash
# 添加API调用任务
POST /queue/api-call
{
  "url": "https://api.example.com/data",
  "method": "GET",
  "headers": { "Authorization": "Bearer token" },
  "timeout": 30000,
  "userId": 1
}

# 添加使用量预警
POST /queue/usage-warning
{
  "userId": 1,
  "usagePercent": 85
}

# 获取任务状态
GET /queue/task/{queueName}/{jobId}

# 取消任务
DELETE /queue/task/{queueName}/{jobId}

# 重试任务
PATCH /queue/task/{queueName}/{jobId}/retry
```

### 队列监控

```bash
# 获取所有队列状态
GET /queue/status

# 获取队列统计
GET /queue/stats/{queueName}

# 获取系统健康状态
GET /queue/health

# 暂停队列
PATCH /queue/pause/{queueName}

# 恢复队列
PATCH /queue/resume/{queueName}

# 清理队列
DELETE /queue/clean/{queueName}?type=completed&age=3600000
```

## 事件监听

```typescript
import { Injectable, OnModuleInit } from '@nestjs/common';
import { QueueManagerService } from './modules/queue';

@Injectable()
export class EventListenerService implements OnModuleInit {
  constructor(
    private readonly queueManager: QueueManagerService,
  ) {}

  onModuleInit() {
    // 监听任务完成事件
    this.queueManager.onTaskCompleted((event) => {
      console.log('任务完成:', event.jobId, event.data);
    });

    // 监听任务失败事件
    this.queueManager.onTaskFailed((event) => {
      console.error('任务失败:', event.jobId, event.error);
    });

    // 监听任务进度事件
    this.queueManager.onTaskProgress((event) => {
      console.log('任务进度:', event.jobId, event.data);
    });
  }
}
```

## 架构设计

### 目录结构

```
src/modules/queue/
├── config/
│   └── bull.config.ts          # Bull队列配置
├── dto/
│   └── task.dto.ts             # 任务DTO定义
├── interfaces/
│   └── queue.interface.ts      # 接口定义
├── processors/
│   ├── api.processor.ts        # API调用处理器
│   └── alert.processor.ts      # 告警处理器
├── services/
│   ├── bull-queue.service.ts   # Bull队列服务
│   └── queue-manager.service.ts # 队列管理服务
├── queue.constants.ts          # 常量定义
├── queue.controller.ts         # HTTP控制器
├── queue.module.ts            # 模块定义
├── index.ts                   # 导出文件
└── README.md                  # 使用说明
```

### 核心组件

1. **QueueManagerService**: 统一的队列管理入口
2. **BullQueueService**: Bull队列的具体实现
3. **ApiProcessor**: API调用任务处理器
4. **AlertProcessor**: 告警任务处理器
5. **QueueController**: HTTP API控制器

### 设计原则

- **单一职责**: 每个服务和处理器都有明确的职责
- **依赖注入**: 使用NestJS的依赖注入系统
- **事件驱动**: 支持任务生命周期事件
- **可扩展性**: 易于添加新的任务类型
- **可监控性**: 提供完整的监控和统计功能

## 最佳实践

### 1. 任务设计

- 确保任务具有幂等性
- 合理设置任务超时时间
- 使用适当的优先级
- 添加必要的元数据

### 2. 错误处理

- 区分可重试和不可重试的错误
- 设置合理的重试次数和间隔
- 记录详细的错误日志
- 及时处理失败任务

### 3. 性能优化

- 根据业务需求调整并发数
- 定期清理已完成的任务
- 监控队列长度和处理速度
- 合理配置Redis连接池

### 4. 监控告警

- 设置队列长度告警
- 监控任务失败率
- 跟踪处理时间
- 检查Redis连接状态

## 故障排查

### 常见问题

1. **Redis连接失败**
   - 检查Redis服务状态
   - 验证连接配置
   - 检查网络连通性

2. **任务处理缓慢**
   - 检查处理器性能
   - 调整并发数设置
   - 优化任务逻辑

3. **内存使用过高**
   - 检查任务数据大小
   - 调整队列清理策略
   - 优化数据结构

### 调试技巧

- 启用详细日志记录
- 使用Bull Dashboard监控
- 分析任务执行时间
- 检查错误日志

## 扩展开发

### 添加新的任务类型

1. 在`queue.constants.ts`中添加新的队列名称和任务类型
2. 在`interfaces/queue.interface.ts`中定义任务数据接口
3. 在`dto/task.dto.ts`中创建对应的DTO
4. 创建新的处理器文件
5. 在`queue.module.ts`中注册新的队列和处理器
6. 在`QueueManagerService`中添加相应的方法

### 示例：添加邮件任务

```typescript
// 1. 添加常量
export const QUEUE_NAMES = {
  API_CALL: 'api-call',
  ALERT: 'alert',
  EMAIL: 'email', // 新增
} as const;

// 2. 定义接口
export interface EmailTaskData extends BaseTask {
  type: 'email';
  to: string;
  subject: string;
  content: string;
}

// 3. 创建处理器
@Processor(QUEUE_NAMES.EMAIL)
export class EmailProcessor {
  @Process()
  async handleEmail(job: Job<EmailTaskData>): Promise<TaskResult> {
    // 邮件发送逻辑
  }
}
```

## 版本历史

### v1.0.0 (当前版本)
- 实现API调用任务处理
- 实现调用次数预警功能
- 提供完整的HTTP API
- 支持任务监控和管理
- 基于Bull队列的可靠实现