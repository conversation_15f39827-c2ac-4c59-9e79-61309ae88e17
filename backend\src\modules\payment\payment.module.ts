import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { PaymentController } from './payment.controller';
import { PaymentService } from './payment.service';
import { PaymentEntity } from './entities/payment.entity';
import { AlipayService } from './services/alipay.service';
import { WechatPayService } from './services/wechat-pay.service';
import { BalancePayService } from './services/balance-pay.service';
import { OrderModule } from '../order/order.module';
import { UserModule } from '../user/user.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PaymentEntity,
    ]),
    forwardRef(() => OrderModule),
    forwardRef(() => UserModule),
    EventEmitterModule.forRoot(),
  ],
  controllers: [PaymentController],
  providers: [
    PaymentService,
    AlipayService,
    WechatPayService,
    BalancePayService,
  ],
  exports: [
    PaymentService,
    AlipayService,
    WechatPayService,
    BalancePayService,
  ],
})
export class PaymentModule {}
