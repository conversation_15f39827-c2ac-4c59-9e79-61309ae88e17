import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsBoolean,
  MinLength,
  MaxLength,
  Min,
  IsInt,
  Max,
  IsIn,
  IsDateString,
  IsUrl,
  IsArray,
  IsPhoneNumber,
  Length,
} from 'class-validator';

/**
 * 审核状态枚举
 */
export enum ReviewStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

/**
 * 创建企业认证DTO
 */
export class CreateUserCompanyDto {
  @ApiProperty({ description: '用户ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  userId: number;

  @ApiProperty({ description: '企业名称', example: '北京科技有限公司' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  enterprise: string;

  @ApiProperty({ description: '营业执照号', example: '91110000123456789X' })
  @IsString()
  @IsNotEmpty()
  @Length(15, 18)
  licenseNo: string;

  @ApiProperty({ description: '营业执照照片URL', example: 'https://example.com/license.jpg' })
  @IsString()
  @IsNotEmpty()
  @IsUrl()
  licenseImage: string;

  @ApiProperty({ description: '法人姓名', example: '张三' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(30)
  legalPerson: string;

  @ApiProperty({ description: '企业地址', example: '北京市朝阳区xxx街道xxx号' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(200)
  address: string;

  @ApiProperty({ description: '联系人姓名', example: '李四' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(30)
  contactName: string;

  @ApiProperty({ description: '联系人电话', example: '010-12345678' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(30)
  contactNumber: string;

  @ApiProperty({ description: '联系人手机', example: '13800138000' })
  @IsString()
  @IsNotEmpty()
  @IsPhoneNumber('CN')
  @Length(11, 11)
  contactPhone: string;
}

/**
 * 更新企业认证DTO
 */
export class UpdateUserCompanyDto {
  @ApiPropertyOptional({ description: '企业名称' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  enterprise?: string;

  @ApiPropertyOptional({ description: '营业执照号' })
  @IsOptional()
  @IsString()
  @Length(15, 18)
  licenseNo?: string;

  @ApiPropertyOptional({ description: '营业执照照片URL' })
  @IsOptional()
  @IsString()
  @IsUrl()
  licenseImage?: string;

  @ApiPropertyOptional({ description: '法人姓名' })
  @IsOptional()
  @IsString()
  @MaxLength(30)
  legalPerson?: string;

  @ApiPropertyOptional({ description: '企业地址' })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  address?: string;

  @ApiPropertyOptional({ description: '联系人姓名' })
  @IsOptional()
  @IsString()
  @MaxLength(30)
  contactName?: string;

  @ApiPropertyOptional({ description: '联系人电话' })
  @IsOptional()
  @IsString()
  @MaxLength(30)
  contactNumber?: string;

  @ApiPropertyOptional({ description: '联系人手机' })
  @IsOptional()
  @IsString()
  @IsPhoneNumber('CN')
  @Length(11, 11)
  contactPhone?: string;
}

/**
 * 审核企业认证DTO
 */
export class ReviewUserCompanyDto {
  @ApiProperty({ description: '审核状态', enum: ReviewStatus, example: ReviewStatus.APPROVED })
  @IsEnum(ReviewStatus)
  @IsNotEmpty()
  reviewedStatus: ReviewStatus;

  @ApiProperty({ description: '审核人ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  reviewerId: number;

  @ApiPropertyOptional({ description: '审核备注', example: '企业信息核实无误，审核通过' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  reviewRemark?: string;
}

/**
 * 企业认证查询DTO
 */
export class QueryUserCompanyDto {
  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', example: 10, minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: '用户ID', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  userId?: number;

  @ApiPropertyOptional({ description: '审核状态', enum: ReviewStatus })
  @IsOptional()
  @IsEnum(ReviewStatus)
  reviewedStatus?: ReviewStatus;

  @ApiPropertyOptional({ description: '审核人ID', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  reviewerId?: number;

  @ApiPropertyOptional({ description: '企业名称搜索', example: '科技' })
  @IsOptional()
  @IsString()
  enterprise?: string;

  @ApiPropertyOptional({ description: '营业执照号搜索', example: '91110000' })
  @IsOptional()
  @IsString()
  licenseNo?: string;

  @ApiPropertyOptional({ description: '法人姓名搜索', example: '张' })
  @IsOptional()
  @IsString()
  legalPerson?: string;

  @ApiPropertyOptional({ description: '联系人姓名搜索', example: '李' })
  @IsOptional()
  @IsString()
  contactName?: string;

  @ApiPropertyOptional({ description: '联系人手机搜索', example: '138' })
  @IsOptional()
  @IsString()
  contactPhone?: string;

  @ApiPropertyOptional({ description: '提交开始时间' })
  @IsOptional()
  @IsDateString()
  submittedStartDate?: string;

  @ApiPropertyOptional({ description: '提交结束时间' })
  @IsOptional()
  @IsDateString()
  submittedEndDate?: string;

  @ApiPropertyOptional({ description: '审核开始时间' })
  @IsOptional()
  @IsDateString()
  reviewedStartDate?: string;

  @ApiPropertyOptional({ description: '审核结束时间' })
  @IsOptional()
  @IsDateString()
  reviewedEndDate?: string;

  @ApiPropertyOptional({ description: '排序字段', example: 'submittedAt' })
  @IsOptional()
  @IsString()
  @IsIn(['id', 'reviewedStatus', 'submittedAt', 'reviewedAt', 'enterprise', 'legalPerson', 'createdAt'])
  sortBy?: string = 'submittedAt';

  @ApiPropertyOptional({ description: '排序方向', example: 'DESC' })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}

/**
 * 企业认证响应DTO
 */
export class UserCompanyResponseDto {
  @ApiProperty({ description: '认证ID', example: 1 })
  id: number;

  @ApiProperty({ description: '用户ID', example: 1 })
  userId: number;

  @ApiProperty({ description: '审核状态', enum: ReviewStatus })
  reviewedStatus: ReviewStatus;

  @ApiPropertyOptional({ description: '提交时间' })
  submittedAt?: Date;

  @ApiPropertyOptional({ description: '审核时间' })
  reviewedAt?: Date;

  @ApiPropertyOptional({ description: '审核人ID' })
  reviewerId?: number;

  @ApiPropertyOptional({ description: '审核备注' })
  reviewRemark?: string;

  @ApiProperty({ description: '企业名称' })
  enterprise: string;

  @ApiProperty({ description: '营业执照号' })
  licenseNo: string;

  @ApiProperty({ description: '营业执照照片URL' })
  licenseImage: string;

  @ApiProperty({ description: '法人姓名' })
  legalPerson: string;

  @ApiProperty({ description: '企业地址' })
  address: string;

  @ApiProperty({ description: '联系人姓名' })
  contactName: string;

  @ApiProperty({ description: '联系人电话' })
  contactNumber: string;

  @ApiProperty({ description: '联系人手机' })
  contactPhone: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 关联信息
  @ApiPropertyOptional({ description: '用户信息' })
  user?: {
    id: number;
    username: string;
    email: string;
    realName?: string;
    phone?: string;
    type: string;
    status: string;
  };

  @ApiPropertyOptional({ description: '审核人信息' })
  reviewer?: {
    id: number;
    username: string;
    email: string;
    realName?: string;
  };
}

/**
 * 企业认证列表响应DTO
 */
export class UserCompanyListResponseDto {
  @ApiProperty({ description: '企业认证列表', type: [UserCompanyResponseDto] })
  data: UserCompanyResponseDto[];

  @ApiProperty({ description: '总数', example: 100 })
  total: number;

  @ApiProperty({ description: '当前页', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  limit: number;

  @ApiProperty({ description: '总页数', example: 10 })
  totalPages: number;
}

/**
 * 企业认证统计DTO
 */
export class UserCompanyStatsDto {
  @ApiProperty({ description: '总申请数', example: 500 })
  totalApplications: number;

  @ApiProperty({ description: '待审核数', example: 50 })
  pendingApplications: number;

  @ApiProperty({ description: '已通过数', example: 400 })
  approvedApplications: number;

  @ApiProperty({ description: '已拒绝数', example: 50 })
  rejectedApplications: number;

  @ApiProperty({ description: '通过率', example: 88.9 })
  approvalRate: number;

  @ApiProperty({ description: '平均审核时间（小时）', example: 24.5 })
  avgReviewTime: number;

  @ApiProperty({ description: '按审核状态分组统计' })
  byStatus: Record<ReviewStatus, number>;

  @ApiProperty({ description: '按月份分组统计' })
  byMonth: Record<string, {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
  }>;

  @ApiProperty({ description: '按审核人分组统计' })
  byReviewer: Record<string, {
    total: number;
    approved: number;
    rejected: number;
    avgReviewTime: number;
  }>;

  @ApiProperty({ description: '今日新增申请数', example: 5 })
  todayApplications: number;

  @ApiProperty({ description: '本周新增申请数', example: 25 })
  weekApplications: number;

  @ApiProperty({ description: '本月新增申请数', example: 100 })
  monthApplications: number;
}

/**
 * 批量审核企业认证DTO
 */
export class BatchReviewUserCompanyDto {
  @ApiProperty({ description: '企业认证ID列表', example: [1, 2, 3] })
  @IsArray()
  @IsInt({ each: true })
  @IsNotEmpty()
  ids: number[];

  @ApiProperty({ description: '审核状态', enum: ReviewStatus })
  @IsEnum(ReviewStatus)
  @IsNotEmpty()
  reviewedStatus: ReviewStatus;

  @ApiProperty({ description: '审核人ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  reviewerId: number;

  @ApiPropertyOptional({ description: '审核备注' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  reviewRemark?: string;
}