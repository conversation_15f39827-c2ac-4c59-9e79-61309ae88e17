# API密钥管理系统设计与实现

## 总体设计

本系统采用"统一密钥多服务授权"模式，主要特点如下：

1. **每用户一个主密钥**：为每个用户创建一个主密钥，用于访问所有已授权服务
2. **基于User-Service授权控制**：通过UserService实体控制用户对特定服务的访问权限
3. **企业用户多密钥支持**：企业用户可以创建多个密钥，用于不同场景和权限管理
4. **自动密钥生成**：用户注册时自动生成密钥并授权默认服务

## 核心组件

### 1. KeyManagementService

密钥管理服务是系统的核心，负责：
- 创建和管理用户主密钥
- 为企业用户创建服务特定密钥
- 检查用户的服务访问权限
- 批量授权服务

### 2. 用户授权流程

1. **用户注册时**：
   - 创建用户账号
   - 自动生成主密钥
   - 授权默认服务
   
2. **服务访问时**：
   - 网关验证API密钥
   - 检查用户对请求服务的访问权限
   - 验证用户是否有足够的使用次数

### 3. 分层权限验证设计

系统采用分层权限验证机制，避免重复验证，同时确保安全性：

| 层级 | 组件 | 职责 |
|------|------|------|
| 全局守卫 | UnifiedAuthGuard | 验证密钥有效性和用户身份 |
| 网关服务 | GatewayProxyService | 验证服务特定权限和使用次数 |

这种分层设计的优势：
- 符合"职责单一"原则，每层只负责自己的验证职责
- 提供多层安全防护
- 通过明确职责分工避免冗余验证

### 4. 密钥类型与权限模型

根据用户类型分配不同的密钥类型和权限：

| 用户类型 | 密钥类型 | 默认权限 |
|---------|---------|---------|
| 企业用户 | enterprise | `*` (全部权限) |
| 组织用户 | premium | `service:*:read`, `service:*:write` |
| 个人用户 | basic | `service:*:read` |
| 其他用户 | basic | `service:basic:read` |

## 技术实现

### 1. 密钥生成与存储

- 使用安全随机生成器创建密钥
- 密钥采用SHA-256哈希后存储
- 设计了密钥格式验证和哈希比对机制

### 2. 手机号码登录与密钥生成

在手机短信验证登录的场景中也实现了密钥自动生成：

- **手机号码注册**：与常规注册过程相同，完成注册后自动生成API密钥
- **短信验证码登录（静默注册）**：
  - 如用户不存在，系统自动创建用户账号
  - 自动生成API密钥并授权默认服务
  - 返回isNewUser标记以便前端区分处理

这样确保了所有渠道注册的用户都能获得一致的API密钥体验。

### 3. 鉴权与守卫集成

- 与UnifiedAuthGuard无缝集成
- 支持JWT和API密钥双重验证方式
- 内置熔断器防止认证服务故障

### 4. 网关集成

- 网关服务在请求处理前验证服务权限
- 支持同步/异步处理模式自动选择
- 完整的请求跟踪和错误处理

## 使用场景

1. **个人用户**：注册时获得单一主密钥，可访问所有被授权的基础服务
2. **企业用户**：除主密钥外，可为不同业务场景创建专用密钥，细化权限控制
3. **服务调用**：通过网关统一入口访问所有服务，密钥自动完成身份验证和权限控制

## 扩展能力

1. **密钥轮换**：支持定期或按需轮换密钥，增强安全性
2. **权限细粒度控制**：可扩展实现更细粒度的权限控制，如读/写/管理权限分离
3. **使用量控制**：集成计费系统，实现基于使用量的服务访问控制

## 最佳实践

1. **密钥管理**：
   - 不要在代码中硬编码密钥
   - 定期轮换密钥
   - 只分配必要的最小权限

2. **安全考虑**：
   - 密钥传输应使用HTTPS
   - 生产环境密钥应与测试环境密钥分离
   - 密钥泄露时及时撤销并重新生成

# API密钥管理与缓存机制

## 架构概述

本系统采用API密钥作为身份验证的主要方式之一，使用双层架构（数据库+Redis缓存）来管理API密钥。这种设计提供了高性能的访问控制，同时保持了数据的持久性和一致性。

### 关键组件

- **ApiKeyEntity**: 存储在数据库中的API密钥实体
- **ApiKeyService**: 管理API密钥的CRUD操作以及缓存同步
- **UnifiedAuthGuard**: 全局认证守卫，验证请求中的API密钥
- **CoreAuthService**: 提供基础的认证相关功能，如从缓存读取API密钥
- **Redis缓存**: 存储活跃的API密钥，提高认证速度

## 缓存机制

为了提高API认证的性能，系统将活跃的API密钥信息存储在Redis缓存中。认证过程首先检查缓存，只有在缓存未命中时才会考虑其他认证方式。

### 缓存加载策略

1. **应用启动时加载**：通过OpenModule实现的`onModuleInit`钩子，在应用启动时自动加载所有活跃的API密钥到缓存
2. **种子数据加载**：运行种子脚本后自动将创建的API密钥加载到缓存
3. **增量更新**：当API密钥被创建、更新或状态变化时，自动同步到缓存

### 缓存结构

API密钥在Redis中的存储格式：

```
key: api_key:{keyHash}
value: {
  id: number,
  userId: number,
  serviceId: number,
  name: string,
  status: ApiKeyStatus,
  keyType: string,
  description: string,
  permissions: string[],
  expiresAt: Date | null,
  lastUsedAt: Date | null,
  createdAt: Date,
  updatedAt: Date
}
```

辅助索引:
```
key: api_key_id:{id}
value: keyHash
```

### 缓存生命周期

- 默认TTL: 24小时（86400秒）
- 自动过期：过期密钥不需要主动删除，依赖Redis的过期机制
- 手动清除：当API密钥被撤销、删除或状态改变为非活跃状态时，主动从缓存中清除

## 认证流程

1. 客户端请求API，在请求头中包含API密钥
2. UnifiedAuthGuard拦截请求，提取并哈希API密钥
3. 首先检查Redis缓存是否存在该API密钥
4. 如果缓存命中，验证密钥状态、权限等信息
5. 如果验证通过，在请求上下文中设置用户信息，继续处理请求
6. 如果缓存未命中或验证失败，返回401未授权错误

## 常见问题排查

### 401 未授权错误

如果API调用返回401错误，可能的原因和解决方案：

1. **缓存与数据库不同步**
   - 问题：API密钥在数据库中存在，但Redis缓存中不存在
   - 解决：手动执行API密钥缓存预加载
     ```
     // 运行种子数据脚本重新加载缓存
     cd src/database/seeds
     npx ts-node run-seeds.ts
     ```

2. **API密钥格式错误**
   - 检查请求头中的API密钥格式是否正确
   - 确认请求头中使用了正确的键名：`X-API-KEY`

3. **API密钥已过期或被撤销**
   - 在管理后台检查API密钥状态
   - 如果已过期或被撤销，需要重新生成或激活API密钥

4. **缓存故障**
   - 检查Redis连接是否正常
   - 可以尝试重启应用以重新初始化缓存

### 缓存一致性问题

为确保数据库和缓存的一致性，系统采用以下策略：

1. **写入模式**：先写入数据库，再更新缓存
2. **删除模式**：先清除缓存，再操作数据库
3. **定期刷新**：应用启动时重新加载所有活跃密钥到缓存

如果遇到一致性问题，可以手动执行缓存重建：

```typescript
// 通过ApiKeyService手动重建缓存
await apiKeyService.loadApiKeysToCache();
```

## 最佳实践

1. **API密钥轮换**：定期使用`regenerateApiKey`方法更新API密钥
2. **权限最小化**：仅授予API密钥所需的最小权限集
3. **活跃监控**：监控API密钥使用情况，及时发现异常行为
4. **缓存预热**：在高负载操作前确保缓存已完全加载

## 开发指南

### 添加新的API密钥存储字段

如需在API密钥中添加新字段，需要同时更新以下位置：

1. `ApiKeyEntity`实体类
2. `syncApiKeyToCache`方法中的缓存数据结构
3. `cacheApiKeys`函数（在种子脚本中）

### 修改缓存TTL

默认缓存TTL为24小时，可以通过以下方式修改：

1. `ApiKeyService.syncApiKeyToCache`方法中的ttl参数
2. 种子脚本中的`redis.setex`调用 