const crypto = require('crypto');

/**
 * 调试前端签名计算
 * 模拟前端的实际行为
 */

// 模拟前端的buildCanonicalRequest函数
function frontendBuildCanonicalRequest(method, path, queryParams, bodyData, timestamp) {
  console.log('=== 前端buildCanonicalRequest调用参数 ===');
  console.log('method:', method);
  console.log('path:', path);
  console.log('queryParams:', JSON.stringify(queryParams));
  console.log('bodyData:', JSON.stringify(bodyData));
  console.log('timestamp:', timestamp);

  // 确保路径以/开头
  if (path && !path.startsWith('/')) {
    path = '/' + path;
  }
  
  // 移除路径中可能存在的域名部分
  try {
    // 如果是完整URL，提取路径部分
    if (path.includes('://')) {
      const urlObj = new URL(path);
      path = urlObj.pathname;
    }
  } catch (error) {
    console.log('路径解析失败，使用原始路径');
  }
  
  console.log('处理后的路径:', path);
  
  // 确保路径有/v1前缀（但避免重复添加）
  if (!path.startsWith('/v1')) {
    path = '/v1' + path;
  }
  console.log('签名使用的路径:', path);
  
  // 处理查询参数
  console.log('原始查询参数:', JSON.stringify(queryParams));
  
  // 确保queryParams是对象
  const params = { ...queryParams };
  
  // 如果请求体中有mode参数，确保它也在查询参数中
  if (bodyData && typeof bodyData === 'object' && !(bodyData instanceof FormData) && bodyData.mode) {
    params.mode = bodyData.mode;
  }
  
  console.log('合并后的查询参数:', JSON.stringify(params));
  
  // 排序查询参数
  const sortedQuery = Object.keys(params)
    .sort()
    .map(key => `${key}=${encodeURIComponent(String(params[key]))}`)
    .join('&');
  
  console.log('排序后的查询参数:', sortedQuery);

  // 处理请求体
  let bodyString = '';
  if (bodyData) {
    if (typeof bodyData === 'string') {
      bodyString = bodyData;
    } else if (bodyData instanceof FormData) {
      // 简化处理，与后端保持一致
      bodyString = 'multipart-form-data';
    } else {
      try {
        bodyString = JSON.stringify(bodyData);
        console.log('JSON序列化请求体:', bodyString);
      } catch (error) {
        console.error('JSON序列化失败:', error);
        bodyString = String(bodyData); // 降级处理
      }
    }
  }

  const result = [
    method.toUpperCase(),
    path,
    sortedQuery,
    bodyString,
    timestamp
  ].join('\n');

  console.log('规范化请求字符串:');
  console.log(result);
  return result;
}

// 模拟前端的generateSignature函数
function frontendGenerateSignature(canonicalRequest, secretKey) {
  console.log(`生成签名，使用密钥: ${secretKey.substring(0, 3)}...${secretKey.substring(secretKey.length - 3)}`);
  console.log(`完整规范化请求字符串: ${canonicalRequest}`);
  
  // 确保使用正确的编码
  const signature = crypto.createHmac('sha256', secretKey).update(canonicalRequest).digest('base64');
  console.log(`生成的签名: ${signature}`);
  return signature;
}

// 模拟前端的buildSignedHeaders函数
function frontendBuildSignedHeaders(method, path, queryParams, body, timestamp, secretKey) {
  console.log('\n=== 前端buildSignedHeaders调用 ===');
  const canonical = frontendBuildCanonicalRequest(method, path, queryParams, body, timestamp);
  const signature = frontendGenerateSignature(canonical, secretKey);
  return signature;
}

// 测试用例1：逆地理编码
console.log('=== 测试逆地理编码签名 ===');

// 模拟前端的实际调用
const testCase1 = {
  // 前端的requestConfig.method
  method: 'GET',
  // 前端的requestConfig.endpoint
  endpoint: '/v1/op/geo/reverse',
  // 前端构建的query对象
  query: {
    lat: '22.543096',
    lng: '114.057865',
    mode: 'async' // 这是在第874行添加的
  },
  // 前端的data（对于GET请求通常是undefined）
  data: undefined,
  // 实际的时间戳
  timestamp: '1754760120',
  secretKey: 'sk-93f682d5dc7bdbd36fcbd891340bb1e097fa9738d6aefb2c6cda908f646ca950'
};

const frontendSig1 = frontendBuildSignedHeaders(
  testCase1.method,
  testCase1.endpoint,
  testCase1.query,
  testCase1.data,
  testCase1.timestamp,
  testCase1.secretKey
);

console.log('\n=== 结果对比 ===');
console.log('前端计算的签名:', frontendSig1);
console.log('实际前端发送的签名:', 'fnibKUI0iARKtuA6QqKcacMatGyA72yjyEYATTMQV6Q=');
console.log('后端计算的签名:', '9MMHlc7FN3o+/FnPe6Dokvepj2uVpc3EJlOIx+crJyM=');
console.log('前端计算与实际发送一致:', frontendSig1 === 'fnibKUI0iARKtuA6QqKcacMatGyA72yjyEYATTMQV6Q=');
console.log('前端计算与后端计算一致:', frontendSig1 === '9MMHlc7FN3o+/FnPe6Dokvepj2uVpc3EJlOIx+crJyM=');

// 测试用例2：正地理编码
console.log('\n\n=== 测试正地理编码签名 ===');

const testCase2 = {
  method: 'GET',
  endpoint: '/v1/op/geo/forward',
  query: {
    lat: '22.543096',
    lng: '114.057865',
    mode: 'async'
  },
  data: undefined,
  timestamp: '1754760130',
  secretKey: 'sk-93f682d5dc7bdbd36fcbd891340bb1e097fa9738d6aefb2c6cda908f646ca950'
};

const frontendSig2 = frontendBuildSignedHeaders(
  testCase2.method,
  testCase2.endpoint,
  testCase2.query,
  testCase2.data,
  testCase2.timestamp,
  testCase2.secretKey
);

console.log('\n=== 结果对比 ===');
console.log('前端计算的签名:', frontendSig2);
console.log('实际前端发送的签名:', 'hW7+RWYEpBNXmqZyHI3vKs7qMNLriX0ZTUtCf1mb7ec=');
console.log('后端计算的签名:', 'cND3TYcIG5+GrlC25URcakaxhOckQY+9bgaXZ1q6RzE=');
console.log('前端计算与实际发送一致:', frontendSig2 === 'hW7+RWYEpBNXmqZyHI3vKs7qMNLriX0ZTUtCf1mb7ec=');
console.log('前端计算与后端计算一致:', frontendSig2 === 'cND3TYcIG5+GrlC25URcakaxhOckQY+9bgaXZ1q6RzE=');
