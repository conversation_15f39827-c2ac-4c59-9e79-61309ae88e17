import { DataSource } from 'typeorm';
import { ApiKeyEntity } from '../src/modules/api-key/entities/api-key.entity';
import { RedisService } from '../src/shared/redis.service';
import * as crypto from 'crypto';

/**
 * 检查API密钥缓存数据的脚本
 * 用于诊断API密钥认证问题
 */
async function checkApiKeys() {
  // 创建数据源连接
  const dataSource = new DataSource({
    type: 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || '123456',
    database: process.env.DB_DATABASE || 'openapidb',
    entities: [ApiKeyEntity],
    synchronize: false,
  });

  // 创建Redis连接
  const redis = new (require('ioredis'))({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0'),
  });

  // 简单的Redis操作函数
  const redisGet = async (key: string): Promise<string | null> => {
    try {
      return await redis.get(key);
    } catch (error) {
      console.error(`Redis get失败: ${key}`, error);
      return null;
    }
  };

  const redisSetex = async (key: string, ttl: number, value: string): Promise<string> => {
    try {
      return await redis.setex(key, ttl, value);
    } catch (error) {
      console.error(`Redis setex失败: ${key}`, error);
      return 'OK';
    }
  };

  try {
    await dataSource.initialize();
    console.log('数据库连接成功');

    const apiKeyRepository = dataSource.getRepository(ApiKeyEntity);

    // 获取所有API密钥
    const apiKeys = await apiKeyRepository.find();
    console.log(`找到 ${apiKeys.length} 个API密钥`);

    for (const apiKey of apiKeys) {
      console.log(`\n检查API密钥: ${apiKey.key}`);
      console.log(`ID: ${apiKey.id}`);
      console.log(`用户ID: ${apiKey.userId}`);
      console.log(`状态: ${apiKey.status}`);
      console.log(`是否已查看: ${apiKey.isViewed}`);

      // 检查Redis缓存
      const cacheKey = `api_key:${apiKey.key}`;
      const cachedData = await redisGet(cacheKey);
      
      if (cachedData) {
        console.log(`✅ Redis缓存存在: ${cacheKey}`);
        try {
          const parsed = JSON.parse(cachedData);
          console.log(`缓存数据:`, {
            id: parsed.id,
            userId: parsed.userId,
            status: parsed.status,
            scopes: parsed.scopes
          });
        } catch (error) {
          console.log(`❌ 缓存数据解析失败: ${error.message}`);
        }
      } else {
        console.log(`❌ Redis缓存不存在: ${cacheKey}`);
      }

      // 检查Secret Key缓存
      const secretKeyRedisKey = `api_key_secret:${apiKey.id}`;
      const secretKeyFromRedis = await redisGet(secretKeyRedisKey);
      
      if (secretKeyFromRedis) {
        console.log(`✅ Secret Key缓存存在: ${secretKeyRedisKey}`);
        console.log(`Secret Key: ${secretKeyFromRedis.substring(0, 10)}...${secretKeyFromRedis.substring(secretKeyFromRedis.length - 10)}`);
      } else {
        console.log(`❌ Secret Key缓存不存在: ${secretKeyRedisKey}`);
        
        // 尝试生成新的Secret Key并缓存
        if (apiKey.encryptedSecretKey) {
          console.log(`尝试解密并缓存Secret Key...`);
          try {
            // 使用与API密钥服务相同的解密方法
            const algorithm = 'aes-256-cbc';
            const key = crypto.scryptSync('api-key-secret', 'salt', 32);
            
            const parts = apiKey.encryptedSecretKey.split(':');
            if (parts.length === 2) {
              const iv = Buffer.from(parts[0], 'hex');
              const encrypted = parts[1];
              
              const decipher = crypto.createDecipheriv(algorithm, key, iv);
              let decrypted = decipher.update(encrypted, 'hex', 'utf8');
              decrypted += decipher.final('utf8');
              
              // 缓存Secret Key
              await redisSetex(secretKeyRedisKey, 86400, decrypted);
              console.log(`✅ Secret Key已解密并缓存`);
            } else {
              console.log(`❌ 加密数据格式不正确`);
            }
          } catch (error) {
            console.log(`❌ 解密失败: ${error.message}`);
          }
        } else {
          console.log(`❌ 数据库中没有加密的Secret Key`);
        }
      }

      // 检查最后使用时间缓存
      const lastUsedKey = `api_key_last_used:${apiKey.id}`;
      const lastUsedData = await redisGet(lastUsedKey);
      if (lastUsedData) {
        console.log(`✅ 最后使用时间缓存存在: ${lastUsedKey}`);
      } else {
        console.log(`❌ 最后使用时间缓存不存在: ${lastUsedKey}`);
      }
    }

    // 检查特定API密钥（从日志中看到的）
    const specificKey = 'ak-579b27759508f152525a4e5a567efd5a';
    console.log(`\n检查特定API密钥: ${specificKey}`);
    
    const specificCacheKey = `api_key:${specificKey}`;
    const specificCachedData = await redisGet(specificCacheKey);
    
    if (specificCachedData) {
      console.log(`✅ 特定密钥缓存存在`);
      const parsed = JSON.parse(specificCachedData);
      console.log(`特定密钥数据:`, parsed);
    } else {
      console.log(`❌ 特定密钥缓存不存在`);
      
      // 在数据库中查找这个密钥
      const specificApiKey = await apiKeyRepository.findOne({
        where: { key: specificKey }
      });
      
      if (specificApiKey) {
        console.log(`✅ 在数据库中找到特定密钥`);
        console.log(`ID: ${specificApiKey.id}, 用户ID: ${specificApiKey.userId}`);
        
        // 尝试缓存这个密钥
        const cacheData = {
          id: specificApiKey.id,
          key: specificApiKey.key,
          userId: specificApiKey.userId,
          status: specificApiKey.status,
          scopes: specificApiKey.scopes || [],
          expiresAt: specificApiKey.expiresAt,
          allowedIps: specificApiKey.allowedIps,
          serviceId: specificApiKey.serviceId,
          keyType: specificApiKey.keyType,
          createdAt: specificApiKey.createdAt,
          updatedAt: specificApiKey.updatedAt,
          lastUsedAt: specificApiKey.lastUsedAt,
          isViewed: specificApiKey.isViewed
        };
        
        await redisSetex(specificCacheKey, 86400, JSON.stringify(cacheData));
        console.log(`✅ 特定密钥已缓存到Redis`);
      } else {
        console.log(`❌ 在数据库中也找不到特定密钥`);
      }
    }

  } catch (error) {
    console.error('脚本执行失败:', error);
  } finally {
    await dataSource.destroy();
    await redis.disconnect();
    console.log('连接已关闭');
  }
}

// 执行脚本
checkApiKeys().catch(console.error); 