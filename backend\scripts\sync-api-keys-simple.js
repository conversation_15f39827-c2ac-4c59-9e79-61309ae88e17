const { createConnection } = require('typeorm');
const Redis = require('ioredis');

/**
 * 简单的API密钥同步脚本
 * 直接使用JavaScript避免TypeScript编译问题
 */
async function syncApiKeysSimple() {
  console.log('开始手动同步API密钥到缓存...');
  
  // 创建数据库连接
  const connection = await createConnection({
    type: 'mysql',
    host: 'localhost',
    port: 3306,
    username: 'root',
    password: '123456',
    database: 'openapidb',
    entities: [],
    synchronize: false,
  });

  // 创建Redis连接
  const redis = new Redis({
    host: 'localhost',
    port: 6379,
    db: 0,
  });

  try {
    // 查询所有活跃的API密钥
    const apiKeys = await connection.query(`
      SELECT id, api_key as \`key\`, user_id as userId, status, scopes, expires_at as expiresAt, allowed_ips as allowedIps, service_id as serviceId, key_type as keyType, encrypted_secret_key as encryptedSecretKey, name, is_viewed as isViewed, createdAt, updatedAt, last_used_at as lastUsedAt
      FROM api_keys
      WHERE status = 'active'
    `);

    console.log(`找到 ${apiKeys.length} 个活跃的API密钥`);

    let syncedCount = 0;
    const errors = [];

    // 同步每个密钥
    for (const apiKey of apiKeys) {
      try {
        // 准备Redis缓存数据，使用keyStatus字段以匹配AuthApiKey接口
        const redisCacheData = {
          id: apiKey.id,
          userId: apiKey.userId,
          serviceId: apiKey.serviceId,
          name: apiKey.name,
          keyStatus: apiKey.status, // 注意：这里将status映射为keyStatus
          keyType: apiKey.keyType,
          description: apiKey.name, // 使用name作为description
          permissions: apiKey.scopes ? apiKey.scopes.split(',') : ['*'],
          expiresAt: apiKey.expiresAt,
          lastUsedAt: apiKey.lastUsedAt,
          createdAt: apiKey.createdAt,
          updatedAt: apiKey.updatedAt,
          isSecretViewed: apiKey.isViewed
        };

        // 缓存API密钥信息到Redis（24小时）
        const cacheKey = `api_key:${apiKey.key}`;
        await redis.setex(cacheKey, 86400, JSON.stringify(redisCacheData));

        // 缓存密钥秘钥到Redis（如果存在）
        if (apiKey.encryptedSecretKey) {
          const secretKeyRedisKey = `api_key_secret:${apiKey.id}`;

          // 解密Secret Key后再缓存
          try {
            const crypto = require('crypto');
            const algorithm = 'aes-256-cbc';
            const key = crypto.scryptSync('api-key-secret', 'salt', 32);

            const parts = apiKey.encryptedSecretKey.split(':');
            if (parts.length === 2) {
              const iv = Buffer.from(parts[0], 'hex');
              const encrypted = parts[1];

              const decipher = crypto.createDecipheriv(algorithm, key, iv);
              let decrypted = decipher.update(encrypted, 'hex', 'utf8');
              decrypted += decipher.final('utf8');

              // 缓存解密后的Secret Key
              await redis.setex(secretKeyRedisKey, 86400, decrypted);
              console.log(`  ✅ Secret Key已解密并缓存: ${decrypted.substring(0, 5)}...`);
            } else {
              console.log(`  ❌ 加密数据格式不正确: ${apiKey.encryptedSecretKey}`);
            }
          } catch (error) {
            console.log(`  ❌ 解密Secret Key失败: ${error.message}`);
            // 如果解密失败，仍然缓存加密的值作为备用
            await redis.setex(secretKeyRedisKey, 86400, apiKey.encryptedSecretKey);
          }
        }

        console.log(`✅ 同步成功: ID=${apiKey.id}, Key=${apiKey.key}`);
        syncedCount++;
      } catch (error) {
        const errorMsg = `同步密钥 ${apiKey.key} 失败: ${error.message}`;
        console.error(`❌ ${errorMsg}`);
        errors.push(errorMsg);
      }
    }

    console.log(`\n✅ 同步完成！成功同步 ${syncedCount}/${apiKeys.length} 个密钥`);
    
    if (errors.length > 0) {
      console.log('\n❌ 同步过程中的错误:');
      errors.forEach(error => console.log(`  - ${error}`));
    }
    
  } catch (error) {
    console.error('❌ 同步失败:', error.message);
  } finally {
    await connection.close();
    await redis.quit();
    console.log('连接已关闭');
  }
}

// 运行脚本
syncApiKeysSimple().catch(console.error);
