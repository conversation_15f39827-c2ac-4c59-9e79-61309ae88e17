import { Injectable, Logger, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { ApiException } from '@/common/exceptions/api.exception';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '@/shared/redis.service';
import { EmailService } from './email.service';
import { SmsService } from './sms.service';
import * as svgCaptcha from 'svg-captcha';
import * as crypto from 'crypto';

// 设备指纹接口
interface DeviceFingerprint {
  id: string;
  confidence: number;
  components: Record<string, any>;
}

// 行为模式接口
interface BehaviorPattern {
  mouseMovements: Array<{ x: number; y: number; timestamp: number }>;
  keystrokes: Array<{ key: string; timestamp: number; duration: number }>;
  clickPatterns: Array<{ x: number; y: number; timestamp: number }>;
  scrollBehavior: Array<{ scrollY: number; timestamp: number }>;
  focusEvents: Array<{ element: string; timestamp: number; duration: number }>;
  pageInteractions: Array<{ type: string; target: string; timestamp: number }>;
}

@Injectable()
export class CaptchaService {
  constructor(
    private configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly emailService: EmailService,
    private readonly smsService: SmsService,
  ) {}

  /**
   * 生成图像验证码
   */
  async generateImageCaptcha(): Promise<{
    captchaId: string;
    captchaImage: string;
  }> {
    // 生成SVG验证码
    const captcha = svgCaptcha.create({
      size: 4, // 验证码长度
      ignoreChars: '0o1iIl', // 忽略容易混淆的字符
      noise: 2, // 干扰线条数
      color: true, // 彩色
      background: '#f0f0f0', // 背景色
      width: 120,
      height: 40,
    });

    // 生成唯一ID
    const captchaId = crypto.randomUUID();

    // 将验证码答案存储到Redis，5分钟过期
    await this.redisService
      .getClient()
      .setex(`captcha:${captchaId}`, 300, captcha.text.toLowerCase());

    return {
      captchaId,
      captchaImage: captcha.data, // SVG格式的验证码图片
    };
  }

  /**
   * 验证图像验证码
   */
  async verifyImageCaptcha(
    captchaId: string,
    userInput: string,
  ): Promise<boolean> {
    if (!captchaId || !userInput) {
      return false;
    }

    const storedAnswer = await this.redisService
      .getClient()
      .get(`captcha:${captchaId}`);
    if (!storedAnswer) {
      return false;
    }

    // 验证成功后删除验证码
    await this.redisService.getClient().del(`captcha:${captchaId}`);

    return storedAnswer === userInput.toLowerCase();
  }

  /**
   * 验证拖拽轨迹
   */
  private validateTrajectory(
    trajectory: Array<{ x: number; y: number; t: number }>,
    totalDistance: number,
    totalDuration: number,
  ): boolean {
    if (trajectory.length < 3) {
      return false;
    }

    // 检查轨迹是否过于平滑（可能是机器人）
    let smoothCount = 0;
    for (let i = 1; i < trajectory.length - 1; i++) {
      const prev = trajectory[i - 1];
      const curr = trajectory[i];
      const next = trajectory[i + 1];

      const angle1 = Math.atan2(curr.y - prev.y, curr.x - prev.x);
      const angle2 = Math.atan2(next.y - curr.y, next.x - curr.x);
      const angleDiff = Math.abs(angle1 - angle2);

      if (angleDiff < 0.1) {
        smoothCount++;
      }
    }

    // 如果超过80%的点都过于平滑，可能是机器人
    if (smoothCount / (trajectory.length - 2) > 0.8) {
      return false;
    }

    // 检查速度变化（人类操作会有速度变化）
    const speeds: any[] = [];
    for (let i = 1; i < trajectory.length; i++) {
      const prev = trajectory[i - 1];
      const curr = trajectory[i];
      const distance = Math.sqrt(
        Math.pow(curr.x - prev.x, 2) + Math.pow(curr.y - prev.y, 2),
      );
      const time = curr.t - prev.t;
      if (time > 0) {
        speeds.push(distance / time);
      }
    }

    if (speeds.length > 0) {
      const avgSpeed = speeds.reduce((a, b) => a + b, 0) / speeds.length;
      const speedVariance =
        speeds.reduce((acc, speed) => {
          return acc + Math.pow(speed - avgSpeed, 2);
        }, 0) / speeds.length;

      // 如果速度变化太小，可能是机器人
      if (speedVariance < 0.01) {
        return false;
      }
    }

    return true;
  }

  /**
   * 发送短信验证码
   */
  async sendSmsCode(
    phone: string,
    type: 'register' | 'login' | 'reset',
  ): Promise<boolean> {
    // 验证手机号格式
    if (!this.isValidPhoneNumber(phone)) {
      throw new ApiException(30001);
    }

    // 检查发送频率限制（1分钟内只能发送一次）
    const rateLimitKey = `sms_rate_limit:${phone}`;
    const lastSent = await this.redisService.getClient().get(rateLimitKey);
    if (lastSent) {
      throw new ApiException(20002);
    }

    // 生成6位数字验证码
    const code = Math.floor(100000 + Math.random() * 900000).toString();

    // 存储验证码到Redis，5分钟过期
    const codeKey = `sms_code:${type}:${phone}`;
    await this.redisService.getClient().setex(codeKey, 300, code);

    // 设置发送频率限制，60秒
    await this.redisService
      .getClient()
      .setex(rateLimitKey, 60, Date.now().toString());

    // 使用SmsService发送短信
    const result = await this.smsService.sendVerificationCode(phone, code, type);

    if (!result.success) {
      // 发送失败，删除验证码和限流记录
      await this.redisService.getClient().del(codeKey);
      await this.redisService.getClient().del(rateLimitKey);
      throw new ApiException(20003);
    }

    return true;
  }

  /**
   * 验证短信验证码
   */
  async verifySmsCode(
    phone: string,
    code: string,
    type: 'register' | 'login' | 'reset',
  ): Promise<boolean> {
    if (!phone || !code) {
      return false;
    }

    const codeKey = `sms_code:${type}:${phone}`;
    const storedCode = await this.redisService.getClient().get(codeKey);

    if (!storedCode) {
      return false;
    }

    if (storedCode !== code) {
      return false;
    }

    // 验证成功后删除验证码
    await this.redisService.getClient().del(codeKey);

    return true;
  }

  /**
   * 发送邮箱验证码（增强版）
   */
  async sendEmailCode(
    email: string,
    type: 'register' | 'login' | 'reset',
    ip?: string,
    fingerprint?: DeviceFingerprint,
    behavior?: BehaviorPattern,
  ): Promise<Record<string,any>> {
    try {
      // 验证邮箱格式
      if (!this.isValidEmail(email)) {
        throw new ApiException(30002);
      }

      // 生成6位数字验证码
      const code = Math.floor(100000 + Math.random() * 900000).toString();
      const codeKey = `email_code:${type}:${email}`;

      // 使用新的邮件服务发送
      const result = await this.emailService.sendVerificationCode(
        email,
        code,
        type,
        ip || '127.0.0.1',
      );
      console.log('result>>', result);
      if (result?.success) {
        // 存储验证码，8分钟过期
        await this.redisService.getClient().setex(codeKey, 480, code);

        // 记录发送成功日志
        console.log(
          `邮件验证码发送成功: ${email}, 提供商: ${result?.provider}`,
        );
        return { ...result };
      } else {
        throw new ApiException(20004);
      }
    } catch (error) {
      console.error(`发送邮箱验证码失败: ${email}`, error);
      throw new ApiException(20005);
    }
  }

  /**
   * 验证邮箱验证码（增强版）
   */
  async verifyEmailCode(
    email: string,
    code: string,
    type: 'register' | 'login' | 'reset',
    fingerprint?: DeviceFingerprint,
  ): Promise<{ valid: boolean; message: string; securityCheck?: boolean }> {
    try {
      if (!email || !code) {
        return { valid: false, message: '邮箱或验证码不能为空' };
      }

      const codeKey = `email_code:${type}:${email}`;
      const storedCode = await this.redisService.getClient().get(codeKey);

      if (!storedCode || storedCode !== code) {
        return { valid: false, message: '验证码错误或已过期' };
      }

      // 设备指纹验证
      let securityCheck = true;
      if (fingerprint) {
        // 查找最近的安全记录
        const pattern = `email_security:${email}:*`;
        const keys = await this.redisService.getClient().keys(pattern);

        if (keys.length > 0) {
          // 获取最新的安全记录
          const latestKey = keys.sort().pop();
          const securityData = await this.redisService
            .getClient()
            .get(latestKey as string);

          if (securityData) {
            const parsed = JSON.parse(securityData);
            const storedFingerprint = parsed.fingerprint;

            // 比较设备指纹
            if (storedFingerprint.id !== fingerprint.id) {
              securityCheck = false;
              console.warn(
                `设备指纹不匹配: ${email}, 存储: ${storedFingerprint.id}, 当前: ${fingerprint.id}`,
              );
            }
          }
        }
      }

      // 验证成功后删除验证码
      await this.redisService.getClient().del(codeKey);

      return {
        valid: true,
        message: '验证成功',
        securityCheck,
      };
    } catch (error) {
      console.error(`验证邮箱验证码失败: ${email}`, error);
      return { valid: false, message: '验证失败' };
    }
  }

  /**
   * 验证手机号格式
   */
  private isValidPhoneNumber(phone: string): boolean {
    // 简单的手机号验证，可以根据需要调整
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }

  /**
   * 通过邮件平台发送验证码
   */
  private async sendEmailViaPlatform(
    email: string,
    code: string,
    type: string,
  ): Promise<boolean> {
    const emailConfig = {
      provider: this.configService.get('email.provider', 'smtp'), // 默认使用SMTP
      host: this.configService.get('email.host'),
      port: this.configService.get('email.port', 587),
      secure: this.configService.get('email.secure', false),
      user: this.configService.get('email.user'),
      pass: this.configService.get('email.pass'),
      from: this.configService.get('email.from'),
    };

    try {
      // 开发环境下模拟发送成功
      if (this.configService.get('NODE_ENV') === 'development') {
        console.log(`[EMAIL] 发送验证码到 ${email}: ${code}`);
        return true;
      }

      // 这里可以集成真实的邮件服务，如阿里云邮件推送、腾讯云邮件等
      // 目前提供基础的SMTP实现示例
      const nodemailer = require('nodemailer');

      const transporter = nodemailer.createTransporter({
        host: emailConfig.host,
        port: emailConfig.port,
        secure: emailConfig.secure,
        auth: {
          user: emailConfig.user,
          pass: emailConfig.pass,
        },
      });

      const typeMap = {
        register: '注册',
        login: '登录',
        reset: '重置密码',
      };

      const mailOptions = {
        from: emailConfig.from,
        to: email,
        subject: `${typeMap[type]}验证码`,
        html: `
          <div style="padding: 20px; font-family: Arial, sans-serif;">
            <h2>验证码</h2>
            <p>您的${typeMap[type]}验证码是：</p>
            <div style="background: #f5f5f5; padding: 15px; font-size: 24px; font-weight: bold; text-align: center; margin: 20px 0;">
              ${code}
            </div>
            <p>验证码有效期为5分钟，请及时使用。</p>
            <p>如果这不是您的操作，请忽略此邮件。</p>
          </div>
        `,
      };

      await transporter.sendMail(mailOptions);
      return true;
    } catch (error) {
      console.error('邮件发送失败:', error);
      return false;
    }
  }

  /**
   * 验证邮箱格式
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * 清理过期的验证码
   */
  async cleanupExpiredCodes(): Promise<void> {
    // Redis会自动清理过期的key，这里可以添加额外的清理逻辑
    console.log('清理过期验证码任务执行');
  }

  /**
   * 企业级多重验证
   * 结合拼图验证码和Google reCAPTCHA
   */
  async verifyEnterpriseCaptcha(
    puzzleCaptcha: {
      captchaId: string;
      moveDistance: number;
      duration: number;
      trajectory?: Array<{ x: number; y: number; t: number }>;
    },
    recaptchaToken: string,
    action: string,
  ): Promise<{
    success: boolean;
    message: string;
    puzzleVerified: boolean;
    recaptchaVerified: boolean;
    securityLevel: string;
    riskScore?: number;
  }> {
    try {
      // 1. 验证滑动验证码
      const puzzleResult = await this.verifySlideVerify(
        puzzleCaptcha.moveDistance,
        puzzleCaptcha.duration,
      );

      // 2. 验证Google reCAPTCHA
      // 注意：reCAPTCHA验证应该通过@Recaptcha()装饰器在控制器层面处理
      let recaptchaResult = { success: false, score: 0 };
      if (recaptchaToken) {
        // 在实际应用中，reCAPTCHA验证由@Recaptcha()装饰器自动处理
        // 这里提供一个基本的验证逻辑
        recaptchaResult = { success: true, score: 0.8 };
      }

      // 3. 计算安全等级
      let securityLevel = 'low';
      let riskScore = 0;

      if (puzzleResult.success && recaptchaResult.success) {
        // 基于reCAPTCHA分数和拼图验证结果计算风险分数
        riskScore = recaptchaResult.score || 0;

        if (riskScore >= 0.7) {
          securityLevel = 'high';
        } else if (riskScore >= 0.5) {
          securityLevel = 'medium';
        } else {
          securityLevel = 'low';
        }

        // 如果拼图验证显示可疑行为，降低安全等级
        if (puzzleCaptcha.duration < 1000 || puzzleCaptcha.duration > 30000) {
          securityLevel = securityLevel === 'high' ? 'medium' : 'low';
        }
      }

      const success = puzzleResult.success && recaptchaResult.success;

      return {
        success,
        message: success ? '企业级验证通过' : '验证失败，请重试',
        puzzleVerified: puzzleResult.success,
        recaptchaVerified: recaptchaResult.success,
        securityLevel,
        riskScore,
      };
    } catch (error) {
      console.error('企业级验证失败:', error);
      return {
        success: false,
        message: '验证服务异常',
        puzzleVerified: false,
        recaptchaVerified: false,
        securityLevel: 'low',
      };
    }
  }

  /**
   * 验证单独的reCAPTCHA token
   */
  async verifyRecaptchaToken(
    token: string,
    action?: string,
    remoteip?: string,
  ): Promise<{
    success: boolean;
    score?: number;
    action?: string;
    challenge_ts?: string;
    hostname?: string;
    error_codes?: string[];
  }> {
    try {
      // 注意：reCAPTCHA验证应该通过@Recaptcha()装饰器在控制器层面处理
      // 这里提供一个基本的验证逻辑
      if (!token) {
        throw new Error('reCAPTCHA token is required');
      }

      return {
        success: true,
        score: 0.8,
        action: 'submit',
        challenge_ts: new Date().toISOString(),
        hostname: 'localhost',
        error_codes: [],
      };
    } catch (error) {
      console.error('reCAPTCHA验证失败:', error);
      return {
        success: false,
        error_codes: ['verification-failed'],
      };
    }
  }

  /**
   * 获取安全风险评估
   */
  async getSecurityRiskAssessment(
    userAgent?: string,
    ip?: string,
    behaviorData?: any,
  ): Promise<{
    riskLevel: 'low' | 'medium' | 'high';
    riskScore: number;
    factors: string[];
    recommendations: string[];
  }> {
    const factors: string[] = [];
    const recommendations: string[] = [];
    let riskScore = 0;

    // 基于用户代理分析
    if (userAgent) {
      if (userAgent.includes('bot') || userAgent.includes('crawler')) {
        riskScore += 0.3;
        factors.push('可疑用户代理');
        recommendations.push('建议使用更严格的验证');
      }
    }

    // 基于行为数据分析
    if (behaviorData) {
      if (behaviorData.fastClicks > 5) {
        riskScore += 0.2;
        factors.push('异常快速点击');
      }

      if (behaviorData.mouseMovements < 10) {
        riskScore += 0.2;
        factors.push('鼠标移动异常');
      }
    }

    // 确定风险等级
    let riskLevel: 'low' | 'medium' | 'high' = 'low';
    if (riskScore >= 0.7) {
      riskLevel = 'high';
      recommendations.push('建议拒绝请求或要求额外验证');
    } else if (riskScore >= 0.4) {
      riskLevel = 'medium';
      recommendations.push('建议增加验证步骤');
    } else {
      recommendations.push('正常处理');
    }

    return {
      riskLevel,
      riskScore,
      factors,
      recommendations,
    };
  }

  /**
   * 验证滑动拼图验证码（企业级多重验证）
   */
  async verifySlideVerify(
    moveDistance: number,
    duration: number,
    securityData?: {
      deviceFingerprint?: any;
      behaviorPattern?: any;
      userAgent?: string;
      screenResolution?: string;
      timezone?: string;
      language?: string;
      timestamp?: number;
      sessionId?: string;
      clientIP?: string;
      browserFingerprint?: string;
      mouseTrajectory?: any[];
      keyboardPattern?: any[];
      touchPattern?: any[];
    },
  ): Promise<{ success: boolean; message: string; riskScore?: number }> {
    try {
      let riskScore = 0;
      const riskFactors: string[] = [];

      // 1. 基础验证：拖拽时间（放宽时间限制）
      if (duration < 0.3 || duration > 30) {
        riskScore += 15; // 降低风险分数
        riskFactors.push('异常操作时间');
      }

      // 2. 基础验证：移动距离（放宽距离限制）
      if (moveDistance < 20 || moveDistance > 500) {
        riskScore += 10; // 降低风险分数
        riskFactors.push('异常移动距离');
      }

      // 3. 基础验证：操作速度（放宽速度限制）
      const speed = moveDistance / duration;
      if (speed < 5 || speed > 1000) {
        riskScore += 8; // 降低风险分数
        riskFactors.push('异常操作速度');
      }

      // 4. 设备指纹验证
      if (securityData?.deviceFingerprint) {
        const deviceRisk = await this.analyzeDeviceFingerprint(
          securityData.deviceFingerprint,
        );
        riskScore += deviceRisk.score;
        if (deviceRisk.factors.length > 0) {
          riskFactors.push(...deviceRisk.factors);
        }
      }

      // 5. 行为模式分析
      if (securityData?.behaviorPattern) {
        const behaviorRisk = await this.analyzeBehaviorPattern(
          securityData.behaviorPattern,
        );
        riskScore += behaviorRisk.score;
        if (behaviorRisk.factors.length > 0) {
          riskFactors.push(...behaviorRisk.factors);
        }
      }

      // 6. 浏览器环境验证
      if (securityData?.userAgent && securityData?.browserFingerprint) {
        const browserRisk = await this.analyzeBrowserEnvironment({
          userAgent: securityData.userAgent,
          browserFingerprint: securityData.browserFingerprint,
          screenResolution: securityData.screenResolution,
          timezone: securityData.timezone,
          language: securityData.language,
        });
        riskScore += browserRisk.score;
        if (browserRisk.factors.length > 0) {
          riskFactors.push(...browserRisk.factors);
        }
      }

      // 7. 时间戳验证（防重放攻击）
      if (securityData?.timestamp) {
        const timeDiff = Date.now() - securityData.timestamp;
        if (timeDiff > 30000 || timeDiff < 0) {
          // 30秒内有效
          riskScore += 40;
          riskFactors.push('时间戳异常');
        }
      }

      // 8. IP地址分析
      if (securityData?.clientIP) {
        const ipRisk = await this.analyzeClientIP(securityData.clientIP);
        riskScore += ipRisk.score;
        if (ipRisk.factors.length > 0) {
          riskFactors.push(...ipRisk.factors);
        }
      }

      // 9. 鼠标轨迹分析
      if (
        securityData?.mouseTrajectory &&
        securityData.mouseTrajectory.length > 0
      ) {
        const trajectoryRisk = await this.analyzeMouseTrajectory(
          securityData.mouseTrajectory,
        );
        riskScore += trajectoryRisk.score;
        if (trajectoryRisk.factors.length > 0) {
          riskFactors.push(...trajectoryRisk.factors);
        }
      }

      // 风险评估（提高风险阈值，降低误判率）
      const isHighRisk = riskScore >= 100; // 提高高风险阈值
      const isMediumRisk = riskScore >= 60; // 提高中等风险阈值

      if (isHighRisk) {
        console.warn(
          `高风险验证尝试 - 风险分数: ${riskScore}, 风险因素: ${riskFactors.join(', ')}`,
        );
        return {
          success: false,
          message: '安全验证失败，请稍后重试',
          riskScore,
        };
      }

      if (isMediumRisk) {
        console.warn(
          `中等风险验证尝试 - 风险分数: ${riskScore}, 风险因素: ${riskFactors.join(', ')}`,
        );
        // 中等风险可以通过，但记录日志
      }

      // 记录成功的验证
      console.log(`验证成功 - 风险分数: ${riskScore}`);

      return {
        success: true,
        message: '验证成功',
        riskScore,
      };
    } catch (error) {
      console.error('验证滑动拼图验证码失败:', error);
      return { success: false, message: '验证失败' };
    }
  }

  /**
   * 分析设备指纹
   */
  private async analyzeDeviceFingerprint(
    deviceFingerprint: any,
  ): Promise<{ score: number; factors: string[] }> {
    const factors: string[] = [];
    let score = 0;

    // 检查设备指纹的完整性（降低评分）
    if (!deviceFingerprint.canvas || !deviceFingerprint.webgl) {
      score += 5; // 降低风险分数
      factors.push('设备指纹不完整');
    }

    // 检查是否为虚拟环境（降低评分）
    if (
      deviceFingerprint.userAgent?.includes('HeadlessChrome') ||
      deviceFingerprint.webgl?.includes('SwiftShader')
    ) {
      score += 15; // 降低风险分数
      factors.push('疑似虚拟环境');
    }

    return { score, factors };
  }

  /**
   * 分析行为模式
   */
  private async analyzeBehaviorPattern(
    behaviorPattern: any,
  ): Promise<{ score: number; factors: string[] }> {
    const factors: string[] = [];
    let score = 0;

    // 检查鼠标移动轨迹的自然性（放宽标准）
    if (behaviorPattern.mouseTrajectory) {
      const trajectory = behaviorPattern.mouseTrajectory;
      if (trajectory.length < 3) {
        // 降低最小轨迹点要求
        score += 8; // 降低风险分数
        factors.push('鼠标轨迹过于简单');
      }

      // 检查轨迹是否过于规律（机器人特征）
      const isRegular = this.isTrajectoryRegular(trajectory);
      if (isRegular) {
        score += 12; // 降低风险分数
        factors.push('鼠标轨迹过于规律');
      }
    }

    return { score, factors };
  }

  /**
   * 分析浏览器环境
   */
  private async analyzeBrowserEnvironment(env: {
    userAgent: string;
    browserFingerprint: string;
    screenResolution?: string;
    timezone?: string;
    language?: string;
  }): Promise<{ score: number; factors: string[] }> {
    const factors: string[] = [];
    let score = 0;

    // 检查User-Agent是否异常（放宽标准）
    if (!env.userAgent || env.userAgent.length < 20) {
      // 降低长度要求
      score += 8; // 降低风险分数
      factors.push('User-Agent异常');
    }

    // 检查屏幕分辨率是否常见（降低评分）
    if (env?.screenResolution) {
      const commonResolutions = [
        '1920x1080',
        '1366x768',
        '1440x900',
        '1536x864',
        '1280x720',
        '2560x1440',
        '3840x2160',
        '1600x900',
        '1024x768',
      ];
      if (
        !commonResolutions.some((res) => env?.screenResolution?.includes(res))
      ) {
        score += 2; // 降低风险分数
        factors.push('非常见屏幕分辨率');
      }
    }

    return { score, factors };
  }

  /**
   * 分析客户端IP
   */
  private async analyzeClientIP(
    clientIP: string,
  ): Promise<{ score: number; factors: string[] }> {
    const factors: string[] = [];
    let score = 0;

    // 检查是否为本地IP（降低评分）
    if (
      clientIP === '127.0.0.1' ||
      clientIP === 'localhost' ||
      clientIP === 'unknown'
    ) {
      score += 3; // 降低风险分数，开发环境常见
      factors.push('本地或未知IP');
    }

    // 这里可以集成IP地理位置和威胁情报服务
    // 例如检查IP是否在黑名单中、是否为代理IP等

    return { score, factors };
  }

  /**
   * 分析鼠标轨迹
   */
  private async analyzeMouseTrajectory(
    trajectory: any[],
  ): Promise<{ score: number; factors: string[] }> {
    const factors: string[] = [];
    let score = 0;

    if (trajectory.length < 2) {
      // 降低最小点数要求
      score += 8; // 降低风险分数
      factors.push('鼠标轨迹点过少');
    }

    // 检查轨迹是否过于直线（放宽标准）
    const isStraight = this.isTrajectoryTooStraight(trajectory);
    if (isStraight) {
      score += 6; // 降低风险分数
      factors.push('鼠标轨迹过于直线');
    }

    return { score, factors };
  }

  /**
   * 检查轨迹是否过于规律
   */
  private isTrajectoryRegular(trajectory: any[]): boolean {
    if (trajectory.length < 4) return false;

    // 简单检查：如果连续点的间距都相等，可能是机器人
    const distances: number[] = [];
    for (let i = 1; i < trajectory.length; i++) {
      const dx = trajectory[i].x - trajectory[i - 1].x;
      const dy = trajectory[i].y - trajectory[i - 1].y;
      distances.push(Math.sqrt(dx * dx + dy * dy));
    }

    // 如果所有距离都相等（误差在1像素内），认为是规律的
    const avgDistance = distances.reduce((a, b) => a + b, 0) / distances.length;
    const isRegular = distances.every((d) => Math.abs(d - avgDistance) < 1);

    return isRegular;
  }

  /**
   * 检查轨迹是否过于直线
   */
  private isTrajectoryTooStraight(trajectory: any[]): boolean {
    if (trajectory.length < 3) return false;

    // 计算轨迹的曲率，如果过于直线则可疑
    let totalDeviation = 0;
    for (let i = 1; i < trajectory.length - 1; i++) {
      const p1 = trajectory[i - 1];
      const p2 = trajectory[i];
      const p3 = trajectory[i + 1];

      // 计算点到直线的距离
      const deviation = this.pointToLineDistance(p2, p1, p3);
      totalDeviation += deviation;
    }

    const avgDeviation = totalDeviation / (trajectory.length - 2);
    return avgDeviation < 0.5; // 平均偏差小于0.5像素认为过于直线（更严格的标准）
  }

  /**
   * 计算点到直线的距离
   */
  private pointToLineDistance(
    point: any,
    lineStart: any,
    lineEnd: any,
  ): number {
    const A = point.x - lineStart.x;
    const B = point.y - lineStart.y;
    const C = lineEnd.x - lineStart.x;
    const D = lineEnd.y - lineStart.y;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;

    if (lenSq === 0) return Math.sqrt(A * A + B * B);

    const param = dot / lenSq;
    let xx, yy;

    if (param < 0) {
      xx = lineStart.x;
      yy = lineStart.y;
    } else if (param > 1) {
      xx = lineEnd.x;
      yy = lineEnd.y;
    } else {
      xx = lineStart.x + param * C;
      yy = lineStart.y + param * D;
    }

    const dx = point.x - xx;
    const dy = point.y - yy;
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * 测试发送短信（仅用于开发环境验证）
   */
  async testSendSms(
    phone: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // 验证手机号格式
      if (!this.isValidPhoneNumber(phone)) {
        return {
          success: false,
          message: '手机号格式不正确',
        };
      }

      // 生成6位数字验证码
      const code = '123456'; // 固定测试验证码

      // 直接调用SMS服务
      const result = await this.smsService.sendVerificationCode(phone, code, 'login');

      if (result.success) {
        return {
          success: true,
          message: `测试短信发送成功: ${phone}, 验证码: ${code}`,
        };
      } else {
        return {
          success: false,
          message: `短信发送失败: ${result.message}`,
        };
      }
    } catch (error) {
      console.error('测试短信发送异常:', error);
      return {
        success: false,
        message: `发送异常: ${error.message || '未知错误'}`,
      };
    }
  }
}
