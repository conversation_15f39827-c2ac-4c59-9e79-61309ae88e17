<template>
  <div class="api-keys-page">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">API密钥管理</h1>
        <p class="page-subtitle">查看您的API密钥信息及使用情况</p>
      </div>
      <div class="header-right">
        <!-- <el-button type="primary" @click="openCreateKeyDialog">
          <el-icon>
            <Plus />
          </el-icon>
          创建密钥
        </el-button> -->
      </div>
    </div>

    <!-- 安全说明 -->
    <el-alert title="密钥安全说明" type="info"
      description="密钥凭证只会在创建或重置后显示一次，请妥善保存。一旦查看后，出于安全考虑，系统将无法再次显示完整密钥。如果您遗失密钥，可以通过重置功能生成新的密钥。" show-icon
      :closable="false" class="security-alert" />

    <!-- 统计信息 -->
    <div class="stats-section">
      <div class="stat-item">
        <div class="stat-value">{{ apiKeys.length }}</div>
        <div class="stat-label">总密钥数</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ activeKeysCount }}</div>
        <div class="stat-label">活跃密钥</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ totalCalls }}</div>
        <div class="stat-label">总调用次数</div>
      </div>
    </div>

    <!-- 密钥列表 -->
    <div class="keys-section">
      <div class="section-header">
        <h2>密钥列表</h2>
        <!-- <div class="filters">
          <el-input v-model="searchText" placeholder="搜索密钥名称" style="width: 200px;" clearable>
            <template #prefix>
              <el-icon>
                <Search />
              </el-icon>
            </template>
</el-input>

<el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px;">
  <el-option label="全部" value="" />
  <el-option label="活跃" value="active" />
  <el-option label="禁用" value="disabled" />
</el-select>
</div> -->
      </div>
      <!-- @ts-ignore -->
      <div class="keys-list" v-loading="apiKeyStore.loading">
        <template v-if="filteredKeys && filteredKeys.length > 0">
          <div class="key-card" v-for="apiKey in filteredKeys" :key="apiKey.id">
            <div class="key-header">
              <div class="key-info">
                <h3 class="key-name">{{ apiKey?.name }}</h3>
                <p class="key-description">{{ apiKey?.description || '暂无描述' }}</p>
              </div>
              <div class="key-status">
                <el-tag :type="apiKey.status === 'active' ? 'success' : 'danger'" size="small">
                  {{ apiKey.status === 'active' ? '活跃' : '禁用' }}
                </el-tag>
              </div>
            </div>

            <div class="key-details">

              <div class="detail-row">
                <span class="label">密钥:</span>
                <div class="key-value">
                  <span class="masked-key">
                    {{ showFullKey[apiKey.id] ? apiKey.key : maskKey(apiKey.key) }}
                  </span>
                  <el-button text size="small" @click="toggleKeyVisibility(apiKey.id)">
                    <el-icon>
                      <View v-if="!showFullKey[apiKey.id]" />
                      <Hide v-else />
                    </el-icon>
                  </el-button>
                  <el-button text size="small" @click="copyKey(apiKey.key)">
                    <el-icon>
                      <CopyDocument />
                    </el-icon>
                  </el-button>
                </div>
              </div>

              <!-- 密钥对应的临时密钥 -->
              <div class="detail-row">
                <span class="label">安全秘钥:</span>
                <div class="key-value">
                  <span class="masked-key secret-key">
                    {{ getSecretKeyDisplay(apiKey) }}
                  </span>
                  <el-tooltip content="此密钥仅显示一次，请务必保存" placement="top" v-if="!apiKey.isViewed">
                    <el-button type="warning" size="small" @click="viewSecret(apiKey)"
                      :disabled="showFullSecret[apiKey.id]" :loading="viewingSecret[apiKey.id]">
                      <el-icon>
                        <Warning />
                      </el-icon> 查看密钥
                    </el-button>
                  </el-tooltip>
                  <el-button text size="small" @click="copyKey(apiKey.secretKey)" v-if="showFullSecret[apiKey.id]">
                    <el-icon>
                      <CopyDocument />
                    </el-icon>
                  </el-button>
                  <el-button type="danger" size="small" @click="confirmResetSecret(apiKey)"
                    :loading="resettingSecret[apiKey.id]">
                    重置密钥
                  </el-button>
                </div>
              </div>

              <div class="detail-row">
                <span class="label">权限:</span>
                <div class="permissions">
                  <el-tag v-for="permission in apiKey.permissions" :key="permission" size="small"
                    class="permission-tag">
                    {{ formatPermission(permission) }}
                  </el-tag>
                </div>
              </div>

              <div class="detail-row">
                <span class="label">使用统计:</span>
                <div class="usage-stats">
                  <span class="usage-item">今日: {{ apiKey.todayCalls || 0 }}次</span>
                  <span class="usage-item">本月: {{ apiKey.monthlyCalls || 0 }}次</span>
                  <span class="usage-item">总计: {{ apiKey.totalCalls || 0 }}次</span>
                </div>
              </div>

              <div class="detail-row">
                <span class="label">配额限制:</span>
                <div class="quota-info">
                  <span class="quota-item">月限额: {{ apiKey.quotaLimit || 10000 }}次</span>
                  <span class="quota-item">剩余: {{ apiKey.remainingCalls || (apiKey.quotaLimit || 10000) - (apiKey.monthlyCalls || 0) }}次</span>
                </div>
              </div>

              <div class="detail-row">
                <span class="label">权限范围:</span>
                <div class="scopes-info">
                  <el-tag v-if="apiKey.scopes && apiKey.scopes.includes('*')" type="success" size="small">
                    全部服务
                  </el-tag>
                  <el-tag v-else v-for="scope in (apiKey.scopes || [])" :key="scope" size="small" style="margin-right: 4px;">
                    {{ scope }}
                  </el-tag>
                  <span v-if="!apiKey.scopes || apiKey.scopes.length === 0" class="no-scopes">无权限</span>
                </div>
              </div>

              <div class="detail-row">
                <span class="label">创建时间:</span>
                <span class="value">{{ formatDate(apiKey.createdAt) }}</span>
              </div>

              <div class="detail-row">
                <span class="label">最后使用:</span>
                <span class="value">
                  {{ apiKey.lastUsedAt ? formatDate(apiKey.lastUsedAt) : '从未使用' }}
                </span>
              </div>
            </div>

            <!-- <div class="key-actions">
              <el-button :type="apiKey.keyStatus === 'active' ? 'danger' : 'success'" size="small"
                @click="toggleKeyStatus(apiKey)" :loading="updatingStatus[apiKey.id]">
                {{ apiKey.keyStatus === 'active' ? '禁用' : '启用' }}
              </el-button>
              <el-button type="danger" size="small" @click="confirmDeleteKey(apiKey)">
                删除
              </el-button>
            </div> -->
          </div>
        </template>

        <!-- 空状态 -->
        <div v-if="!apiKeyStore.loading && (!filteredKeys || filteredKeys.length === 0)" class="empty-state">
          <el-empty description="暂无API密钥">
            <!-- <el-button type="primary" @click="openCreateKeyDialog">创建密钥</el-button> -->
          </el-empty>
        </div>
      </div>
    </div>

    <!-- 创建密钥对话框 -->
    <el-dialog v-model="createKeyDialogVisible" title="创建新密钥" width="500px" :close-on-click-modal="false">
      <el-form :model="newKeyForm" :rules="keyFormRules" ref="keyFormRef" label-width="100px">
        <el-form-item label="密钥名称" prop="name">
          <el-input v-model="newKeyForm.name" placeholder="请输入密钥名称"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="newKeyForm.description" type="textarea" placeholder="请输入密钥描述（可选）"></el-input>
        </el-form-item>
        <el-form-item label="权限" prop="permissions">
          <el-checkbox-group v-model="newKeyForm.permissions">
            <el-checkbox label="ocr:read">OCR识别</el-checkbox>
            <el-checkbox label="nlp:read">NLP处理</el-checkbox>
            <el-checkbox label="ai:read">AI生成</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createKeyDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="createApiKey" :loading="creatingKey">
            创建
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看密钥警告对话框 -->
    <el-dialog v-model="secretWarningVisible" title="重要提示" width="500px" :close-on-click-modal="false">
      <div class="secret-warning">
        <el-alert title="该密钥仅显示一次，请妥善保存！" type="warning" :closable="false" show-icon>
          <div class="alert-content">
            <p>您现在看到的密钥<strong>只会显示这一次</strong>，请立即复制并安全保存。</p>
            <p>如果您丢失了密钥，您需要重置生成新的密钥。</p>
          </div>
        </el-alert>

        <div class="secret-display" v-if="currentViewingKey">
          <div class="secret-item">
            <div class="secret-label">密钥 (api-Key):</div>
            <div class="secret-value">{{ currentViewingKey.key }}</div>
            <el-button text size="small" @click="copyKey(currentViewingKey.key)" class="copy-btn">
              <el-icon>
                <CopyDocument />
              </el-icon>
            </el-button>
          </div>

          <div class="secret-item">
            <div class="secret-label">密钥凭证 (api-secret):</div>
            <div class="secret-value">{{ currentViewingKey.secretKey }}</div>
            <el-button text size="small" @click="copyKey(currentViewingKey.secretKey)" class="copy-btn">
              <el-icon>
                <CopyDocument />
              </el-icon>
            </el-button>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-checkbox v-model="secretConfirmed" class="secret-confirm-checkbox">
            我已保存密钥
          </el-checkbox>

          <el-button type="primary" @click="closeSecretWarning" :disabled="!secretConfirmed" class="confirm-btn">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { storeToRefs } from 'pinia'
import {
  Search,
  View,
  Hide,
  CopyDocument,
  Plus,
  Warning
} from '@element-plus/icons-vue'
import { useApiKeyStore } from '@/stores/apiKey'
import { useUserStore } from '@/stores/user'
import { request } from '@/utils/request'
import dayjs from 'dayjs'
import type { ApiKey } from '@/stores/apiKey'

const apiKeyStore = useApiKeyStore()
const userStore = useUserStore()

// 使用storeToRefs保持响应式
const { apiKeys } = storeToRefs(apiKeyStore)
const { userInfo } = storeToRefs(userStore)

// 方法直接从store实例访问，不需要解构
// const {
//   getApiKeysByUserId,
//   markSecretAsViewed,
//   resetApiKeySecret,
//   createKey,
//   toggleApiKeyStatus,
//   deleteApiKey
// } = apiKeyStore

// const {
//   getUserApiKeysInfo,
//   getUserApiKeysInfoById,
// } = userStore

const searchText = ref('')
const statusFilter = ref('')
const showFullKey = ref<Record<string, boolean>>({})
const showFullSecret = ref<Record<string, boolean>>({})
const viewingSecret = ref<Record<string, boolean>>({})
const resettingSecret = ref<Record<string, boolean>>({})
const updatingStatus = ref<Record<string, boolean>>({})
const createKeyDialogVisible = ref(false)
const secretWarningVisible = ref(false)
const secretConfirmed = ref(false)
const currentViewingKey = ref<ApiKey | null>(null)
const creatingKey = ref(false)

// 表单相关
const keyFormRef = ref<FormInstance>()
const newKeyForm = reactive({
  name: '',
  description: '',
  permissions: [] as string[]
})

const keyFormRules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入密钥名称', trigger: 'blur' },
    { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  permissions: [
    { type: 'array', required: true, message: '请至少选择一个权限', trigger: 'change' }
  ]
})

// 计算属性
const activeKeysCount = computed(() => {
  return apiKeys.value.filter((key: any) => key.status === 'active').length
})

const totalCalls = computed(() => {
  return apiKeys.value.reduce((sum: number, key: any) => sum + (key.totalCalls || 0), 0)
})

const filteredKeys = computed(() => {
  let filtered = apiKeys.value

  // 按名称搜索
  if (searchText.value) {
    filtered = filtered.filter((key: any) =>
      key.name.toLowerCase().includes(searchText.value.toLowerCase())
    )
  }

  // 按状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter((key: any) => key.status === statusFilter.value)
  }

  return filtered
})

// 掩码密钥
const maskKey = (key: string) => {
  if (!key || key === 'HIDDEN') return '******'
  if (key.length <= 8) return key
  return key.substring(0, 8) + '*'.repeat(key.length - 16) + key.substring(key.length - 8)
}

// 获取安全密钥显示内容
const getSecretKeyDisplay = (apiKey: any) => {
  // 如果正在显示完整密钥
  if (showFullSecret.value[apiKey.id]) {
    return apiKey.secretKey || '******'
  }
  
  // 如果密钥已被查看过，显示星号
  if (apiKey.isViewed) {
    return '******'
  }
  
  // 如果密钥未被查看过，显示掩码
  return maskKey(apiKey.secretKey || 'HIDDEN')
}

// 切换密钥可见性
const toggleKeyVisibility = (keyId: number) => {
  showFullKey.value[keyId] = !showFullKey.value[keyId]
}

// 复制密钥
const copyKey = async (key: string | undefined) => {
  if (!key) {
    ElMessage.error('密钥不存在或已被查看')
    return
  }

  try {
    await navigator.clipboard.writeText(key)
    ElMessage.success('密钥已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 查看密钥秘钥
const viewSecret = async (apiKey: any) => {
  if (apiKey.isViewed) {
    ElMessage.warning('该密钥已被查看过，无法再次查看')
    return
  }

  viewingSecret.value[apiKey.id] = true

  try {
    // 获取密钥详情（如果未查看过，会包含secretKey）
    const keyDetail = await request.get(`/api-keys/${apiKey.id}`)
    console.log('获取密钥详情结果:', keyDetail)

    if (keyDetail && keyDetail.secretKey) {
      // 显示密钥信息
      currentViewingKey.value = keyDetail
      showFullSecret.value[apiKey.id] = true
      secretWarningVisible.value = true
    } else {
      ElMessage.error('无法获取密钥信息，可能已被查看过')
    }
  } catch (error) {
    console.error('查看密钥失败:', error)
    ElMessage.error('查看密钥失败')
  } finally {
    viewingSecret.value[apiKey.id] = false
  }
}

// 关闭密钥警告
const closeSecretWarning = async () => {
  if (!secretConfirmed.value || !currentViewingKey.value) return

  try {
    // 标记密钥为已查看
    await apiKeyStore.markSecretAsViewed(currentViewingKey.value.id)

    // 更新本地数据
    const keyIndex = apiKeys.value.findIndex((key: any) => key.id === currentViewingKey.value.id)
    if (keyIndex !== -1) {
      apiKeys.value[keyIndex].isViewed = true
      // 隐藏完整密钥显示
      showFullSecret.value[currentViewingKey.value.id] = false
    }

    // 更新UI状态
    secretWarningVisible.value = false
    secretConfirmed.value = false
    currentViewingKey.value = null

    ElMessage.success('密钥已标记为已查看')
  } catch (error) {
    console.error('标记密钥为已查看失败:', error)
    ElMessage.error('标记失败，请重试')
  }
}

// 确认重置密钥
const confirmResetSecret = async (apiKey: any) => {
  try {
    await ElMessageBox.confirm(
      '重置密钥后，原有密钥将无法使用，您需要使用新生成的密钥。确定要重置吗？',
      '重置密钥',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    await resetKeySecret(apiKey)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作被取消')
    }
  }
}

// 重置密钥
const resetKeySecret = async (apiKey: any) => {
  resettingSecret.value[apiKey.id] = true

  try {
    const result = await apiKeyStore.resetApiKeySecret(apiKey.id) as ApiKey
    if (result) {
      // 更新本地状态
      const keyIndex = apiKeys.value.findIndex((key: any) => key.id === apiKey.id)
      if (keyIndex !== -1) {
        apiKeys.value[keyIndex] = { ...result }
        // 显示重置后的密钥
        currentViewingKey.value = result
        secretWarningVisible.value = true
        showFullSecret.value[result.id] = true
      }
      ElMessage.success('密钥重置成功')
    }
  } catch (error) {
    ElMessage.error('重置密钥失败')
  } finally {
    resettingSecret.value[apiKey.id] = false
  }
}

// 切换密钥状态
const toggleKeyStatus = async (apiKey: any) => {
  const newStatus = apiKey.status === 'active' ? 'disabled' : 'active'
  updatingStatus.value[apiKey.id] = true

  try {
    await apiKeyStore.toggleApiKeyStatus(apiKey.id, newStatus)
    // 更新本地状态
    const keyIndex = apiKeys.value.findIndex((key: any) => key.id === apiKey.id)
    if (keyIndex !== -1) {
      apiKeys.value[keyIndex].status = newStatus
    }
    ElMessage.success(`密钥已${newStatus === 'active' ? '启用' : '禁用'}`)
  } catch (error) {
    ElMessage.error('更新密钥状态失败')
  } finally {
    updatingStatus.value[apiKey.id] = false
  }
}

// 确认删除密钥
const confirmDeleteKey = async (apiKey: any) => {
  try {
    await ElMessageBox.confirm(
      '删除密钥后，无法恢复，所有使用该密钥的服务将无法访问。确定要删除吗？',
      '删除密钥',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    await apiKeyStore.deleteApiKey(apiKey.id)
    // 更新本地状态
    const keyIndex = apiKeys.value.findIndex((key: any) => key.id === apiKey.id)
    if (keyIndex !== -1) {
      apiKeys.value.splice(keyIndex, 1)
    }
    ElMessage.success('密钥已删除')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除密钥失败')
    }
  }
}

// 打开创建密钥对话框
const openCreateKeyDialog = () => {
  // 重置表单
  if (keyFormRef.value) {
    keyFormRef.value.resetFields()
  }
  createKeyDialogVisible.value = true
}

// 创建API密钥
const createApiKey = async () => {
  if (!keyFormRef.value) return

  await keyFormRef.value.validate(async (valid) => {
    if (valid) {
      creatingKey.value = true
    }
  })
}

// 格式化权限
const formatPermission = (permission: string) => {
  const permissionMap: Record<string, string> = {
    'ocr:read': 'OCR识别',
    'nlp:read': 'NLP处理',
    'ai:read': 'AI生成',
    'admin': '管理员'
  }
  return permissionMap[permission] || permission
}

// 格式化日期
const formatDate = (date: string | Date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 加载数据
const loadApiKeys = async () => {
  try {
    apiKeyStore.loading = true
    console.log('开始加载API密钥数据...')

    if (userInfo.value && userInfo.value.id) {
      // 使用用户store中的getUserApiKeysInfoById方法获取密钥
      console.log('使用用户ID获取API密钥:', userInfo.value.id)
      const response: any = await userStore.getUserApiKeysInfoById(parseInt(userInfo.value.id))
      console.log('API密钥接口返回数据:', response)

      if (response && Array.isArray(response)) {
        apiKeyStore.$patch({ apiKeys: response })
      } else if (response && response.records && Array.isArray(response.records)) {
        apiKeyStore.$patch({ apiKeys: response.records })
      }
    } else {
      // 如果没有userInfo，先获取用户信息再获取API密钥
      console.log('未找到用户信息，先获取用户信息...')
      await userStore.getUserInfo()

      if (userStore.userInfo && userStore.userInfo.id) {
        console.log('获取到用户信息，使用用户ID获取API密钥:', userStore.userInfo.id)
        const response: any = await userStore.getUserApiKeysInfoById(parseInt(userStore.userInfo.id))
        console.log('API密钥接口返回数据:', response)

        if (response && Array.isArray(response)) {
          apiKeyStore.$patch({ apiKeys: response })
        } else if (response && response.records && Array.isArray(response.records)) {
          apiKeyStore.$patch({ apiKeys: response.records })
        }
      } else {
        // 如果仍然没有用户信息，尝试使用getUserApiKeysInfo方法
        console.log('尝试使用通用方法获取API密钥...')
        const response: any = await userStore.getUserApiKeysInfo()
        console.log('API密钥接口返回数据:', response)

        if (response && Array.isArray(response)) {
          apiKeyStore.$patch({ apiKeys: response })
        } else if (response && response.records && Array.isArray(response.records)) {
          apiKeyStore.$patch({ apiKeys: response.records })
        }
      }
    }

    // 等待DOM更新后检查数据
    await nextTick(() => {
      console.log('API密钥加载完成，数据状态:', {
        apiKeys: apiKeys.value,
        filteredKeys: filteredKeys.value,
        length: filteredKeys.value?.length || 0
      })
    })
  } catch (error) {
    console.error('加载API密钥失败:', error)
    ElMessage.error('加载API密钥失败')
  } finally {
    apiKeyStore.loading = false
  }
}

// 确保在组件挂载时加载数据
onMounted(async () => {
  await loadApiKeys()
})
</script>

<style scoped>
.api-keys-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #606266;
  margin: 0;
  font-size: 1rem;
}

.header-right {
  flex-shrink: 0;
}

.stats-section {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
}

.stat-item {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.keys-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.filters {
  display: flex;
  gap: 12px;
}

.keys-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.key-card {
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s;
}

.key-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
}

.key-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.key-info {
  flex: 1;
}

.key-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.key-description {
  color: #606266;
  margin: 0;
  font-size: 14px;
}

.key-status {
  flex-shrink: 0;
}

.key-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.label {
  color: #909399;
  font-size: 14px;
  width: 80px;
  flex-shrink: 0;
}

.value {
  color: #303133;
  font-size: 14px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.key-value {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.masked-key {
  color: #303133;
  font-size: 14px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  flex: 1;
  word-break: break-all;
}

.secret-key {
  color: #ff6b6b;
  font-weight: 500;
}

.permissions {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.permission-tag {
  margin: 0;
}

.usage-stats {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.usage-item {
  color: #606266;
  font-size: 14px;
}

.quota-info {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.quota-item {
  color: #606266;
  font-size: 14px;
}

.scopes-info {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.no-scopes {
  color: #909399;
  font-style: italic;
  font-size: 14px;
}

.key-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 16px;
  border-top: 1px solid #ebeef5;
  padding-top: 16px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.secret-warning {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.secret-display {
  margin-top: 16px;
  border: 1px solid #e6a23c;
  border-radius: 8px;
  padding: 20px;
  background-color: #fdf6ec;
}

.secret-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.secret-item:last-child {
  margin-bottom: 0;
}

.secret-label {
  font-weight: 600;
  color: #606266;
  min-width: 140px;
  padding-left: 8px;
}

.secret-value {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: white;
  padding: 8px 12px;
  border-radius: 4px;
  flex: 1;
  word-break: break-all;
  border: 1px dashed #e6a23c;
}

.copy-btn {
  margin-left: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .stats-section {
    flex-direction: column;
  }

  .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filters {
    flex-direction: column;
  }

  .key-header {
    flex-direction: column;
    gap: 12px;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .label {
    width: auto;
  }

  .key-value {
    width: 100%;
  }

  .usage-stats {
    flex-direction: column;
    gap: 4px;
  }

  .key-actions {
    flex-direction: column;
  }

  .secret-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .secret-label {
    min-width: auto;
  }
}

.security-alert {
  margin-bottom: 24px;
}

.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 16px;
}

.secret-confirm-checkbox {
  margin-right: 16px;
}

.confirm-btn {
  min-width: 80px;
}

.alert-content {
  padding: 8px 0;
  line-height: 1.6;
}

.alert-content p {
  margin: 6px 0;
}

.alert-content strong {
  color: #e6a23c;
  font-weight: 700;
}
</style>