import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ength, IsOptional, IsObject, IsNotEmpty } from 'class-validator';

export class LoginDto {
  @ApiProperty({
    description: 'Username (for username/password login)',
    example: 'johndoe',
    required: false,
  })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiProperty({
    description: 'Email address (for email/password login)',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({
    description: 'Phone number (for phone/password or phone/sms login)',
    example: '13800138000',
    required: false,
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({
    description: 'User password (required for password login)',
    example: 'password123',
    minLength: 6,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MinLength(6)
  password?: string;

  @ApiProperty({
    description: 'SMS verification code (required for SMS login)',
    example: '123456',
    required: false,
  })
  @IsOptional()
  @IsString()
  smsCode?: string;

  @ApiProperty({
    description: 'Image captcha ID (optional for password login)',
    example: 'captcha-123',
    required: false,
  })
  @IsOptional()
  @IsString()
  captchaId?: string;

  @ApiProperty({
    description: 'Image captcha code (optional for password login)',
    example: '1234',
    required: false,
  })
  @IsOptional()
  @IsString()
  captchaCode?: string;

  @ApiProperty({
    description: 'Drag verification data (required for password login)',
    required: false,
    example: {
      level: 'high',
      deviceFingerprint: {},
      behaviorPattern: {},
      verificationTimestamp: 1234567890,
      clientInfo: {}
    }
  })
  @IsOptional()
  securityVerification?: {
    level?: string;
    deviceFingerprint?: any;
    behaviorPattern?: any;
    verificationTimestamp?: number;
    clientInfo?: any;
    moveDistance?: number;
    duration?: number;
  };

  @ApiProperty({
    description: 'Remember me option',
    example: false,
    required: false,
  })
  @IsOptional()
  rememberMe?: boolean;
}

export class RegisterDto {
  @ApiProperty({
    description: 'Email address (for email registration)',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({
    description: 'Phone number (for phone registration)',
    example: '13800138000',
    required: false,
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({
    description: 'User password',
    example: 'password123',
    minLength: 6,
  })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({
    description: 'Confirm password',
    example: 'password123',
    minLength: 6,
  })
  @IsString()
  @MinLength(6)
  confirmPassword: string;

  @ApiProperty({
    description: 'Email verification code (required for email registration)',
    example: '123456',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MinLength(6)
  @MaxLength(6)
  emailCode?: string;

  @ApiProperty({
    description: 'SMS verification code (required for phone registration)',
    example: '123456',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MinLength(6)
  @MaxLength(6)
  smsCode?: string;

  @ApiProperty({
    description: 'Drag verification data (required for registration)',
    required: true,
    example: {
      level: 'high',
      deviceFingerprint: {},
      behaviorPattern: {},
      verificationTimestamp: 1234567890,
      clientInfo: {}
    }
  })
  @IsObject()
  @IsNotEmpty()
  securityVerification: {
    level?: string;
    deviceFingerprint?: any;
    behaviorPattern?: any;
    verificationTimestamp?: number;
    clientInfo?: any;
  };

  @ApiProperty({
    description: 'User type',
    example: 'individual',
    enum: ['individual', 'enterprise', 'organization'],
    required: false,
  })
  @IsOptional()
  @IsString()
  userType?: string;
}

export class RefreshTokenDto {
  @ApiProperty({
    description: 'Refresh token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString()
  refresh_token: string;
}

export class AuthResponseDto {
  @ApiProperty({
    description: 'JWT access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  access_token: string;

  @ApiProperty({
    description: 'JWT refresh token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  refresh_token: string;

  @ApiProperty({
    description: 'User information'
  })
  user: {
    id: number;
    email: string;
    username: string;
  };
}

export class LoginResponseDto {
  @ApiProperty({
    description: 'User information'
  })
  user: any;

  @ApiProperty({
    description: 'JWT access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'JWT refresh token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  refreshToken: string;

  @ApiProperty({
    description: 'JWT token (for backward compatibility)',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  token: string;

  @ApiProperty({
    description: 'Whether user has unviewed API keys',
    example: false,
  })
  hasUnviewedApiKey: boolean;
}

export class RefreshResponseDto {
  @ApiProperty({
    description: 'New JWT access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'New JWT refresh token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  refreshToken: string;
}