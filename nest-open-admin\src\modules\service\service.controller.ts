import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ServiceService } from './service.service';
import {
  CreateServiceDto,
  UpdateServiceDto,
  QueryServiceDto,
  ServiceResponseDto,
  ServiceListResponseDto,
  ServiceStatus,
} from './dto/service.dto';
import { Public } from '../../common/decorators/public.decorator';

@ApiTags('服务管理')
@Controller('service')
export class ServiceController {
  constructor(private readonly serviceService: ServiceService) {}

  @Post()
  @ApiOperation({ summary: '创建服务', description: '创建新的API服务' })
  async create(@Body() createServiceDto: CreateServiceDto): Promise<ServiceResponseDto> {
    return await this.serviceService.create(createServiceDto);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: '获取服务列表', description: '分页查询服务列表，支持多种筛选条件' })
  @ApiResponse({ status: 200, description: '公开接口，不需要认证' })
  @ApiQuery({ name: 'page', required: false, description: '页码', example: 1 })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量', example: 10 })
  @ApiQuery({ name: 'code', required: false, description: '服务代码搜索' })
  @ApiQuery({ name: 'name', required: false, description: '服务名称搜索' })
  @ApiQuery({ name: 'type', required: false, description: '服务类型', enum: ['OCR', 'NLP', 'CV', 'AI', 'DATA', 'OTHER'] })
  @ApiQuery({ name: 'serviceStatus', required: false, description: '服务状态', enum: ['active', 'inactive', 'maintenance', 'deprecated'] })
  @ApiQuery({ name: 'pricingModel', required: false, description: '定价模式', enum: ['free', 'pay_per_use', 'subscription', 'tiered'] })
  @ApiQuery({ name: 'requiresAuth', required: false, description: '是否需要认证', type: Boolean })
  @ApiQuery({ name: 'supportsBatch', required: false, description: '是否支持批量处理', type: Boolean })
  @ApiQuery({ name: 'sortBy', required: false, description: '排序字段', example: 'createdAt' })
  @ApiQuery({ name: 'sortOrder', required: false, description: '排序方向', enum: ['ASC', 'DESC'] })
  async findAll(@Query() queryDto: QueryServiceDto): Promise<ServiceListResponseDto> {
    return await this.serviceService.findAll(queryDto);
  }

  @Get('stats')
  @Public()
  @ApiOperation({ summary: '获取服务统计信息', description: '获取服务的统计数据' })
  @ApiResponse({ status: 200, description: '公开接口，不需要认证' })
  async getStats() {
    return await this.serviceService.getStats();
  }

  @Get('code/:code')
  @Public()
  @ApiOperation({ summary: '根据代码获取服务', description: '通过服务代码获取服务详情' })
  @ApiResponse({ status: 200, description: '公开接口，不需要认证' })
  @ApiParam({ name: 'code', description: '服务代码', example: 'ocr-invoice' })
  async findByCode(@Param('code') code: string): Promise<ServiceResponseDto> {
    return await this.serviceService.findByCode(code);
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取服务', description: '通过服务ID获取服务详情' })
  @ApiParam({ name: 'id', description: '服务ID', example: 1 })
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<any> {
    return await this.serviceService.findEntityById(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新服务', description: '更新指定ID的服务信息' })
  @ApiParam({ name: 'id', description: '服务ID', example: 1 })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateServiceDto: UpdateServiceDto,
  ): Promise<ServiceResponseDto> {
    return await this.serviceService.update(id, updateServiceDto);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: '更新服务状态', description: '更新指定服务的状态' })
  @ApiParam({ name: 'id', description: '服务ID', example: 1 })
  async updateStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body('status') status: ServiceStatus,
  ): Promise<ServiceResponseDto> {
    return await this.serviceService.updateStatus(id, status);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除服务', description: '删除指定ID的服务（软删除）' })
  @ApiParam({ name: 'id', description: '服务ID', example: 1 })
  async remove(@Param('id', ParseIntPipe) id: number): Promise<{ message: string }> {
    return await this.serviceService.remove(id);
  }

  @Delete('batch')
  @ApiOperation({ summary: '批量删除服务', description: '批量删除多个服务' })
  async batchRemove(@Body('ids') ids: number[]): Promise<{ message: string }> {
    return await this.serviceService.batchRemove(ids);
  }
}
