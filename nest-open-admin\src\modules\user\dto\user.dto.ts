import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsEmail,
  IsOptional,
  IsObject,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsBoolean,
  IsPhoneNumber,
  MinLength,
  MaxLength,
  Min,
  IsDateString,
  IsInt,
  Max,
  Length,
  IsIn,
  IsUrl,
} from 'class-validator';
import { UserType, UserStatus, TierEnum, RoleEnum, UserVerificationStatus } from '../entities/user.entity';

/**
 * 创建用户DTO
 */
export class CreateUserDto {
  @ApiProperty({ description: '用户名', example: 'john_doe' })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(50)
  username: string;

  @ApiProperty({ description: '邮箱', example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: '密码', example: 'password123' })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  @MaxLength(100)
  password: string;

  @ApiPropertyOptional({ description: '真实姓名', example: 'John Doe' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  realName?: string;

  @ApiPropertyOptional({ description: '手机号', example: '+86 13800138000' })
  @IsOptional()
  @IsPhoneNumber()
  phone?: string;

  @ApiPropertyOptional({ description: '头像URL', example: 'https://example.com/avatar.jpg' })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiPropertyOptional({ description: '用户类型', enum: UserType, default: UserType.INDIVIDUAL })
  @IsOptional()
  @IsEnum(UserType)
  userType?: UserType;

  @ApiPropertyOptional({ description: '用户设置', example: { theme: 'dark', language: 'zh-CN' } })
  @IsOptional()
  @IsObject()
  settings?: Record<string, any>;

  @ApiPropertyOptional({ description: '用户等级', enum: TierEnum, default: TierEnum.BASIC })
  @IsOptional()
  @IsEnum(TierEnum)
  tier?: TierEnum;

  @ApiPropertyOptional({ description: '用户角色', enum: RoleEnum, default: RoleEnum.USER })
  @IsOptional()
  @IsEnum(RoleEnum)
  role?: RoleEnum;

  @ApiPropertyOptional({ description: '初始余额', example: 0, minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  balance?: number;

  @ApiPropertyOptional({ description: '验证码', example: '123456' })
  @IsOptional()
  @IsString()
  @Length(4, 8)
  captchaCode?: string;

  @ApiPropertyOptional({ description: '短信验证码', example: '123456' })
  @IsOptional()
  @IsString()
  @Length(4, 8)
  smsCode?: string;

  @ApiPropertyOptional({ description: '邮箱验证码', example: '123456' })
  @IsOptional()
  @IsString()
  @Length(4, 8)
  emailCode?: string;
}

/**
 * 更新用户DTO
 */
export class UpdateUserDto extends PartialType(CreateUserDto) {
  @ApiPropertyOptional({ description: '用户状态', enum: UserStatus })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;

  @ApiPropertyOptional({ description: '认证状态', enum: UserVerificationStatus })
  @IsOptional()
  @IsEnum(UserVerificationStatus)
  verificationStatus?: UserVerificationStatus;

  @ApiPropertyOptional({ description: '最后登录IP', example: '***********' })
  @IsOptional()
  @IsString()
  lastLoginIp?: string;

  @ApiPropertyOptional({ description: '最后登录时间' })
  @IsOptional()
  @IsDateString()
  lastLoginAt?: Date;
}

/**
 * 用户查询DTO
 */
export class QueryUserDto {
  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', example: 10, minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: '用户名搜索', example: 'john' })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiPropertyOptional({ description: '邮箱搜索', example: '<EMAIL>' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ description: '手机号搜索', example: '13800138000' })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({ description: '用户类型', enum: UserType })
  @IsOptional()
  @IsEnum(UserType)
  userType?: UserType;

  @ApiPropertyOptional({ description: '用户状态', enum: UserStatus })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;

  @ApiPropertyOptional({ description: '用户等级', enum: TierEnum })
  @IsOptional()
  @IsEnum(TierEnum)
  tier?: TierEnum;

  @ApiPropertyOptional({ description: '用户角色', enum: RoleEnum })
  @IsOptional()
  @IsEnum(RoleEnum)
  role?: RoleEnum;

  @ApiPropertyOptional({ description: '认证状态', enum: UserVerificationStatus })
  @IsOptional()
  @IsEnum(UserVerificationStatus)
  verificationStatus?: UserVerificationStatus;

  @ApiPropertyOptional({ description: '排序字段', example: 'createdAt' })
  @IsOptional()
  @IsString()
  @IsIn(['id', 'username', 'email', 'createdAt', 'updatedAt', 'lastLoginAt'])
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', example: 'DESC' })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}

/**
 * 修改密码DTO
 */
export class UpdatePwdDto {
  @ApiProperty({ description: '旧密码', example: 'oldPassword123' })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  @MaxLength(100)
  oldPassword: string;

  @ApiProperty({ description: '新密码', example: 'newPassword123' })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  @MaxLength(100)
  newPassword: string;

  @ApiProperty({ description: '确认新密码', example: 'newPassword123' })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  @MaxLength(100)
  confirmPassword: string;
}

/**
 * 更新用户资料DTO
 */
export class UpdateProfileDto {
  @ApiPropertyOptional({ description: '昵称', example: 'John' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  nickName?: string;

  @ApiPropertyOptional({ description: '真实姓名', example: 'John Doe' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  realName?: string;

  @ApiPropertyOptional({ description: '头像URL', example: 'https://example.com/avatar.jpg' })
  @IsOptional()
  @IsString()
  @IsUrl()
  avatar?: string;

  @ApiPropertyOptional({ description: '个人简介', example: '这是我的个人简介' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  bio?: string;

  @ApiPropertyOptional({ description: '用户设置', example: { theme: 'dark', language: 'zh-CN' } })
  @IsOptional()
  @IsObject()
  settings?: Record<string, any>;
}

/**
 * 用户响应DTO
 */
export class UserResponseDto {
  @ApiProperty({ description: '用户ID', example: 1 })
  id: number;

  @ApiProperty({ description: '用户名', example: 'john_doe' })
  username: string;

  @ApiProperty({ description: '邮箱', example: '<EMAIL>' })
  email: string;

  @ApiPropertyOptional({ description: '真实姓名', example: 'John Doe' })
  realName?: string;

  @ApiPropertyOptional({ description: '手机号', example: '+86 13800138000' })
  phone?: string;

  @ApiPropertyOptional({ description: '头像URL', example: 'https://example.com/avatar.jpg' })
  avatar?: string;

  @ApiProperty({ description: '用户类型', enum: UserType })
  userType: UserType;

  @ApiProperty({ description: '用户状态', enum: UserStatus })
  status: UserStatus;

  @ApiProperty({ description: '用户等级', enum: TierEnum })
  tier: TierEnum;

  @ApiProperty({ description: '用户角色', enum: RoleEnum })
  role: RoleEnum;

  @ApiProperty({ description: '认证状态', enum: UserVerificationStatus })
  verificationStatus: UserVerificationStatus;

  @ApiProperty({ description: '余额', example: 100.50 })
  balance: number;

  @ApiPropertyOptional({ description: '用户设置' })
  settings?: Record<string, any>;

  @ApiPropertyOptional({ description: '最后登录IP', example: '***********' })
  lastLoginIp?: string;

  @ApiPropertyOptional({ description: '最后登录时间' })
  lastLoginAt?: Date;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

/**
 * 用户列表响应DTO
 */
export class UserListResponseDto {
  @ApiProperty({ description: '用户列表', type: [UserResponseDto] })
  data: UserResponseDto[];

  @ApiProperty({ description: '总数', example: 100 })
  total: number;

  @ApiProperty({ description: '当前页', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  limit: number;

  @ApiProperty({ description: '总页数', example: 10 })
  totalPages: number;
}

/**
 * 企业认证DTO
 */
export class EnterpriseVerifyDto {
  @ApiProperty({ description: '企业名称', example: '北京科技有限公司' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  companyName: string;

  @ApiProperty({ description: '统一社会信用代码', example: '91110000123456789X' })
  @IsString()
  @IsNotEmpty()
  @Length(18, 18)
  creditCode: string;

  @ApiProperty({ description: '法定代表人', example: '张三' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  legalPerson: string;

  @ApiProperty({ description: '营业执照图片URL', example: 'https://example.com/license.jpg' })
  @IsString()
  @IsNotEmpty()
  @IsUrl()
  businessLicense: string;

  @ApiPropertyOptional({ description: '企业地址', example: '北京市朝阳区xxx街道xxx号' })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  address?: string;

  @ApiPropertyOptional({ description: '联系电话', example: '010-12345678' })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  contactPhone?: string;

  @ApiPropertyOptional({ description: '备注信息', example: '企业认证申请' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  remark?: string;
}

/**
 * 充值DTO
 */
export class RechargeDto {
  @ApiProperty({ description: '充值金额', example: 100, minimum: 0.01 })
  @IsNumber()
  @IsNotEmpty()
  @Min(0.01)
  amount: number;

  @ApiPropertyOptional({ description: '支付方式', example: 'alipay', enum: ['alipay', 'wechat', 'bank'] })
  @IsOptional()
  @IsString()
  @IsIn(['alipay', 'wechat', 'bank'])
  paymentMethod?: string;

  @ApiPropertyOptional({ description: '备注信息', example: '账户充值' })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  remark?: string;
}

/**
 * 重置密码DTO
 */
export class ResetPwdDto {
  @ApiProperty({ description: '新密码', example: 'newPassword123' })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  @MaxLength(100)
  newPassword: string;

  @ApiProperty({ description: '确认新密码', example: 'newPassword123' })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  @MaxLength(100)
  confirmPassword: string;

  @ApiPropertyOptional({ description: '重置令牌', example: 'reset-token-123' })
  @IsOptional()
  @IsString()
  resetToken?: string;

  @ApiPropertyOptional({ description: '邮箱验证码', example: '123456' })
  @IsOptional()
  @IsString()
  @Length(4, 8)
  emailCode?: string;
}

/**
 * 修改用户状态DTO
 */
/**
 * 实名认证DTO
 */
export class CertificationDto {
  @ApiProperty({ description: '真实姓名', example: '张三' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  realName: string;

  @ApiProperty({ description: '身份证号', example: '110101199001011234' })
  @IsString()
  @IsNotEmpty()
  @Length(15, 18)
  idCard: string;

  @ApiPropertyOptional({ description: '身份证正面照片URL', example: 'https://example.com/id-front.jpg' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  idCardFrontUrl?: string;

  @ApiPropertyOptional({ description: '身份证背面照片URL', example: 'https://example.com/id-back.jpg' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  idCardBackUrl?: string;

  @ApiPropertyOptional({ description: '手持身份证照片URL', example: 'https://example.com/id-hand.jpg' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  idCardHandUrl?: string;
}

/**
 * 用户列表查询DTO
 */
export class ListUserDto {
  @ApiPropertyOptional({ description: '页码', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', example: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: '用户名', example: 'john' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  username?: string;

  @ApiPropertyOptional({ description: '邮箱', example: '<EMAIL>' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ description: '真实姓名', example: '张三' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  realName?: string;

  @ApiPropertyOptional({ description: '用户状态', enum: UserStatus })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;

  @ApiPropertyOptional({ description: '用户类型', enum: UserType })
  @IsOptional()
  @IsEnum(UserType)
  userType?: UserType;

  @ApiPropertyOptional({ description: '排序字段', example: 'createdAt' })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiPropertyOptional({ description: '排序方向', enum: ['ASC', 'DESC'], example: 'DESC' })
  @IsOptional()
  @IsIn(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC';
}
export class ChangeStatusDto {
  @ApiProperty({ description: '用户状态', enum: UserStatus, example: UserStatus.ACTIVE })
  @IsEnum(UserStatus)
  @IsNotEmpty()
  userStatus: UserStatus;

  @ApiPropertyOptional({ description: '状态变更原因', example: '违规操作' })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  reason?: string;
}

/**
 * 手机注册DTO
 */
export class PhoneRegisterDto {
  @ApiProperty({ description: '手机号', example: '13800138000' })
  @IsString()
  @IsNotEmpty()
  @IsPhoneNumber('CN')
  phone: string;

  @ApiProperty({ description: '短信验证码', example: '123456' })
  @IsString()
  @IsNotEmpty()
  @Length(4, 8)
  smsCode: string;

  @ApiProperty({ description: '密码', example: 'password123' })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  @MaxLength(100)
  password: string;

  @ApiPropertyOptional({ description: '真实姓名', example: '张三' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  realName?: string;

  @ApiPropertyOptional({ description: '用户类型', enum: UserType, example: UserType.INDIVIDUAL })
  @IsOptional()
  @IsEnum(UserType)
  userType?: UserType;
}

/**
 * 手机登录DTO
 */
export class PhoneLoginDto {
  @ApiProperty({ description: '手机号', example: '13800138000' })
  @IsString()
  @IsNotEmpty()
  @IsPhoneNumber('CN')
  phone: string;

  @ApiProperty({ description: '短信验证码', example: '123456' })
  @IsString()
  @IsNotEmpty()
  @Length(4, 8)
  code: string;

  @ApiPropertyOptional({ description: '记住我', example: false })
  @IsOptional()
  @IsBoolean()
  rememberMe?: boolean;
}

/**
 * 邮箱登录DTO
 */
export class EmailLoginDto {
  @ApiProperty({ description: '邮箱', example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: '密码', example: 'password123' })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  @MaxLength(100)
  password: string;

  @ApiPropertyOptional({ description: '记住我', example: false })
  @IsOptional()
  @IsBoolean()
  rememberMe?: boolean;
}