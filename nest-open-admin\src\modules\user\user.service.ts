import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
  HttpException,
  HttpStatus,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { ApiException } from '@/common/exceptions/api.exception';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In } from 'typeorm';
import {
  UserEntity,
  UserType,
  UserVerificationStatus,
  UserStatus,
} from './entities/user.entity';
import {
  CreateUserDto,
  UpdateUserDto,
  QueryUserDto,
  ChangeStatusDto,
  ResetPwdDto,
  UpdateProfileDto,
  UpdatePwdDto,
  RechargeDto,
  EnterpriseVerifyDto,
} from './dto';
import { getUserUsageConfig } from './user.config';
import { GetNowDate, GenerateUUID, Uniq } from 'src/common/utils/index';
import * as bcrypt from 'bcrypt';
import { validPhone, validEmail } from '@/common/utils/validate';
import { CaptchaService } from '@/shared/captcha.service';
import { KeyManagementService } from '../api-key/services/key-management.service';
import { RedisService } from '@/shared/redis.service';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    @InjectRepository(UserEntity)
    private userRepository: Repository<UserEntity>,
    private dataSource: DataSource,
    private readonly captchaService: CaptchaService,
    private readonly redisService: RedisService,
    @Inject(forwardRef(() => KeyManagementService))
    private readonly keyManagementService?: KeyManagementService,
  ) {
    this.logger.log('UserService初始化 - KeyManagementService是否注入: ' + (keyManagementService ? 'Yes' : 'No'));
  }

  /**
   * 注册
   */
  async register(registerUserDto: CreateUserDto) {
    const lastLoginAt = GetNowDate();
    const { phone, username, email, emailCode, captchaCode } = registerUserDto;
    if (phone) {
      if (!validPhone(phone)) {
        throw new ApiException(30001);
      }
    }
    if (email) {
      if (!validEmail(email)) {
        throw new ApiException(30002);
      }
    }

    // 验证邮箱验证码
    if (email && (emailCode || captchaCode)) {
      const code: string = (emailCode || captchaCode) as string;
      const emailCodeResult = await this.captchaService.verifyEmailCode(
        email,
        code,
        'register',
      );
      if (!emailCodeResult.valid) {
        throw new ApiException(10024); // 或者使用现有的 10002
      }

      // 检查安全验证
      if (emailCodeResult.securityCheck === false) {
        console.warn(`用户注册安全检查失败: ${email}`);
        // 可以选择是否阻止注册或记录风险
      }
    } else if (email) {
      throw new ApiException(30003);
    }

    // 防止重复创建 start
    if (username && await this.userRepository.findOne({ where: { username } }))
      throw new ApiException(30004);
    if (phone && validPhone(phone)) {
      if (await this.userRepository.findOne({ where: { phone: phone } })) {
        throw new ApiException(30005);
      }
    }
    if (email && validEmail(email)) {
      if (await this.userRepository.findOne({ where: { email: email } }))
        throw new ApiException(30006);
    }

    // 生成用户名：如果没有提供用户名，使用邮箱或手机号作为用户名
    let finalUsername = username;
    if (!finalUsername) {
      if (email) {
        finalUsername = email;
      } else if (phone) {
        finalUsername = phone;
      } else {
        finalUsername = 'user_' + Date.now().toString().slice(-8);
      }
    }

    // 确保用户名唯一性
    let uniqueUsername = finalUsername;
    let counter = 1;
    while (await this.userRepository.findOne({ where: { username: uniqueUsername } })) {
      uniqueUsername = `${finalUsername}_${counter}`;
      counter++;
    }

    const newUser = this.userRepository.create({
      username: uniqueUsername,
      nickname: uniqueUsername, // 默认昵称与用户名相同
      email: registerUserDto.email,
      password: registerUserDto.password,
      realName: registerUserDto?.realName,
      phone: registerUserDto?.phone,
      avatar: registerUserDto?.avatar,
      userType: (registerUserDto?.userType ?? UserType.INDIVIDUAL) as UserType,
      settings: registerUserDto?.settings ? JSON.stringify(registerUserDto.settings) : undefined,
      tier: registerUserDto?.tier,
      role: registerUserDto?.role,
      balance: registerUserDto?.balance ?? 0,
      lastLoginAt: new Date(lastLoginAt),
    }) as any;

    // 保存用户并创建API密钥
    const savedUser = await this.userRepository.save(newUser);

    // 自动创建API密钥
    await this.createApiKeyForUser(savedUser.id);

    return savedUser;
  }

  /**
   * 创建用户（支持邮箱验证优先）
   */
  async createUser(createUserDto: CreateUserDto): Promise<Record<string,any>> {
    const { phone, username, email, userType, captchaCode, smsCode } =
      createUserDto;

    // 验证必须提供邮箱或手机号
    if (!email && !phone) {
      throw new ApiException(30007);
    }

    // 验证邮箱格式
    if (email && !validEmail(email)) {
      throw new ApiException(30002);
    }

    // 验证手机号格式
    if (phone && !validPhone(phone)) {
      throw new ApiException(30001);
    }

    let emailVerified = false;
    let phoneVerified = false;

    // 优先使用邮箱验证（低成本方案）
    if (email) {
      // 检查邮箱是否已存在
      if (await this.userRepository.findOne({ where: { email } })) {
        throw new ApiException(30006);
      }

      // 验证邮箱验证码
      if (captchaCode) {
        const emailCodeResult = await this.captchaService.verifyEmailCode(
          email,
          captchaCode,
          'register',
        );
        if (!emailCodeResult.valid) {
          throw new ApiException(10024);
        }

        // 检查安全验证
        if (emailCodeResult.securityCheck === false) {
          console.warn(`用户注册安全检查失败: ${email}`);
          // 可以选择是否阻止注册或记录风险
        }
        emailVerified = true;
      } else {
        throw new ApiException(30008);
      }
    }

    // 手机号验证（备用方案）
    if (phone && !emailVerified) {
      // 检查手机号是否已存在
      if (await this.userRepository.findOne({ where: { phone } })) {
        throw new ApiException(30005);
      }

      // 验证短信证码
      if (smsCode) {
        const smsCodeResult = await this.captchaService.verifySmsCode(
          phone,
          smsCode,
          'register',
        );
        if (!smsCodeResult) {
          throw new ApiException(30009);
        }

        phoneVerified = true;
      } else {
        throw new ApiException(30008);
      }
    }

    // 检查用户名是否已存在（如果提供）
    if (
      username &&
      (await this.userRepository.findOne({ where: { username } }))
    ) {
      throw new ApiException(30004);
    }

    // 获取使用次数配置
    const usageConfig = getUserUsageConfig();
    const lastLoginAt = GetNowDate();

    // 创建新用户
    const newUser = this.userRepository.create({
      ...createUserDto,
      lastLoginAt,
      userType: (userType ? userType : UserType.INDIVIDUAL) as UserType,
      balance: 0,
      verificationStatus: UserVerificationStatus.UNVERIFIED,
      remainingUsage: usageConfig.unverifiedDefaultUsage,
      totalUsage: 0,
      emailVerified,
      phoneVerified,
      settings: createUserDto.settings ? JSON.stringify(createUserDto.settings) : undefined
    });

    const savedUser = await this.userRepository.save(newUser);

    // 自动创建API密钥
    await this.createApiKeyForUser(savedUser.id);

    return {
      id: savedUser.id,
      email: savedUser.email,
      phone: savedUser.phone,
      userType: savedUser.userType,
      balance: savedUser.balance,
      emailVerified: savedUser.emailVerified,
      phoneVerified: savedUser.phoneVerified,
    };
  }

  /**
   * 为新用户创建API密钥和授权默认服务
   */
  private async createApiKeyForUser(userId: number): Promise<void> {
    try {
      this.logger.log(`开始为用户 #${userId} 创建API密钥...`);
      
      if (!this.keyManagementService) {
        this.logger.error(`KeyManagementService未注入到UserService中，无法创建API密钥`);
        throw new Error('KeyManagementService not available');
      }
      
      // 检查用户是否已经有API密钥
      const existingApiKeys = await this.dataSource
        .createQueryBuilder()
        .select('id')
        .from('open_api_key', 'api_key')
        .where('api_key.userId = :userId', { userId })
        .andWhere('api_key.deletedAt IS NULL')
        .getRawMany();
        
      if (existingApiKeys && existingApiKeys.length > 0) {
        this.logger.log(`用户 #${userId} 已有 ${existingApiKeys.length} 个API密钥，跳过创建`);
        return;
      }
      
      // 创建API密钥
      this.logger.log(`调用KeyManagementService.createUserMasterKey为用户 #${userId} 创建密钥`);
      const apiKey = await this.keyManagementService.createUserMasterKey(userId);
      this.logger.log(`为用户 #${userId} 创建API密钥成功: ${apiKey.id}`);
      
      // 获取默认服务列表进行授权
      try {
        const defaultServiceIds = await this.getDefaultServiceIds();
        this.logger.log(`获取到默认服务ID: ${JSON.stringify(defaultServiceIds)}`);
        
        if (defaultServiceIds && defaultServiceIds.length > 0) {
          await this.keyManagementService.batchAssignServices(userId, defaultServiceIds, 10);
          this.logger.log(`为用户 #${userId} 授权默认服务成功: ${JSON.stringify(defaultServiceIds)}`);
        } else {
          this.logger.warn(`没有找到默认服务ID，跳过授权`);
        }
      } catch (error) {
        this.logger.error(`为用户 #${userId} 授权默认服务失败: ${error.message}`, error.stack);
        // 不抛出异常，不阻止注册流程
      }
    } catch (error) {
      this.logger.error(`为用户 #${userId} 创建API密钥失败: ${error.message}`, error.stack);
      
      // 尝试使用数据库直接创建API密钥作为备选方案
      try {
        this.logger.log(`尝试使用备选方案为用户 #${userId} 创建API密钥...`);
        
        // 生成UUID
        const crypto = require('crypto');
        const generateUUID = () => {
          return crypto.randomBytes(16).toString('hex');
        };
        
        // 生成API密钥和秘钥
        const apiKey = `ak_${generateUUID()}`;
        const secretKey = `sk_${generateUUID()}`;
        
        // 哈希秘钥
        const bcrypt = require('bcrypt');
        const salt = await bcrypt.genSalt(10);
        const secretHash = await bcrypt.hash(secretKey, salt);
        
        // 直接插入API密钥记录
        await this.dataSource
          .createQueryBuilder()
          .insert()
          .into('open_api_key')
          .values({
            userId,
            serviceId: 0, // 0表示平台级密钥，不绑定特定服务
            apiKey,
            secretHash,
            name: '用户主密钥(备选方案)',
            keyType: 'trial', // 使用枚举中定义的有效值：trial, basic, premium, enterprise
            keyStatus: 'active',
            permissions: JSON.stringify(['*']),
            description: '系统备选方案创建的主密钥',
            isSecretViewed: false,
            createdAt: new Date(),
            updatedAt: new Date()
          })
          .execute();
        
        this.logger.log(`备选方案成功为用户 #${userId} 创建API密钥: ${apiKey}`);
      } catch (backupError) {
        this.logger.error(`备选方案为用户 #${userId} 创建API密钥失败: ${backupError.message}`, backupError.stack);
        // 仍然不阻止注册流程
      }
    }
  }
  
  /**
   * 获取默认服务ID列表
   * 返回所有服务的ID
   */
  private async getDefaultServiceIds(): Promise<number[]> {
    try {
      // 从数据库中查询所有服务
      const services = await this.dataSource
        .createQueryBuilder()
        .select('id')
        .from('open_service', 'service')
        .where('service.deletedAt IS NULL')
        .getRawMany();

      this.logger.log(`找到 ${services.length} 个服务`);
      
      // 提取服务ID
      const serviceIds = services.map(service => service.id);
      this.logger.log(`默认服务ID列表: ${JSON.stringify(serviceIds)}`);
      
      return serviceIds;
    } catch (error) {
      this.logger.error(`获取默认服务ID失败: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * 个人中心-用户信息
   * @param user
   * @returns
   */
  async profile(user) {
    // 返回用户信息，过滤敏感字段
    const {
      password,
      refreshToken,
      ...userInfo
    } = user;
    
    return userInfo;
  }
  /**
   * 根据ID查找用户
   */
  async findById(id: number): Promise<UserEntity> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['apiKeys'],
    });

    if (!user) {
      throw new ApiException(30010);
    }

    return user;
  }

  /**
   * 根据邮箱查找用户
   */
  async findByEmail(email: string): Promise<UserEntity | null> {
    return this.userRepository.findOne({ where: { email } });
  }

  /**
   * 根据用户名查找用户
   */
  async findByUsername(username: string): Promise<UserEntity | null> {
    return this.userRepository.findOne({ where: { username } });
  }

  /**
   * 根据手机号查找用户
   */
  async findByPhone(phone: string): Promise<UserEntity | null> {
    return this.userRepository.findOne({ where: { phone } });
  }

  /**
   * 通过手机号创建用户（用于静默注册）
   */
  async createUserByPhone(userData: {
    phone: string;
    userType?: string;
    userStatus?: string;
    level?: string;
    role?: string;
  }): Promise<UserEntity> {
    const { phone, userType = 'individual' } = userData;

    // 验证手机号格式
    if (!validPhone(phone)) {
      throw new ApiException(30001);
    }

    // 检查手机号是否已存在
    const existingUser = await this.userRepository.findOne({ where: { phone } });
    if (existingUser) {
      throw new ApiException(30005);
    }

    // 获取使用次数配置
    const usageConfig = getUserUsageConfig();
    const lastLoginAt = GetNowDate();

    // 生成用户名：使用手机号
    let username = phone;
    let counter = 1;
    while (await this.userRepository.findOne({ where: { username } })) {
      username = `${phone}_${counter}`;
      counter++;
    }

    // 创建新用户
    const newUser = this.userRepository.create({
      phone,
      username,
      nickname: username,
      password: `szyl_${phone}`, // 设置默认密码为'szyl_'加上手机号
      userType: userType as UserType,
      balance: 0,
      phoneVerified: true, // 通过短信验证码注册，手机号已验证
      emailVerified: false,
      verificationStatus: UserVerificationStatus.UNVERIFIED,
      remainingUsage: usageConfig.unverifiedDefaultUsage,
      totalUsage: 0,
      userStatus: UserStatus.ACTIVE, // 静默注册直接激活
      lastLoginAt,
    });

    const savedUser = await this.userRepository.save(newUser);
    
    // 为静默注册用户创建API密钥
    try {
      await this.createApiKeyForUser(savedUser.id);
      this.logger.log(`为静默注册用户 #${savedUser.id} 创建API密钥成功`);
    } catch (error) {
      this.logger.error(`为静默注册用户 #${savedUser.id} 创建API密钥失败: ${error.message}`, error.stack);
      // 不阻止注册流程，继续返回用户
    }

    return savedUser;
  }

  /**
   * 创建用户（简化版本，供认证服务使用）
   */
  async create(userData: {
    email?: string;
    phone?: string;
    password: string;
    userType?: UserType;
    emailVerified?: boolean;
    phoneVerified?: boolean;
    createApiKey?: boolean; // 是否创建API密钥，默认为true
  }): Promise<UserEntity> {
    // 获取使用次数配置
    const usageConfig = getUserUsageConfig();

    // 生成用户名：优先使用邮箱前缀，其次使用手机号，最后生成随机用户名
    let username = '';
    if (userData.email) {
      username = userData.email;
    } else if (userData.phone) {
      username = userData.phone;
    } else {
      username = 'user_' + Date.now().toString().slice(-8);
    }

    // 确保用户名唯一性
    let finalUsername = username;
    let counter = 1;
    while (
      await this.userRepository.findOne({ where: { username: finalUsername } })
    ) {
      finalUsername = `${username}_${counter}`;
      counter++;
    }

    const user = this.userRepository.create({
      email: userData.email,
      phone: userData.phone,
      username: finalUsername,
      nickname: finalUsername, // 默认昵称与用户名相同
      password: userData.password,
      userType: userData.userType || UserType.INDIVIDUAL,
      balance: 0,
      emailVerified: userData.emailVerified || false,
      phoneVerified: userData.phoneVerified || false,
      verificationStatus: UserVerificationStatus.UNVERIFIED,
      remainingUsage: usageConfig.unverifiedDefaultUsage,
      totalUsage: 0,
      userStatus: UserStatus.ACTIVE, // 注册时直接激活
    });

    const savedUser = await this.userRepository.save(user);
    
    // 默认创建API密钥，除非明确指定不创建
    if (userData.createApiKey !== false) {
      try {
        await this.createApiKeyForUser(savedUser.id);
        this.logger.log(`为新创建用户 #${savedUser.id} 创建API密钥成功`);
      } catch (error) {
        this.logger.error(`为新创建用户 #${savedUser.id} 创建API密钥失败: ${error.message}`, error.stack);
        // 不阻止注册流程
      }
    }

    return savedUser;
  }

  /**
   * 更新用户信息
   */
  async updateUser(
    id: number,
    updateUserDto: UpdateUserDto,
  ): Promise<Record<string,any>> {
    const user = await this.findById(id);

    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingUser = await this.userRepository.findOne({
        where: { email: updateUserDto.email },
      });
      if (existingUser) {
        throw new ApiException(30006);
      }
    }

    // 密码加密由实体的 @BeforeUpdate 生命周期钩子自动处理
    // 直接更新用户信息，密码会在保存前自动加密
    Object.assign(user, updateUserDto);
    const updatedUser = await this.userRepository.save(user);

    return {
      id: updatedUser.id,
      email: updatedUser.email,
      userType: updatedUser.userType,
      balance: updatedUser.balance,
    };
  }

  /**
   * 分页查询用户列表
   */
  async findUsers(queryUserDto: QueryUserDto): Promise<Record<string,any>> {
    const { page = 1, limit = 10, email, userType } = queryUserDto;

    const queryBuilder = this.userRepository.createQueryBuilder('user');

    if (email) {
      queryBuilder.andWhere('user.email LIKE :email', { email: `%${email}%` });
    }

    if (userType) {
      queryBuilder.andWhere('user.userType = :userType', { userType });
    }

    const [users, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      list: users.map((user) => ({
        id: user.id,
        email: user.email,
        type: user.userType,
        balance: user.balance,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      })),
      total,
      page,
      limit,
    };
  }

  /**
   * 删除用户
   */
  async deleteUser(id: number): Promise<Record<string,any>> {
    const user = await this.findById(id);
    
    try {
      this.logger.log(`执行用户 #${user.id} 删除前的清理工作`);
      
      // 1. 清除用户缓存
      await this.clearUserCache(user.id);
      
      // 2. 删除用户API密钥（包括缓存和数据库记录）
      await this.deleteUserApiKeys(user.id);
      
      // 3. 将用户的所有JWT令牌加入黑名单
      await this.invalidateUserTokens(user.id);
    } catch (error) {
      this.logger.error(`用户 #${user.id} 清理工作失败: ${error.message}`, error.stack);
    }
    
    // 执行用户删除操作
    await this.userRepository.remove(user);
    
    return { message: '用户删除成功' };
  }

  /**
   * 删除用户（批量）
   */
  async remove(userIds: number[]): Promise<Record<string,any>> {
    const users = await this.userRepository.findBy({ id: In(userIds) });

    if (users.length === 0) {
      throw new ApiException(30010);
    }
    
    // 用户删除前的清理工作，包括缓存、令牌和API密钥
    for (const user of users) {
      try {
        this.logger.log(`执行用户 #${user.id} 删除前的清理工作`);
        
        // 1. 清除用户缓存
        await this.clearUserCache(user.id);
        
        // 2. 删除用户API密钥（包括缓存和数据库记录）
        await this.deleteUserApiKeys(user.id);
        
        // 3. 将用户的所有JWT令牌加入黑名单
        await this.invalidateUserTokens(user.id);
      } catch (error) {
        this.logger.error(`用户 #${user.id} 清理工作失败: ${error.message}`, error.stack);
      }
    }

    // 执行用户删除操作
    await this.userRepository.remove(users);

    return {
      message: `成功删除 ${users.length} 个用户`,
      deletedCount: users.length,
    };
  }

  /**
   * 锁定用户账户（用于事务操作）
   */
  async lockUser(id: number): Promise<UserEntity> {
    const user = await this.userRepository.findOne({
      where: { id },
      lock: { mode: 'pessimistic_write' },
    });
    if (!user) {
      throw new ApiException(30010);
    }
    return user;
  }

  /**
   * 更新用户余额
   */
  async updateBalance(id: number, amount: number): Promise<Record<string,any>> {
    return this.dataSource.transaction(async (manager) => {
      const user = await manager.findOne(UserEntity, {
        where: { id },
        lock: { mode: 'pessimistic_write' },
      });

      if (!user) {
        throw new ApiException(30010);
      }

      const newBalance = Number(user.balance) + amount;
      if (newBalance < 0) {
        throw new ApiException(30011);
      }

      user.balance = newBalance;
      await manager.save(user);

      return { balance: user.balance };
    });
  }

  /**
   * 检查用户是否有足够的使用次数
   */
  async checkUsageAvailable(
    userId: number,
    requiredUsage: number = 1,
  ): Promise<boolean> {
    const user = await this.findById(userId);
    return user.remainingUsage >= requiredUsage;
  }

  /**
   * 扣减用户使用次数
   */
  async deductUsage(
    userId: number,
    usageCount: number = 1,
  ): Promise<Record<string,any>> {
    return this.dataSource.transaction(async (manager) => {
      const user = await manager.findOne(UserEntity, {
        where: { id: userId },
        lock: { mode: 'pessimistic_write' },
      });

      if (!user) {
        throw new ApiException(30010);
      }

      if (user.remainingUsage < usageCount) {
        throw new ApiException(30012);
      }

      user.remainingUsage -= usageCount;
      user.totalUsage += usageCount;
      await manager.save(user);

      return {
        id: user.id,
        remainingUsage: user.remainingUsage,
        totalUsage: user.totalUsage,
      };
    });
  }

  /**
   * 增加用户使用次数
   */
  async addUsage(userId: number, usageCount: number): Promise<Record<string,any>> {
    return this.dataSource.transaction(async (manager) => {
      const user = await manager.findOne(UserEntity, {
        where: { id: userId },
        lock: { mode: 'pessimistic_write' },
      });

      if (!user) {
        throw new ApiException(30010);
      }

      user.remainingUsage += usageCount;
      await manager.save(user);

      return {
        id: user.id,
        remainingUsage: user.remainingUsage,
        totalUsage: user.totalUsage,
      };
    });
  }

  /**
   * 更新用户认证状态并调整使用次数
   */
  async updateVerificationStatus(
    userId: number,
    status: UserVerificationStatus,
  ): Promise<Record<string,any>> {
    return this.dataSource.transaction(async (manager) => {
      const user = await manager.findOne(UserEntity, {
        where: { id: userId },
        lock: { mode: 'pessimistic_write' },
      });

      if (!user) {
        throw new ApiException(30010);
      }

      const oldStatus = user.verificationStatus;
      user.verificationStatus = status;

      // 根据新的认证状态调整使用次数
      const usageConfig = getUserUsageConfig();
      let bonusUsage = 0;

      if (oldStatus === UserVerificationStatus.UNVERIFIED) {
        if (status === UserVerificationStatus.PERSONAL_VERIFIED) {
          bonusUsage = usageConfig.personalVerificationBonus;
        } else if (status === UserVerificationStatus.ENTERPRISE_VERIFIED) {
          bonusUsage = usageConfig.enterpriseVerificationBonus;
        }
      } else if (
        oldStatus === UserVerificationStatus.PERSONAL_VERIFIED &&
        status === UserVerificationStatus.ENTERPRISE_VERIFIED
      ) {
        // 从个人认证升级到企业认证，给予企业认证奖励减去个人认证奖励的差额
        bonusUsage =
          usageConfig.enterpriseVerificationBonus -
          usageConfig.personalVerificationBonus;
      }

      if (bonusUsage > 0) {
        user.remainingUsage += bonusUsage;
      }

      await manager.save(user);

      return {
        id: user.id,
        verificationStatus: user.verificationStatus,
        remainingUsage: user.remainingUsage,
        bonusUsage,
      };
    });
  }

  /**
   * 获取用户每日免费使用次数
   */
  async getDailyFreeUsage(userId: number): Promise<number> {
    const usageConfig = getUserUsageConfig();
    return usageConfig.dailyFreeUsage;
  }

  /**
   * 检查并重置每日免费使用次数
   */
  async checkAndResetDailyFreeUsage(userId: number): Promise<Record<string,any>> {
    const user = await this.findById(userId);
    const today = new Date().toDateString();
    const lastResetDate = user.lastDailyResetDate
      ? user.lastDailyResetDate.toDateString()
      : null;

    if (lastResetDate !== today) {
      const usageConfig = getUserUsageConfig();
      user.dailyFreeUsageRemaining = usageConfig.dailyFreeUsage;
      user.lastDailyResetDate = new Date();
      await this.userRepository.save(user);

      return {
        message: '每日免费使用次数已重置',
        dailyFreeUsageRemaining: user.dailyFreeUsageRemaining,
      };
    }

    return {
      dailyFreeUsageRemaining: user.dailyFreeUsageRemaining || 0,
    };
  }

  /**
   * 使用每日免费次数
   */
  async useDailyFreeUsage(
    userId: number,
    usageCount: number = 1,
  ): Promise<boolean> {
    return this.dataSource.transaction(async (manager) => {
      const user = await manager.findOne(UserEntity, {
        where: { id: userId },
        lock: { mode: 'pessimistic_write' },
      });

      if (!user) {
        return false;
      }
      
      // 检查用户是否有资格使用免费额度
      if (!user.isFreeQuotaEligible) {
        this.logger.debug(`用户 #${userId} 不再享有免费额度资格，已付费`);
        return false;
      }

      // 检查并重置每日免费使用次数
      const today = new Date().toDateString();
      const lastResetDate = user.lastDailyResetDate
        ? user.lastDailyResetDate.toDateString()
        : null;

      if (lastResetDate !== today) {
        const usageConfig = getUserUsageConfig();
        user.dailyFreeUsageRemaining = usageConfig.dailyFreeUsage;
        user.lastDailyResetDate = new Date();
      }

      if ((user.dailyFreeUsageRemaining || 0) >= usageCount) {
        user.dailyFreeUsageRemaining =
          (user.dailyFreeUsageRemaining || 0) - usageCount;
        await manager.save(user);
        return true;
      }

      return false;
    });
  }

  /**
   * 获取用户使用统计
   */
  async getUserUsageStats(userId: number): Promise<Record<string,any>> {
    const user = await this.findById(userId);

    return {
      userId: user.id,
      email: user.email,
      verificationStatus: user.verificationStatus,
      remainingUsage: user.remainingUsage,
      totalUsage: user.totalUsage,
      balance: user.balance,
    };
  }

  /**
   * 验证用户密码
   */
  async validatePassword(user: UserEntity, password: string): Promise<boolean> {
    if (!user || !user.password || !password) {
      this.logger.error(`验证密码失败: user=${!!user}, password=${!!password}, hashedPassword=${!!user?.password}`);
      return false;
    }
    try {
      return await bcrypt.compare(password, user.password);
    } catch (error) {
      this.logger.error(`密码验证错误: ${error.message}`);
      return false;
    }
  }

  /**
   * 更新用户密码
   */
  async updateUserPassword(
    user: any,
    updatePwdDto: UpdatePwdDto,
  ): Promise<Record<string,any>> {
    const { oldPassword, newPassword, confirmPassword } = updatePwdDto;

    // 从数据库获取完整的用户信息，包括密码哈希
    const fullUserInfo = await this.userRepository.findOne({ where: { id: user.id } });
    
    if (!fullUserInfo) {
      throw new ApiException(30010); // 用户不存在
    }

    // 验证原密码
    const isOldPasswordValid = await this.validatePassword(fullUserInfo, oldPassword);
    if (!isOldPasswordValid) {
      throw new ApiException(30015); // 原密码错误
    }

    // 验证新密码确认
    if (newPassword !== confirmPassword) {
      throw new ApiException(30013); // 两次密码输入不一致
    }

    // 更新密码
    fullUserInfo.password = newPassword; // 密码会在保存前自动加密

    await this.userRepository.save(fullUserInfo);

    return {
      message: '密码更新成功',
    };
  }

  /**
   * 更新用户密码（管理员重置）
   */
  async updatePassword(userId: number, plainPassword: string): Promise<void> {
    const user = await this.findById(userId);
    user.password = plainPassword; // 密码会在保存前由 @BeforeUpdate 钩子自动加密
    await this.userRepository.save(user);
  }

  /**
   * 手机号注册
   */
  async phoneRegister(phoneRegisterDto: any): Promise<Record<string,any>> {
    const { phone, smsCode, password, confirmPassword, userType } =
      phoneRegisterDto;

    // 验证密码确认
    if (password !== confirmPassword) {
      throw new ApiException(30013);
    }

    // 验证手机验证码
    const isCodeValid = await this.captchaService.verifySmsCode(phone, smsCode, 'register');
    if (!isCodeValid) {
      throw new ApiException(30009);
    }

    // 检查手机号是否已注册
    const existingUser = await this.findByPhone(phone);
    if (existingUser) {
      throw new ApiException(30005);
    }

    // 创建用户
    const user = await this.create({
      phone,
      password,
      userType: (userType || UserType.INDIVIDUAL) as UserType,
      phoneVerified: true,
      // API密钥会在create方法中自动创建
    });

    return {
      id: user.id,
      phone: user.phone,
      username: user.username,
      userType: user.userType,
      message: '注册成功',
    };
  }

  /**
   * 手机号登录
   */
  async phoneLogin(phoneLoginDto: any): Promise<Record<string,any>> {
    const { phone, code, rememberMe } = phoneLoginDto;

    // 验证手机验证码
    const isCodeValid = await this.captchaService.verifySmsCode(phone, code, 'login');
    if (!isCodeValid) {
      throw new ApiException(30009);
    }

    // 查找用户
    let user = await this.findByPhone(phone);
    let isNewUser = false;
    
    if (!user) {
      // 如果用户不存在，自动注册
      user = await this.create({
        phone,
        password: 'auto_generated_' + Date.now(), // 自动生成密码
        userType: UserType.INDIVIDUAL,
        phoneVerified: true,
        // API密钥会在create方法中自动创建
      });
      isNewUser = true;
    }

    // 更新登录信息
    user.lastLoginAt = new Date();
    user.loginFailCount = 0;
    await this.userRepository.save(user);

    return {
      id: user.id,
      phone: user.phone,
      username: user.username,
      userType: user.userType,
      rememberMe,
      isNewUser, // 新增标记是否为新用户
      message: isNewUser ? '首次登录成功，已自动注册' : '登录成功',
    };
  }

  /**
   * 实名认证
   */
  async certification(certificationDto: any): Promise<Record<string,any>> {
    const { realName, idCard, companyName, creditCode, type } =
      certificationDto;

    // 这里应该调用第三方实名认证服务进行验证
    // 暂时模拟认证成功
    const verificationResult = {
      success: true,
      message: '认证成功',
    };

    if (!verificationResult.success) {
      throw new ApiException(30014);
    }

    return {
      realName,
      idCard: idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2'), // 脱敏处理
      companyName,
      creditCode: creditCode
        ? creditCode.replace(/(\w{10})\w{6}(\w{2})/, '$1******$2')
        : undefined,
      type,
      status: 'verified',
      message: '实名认证成功',
    };
  }

  /**
   * 获取用户列表
   */
  async list(listUserDto: any): Promise<Record<string,any>> {
    const {
      page = 1,
      limit = 10,
      email,
      userType,
      userStatus,
      keyword,
    } = listUserDto;

    const queryBuilder = this.userRepository.createQueryBuilder('user');

    if (email) {
      queryBuilder.andWhere('user.email LIKE :email', { email: `%${email}%` });
    }

    if (userType) {
      queryBuilder.andWhere('user.userType = :userType', { userType });
    }

    if (userStatus) {
      queryBuilder.andWhere('user.userStatus = :userStatus', { userStatus });
    }

    if (keyword) {
      queryBuilder.andWhere(
        '(user.username LIKE :keyword OR user.email LIKE :keyword OR user.realName LIKE :keyword)',
        { keyword: `%${keyword}%` },
      );
    }

    const [users, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .orderBy('user.createdAt', 'DESC')
      .getManyAndCount();

    return {
      list: users.map((user) => ({
        id: user.id,
        username: user.username,
        email: user.email,
        phone: user.phone,
        realName: user.realName,
        userType: user.userType,
        status: user.userStatus,
        balance: user.balance,
        emailVerified: user.emailVerified,
        phoneVerified: user.phoneVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      })),
      total,
      page,
      limit,
    };
  }

  /**
   * 更改用户状态
   */
  async changeStatus(
    id: number,
    changeStatusDto: ChangeStatusDto,
  ): Promise<Record<string,any>> {
    const { userStatus, reason } = changeStatusDto;
    const user = await this.findById(id);

    user.userStatus = userStatus;

    // 如果是锁定状态，设置锁定时间
    if (userStatus === UserStatus.LOCKED) {
      user.lockedUntil = new Date(Date.now() + 24 * 60 * 60 * 1000); // 锁定24小时
    } else if (userStatus === UserStatus.ACTIVE) {
      user.lockedUntil = undefined;
    }

    await this.userRepository.save(user);

    return {
      id: user.id,
      userStatus,
      reason,
      message: '用户状态更新成功',
    };
  }

  /**
   * 重置用户密码
   */
  async resetPassword(
    id: number,
    resetPwdDto: ResetPwdDto,
  ): Promise<Record<string,any>> {
    const { newPassword, confirmPassword } = resetPwdDto;

    if (newPassword !== confirmPassword) {
      throw new ApiException(30013);
    }

    const user = await this.findById(id);
    user.password = newPassword; // 密码会在保存前自动加密

    await this.userRepository.save(user);

    return {
      id: user.id,
      message: '密码重置成功',
    };
  }

  /**
   * 更新个人资料
   */
  async updateProfile(
    user: UserEntity,
    updateProfileDto: UpdateProfileDto,
  ): Promise<Record<string,any>> {
    const { nickName, realName, avatar, bio, settings } = updateProfileDto;

    if (nickName !== undefined) user.nickname = nickName;
    if (realName !== undefined) user.realName = realName;
    if (avatar !== undefined) user.avatar = avatar;
    if (bio !== undefined) user.bio = bio;
    if (settings !== undefined) user.settings = JSON.stringify(settings);

    const updatedUser = await this.userRepository.save(user);

    return {
      id: updatedUser.id,
      nickname: updatedUser.nickname,
      realName: updatedUser.realName,
      avatar: updatedUser.avatar,
      bio: updatedUser.bio,
      settings: updatedUser.settings,
      message: '个人资料更新成功',
    };
  }

  /**
   * 获取API使用情况
   */
  async getApiUsage(user: UserEntity): Promise<Record<string,any>> {
    // 这里应该从API调用记录表中统计数据
    // 暂时返回模拟数据
    const mockUsageData = {
      totalCalls: user.totalUsage || 0,
      successCalls: Math.floor((user.totalUsage || 0) * 0.95),
      failedCalls: Math.floor((user.totalUsage || 0) * 0.05),
      todayCalls: Math.floor(Math.random() * 100),
      monthCalls: Math.floor((user.totalUsage || 0) * 0.3),
      remainingUsage: user.remainingUsage,
      serviceDistribution: {
        OCR: Math.floor((user.totalUsage || 0) * 0.4),
        NLP: Math.floor((user.totalUsage || 0) * 0.3),
        CV: Math.floor((user.totalUsage || 0) * 0.3),
      },
    };

    return mockUsageData;
  }

  /**
   * 获取账户余额
   */
  async getBalance(user: UserEntity): Promise<Record<string,any>> {
    return {
      userId: user.id,
      balance: user.balance,
      remainingUsage: user.remainingUsage,
      totalUsage: user.totalUsage,
      verificationStatus: user.verificationStatus,
    };
  }

  /**
   * 账户充值
   */
  async recharge(
    user: UserEntity,
    rechargeDto: RechargeDto,
  ): Promise<Record<string,any>> {
    const { amount, paymentMethod, remark } = rechargeDto;

    // 这里应该调用支付服务处理充值
    // 暂时模拟充值成功
    const paymentResult = {
      success: true,
      transactionId: 'TXN_' + Date.now(),
      amount,
      paymentMethod,
    };

    if (!paymentResult.success) {
      throw new ApiException(30016);
    }

    // 使用事务确保数据一致性
    return this.dataSource.transaction(async manager => {
      // 更新用户余额
      const updatedUser = await manager.findOne(UserEntity, { 
        where: { id: user.id },
        lock: { mode: 'pessimistic_write' }
      });
      
      if (!updatedUser) {
        throw new ApiException(30010);
      }
      
      // 更新用户余额
      updatedUser.balance = (updatedUser.balance || 0) + amount;
      
      // 用户进行充值，禁用免费额度资格
      updatedUser.isFreeQuotaEligible = false;
      
      this.logger.log(`用户 #${user.id} 充值 ${amount}，禁用免费额度资格`);
      
      // 保存用户信息
      await manager.save(updatedUser);
      
      return {
        transactionId: paymentResult.transactionId,
        amount,
        paymentMethod,
        newBalance: updatedUser.balance,
        remark,
        message: '充值成功，您已不再享受免费额度',
      };
    });
  }

  /**
   * 根据ID查找单个用户（用于控制器）
   */
  async findOne(id: number): Promise<Record<string,any>> {
    const user = await this.userRepository.findOne({
      where: { id: id },
      relations: ['apiKeys'],
    });

    if (!user) {
      throw new ApiException(30010);
    }

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      phone: user.phone,
      realName: user.realName,
      userType: user.userType,
      userStatus: user.userStatus,
      balance: user.balance,
      emailVerified: user.emailVerified,
      phoneVerified: user.phoneVerified,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }

  /**
   * 获取用户列表（用于控制器）
   */
  async findList(
    listUserDto: any,
    currentUser: any,
  ): Promise<Record<string,any>> {
    return await this.list(listUserDto);
  }

  /**
   * 清除用户缓存
   */
  private async clearUserCache(userId: number): Promise<void> {
    try {
      // 清除用户基本信息缓存
      await this.redisService.del(`user:${userId}`);
      
      // 清除可能存在的其他用户相关缓存
      const userCacheKeys = await this.redisService.keys(`user:${userId}:*`);
      if (userCacheKeys.length > 0) {
        await this.redisService.delMany(userCacheKeys);
      }
      
      this.logger.log(`已清除用户 #${userId} 的缓存`);
    } catch (error) {
      this.logger.error(`清除用户 #${userId} 缓存失败: ${error.message}`);
    }
  }

  /**
   * 清除用户API密钥缓存
   */
  private async clearUserApiKeyCache(userId: number): Promise<void> {
    try {
      // 查询用户的所有API密钥
      const apiKeys = await this.dataSource
        .createQueryBuilder()
        .select('apiKey')
        .from('open_api_key', 'apiKey')
        .where('apiKey.userId = :userId', { userId })
        .getRawMany();
      
      if (apiKeys && apiKeys.length > 0) {
        // 清除每个API密钥的缓存
        for (const apiKey of apiKeys) {
          if (apiKey.apiKey) {
            await this.redisService.del(`api_key:${apiKey.apiKey}`);
          }
        }
        this.logger.log(`已清除用户 #${userId} 的 ${apiKeys.length} 个API密钥缓存`);
      }
    } catch (error) {
      this.logger.error(`清除用户 #${userId} API密钥缓存失败: ${error.message}`);
    }
  }

  /**
   * 使用户的所有JWT令牌失效（加入黑名单）
   */
  private async invalidateUserTokens(userId: number): Promise<void> {
    try {
      // 此处我们只能通过设置一个用户级别的黑名单标记
      // 由于JWT是无状态的，无法直接获取用户的所有令牌
      const blacklistKey = `user_tokens_blacklist:${userId}`;
      
      // 设置用户令牌黑名单标记，有效期7天（JWT令牌的最长有效期）
      await this.redisService.set(blacklistKey, Date.now().toString(), 7 * 24 * 60 * 60);
      
      this.logger.log(`已将用户 #${userId} 的所有令牌标记为失效`);
    } catch (error) {
      this.logger.error(`使用户 #${userId} 令牌失效失败: ${error.message}`);
    }
  }

  /**
   * 企业认证
   */
  async enterpriseVerify(
    enterpriseVerifyDto: EnterpriseVerifyDto,
  ): Promise<Record<string,any>> {
    const {
      companyName,
      creditCode,
      businessLicense,
      legalPerson,
      contactPhone,
    } = enterpriseVerifyDto;

    // 这里应该调用第三方企业认证服务进行验证
    // 暂时模拟认证成功
    const verificationResult = {
      success: true,
      message: '企业认证成功',
    };

    if (!verificationResult.success) {
      throw new ApiException(30017);
    }

    return {
      companyName,
      creditCode: creditCode.replace(/(\w{10})\w{6}(\w{2})/, '$1******$2'), // 脱敏处理
      legalPerson,
      contactPhone: contactPhone?.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') || contactPhone, // 脱敏处理
      status: 'verified',
      message: '企业认证成功',
    };
  }

  /**
   * 获取用户API使用情况（通过参数对象）
   */
  async getApiUsageByParams(params: {
    id: number;
    startDate?: string;
    endDate?: string;
  }): Promise<Record<string,any>> {
    const user = await this.findById(params.id);
    return await this.getApiUsage(user);
  }

  /**
   * 获取用户余额（通过ID）
   */
  async getBalanceById(id: number): Promise<Record<string,any>> {
    const user = await this.findById(id);
    return await this.getBalance(user);
  }

  /**
   * 用户充值（通过ID和金额）
   */
  async rechargeById(id: number, balance: number): Promise<Record<string,any>> {
    const user = await this.findById(id);
    const rechargeDto: RechargeDto = {
      amount: balance,
      paymentMethod: 'admin' as any, // 管理员充值
      remark: '管理员充值',
    };
    return await this.recharge(user, rechargeDto);
  }

  /**
   * 扣减用户余额
   */
  async deductBalance(
    userId: number,
    amount: number,
    queryRunner?: any,
  ): Promise<{ success: boolean; newBalance: number; oldBalance: number }> {
    const manager = queryRunner?.manager || this.dataSource.manager;
    
    const user = await manager.findOne(UserEntity, {
      where: { id: userId },
      lock: { mode: 'pessimistic_write' },
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    const currentBalance = (user as any).balance || 0;
    
    if (currentBalance < amount) {
      throw new BadRequestException('余额不足');
    }

    const oldBalance = currentBalance;
    (user as any).balance = currentBalance - amount;
    await manager.save(user);

    return {
      success: true,
      newBalance: currentBalance - amount,
      oldBalance,
    };
  }

  /**
   * 获取用户余额
   */
  async getUserBalance(userId: number): Promise<number> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    return (user as any).balance || 0;
  }

  /**
   * 彻底删除用户的API密钥
   */
  private async deleteUserApiKeys(userId: number): Promise<void> {
    try {
      // 先清除缓存中的API密钥
      await this.clearUserApiKeyCache(userId);
      
      // 直接从数据库删除API密钥记录
      const result = await this.dataSource
        .createQueryBuilder()
        .delete()
        .from('open_api_key')
        .where('userId = :userId', { userId })
        .execute();
      
      this.logger.log(`已从数据库删除用户 #${userId} 的 ${result.affected || 0} 个API密钥记录`);
    } catch (error) {
      this.logger.error(`删除用户 #${userId} 的API密钥记录失败: ${error.message}`);
    }
  }
}
