import { Test, TestingModule } from '@nestjs/testing';
import { DataSource } from 'typeorm';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { BalancePayService } from '../services/balance-pay.service';
import { UserService } from '../../user/user.service';
import { PaymentMethod } from '../../order/enums/order.enum';

describe('BalancePayService', () => {
  let service: BalancePayService;
  let userService: UserService;
  let dataSource: DataSource;

  const mockUser = {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    balance: 1000,
  };

  const mockPaymentRequest = {
    paymentNo: 'PAY20231201001',
    orderNo: 'ORD20231201001',
    amount: 100.00,
    subject: '服务购买',
    body: '订单号: ORD20231201001, 金额: 100.00元',
    notifyUrl: 'https://example.com/callback',
    returnUrl: 'https://example.com/return',
    userId: 1,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BalancePayService,
        {
          provide: UserService,
          useValue: {
            findById: jest.fn(),
          },
        },
        {
          provide: DataSource,
          useValue: {
            createQueryRunner: jest.fn(() => ({
              connect: jest.fn(),
              startTransaction: jest.fn(),
              commitTransaction: jest.fn(),
              rollbackTransaction: jest.fn(),
              release: jest.fn(),
              manager: {
                query: jest.fn(),
                queryRunner: {
                  data: { affectedRows: 1 },
                },
              },
            })),
          },
        },
      ],
    }).compile();

    service = module.get<BalancePayService>(BalancePayService);
    userService = module.get<UserService>(UserService);
    dataSource = module.get<DataSource>(DataSource);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getPaymentMethod', () => {
    it('应该返回余额支付方式', () => {
      // Act
      const result = service.getPaymentMethod();

      // Assert
      expect(result).toBe(PaymentMethod.BALANCE);
    });
  });

  describe('createPayment', () => {
    it('应该成功创建余额支付', async () => {
      // Arrange
      jest.spyOn(userService, 'findById').mockResolvedValue(mockUser as any);
      
      const mockQueryRunner = {
        connect: jest.fn(),
        startTransaction: jest.fn(),
        commitTransaction: jest.fn(),
        rollbackTransaction: jest.fn(),
        release: jest.fn(),
        manager: {
          query: jest.fn().mockResolvedValue(undefined),
          queryRunner: {
            data: { affectedRows: 1 },
          },
        },
      };
      
      jest.spyOn(dataSource, 'createQueryRunner').mockReturnValue(mockQueryRunner as any);

      // Act
      const result = await service.createPayment(mockPaymentRequest);

      // Assert
      expect(userService.findById).toHaveBeenCalledWith(1);
      expect(mockQueryRunner.manager.query).toHaveBeenCalledWith(
        'UPDATE users SET balance = balance - ? WHERE id = ? AND balance >= ?',
        [100.00, 1, 100.00]
      );
      expect(result.success).toBe(true);
      expect(result.thirdPartyNo).toContain('BALANCE_');
    });

    it('当用户不存在时应该返回失败', async () => {
      // Arrange
      jest.spyOn(userService, 'findById').mockRejectedValue(new NotFoundException('用户不存在'));

      // Act
      const result = await service.createPayment(mockPaymentRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('用户不存在');
    });

    it('当余额不足时应该返回失败', async () => {
      // Arrange
      const poorUser = { ...mockUser, balance: 50 };
      jest.spyOn(userService, 'findById').mockResolvedValue(poorUser as any);

      // Act
      const result = await service.createPayment(mockPaymentRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.errorMessage).toContain('余额不足');
    });

    it('当余额扣减失败时应该返回失败', async () => {
      // Arrange
      jest.spyOn(userService, 'findById').mockResolvedValue(mockUser as any);
      
      const mockQueryRunner = {
        connect: jest.fn(),
        startTransaction: jest.fn(),
        commitTransaction: jest.fn(),
        rollbackTransaction: jest.fn(),
        release: jest.fn(),
        manager: {
          query: jest.fn().mockResolvedValue(undefined),
          queryRunner: {
            data: { affectedRows: 0 },
          },
        },
      };
      
      jest.spyOn(dataSource, 'createQueryRunner').mockReturnValue(mockQueryRunner as any);

      // Act
      const result = await service.createPayment(mockPaymentRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('余额扣减失败，可能余额不足');
    });
  });

  describe('verifyCallback', () => {
    const callbackData = {
      paymentNo: 'PAY20231201001',
      thirdPartyNo: 'BALANCE_PAY20231201001_123456',
      amount: '100.00',
    };

    it('应该成功验证余额支付回调', async () => {
      // Act
      const result = await service.verifyCallback(callbackData);

      // Assert
      expect(result.success).toBe(true);
      expect(result.paymentNo).toBe(callbackData.paymentNo);
      expect(result.thirdPartyNo).toBe(callbackData.thirdPartyNo);
      expect(result.status).toBe('success');
      expect(result.amount).toBe(100.00);
    });
  });

  describe('queryPayment', () => {
    it('应该返回支付成功状态', async () => {
      // Act
      const result = await service.queryPayment('PAY20231201001', 'BALANCE_PAY20231201001_123456');

      // Assert
      expect(result.success).toBe(true);
      expect(result.status).toBe('success');
    });
  });

  describe('refund', () => {
    const refundRequest = {
      paymentNo: 'PAY20231201001',
      thirdPartyNo: 'BALANCE_PAY20231201001_123456',
      refundNo: 'REF20231201001',
      totalAmount: 100.00,
      refundAmount: 50.00,
      refundReason: '用户申请退款',
    };

    it('应该成功处理余额退款', async () => {
      // Arrange
      const mockQueryRunner = {
        connect: jest.fn(),
        startTransaction: jest.fn(),
        commitTransaction: jest.fn(),
        rollbackTransaction: jest.fn(),
        release: jest.fn(),
        manager: {
          query: jest.fn().mockResolvedValue(undefined),
        },
      };
      
      jest.spyOn(dataSource, 'createQueryRunner').mockReturnValue(mockQueryRunner as any);

      // Act
      const result = await service.refund(refundRequest);

      // Assert
      expect(mockQueryRunner.manager.query).toHaveBeenCalledWith(
        'UPDATE users SET balance = balance + ? WHERE id = ?',
        [50.00, 1]
      );
      expect(result.success).toBe(true);
      expect(result.refundNo).toBe(refundRequest.refundNo);
      expect(result.refundAmount).toBe(50.00);
    });

    it('当第三方支付单号格式不正确时应该返回失败', async () => {
      // Arrange
      const invalidRequest = { ...refundRequest, thirdPartyNo: 'INVALID_FORMAT' };

      // Act
      const result = await service.refund(invalidRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('无效的余额支付单号');
    });
  });

  describe('checkBalance', () => {
    it('当余额充足时应该返回true', async () => {
      // Arrange
      jest.spyOn(userService, 'findById').mockResolvedValue(mockUser as any);

      // Act
      const result = await service.checkBalance(1, 500);

      // Assert
      expect(result).toBe(true);
    });

    it('当余额不足时应该返回false', async () => {
      // Arrange
      jest.spyOn(userService, 'findById').mockResolvedValue(mockUser as any);

      // Act
      const result = await service.checkBalance(1, 1500);

      // Assert
      expect(result).toBe(false);
    });

    it('当用户不存在时应该返回false', async () => {
      // Arrange
      jest.spyOn(userService, 'findById').mockRejectedValue(new NotFoundException('用户不存在'));

      // Act
      const result = await service.checkBalance(1, 100);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('getUserBalance', () => {
    it('应该返回用户余额', async () => {
      // Arrange
      jest.spyOn(userService, 'findById').mockResolvedValue(mockUser as any);

      // Act
      const result = await service.getUserBalance(1);

      // Assert
      expect(result).toBe(1000);
    });

    it('当用户不存在时应该返回0', async () => {
      // Arrange
      jest.spyOn(userService, 'findById').mockRejectedValue(new NotFoundException('用户不存在'));

      // Act
      const result = await service.getUserBalance(1);

      // Assert
      expect(result).toBe(0);
    });
  });
});
