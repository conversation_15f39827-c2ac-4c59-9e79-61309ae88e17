# NestJS 14 + MySQL 5 项目结构和编码标准

## 目录

- [项目目录结构](#项目目录结构)
- [编码规范](#编码规范)
- [数据库设计与操作](#数据库设计与操作)
- [性能优化](#性能优化)
- [安全性最佳实践](#安全性最佳实践)
- [测试策略](#测试策略)
- [常见陷阱和解决方案](#常见陷阱和解决方案)

## 项目目录结构

### 基础项目结构

```
backend/
├── src/
│   ├── common/                # 通用代码
│   │   ├── base/              # 基础服务类
│   │   ├── constant/          # 常量定义
│   │   ├── decorators/        # 自定义装饰器
│   │   ├── dto/               # 数据传输对象
│   │   ├── entities/          # 基础实体
│   │   ├── events/            # 事件定义
│   │   ├── exceptions/        # 自定义异常
│   │   ├── filters/           # 异常过滤器
│   │   ├── guards/            # 守卫
│   │   ├── interceptors/      # 拦截器
│   │   ├── interfaces/        # 接口定义
│   │   ├── listeners/         # 事件监听器
│   │   ├── logging/           # 日志相关
│   │   ├── result/            # 统一响应格式
│   │   ├── types/             # 类型定义
│   │   └── utils/             # 工具函数
│   ├── config/                # 配置文件
│   │   ├── dev.yml            # 开发环境配置
│   │   ├── prod.yml           # 生产环境配置
│   │   └── test.yml           # 测试环境配置
│   ├── database/              # 数据库相关
│   │   ├── migrations/        # 数据库迁移
│   │   └── seeds/             # 数据种子
│   ├── modules/               # 业务模块
│   │   ├── auth/              # 认证模块
│   │   │   ├── dto/           # 模块DTO
│   │   │   ├── auth.controller.ts
│   │   │   ├── auth.module.ts
│   │   │   └── auth.service.ts
│   │   └── [其他业务模块]/
│   ├── shared/                # 共享服务
│   │   └── [共享服务文件]
│   ├── app.module.ts          # 应用主模块
│   ├── main.ts                # 应用入口
│   └── setupSwagger.ts        # Swagger配置
├── test/                      # 测试目录
│   ├── e2e/                   # 端到端测试
│   └── unit/                  # 单元测试
├── docs/                      # 文档
│   └── [文档文件]
├── .env                       # 环境变量
├── .env.example               # 环境变量示例
├── .eslintrc.js               # ESLint配置
├── .prettierrc                # Prettier配置
├── nest-cli.json              # NestJS CLI配置
├── tsconfig.json              # TypeScript配置
└── package.json               # 项目依赖
```

### 模块结构

每个业务模块应包含以下结构:

```
modules/模块名/
├── dto/                  # 数据传输对象
├── entities/             # 数据库实体
├── interfaces/           # 接口定义（可选）
├── services/             # 服务层拆分（大型模块）
├── 模块名.controller.ts   # 控制器
├── 模块名.module.ts       # 模块定义
├── 模块名.service.ts      # 服务
└── 模块名.spec.ts         # 单元测试
```

### 执行层结构

对于复杂业务，建议添加执行层:

```
modules/模块名/
├── executors/            # 执行器
│   ├── 具体执行器1.executor.ts
│   └── 具体执行器2.executor.ts
```

## 编码规范

### 命名规范

1. **文件命名**: 使用kebab-case (破折号命名法)
   ```
   // 正确
   user-profile.service.ts
   auth-strategies.enum.ts
   
   // 错误
   userProfile.service.ts
   AuthStrategies.enum.ts
   ```

2. **类命名**: 使用PascalCase (帕斯卡命名法)
   ```typescript
   // 正确
   export class UserService {}
   export class AuthenticationGuard {}
   
   // 错误
   export class userService {}
   export class authenticationGuard {}
   ```

3. **方法和属性**: 使用camelCase (驼峰命名法)
   ```typescript
   // 正确
   public async getUserById(id: number): Promise<User> {}
   private readonly userRepository: Repository<User>;
   
   // 错误
   public async GetUserById(Id: number): Promise<User> {}
   private readonly UserRepository: Repository<User>;
   ```

4. **接口**: 不使用"I"前缀
   ```typescript
   // 正确
   export interface UserData {}
   
   // 错误
   export interface IUserData {}
   ```

5. **常量**: 使用UPPER_SNAKE_CASE (大写蛇形命名法)
   ```typescript
   // 正确
   export const MAX_LOGIN_ATTEMPTS = 5;
   
   // 错误
   export const maxLoginAttempts = 5;
   ```

### 导入顺序

按以下顺序组织导入语句:

```typescript
// 1. 外部库
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

// 2. 相对路径的项目内部导入
import { UserDto } from './dto/user.dto';
import { User } from './entities/user.entity';

// 3. 类型导入
import type { UserProfile } from './interfaces/user-profile.interface';
```

### 注释规范

1. **类注释**: 描述类的用途和职责
   ```typescript
   /**
    * 用户服务 - 处理用户相关的业务逻辑
    * 
    * 主要功能:
    * - 用户注册
    * - 用户信息更新
    * - 用户查询
    */
   export class UserService {
     // ...
   }
   ```

2. **方法注释**: 描述方法的功能、参数和返回值
   ```typescript
   /**
    * 根据ID获取用户信息
    * 
    * @param id - 用户ID
    * @returns 用户实体，如果未找到则返回null
    * @throws NotFoundException - 当用户不存在时抛出
    */
   public async getUserById(id: number): Promise<User> {
     // ...
   }
   ```

### 异步代码规范

1. 使用`async/await`而非直接使用Promise链
   ```typescript
   // 正确
   async function getUserData(id: number): Promise<UserData> {
     const user = await this.userRepository.findOne({ where: { id } });
     return this.transformToUserData(user);
   }
   
   // 避免
   function getUserData(id: number): Promise<UserData> {
     return this.userRepository.findOne({ where: { id } })
       .then(user => this.transformToUserData(user));
   }
   ```

2. 使用`Promise.all`处理并行异步操作
   ```typescript
   async function getUserWithRelations(id: number) {
     const [user, posts, comments] = await Promise.all([
       this.userRepository.findOne({ where: { id } }),
       this.postRepository.find({ where: { userId: id } }),
       this.commentRepository.find({ where: { userId: id } })
     ]);
     
     return { user, posts, comments };
   }
   ```

## 数据库设计与操作

### 实体设计

1. **基础实体**: 使用继承实现通用字段
   ```typescript
   // common/entities/base.entity.ts
   export abstract class BaseEntity {
     @PrimaryGeneratedColumn()
     id: number;
   
    @CreateDateColumn({
        type: 'timestamp',
        comment: '创建时间',
    })
    createdAt: Date;
    @BeforeInsert()
    createDate() {
        // 更新entity前更新LastUpdatedDate
        this.createdAt = new Date();
    }
   
     @UpdateDateColumn()
     updatedAt: Date;
   
     @DeleteDateColumn()
        deletedAt: Date;
        @UpdateDateColumn({
        type: 'timestamp',
        comment: '更新时间',
    })
    updatedAt: Date;


    @Column({
        type: 'varchar',
        length: 50,
        nullable: true,
        comment: '创建者',
    })
    createdBy?: string;

    @Column({
        type: 'varchar',
        length: 50,
        nullable: true,
        comment: '更新者',
    })
    updatedBy?: string;

    @Column({
        type: 'tinyint',
        default: 1,
        comment: '状态：0-禁用，1-启用',
    })
    baseStatus: number;

    //0代表存在 1代表删除
    @Column({
        type: 'char',
        name: 'del_flag',
        default: '0',
        length: 1,
        comment: '删除标志',
    })
    public delFlag?: string;

    @Column({
        type: 'text',
        nullable: true,
        comment: '备注',
    })
    remark?: string;
   }
   ```

2. **实体关系**: 明确定义关系，注意循环引用
   ```typescript
   @Entity('users')
   export class User extends BaseEntity {
     // ...其他字段
   
     @OneToMany(() => Order, order => order.user)
     orders: Order[];
   }
   
   @Entity('orders')
   export class Order extends BaseEntity {
     // ...其他字段
   
     @ManyToOne(() => User, user => user.orders)
     user: User;
   
     @Column()
     userId: number;
   }
   ```

### 查询优化

1. **选择性加载**: 仅加载需要的字段
   ```typescript
   // 不好的实践 - 加载全部字段
   const user = await this.userRepository.findOne({ where: { id } });
   
   // 好的实践 - 仅加载需要的字段
   const user = await this.userRepository
     .createQueryBuilder('user')
     .select(['user.id', 'user.name', 'user.email'])
     .where('user.id = :id', { id })
     .getOne();
   ```

2. **分页查询**: 永远使用分页避免大数据量查询
   ```typescript
   async findUsers(page = 1, limit = 10): Promise<[User[], number]> {
     return this.userRepository.findAndCount({
       skip: (page - 1) * limit,
       take: limit,
       order: { createdAt: 'DESC' }
     });
   }
   ```

3. **索引优化**: 为常用查询字段添加索引
   ```typescript
   @Entity('users')
   export class User extends BaseEntity {
     @Column()
     @Index()  // 为email添加索引
     email: string;
     
     // ...其他字段
   }
   ```

## 性能优化

### 服务优化

1. **缓存策略**: 使用Redis缓存频繁访问的数据
   ```typescript
   @Injectable()
   export class UserService {
     constructor(
       @Inject(CACHE_MANAGER) private cacheManager: Cache,
       private userRepository: UserRepository,
     ) {}
   
     async getUserById(id: number): Promise<User> {
       // 尝试从缓存获取
       const cachedUser = await this.cacheManager.get(`user:${id}`);
       if (cachedUser) {
         return cachedUser;
       }
       
       // 从数据库获取
       const user = await this.userRepository.findOne({ where: { id } });
       
       // 存入缓存
       await this.cacheManager.set(`user:${id}`, user, { ttl: 3600 });
       
       return user;
     }
   }
   ```

2. **数据库连接池配置**:
   ```typescript
   // app.module.ts
   TypeOrmModule.forRoot({
     type: 'mysql',
     host: 'localhost',
     port: 3306,
     username: 'root',
     password: 'password',
     database: 'test',
     extra: {
       // 连接池配置
       connectionLimit: 10,
       queueLimit: 0,
       waitForConnections: true,
     }
   })
   ```

3. **资源释放**: 确保释放占用的资源
   ```typescript
   // 使用try-finally确保释放资源
   async function processLargeFile(file: Buffer) {
     const connection = await this.getConnection();
     try {
       // 处理文件
       return await this.processData(connection, file);
     } finally {
       // 确保释放连接
       await connection.release();
     }
   }
   ```

### API响应优化

1. **数据压缩**: 启用响应压缩
   ```typescript
   // main.ts
   import compression from 'compression';
   
   async function bootstrap() {
     const app = await NestFactory.create(AppModule);
     app.use(compression());
     // ...其他配置
     await app.listen(3000);
   }
   bootstrap();
   ```

2. **数据序列化**: 控制响应数据结构
   ```typescript
   // user.dto.ts
   export class UserResponseDto {
     @Expose()
     id: number;
   
     @Expose()
     name: string;
   
     @Expose()
     email: string;
   
     @Exclude()
     password: string;
     
     // 格式化日期
     @Expose()
     @Transform(({ value }) => value.toISOString())
     createdAt: Date;
   }
   ```

## 安全性最佳实践

### 身份验证与授权

1. **JWT配置**: 设置合理的过期时间和刷新策略
   ```typescript
   // auth.service.ts
   async generateTokens(user: User) {
     const [accessToken, refreshToken] = await Promise.all([
       this.jwtService.signAsync(
         { sub: user.id, email: user.email },
         { expiresIn: '15m' }  // 访问令牌短期有效
       ),
       this.jwtService.signAsync(
         { sub: user.id },
         { expiresIn: '7d' }   // 刷新令牌长期有效
       ),
     ]);
   
     return { accessToken, refreshToken };
   }
   ```

2. **权限控制**: 使用基于角色的访问控制
   ```typescript
   // roles.guard.ts
   @Injectable()
   export class RolesGuard implements CanActivate {
     constructor(private reflector: Reflector) {}
   
     canActivate(context: ExecutionContext): boolean {
       const requiredRoles = this.reflector.getAllAndOverride<Role[]>('roles', [
         context.getHandler(),
         context.getClass(),
       ]);
   
       if (!requiredRoles) {
         return true;
       }
   
       const { user } = context.switchToHttp().getRequest();
       return requiredRoles.some(role => user.roles?.includes(role));
     }
   }
   
   // 使用
   @Roles(Role.Admin)
   @UseGuards(JwtAuthGuard, RolesGuard)
   @Get('admin-data')
   getAdminData() {
     // ...
   }
   ```

### 数据安全

1. **数据加密**: 敏感数据加密存储
   ```typescript
   // 密码哈希
   @BeforeInsert()
   @BeforeUpdate()
   async hashPassword() {
     // 仅在密码被修改时重新哈希
     if (this.password) {
       this.password = await bcrypt.hash(this.password, 10);
     }
   }
   
   // API密钥加密
   @Column({ name: 'api_key' })
   set apiKey(value: string) {
     this._apiKey = value ? encrypt(value) : null;
   }
   
   get apiKey(): string {
     return this._apiKey ? decrypt(this._apiKey) : null;
   }
   
   @Column({ name: 'api_key_encrypted', select: false })
   private _apiKey: string;
   ```

2. **SQL注入防护**: 使用参数化查询
   ```typescript
   // 不安全 - 直接拼接SQL
   const query = `SELECT * FROM users WHERE email = '${email}'`; // 危险!
   
   // 安全 - 使用参数化查询
   const user = await this.userRepository
     .createQueryBuilder('user')
     .where('user.email = :email', { email })
     .getOne();
   ```

3. **请求验证**: 使用DTO和验证管道
   ```typescript
   // user-create.dto.ts
   export class CreateUserDto {
     @IsEmail()
     email: string;
   
     @IsString()
     @MinLength(8)
     @MaxLength(100)
     @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d]{8,}$/, {
       message: '密码必须包含大小写字母和数字',
     })
     password: string;
   
     @IsString()
     @MaxLength(100)
     name: string;
   }
   
   // controller
   @Post()
   async create(@Body(ValidationPipe) createUserDto: CreateUserDto) {
     return this.userService.create(createUserDto);
   }
   ```

### API安全

1. **速率限制**: 防止暴力攻击
   ```typescript
   // main.ts
   import rateLimit from 'express-rate-limit';
   
   async function bootstrap() {
     const app = await NestFactory.create(AppModule);
     
     app.use(
       rateLimit({
         windowMs: 15 * 60 * 1000, // 15分钟
         max: 100, // 每IP限制100个请求
         standardHeaders: true,
         legacyHeaders: false,
       }),
     );
     
     // 登录路由使用更严格的限制
     app.use('/auth/login', 
       rateLimit({
         windowMs: 15 * 60 * 1000,
         max: 5, // 更严格的限制
         standardHeaders: true,
         legacyHeaders: false,
       })
     );
     
     await app.listen(3000);
   }
   ```

2. **CORS配置**: 限制跨域请求
   ```typescript
   // main.ts
   async function bootstrap() {
     const app = await NestFactory.create(AppModule);
     
     app.enableCors({
       origin: ['https://yourdomain.com', /\.yourdomain\.com$/],
       methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
       credentials: true,
     });
     
     await app.listen(3000);
   }
   ```

3. **请求超时**: 防止慢速DDoS攻击
   ```typescript
   // main.ts
   import timeout from 'connect-timeout';
   
   async function bootstrap() {
     const app = await NestFactory.create(AppModule);
     app.use(timeout('15s')); // 15秒超时
     await app.listen(3000);
   }
   ```

## 测试策略

### 单元测试

1. **服务测试**: 测试业务逻辑
   ```typescript
   // user.service.spec.ts
   describe('UserService', () => {
     let service: UserService;
     let repository: MockType<Repository<User>>;
   
     beforeEach(async () => {
       const module = await Test.createTestingModule({
         providers: [
           UserService,
           {
             provide: getRepositoryToken(User),
             useFactory: repositoryMockFactory,
           },
         ],
       }).compile();
   
       service = module.get(UserService);
       repository = module.get(getRepositoryToken(User));
     });
   
     it('应该根据ID查找用户', async () => {
       const user = { id: 1, name: '测试用户' };
       repository.findOne.mockReturnValue(user);
       
       expect(await service.findOne(1)).toEqual(user);
       expect(repository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
     });
   });
   ```

2. **控制器测试**: 测试HTTP请求处理
   ```typescript
   // user.controller.spec.ts
   describe('UserController', () => {
     let controller: UserController;
     let service: UserService;
   
     beforeEach(async () => {
       const module = await Test.createTestingModule({
         controllers: [UserController],
         providers: [
           {
             provide: UserService,
             useValue: {
               findAll: jest.fn().mockResolvedValue([testUser]),
               findOne: jest.fn().mockImplementation((id) => 
                 Promise.resolve({ id, name: '测试用户' })),
             },
           },
         ],
       }).compile();
   
       controller = module.get(UserController);
       service = module.get(UserService);
     });
   
     describe('findAll', () => {
       it('应该返回用户数组', async () => {
         expect(await controller.findAll()).toEqual([testUser]);
         expect(service.findAll).toHaveBeenCalled();
       });
     });
   });
   ```

### 端到端测试

```typescript
// test/user.e2e-spec.ts
describe('UserController (e2e)', () => {
  let app: INestApplication;
  
  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();
    
    app = moduleFixture.createNestApplication();
    await app.init();
  });
  
  afterAll(async () => {
    await app.close();
  });
  
  it('/users (GET)', () => {
    return request(app.getHttpServer())
      .get('/users')
      .expect(200)
      .expect(res => {
        expect(Array.isArray(res.body)).toBeTruthy();
      });
  });
  
  it('/users (POST)', () => {
    return request(app.getHttpServer())
      .post('/users')
      .send({ name: '测试用户', email: '<EMAIL>', password: 'Password123' })
      .expect(201)
      .expect(res => {
        expect(res.body).toHaveProperty('id');
      });
  });
});
```

### 集成测试

```typescript
// test/integration/user-auth.spec.ts
describe('用户认证流程', () => {
  let app: INestApplication;
  let userService: UserService;
  let authService: AuthService;
  
  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();
    
    app = moduleFixture.createNestApplication();
    userService = moduleFixture.get(UserService);
    authService = moduleFixture.get(AuthService);
    
    await app.init();
  });
  
  it('应该完成注册-登录-获取资料流程', async () => {
    // 1. 注册新用户
    const userData = {
      email: `test-${Date.now()}@example.com`,
      password: 'Password123',
      name: '测试用户',
    };
    
    const newUser = await userService.create(userData);
    expect(newUser.id).toBeDefined();
    
    // 2. 用户登录
    const auth = await authService.validateUser(userData.email, userData.password);
    expect(auth).toBeTruthy();
    
    const { accessToken } = await authService.login(auth);
    expect(accessToken).toBeDefined();
    
    // 3. 使用令牌获取用户资料
    await request(app.getHttpServer())
      .get('/users/profile')
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200)
      .expect(res => {
        expect(res.body.email).toEqual(userData.email);
      });
  });
});
```

## 常见陷阱和解决方案

### 循环依赖问题

**问题**: 模块A依赖模块B，模块B又依赖模块A

**解决方案**:

1. **使用forwardRef**:
   ```typescript
   // auth.module.ts
   @Module({
     imports: [
       forwardRef(() => UserModule),
     ],
     providers: [AuthService],
     exports: [AuthService],
   })
   export class AuthModule {}
   
   // user.module.ts
   @Module({
     imports: [
       forwardRef(() => AuthModule),
     ],
     providers: [UserService],
     exports: [UserService],
   })
   export class UserModule {}
   ```

2. **使用事件系统解耦**:
   ```typescript
   // auth.events.ts
   export class UserCreatedEvent {
     constructor(public readonly user: User) {}
   }
   
   // user.service.ts
   @Injectable()
   export class UserService {
     constructor(private eventEmitter: EventEmitter2) {}
   
     async create(createUserDto: CreateUserDto) {
       const user = await this.userRepository.save(createUserDto);
       this.eventEmitter.emit('user.created', new UserCreatedEvent(user));
       return user;
     }
   }
   
   // auth.service.ts
   @Injectable()
   export class AuthService {
     constructor(private eventEmitter: EventEmitter2) {}
   
     @OnEvent('user.created')
     handleUserCreated(event: UserCreatedEvent) {
       // 处理用户创建事件
     }
   }
   ```

3. **重构设计，引入执行层**:
   
   创建共享的执行器服务，将共同逻辑抽离出来，避免循环依赖。

### 内存泄漏

**问题**: 长时间运行的应用内存占用逐渐增加

**解决方案**:

1. **观察者清理**:
   ```typescript
   // 在服务中
   private subscription = new Subscription();
   
   constructor() {
     this.subscription.add(
       someObservable.subscribe(data => {
         // 处理数据
       })
     );
   }
   
   @OnModuleDestroy()
   onDestroy() {
     this.subscription.unsubscribe();
   }
   ```

2. **定时清理缓存**:
   ```typescript
   @Injectable()
   export class CacheCleanupService {
     constructor(
       @Inject(CACHE_MANAGER) private cacheManager: Cache,
       private schedulerRegistry: SchedulerRegistry
     ) {
       this.setupCleanupJob();
     }
   
     private setupCleanupJob() {
       const job = new CronJob('0 0 * * *', () => {
         this.cleanupCache();
       });
       
       this.schedulerRegistry.addCronJob('cache-cleanup', job);
       job.start();
     }
   
     private async cleanupCache() {
       // 根据业务需求清理特定缓存
       try {
         await this.cacheManager.del('some-cache-key');
         // 或者重置整个缓存
         // await this.cacheManager.reset();
       } catch (e) {
         console.error('缓存清理失败', e);
       }
     }
   }
   ```

### 数据库连接问题

**问题**: 连接池耗尽或数据库连接不稳定

**解决方案**:

1. **添加重试逻辑**:
   ```typescript
   async function executeWithRetry<T>(
     operation: () => Promise<T>,
     retries = 3,
     delay = 1000
   ): Promise<T> {
     try {
       return await operation();
     } catch (error) {
       if (retries <= 0 || !isRetryableError(error)) {
         throw error;
       }
       
       await new Promise(resolve => setTimeout(resolve, delay));
       return executeWithRetry(operation, retries - 1, delay * 2);
     }
   }
   
   // 使用
   async function findUser(id: number) {
     return executeWithRetry(() => this.userRepository.findOne({ where: { id } }));
   }
   ```

2. **使用断路器模式**:
   ```typescript
   // 使用@nestjs/circuit-breaker或自定义实现
   @CircuitBreaker({ 
     failureThreshold: 3,
     resetTimeout: 30000,
     fallback: (id: number) => ({ id, name: 'Default User' })
   })
   async findUser(id: number) {
     return this.userRepository.findOne({ where: { id } });
   }
   ```

### 处理大量异步操作

**问题**: 处理大量并发请求时可能导致服务崩溃

**解决方案**:

1. **批量处理**:
   ```typescript
   async function processManyItems(items: any[]) {
     const batchSize = 100;
     const results = [];
     
     for (let i = 0; i < items.length; i += batchSize) {
       const batch = items.slice(i, i + batchSize);
       const batchResults = await Promise.all(
         batch.map(item => processItem(item))
       );
       results.push(...batchResults);
     }
     
     return results;
   }
   ```

2. **限制并发**:
   ```typescript
   import pLimit from 'p-limit';
   
   async function processWithConcurrencyLimit(items: any[]) {
     const limit = pLimit(5); // 最多5个并发操作
     const promises = items.map(item => 
       limit(() => processItem(item))
     );
     
     return Promise.all(promises);
   }
   ```

### API接口版本控制

**问题**: 需要维护多个版本的API

**解决方案**:

1. **URL版本控制**:
   ```typescript
   // main.ts
   const app = await NestFactory.create(AppModule);
   app.enableVersioning({
     type: VersioningType.URI,
     prefix: 'v',
   });
   
   // user.controller.ts
   @Controller({ path: 'users', version: '1' })
   export class UserControllerV1 {
     // v1版本的接口
   }
   
   @Controller({ path: 'users', version: '2' })
   export class UserControllerV2 {
     // v2版本的接口
   }
   ```

2. **媒体类型版本控制**:
   ```typescript
   // main.ts
   app.enableVersioning({
     type: VersioningType.MEDIA_TYPE,
     key: 'v',
   });
   
   // 客户端请求时使用: Accept: application/json;v=1
   ```

通过以上结构和规范，可以构建出高质量、可扩展且易于维护的NestJS应用。根据项目需求，可能需要对某些部分进行适当调整，但整体架构应保持一致，以确保项目的长期可维护性和可扩展性。 