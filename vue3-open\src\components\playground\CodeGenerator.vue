<template>
  <div class="code-generator">
    <div class="generator-header">
      <h3>代码生成</h3>
      <div class="header-actions">
        <el-button size="small" @click="copyCode" :disabled="!generatedCode">
          <el-icon><CopyDocument /></el-icon>
          复制
        </el-button>
        <el-button size="small" @click="downloadCode" :disabled="!generatedCode">
          <el-icon><Download /></el-icon>
          下载
        </el-button>
      </div>
    </div>

    <!-- 语言选择 -->
    <div class="language-selector">
      <el-select v-model="selectedLanguage" @change="generateCode" placeholder="选择编程语言">
        <el-option
          v-for="lang in supportedLanguages"
          :key="lang.value"
          :label="lang.label"
          :value="lang.value"
        >
          <div class="language-option">
            <span class="language-name">{{ lang.label }}</span>
            <span class="language-desc">{{ lang.description }}</span>
          </div>
        </el-option>
      </el-select>
    </div>

    <!-- 代码显示 -->
    <div class="code-display" v-if="generatedCode">
      <div class="code-header">
        <div class="code-info">
          <span class="language-tag">{{ getCurrentLanguage()?.label }}</span>
          <span class="code-size">{{ getCodeSize() }}</span>
        </div>
        <div class="code-actions">
          <el-button-group size="small">
            <el-button 
              :type="showComments ? 'primary' : ''"
              @click="toggleComments"
            >
              注释
            </el-button>
            <el-button 
              :type="formatCode ? 'primary' : ''"
              @click="toggleFormat"
            >
              格式化
            </el-button>
          </el-button-group>
        </div>
      </div>
      
      <div class="code-content">
        <pre><code :class="`language-${selectedLanguage}`">{{ displayCode }}</code></pre>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-empty description="选择语言生成代码">
        <template #image>
          <el-icon size="60" color="#c0c4cc">
            <Document />
          </el-icon>
        </template>
        <template #description>
          <p>选择编程语言后，将自动生成对应的请求代码</p>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  CopyDocument,
  Download,
  Document
} from '@element-plus/icons-vue'

interface Props {
  method: string
  url: string
  headers: Record<string, string>
  body?: string
  apiKey?: string
}

const props = withDefaults(defineProps<Props>(), {
  method: 'GET',
  url: '',
  headers: () => ({}),
  body: '',
  apiKey: ''
})

const selectedLanguage = ref('')
const generatedCode = ref('')
const showComments = ref(true)
const formatCode = ref(true)

// 支持的编程语言
const supportedLanguages = [
  {
    value: 'javascript',
    label: 'JavaScript',
    description: 'Fetch API'
  },
  {
    value: 'typescript',
    label: 'TypeScript',
    description: 'Fetch API with types'
  },
  {
    value: 'python',
    label: 'Python',
    description: 'requests library'
  },
  {
    value: 'java',
    label: 'Java',
    description: 'OkHttp library'
  },
  {
    value: 'csharp',
    label: 'C#',
    description: 'HttpClient'
  },
  {
    value: 'php',
    label: 'PHP',
    description: 'cURL'
  },
  {
    value: 'go',
    label: 'Go',
    description: 'net/http package'
  },
  {
    value: 'curl',
    label: 'cURL',
    description: 'Command line'
  }
]

// 计算属性
const displayCode = computed(() => {
  if (!generatedCode.value) return ''
  
  let code = generatedCode.value
  
  if (!showComments.value) {
    // 移除注释
    code = code.replace(/\/\*[\s\S]*?\*\//g, '')
                .replace(/\/\/.*$/gm, '')
                .replace(/#.*$/gm, '')
                .replace(/^\s*\n/gm, '')
  }
  
  return code
})

// 监听props变化，自动重新生成代码
watch(
  () => [props.method, props.url, props.headers, props.body, props.apiKey],
  () => {
    if (selectedLanguage.value) {
      generateCode()
    }
  },
  { deep: true }
)

// 方法
const generateCode = () => {
  if (!selectedLanguage.value || !props.url) {
    generatedCode.value = ''
    return
  }

  switch (selectedLanguage.value) {
    case 'javascript':
      generatedCode.value = generateJavaScript()
      break
    case 'typescript':
      generatedCode.value = generateTypeScript()
      break
    case 'python':
      generatedCode.value = generatePython()
      break
    case 'java':
      generatedCode.value = generateJava()
      break
    case 'csharp':
      generatedCode.value = generateCSharp()
      break
    case 'php':
      generatedCode.value = generatePHP()
      break
    case 'go':
      generatedCode.value = generateGo()
      break
    case 'curl':
      generatedCode.value = generateCurl()
      break
    default:
      generatedCode.value = ''
  }
}

const generateJavaScript = () => {
  const headers = { ...props.headers }
  if (props.apiKey) {
    headers['Authorization'] = `Bearer ${props.apiKey}`
  }

  let code = `// JavaScript Fetch API 请求示例\n`
  code += `const url = '${props.url}';\n\n`
  
  code += `const options = {\n`
  code += `  method: '${props.method}',\n`
  
  if (Object.keys(headers).length > 0) {
    code += `  headers: {\n`
    Object.entries(headers).forEach(([key, value]) => {
      code += `    '${key}': '${value}',\n`
    })
    code += `  },\n`
  }
  
  if (props.body && props.method !== 'GET') {
    code += `  body: ${JSON.stringify(props.body, null, 2)}\n`
  }
  
  code += `};\n\n`
  
  code += `fetch(url, options)\n`
  code += `  .then(response => {\n`
  code += `    if (!response.ok) {\n`
  code += `      throw new Error('Network response was not ok');\n`
  code += `    }\n`
  code += `    return response.json();\n`
  code += `  })\n`
  code += `  .then(data => {\n`
  code += `    console.log('Success:', data);\n`
  code += `  })\n`
  code += `  .catch(error => {\n`
  code += `    console.error('Error:', error);\n`
  code += `  });`

  return code
}

const generateTypeScript = () => {
  const headers = { ...props.headers }
  if (props.apiKey) {
    headers['Authorization'] = `Bearer ${props.apiKey}`
  }

  let code = `// TypeScript Fetch API 请求示例\n`
  code += `interface ApiResponse {\n`
  code += `  // 根据实际API响应定义类型\n`
  code += `  [key: string]: any;\n`
  code += `}\n\n`
  
  code += `const url: string = '${props.url}';\n\n`
  
  code += `const options: RequestInit = {\n`
  code += `  method: '${props.method}',\n`
  
  if (Object.keys(headers).length > 0) {
    code += `  headers: {\n`
    Object.entries(headers).forEach(([key, value]) => {
      code += `    '${key}': '${value}',\n`
    })
    code += `  },\n`
  }
  
  if (props.body && props.method !== 'GET') {
    code += `  body: ${JSON.stringify(props.body, null, 2)}\n`
  }
  
  code += `};\n\n`
  
  code += `async function makeRequest(): Promise<ApiResponse> {\n`
  code += `  try {\n`
  code += `    const response = await fetch(url, options);\n`
  code += `    \n`
  code += `    if (!response.ok) {\n`
  code += `      throw new Error(\`HTTP error! status: \${response.status}\`);\n`
  code += `    }\n`
  code += `    \n`
  code += `    const data: ApiResponse = await response.json();\n`
  code += `    console.log('Success:', data);\n`
  code += `    return data;\n`
  code += `  } catch (error) {\n`
  code += `    console.error('Error:', error);\n`
  code += `    throw error;\n`
  code += `  }\n`
  code += `}\n\n`
  code += `// 调用函数\n`
  code += `makeRequest();`

  return code
}

const generatePython = () => {
  const headers = { ...props.headers }
  if (props.apiKey) {
    headers['Authorization'] = `Bearer ${props.apiKey}`
  }

  let code = `# Python requests 库请求示例\n`
  code += `import requests\n`
  code += `import json\n\n`
  
  code += `url = '${props.url}'\n\n`
  
  if (Object.keys(headers).length > 0) {
    code += `headers = {\n`
    Object.entries(headers).forEach(([key, value]) => {
      code += `    '${key}': '${value}',\n`
    })
    code += `}\n\n`
  }
  
  if (props.body && props.method !== 'GET') {
    code += `data = ${JSON.stringify(JSON.parse(props.body || '{}'), null, 4).replace(/"/g, "'")}
\n`
  }
  
  code += `try:\n`
  code += `    response = requests.${props.method.toLowerCase()}(\n`
  code += `        url,\n`
  
  if (Object.keys(headers).length > 0) {
    code += `        headers=headers,\n`
  }
  
  if (props.body && props.method !== 'GET') {
    code += `        json=data,\n`
  }
  
  code += `        timeout=30\n`
  code += `    )\n\n`
  
  code += `    # 检查响应状态\n`
  code += `    response.raise_for_status()\n\n`
  
  code += `    # 解析JSON响应\n`
  code += `    result = response.json()\n`
  code += `    print('Success:', result)\n\n`
  
  code += `except requests.exceptions.RequestException as e:\n`
  code += `    print('Error:', e)\n`
  code += `except json.JSONDecodeError as e:\n`
  code += `    print('JSON decode error:', e)`

  return code
}

const generateCurl = () => {
  const headers = { ...props.headers }
  if (props.apiKey) {
    headers['Authorization'] = `Bearer ${props.apiKey}`
  }

  let code = `# cURL 命令行请求示例\n`
  code += `curl -X ${props.method} \\\n`
  code += `  '${props.url}' \\\n`
  
  Object.entries(headers).forEach(([key, value]) => {
    code += `  -H '${key}: ${value}' \\\n`
  })
  
  if (props.body && props.method !== 'GET') {
    code += `  -d '${props.body}' \\\n`
  }
  
  code += `  --compressed`

  return code
}

const generateJava = () => {
  // Java代码生成逻辑
  return `// Java OkHttp 请求示例\n// 需要添加 OkHttp 依赖\n\n// 代码生成中...`
}

const generateCSharp = () => {
  // C#代码生成逻辑
  return `// C# HttpClient 请求示例\n\n// 代码生成中...`
}

const generatePHP = () => {
  // PHP代码生成逻辑
  return `<?php\n// PHP cURL 请求示例\n\n// 代码生成中...`
}

const generateGo = () => {
  // Go代码生成逻辑
  return `// Go net/http 请求示例\n\n// 代码生成中...`
}

const getCurrentLanguage = () => {
  return supportedLanguages.find(lang => lang.value === selectedLanguage.value)
}

const getCodeSize = () => {
  const bytes = new Blob([generatedCode.value]).size
  if (bytes < 1024) return `${bytes} B`
  return `${(bytes / 1024).toFixed(1)} KB`
}

const toggleComments = () => {
  showComments.value = !showComments.value
}

const toggleFormat = () => {
  formatCode.value = !formatCode.value
}

const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(displayCode.value)
    ElMessage.success('代码已复制到剪贴板')
  } catch {
    ElMessage.error('复制失败')
  }
}

const downloadCode = () => {
  try {
    const extensions: Record<string, string> = {
      javascript: 'js',
      typescript: 'ts',
      python: 'py',
      java: 'java',
      csharp: 'cs',
      php: 'php',
      go: 'go',
      curl: 'sh'
    }
    
    const ext = extensions[selectedLanguage.value] || 'txt'
    const filename = `api_request.${ext}`
    
    const blob = new Blob([displayCode.value], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    
    URL.revokeObjectURL(url)
    ElMessage.success('代码文件下载成功')
  } catch {
    ElMessage.error('下载失败')
  }
}
</script>

<style scoped>
.code-generator {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.generator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f2f5;
  background: #fafbfc;
  flex-shrink: 0;
}

.generator-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.language-selector {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f2f5;
  background: #fafbfc;
  flex-shrink: 0;
}

.language-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.language-name {
  font-weight: 500;
  color: #303133;
}

.language-desc {
  font-size: 12px;
  color: #909399;
}

.code-display {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.code-info {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #6c757d;
}

.language-tag {
  background: #e6f7ff;
  color: #1890ff;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.code-content {
  flex: 1;
  overflow: auto;
  background: #fff;
}

.code-content pre {
  margin: 0;
  padding: 20px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
}

.code-content code {
  background: none;
  padding: 0;
  border-radius: 0;
  font-size: inherit;
  color: inherit;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 24px;
}

/* 语法高亮样式 */
.language-javascript,
.language-typescript {
  color: #333;
}

.language-python {
  color: #3776ab;
}

.language-java {
  color: #ed8b00;
}

.language-csharp {
  color: #239120;
}

.language-php {
  color: #777bb4;
}

.language-go {
  color: #00add8;
}

.language-curl {
  color: #2c3e50;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .generator-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
  }
  
  .language-selector {
    padding: 12px 16px;
  }
  
  .code-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .code-info {
    flex-direction: column;
    gap: 4px;
  }
  
  .code-content pre {
    padding: 16px;
    font-size: 12px;
  }
}

/* 滚动条样式 */
.code-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.code-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.code-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.code-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>