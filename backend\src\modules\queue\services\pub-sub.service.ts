import { Injectable, OnModuleDestroy } from '@nestjs/common';
import Redis from 'ioredis';
import { Subject, Subscription } from 'rxjs';
import { RedisService } from '../../../shared/redis.service';

/**
 * Redis发布订阅服务
 * 负责处理基于Redis的消息发布和订阅
 */
@Injectable()
export class RedisPubSubService implements OnModuleDestroy {
  private readonly subscribers: Map<string, Subject<any>> = new Map();
  private readonly subscribeClient: Redis;

  constructor(private readonly redisService: RedisService) {
    // 创建独立的Redis连接用于订阅，避免阻塞主Redis连接
    this.subscribeClient = this.redisService.getClient().duplicate();
    
    // 设置消息处理函数
    this.subscribeClient.on('message', (channel, message) => {
      const subject = this.subscribers.get(channel);
      if (subject) {
        try {
          const parsedMessage = JSON.parse(message);
          subject.next(parsedMessage);
        } catch (error) {
          subject.next(message);
        }
      }
    });
  }

  /**
   * 发布消息到指定的频道
   */
  async publish(channel: string, data: any): Promise<number> {
    const message = typeof data === 'string' ? data : JSON.stringify(data);
    // 使用RedisService提供的原生客户端进行发布
    return await this.redisService.getClient().publish(channel, message);
  }

  /**
   * 订阅指定的频道
   */
  subscribe(channel: string, callback: (data: any) => void): Subscription {
    // 如果该频道还没有Subject，创建一个并订阅Redis频道
    if (!this.subscribers.has(channel)) {
      const subject = new Subject<any>();
      this.subscribers.set(channel, subject);
      this.subscribeClient.subscribe(channel);
    }

    // 返回可取消的订阅
    const subject = this.subscribers.get(channel);
    if (!subject) {
      throw new Error(`频道${channel}的Subject未创建`);
    }
    
    const subscription = subject.subscribe({
      next: callback,
    });

    // 增加自定义的unsubscribe逻辑
    const originalUnsubscribe = subscription.unsubscribe.bind(subscription);
    subscription.unsubscribe = () => {
      originalUnsubscribe();
      
      // 确保subject存在
      const currentSubject = this.subscribers.get(channel);
      if (currentSubject) {
        // 检查是否还有观察者
        const hasObservers = currentSubject.observed;
        if (!hasObservers) {
          this.subscribeClient.unsubscribe(channel);
          this.subscribers.delete(channel);
        }
      }
    };

    return subscription;
  }

  /**
   * 模块销毁时清理资源
   */
  onModuleDestroy() {
    // 清理所有订阅
    this.subscribers.forEach(subject => {
      subject.complete();
    });
    this.subscribers.clear();
    
    // 关闭订阅客户端
    this.subscribeClient.quit();
  }
} 