<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 主要蓝紫色渐变 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    
    <!-- 浅色渐变 -->
    <linearGradient id="lightGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a78bfa;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#818cf8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#60a5fa;stop-opacity:1" />
    </linearGradient>
    
    <!-- 深色渐变 -->
    <linearGradient id="darkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#4f46e5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
    </linearGradient>
    
    <!-- 发光效果 -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 网络节点图标 -->
  <g transform="translate(30, 30)">
    <!-- 主要节点 -->
    <!-- 中心节点 -->
    <circle cx="0" cy="0" r="4" 
            fill="url(#primaryGradient)" 
            stroke="url(#lightGradient)" 
            stroke-width="1" 
            filter="url(#glow)"/>
    
    <!-- 上方节点 -->
    <circle cx="0" cy="-18" r="3" 
            fill="url(#lightGradient)" 
            stroke="url(#primaryGradient)" 
            stroke-width="0.8"/>
    
    <!-- 右上节点 -->
    <circle cx="15" cy="-9" r="3.5" 
            fill="url(#primaryGradient)" 
            stroke="url(#darkGradient)" 
            stroke-width="0.8"/>
    
    <!-- 右下节点 -->
    <circle cx="15" cy="9" r="2.5" 
            fill="url(#darkGradient)" 
            stroke="url(#primaryGradient)" 
            stroke-width="0.8"/>
    
    <!-- 下方节点 -->
    <circle cx="0" cy="18" r="3" 
            fill="url(#lightGradient)" 
            stroke="url(#primaryGradient)" 
            stroke-width="0.8"/>
    
    <!-- 左下节点 -->
    <circle cx="-15" cy="9" r="3.5" 
            fill="url(#primaryGradient)" 
            stroke="url(#darkGradient)" 
            stroke-width="0.8"/>
    
    <!-- 左上节点 -->
    <circle cx="-15" cy="-9" r="2.5" 
            fill="url(#darkGradient)" 
            stroke="url(#primaryGradient)" 
            stroke-width="0.8"/>
    
    <!-- 连接线 -->
    <!-- 从中心到各个节点的连接线 -->
    <line x1="0" y1="0" x2="0" y2="-18" 
          stroke="url(#primaryGradient)" 
          stroke-width="2" 
          opacity="0.8"/>
    <line x1="0" y1="0" x2="15" y2="-9" 
          stroke="url(#primaryGradient)" 
          stroke-width="2" 
          opacity="0.8"/>
    <line x1="0" y1="0" x2="15" y2="9" 
          stroke="url(#primaryGradient)" 
          stroke-width="2" 
          opacity="0.8"/>
    <line x1="0" y1="0" x2="0" y2="18" 
          stroke="url(#primaryGradient)" 
          stroke-width="2" 
          opacity="0.8"/>
    <line x1="0" y1="0" x2="-15" y2="9" 
          stroke="url(#primaryGradient)" 
          stroke-width="2" 
          opacity="0.8"/>
    <line x1="0" y1="0" x2="-15" y2="-9" 
          stroke="url(#primaryGradient)" 
          stroke-width="2" 
          opacity="0.8"/>
    
    <!-- 外围节点间的连接线 -->
    <line x1="0" y1="-18" x2="15" y2="-9" 
          stroke="url(#lightGradient)" 
          stroke-width="1.5" 
          opacity="0.6"/>
    <line x1="15" y1="-9" x2="15" y2="9" 
          stroke="url(#lightGradient)" 
          stroke-width="1.5" 
          opacity="0.6"/>
    <line x1="15" y1="9" x2="0" y2="18" 
          stroke="url(#lightGradient)" 
          stroke-width="1.5" 
          opacity="0.6"/>
    <line x1="0" y1="18" x2="-15" y2="9" 
          stroke="url(#lightGradient)" 
          stroke-width="1.5" 
          opacity="0.6"/>
    <line x1="-15" y1="9" x2="-15" y2="-9" 
          stroke="url(#lightGradient)" 
          stroke-width="1.5" 
          opacity="0.6"/>
    <line x1="-15" y1="-9" x2="0" y2="-18" 
          stroke="url(#lightGradient)" 
          stroke-width="1.5" 
          opacity="0.6"/>
    
    <!-- 装饰性小节点 -->
    <circle cx="7" cy="-13" r="1" 
            fill="url(#lightGradient)" 
            opacity="0.8"/>
    <circle cx="-7" cy="13" r="1" 
            fill="url(#lightGradient)" 
            opacity="0.8"/>
    <circle cx="-7" cy="-2" r="1" 
            fill="url(#lightGradient)" 
            opacity="0.8"/>
    
    <!-- 中心高光效果 -->
    <circle cx="0" cy="0" r="1.5" 
            fill="url(#lightGradient)" 
            opacity="0.9"/>
  </g>
</svg>