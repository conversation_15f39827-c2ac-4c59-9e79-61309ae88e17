import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { OrderService } from './order.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { QueryOrderDto } from './dto/query-order.dto';
import {
  OrderResponseDto,
  OrderListResponseDto,
} from './dto/order-response.dto';

@ApiTags('订单管理')
@Controller('order')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Post()
  @ApiOperation({ summary: '创建订单' })
  async create(@Body() createOrderDto: CreateOrderDto): Promise<OrderResponseDto> {
    return await this.orderService.create(createOrderDto);
  }

  @Get()
  @ApiOperation({ summary: '获取订单列表' })
  async findAll(@Query() queryDto: QueryOrderDto): Promise<OrderListResponseDto> {
    return await this.orderService.findAll(queryDto);
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取订单详情' })
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<OrderResponseDto> {
    return await this.orderService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新订单' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateOrderDto: UpdateOrderDto,
  ): Promise<OrderResponseDto> {
    return await this.orderService.update(id, updateOrderDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除订单' })
  async remove(@Param('id', ParseIntPipe) id: number): Promise<{ message: string }> {
    return await this.orderService.remove(id);
  }
}
