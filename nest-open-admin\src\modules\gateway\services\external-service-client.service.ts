import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom, timeout, catchError } from 'rxjs';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { ServiceDefinition } from '../config/service-registry.config';
import { NotFoundException } from '@nestjs/common';

// 地图API配置接口
interface MapApiConfig {
  provider: string;
  url: string;
  key: string;
  parseResponse: (data: any, coordinates: any) => any;
}

/**
 * 外部服务客户端
 * 统一处理对外部服务的HTTP调用
 */
@Injectable()
export class ExternalServiceClient {
  private readonly logger = new Logger(ExternalServiceClient.name);
  private readonly mapApiConfigs: MapApiConfig[];
  
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    // 初始化地图API配置
    this.mapApiConfigs = [
      // 高德地图配置
      {
        provider: '高德地图',
        url: 'https://restapi.amap.com/v3/geocode/regeo',
        key: this.configService.get<string>('AMAP_KEY') || '941c8903ad31b2acf1b20118d2a54bb1', // 测试密钥
        parseResponse: (data: any, coordinates: any): any => {
          if (data.status === '1' && data.regeocode) {
            const regeocode = data.regeocode;
            const addressComponent = regeocode.addressComponent || {};
            
            return {
              success: true,
              coordinates: {
                latitude: coordinates.latitude,
                longitude: coordinates.longitude
              },
              address: {
                full_address: regeocode.formatted_address || '',
                province: addressComponent.province || '',
                city: addressComponent.city || '',
                district: addressComponent.district || '',
                street: addressComponent.township || addressComponent.street || '',
                house_number: addressComponent.streetNumber || '',
                postcode: addressComponent.postcode || '',
                confidence: 0.9,
                resolution_method: '高德地图API'
              }
            };
          }
          
          throw new Error('高德地图API返回无效数据');
        }
      },
      // 腾讯地图配置
      {
        provider: '腾讯地图',
        url: 'https://apis.map.qq.com/ws/geocoder/v1/',
        key: this.configService.get<string>('TENCENT_MAP_KEY') || 'GT7BZ-QV2RD-KH74P-HKCNM-5AF76-3VBHL', // 测试密钥
        parseResponse: (data: any, coordinates: any): any => {
          if (data.status === 0 && data.result) {
            const result = data.result;
            const addressComponent = result.address_component || {};
            
            return {
              success: true,
              coordinates: {
                latitude: coordinates.latitude,
                longitude: coordinates.longitude
              },
              address: {
                full_address: result.address || '',
                province: addressComponent.province || '',
                city: addressComponent.city || '',
                district: addressComponent.district || '',
                street: addressComponent.street || '',
                house_number: '',
                postcode: '',
                confidence: 0.9,
                resolution_method: '腾讯地图API'
              }
            };
          }
          
          throw new Error('腾讯地图API返回无效数据');
        }
      },
      // 百度地图配置
      {
        provider: '百度地图',
        url: 'https://api.map.baidu.com/reverse_geocoding/v3/',
        key: this.configService.get<string>('BAIDU_MAP_KEY') || 'V4GVHuRD0T0TImlunB7UoehFiFY4Rbme', // 测试密钥
        parseResponse: (data: any, coordinates: any): any => {
          if (data.status === 0 && data.result) {
            const result = data.result;
            const addressComponent = result.addressComponent || {};
            
            return {
              success: true,
              coordinates: {
                latitude: coordinates.latitude,
                longitude: coordinates.longitude
              },
              address: {
                full_address: result.formatted_address || '',
                province: addressComponent.province || '',
                city: addressComponent.city || '',
                district: addressComponent.district || '',
                street: addressComponent.street || '',
                house_number: addressComponent.street_number || '',
                postcode: '',
                confidence: 0.9,
                resolution_method: '百度地图API'
              }
            };
          }
          
          throw new Error('百度地图API返回无效数据');
        }
      }
    ];
  }
  
  /**
   * 调用外部服务
   * @param serviceConfig 服务配置
   * @param method HTTP方法
   * @param path 请求路径
   * @param data 请求数据
   * @param options 请求选项
   * @returns 响应数据
   */
  async call<T = any>(
    serviceConfig: ServiceDefinition,
    method: string,
    path: string,
    data?: any,
    options: {
      timeout?: number;
      headers?: Record<string, string>;
      params?: Record<string, any>;
    } = {}
  ): Promise<T> {
    const startTime = Date.now();
    const baseUrl = serviceConfig.baseUrl.replace(/\/$/, '');
    
    // 特殊处理OCR服务路径
    let cleanPath = path.replace(/^\//, '');
    if (serviceConfig.code === 'ocr') {
      // 对于OCR服务，始终使用predict/ocr_system端点
      cleanPath = 'predict/ocr_system';
      this.logger.debug(`OCR服务使用标准端点路径: ${cleanPath}`);
      this.logger.debug(`OCR服务原始路径: ${path}, 标准化后: ${cleanPath}`);
    } else if (serviceConfig.code === 'geo') {
      // 坐标逆解析服务路径处理
      if (cleanPath === 'reverse') {
        // 修正为Python服务中的正确路径
        cleanPath = 'rev-geo';
      } else if (cleanPath === 'batch-reverse') {
        cleanPath = 'b-rev-geo';
      }
    } else if (serviceConfig.code === 'address') {
      // 地址提取服务路径处理
      if (cleanPath === 'extract') {
        // 修正为Python服务中的正确路径
        cleanPath = 'extract-address';
      } else if (cleanPath === 'extract-batch') {
        cleanPath = 'extract-address-batch';
      }
    }
    
    // 根据cleanPath是否为空构建URL
    const url = cleanPath ? `${baseUrl}/${cleanPath}` : baseUrl;
    
    // 添加调试日志
    if (serviceConfig.code === 'ocr') {
      this.logger.debug(`OCR服务构建URL: ${url}`);
    }
    
    // 设置超时时间
    let timeoutMs = options.timeout || serviceConfig.timeout || 30000;
    
    // 对OCR服务增加超时时间
    if (serviceConfig.code === 'ocr') {
      timeoutMs = Math.max(timeoutMs, 60000); // 最少60秒超时
      this.logger.debug(`OCR服务请求，设置更长超时时间: ${timeoutMs}ms`);
    }
    
    // 判断是否为FormData类型
    const isFormData = data && typeof data.getHeaders === 'function';
    
    // 构建请求配置
    const config: AxiosRequestConfig = {
      url,
      method: method.toLowerCase(),
      headers: {
        // 仅对非FormData请求设置默认Content-Type
        ...(isFormData ? {} : { 
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        }),
        ...options.headers,
      },
      timeout: timeoutMs,
      params: options.params,
    };
    
    // 根据HTTP方法添加数据
    if (['post', 'put', 'patch'].includes(method.toLowerCase()) && data !== undefined) {
      config.data = data;
    }
    
    try {
      this.logger.debug(`调用外部服务: ${method.toUpperCase()} ${url} ${isFormData ? '[FormData]' : ''}`);
      
      // 添加更详细的请求日志
      if (serviceConfig.code === 'ocr') {
        this.logger.debug(`OCR请求详情: ${JSON.stringify({
          url: config.url,
          method: config.method,
          headers: config.headers,
          timeout: config.timeout,
          dataType: config.data ? (typeof config.data === 'object' ? 'FormData' : typeof config.data) : 'none'
        })}`);
      }
      
      // 执行请求
      const response = await firstValueFrom(
        this.httpService.request<T>(config).pipe(
          timeout(timeoutMs + 5000), // 额外的5秒作为安全边界
          catchError(error => {
            if (serviceConfig.code === 'ocr') {
              this.logger.error(`OCR请求失败: ${error.message}`);
              if (error.response) {
                this.logger.error(`OCR错误响应: 状态=${error.response.status}, 数据=${JSON.stringify(error.response.data)}`);
              }
            }
            throw error;
          }),
        ),
      );
      
      const duration = Date.now() - startTime;
      this.logger.debug(`外部服务调用成功: ${method.toUpperCase()} ${url}, 耗时: ${duration}ms`);
      
      // 增加OCR服务的特殊响应日志
      if (serviceConfig.code === 'ocr') {
        try {
          const responseData = response.data as any;
          this.logger.debug(`OCR服务原始响应: ${JSON.stringify(responseData).substring(0, 500)}`);
          console.log(`调用外部服务data:>>>>>>: ${typeof responseData === 'string' ? responseData.substring(0, 500) : JSON.stringify(responseData).substring(0, 500)}`);
          
          // 检查响应是否包含期望的结构
          if (responseData) {
            this.logger.debug(`OCR响应结构检查: 
              success: ${responseData.success !== undefined ? '存在' : '不存在'}, 
              data: ${responseData.data !== undefined ? '存在' : '不存在'}, 
              results: ${responseData.results !== undefined ? '存在' : '不存在'}`
            );
            
            // 检查是否为HTML错误响应
            if (typeof responseData === 'string' && responseData.includes('<!doctype html>')) {
              this.logger.error(`OCR服务返回HTML错误页面，可能是端点错误或服务问题`);
              throw new Error('OCR服务返回HTML错误页面，请检查服务配置');
            }
            
            // 检查是否缺少必要的字段
            if (!responseData.success && !responseData.results) {
              this.logger.error(`OCR响应缺少必要字段，响应格式不符合预期`);
            }
          }
        } catch (err) {
          this.logger.warn(`无法解析OCR响应数据: ${err.message}`);
        }
      }
      
      return response.data;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(
        `外部服务调用失败: ${method.toUpperCase()} ${url}, 耗时: ${duration}ms, 错误: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
  
  /**
   * 调用外部服务 (供网关代理使用)
   * @param serviceConfig 服务配置
   * @param path 请求路径
   * @param method HTTP方法
   * @param data 请求数据
   * @param headers 请求头
   * @returns 响应数据
   */
  async callExternalService(
    serviceConfig: ServiceDefinition,
    path: string,
    method: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<any> {
    try {
      // 添加服务特定的请求头
      const serviceHeaders = {
        ...headers,
        'X-Service-ID': serviceConfig.id.toString(),
        'X-Service-Name': serviceConfig.code,
      };
      
      // 坐标逆解析服务特殊处理
      if (serviceConfig.code === 'geo' && path === 'reverse' && data && data.latitude !== undefined && data.longitude !== undefined) {
        this.logger.debug(`使用直接地图API进行坐标逆解析: ${data.latitude}, ${data.longitude}`);
        return await this.reverseGeocode(data);
      }
      
      // 批量坐标逆解析服务特殊处理
      if (serviceConfig.code === 'geo' && (path === 'reverse-batch' || path === 'batch-reverse') && data && data.coordinates && Array.isArray(data.coordinates)) {
        this.logger.debug(`使用直接地图API进行批量坐标逆解析，共${data.coordinates.length}个坐标点`);
        return await this.batchReverseGeocode(data.coordinates);
      }
      
      // OCR服务特殊处理 - 如果包含文件，使用FormData提交
      if (serviceConfig.code === 'ocr' && data && (data.file || data.images)) {
        const fileField = data.images || data.file;
        this.logger.debug(`OCR文件上传请求: 使用FormData格式处理, 路径: ${path}, 文件名: ${fileField.originalname || '未知'}`);
        
        // 创建FormData对象
        const FormData = require('form-data');
        const formData = new FormData();
        
        try {
          let filePath;
          const fileField = data.images || data.file;
          
          // 处理已上传的文件
          if (fileField && fileField.path) {
            filePath = fileField.path;
            this.logger.debug(`使用上传的文件: ${filePath}`);
          }
          // 处理Base64图片
          else if (data.images || data.image_base64) {
            // 确保完全删除可能的前缀 - 不管是哪种图像格式
            const base64Data = (data.images || data.image_base64 || '').replace(/^data:image\/[^;]+;base64,/, '');
            this.logger.debug(`已移除Base64前缀，数据长度: ${base64Data.length}`);
            
            // 修复Base64填充问题
            const padding = base64Data.length % 4;
            const paddedBase64 = padding ? base64Data + '='.repeat(4 - padding) : base64Data;
            
            this.logger.debug(`Base64数据处理: 原长度=${base64Data.length}, 填充后长度=${paddedBase64.length}, 添加了${padding ? (4 - padding) : 0}个填充字符`);
            
            // 确保没有其他非法字符
            const cleanBase64 = paddedBase64.replace(/[^A-Za-z0-9+/=]/g, '');
            if (cleanBase64.length !== paddedBase64.length) {
              this.logger.debug(`已移除${paddedBase64.length - cleanBase64.length}个非法字符`);
            }
            
            const buffer = Buffer.from(cleanBase64, 'base64');
            filePath = require('path').join(process.cwd(), 'uploads/temp', `temp_${Date.now()}.png`);
            
            // 确保目录存在
            require('fs').mkdirSync(require('path').dirname(filePath), { recursive: true });
            
            // 保存为临时文件
            require('fs').writeFileSync(filePath, buffer);
            this.logger.debug(`已将Base64数据保存为临时文件: ${filePath}, 大小: ${buffer.length}字节`);
          }
          
          if (!filePath) {
            throw new Error('没有有效的文件可供处理');
          }
          
          // 验证文件是否存在且可读
          const fs = require('fs');
          if (!fs.existsSync(filePath)) {
            throw new Error(`文件不存在: ${filePath}`);
          }
          
          const stats = fs.statSync(filePath);
          if (!stats.isFile()) {
            throw new Error(`路径不是一个文件: ${filePath}`);
          }
          
          if (stats.size === 0) {
            throw new Error(`文件大小为0: ${filePath}`);
          }
          
          this.logger.debug(`文件验证通过: ${filePath}, 大小: ${stats.size}字节`);
          
          // 读取文件内容
          // 对于小文件，直接读取整个文件而不是流
          const fileSize = fs.statSync(filePath).size;
          let fileStream;
          
          if (fileSize < 5 * 1024 * 1024) { // 小于5MB的文件直接读取
            const fileBuffer = fs.readFileSync(filePath);
            this.logger.debug(`直接读取小文件(${fileSize}字节)到内存`);
            fileStream = fileBuffer;
          } else {
            fileStream = fs.createReadStream(filePath);
            this.logger.debug(`使用流读取大文件(${fileSize}字节)`);
          }
          
          // 对于OCR服务，始终使用'images'字段名
          const fieldName = 'images';
          formData.append(fieldName, fileStream, {
            filename: require('path').basename(filePath),
            contentType: 'image/png'
          });
          this.logger.debug(`已添加图片文件到FormData，使用字段名'${fieldName}'，文件名：${require('path').basename(filePath)}`);
          
          // 添加其他表单字段
          Object.keys(data).forEach(key => {
            if (!['images', 'image_base64', 'file'].includes(key)) {
              formData.append(key, data[key]);
              this.logger.debug(`添加额外表单字段: ${key}=${data[key]}`);
            }
          });
          
          // 添加详细的调试日志
          this.logger.debug(`发送FormData请求到: ${serviceConfig.baseUrl}`);
          this.logger.debug(`完整请求路径: ${path}`);
          this.logger.debug(`文件大小: ${require('fs').statSync(filePath).size}字节`);
          this.logger.debug(`FormData内容: 字段数量=${Object.keys(data).length + 1}`);
          
          // 验证FormData是否正确构建
          try {
            const formDataLength = formData.getLengthSync();
            this.logger.debug(`FormData长度: ${formDataLength}`);
          } catch (err) {
            this.logger.warn(`无法获取FormData长度: ${err.message}`);
          }
          
          // 调用基础方法时使用FormData
          const response = await this.call(
            serviceConfig,
            method,
            path,
            formData,
            {
              headers: { 
                ...serviceHeaders,
                ...formData.getHeaders() // 使用FormData的请求头
              },
              timeout: 60000 // 文件上传请求使用1分钟超时
            }
          );
          
          // 删除临时文件
          try {
            require('fs').unlinkSync(filePath);
          } catch (err) {
            this.logger.warn(`删除临时文件失败: ${err.message}`);
          }
          
          return response;
        } catch (err) {
          this.logger.error(`处理OCR FormData时出错: ${err.message}`, err.stack);
          throw err;
        }
      }
      
      // 调用基础方法
      return await this.call(
        serviceConfig,
        method,
        path,
        data,
        {
          headers: serviceHeaders,
          timeout: serviceConfig.timeout,
        }
      );
    } catch (error) {
      this.logger.error(
        `调用外部服务失败: ${method} ${serviceConfig.code}/${path}, 错误: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
  
  /**
   * 检查服务健康状态
   * @param serviceConfig 服务配置
   * @returns 服务是否健康
   */
  async checkHealth(serviceConfig: ServiceDefinition): Promise<boolean> {
    try {
      const baseUrl = serviceConfig.baseUrl.replace(/\/$/, '');
      const healthUrl = `${baseUrl}/health`;
      
      const response = await firstValueFrom(
        this.httpService.get(healthUrl, {
          timeout: 5000,
          validateStatus: status => status < 500,
        }),
      );
      
      return response.status === 200;
    } catch (error) {
      this.logger.warn(`服务健康检查失败: ${serviceConfig.name}, 错误: ${error.message}`);
      return false;
    }
  }

  /**
   * 地理坐标逆解析 - 通过多个地图API依次尝试
   * @param coordinates 坐标信息
   * @returns 逆解析结果
   */
  public async reverseGeocode(coordinates: any): Promise<any> {
    const startTime = Date.now();
    
    try {
      this.logger.log(`开始地理坐标逆解析，坐标: ${coordinates.latitude},${coordinates.longitude}`);
      
      // 标准化坐标值为数字类型
      const lat = Number(coordinates.latitude);
      const lng = Number(coordinates.longitude);
      
      // 按顺序尝试调用国内地图API
      for (const apiConfig of this.mapApiConfigs) {
        try {
          this.logger.log(`尝试使用${apiConfig.provider}进行逆地理编码，API Key: ${apiConfig.key.substring(0, 5)}...`);
          
          // 根据不同地图API构建请求参数
          const params: Record<string, string> = {};
          
          if (apiConfig.provider === '高德地图') {
            params.key = apiConfig.key;
            params.location = `${lng},${lat}`; // 高德要求经度在前，纬度在后
            params.output = 'json';
            params.radius = '1000';
            params.extensions = 'all';
            params.roadlevel = '0';
            params.homeorcorp = '1';
            params.poitype = 'all';
          } else if (apiConfig.provider === '腾讯地图') {
            params.key = apiConfig.key;
            params.location = `${lat},${lng}`; // 腾讯要求纬度在前，经度在后
            params.output = 'json';
            params.get_poi = '0';
          } else if (apiConfig.provider === '百度地图') {
            params.ak = apiConfig.key; // 百度使用ak而不是key
            params.output = 'json';
            params.coordtype = 'wgs84ll'; // 使用WGS84坐标系
            params.location = `${lat},${lng}`; // 百度要求纬度在前，经度在后
            params.extensions_poi = '0';
            params.language = 'zh-CN';
            params.ret_coordtype = 'gcj02ll'; // 返回坐标类型为国测局坐标
          }
          
          // 发送请求，设置较短的超时时间
          this.logger.debug(`请求${apiConfig.provider} API: ${apiConfig.url} 参数: ${JSON.stringify(params)}`);
          const response = await firstValueFrom(
            this.httpService.get(apiConfig.url, {
              params,
              timeout: 3000, // 减少超时时间，以便快速失败并尝试下一个
              headers: {
                'User-Agent': 'OpenPlatform-NestJS/1.0',
                'Accept': 'application/json'
              }
            })
          );
          
          // 处理响应
          if (response.status === 200) {
            this.logger.debug(`${apiConfig.provider}返回成功: ${JSON.stringify(response.data).substring(0, 200)}...`);
            
            // 检查API返回状态
            if (apiConfig.provider === '高德地图' && response.data.status !== '1') {
              throw new Error(`API返回错误: ${response.data.info || '未知错误'}`);
            } else if (apiConfig.provider === '腾讯地图' && response.data.status !== 0) {
              throw new Error(`API返回错误: ${response.data.message || '未知错误'}`);
            } else if (apiConfig.provider === '百度地图' && response.data.status !== 0) {
              throw new Error(`API返回错误: ${response.data.message || '未知错误'}`);
            }
            
            const result = apiConfig.parseResponse(response.data, coordinates);
            
            const processingTime = Date.now() - startTime;
            result.processing_time = `${processingTime}ms`;
            
            this.logger.log(`成功使用${apiConfig.provider}解析坐标，耗时: ${processingTime}ms`);
            return result;
          } else {
            throw new Error(`API返回状态码: ${response.status}`);
          }
        } catch (error) {
          const errorMsg = error.response?.data 
            ? `${error.message}, 响应: ${JSON.stringify(error.response.data)}` 
            : error.message;
          
          this.logger.warn(`${apiConfig.provider}逆地理编码失败: ${errorMsg}`);
          // 继续尝试下一个API
        }
      }
      
      // 所有地图API都失败，返回兜底结果
      this.logger.warn('所有地图API调用失败，返回默认结果');
      const processingTime = Date.now() - startTime;
      
      return {
        success: false,
        error: '所有地理解析服务均不可用',
        coordinates: coordinates,
        address: {
          full_address: `坐标(${coordinates.latitude},${coordinates.longitude})无法解析`,
          province: '',
          city: '',
          district: '',
          street: '',
          house_number: '',
          postcode: '',
          confidence: 0,
          resolution_method: 'default_fallback'
        },
        processing_time: `${processingTime}ms`
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(`地理坐标逆解析失败: ${error.message}`, error.stack);
      
      return {
        success: false,
        error: error.message,
        coordinates: coordinates,
        processing_time: `${processingTime}ms`,
        address: {
          full_address: `坐标(${coordinates.latitude},${coordinates.longitude})无法解析`,
          province: '',
          city: '',
          district: '',
          street: '',
          house_number: '',
          postcode: '',
          confidence: 0,
          resolution_method: 'error_fallback'
        }
      };
    }
  }

  /**
   * 批量地理坐标逆解析 - 通过多个地图API依次尝试
   * @param coordinates 坐标数组
   * @returns 批量逆解析结果
   */
  public async batchReverseGeocode(coordinates: any[]): Promise<any> {
    const startTime = Date.now();
    const results: any[] = [];

    try {
      this.logger.log(`开始批量地理坐标逆解析，共${coordinates.length}个坐标点`);
      
      // 处理每个坐标点
      for (const coord of coordinates) {
        try {
          const result = await this.reverseGeocode(coord);
          results.push(result);
        } catch (error) {
          this.logger.warn(`坐标(${coord.latitude},${coord.longitude})解析失败: ${error.message}`);
          // 添加失败结果
          results.push({
            success: false,
            error: error.message,
            coordinates: {
              latitude: coord.latitude,
              longitude: coord.longitude
            },
            address: {
              full_address: `坐标(${coord.latitude},${coord.longitude})无法解析`,
              resolution_method: 'failed'
            }
          });
        }
      }
      
      const processingTime = Date.now() - startTime;
      
      // 返回与原接口一致的格式
      return {
        success: true,
        total_count: coordinates.length,
        processed_count: results.length,
        results: results,
        processing_time: `${processingTime}ms`
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(`批量地理坐标逆解析失败: ${error.message}`, error.stack);
      
      return {
        success: false,
        error: error.message,
        total_count: coordinates.length,
        processed_count: results.length,
        results: results,
        processing_time: `${processingTime}ms`
      };
    }
  }
} 