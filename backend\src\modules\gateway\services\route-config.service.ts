import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '@/shared/redis.service';
import { IRouteConfig } from '../interfaces/gateway.interfaces';
import { API_ROUTES } from '../config/gateway.constants';

/**
 * 路由配置服务
 * 负责动态路由配置的管理和持久化
 * 遵循单一职责原则，专注于路由配置管理
 */
@Injectable()
export class RouteConfigService {
  private readonly logger = new Logger(RouteConfigService.name);
  private readonly configKeyPrefix = 'gateway:route:config:';
  private readonly configCacheKey = 'gateway:route:cache';

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * 获取所有路由配置
   */
  async getAllRouteConfigs(): Promise<IRouteConfig[]> {
    try {
      // 先尝试从缓存获取
      const cached = await this.getCachedConfigs();
      if (cached.length > 0) {
        return cached;
      }

      // 从存储获取
      const stored = await this.getStoredConfigs();
      if (stored.length > 0) {
        await this.setCachedConfigs(stored);
        return stored;
      }

      // 返回默认配置
      const defaultConfigs = this.getDefaultConfigs();
      await this.setCachedConfigs(defaultConfigs);
      return defaultConfigs;

    } catch (error) {
      this.logger.error(`获取路由配置失败: ${error.message}`);
      return this.getDefaultConfigs();
    }
  }

  /**
   * 获取单个路由配置
   */
  async getRouteConfig(prefix: string): Promise<IRouteConfig | null> {
    try {
      const configs = await this.getAllRouteConfigs();
      return configs.find(config => config.prefix === prefix) || null;
    } catch (error) {
      this.logger.error(`获取路由配置失败 [${prefix}]: ${error.message}`);
      return null;
    }
  }

  /**
   * 保存路由配置
   */
  async saveRouteConfig(config: IRouteConfig): Promise<void> {
    try {
      const configKey = `${this.configKeyPrefix}${config.prefix}`;
      const configData = JSON.stringify(config);
      
      await this.redisService.set(configKey, configData);
      
      // 清除缓存，强制重新加载
      await this.clearConfigCache();
      
      this.logger.log(`保存路由配置: ${config.prefix}`);

    } catch (error) {
      this.logger.error(`保存路由配置失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 删除路由配置
   */
  async deleteRouteConfig(prefix: string): Promise<void> {
    try {
      const configKey = `${this.configKeyPrefix}${prefix}`;
      await this.redisService.del(configKey);
      
      // 清除缓存
      await this.clearConfigCache();
      
      this.logger.log(`删除路由配置: ${prefix}`);

    } catch (error) {
      this.logger.error(`删除路由配置失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新路由配置
   */
  async updateRouteConfig(prefix: string, updates: Partial<IRouteConfig>): Promise<void> {
    try {
      const existingConfig = await this.getRouteConfig(prefix);
      if (!existingConfig) {
        throw new Error(`路由配置不存在: ${prefix}`);
      }

      const updatedConfig: IRouteConfig = {
        ...existingConfig,
        ...updates,
        prefix, // 确保prefix不被修改
      };

      await this.saveRouteConfig(updatedConfig);
      this.logger.log(`更新路由配置: ${prefix}`);

    } catch (error) {
      this.logger.error(`更新路由配置失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 批量保存路由配置
   */
  async saveRouteConfigs(configs: IRouteConfig[]): Promise<void> {
    try {
      const promises = configs.map(config => this.saveRouteConfig(config));
      await Promise.all(promises);
      
      this.logger.log(`批量保存 ${configs.length} 个路由配置`);

    } catch (error) {
      this.logger.error(`批量保存路由配置失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 重置为默认配置
   */
  async resetToDefaultConfigs(): Promise<void> {
    try {
      // 清除所有存储的配置
      const pattern = `${this.configKeyPrefix}*`;
      const keys = await this.redisService.keys(pattern);
      
      if (keys.length > 0) {
        await Promise.all(keys.map(key => this.redisService.del(key)));
      }

      // 清除缓存
      await this.clearConfigCache();
      
      this.logger.log('重置为默认路由配置');

    } catch (error) {
      this.logger.error(`重置路由配置失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证路由配置
   */
  validateRouteConfig(config: IRouteConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证必填字段
    if (!config.prefix) {
      errors.push('prefix字段不能为空');
    } else if (!config.prefix.startsWith('/')) {
      errors.push('prefix必须以/开头');
    }

    if (!config.target) {
      errors.push('target字段不能为空');
    }

    if (!config.methods || config.methods.length === 0) {
      errors.push('methods字段不能为空');
    }

    if (!config.queue) {
      errors.push('queue字段不能为空');
    }

    // 验证数值字段
    if (config.timeout && config.timeout <= 0) {
      errors.push('timeout必须大于0');
    }

    if (config.maxProxyWaitTime && config.maxProxyWaitTime <= 0) {
      errors.push('maxProxyWaitTime必须大于0');
    }

    if (config.avgProcessingTime && config.avgProcessingTime <= 0) {
      errors.push('avgProcessingTime必须大于0');
    }

    // 验证HTTP方法
    const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];
    const invalidMethods = config.methods?.filter(method => 
      !validMethods.includes(method.toUpperCase())
    ) || [];
    
    if (invalidMethods.length > 0) {
      errors.push(`无效的HTTP方法: ${invalidMethods.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 获取配置统计信息
   */
  async getConfigStats(): Promise<{
    totalConfigs: number;
    configsByTarget: Record<string, number>;
    configsByMethod: Record<string, number>;
    avgTimeout: number;
    enabledAsyncConfigs: number;
    enabledProxyAsyncConfigs: number;
  }> {
    try {
      const configs = await this.getAllRouteConfigs();
      
      const stats = {
        totalConfigs: configs.length,
        configsByTarget: {} as Record<string, number>,
        configsByMethod: {} as Record<string, number>,
        avgTimeout: 0,
        enabledAsyncConfigs: 0,
        enabledProxyAsyncConfigs: 0,
      };

      let totalTimeout = 0;

      configs.forEach(config => {
        // 统计目标服务
        stats.configsByTarget[config.target] = (stats.configsByTarget[config.target] || 0) + 1;

        // 统计HTTP方法
        config.methods.forEach(method => {
          stats.configsByMethod[method] = (stats.configsByMethod[method] || 0) + 1;
        });

        // 统计超时时间
        totalTimeout += config.timeout;

        // 统计异步配置
        if (config.allowAsync) {
          stats.enabledAsyncConfigs++;
        }
        if (config.allowProxyAsync) {
          stats.enabledProxyAsyncConfigs++;
        }
      });

      if (configs.length > 0) {
        stats.avgTimeout = Math.round(totalTimeout / configs.length);
      }

      return stats;

    } catch (error) {
      this.logger.error(`获取配置统计失败: ${error.message}`);
      return {
        totalConfigs: 0,
        configsByTarget: {},
        configsByMethod: {},
        avgTimeout: 0,
        enabledAsyncConfigs: 0,
        enabledProxyAsyncConfigs: 0,
      };
    }
  }

  /**
   * 从缓存获取配置
   */
  private async getCachedConfigs(): Promise<IRouteConfig[]> {
    try {
      const cached = await this.redisService.get(this.configCacheKey);
      return cached ? JSON.parse(cached) : [];
    } catch (error) {
      this.logger.error(`获取缓存配置失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 设置缓存配置
   */
  private async setCachedConfigs(configs: IRouteConfig[]): Promise<void> {
    try {
      const configData = JSON.stringify(configs);
      await this.redisService.set(this.configCacheKey, configData, 3600); // 1小时过期
    } catch (error) {
      this.logger.error(`设置缓存配置失败: ${error.message}`);
    }
  }

  /**
   * 清除配置缓存
   */
  private async clearConfigCache(): Promise<void> {
    try {
      await this.redisService.del(this.configCacheKey);
    } catch (error) {
      this.logger.error(`清除配置缓存失败: ${error.message}`);
    }
  }

  /**
   * 从存储获取配置
   */
  private async getStoredConfigs(): Promise<IRouteConfig[]> {
    try {
      const pattern = `${this.configKeyPrefix}*`;
      const keys = await this.redisService.keys(pattern);
      
      const configs: IRouteConfig[] = [];
      
      for (const key of keys) {
        try {
          const configData = await this.redisService.get(key);
          if (configData) {
            const config: IRouteConfig = JSON.parse(configData);
            configs.push(config);
          }
        } catch (error) {
          this.logger.error(`解析配置失败: ${key}`, error);
        }
      }

      return configs;

    } catch (error) {
      this.logger.error(`获取存储配置失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfigs(): IRouteConfig[] {
    const configs: IRouteConfig[] = [];

    // 添加OCR路由
    configs.push(API_ROUTES.LOGISTICS_OCR);

    // 添加申通面单OCR路由
    configs.push(API_ROUTES.STO_OCR);

    // 添加地址提取路由
    configs.push(API_ROUTES.ADDRESS_EXTRACTION);

    // 添加地址标准化路由
    configs.push(API_ROUTES.ADDRESS_NORMALIZE);

    // 添加地理坐标路由
    const geoRoute = API_ROUTES.GEO_COORDINATE;
    if (geoRoute.paths) {
      Object.values(geoRoute.paths).forEach(pathConfig => {
        configs.push({
          prefix: `${geoRoute.prefix}${pathConfig.path}`,
          target: pathConfig.target,
          methods: pathConfig.methods,
          allowAsync: pathConfig.allowAsync,
          allowProxyAsync: pathConfig.allowProxyAsync,
          defaultMode: pathConfig.defaultMode,
          timeout: pathConfig.timeout,
          queue: pathConfig.queue,
        });
      });
    }

    return configs;
  }

  /**
   * 导出配置
   */
  async exportConfigs(): Promise<string> {
    try {
      const configs = await this.getAllRouteConfigs();
      return JSON.stringify(configs, null, 2);
    } catch (error) {
      this.logger.error(`导出配置失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 导入配置
   */
  async importConfigs(configJson: string): Promise<void> {
    try {
      const configs: IRouteConfig[] = JSON.parse(configJson);
      
      // 验证所有配置
      for (const config of configs) {
        const validation = this.validateRouteConfig(config);
        if (!validation.isValid) {
          throw new Error(`配置验证失败 [${config.prefix}]: ${validation.errors.join(', ')}`);
        }
      }

      // 保存配置
      await this.saveRouteConfigs(configs);
      
      this.logger.log(`导入 ${configs.length} 个路由配置`);

    } catch (error) {
      this.logger.error(`导入配置失败: ${error.message}`);
      throw error;
    }
  }
}
