<template>
  <div ref="slideVerifyContainer" class="slide-verify-container">
    <slide-verify ref="slideVerifyRef" :width="props.width" :height="props.height" :slider-text="props.sliderText"
      :success-text="props.successText" :fail-text="props.failText" :refresh-text="props.refreshText"
      :accuracy="props.accuracy" :imgs="backgroundImages" @success="onSuccess" @fail="onFail" @refresh="onRefresh"
      @again="onAgain" />

    <!-- 安全验证状态指示器（可选） -->
    <div v-if="props.enableBehaviorTracking || props.enableDeviceFingerprint" class="security-indicators">
      <div v-if="props.enableDeviceFingerprint && deviceFingerprint" class="security-indicator"
        :class="{ 'fingerprint-ready': deviceFingerprint }">
        <span class="indicator-icon">🔒</span>
        <span class="indicator-text">设备已识别</span>
      </div>
      <div v-if="props.enableBehaviorTracking && isTrackingBehavior" class="security-indicator"
        :class="{ 'tracking': isTrackingBehavior }">
        <span class="indicator-icon">👁️</span>
        <span class="indicator-text">行为监测中</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import SlideVerify, { type SlideVerifyInstance } from 'vue3-slide-verify'
import 'vue3-slide-verify/dist/style.css'
import { request } from '@/utils/request'
import { deviceFingerprint as deviceFingerprintGenerator, type DeviceFingerprint } from '@/utils/deviceFingerprint'
import { behaviorTracker, type BehaviorPattern } from '@/utils/behaviorTracker'

// 定义 props
interface Props {
  width?: number
  height?: number
  sliderText?: string
  successText?: string
  failText?: string
  refreshText?: string
  accuracy?: number
  show?: boolean
  imgs?: string[]
  enableBehaviorTracking?: boolean
  enableDeviceFingerprint?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  width: 310,
  height: 155,
  sliderText: '请拖动滑块完成验证',
  successText: '验证成功',
  failText: '验证失败',
  refreshText: '点击重新验证',
  accuracy: 5,
  show: true,
  imgs: () => [],
  enableBehaviorTracking: true,
  enableDeviceFingerprint: true
})

// 定义 emits
const emit = defineEmits<{
  success: [data: {
    x: number
    y: number
    timestamp: number
    deviceFingerprint?: DeviceFingerprint
    behaviorPattern?: BehaviorPattern
  }]
  fail: [data?: {
    deviceFingerprint?: DeviceFingerprint
    behaviorPattern?: BehaviorPattern
  }]
  refresh: []
  again: []
}>()

// 组件引用
const slideVerifyRef = ref<SlideVerifyInstance>()
const slideVerifyContainer = ref<HTMLElement>()

// 安全验证状态
const deviceFingerprint = ref<DeviceFingerprint | null>(null)
const isTrackingBehavior = ref(false)

// 默认背景图片（使用本地SVG图片）
const defaultImages = [
  '/images/bg1.svg',
  '/images/bg2.svg',
  '/images/bg3.svg',
  '/images/bg4.svg',
  '/images/bg5.svg'
]

// 背景图片
const backgroundImages = computed(() => {
  return props.imgs && props.imgs.length > 0 ? props.imgs : defaultImages
})

/**
 * 开始行为轨迹记录
 */
const startBehaviorTracking = () => {
  if (props.enableBehaviorTracking && !isTrackingBehavior.value) {
    try {
      behaviorTracker.startTracking(slideVerifyContainer.value)
      isTrackingBehavior.value = true
    } catch (error) {
      console.warn('Failed to start behavior tracking:', error)
    }
  }
}

/**
 * 停止行为轨迹记录
 */
const stopBehaviorTracking = (): BehaviorPattern | null => {
  if (props.enableBehaviorTracking && isTrackingBehavior.value) {
    try {
      const pattern = behaviorTracker.stopTracking()
      isTrackingBehavior.value = false
      return pattern
    } catch (error) {
      console.warn('Failed to stop behavior tracking:', error)
      isTrackingBehavior.value = false
    }
  }
  return null
}

/**
 * 获取设备指纹
 */
const getDeviceFingerprint = async (): Promise<DeviceFingerprint | null> => {
  if (props.enableDeviceFingerprint) {
    try {
      return await deviceFingerprintGenerator.generateFingerprint()
    } catch (error) {
      console.warn('Failed to generate device fingerprint:', error)
    }
  }
  return null
}

/**
 * 从行为轨迹中计算移动距离
 */
const calculateMoveDistance = (behaviorPattern: BehaviorPattern | null): number => {
  if (!behaviorPattern || !behaviorPattern.mouseEvents || behaviorPattern.mouseEvents.length < 2) {
    return 0
  }

  const mouseEvents = behaviorPattern.mouseEvents
  const startPoint = mouseEvents[0]
  const endPoint = mouseEvents[mouseEvents.length - 1]

  // 计算水平移动距离（拼图验证主要关注水平移动）
  const horizontalDistance = Math.abs(endPoint.x - startPoint.x)

  return Math.round(horizontalDistance)
}

// 验证成功
const onSuccess = async (times: number) => {
  console.log('vue3-slide-verify success event times (ms):', times)
  // 停止行为轨迹记录
  const behaviorPattern = stopBehaviorTracking()

  try {
    // 根据官方文档，vue3-slide-verify的onSuccess回调只返回时间参数（毫秒单位）
    // 我们需要从行为轨迹中计算移动距离
    const duration = times // 毫秒单位
    const moveDistance = calculateMoveDistance(behaviorPattern)

    console.log('Duration from plugin (ms):', duration)
    console.log('Calculated moveDistance:', moveDistance)

    // 收集安全验证数据
    const securityData = {
      moveDistance: moveDistance,
      duration: duration,
      deviceFingerprint: deviceFingerprint.value,
      behaviorPattern: behaviorPattern,
      userAgent: navigator.userAgent,
      screenResolution: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language,
      // 企业级多重验证数据
      timestamp: Date.now(),
      sessionId: generateSessionId(),
      clientIP: await getClientIP(),
      browserFingerprint: await generateBrowserFingerprint(),
      mouseTrajectory: behaviorPattern?.mouseEvents || []
    }

    console.log('Security data to send:', securityData)

    // 调用后端验证接口
    const response: Record<string, any> = await request.post('/auth/slide/verify', securityData)
    if (response?.success) {
      emit('success', {
        x: moveDistance,
        y: 0,
        timestamp: Date.now(),
        deviceFingerprint: deviceFingerprint.value || undefined,
        behaviorPattern: behaviorPattern || undefined
      })
    } else {
      console.error('Backend verification failed:', response.data)
      // 后端验证失败，重置验证码
      reset()
      emit('fail', {
        deviceFingerprint: deviceFingerprint.value || undefined,
        behaviorPattern: behaviorPattern || undefined
      })
    }
  } catch (error) {
    console.error('后端验证失败:', error)
    // 网络错误或其他错误，重置验证码
    reset()
    emit('fail', {
      deviceFingerprint: deviceFingerprint.value || undefined,
      behaviorPattern: behaviorPattern || undefined
    })
  }
}

/**
 * 生成会话ID
 */
const generateSessionId = (): string => {
  return 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
}

/**
 * 获取客户端IP（通过第三方服务）
 */
const getClientIP = async (): Promise<string> => {
  try {
    const response = await fetch('https://api.ipify.org?format=json')
    const data = await response.json()
    return data.ip || 'unknown'
  } catch (error) {
    console.warn('Failed to get client IP:', error)
    return 'unknown'
  }
}

/**
 * 生成浏览器指纹
 */
const generateBrowserFingerprint = async (): Promise<string> => {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  if (ctx) {
    ctx.textBaseline = 'top'
    ctx.font = '14px Arial'
    ctx.fillText('Browser fingerprint test', 2, 2)
  }

  const fingerprint = {
    userAgent: navigator.userAgent,
    language: navigator.language,
    platform: navigator.platform,
    cookieEnabled: navigator.cookieEnabled,
    doNotTrack: navigator.doNotTrack,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    screen: `${screen.width}x${screen.height}x${screen.colorDepth}`,
    canvas: canvas.toDataURL(),
    webgl: getWebGLFingerprint(),
    plugins: Array.from(navigator.plugins).map(p => p.name).join(',')
  }

  // 生成指纹哈希
  const fingerprintString = JSON.stringify(fingerprint)
  return await hashString(fingerprintString)
}

/**
 * 获取WebGL指纹
 */
const getWebGLFingerprint = (): string => {
  try {
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl') as WebGLRenderingContext | null
    if (!gl) return 'no-webgl'

    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info')
    if (debugInfo && debugInfo.UNMASKED_VENDOR_WEBGL && debugInfo.UNMASKED_RENDERER_WEBGL) {
      const vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL)
      const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)
      return `${vendor}~${renderer}`
    }
    return 'webgl-available'
  } catch (error) {
    return 'webgl-error'
  }
}

/**
 * 字符串哈希函数
 */
const hashString = async (str: string): Promise<string> => {
  const encoder = new TextEncoder()
  const data = encoder.encode(str)
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}

// 验证失败
const onFail = () => {
  const behaviorPattern = stopBehaviorTracking()
  emit('fail', {
    deviceFingerprint: deviceFingerprint.value || undefined,
    behaviorPattern: behaviorPattern || undefined
  })
}

// 刷新验证码
const onRefresh = () => {
  restartBehaviorTracking()
  emit('refresh')
}

// 重新验证
const onAgain = () => {
  restartBehaviorTracking()
  emit('again')
}

/**
 * 重新开始行为轨迹记录的通用方法
 */
const restartBehaviorTracking = () => {
  if (isTrackingBehavior.value) {
    stopBehaviorTracking()
  }
  startBehaviorTracking()
}

// 重置验证码
const reset = () => {
  if (slideVerifyRef.value) {
    slideVerifyRef.value.refresh()
  }
  restartBehaviorTracking()
}

// 刷新验证码
const refresh = () => {
  if (slideVerifyRef.value) {
    slideVerifyRef.value.refresh()
  }
  restartBehaviorTracking()
}

// 暴露方法给父组件
defineExpose({
  reset,
  refresh,
  startBehaviorTracking,
  stopBehaviorTracking,
  getDeviceFingerprint
})

onMounted(async () => {
  // 生成设备指纹
  if (props.enableDeviceFingerprint) {
    deviceFingerprint.value = await getDeviceFingerprint()
  }

  // 开始行为轨迹记录
  startBehaviorTracking()
})
</script>

<style scoped>
.slide-verify-container {
  width: 100%;
  margin: 10px 0;
}

.security-indicators {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  font-size: 12px;
  color: #666;
}

.security-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.indicator-icon {
  font-size: 10px;
}

.indicator-text {
  font-size: 10px;
  color: #888;
}

/* 行为追踪时的动画效果 */
.security-indicator.tracking {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }

  100% {
    opacity: 1;
  }
}

/* 设备指纹识别成功的样式 */
.security-indicator.fingerprint-ready {
  background: #e8f5e8;
  border-color: #c3e6c3;
  color: #2e7d32;
}

.security-indicator.fingerprint-ready .indicator-text {
  color: #2e7d32;
}

/* 自定义样式覆盖 */
:deep(.slide-verify) {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.slide-verify-slider) {
  background: linear-gradient(90deg, #1890ff, #36cfc9);
  border-radius: 4px;
}

:deep(.slide-verify-slider:hover) {
  background: linear-gradient(90deg, #40a9ff, #5cdbd3);
}

:deep(.slide-verify-slider-mask) {
  background: rgba(24, 144, 255, 0.1);
}

:deep(.slide-verify-slider-mask-item) {
  border-radius: 4px;
}

:deep(.slide-verify-refresh) {
  color: #1890ff;
}

:deep(.slide-verify-refresh:hover) {
  color: #40a9ff;
}
</style>