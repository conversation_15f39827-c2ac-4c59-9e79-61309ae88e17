import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import { RedisService } from '../../shared/redis.service';
import { AuthCacheService } from '../../shared/auth-cache.service';
import { IS_PUBLIC_KEY } from '../constant';
import { CoreAuthService } from '../../shared/core-auth.service';
import { AuthUser, AuthApiKey, AuthResult } from '../types/auth.types';
import { RequestUtils } from '../utils/request.utils';
import * as crypto from 'crypto';
import { AuthStrategy, AUTH_STRATEGY_KEY } from '../decorators/auth-strategy.decorator';

/**
 * 签名认证信息接口
 */
interface AuthInfo {
  apiKey: string;
  timestamp: string;
  nonce: string;
  signature: string;
}

/**
 * 全局权限守卫
 * 根据指定的认证策略执行不同的认证流程：
 * 1. API_KEY策略：使用基于HMAC-SHA256的签名认证
 * 2. JWT策略：使用JWT令牌进行认证（默认策略）
 * 
 * 包含Redis容错机制和熔断器模式
 */
@Injectable()
export class UnifiedAuthGuard implements CanActivate {
  protected readonly logger = new Logger(UnifiedAuthGuard.name);
  private readonly circuitBreaker = new Map<
    string,
    { failures: number; lastFailure: number; isOpen: boolean }
  >();
  private readonly CIRCUIT_BREAKER_THRESHOLD = 5;
  private readonly CIRCUIT_BREAKER_TIMEOUT = 60000; // 1分钟

  constructor(
    private readonly reflector: Reflector,
    private readonly coreAuthService: CoreAuthService,
    private readonly redisService: RedisService,
    private readonly authCacheService: AuthCacheService,
    protected readonly configService: ConfigService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const handler = context.getHandler();
    const classRef = context.getClass();
    try {
      // 检查是否为公开接口
      const isPublic = this.reflector.getAllAndOverride<boolean>(
        IS_PUBLIC_KEY,
        [handler, classRef],
      );

      if (isPublic) {
        this.logger.debug(`公开接口访问: ${request.method} ${request.url}`);
        return true;
      }

      // 获取认证策略
      const authStrategy = this.reflector.getAllAndOverride<AuthStrategy>(
        AUTH_STRATEGY_KEY,
        [handler, classRef],
      );
      
      // 检查是否不需要认证
      if (authStrategy === AuthStrategy.NONE) {
        this.logger.debug(`无需认证的接口: ${request.method} ${request.url}`);
        return true;
      }

      // 执行认证
      let authResult: AuthResult;

      if (authStrategy === AuthStrategy.API_KEY) {
        // 使用签名认证
        const authInfo = RequestUtils.extractSignatureAuth(request);

        if (!authInfo) {
          this.logger.warn(`缺少签名认证信息: ${request.method} ${request.url}`);
          throw new UnauthorizedException('API签名认证失败: 缺少必要的认证信息');
        }

        this.logger.debug(`使用签名认证策略: ${request.method} ${request.url}`);
        authResult = await this.validateSignatureAuth(authInfo, request);
      } else {
        // 默认使用JWT认证策略
        this.logger.debug(`使用JWT认证策略: ${request.method} ${request.url}`);
        const jwtToken = this.extractJwtToken(request);

        if (!jwtToken) {
          throw new UnauthorizedException('JWT认证失败: 缺少令牌');
        }

        authResult = await this.validateJwtWithCircuitBreaker(jwtToken);
      }

      if (!authResult.isValid) {
        this.logger.warn(
          `认证失败: ${authResult.error} - ${request.method} ${request.url}`,
        );
        throw new UnauthorizedException(authResult.error || '认证失败');
      }
      
      // 设置请求上下文
      (request as any).user = authResult.user;
      if (authResult.apiKey) {
        (request as any).apiKey = authResult.apiKey;
      }
      
      this.logger.debug(
        `认证成功: ${authResult.authType} - 用户ID: ${authResult.user!.id}`,
      );
      return true;
    } catch (error) {
      this.logger.error(`认证守卫错误: ${error.message}`, error.stack);
      if (
        error instanceof UnauthorizedException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }
      throw new UnauthorizedException('认证服务异常');
    }
  }

  /**
   * 从请求中提取JWT令牌
   */
  private extractJwtToken(request: Request): string | null {
    return RequestUtils.extractJwtToken(request);
  }

  /**
   * 验证签名认证
   */
  private async validateSignatureAuth(
    authInfo: AuthInfo,
    request: Request
  ): Promise<AuthResult> {
    const circuitKey = 'signature-validation';
    if (this.isCircuitOpen(circuitKey)) {
      this.logger.warn('签名验证熔断器开启，跳过验证');
      return { isValid: false, error: 'Service temporarily unavailable' };
    }
    
    try {
      // 1. 验证时间戳是否在有效期内（15分钟）
      const currentTime = Math.floor(Date.now() / 1000);
      const requestTime = parseInt(authInfo.timestamp, 10);
      
      if (isNaN(requestTime) || currentTime - requestTime > 15 * 60) {
        this.logger.warn(`请求已过期: ${currentTime - requestTime}秒`);
        return { isValid: false, error: '请求已过期' };
      }
      
      // 2. 验证nonce是否已使用（防止重放攻击）
      const nonceKey = `auth:nonce:${authInfo.apiKey}:${authInfo.nonce}`;

      // 检查nonce是否已存在
      const existingNonce = await this.redisService.get(nonceKey);

      if (existingNonce) {
        // 获取nonce的创建时间
        const nonceTimestamp = parseInt(existingNonce);
        const currentTime = Date.now();
        const timeDiff = currentTime - nonceTimestamp;

        // 如果nonce在5秒内被重复使用，可能是浏览器重复请求，给予警告但允许通过
        if (timeDiff < 5000) {
          this.logger.warn(`检测到可能的重复请求，但在容忍时间内: ${authInfo.nonce}, 时间差: ${timeDiff}ms`);
          // 允许通过，但不更新nonce时间戳
        } else {
          this.logger.warn(`Nonce已被使用: ${authInfo.nonce}, 时间差: ${timeDiff}ms`);
          return { isValid: false, error: '重复的请求标识' };
        }
      } else {
        // 设置nonce，记录当前时间戳
        await this.redisService.setex(nonceKey, 15 * 60, Date.now().toString());
      }

      // 3. 从缓存中获取API密钥信息
      const cachedApiKey = await this.authCacheService.getApiKey(authInfo.apiKey);

      if (!cachedApiKey) {
        this.logger.warn(`API密钥在缓存中不存在: ${authInfo.apiKey}`);
        return { isValid: false, error: 'API密钥不存在' };
      }

      // 4. 转换为AuthApiKey类型
      const authApiKey: AuthApiKey = {
        id: Number(cachedApiKey.id),
        userId: Number(cachedApiKey.userId),
        serviceId: Number(cachedApiKey.serviceId || 0),
        name: cachedApiKey.name,
        keyStatus: cachedApiKey.keyStatus,
        keyType: cachedApiKey.keyType,
        description: cachedApiKey.name, // 使用name作为description
        permissions: cachedApiKey.permissions,
        expiresAt: cachedApiKey.expiresAt ? new Date(cachedApiKey.expiresAt) : undefined,
        lastUsedAt: cachedApiKey.lastUsedAt ? new Date(cachedApiKey.lastUsedAt) : undefined,
        createdAt: new Date(cachedApiKey.createdAt),
        updatedAt: new Date(cachedApiKey.updatedAt),
        isSecretViewed: false // 默认值
      };

      // 5. 检查API密钥状态
      if (!this.coreAuthService.isApiKeyActive(authApiKey)) {
        this.logger.warn(
          `API密钥非活跃状态: ${authInfo.apiKey}, 状态: ${authApiKey.keyStatus || 'unknown'}`,
        );
        return { isValid: false, error: 'API密钥非活跃状态' };
      }

      // 6. 从缓存中获取Secret Key
      const secretKeyFromCache = await this.authCacheService.getApiKeySecret(authApiKey.id);
      
      // 确保有Secret Key
      if (!secretKeyFromCache) {
        this.logger.warn(`缓存中不存在Secret Key: ${authInfo.apiKey}，认证失败`);
        return { isValid: false, error: '无法获取密钥信息，请联系管理员重新同步密钥' };
      }

      this.logger.debug(`从缓存获取到Secret Key: ${authInfo.apiKey}`);
      
      // 6. 重建规范化请求字符串
      const canonicalRequest = this.buildCanonicalRequest(request, authInfo.timestamp);

      // 7. 计算签名
      const calculatedSignature = this.generateSignature(canonicalRequest, secretKeyFromCache);
      
      // 8. 安全比较签名（防止时序攻击）
      const isSignatureValid = this.compareSignatures(authInfo.signature, calculatedSignature);

      this.logger.debug(`比较签名: 接收=${authInfo.signature}, 计算=${calculatedSignature}`);

      if (!isSignatureValid) {
        this.logger.warn(`签名验证失败: ${authInfo.apiKey}`);
        return { isValid: false, error: '签名验证失败' };
      }
      
      // 10. Nonce已在前面原子性设置，无需重复设置
      
      // 11. 使用已转换的API密钥数据
      const apiKeyData = authApiKey;
      
      // 12. 更新最后使用时间（记录到Redis中）
      const lastUsedKey = `api_key_last_used:${cachedApiKey.id}`;
      this.redisService.setex(lastUsedKey, 86400, JSON.stringify({
        time: new Date().toISOString(),
        ip: RequestUtils.getClientIp(request)
      })).catch(error => this.logger.error(`更新API密钥最后使用时间失败: ${error.message}`));
      
      // 13. 验证成功
      this.recordSuccess(circuitKey);
          this.logger.log(
        `签名验证成功: ${authInfo.apiKey}, 用户ID: ${cachedApiKey.userId}`,
          );
          return {
            isValid: true,
        authType: 'api-key-signature',
        apiKey: apiKeyData,
            user: { id: cachedApiKey.userId } as AuthUser,
          };
        } catch (error) {
      this.recordFailure(circuitKey);
      this.logger.error(`签名验证失败: ${error.message}`, error.stack);
      return { isValid: false, error: '签名验证过程中发生错误' };
    }
  }

/**
 * 构建规范化请求字符串
 */
private buildCanonicalRequest(request: Request, timestamp: string): string {
  const method = request.method;
  const path = request.path;
  
  this.logger.debug(`构建规范化请求字符串 - 方法: ${method}, 路径: ${path}`);
  
  // 排序查询参数
  const queryParams = { ...request.query };
  this.logger.debug(`原始查询参数: ${JSON.stringify(queryParams)}`);
  
  const sortedQuery = Object.keys(queryParams)
    .sort()
    .map(key => `${key}=${encodeURIComponent(queryParams[key] as string)}`)
    .join('&');
  
  this.logger.debug(`排序后的查询参数: ${sortedQuery}`);
  
  // 获取请求体
  let bodyString = '';
  if (request.body) {
    if (typeof request.body === 'string') {
      bodyString = request.body;
    } else if (Buffer.isBuffer(request.body)) {
      // 对于二进制数据，使用长度信息
      bodyString = `binary:${request.body.length}`;
    } else if (request.is('multipart/form-data')) {
      // 与前端保持一致的简化处理
      bodyString = 'multipart-form-data';
    } else {
      try {
        bodyString = JSON.stringify(request.body);
        // 对于GET请求，即使有空对象也应该使用空字符串（与前端保持一致）
        if (request.method === 'GET' && bodyString === '{}') {
          bodyString = '';
        }
        this.logger.debug(`JSON序列化请求体: ${bodyString}`);
      } catch (error) {
        this.logger.error(`JSON序列化失败: ${error.message}`);
        bodyString = String(request.body);
      }
    }
  }
  
  const result = [
    method.toUpperCase(),
    path,
    sortedQuery,
    bodyString,
    timestamp
  ].join('\n');
  
  this.logger.debug(`规范化请求字符串: ${result}`);
  return result;
}
  
  /**
   * 生成签名
   */
  private generateSignature(canonicalRequest: string, secretKey: string): string {
    try {
      if (!secretKey) {
        throw new Error('Secret Key不能为空');
      }
      
      this.logger.debug(`生成签名，使用密钥: ${secretKey.substring(0, 3)}...${secretKey.substring(secretKey.length - 3)}`);
      this.logger.debug(`规范化请求字符串: ${canonicalRequest}`);
      
      const hmac = crypto.createHmac('sha256', secretKey);
      hmac.update(canonicalRequest);
      const signature = hmac.digest('base64');
      
      this.logger.debug(`生成的签名: ${signature}`);
      return signature;
    } catch (error) {
      this.logger.error(`生成签名失败: ${error.message}`);
      throw error;
    }
  }
  
/**
 * 安全比较签名（防止时序攻击）
 */
private compareSignatures(sig1: string, sig2: string): boolean {
  try {
    this.logger.debug(`比较签名: 接收=${sig1}, 计算=${sig2}`);
    
    // 检查长度
    if (sig1.length !== sig2.length) {
      this.logger.warn(`签名长度不匹配: 接收=${sig1.length}, 计算=${sig2.length}`);
      return false;
    }
    
    // 使用时序安全比较
    try {
      const result = crypto.timingSafeEqual(
        Buffer.from(sig1),
        Buffer.from(sig2)
      );
      
      if (!result) {
        this.logger.warn(`签名不匹配: 接收=${sig1}, 计算=${sig2}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error(`时序安全比较失败: ${error.message}, 回退到常规比较`);
      // 如果时序安全比较失败，回退到常规比较（不理想但作为最后手段）
      const result = sig1 === sig2;
      
      if (!result) {
        this.logger.warn(`常规比较签名不匹配: 接收=${sig1}, 计算=${sig2}`);
      }
      
      return result;
    }
  } catch (error) {
    this.logger.error(`签名比较失败: ${error.message}`);
    return false;
    }
  }

  /**
   * 带熔断器的JWT令牌验证
   */
  private async validateJwtWithCircuitBreaker(
    token: string,
  ): Promise<AuthResult> {
    const circuitKey = 'jwt-validation';
    if (this.isCircuitOpen(circuitKey)) {
      this.logger.warn('JWT验证熔断器开启，跳过验证');
      return { isValid: false, error: 'Service temporarily unavailable' };
    }
    try {
      const result = await this.coreAuthService.validateJwtToken(token);
      if (result.isValid) {
        this.recordSuccess(circuitKey);
      } else {
        this.recordFailure(circuitKey);
        // 记录失败
        await this.coreAuthService.recordAuthFailure(
          result.user?.id?.toString() || 'unknown',
          result.error || 'JWT validation failed',
        );
      }
      return result;
    } catch (error) {
      this.recordFailure(circuitKey);
      this.logger.error('JWT令牌验证失败:', error);
      return { isValid: false, error: 'JWT validation failed' };
    }
  }

  /**
   * 熔断器相关方法
   */
  private isCircuitOpen(key: string): boolean {
    const circuit = this.circuitBreaker.get(key);
    if (!circuit) return false;

    if (circuit.isOpen) {
      const now = Date.now();
      if (now - circuit.lastFailure > this.CIRCUIT_BREAKER_TIMEOUT) {
        // 重置熔断器
        circuit.isOpen = false;
        circuit.failures = 0;
        this.logger.log(`熔断器 ${key} 已重置`);
        return false;
      }
      return true;
    }

    return false;
  }

  private recordFailure(key: string): void {
    const circuit = this.circuitBreaker.get(key) || {
      failures: 0,
      lastFailure: 0,
      isOpen: false,
    };
    circuit.failures++;
    circuit.lastFailure = Date.now();

    if (circuit.failures >= this.CIRCUIT_BREAKER_THRESHOLD) {
      circuit.isOpen = true;
      this.logger.warn(`熔断器 ${key} 已开启，失败次数: ${circuit.failures}`);
    }

    this.circuitBreaker.set(key, circuit);
  }

  private recordSuccess(key: string): void {
    const circuit = this.circuitBreaker.get(key);
    if (circuit) {
      circuit.failures = 0;
      circuit.isOpen = false;
    }
  }
}
