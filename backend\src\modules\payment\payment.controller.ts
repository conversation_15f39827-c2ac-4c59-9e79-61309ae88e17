import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  ParseIntPipe,
  UseGuards,
  Request,
  ForbiddenException,
  ParseEnumPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { PaymentService } from './payment.service';
import {
  CreatePaymentDto,
  PaymentCallbackDto,
  QueryPaymentDto,
  UpdatePaymentDto,
  PaymentResponseDto,
  PaymentListResponseDto,
  PaymentResultDto,
} from './dto/payment.dto';
import { PaymentMethod } from '../order/enums/order.enum';
import { UnifiedAuthGuard } from '@/common/guards/unified-auth.guard';
import { AuthPermission } from '@/common/decorators/auth-permission.decorator';
import { ApiResult } from '@/common/decorators/api-result.decorator';
import { Public } from '@/common/decorators/public.decorator';

@ApiTags('支付管理')
@Controller('payments')
@UseGuards(UnifiedAuthGuard)
@ApiBearerAuth()
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  @Post()
  @ApiOperation({ summary: '创建支付' })
  @ApiBody({ type: CreatePaymentDto })
  @ApiResponse({ status: 201, type: PaymentResultDto })
  @ApiResult()
  async createPayment(
    @Request() req,
    @Body() createPaymentDto: CreatePaymentDto,
  ): Promise<PaymentResultDto> {
    const userId = req.user?.id;
    if (!userId) {
      throw new ForbiddenException('用户未登录');
    }
    return this.paymentService.createPayment(createPaymentDto);
  }

  @Post('callback/:paymentMethod')
  @Public()
  @ApiOperation({ summary: '支付回调处理' })
  @ApiParam({ name: 'paymentMethod', enum: PaymentMethod, description: '支付方式' })
  @ApiResponse({ status: 200, description: '回调处理结果' })
  async handleCallback(
    @Param('paymentMethod', new ParseEnumPipe(PaymentMethod)) paymentMethod: PaymentMethod,
    @Body() callbackData: Record<string, any>,
  ): Promise<{ success: boolean; message: string }> {
    return this.paymentService.handleCallback(paymentMethod, callbackData);
  }

  @Get()
  @ApiOperation({ summary: '查询支付列表' })
  @ApiQuery({ type: QueryPaymentDto })
  @ApiResponse({ status: 200, type: PaymentListResponseDto })
  @ApiResult()
  async findAll(
    @Request() req,
    @Query() queryDto: QueryPaymentDto,
  ): Promise<PaymentListResponseDto> {
    // 普通用户只能查看自己的支付记录
    if (!req.user?.permissions?.includes('payment:read-all')) {
      queryDto.userId = req.user?.id;
    }
    return this.paymentService.findAll(queryDto);
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID查询支付详情' })
  @ApiParam({ name: 'id', description: '支付ID', type: Number })
  @ApiResponse({ status: 200, type: PaymentResponseDto })
  @ApiResult()
  async findOne(
    @Request() req,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<PaymentResponseDto> {
    const payment = await this.paymentService.findById(id);
    
    // 普通用户只能查看自己的支付记录
    if (!req.user?.permissions?.includes('payment:read-all') && payment.userId !== req.user?.id) {
      throw new ForbiddenException('无权查看此支付记录');
    }
    
    return payment;
  }

  @Get('payment-no/:paymentNo')
  @ApiOperation({ summary: '根据支付单号查询支付详情' })
  @ApiParam({ name: 'paymentNo', description: '支付单号', type: String })
  @ApiResponse({ status: 200, type: PaymentResponseDto })
  @ApiResult()
  async findByPaymentNo(
    @Request() req,
    @Param('paymentNo') paymentNo: string,
  ): Promise<PaymentResponseDto> {
    const payment = await this.paymentService.findByPaymentNo(paymentNo);
    
    // 普通用户只能查看自己的支付记录
    if (!req.user?.permissions?.includes('payment:read-all') && payment.userId !== req.user?.id) {
      throw new ForbiddenException('无权查看此支付记录');
    }
    
    return payment;
  }

  @Patch(':id')
  @AuthPermission('payment:update')
  @ApiOperation({ summary: '更新支付信息' })
  @ApiParam({ name: 'id', description: '支付ID', type: Number })
  @ApiBody({ type: UpdatePaymentDto })
  @ApiResponse({ status: 200, type: PaymentResponseDto })
  @ApiResult()
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePaymentDto: UpdatePaymentDto,
  ): Promise<PaymentResponseDto> {
    return this.paymentService.update(id, updatePaymentDto);
  }
}
