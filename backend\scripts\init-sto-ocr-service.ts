import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { ServiceService } from '../src/modules/service/service.service';
import { ServiceType, ServiceStatus, PricingModel } from '../src/modules/service/enums/service.enum';
import { Logger } from '@nestjs/common';

/**
 * 申通面单OCR服务初始化脚本
 * 在数据库中创建申通面单OCR服务记录
 */
async function initStoOcrService() {
  const logger = new Logger('InitStoOcrService');
  
  try {
    // 创建应用实例
    const app = await NestFactory.createApplicationContext(AppModule);
    const serviceService = app.get(ServiceService);

    // 检查服务是否已存在
    try {
      const existingService = await serviceService.findByCode('sto-ocr');
      if (existingService) {
        logger.log('申通面单OCR服务已存在，跳过创建');
        await app.close();
        return;
      }
    } catch (error) {
      // 服务不存在，继续创建
    }

    // 创建申通面单OCR服务
    const stoOcrService = await serviceService.create({
      code: 'sto-ocr',
      name: '申通面单识别',
      type: ServiceType.OCR,
      status: ServiceStatus.ACTIVE,
      description: '专门针对申通物流面单的OCR识别服务，优化识别准确率和字段提取，支持申通特有字段识别如分拣码、路由码等。相比通用OCR服务，具有更高的识别精度和更完整的字段提取能力。',
      pricingModel: PricingModel.PER_REQUEST,
      unitPrice: 0.12,
      endpoint: '/v1/op/ocr/sto',
      sortOrder: 101,
    });

    logger.log(`申通面单OCR服务创建成功: ${JSON.stringify({
      id: stoOcrService.id,
      code: stoOcrService.code,
      name: stoOcrService.name,
      unitPrice: stoOcrService.unitPrice,
    })}`);

    await app.close();
    logger.log('申通面单OCR服务初始化完成');

  } catch (error) {
    logger.error(`申通面单OCR服务初始化失败: ${error.message}`);
    process.exit(1);
  }
}

// 执行初始化
if (require.main === module) {
  initStoOcrService();
}
