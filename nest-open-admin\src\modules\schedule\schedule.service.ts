import { Injectable, Logger, Optional } from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { ServiceStatus } from '../service/dto/service.dto';
import {
  AlertType,
  AlertLevel,
} from '../alert-record/entities/alert-record.entity';
import { RedisService } from '@/shared/redis.service';

import { UserServiceService } from '../user-service/user-service.service';
import { ServiceService } from '../service/service.service';
import { AlertRecordService } from '../alert-record/alert-record.service';
import { CallRecordService } from '../call-record/call-record.service';
import { OrderService } from '../order/order.service';
import { ApiUsageTrackerService } from '../call-record/services/api-usage-tracker.service';
import { ApiKeyService } from '../api-key/api-key.service';
import dayjs from 'dayjs';

@Injectable()
export class ScheduleService {
  private readonly logger = new Logger(ScheduleService.name);
  private readonly DAILY_FREE_QUOTA = 5; // 每日免费额度
  private readonly ALERT_THRESHOLD = 0.2; // 20%预警阈值

  constructor(
    private readonly redisService: RedisService,

    private readonly userServiceService: UserServiceService,
    private readonly serviceService: ServiceService,
    private readonly alertRecordService: AlertRecordService,
    private readonly callRecordService: CallRecordService,
    private readonly apiUsageTracker: ApiUsageTrackerService,
    private readonly orderService: OrderService,
    @Optional() private readonly apiKeyService: ApiKeyService,
  ) {}

  /**
   * 检查用户是否为新用户（未有过购买或充值记录）
   * @param userId 用户ID
   * @returns 是否为新用户
   */
  private async isNewUser(userId: number): Promise<boolean> {
    try {
      // 通过订单服务检查是否有购买或充值订单记录
      const hasPurchaseOrders =
        await this.orderService.hasUserPurchaseOrRechargeOrders(userId);

      // 如果没有购买/充值订单，则认为是新用户
      return !hasPurchaseOrders;
    } catch (error) {
      this.logger.error(`检查新用户状态失败: 用户ID=${userId}`, error);
      // 出错时保守处理，认为不是新用户
      return false;
    }
  }

  /**
   * 重置每日免费额度
   * 只为新用户（未有过购买或充值记录的用户）重置免费额度
   */
  @Cron('0 0 * * *') // 每天凌晨执行
  async resetDailyFreeQuota(): Promise<void> {
    this.logger.log('开始重置每日免费额度');

    try {
      // 使用新的API使用跟踪服务重置免费额度
      await this.apiUsageTracker.resetDailyFreeQuota();
      this.logger.log('重置每日免费额度完成');
    } catch (error) {
      this.logger.error('重置每日免费额度失败', error);
    }
  }

  /**
   * 定期同步API密钥到缓存
   * 确保缓存中的API密钥数据始终是最新的
   */
  @Cron('0 */30 * * * *') // 每30分钟执行一次
  async syncApiKeysToCache(): Promise<void> {
    if (!this.apiKeyService) {
      this.logger.warn('ApiKeyService未注入，跳过API密钥同步');
      return;
    }

    this.logger.log('开始同步API密钥到缓存');
    try {
      const count = await this.apiKeyService.loadApiKeysToCache();
      this.logger.log(`API密钥同步完成，共同步 ${count} 个密钥`);
    } catch (error) {
      this.logger.error('同步API密钥到缓存失败', error);
    }
  }

  /**
   * 检查余额预警
   * 只为非新用户（有过购买或充值记录的用户）发送预警
   */
  @Cron('0 */6 * * *') // 每6小时执行一次
  async checkBalanceAlerts(): Promise<void> {
    this.logger.log('开始检查余额预警');

    try {
      let alertCount = 0;
      let processedCount = 0;
      let newUserSkippedCount = 0;

      // 获取所有启用的用户服务
      const userServicesResponse = await this.userServiceService.findAll({
        enabled: true,
      });
      const userServices = userServicesResponse.data;

      for (const userService of userServices) {
        processedCount++;

        // 检查是否为新用户，新用户跳过预警
        const isNew = await this.isNewUser(userService.userId);
        if (isNew) {
          newUserSkippedCount++;
          continue;
        }

        const totalCount = userService.totalCount || 0;
        const usedCount = userService.usedCount || 0;
        const remainingCount = totalCount - usedCount;
        const usageRatio = totalCount > 0 ? usedCount / totalCount : 0;
        const remainingRatio = totalCount > 0 ? remainingCount / totalCount : 0;

        // 检查是否需要发送预警（剩余额度低于阈值且大于0）
        if (remainingRatio <= this.ALERT_THRESHOLD && remainingRatio > 0) {
          // 检查是否已经发送过预警（24小时内）
          const recentAlerts = await this.alertRecordService.findAll({
            userId: userService.userId,
            serviceId: userService.serviceId,
            type: AlertType.LOW_BALANCE,
            startDate: dayjs().subtract(24, 'hour').toISOString(),
          });

          const hasRecentAlert = recentAlerts.data.some((alert) => {
            const alertTime = new Date(alert.createdAt);
            const twentyFourHoursAgo = new Date(
              Date.now() - 24 * 60 * 60 * 1000,
            );
            return alertTime >= twentyFourHoursAgo;
          });

          if (!hasRecentAlert) {
            // 获取服务信息
            const service = await this.serviceService.findOne(
              userService.serviceId,
            );

            // 创建预警记录
            await this.alertRecordService.create({
              userId: userService.userId,
              serviceId: userService.serviceId,
              type: AlertType.LOW_BALANCE,
              level: AlertLevel.HIGH,
              title: '余额不足预警',
              content: `您的${service.name}服务余额不足，剩余${remainingCount}次调用，建议及时充值。`,
              threshold: this.ALERT_THRESHOLD.toString(),
              currentValue: remainingRatio.toString(),
              metadata: {
                totalCount: userService.totalCount,
                usedCount: userService.usedCount,
                remainingCount: remainingCount,
                usageRatio: usageRatio,
                remainingRatio: remainingRatio,
              } as Record<string, any>,
            });

            alertCount++;

            this.logger.log(
              `发送余额预警: 用户${userService.userId}, 服务${service.name}, 剩余${remainingCount}次`,
            );
          }
        }
      }

      this.logger.log(
        `余额预警检查完成，共处理 ${processedCount} 个用户服务，跳过新用户 ${newUserSkippedCount} 个，发送预警 ${alertCount} 条`,
      );
    } catch (error) {
      this.logger.error('余额预警检查失败', error);
    }
  }

  /**
   * 清理过期数据
   */
  @Cron('0 2 * * *') // 每天凌晨2点执行
  async cleanupExpiredData(): Promise<void> {
    this.logger.log('开始清理过期数据');

    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);

      let cleanupCount = 0;

      // 清理无效的用户服务关联（总额度为0且已使用为0的记录）
      const userServicesResponse = await this.userServiceService.findAll({});
      for (const userService of userServicesResponse.data) {
        if (userService.totalCount === 0 && userService.usedCount === 0) {
          await this.userServiceService.remove(userService.id);
          cleanupCount++;
        }
      }

      // 清理30天前的预警记录
      const alertsResponse = await this.alertRecordService.findAll({});
      for (const alert of alertsResponse.data) {
        const alertDate = new Date(alert.createdAt);
        if (alertDate < thirtyDaysAgo) {
          await this.alertRecordService.remove(alert.id);
          cleanupCount++;
        }
      }

      // 清理90天前的调用记录
      const callRecordsResponse = await this.callRecordService.findAll({});
      for (const callRecord of callRecordsResponse.data) {
        const recordDate = new Date(callRecord.createdAt);
        if (recordDate < ninetyDaysAgo) {
          await this.callRecordService.remove(callRecord.id);
          cleanupCount++;
        }
      }

      // 清理过期缓存
      await this.cleanupExpiredCache();

      this.logger.log(`数据清理完成，共清理 ${cleanupCount} 条记录`);

      // 发送清理完成事件
      const cleanupCompletedEvent: any = {
        id: `data_cleanup_completed_${Date.now()}`,
        type: 'system_status' as const,
        source: 'schedule-service',
        timestamp: Date.now(),
        data: {
          id: `data_cleanup_completed_${Date.now()}`,
          component: 'data_cleanup',
          status: 'healthy',
          message: `Data cleanup completed. Cleaned ${cleanupCount} records.`,
          timestamp: Date.now(),
        },
        payload: {
          id: `data_cleanup_completed_${Date.now()}`,
          component: 'data_cleanup',
          status: 'healthy',
          timestamp: Date.now(),
        },
        metadata: {
          serviceName: 'schedule-service',
          operation: 'data_cleanup',
          timestamp: Date.now(),
        },
      };

      // 系统状态事件已记录到日志
      this.logger.log(`系统状态事件: ${JSON.stringify(cleanupCompletedEvent)}`);
    } catch (error) {
      this.logger.error('数据清理失败', error);

      // 发送清理失败事件
      const cleanupFailedEvent: any = {
        id: `data_cleanup_failed_${Date.now()}`,
        type: 'system_status' as const,
        source: 'schedule-service',
        timestamp: Date.now(),
        data: {
          id: `data_cleanup_failed_${Date.now()}`,
          component: 'data_cleanup',
          status: 'down',
          message: error.message,
          timestamp: Date.now(),
        },
        payload: {
          id: `data_cleanup_failed_${Date.now()}`,
          component: 'data_cleanup',
          status: 'down',
          timestamp: Date.now(),
        },
        metadata: {
          serviceName: 'schedule-service',
          operation: 'data_cleanup',
          timestamp: Date.now(),
        },
      };

      // 系统状态事件已记录到日志
      this.logger.error(`系统状态事件: ${JSON.stringify(cleanupFailedEvent)}`);
    }
  }

  /**
   * 清除用户额度缓存
   */
  private async clearUserQuotaCache(userIds: number[]): Promise<void> {
    try {
      const cacheKeys: string[] = [];

      for (const userId of userIds) {
        cacheKeys.push(`user_quota:${userId}:*`);
        cacheKeys.push(`daily_free:${userId}:*`);
      }

      // 使用Redis的SCAN命令查找匹配的键并删除
      for (const pattern of cacheKeys) {
        const keys = await this.redisService.keys(pattern);
        if (keys.length > 0) {
          await this.redisService.delMany(keys);
        }
      }

      this.logger.log(
        `清除用户额度缓存完成，共清除 ${userIds.length} 个用户的缓存`,
      );
    } catch (error) {
      this.logger.error('清除用户额度缓存失败', error);
    }
  }

  /**
   * 清理过期缓存
   */
  private async cleanupExpiredCache(): Promise<void> {
    try {
      const patterns = [
        'user_quota:*',
        'daily_free:*',
        'api_key:*',
        'user_info:*',
        'service_config:*',
        'billing_cache:*',
      ];

      for (const pattern of patterns) {
        const keys = await this.redisService.keys(pattern);

        for (const key of keys) {
          const ttl = await this.redisService.ttl(key);
          // 删除已过期的键（TTL为-2表示键不存在或已过期）
          if (ttl === -2) {
            await this.redisService.del(key);
          }
        }
      }
    } catch (error) {
      this.logger.error('清理过期缓存失败', error);
    }
  }

  /**
   * 手动重置免费额度
   */
  async manualResetFreeQuota(): Promise<{ success: boolean; message: string }> {
    try {
      await this.resetDailyFreeQuota();
      return { success: true, message: '手动重置免费额度成功' };
    } catch (error) {
      this.logger.error('手动重置免费额度失败', error);
      return { success: false, message: `手动重置免费额度失败: ${error.message}` };
    }
  }

  /**
   * 手动同步API密钥到缓存
   */
  async manualSyncApiKeys(): Promise<{ success: boolean; message: string; count?: number }> {
    if (!this.apiKeyService) {
      return { success: false, message: 'ApiKeyService未注入，无法同步API密钥' };
    }

    try {
      const count = await this.apiKeyService.loadApiKeysToCache();
      this.logger.log(`手动同步API密钥完成，共同步 ${count} 个密钥`);
      return { 
        success: true, 
        message: '手动同步API密钥成功', 
        count 
      };
    } catch (error) {
      this.logger.error('手动同步API密钥失败', error);
      return { 
        success: false, 
        message: `手动同步API密钥失败: ${error.message}` 
      };
    }
  }

  /**
   * 检查服务异常预警
   */
  @Cron('0 */1 * * *') // 每小时执行一次
  async checkServiceAnomalyAlerts(): Promise<void> {
    this.logger.log('开始检查服务异常预警');

    try {
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

      // 获取所有活跃服务
      const servicesResponse = await this.serviceService.findAll({
        serviceStatus: ServiceStatus.ACTIVE,
      });

      for (const service of servicesResponse.data) {
        // 获取过去1小时的调用记录
        const callRecordsResponse = await this.callRecordService.findAll({
          serviceId: service.id,
          startDate: oneHourAgo.toISOString(),
        });

        const recentCalls = callRecordsResponse.data.filter((call) => {
          const callTime = new Date(call.createdAt);
          return callTime >= oneHourAgo;
        });

        const failedCalls = recentCalls.filter((call) => !call.success);
        const errorRate =
          recentCalls.length > 0 ? failedCalls.length / recentCalls.length : 0;

        // 如果错误率超过50%且调用次数大于10次，发送预警
        if (errorRate > 0.5 && recentCalls.length > 10) {
          // 检查是否已经发送过预警（1小时内）
          const recentAlerts = await this.alertRecordService.findAll({
            serviceId: service.id,
            type: AlertType.SERVICE_ERROR,
            startDate: oneHourAgo.toISOString(),
          });

          const hasRecentAlert = recentAlerts.data.some((alert) => {
            const alertTime = new Date(alert.createdAt);
            return alertTime >= oneHourAgo;
          });

          if (!hasRecentAlert) {
            await this.alertRecordService.create({
              userId: 1, // 系统管理员
              serviceId: service.id,
              type: AlertType.SERVICE_ERROR,
              level: AlertLevel.MEDIUM,
              title: '服务异常预警',
              content: `服务${service.name}在过去1小时内错误率过高，错误率: ${(errorRate * 100).toFixed(2)}%，总调用: ${recentCalls.length}次，失败: ${failedCalls.length}次`,
              threshold: '0.5',
              currentValue: errorRate.toString(),
              metadata: {
                totalCalls: recentCalls.length,
                failedCalls: failedCalls.length,
                errorRate: errorRate,
                timeRange: '1hour',
              } as Record<string, any>,
            });

            this.logger.warn(
              `发送服务异常预警: 服务${service.name}, 错误率${(errorRate * 100).toFixed(2)}%`,
            );
          }
        }
      }

      this.logger.log('服务异常预警检查完成');
    } catch (error) {
      this.logger.error('服务异常预警检查失败', error);
    }
  }

  /**
   * 检查API调用频率异常
   */
  @Cron('*/15 * * * *') // 每15分钟执行一次
  async checkApiFrequencyAlerts(): Promise<void> {
    this.logger.log('开始检查API调用频率异常');

    try {
      const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);

      // 获取过去15分钟的调用记录
      const callRecordsResponse = await this.callRecordService.findAll({
        startDate: fifteenMinutesAgo.toISOString(),
      });

      const recentCalls = callRecordsResponse.data.filter((call) => {
        const callTime = new Date(call.createdAt);
        return callTime >= fifteenMinutesAgo;
      });

      // 统计每个用户的调用次数
      const userFrequencies = new Map<number, number>();
      recentCalls.forEach((call) => {
        const count = userFrequencies.get(call.userId) || 0;
        userFrequencies.set(call.userId, count + 1);
      });

      // 检查高频调用用户
      for (const [userId, callCount] of userFrequencies) {
        if (callCount > 1000) {
          // 15分钟内超过1000次调用
          // 检查是否已经发送过预警（1小时内）
          const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
          const recentAlerts = await this.alertRecordService.findAll({
            userId: userId,
            type: AlertType.HIGH_FREQUENCY,
            startDate: oneHourAgo.toISOString(),
          });

          const hasRecentAlert = recentAlerts.data.some((alert) => {
            const alertTime = new Date(alert.createdAt);
            return alertTime >= oneHourAgo;
          });

          if (!hasRecentAlert) {
            await this.alertRecordService.create({
              userId: userId,
              serviceId: 1, // 系统服务ID
              type: AlertType.HIGH_FREQUENCY,
              level: AlertLevel.HIGH,
              title: 'API调用频率异常预警',
              content: `用户在过去15分钟内API调用频率异常，调用次数: ${callCount}次`,
              threshold: '1000',
              currentValue: callCount.toString(),
              metadata: {
                callCount: callCount,
                timeRange: '15minutes',
                checkTime: new Date().toISOString(),
              } as Record<string, any>,
            });

            this.logger.warn(
              `发送API高频调用预警: 用户${userId}, 15分钟内调用${callCount}次`,
            );
          }
        }
      }

      this.logger.log('API调用频率异常检查完成');
    } catch (error) {
      this.logger.error('API调用频率异常检查失败', error);
    }
  }

  /**
   * 手动检查预警
   */
  async manualCheckAlerts(): Promise<{ success: boolean; message: string }> {
    try {
      await this.checkBalanceAlerts();
      await this.checkServiceAnomalyAlerts();
      await this.checkApiFrequencyAlerts();
      return {
        success: true,
        message: '手动预警检查完成',
      };
    } catch (error) {
      return {
        success: false,
        message: `手动预警检查失败: ${error.message}`,
      };
    }
  }

  /**
   * 手动触发数据清理（用于测试）
   */
  async manualCleanupData(): Promise<{ success: boolean; message: string }> {
    try {
      await this.cleanupExpiredData();
      return {
        success: true,
        message: '手动数据清理完成',
      };
    } catch (error) {
      return {
        success: false,
        message: `手动数据清理失败: ${error.message}`,
      };
    }
  }

  /**
   * 获取调度任务统计信息
   */
  async getScheduleStats(): Promise<any> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // 获取今日预警数量
      const alertsResponse = await this.alertRecordService.findAll({});
      const todayAlerts = alertsResponse.data.filter((alert) => {
        const alertDate = new Date(alert.createdAt);
        alertDate.setHours(0, 0, 0, 0);
        return alertDate.getTime() === today.getTime();
      });

      // 获取系统统计
      const userServicesResponse = await this.userServiceService.findAll({});
      const callRecordsResponse = await this.callRecordService.findAll({});
      const servicesResponse = await this.serviceService.findAll({
        serviceStatus: ServiceStatus.ACTIVE,
      });

      return {
        success: true,
        data: {
          todayStats: {
            alertCount: todayAlerts.length,
            userServiceCount: userServicesResponse.data.length,
            callRecordCount: callRecordsResponse.data.length,
          },
          systemStats: {
            activeServices: servicesResponse.data.length,
            totalAlerts: alertsResponse.data.length,
          },
          scheduleStatus: {
            freeQuotaReset: {
              enabled: true,
              schedule: '0 0 * * *',
              description: '每天凌晨重置免费额度',
            },
            balanceAlerts: {
              enabled: true,
              schedule: '0 */6 * * *',
              description: '每6小时检查余额预警',
            },
            serviceAnomalyAlerts: {
              enabled: true,
              schedule: '0 */1 * * *',
              description: '每小时检查服务异常',
            },
            apiFrequencyAlerts: {
              enabled: true,
              schedule: '*/15 * * * *',
              description: '每15分钟检查API频率异常',
            },
            dataCleanup: {
              enabled: true,
              schedule: '0 2 * * *',
              description: '每天凌晨2点清理过期数据',
            },
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        message: `获取统计信息失败: ${error.message}`,
      };
    }
  }
}
