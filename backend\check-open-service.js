const mysql = require('mysql2/promise');

async function checkOpenService() {
  const connection = await mysql.createConnection({
    host: 'localhost', 
    user: 'root', 
    password: '123456', 
    database: 'openapidb'
  });
  
  try {
    console.log('=== 查询open_service表结构 ===');
    const [columns] = await connection.execute('DESCRIBE open_service');
    columns.forEach(col => {
      console.log(`${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Key} ${col.Default || ''}`);
    });
    
    console.log('\n=== 查询所有服务信息 ===');
    const [services] = await connection.execute('SELECT * FROM open_service ORDER BY id');
    
    services.forEach(service => {
      console.log(`ID: ${service.id}, 名称: ${service.name}, 描述: ${service.description || service.desc}, 路径: ${service.path || service.api_path || 'N/A'}`);
    });
    
  } catch (error) {
    console.error('查询失败:', error.message);
  } finally {
    await connection.end();
  }
}

checkOpenService();
