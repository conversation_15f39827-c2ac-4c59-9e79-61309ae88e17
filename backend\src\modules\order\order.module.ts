import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { OrderController } from './order.controller';
import { OrderService } from './order.service';
import { RechargeService } from './services/recharge.service';
import { OrderStatusService } from './services/order-status.service';
import { OrderEntity } from './entities/order.entity';
import { OrderItemEntity } from './entities/order-item.entity';
import { OrderListener } from './listeners/order.listener';
import { UserModule } from '../user/user.module';
import { ServiceModule } from '../service/service.module';
import { UserServiceModule } from '../user-service/user-service.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      OrderEntity,
      OrderItemEntity,
    ]),
    forwardRef(() => UserModule),
    ServiceModule,
    forwardRef(() => UserServiceModule),
    EventEmitterModule.forRoot(),
  ],
  controllers: [OrderController],
  providers: [OrderService, RechargeService, OrderStatusService, OrderListener],
  exports: [OrderService, RechargeService, OrderStatusService],
})
export class OrderModule {}
