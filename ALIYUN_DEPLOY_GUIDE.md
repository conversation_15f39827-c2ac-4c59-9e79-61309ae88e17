# 阿里云服务器部署指南

本文档提供在阿里云服务器（Ubuntu 24.04.2 LTS）上部署开放平台全栈应用的详细步骤。

### 系统环境
- **操作系统**: Ubuntu 24.04.2 LTS (Noble Numbat)
- **架构**: x86_64
- **CPU**: Intel Xeon Platinum 8369B (4核)
- **虚拟化**: KVM

## 1. Docker 环境配置

### 1.1 配置阿里云镜像加速

阿里云服务器部署时，访问 Docker Hub 官方源经常受限，必须配置国内镜像加速：

```bash
# 创建或编辑 daemon.json
sudo mkdir -p /etc/docker
sudo nano /etc/docker/daemon.json
```

# 配置镜像加速
cat > /etc/docker/daemon.json <<EOF
{
  "registry-mirrors": [
    "https://la7b45hm.mirror.aliyuncs.com",
    "https://registry-vpc.cn-hangzhou.aliyuncs.com",
    "https://registry.cn-hangzhou.aliyuncs.com",
    "https://docker.m.daocloud.io",
    "https://registry.docker-cn.com",
    "https://dockerhub.azk8s.cn",
    "https://f1361db2.m.daocloud.io",
    "https://registry-1.docker.io"
  ]
}
EOF


重启 Docker 服务：

```bash
sudo systemctl daemon-reload
sudo systemctl restart docker

# 验证镜像加速器配置
docker info | grep Mirror
```

### 1.2 安装 Docker Compose V2

Ubuntu 24.04 默认有 Docker，但需要正确安装 Compose 插件：

```bash
# 添加 Docker 官方源（阿里云镜像）
sudo apt update
sudo apt install -y ca-certificates curl gnupg lsb-release
sudo install -m 0755 -d /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://mirrors.aliyun.com/docker-ce/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

一步到位方法
```bash
 # 添加 Docker 官方源（阿里云镜像，适配 Ubuntu 24.04 noble）
echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://mirrors.aliyun.com/docker-ce/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 更新 apt 源
sudo apt update

# 安装 compose 插件
sudo apt install -y docker-compose-plugin

# 验证 
docker compose version
```

# 更新源和安装 compose 插件
sudo apt update
sudo apt install -y docker-compose-plugin

# 验证安装
docker compose version

注意：安装后使用 `docker compose`（无横线）命令，不再使用 `docker-compose`。

## 2. 清理 Docker 环境

在开始部署前，清理 Docker 环境，确保没有旧的容器、镜像和卷影响新部署：

```bash
# 确保在项目根目录
cd /home/<USER>/open-backend-frontend

# 停止所有容器并删除卷、孤立容器
docker compose down -v --remove-orphans

# 删除所有未使用的数据（包括构建缓存）
docker system prune -a --volumes -f

# 重新构建所有服务
docker compose build --no-cache

# 启动所有服务
docker compose up -d
```

## 3. 构建并启动服务

### 3.1 Python 服务

```bash
# 确保在项目根目录
cd /home/<USER>/open-backend-frontend

# 仅构建并启动 Python 服务和它的依赖（Redis）
docker compose build --no-cache redis python-service
docker compose up -d redis python-service

docker compose down
docker compose build --no-cache python-service
docker compose up -d

# 查看容器状态
docker compose ps

# 查看 Python 服务日志
docker compose logs -f python-service
```

### 3.2 NestJS 后端服务

```bash
# 构建并启动 NestJS 后端
docker compose stop python-service
docker compose rm -f python-service

docker compose build --no-cache nest-backend
docker compose up -d nest-backend

docker compose build --no-cache python-service && docker compose up -d python-service

# 查看容器状态
docker compose ps

# 查看 NestJS 后端日志
docker compose logs -f nest-backend
docker compose logs -f python-service
```

#### 1. 查看服务器资源使用情况
```bash
# 查看内存使用情况
free -h

# 查看CPU使用情况
top -n 1

# 查看系统负载
uptime

# 查看磁盘使用情况
df -h

# 查看Docker资源使用情况
docker system df
```
##### 清理Docker镜像和缓存
```bash
# 查看Docker系统使用情况
docker system df

# 清理未使用的容器、网络、镜像（保留卷）
docker system prune -f

# 清理所有未使用的Docker数据（包括镜像、缓存、卷）
docker system prune -a --volumes -f

# 仅清理未使用的镜像
docker image prune -a -f

# 清理构建缓存
docker builder prune -f
```

## 4. Nginx 配置

### 4.1 安装 Nginx

```bash
# 更新包列表
sudo apt update

# 安装 Nginx
sudo apt install -y nginx

# 检查 Nginx 状态
sudo systemctl status nginx
```

### 4.2 创建 Nginx 配置文件

```bash
# 创建站点配置文件
sudo nano /etc/nginx/sites-available/api.aiszyl.cn.conf
```

粘贴以下配置：

```nginx
server {
    listen 80;
    server_name api.aiszyl.cn;

    # 访问日志
    access_log /var/log/nginx/api.aiszyl.cn.access.log;
    error_log /var/log/nginx/api.aiszyl.cn.error.log;

    # 反向代理到NestJS服务
    location / {
        proxy_pass http://localhost:8088;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # CORS配置
        add_header 'Access-Control-Allow-Origin' '$http_origin' always;  # 动态设置为请求的Origin
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, PATCH, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-API-KEY,X-SECRET-KEY,X-Request-ID' always;
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;
        
        # 预检请求处理
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '$http_origin';
            add_header 'Access-Control-Allow-Credentials' 'true';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, PATCH, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-API-KEY,X-SECRET-KEY,X-Request-ID';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }

    # 禁止直接访问Python服务
    location /python/ {
        deny all;
        return 403;
    }
} 
```

### 4.3 启用配置并测试

```bash
# 创建符号链接，启用站点配置
sudo ln -s /etc/nginx/sites-available/api.aiszyl.cn.conf /etc/nginx/sites-enabled/

# 测试 Nginx 配置是否有语法错误
sudo nginx -t

# 重新加载 Nginx 配置
sudo systemctl reload nginx

# 检查Nginx状态
sudo systemctl status nginx
```

### 4.4 配置防火墙

```bash
# 安装 ufw (如果尚未安装)
sudo apt install -y ufw

# 允许 SSH、HTTP、HTTPS
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https

# 拒绝 8866 端口的外部访问
sudo ufw deny 8866

# 允许 8088 端口（可选，因为已通过 Nginx 代理）
sudo ufw allow 8088

# 启用防火墙
sudo ufw enable

# 查看防火墙状态
sudo ufw status
```

### 4.5 设置 HTTPS（强烈建议）

```bash
# 安装 Certbot
sudo apt install -y certbot python3-certbot-nginx

# 获取并安装证书
sudo certbot --nginx -d api.aiszyl.cn

# 按照提示完成配置（证书会自动续期）
```

## 5. 常见问题和解决方案

### 5.1 Docker 相关问题

#### 镜像拉取失败

症状：构建时无法拉取基础镜像或依赖

解决方案：
- 检查 `/etc/docker/daemon.json` 中的镜像加速器配置
- 使用 `docker pull` 手动拉取镜像测试
- 在 Dockerfile 中显式指定阿里云镜像源

```dockerfile
# 在 Dockerfile 中添加阿里云镜像源
RUN npm config set registry https://registry.npmmirror.com
# 或者 Python 项目
RUN pip install -i https://mirrors.aliyun.com/pypi/simple/ --upgrade pip
```

#### 容器启动失败

症状：容器退出或显示 `Exit 1` 状态

解决方案：
- 查看容器日志：`docker compose logs -f 服务名`
- 进入容器排查：`docker compose exec 服务名 /bin/bash` 或 `docker compose exec 服务名 /bin/sh`
- 检查卷挂载、环境变量配置

### 5.2 数据库初始化问题

NestJS 项目首次部署时需要初始化数据库，有两种方法：

#### 方法1：使用 TypeORM 自动同步（推荐初次部署使用）

1. 临时修改配置启用自动同步：

```bash
# 编辑配置文件
nano nest-open-admin/src/config/prod.yml
```

修改 `synchronize` 为 `true`:

```yaml
database:
  # ...其他配置保持不变
  synchronize: true  # 临时改为 true 让它自动创建表结构
```

2. 重新构建并启动服务：

```bash
# 停止现有服务
docker compose down

# 重新构建并启动所有服务
docker compose build --no-cache
docker compose up -d

# 查看日志，确认表创建成功
docker compose logs -f nest-backend
```

3. 确认数据库初始化完成后，立即将 `synchronize` 改回 `false`：

```bash
# 编辑配置文件
nano nest-open-admin/src/config/prod.yml

# 将 synchronize 改回 false
database:
  # ...其他配置保持不变
  synchronize: false  # 生产环境必须为 false

# 重启服务
docker compose down
docker compose up -d
```

#### 方法2：手动执行迁移和种子命令

如果服务已经成功启动，可以进入容器执行命令：

```bash
# 进入运行中的 NestJS 容器
docker compose exec nest-backend /bin/bash

# 在容器内执行迁移和种子命令
npm run migration:run
npm run seed:run
```

如果容器持续重启无法进入，可以使用临时容器执行命令：

```bash
# 停止 NestJS 服务，保留数据库和其他服务
docker compose stop nest-backend

# 创建临时容器执行迁移命令
docker run -it --rm \
  --network open-backend-frontend_open-platform-network \
  -e NODE_ENV=production \
  -e DB_HOST=mysql \
  -e DB_PORT=3306 \
  -e DB_USERNAME=root \
  -e DB_PASSWORD=4iF6TSENcSaH8pSz \
  -e DB_DATABASE=openapidb \
  open-backend-frontend-nest-backend \
  sh -c "cd /app && npx typeorm-ts-node-commonjs migration:run -d dist/database/data-source.js"

# 创建种子数据
docker run -it --rm \
  --network open-backend-frontend_open-platform-network \
  -e NODE_ENV=production \
  -e DB_HOST=mysql \
  -e DB_PORT=3306 \
  -e DB_USERNAME=root \
  -e DB_PASSWORD=4iF6TSENcSaH8pSz \
  -e DB_DATABASE=openapidb \
  open-backend-frontend-nest-backend \
  sh -c "cd /app && npm run seed:run"

# 重新启动 NestJS 服务
docker compose start nest-backend
```

如果还是不行使用手工操作。
```bash
# 在本地开发环境
# 1. 确保数据库已初始化
# 2. 运行种子脚本
npm run seed:run

# 3. 导出数据库
mysqldump -u root -p your_local_db > db_dump.sql

# 4. 将 SQL 文件复制到服务器
scp db_dump.sql root@your_server_ip:/tmp/

# 在服务器上
# 1. 确保您在项目根目录
cd /path/to/open-backend-frontend

# 2. 导入数据库 (假设SQL文件在nest-open-admin/db_dump.sql)
docker compose exec -T mysql mysql -uroot -p4iF6TSENcSaH8pSz openapidb < nest-open-admin/db_dump.sql

# 3. 如果数据库容器还未启动，先启动MySQL
docker compose up -d mysql

# 4. 等待MySQL完全启动后再导入
sleep 30
docker compose exec -T mysql mysql -uroot -p4iF6TSENcSaH8pSz openapidb < nest-open-admin/db_dump.sql

# 5. 启动所有服务
docker compose up -d --build

# 6. 重启服务
docker compose restart nest-backend

最终成功步骤：

# 在MySQL容器中执行
mysql -uroot -p4iF6TSENcSaH8pSz

然后在MySQL提示符下执行以下命令：

-- 查看表结构
USE openapidb;
SHOW TABLES;
SHOW CREATE TABLE open_service;
SHOW CREATE TABLE open_api_key;
SHOW CREATE TABLE user_service;

根据错误信息，我们需要调整插入语句。让我们按正确的顺序创建数据：

-- 1. 插入用户
USE openapidb;

INSERT INTO open_user (id, createdAt, updatedAt, username, email, password, nickname, role, userStatus, emailVerified, balance, gender, userType, verificationStatus) 
VALUES (1, NOW(), NOW(), 'admin', '<EMAIL>', '$2b$10$aM3ndwyoBXd2wqRKwxC9V.x5u5ATShIfGLruFtYGwa1cG.3qkObc2', '系统管理员', 'admin', 'active', 1, 10000.00, 'unknown', 'organization', 'enterprise_verified');

INSERT INTO open_user (id, createdAt, updatedAt, username, email, password, nickname, role, userStatus, emailVerified, balance, gender, userType, verificationStatus)
VALUES (2, NOW(), NOW(), 'testuser', '<EMAIL>', '$2b$10$urQy9qW1FUWK4c7VcaWxXenmCU4yjQIYNlG1MOArrFP4mUbUDcnBO', '测试用户', 'user', 'active', 1, 100.00, 'male', 'individual', 'personal_verified');

-- 2. 插入服务（注意user_id字段）
INSERT INTO open_service (id, createdAt, updatedAt, user_id, service_id, code, name, description, type, serviceStatus, pricingModel, price)
VALUES 
(1, NOW(), NOW(), 1, 0, 'OCR_EXPRESS', '物流面单OCR识别', '智能识别物流面单信息', 'OCR', 'active', 'pay_per_use', 0.05),
(2, NOW(), NOW(), 1, 0, 'ADDRESS_EXTRACT', '物流文本精确提取省市区详细地址', '从文本中提取地址信息', 'NLP', 'active', 'pay_per_use', 0.05),
(3, NOW(), NOW(), 1, 0, 'GEO_REVERSE', '地理坐标逆解析出地址', '根据坐标解析地址', 'DATA', 'active', 'pay_per_use', 0.05);

-- 3. 插入API密钥
INSERT INTO open_api_key (id, createdAt, updatedAt, user_id, service_id, name, apiKey, secretHash, keyType, `key-status`)
VALUES 
(1, NOW(), NOW(), 1, 0, '管理员主密钥', 'ak_admin_test_key_12345678901234567890', '$2b$10$v.H1mtY02.SVl.AikxONheU1fA/Y5jN1Pt/njXLXcoxwsVXG8fgpi', 'enterprise', 'active'),
(2, NOW(), NOW(), 2, 0, '测试用户主密钥', 'ak_a1fb793ecce140d3851dbae3e5568dbf', '$2b$10$7r1KdenSaBe5PgDvnMV2pegrh9ITEIETHpdzvd.5U3dnPullA/MqC', 'basic', 'active');

-- 4. 插入用户服务关联
INSERT INTO user_service (id, createdAt, updatedAt, total_count, used_count, free_used_today, userId, serviceId)
VALUES 
(1, NOW(), NOW(), 999999999, 0, 0, 1, 1),
(2, NOW(), NOW(), 999999999, 0, 0, 1, 2),
(3, NOW(), NOW(), 999999999, 0, 0, 1, 3),
(4, NOW(), NOW(), 1000, 0, 0, 2, 1),
(5, NOW(), NOW(), 1000, 0, 0, 2, 2),
(6, NOW(), NOW(), 1000, 0, 0, 2, 3);


```
如果导入过程中遇到权限问题，可以尝试以下命令
```bash

# 确保SQL文件有正确的权限
chmod 644 nest-open-admin/db_dump.sql

# 使用cat命令导入
cat nest-open-admin/db_dump.sql | docker compose exec -T mysql mysql -uroot -p4iF6TSENcSaH8pSz openapidb

# 查看所有容器状态
docker compose ps

# 检查后端日志，确认是否正常启动
docker compose logs -f nest-backend
```

### 5.3 网络问题

症状：服务间无法通信

解决方案：
- 确保所有服务在同一 Docker 网络中
- 在容器内测试连通性：`ping mysql` 或 `ping redis`
- 检查 Docker DNS 解析：`docker compose exec nest-backend nslookup mysql`

## 6. 阿里云特有配置

### 6.1 阿里云安全组设置

登录阿里云控制台，为实例配置以下入站规则：
- 允许 TCP 22 端口 (SSH)
- 允许 TCP 80 端口 (HTTP)
- 允许 TCP 443 端口 (HTTPS)
- 如需直接访问后端，添加 TCP 8088 端口
- 阻止 TCP 8866 端口（Python 服务仅内部使用）

### 6.2 为特定项目创建 docker-compose-aliyun.yml

对于阿里云特定环境，可创建专用配置文件：

```yaml
version: '3.8'

services:
  # MySQL 服务
  mysql:
    image: registry.cn-hangzhou.aliyuncs.com/mysql/mysql:5.7
    # 其他配置与原文件相同
    
  # Redis 服务
  redis:
    image: registry.cn-hangzhou.aliyuncs.com/redis/redis:7.0
    # 其他配置与原文件相同
    
  # Python 服务
  python-service:
    build: 
      context: ./python-service
      args:
        - PIP_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
    # 仅监听内部网络
    ports:
      - "127.0.0.1:8866:8866"
    # 其他配置与原文件相同
    
  # NestJS 后端服务
  nest-backend:
    build:
      context: ./nest-open-admin
      args:
        - NPM_REGISTRY=https://registry.npmmirror.com
    # 其他配置与原文件相同
```

使用专用配置文件启动：

```bash
docker compose -f docker-compose-aliyun.yml up -d
```

## 7. 监控与维护

### 7.1 日志监控

```bash
# 安装日志监控工具
sudo apt install -y logwatch

# 设置每日日志摘要
sudo logwatch --output mail --mailto <EMAIL> --detail high
```

### 7.2 定期更新

```bash
# 创建定期更新脚本
sudo nano /usr/local/bin/update-server.sh
```

脚本内容：

```bash
#!/bin/bash
sudo apt update
sudo apt upgrade -y
sudo apt autoremove -y
sudo systemctl restart docker
sudo systemctl restart nginx
```

设置执行权限并添加到 crontab：

```bash
sudo chmod +x /usr/local/bin/update-server.sh
echo "0 3 * * 0 /usr/local/bin/update-server.sh" | sudo tee -a /var/spool/cron/crontabs/root
```

## 8. 测试部署

最终测试清单：

- [ ] NestJS 后端通过 http://api.aiszyl.cn 可访问
- [ ] Python 服务外部无法直接访问
- [ ] NestJS 后端可成功调用 Python 服务
- [ ] 数据库迁移和种子数据已正确初始化
- [ ] HTTPS 证书已正确安装并生效
- [ ] CORS 配置正常，前端可正常调用 API 

## 9. 部署后验证检查清单

完成部署后，请按照以下检查清单验证各服务是否正常运行：

### 9.1 服务状态检查

```bash
# 检查所有容器运行状态
docker compose ps

# 确认所有容器状态为"Up"或"Up (healthy)"
```

### 9.2 日志检查

```bash
# 检查 NestJS 后端日志
docker compose logs -f nest-backend

# 检查 Python 服务日志
docker compose logs -f python-service
```

### 9.3 数据库初始化验证

```bash
# 进入 MySQL 容器
docker compose exec mysql mysql -uroot -p4iF6TSENcSaH8pSz

# 在 MySQL 控制台内执行
use openapidb;
show tables;
select * from open_user limit 5;
select * from open_api_key limit 5;
select * from user_service limit 5;

- 方案1: SQL文件导出导入

导出SQL文件:这个命令会导出表数据但不包含表结构，并使用完整INSERT语句格式，便于查看和修改。

   mysqldump -u root -p --no-create-info --skip-triggers --complete-insert --skip-extended-insert --default-character-set=utf8mb4 openapidb open_user open_api_key open_service user_service > seed_data_utf8.sql
  
   mysqldump -u root -p123456 --no-create-info --skip-triggers --complete-insert --skip-extended-insert openapidb open_user open_api_key open_service user_service > seed_data.sql

导入SQL文件:
   mysql -u用户名 -p密码 openapidb < seed_data.sql

指定utf8mb4编码导入
  mysql -u用户名 -p密码 --default-character-set=utf8mb4 openapidb < seed_data_utf8.sql

线上环境（已有表结构）
使用仅数据版SQL文件：
mysql -u用户名 -p密码 --default-character-set=utf8mb4 数据库名 < ./nest-open-admin/seed_data_only.sql

全新环境（无表结构）
使用完整版SQL文件：
mysql -u用户名 -p密码 --default-character-set=utf8mb4 数据库名 < seed_data_complete.sql

验证导入结果
导入后，可以使用以下命令验证数据是否正确导入：
mysql -u用户名 -p密码 数据库名 -e "SELECT COUNT(*) FROM open_user; SELECT COUNT(*) FROM open_service; SELECT COUNT(*) FROM open_api_key; SELECT COUNT(*) FROM user_service;"

# 验证表是否存在且包含数据
```
##### 方案2: SQL语句文档
```bash
-- 设置字符集和外键检查
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS=0;

-- 用户表
DROP TABLE IF EXISTS `open_user`;
CREATE TABLE `open_user` (
  `id` int NOT NULL AUTO_INCREMENT,
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` datetime(6) DEFAULT NULL,
  `createdBy` varchar(255) DEFAULT NULL,
  `updatedBy` varchar(255) DEFAULT NULL,
  `baseStatus` tinyint NOT NULL DEFAULT '1',
  `del_flag` char(1) NOT NULL DEFAULT '0',
  `remark` varchar(500) DEFAULT NULL,
  `username` varchar(100) NOT NULL COMMENT '用户名',
  `email` varchar(200) DEFAULT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码哈希',
  `nickname` varchar(30) NOT NULL COMMENT '用户昵称',
  `realName` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `bio` text COMMENT '个人简介',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `gender` enum('male','female','unknown') NOT NULL DEFAULT 'unknown' COMMENT '性别',
  `userType` enum('individual','enterprise','organization') NOT NULL DEFAULT 'individual' COMMENT '用户类型',
  `verificationStatus` enum('unverified','personal_verified','enterprise_verified') NOT NULL DEFAULT 'unverified' COMMENT '认证状态',
  `role` enum('user','admin','super') NOT NULL DEFAULT 'user' COMMENT '角色',
  `openid` varchar(255) DEFAULT NULL,
  `userStatus` enum('active','suspended','banned','pending','locked') NOT NULL DEFAULT 'active' COMMENT '账户状态：active-活跃，suspended-暂停，banned-封禁，pending-待审核，locked-锁定',
  `emailVerified` tinyint NOT NULL DEFAULT '0' COMMENT '邮箱是否验证：false-未验证，true-已验证',
  `phoneVerified` tinyint NOT NULL DEFAULT '0' COMMENT '手机是否验证：false-未验证，true-已验证',
  `lastLoginAt` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `lastLoginIp` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `loginFailCount` int NOT NULL DEFAULT '0' COMMENT '登录失败次数',
  `lockedUntil` timestamp NULL DEFAULT NULL COMMENT '账户锁定截止时间',
  `preferences` text COMMENT '用户偏好设置',
  `settings` text COMMENT '用户设置',
  `lastDailyResetDate` timestamp NULL DEFAULT NULL COMMENT '每日免费使用次数重置日期',
  `tier` enum('basic','premium','enterprise') NOT NULL DEFAULT 'basic' COMMENT '用户等级：basic-基础，premium-高级，enterprise-企业',
  `remainingUsage` int NOT NULL DEFAULT '0' COMMENT '剩余使用次数',
  `totalUsage` int NOT NULL DEFAULT '0' COMMENT '总使用次数',
  `dailyFreeUsageRemaining` int NOT NULL DEFAULT '0' COMMENT '每日免费使用次数剩余',
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '账户余额',
  `isFreeQuotaEligible` tinyint NOT NULL DEFAULT '1' COMMENT '是否有资格获取免费额度',
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_open_user_username` (`username`),
  UNIQUE KEY `IDX_open_user_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户信息表';

-- API密钥表
DROP TABLE IF EXISTS `open_api_key`;
CREATE TABLE `open_api_key` (
  `id` int NOT NULL AUTO_INCREMENT,
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` datetime(6) DEFAULT NULL,
  `createdBy` varchar(255) DEFAULT NULL,
  `updatedBy` varchar(255) DEFAULT NULL,
  `baseStatus` tinyint NOT NULL DEFAULT '1',
  `del_flag` char(1) NOT NULL DEFAULT '0',
  `remark` varchar(500) DEFAULT NULL,
  `user_id` int NOT NULL COMMENT '用户ID',
  `service_id` int NOT NULL COMMENT '服务ID',
  `name` varchar(100) NOT NULL COMMENT '密钥名称',
  `apiKey` varchar(64) NOT NULL COMMENT 'API密钥（明文）',
  `secretHash` varchar(64) NOT NULL COMMENT 'API密钥秘钥哈希',
  `keyType` enum('trial','basic','premium','enterprise') NOT NULL DEFAULT 'trial' COMMENT '密钥类型',
  `key-status` enum('active','inactive','expired','revoked') NOT NULL DEFAULT 'active' COMMENT '密钥状态',
  `description` text COMMENT '密钥描述',
  `permissions` text COMMENT '权限范围',
  `expiresAt` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `lastUsedAt` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `is_secret_viewed` tinyint NOT NULL DEFAULT '0' COMMENT '密钥是否已被查看过（用于仅显示一次）',
  `lastUsedIp` varchar(45) DEFAULT NULL COMMENT '最后使用IP',
  `tempSecretKey` varchar(128) DEFAULT NULL COMMENT '临时明文密钥，仅未查看时保存',
  `userId` int DEFAULT NULL,
  `serviceId` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_open_api_key_apiKey` (`apiKey`),
  UNIQUE KEY `IDX_open_api_key_secretHash` (`secretHash`),
  KEY `IDX_open_api_key_userId` (`user_id`),
  KEY `IDX_open_api_key_keyStatus` (`key-status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='API密钥表';

-- 服务表
DROP TABLE IF EXISTS `open_service`;
CREATE TABLE `open_service` (
  `id` int NOT NULL AUTO_INCREMENT,
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` datetime(6) DEFAULT NULL,
  `createdBy` varchar(255) DEFAULT NULL,
  `updatedBy` varchar(255) DEFAULT NULL,
  `baseStatus` tinyint NOT NULL DEFAULT '1',
  `del_flag` char(1) NOT NULL DEFAULT '0',
  `remark` varchar(500) DEFAULT NULL,
  `user_id` int NOT NULL COMMENT '用户ID',
  `service_id` int NOT NULL COMMENT '服务ID',
  `code` varchar(100) NOT NULL COMMENT '服务代码',
  `name` varchar(200) NOT NULL COMMENT '服务名称',
  `description` text COMMENT '服务描述',
  `type` enum('OCR','NLP','CV','AI','DATA','OTHER') NOT NULL COMMENT '服务类型：OCR、NLP、CV、AI、DATA、OTHER等',
  `serviceStatus` enum('active','inactive','maintenance','deprecated') NOT NULL COMMENT '服务状态枚举：active、inactive、maintenance、deprecated等',
  `pricingModel` enum('free','pay_per_use','subscription','tiered') NOT NULL COMMENT '定价模式枚举：free、pay_per_use、subscription、tiered等',
  `currentVersion` varchar(20) NOT NULL DEFAULT 'v1.0.0' COMMENT '当前版本',
  `features` varchar(200) NOT NULL DEFAULT '' COMMENT '服务特性',
  `endpoint` varchar(500) DEFAULT NULL COMMENT '服务端点URL',
  `config` text COMMENT '服务配置参数',
  `callCount` int NOT NULL DEFAULT '0' COMMENT '调用次数',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '服务费用（每次调用）',
  `dailyLimit` int NOT NULL DEFAULT '1000' COMMENT '每日调用限制',
  `minuteLimit` int NOT NULL DEFAULT '100' COMMENT '每分钟调用限制',
  `requireAuth` tinyint NOT NULL DEFAULT '1' COMMENT '是否需要认证：0-否，1-是',
  `isAsync` tinyint NOT NULL DEFAULT '0' COMMENT '是否异步处理：0-否，1-是',
  `timeout` int NOT NULL DEFAULT '30' COMMENT '超时时间（秒）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_open_service_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='API服务表';

-- 用户服务表
DROP TABLE IF EXISTS `user_service`;
CREATE TABLE `user_service` (
  `id` int NOT NULL AUTO_INCREMENT,
  `createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  `deletedAt` datetime(6) DEFAULT NULL,
  `createdBy` varchar(255) DEFAULT NULL,
  `updatedBy` varchar(255) DEFAULT NULL,
  `baseStatus` tinyint NOT NULL DEFAULT '1',
  `del_flag` char(1) NOT NULL DEFAULT '0',
  `remark` varchar(500) DEFAULT NULL,
  `total_count` int NOT NULL DEFAULT '0' COMMENT '总调用次数',
  `used_count` int NOT NULL DEFAULT '0' COMMENT '已用次数',
  `free_used_today` int NOT NULL DEFAULT '0' COMMENT '今日已用免费次数',
  `last_reset_date` date DEFAULT NULL COMMENT '上次免费额度重置日期',
  `alert_sent` tinyint NOT NULL DEFAULT '0' COMMENT '是否已发送预警',
  `userId` int DEFAULT NULL,
  `serviceId` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户服务表';

-- 管理员用户
INSERT INTO open_user (username, email, nickname, realName, password, role, userStatus, userType, verificationStatus, tier, emailVerified, phoneVerified, bio, gender, loginFailCount, remainingUsage, totalUsage, dailyFreeUsageRemaining, balance, preferences, settings) 
VALUES ('admin', '<EMAIL>', '系统管理员', '系统管理员', '$2b$10$PTWGL.f4i2yqZmpOA8w/bOrGkuq/9mQvmV.GBFFQ40VOV9PSUJsHq', 'admin', 'active', 'organization', 'enterprise_verified', 'enterprise', 1, 0, '系统默认管理员账户', 'unknown', 0, 999999999, 0, 999999, 10000.00, '{"language":"zh-CN","theme":"light","timezone":"Asia/Shanghai"}', '{"notifications":{"email":true,"sms":false,"push":true},"security":{"twoFactorAuth":false,"loginNotification":true}}');

-- 测试用户
INSERT INTO open_user (username, email, nickname, realName, password, role, userStatus, userType, verificationStatus, tier, emailVerified, phoneVerified, phone, bio, gender, loginFailCount, remainingUsage, totalUsage, dailyFreeUsageRemaining, balance, preferences, settings) 
VALUES ('testuser', '<EMAIL>', '测试用户', '张测试', '$2b$10$aQitxudmoAhCmPDw4FFOfuUU0UUqdXCpJ061ZcLsP7.udfMbzV52W', 'user', 'active', 'individual', 'personal_verified', 'basic', 1, 1, '13800138000', '测试账户，用于演示和测试', 'male', 0, 1000, 0, 100, 100.00, '{"language":"zh-CN","theme":"light","timezone":"Asia/Shanghai"}', '{"notifications":{"email":true,"sms":true,"push":true},"security":{"twoFactorAuth":false,"loginNotification":true}}');

-- 管理员API密钥
INSERT INTO open_api_key (user_id, service_id, name, apiKey, secretHash, keyType, `key-status`, description, permissions, is_secret_viewed) 
VALUES (1, 0, '管理员主密钥', 'ak_admin_test_key_12345678901234567890', '$2b$10$lbTUrWLVR9hgwKJyos.1S.z2nesGr3CMTnyR5iLaYhcLf9t8Zh92u', 'enterprise', 'active', '管理员全局访问密钥，可以访问所有服务', '["*:*:*"]', 1);

-- 测试用户API密钥
INSERT INTO open_api_key (user_id, service_id, name, apiKey, secretHash, keyType, `key-status`, description, permissions, is_secret_viewed) 
VALUES (2, 0, '测试用户主密钥', 'ak_a1fb793ecce140d3851dbae3e5568dbf', '$2b$10$xw/QOVu4jVgYyBggTWBh.O708oUnJGUqtCE96Y3FF7UsKlk82rH6O', 'basic', 'active', '测试用户访问密钥，有调用次数限制', '["service:OCR_EXPRESS:*","service:ADDRESS_EXTRACT:*","service:GEO_REVERSE:*"]', 1);

-- 服务数据
INSERT INTO open_service (user_id, service_id, code, name, description, type, serviceStatus, pricingModel, currentVersion, features, endpoint, config, callCount, price, dailyLimit, minuteLimit, requireAuth, isAsync, timeout) 
VALUES (1, 0, 'OCR_EXPRESS', '物流面单OCR识别', '智能识别物流面单信息，提取收发件人、地址、电话等关键信息，支持多种面单格式，准确率高达95%以上。适用于物流企业、电商平台等自动化数据录入场景。', 'OCR', 'active', 'pay_per_use', 'v1.0.0', '高精度识别,多格式支持,实时处理,结构化输出,批量处理', '/op/ocr/upload', '{"supportedFormats":["jpg","jpeg","png","pdf","bmp","tiff"],"maxFileSize":"10MB","accuracy":0.95,"supportedCouriers":["顺丰","圆通","中通","申通","韵达","百世","邮政","京东","天天","德邦"],"outputFields":["sender_name","sender_phone","sender_address","receiver_name","receiver_phone","receiver_address","tracking_number","courier_name","weight","package_count"],"batchLimit":10,"preProcessing":{"autoRotate":true,"enhanceImage":true,"cropToContent":true},"returnOriginalImage":false,"accessMethods":[{"type":"file_upload","endpoint":"/op/ocr/upload","method":"POST","contentType":"multipart/form-data","maxFileSize":"5MB","supportedFormats":["jpg","jpeg","png","gif","bmp","webp"]},{"type":"base64","endpoint":"/op/ocr/recognize","method":"POST","contentType":"application/json","maxBase64Length":7000000}]}', 0, 0.05, 10000, 100, 1, 0, 30);

INSERT INTO open_service (user_id, service_id, code, name, description, type, serviceStatus, pricingModel, currentVersion, features, endpoint, config, callCount, price, dailyLimit, minuteLimit, requireAuth, isAsync, timeout) 
VALUES (1, 0, 'ADDRESS_EXTRACT', '物流文本精确提取省市区详细地址', '从文本中精确提取省市区详细地址信息，支持非结构化文本解析，可处理各种格式的地址信息，并进行标准化输出，支持省市区到街道社区的精确识别。', 'NLP', 'active', 'pay_per_use', 'v1.0.0', '智能分词,地址标准化,高准确率,支持模糊地址,批量处理,多维度纠错', '/op/address/extract', '{"supportedRegions":["全国省市区县"],"outputFormat":"standard","accuracy":0.98,"maxTextLength":2000,"batchLimit":50,"supportedOutputFields":{"province":true,"city":true,"district":true,"street":true,"community":true,"building":true,"room":true,"detail":true,"postalCode":true,"formatted":true},"addressNormalization":true,"fuzzyMatching":true,"confidenceScoreThreshold":0.7,"returnMultipleCandidates":true,"candidateLimit":3}', 0, 0.05, 20000, 200, 1, 0, 15);

INSERT INTO open_service (user_id, service_id, code, name, description, type, serviceStatus, pricingModel, currentVersion, features, endpoint, config, callCount, price, dailyLimit, minuteLimit, requireAuth, isAsync, timeout) 
VALUES (1, 0, 'GEO_REVERSE', '地理坐标逆解析出地址', '根据经纬度坐标逆向解析出详细地址信息，支持多种坐标系，可输出丰富的POI信息，精确到建筑物级别。适用于物流配送、位置服务、用户轨迹分析等场景。', 'DATA', 'active', 'pay_per_use', 'v1.0.0', '高精度定位,实时解析,多坐标系支持,POI识别,跨平台兼容,批量处理', '/op/address/rev-geo', '{"supportedCoordSystems":["WGS84","GCJ02","BD09"],"precision":"building","language":["zh-CN","en-US"],"radius":50,"batchLimit":100,"includePoiInfo":true,"maxPois":10,"poiTypes":["餐饮","住宿","购物","交通","教育","医疗","金融","休闲娱乐","旅游景点","商务办公"],"includeRoadInfo":true,"includeAddressComponents":true,"includeBusinessAreas":true,"formatOptions":{"formatted":true,"short":true,"long":true},"distanceCalculation":true}', 0, 0.05, 50000, 500, 1, 0, 10);

-- 用户服务关联
-- 管理员用户服务关联
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (1, 1, 999999999, 0, 0, NOW(), 0);
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (1, 2, 999999999, 0, 0, NOW(), 0);
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (1, 3, 999999999, 0, 0, NOW(), 0);

-- 测试用户服务关联
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (2, 1, 1000, 0, 0, NOW(), 0);
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (2, 2, 1000, 0, 0, NOW(), 0);
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (2, 3, 1000, 0, 0, NOW(), 0);

SET FOREIGN_KEY_CHECKS=1;


方案3.2：仅数据SQL语句（适用于表结构已存在的情况）
如果数据库表结构已经存在，只需要导入数据，可以使用以下SQL语句：

-- 设置字符集和外键检查
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS=0;

-- 管理员用户
INSERT INTO open_user (username, email, nickname, realName, password, role, userStatus, userType, verificationStatus, tier, emailVerified, phoneVerified, bio, gender, loginFailCount, remainingUsage, totalUsage, dailyFreeUsageRemaining, balance, preferences, settings) 
VALUES ('admin', '<EMAIL>', '系统管理员', '系统管理员', '$2b$10$PTWGL.f4i2yqZmpOA8w/bOrGkuq/9mQvmV.GBFFQ40VOV9PSUJsHq', 'admin', 'active', 'organization', 'enterprise_verified', 'enterprise', 1, 0, '系统默认管理员账户', 'unknown', 0, 999999999, 0, 999999, 10000.00, '{"language":"zh-CN","theme":"light","timezone":"Asia/Shanghai"}', '{"notifications":{"email":true,"sms":false,"push":true},"security":{"twoFactorAuth":false,"loginNotification":true}}');

-- 测试用户
INSERT INTO open_user (username, email, nickname, realName, password, role, userStatus, userType, verificationStatus, tier, emailVerified, phoneVerified, phone, bio, gender, loginFailCount, remainingUsage, totalUsage, dailyFreeUsageRemaining, balance, preferences, settings) 
VALUES ('testuser', '<EMAIL>', '测试用户', '张测试', '$2b$10$aQitxudmoAhCmPDw4FFOfuUU0UUqdXCpJ061ZcLsP7.udfMbzV52W', 'user', 'active', 'individual', 'personal_verified', 'basic', 1, 1, '13800138000', '测试账户，用于演示和测试', 'male', 0, 1000, 0, 100, 100.00, '{"language":"zh-CN","theme":"light","timezone":"Asia/Shanghai"}', '{"notifications":{"email":true,"sms":true,"push":true},"security":{"twoFactorAuth":false,"loginNotification":true}}');

-- 管理员API密钥
INSERT INTO open_api_key (user_id, service_id, name, apiKey, secretHash, keyType, `key-status`, description, permissions, is_secret_viewed) 
VALUES (1, 0, '管理员主密钥', 'ak_admin_test_key_12345678901234567890', '$2b$10$lbTUrWLVR9hgwKJyos.1S.z2nesGr3CMTnyR5iLaYhcLf9t8Zh92u', 'enterprise', 'active', '管理员全局访问密钥，可以访问所有服务', '["*:*:*"]', 1);

-- 测试用户API密钥
INSERT INTO open_api_key (user_id, service_id, name, apiKey, secretHash, keyType, `key-status`, description, permissions, is_secret_viewed) 
VALUES (2, 0, '测试用户主密钥', 'ak_a1fb793ecce140d3851dbae3e5568dbf', '$2b$10$xw/QOVu4jVgYyBggTWBh.O708oUnJGUqtCE96Y3FF7UsKlk82rH6O', 'basic', 'active', '测试用户访问密钥，有调用次数限制', '["service:OCR_EXPRESS:*","service:ADDRESS_EXTRACT:*","service:GEO_REVERSE:*"]', 1);

-- 服务数据
INSERT INTO open_service (user_id, service_id, code, name, description, type, serviceStatus, pricingModel, currentVersion, features, endpoint, config, callCount, price, dailyLimit, minuteLimit, requireAuth, isAsync, timeout) 
VALUES (1, 0, 'OCR_EXPRESS', '物流面单OCR识别', '智能识别物流面单信息，提取收发件人、地址、电话等关键信息，支持多种面单格式，准确率高达95%以上。适用于物流企业、电商平台等自动化数据录入场景。', 'OCR', 'active', 'pay_per_use', 'v1.0.0', '高精度识别,多格式支持,实时处理,结构化输出,批量处理', '/op/ocr/upload', '{"supportedFormats":["jpg","jpeg","png","pdf","bmp","tiff"],"maxFileSize":"10MB","accuracy":0.95,"supportedCouriers":["顺丰","圆通","中通","申通","韵达","百世","邮政","京东","天天","德邦"],"outputFields":["sender_name","sender_phone","sender_address","receiver_name","receiver_phone","receiver_address","tracking_number","courier_name","weight","package_count"],"batchLimit":10,"preProcessing":{"autoRotate":true,"enhanceImage":true,"cropToContent":true},"returnOriginalImage":false,"accessMethods":[{"type":"file_upload","endpoint":"/op/ocr/upload","method":"POST","contentType":"multipart/form-data","maxFileSize":"5MB","supportedFormats":["jpg","jpeg","png","gif","bmp","webp"]},{"type":"base64","endpoint":"/op/ocr/recognize","method":"POST","contentType":"application/json","maxBase64Length":7000000}]}', 0, 0.05, 10000, 100, 1, 0, 30);

INSERT INTO open_service (user_id, service_id, code, name, description, type, serviceStatus, pricingModel, currentVersion, features, endpoint, config, callCount, price, dailyLimit, minuteLimit, requireAuth, isAsync, timeout) 
VALUES (1, 0, 'ADDRESS_EXTRACT', '物流文本精确提取省市区详细地址', '从文本中精确提取省市区详细地址信息，支持非结构化文本解析，可处理各种格式的地址信息，并进行标准化输出，支持省市区到街道社区的精确识别。', 'NLP', 'active', 'pay_per_use', 'v1.0.0', '智能分词,地址标准化,高准确率,支持模糊地址,批量处理,多维度纠错', '/op/address/extract', '{"supportedRegions":["全国省市区县"],"outputFormat":"standard","accuracy":0.98,"maxTextLength":2000,"batchLimit":50,"supportedOutputFields":{"province":true,"city":true,"district":true,"street":true,"community":true,"building":true,"room":true,"detail":true,"postalCode":true,"formatted":true},"addressNormalization":true,"fuzzyMatching":true,"confidenceScoreThreshold":0.7,"returnMultipleCandidates":true,"candidateLimit":3}', 0, 0.05, 20000, 200, 1, 0, 15);

INSERT INTO open_service (user_id, service_id, code, name, description, type, serviceStatus, pricingModel, currentVersion, features, endpoint, config, callCount, price, dailyLimit, minuteLimit, requireAuth, isAsync, timeout) 
VALUES (1, 0, 'GEO_REVERSE', '地理坐标逆解析出地址', '根据经纬度坐标逆向解析出详细地址信息，支持多种坐标系，可输出丰富的POI信息，精确到建筑物级别。适用于物流配送、位置服务、用户轨迹分析等场景。', 'DATA', 'active', 'pay_per_use', 'v1.0.0', '高精度定位,实时解析,多坐标系支持,POI识别,跨平台兼容,批量处理', '/op/address/rev-geo', '{"supportedCoordSystems":["WGS84","GCJ02","BD09"],"precision":"building","language":["zh-CN","en-US"],"radius":50,"batchLimit":100,"includePoiInfo":true,"maxPois":10,"poiTypes":["餐饮","住宿","购物","交通","教育","医疗","金融","休闲娱乐","旅游景点","商务办公"],"includeRoadInfo":true,"includeAddressComponents":true,"includeBusinessAreas":true,"formatOptions":{"formatted":true,"short":true,"long":true},"distanceCalculation":true}', 0, 0.05, 50000, 500, 1, 0, 10);

-- 用户服务关联
-- 管理员用户服务关联
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (1, 1, 999999999, 0, 0, NOW(), 0);
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (1, 2, 999999999, 0, 0, NOW(), 0);
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (1, 3, 999999999, 0, 0, NOW(), 0);

-- 测试用户服务关联
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (2, 1, 1000, 0, 0, NOW(), 0);
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (2, 2, 1000, 0, 0, NOW(), 0);
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (2, 3, 1000, 0, 0, NOW(), 0);

SET FOREIGN_KEY_CHECKS=1;
```

##### 方案3.3：导入SQL文件的命令
- 如果您需要在服务器上直接导入SQL文件，可以使用以下命令：
```bash
# 导入完整版SQL（包含表结构和数据）
mysql -u用户名 -p密码 数据库名 < seed_data_complete.sql

# 导入仅数据版SQL（适用于表结构已存在）
mysql -u用户名 -p密码 数据库名 < seed_data_only.sql

```

##### 方案3.4：处理可能的导入错误
如果导入过程中遇到错误，可以尝试以下解决方案：
字符集问题：确保MySQL服务器配置为使用UTF-8编码
```bash
   -- 检查当前字符集
   SHOW VARIABLES LIKE 'character_set%';
   
   -- 如果需要，可以在导入前设置会话字符集
   SET NAMES utf8mb4;

   外键约束问题：如果出现外键约束错误，可以临时禁用外键检查
    SET FOREIGN_KEY_CHECKS=0;
   -- 执行导入操作
   SET FOREIGN_KEY_CHECKS=1;

   方案3.5：验证导入是否成功
-- 检查用户表
SELECT id, username, email, role FROM open_user;

-- 检查API密钥表
SELECT id, name, apiKey, keyType, `key-status` FROM open_api_key;

-- 检查服务表
SELECT id, code, name, type FROM open_service;

-- 检查用户服务关联表
SELECT userId, serviceId, total_count FROM user_service;
```
ak_2c793afedbf54c6cbea73d2e6062b023
sk_032f0aa429c74e9eb2d3303630b09732

ak_f35b284bd7c945a190352936cd9d4c85
sk_afed60a5acf744f4949d47b94e8ce5e9

online:
ak_db462733039e4f6890027b78fa6e124b
sk_ebaa1f8cf92c4b2092f60a3892c0fc07

597035529
ak_55881ec46f9e4ab7a992f9e6b429f4ef
sk_7468e1e829194be3869190c725bb797d

### 9.4 API 连通性测试

```bash
# 测试 NestJS 健康检查
curl http://localhost:8088/v1/health

# 测试 Python 服务健康检查
curl http://localhost:8866/health

# 测试 NestJS API 路由
curl http://localhost:8088/v1/

# 如果配置了 Nginx，测试域名访问
curl https://api.aiszyl.cn/v1/
```

### 9.5 常见问题排查

如果部署后遇到问题，请检查：

1. **数据库连接问题**：
   - 检查数据库容器是否健康：`docker compose logs mysql`
   - 验证连接参数是否正确：host=mysql, port=3306, username=root

2. **Redis 连接问题**：
   - 检查 Redis 容器是否健康：`docker compose logs redis`
   - 测试连接：`docker compose exec redis redis-cli -a BamsxmSesxBDBDEX ping`

3. **网络连通性问题**：
   - 容器间网络测试：`docker compose exec nest-backend ping mysql`
   - 容器间网络测试：`docker compose exec nest-backend ping python-service`

4. **权限问题**：
   - 检查日志目录权限：`ls -la logs/`
   - 修复权限：`chmod -R 777 logs/`

5. **端口冲突问题**：
   - 检查端口占用：`netstat -tuln | grep '8088\|8866\|3306\|6379'`
   - 如有冲突，修改 docker-compose.yml 中对应服务的端口映射

6. **证书问题**（如果配置了 HTTPS）：
   - 检查证书文件：`ls -la /etc/letsencrypt/live/api.aiszyl.cn/`
   - 检查 Nginx 配置：`nginx -t`

### 9.6 维护备忘录

**备份数据库**：

```bash
# 创建备份目录
mkdir -p backups

# 备份数据库
docker compose exec mysql sh -c 'mysqldump -u root -p4iF6TSENcSaH8pSz openapidb' > backups/openapidb_$(date +%Y%m%d).sql
```

**查看磁盘空间**：

```bash
# 查看磁盘使用情况
df -h

# 查看 Docker 数据使用情况
docker system df
```

**清理不需要的数据**：

```bash
# 清理未使用的容器、镜像等（谨慎使用）
docker system prune
```

