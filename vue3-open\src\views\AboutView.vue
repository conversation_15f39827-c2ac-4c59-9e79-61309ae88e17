<template>
  <div class="about-page">
      <!-- 页面头部 -->
      <div class="page-hero">
        <div class="hero-content">
          <h1 class="hero-title">关于我们</h1>
          <p class="hero-subtitle">
            致力于为开发者提供最优质的AI服务平台
          </p>
        </div>
        <div class="hero-decoration">
          <div class="decoration-circle circle-1"></div>
          <div class="decoration-circle circle-2"></div>
          <div class="decoration-circle circle-3"></div>
        </div>
      </div>
      
      <!-- 公司介绍 -->
      <div class="company-intro">
        <div class="container">
          <div class="intro-grid">
            <div class="intro-content">
              <h2>我们的使命</h2>
              <p>
                我们致力于构建一个开放、易用、高效的AI服务平台，
                让每一位开发者都能轻松地将人工智能技术集成到自己的应用中。
                通过提供稳定可靠的API服务，我们希望降低AI技术的使用门槛，
                推动人工智能在各个行业的广泛应用。
              </p>
              
              <h3>核心价值观</h3>
              <ul class="values-list">
                <li>
                  <el-icon><Star /></el-icon>
                  <span><strong>创新驱动</strong> - 持续探索前沿AI技术</span>
                </li>
                <li>
                  <el-icon><Lock /></el-icon>
                  <span><strong>安全可靠</strong> - 保障数据安全和服务稳定</span>
                </li>
                <li>
                  <el-icon><User /></el-icon>
                  <span><strong>用户至上</strong> - 以用户需求为核心导向</span>
                </li>
                <li>
                  <el-icon><Connection /></el-icon>
                  <span><strong>开放合作</strong> - 构建开放的生态系统</span>
                </li>
              </ul>
            </div>
            
            <div class="intro-image">
              <div class="image-placeholder">
                <el-icon size="120"><Operation /></el-icon>
                <p>AI服务平台</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 服务特色 -->
      <div class="features-section">
        <div class="container">
          <h2 class="section-title">我们的优势</h2>
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">
                <el-icon><Lightning /></el-icon>
              </div>
              <h3>高性能</h3>
              <p>采用先进的分布式架构，确保API响应速度和并发处理能力</p>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon">
                <el-icon><Lock /></el-icon>
              </div>
              <h3>安全保障</h3>
              <p>企业级安全防护，数据传输加密，严格的权限控制机制</p>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon">
                <el-icon><Tools /></el-icon>
              </div>
              <h3>易于集成</h3>
              <p>提供多种编程语言的SDK，详细的文档和示例代码</p>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon">
                <el-icon><Headset /></el-icon>
              </div>
              <h3>专业支持</h3>
              <p>7x24小时技术支持，专业团队为您解决技术难题</p>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <h3>弹性扩展</h3>
              <p>根据业务需求自动扩缩容，支持从小规模到企业级应用</p>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon">
                <el-icon><Money /></el-icon>
              </div>
              <h3>灵活计费</h3>
              <p>按需付费，多种套餐选择，为不同规模用户提供最优方案</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 团队介绍 -->
      <div class="team-section">
        <div class="container">
          <h2 class="section-title">核心团队</h2>
          <div class="team-grid">
            <div class="team-member">
              <div class="member-avatar">
                <el-avatar :size="80" :icon="UserFilled" />
              </div>
              <div class="member-info">
                <h4>张三</h4>
                <p class="member-role">创始人 & CEO</p>
                <p class="member-desc">
                  10年AI领域经验，曾任职于知名科技公司，
                  专注于机器学习和深度学习技术研发。
                </p>
              </div>
            </div>
            
            <div class="team-member">
              <div class="member-avatar">
                <el-avatar :size="80" :icon="UserFilled" />
              </div>
              <div class="member-info">
                <h4>李四</h4>
                <p class="member-role">技术总监</p>
                <p class="member-desc">
                  资深架构师，在分布式系统和云计算领域有丰富经验，
                  负责平台技术架构设计和优化。
                </p>
              </div>
            </div>
            
            <div class="team-member">
              <div class="member-avatar">
                <el-avatar :size="80" :icon="UserFilled" />
              </div>
              <div class="member-info">
                <h4>王五</h4>
                <p class="member-role">产品总监</p>
                <p class="member-desc">
                  专注于用户体验设计，深度理解开发者需求，
                  致力于打造最易用的AI服务平台。
                </p>
              </div>
            </div>
            
            <div class="team-member">
              <div class="member-avatar">
                <el-avatar :size="80" :icon="UserFilled" />
              </div>
              <div class="member-info">
                <h4>赵六</h4>
                <p class="member-role">运营总监</p>
                <p class="member-desc">
                  丰富的市场运营经验，负责用户增长和生态建设，
                  推动平台与开发者社区的深度合作。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 发展历程 -->
      <div class="timeline-section">
        <div class="container">
          <h2 class="section-title">发展历程</h2>
          <div class="timeline">
            <div class="timeline-item">
              <div class="timeline-dot"></div>
              <div class="timeline-content">
                <div class="timeline-date">2025年1月</div>
                <h4>公司成立</h4>
                <p>团队正式成立，开始AI服务平台的规划和设计</p>
              </div>
            </div>
            
            <div class="timeline-item">
              <div class="timeline-dot"></div>
              <div class="timeline-content">
                <div class="timeline-date">2025年3月</div>
                <h4>平台上线</h4>
                <p>发布第一版AI服务平台，提供OCR和NLP基础服务</p>
              </div>
            </div>
            
            <div class="timeline-item">
              <div class="timeline-dot"></div>
              <div class="timeline-content">
                <div class="timeline-date">2025年4月</div>
                <h4>服务扩展</h4>
                <p>新增AI生成服务，用户数量突破1000+</p>
              </div>
            </div>
            
            <div class="timeline-item">
              <div class="timeline-dot active"></div>
              <div class="timeline-content">
                <div class="timeline-date">2025年至今</div>
                <h4>持续创新</h4>
                <p>不断优化服务质量，探索更多AI应用场景</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 数据统计 -->
      <div class="stats-section">
        <div class="container">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-number">10,000+</div>
              <div class="stat-label">注册用户</div>
            </div>
            
            <div class="stat-item">
              <div class="stat-number">1,000,000+</div>
              <div class="stat-label">API调用次数</div>
            </div>
            
            <div class="stat-item">
              <div class="stat-number">99.9%</div>
              <div class="stat-label">服务可用性</div>
            </div>
            
            <div class="stat-item">
              <div class="stat-number">50ms</div>
              <div class="stat-label">平均响应时间</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 联系我们 -->
      <div class="contact-section">
        <div class="container">
          <h2 class="section-title">联系我们</h2>
          <div class="contact-grid">
            <div class="contact-info">
              <h3>商务合作</h3>
              <div class="contact-item">
                <el-icon><Message /></el-icon>
                <span><EMAIL></span>
              </div>
              <div class="contact-item">
                <el-icon><Phone /></el-icon>
                <span>************</span>
              </div>
              
              <h3>技术支持</h3>
              <div class="contact-item">
                <el-icon><Message /></el-icon>
                <span><EMAIL></span>
              </div>
              <div class="contact-item">
                <el-icon><ChatDotRound /></el-icon>
                <span>在线客服（工作日 9:00-18:00）</span>
              </div>
              
              <h3>公司地址</h3>
              <div class="contact-item">
                <el-icon><Location /></el-icon>
                <span>上海市奉贤区贤益园区</span>
              </div>
            </div>
            
            <div class="contact-form">
              <h3>留言咨询</h3>
              <el-form 
                ref="contactFormRef"
                :model="contactForm" 
                :rules="contactRules"
                label-width="80px"
              >
                <el-form-item label="姓名" prop="name">
                  <el-input v-model="contactForm.name" placeholder="请输入您的姓名" />
                </el-form-item>
                
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="contactForm.email" placeholder="请输入您的邮箱" />
                </el-form-item>
                
                <el-form-item label="主题" prop="subject">
                  <el-select v-model="contactForm.subject" placeholder="请选择咨询主题" style="width: 100%;">
                    <el-option label="商务合作" value="business" />
                    <el-option label="技术支持" value="support" />
                    <el-option label="产品建议" value="suggestion" />
                    <el-option label="其他" value="other" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="内容" prop="message">
                  <el-input 
                    v-model="contactForm.message" 
                    type="textarea"
                    :rows="4"
                    placeholder="请详细描述您的问题或需求"
                    maxlength="500"
                    show-word-limit
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button 
                    type="primary" 
                    :loading="submitting"
                    @click="submitContact"
                  >
                    提交留言
                  </el-button>
                  <el-button @click="resetForm">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  Star,
  Lock,
  User,
  Connection,
  Operation,
  Lightning,
  Tools,
  Headset,
  TrendCharts,
  Money,
  UserFilled,
  Message,
  Phone,
  ChatDotRound,
  Location
} from '@element-plus/icons-vue'


const contactFormRef = ref<FormInstance>()
const submitting = ref(false)

// 联系表单
const contactForm = reactive({
  name: '',
  email: '',
  subject: '',
  message: ''
})

// 表单验证规则
const contactRules: FormRules = {
  name: [
    { required: true, message: '请输入您的姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请选择咨询主题', trigger: 'change' }
  ],
  message: [
    { required: true, message: '请输入留言内容', trigger: 'blur' },
    { min: 10, message: '留言内容至少10个字符', trigger: 'blur' }
  ]
}

// 提交联系表单
const submitContact = async () => {
  if (!contactFormRef.value) return
  
  try {
    await contactFormRef.value.validate()
    submitting.value = true
    
    // 调用API提交联系表单
    const response = await fetch('/open/v1/contact', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(contactForm)
    })
    
    if (!response.ok) {
      throw new Error('提交失败')
    }
    
    ElMessage.success('留言提交成功，我们会尽快与您联系！')
    resetForm()
  } catch (error) {
    ElMessage.error('发送失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (!contactFormRef.value) return
  
  contactFormRef.value.resetFields()
  Object.assign(contactForm, {
    name: '',
    email: '',
    subject: '',
    message: ''
  })
}
</script>

<style scoped>
.about-page {
  min-height: calc(100vh - 70px);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 页面头部 */
.page-hero {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
  color: white;
  padding: 120px 0 80px;
  position: relative;
  overflow: hidden;
}

.page-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
  opacity: 0.8;
}

.hero-content {
  text-align: center;
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin: 0 0 20px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.3rem;
  margin: 0;
  opacity: 0.9;
}

.hero-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 100px;
  height: 100px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 80px;
  height: 80px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

/* 公司介绍 */
.company-intro {
  padding: 100px 0;
  background: white;
}

.intro-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 60px;
  align-items: center;
}

.intro-content h2 {
  font-size: 2.5rem;
  color: #303133;
  margin: 0 0 24px 0;
}

.intro-content h3 {
  font-size: 1.5rem;
  color: #303133;
  margin: 32px 0 16px 0;
}

.intro-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #606266;
  margin: 0 0 24px 0;
}

.values-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.values-list li {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  font-size: 1rem;
  color: #606266;
}

.values-list li .el-icon {
  color: #409eff;
  font-size: 18px;
}

.intro-image {
  display: flex;
  justify-content: center;
}

.image-placeholder {
  width: 300px;
  height: 300px;
  background: #f0f9ff;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #409eff;
}

.image-placeholder p {
  margin: 16px 0 0 0;
  font-size: 1.2rem;
  font-weight: 600;
}

/* 服务特色 */
.features-section {
  padding: 100px 0;
  background: #f8f9fa;
}

.section-title {
  font-size: 2.5rem;
  text-align: center;
  color: #303133;
  margin: 0 0 60px 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
}

.feature-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border-radius: 16px;
  padding: 32px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(15, 23, 42, 0.08);
  transition: all 0.3s;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.feature-card:hover {
  transform: translateY(-8px);
  background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(248, 250, 252, 1) 100%);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.15);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: #f0f9ff;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  color: #409eff;
  font-size: 32px;
}

.feature-card h3 {
  font-size: 1.5rem;
  color: #303133;
  margin: 0 0 16px 0;
}

.feature-card p {
  color: #606266;
  line-height: 1.6;
  margin: 0;
}

/* 团队介绍 */
.team-section {
  padding: 100px 0;
  background: white;
  display:none;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 40px;
}

.team-member {
  text-align: center;
  padding: 32px;
  background: #f8f9fa;
  border-radius: 16px;
}

.member-avatar {
  margin-bottom: 20px;
}

.member-info h4 {
  font-size: 1.3rem;
  color: #303133;
  margin: 0 0 8px 0;
}

.member-role {
  color: #409eff;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.member-desc {
  color: #606266;
  line-height: 1.6;
  margin: 0;
}

/* 发展历程 */
.timeline-section {
  padding: 100px 0;
  background: #f8f9fa;
}

.timeline {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e4e7ed;
}

.timeline-item {
  position: relative;
  padding: 0 0 40px 80px;
}

.timeline-dot {
  position: absolute;
  left: 22px;
  top: 8px;
  width: 16px;
  height: 16px;
  background: #e4e7ed;
  border-radius: 50%;
  border: 4px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.timeline-dot.active {
  background: #409eff;
}

.timeline-content {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.timeline-date {
  color: #409eff;
  font-weight: 600;
  margin-bottom: 8px;
}

.timeline-content h4 {
  font-size: 1.2rem;
  color: #303133;
  margin: 0 0 8px 0;
}

.timeline-content p {
  color: #606266;
  margin: 0;
  line-height: 1.6;
}

/* 数据统计 */
.stats-section {
  padding: 80px 0;
  background: #409eff;
  color: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* 联系我们 */
.contact-section {
  padding: 100px 0;
  background: white;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
}

.contact-info h3 {
  font-size: 1.3rem;
  color: #303133;
  margin: 0 0 20px 0;
}

.contact-info h3:not(:first-child) {
  margin-top: 40px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  color: #606266;
}

.contact-item .el-icon {
  color: #409eff;
  font-size: 16px;
}

.contact-form {
  background: #f8f9fa;
  padding: 40px;
  border-radius: 16px;
}

.contact-form h3 {
  font-size: 1.5rem;
  color: #303133;
  margin: 0 0 32px 0;
  text-align: center;
}

/* 动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .intro-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .intro-content h2 {
    font-size: 2rem;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .team-grid {
    grid-template-columns: 1fr;
  }
  
  .timeline {
    padding-left: 20px;
  }
  
  .timeline::before {
    left: 10px;
  }
  
  .timeline-item {
    padding-left: 40px;
  }
  
  .timeline-dot {
    left: 2px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .contact-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-number {
    font-size: 2rem;
  }
}
</style>
