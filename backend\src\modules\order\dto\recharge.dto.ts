import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, Min, Max } from 'class-validator';
import { PaymentMethod } from '../enums/order.enum';

/**
 * 创建充值订单DTO
 */
export class CreateRechargeOrderDto {
  @ApiProperty({
    description: '充值金额',
    example: 100.00,
    minimum: 1,
    maximum: 10000,
  })
  @IsNumber({}, { message: '充值金额必须是数字' })
  @Min(1, { message: '充值金额不能少于1元' })
  @Max(10000, { message: '单次充值金额不能超过10000元' })
  amount: number;

  @ApiProperty({
    description: '支付方式',
    enum: PaymentMethod,
    example: PaymentMethod.ALIPAY,
  })
  @IsString({ message: '支付方式必须是字符串' })
  paymentMethod: PaymentMethod;

  @ApiProperty({
    description: '备注',
    example: '账户充值',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '备注必须是字符串' })
  remark?: string;

  @ApiProperty({
    description: '支付回调地址',
    example: 'https://your-domain.com/api/v1/payments/callback',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '回调地址必须是字符串' })
  callbackUrl?: string;

  @ApiProperty({
    description: '支付返回地址',
    example: 'https://your-domain.com/payment/result',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '返回地址必须是字符串' })
  returnUrl?: string;
}

/**
 * 充值配置DTO
 */
export class RechargeConfigDto {
  @ApiProperty({
    description: '预设充值金额列表',
    example: [10, 50, 100, 200, 500, 1000],
  })
  presetAmounts: number[];

  @ApiProperty({
    description: '最小充值金额',
    example: 1,
  })
  minAmount: number;

  @ApiProperty({
    description: '最大充值金额',
    example: 10000,
  })
  maxAmount: number;

  @ApiProperty({
    description: '充值优惠活动',
    example: [
      { amount: 100, bonus: 10, description: '充值100元送10元' },
      { amount: 500, bonus: 60, description: '充值500元送60元' },
    ],
    required: false,
  })
  promotions?: Array<{
    amount: number;
    bonus: number;
    description: string;
  }>;
}

/**
 * 充值统计DTO
 */
export class RechargeStatsDto {
  @ApiProperty({
    description: '总充值次数',
    example: 156,
  })
  totalRecharges: number;

  @ApiProperty({
    description: '总充值金额',
    example: 15600.00,
  })
  totalAmount: number;

  @ApiProperty({
    description: '今日充值次数',
    example: 12,
  })
  todayRecharges: number;

  @ApiProperty({
    description: '今日充值金额',
    example: 1200.00,
  })
  todayAmount: number;

  @ApiProperty({
    description: '平均充值金额',
    example: 100.00,
  })
  averageAmount: number;

  @ApiProperty({
    description: '按支付方式统计',
    example: {
      alipay: { count: 80, amount: 8000 },
      wechat: { count: 60, amount: 6000 },
      balance: { count: 16, amount: 1600 },
    },
  })
  byPaymentMethod: Record<string, { count: number; amount: number }>;

  @ApiProperty({
    description: '按金额区间统计',
    example: {
      '1-50': { count: 50, amount: 1500 },
      '51-100': { count: 60, amount: 4800 },
      '101-500': { count: 40, amount: 8000 },
      '501+': { count: 6, amount: 1300 },
    },
  })
  byAmountRange: Record<string, { count: number; amount: number }>;
}

/**
 * 用户充值记录DTO
 */
export class UserRechargeRecordDto {
  @ApiProperty({
    description: '订单ID',
    example: 1,
  })
  orderId: number;

  @ApiProperty({
    description: '订单号',
    example: 'ORD20231201001',
  })
  orderNo: string;

  @ApiProperty({
    description: '充值金额',
    example: 100.00,
  })
  amount: number;

  @ApiProperty({
    description: '支付方式',
    enum: PaymentMethod,
    example: PaymentMethod.ALIPAY,
  })
  paymentMethod: PaymentMethod;

  @ApiProperty({
    description: '订单状态',
    example: 'completed',
  })
  status: string;

  @ApiProperty({
    description: '创建时间',
    example: '2023-12-01T10:30:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '完成时间',
    example: '2023-12-01T10:35:00.000Z',
    required: false,
  })
  completedAt?: Date;

  @ApiProperty({
    description: '备注',
    example: '账户充值',
    required: false,
  })
  remark?: string;
}
