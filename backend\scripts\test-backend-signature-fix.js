const crypto = require('crypto');

/**
 * 测试修复后的后端签名计算
 */

// 修复后的后端签名算法
function fixedBackendSignature(method, path, queryParams, body, timestamp, secretKey) {
  console.log('=== 修复后的后端签名计算 ===');
  console.log('方法:', method);
  console.log('路径:', path);
  console.log('查询参数:', queryParams);
  console.log('请求体:', body);
  console.log('时间戳:', timestamp);
  
  // 排序查询参数
  const sortedQuery = Object.keys(queryParams)
    .sort()
    .map(key => `${key}=${encodeURIComponent(queryParams[key])}`)
    .join('&');
  
  console.log('排序后的查询参数:', sortedQuery);
  
  // 处理请求体（修复后的逻辑）
  let bodyString = '';
  if (body) {
    if (typeof body === 'string') {
      bodyString = body;
    } else {
      try {
        bodyString = JSON.stringify(body);
        // 对于GET请求，即使有空对象也应该使用空字符串（与前端保持一致）
        if (method === 'GET' && bodyString === '{}') {
          bodyString = '';
        }
        console.log('JSON序列化请求体:', bodyString);
      } catch (error) {
        console.error('JSON序列化失败:', error.message);
        bodyString = String(body);
      }
    }
  }
  
  console.log('最终请求体字符串:', bodyString);
  
  const canonicalRequest = [
    method.toUpperCase(),
    path,
    sortedQuery,
    bodyString,
    timestamp
  ].join('\n');
  
  console.log('规范化请求字符串:');
  console.log(canonicalRequest);
  
  const signature = crypto
    .createHmac('sha256', secretKey)
    .update(canonicalRequest)
    .digest('base64');
  
  console.log('生成的签名:', signature);
  return signature;
}

// 测试用例1：逆地理编码GET请求
console.log('测试逆地理编码GET请求签名（修复后）');

const testCase1 = {
  method: 'GET',
  path: '/v1/op/geo/reverse',
  queryParams: {
    lat: '22.543096',
    lng: '114.057865',
    mode: 'async'
  },
  body: {}, // 模拟NestJS的空对象
  timestamp: '1754760120',
  secretKey: 'sk-93f682d5dc7bdbd36fcbd891340bb1e097fa9738d6aefb2c6cda908f646ca950'
};

const backendSig1 = fixedBackendSignature(
  testCase1.method,
  testCase1.path,
  testCase1.queryParams,
  testCase1.body,
  testCase1.timestamp,
  testCase1.secretKey
);

console.log('\n=== 签名对比 ===');
console.log('修复后的后端签名:', backendSig1);
console.log('前端发送的签名:', 'fnibKUI0iARKtuA6QqKcacMatGyA72yjyEYATTMQV6Q=');
console.log('签名一致:', backendSig1 === 'fnibKUI0iARKtuA6QqKcacMatGyA72yjyEYATTMQV6Q=');

// 测试用例2：正地理编码GET请求
console.log('\n\n测试正地理编码GET请求签名（修复后）');

const testCase2 = {
  method: 'GET',
  path: '/v1/op/geo/forward',
  queryParams: {
    lat: '22.543096',
    lng: '114.057865',
    mode: 'async'
  },
  body: {}, // 模拟NestJS的空对象
  timestamp: '1754760130',
  secretKey: 'sk-93f682d5dc7bdbd36fcbd891340bb1e097fa9738d6aefb2c6cda908f646ca950'
};

const backendSig2 = fixedBackendSignature(
  testCase2.method,
  testCase2.path,
  testCase2.queryParams,
  testCase2.body,
  testCase2.timestamp,
  testCase2.secretKey
);

console.log('\n=== 签名对比 ===');
console.log('修复后的后端签名:', backendSig2);
console.log('前端发送的签名:', 'hW7+RWYEpBNXmqZyHI3vKs7qMNLriX0ZTUtCf1mb7ec=');
console.log('签名一致:', backendSig2 === 'hW7+RWYEpBNXmqZyHI3vKs7qMNLriX0ZTUtCf1mb7ec=');
