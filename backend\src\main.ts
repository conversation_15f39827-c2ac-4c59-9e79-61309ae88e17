// 确保crypto模块在全局可用，解决@nestjs/typeorm 11.0.0的已知问题
import * as crypto from 'crypto';
if (!(global as any).crypto) {
  (global as any).crypto = crypto;
}

import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';
import helmet from 'helmet';
import { GlobalExceptionFilter } from '@/common/filters/global-exception.filter';
import { setupSwagger } from './setupSwagger';
import * as express from 'express';

async function bootstrap() {
  // 设置时区为中国标准时间
  process.env.TZ = 'Asia/Shanghai';
  console.log(`应用时区设置为: ${process.env.TZ}`);
  
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  const config = app.get(ConfigService);
  const logger = new Logger('Bootstrap');

  // 设置请求体的最大大小（例如 10 MB）
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ limit: '10mb', extended: true }));
  //设置全局前缀
  const prefix: string = config.get<string>('app.prefix') as string;
  app.setGlobalPrefix(prefix);
  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );
  // 全局拦截器已在AppModule中配置
  
  // 全局异常过滤器
  app.useGlobalFilters(new GlobalExceptionFilter());

  // 设置swagger文档
  try {
    setupSwagger(app);
    logger.log('Swagger documentation initialized');
  } catch (error) {
    logger.error(`Failed to initialize Swagger: ${error.message}`, error.stack);
    // 继续应用启动，不因Swagger设置失败而中断整个应用
  }
  
  // 配置CORS
  // 从配置中获取CORS设置
  const enableCors = config.get<boolean>('app.cors', false);
  if (enableCors) {
    // 配置CORS，支持跨域请求
    app.enableCors({
      origin: true, // 允许所有来源
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: [
        'Content-Type', 
        'Authorization', 
        'X-API-Key', 
        'X-API-KEY',  // 添加大写版本
        'X-SECRET-KEY',
        'X-Request-ID',
        'X-Requested-With',
        'Accept',
        'Origin',
        'If-Modified-Since',
        'Cache-Control',
        'Range'
      ],
      exposedHeaders: ['Content-Length', 'Content-Range'],
      credentials: true,
      maxAge: 3600,
    });
    logger.log('CORS已启用，支持跨域请求');
  } else {
    logger.log('CORS未启用，仅允许同源请求');
  }
  
  // web 安全，防常见漏洞
  // 注意： 开发环境如果开启 nest static module 需要将 crossOriginResourcePolicy 设置为 false 否则 静态资源 跨域不可访问
  // 安全中间件
  app.use(
    helmet({
      crossOriginOpenerPolicy: { policy: 'same-origin-allow-popups' },
      crossOriginResourcePolicy: false,
    }),
  );

  //服务端口
  const port = config.get<number>('app.port') || 8088;

  await app.listen(port, '0.0.0.0');
  const serverUrl = await app.getUrl();

  logger.log(`服务已经启动,请访问: ${serverUrl}/${prefix}/`);
  logger.log(`swagger文档已生成,请访问: ${serverUrl}/${prefix}/swagger-ui/`);
}
bootstrap();
