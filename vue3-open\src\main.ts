import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'
import { useUserStore } from '@/stores/user'

const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// 应用启动时初始化用户状态
const userStore = useUserStore()
if (userStore.token) {
  console.log('应用启动：检测到token，初始化用户信息')
  userStore.initUserInfo()
  // 异步检查并获取用户信息，不阻塞应用启动
  userStore.checkAndFetchUserInfo().catch(error => {
    console.error('应用启动：初始化用户信息失败:', error)
  })
} else {
  console.log('应用启动：未检测到token')
}

app.mount('#app')
