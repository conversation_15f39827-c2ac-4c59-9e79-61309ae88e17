import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  Optional,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { UserVerificationEntity } from './entities/user-verification.entity';
import { UserEntity, UserVerificationStatus } from './entities/user.entity';
import {
  ReviewUserVerificationDto,
  BatchReviewUserVerificationDto,
  QueryUserVerificationDto,
} from './dto/user-verification.dto';
import { UserServiceService } from '../user-service/user-service.service';
import { OrderService } from '../order/order.service';
import { getVerificationBonus } from './user.config';


/**
 * 用户认证审核服务
 * 处理用户实名认证的审核流程和奖励发放
 */
@Injectable()
export class UserVerificationService {
  private readonly logger = new Logger(UserVerificationService.name);

  constructor(
    @InjectRepository(UserVerificationEntity)
    private readonly verificationRepository: Repository<UserVerificationEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    @Inject(forwardRef(() => UserServiceService))
    private readonly userServiceService: UserServiceService,
    private readonly orderService: OrderService,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * 审核用户认证
   * @param verificationId 认证记录ID
   * @param reviewDto 审核信息
   */
  async reviewVerification(
    verificationId: number,
    reviewDto: ReviewUserVerificationDto,
  ): Promise<{ success: boolean; message: string }> {
    return await this.dataSource.transaction(async (queryRunner) => {
      // 查找认证记录
      const verification = await queryRunner.findOne(UserVerificationEntity, {
        where: { id: verificationId },
        relations: ['user'],
      });

      if (!verification) {
        throw new NotFoundException('认证记录不存在');
      }

      if (verification.reviewStatus !== 'pending') {
        throw new BadRequestException('该认证记录已经审核过了');
      }

      // 更新认证记录
      verification.reviewStatus = reviewDto.status;
      verification.reviewRemark = reviewDto.reviewRemark || '';
      verification.reviewedAt = new Date();
      verification.reviewerId = reviewDto.reviewerId;
      await queryRunner.save(UserVerificationEntity, verification);

      // 如果审核通过，更新用户认证状态并发放奖励
      if (reviewDto.status === 'approved') {
        await this.processApprovedVerification(queryRunner, verification);
      }

      this.logger.log(
        `用户认证审核完成: 用户ID=${verification.userId}, 状态=${reviewDto.status}, 审核人=${reviewDto.reviewerId}`,
      );

      // 发送审核完成事件
      const verificationReviewedEvent: any = {
        id: `verification_reviewed_${verification.id}_${Date.now()}`,
        type: 'user_action' as const,
        source: 'user-verification-service',
        timestamp: Date.now(),
        data: {
          id: `verification_reviewed_${verification.id}_${Date.now()}`,
          userId: verification.userId,
          action: 'verification_reviewed',
          resource: 'user_verification',
          timestamp: Date.now(),
          success: true,
          details: {
            verificationId: verification.id,
            status: reviewDto.status,
            reviewerId: reviewDto.reviewerId,
            reviewRemark: reviewDto.reviewRemark,
          },
        },
        payload: {
          id: `verification_reviewed_${verification.id}_${Date.now()}`,
          userId: verification.userId,
          action: 'verification_reviewed',
          resource: 'user_verification',
          timestamp: Date.now(),
          success: true,
          details: {
            verificationId: verification.id,
            status: reviewDto.status,
            reviewerId: reviewDto.reviewerId,
            reviewRemark: reviewDto.reviewRemark,
          },
        },
        metadata: {
          serviceName: 'user-verification-service',
          operation: 'verification_review',
          timestamp: Date.now(),
        },
      };

      // 用户操作事件已记录到日志
      this.logger.log(`用户操作事件: ${JSON.stringify(verificationReviewedEvent)}`);

      return {
        success: true,
        message:
          reviewDto.status === 'approved' ? '审核通过，奖励已发放' : '审核完成',
      };
    });
  }

  /**
   * 处理审核通过的认证
   * @param queryRunner 事务查询器
   * @param verification 认证记录
   */
  private async processApprovedVerification(
    queryRunner: any,
    verification: UserVerificationEntity,
  ): Promise<void> {
    const user = verification.user;

    // 更新用户认证状态
    const newVerificationStatus = UserVerificationStatus.PERSONAL_VERIFIED;
    user.verificationStatus = newVerificationStatus;
    user.realName = verification.realName;
    await queryRunner.save(UserEntity, user);

    // 获取认证奖励配置
    const bonusAmount = getVerificationBonus('personal_verified');

    if (bonusAmount > 0) {
      try {
        // 创建0元订单记录认证奖励
        await this.createVerificationRewardOrder(
          queryRunner,
          user.id,
          bonusAmount,
        );

        // 为用户分配认证奖励使用次数
        await this.grantVerificationReward(queryRunner, user.id, bonusAmount);

        this.logger.log(
          `认证奖励发放成功: 用户ID=${user.id}, 奖励次数=${bonusAmount}`,
        );
      } catch (error) {
        this.logger.error(
          `认证奖励发放失败: 用户ID=${user.id}, 错误=${error.message}`,
          error.stack,
        );
        // 不抛出错误，避免影响审核流程
      }
    }
  }

  /**
   * 创建认证奖励订单（0元订单）
   * @param queryRunner 事务查询器
   * @param userId 用户ID
   * @param rewardAmount 奖励数量
   */
  private async createVerificationRewardOrder(
    queryRunner: any,
    userId: number,
    rewardAmount: number,
  ): Promise<void> {
    // 创建0元订单记录认证奖励
    const orderData = {
      userId,
      type: 'reward', // 奖励类型订单
      amount: 0, // 0元订单
      description: `实名认证奖励 - ${rewardAmount}次使用量`,
      status: 'completed', // 直接完成状态
      rewardType: 'verification_bonus',
      rewardAmount,
    };

    // 使用订单服务创建订单
    await this.orderService.createRewardOrder(
      userId,
      rewardAmount,
      `实名认证奖励 - ${rewardAmount}次使用量`,
    );
  }

  /**
   * 发放认证奖励
   * @param queryRunner 事务查询器
   * @param userId 用户ID
   * @param rewardAmount 奖励数量
   */
  private async grantVerificationReward(
    queryRunner: any,
    userId: number,
    rewardAmount: number,
  ): Promise<void> {
    try {
      // 为用户的所有服务增加免费使用次数
      await this.userServiceService.addFreeCountForAllServices(
        userId,
        rewardAmount,
        '实名认证奖励',
      );
    } catch (error) {
      this.logger.error(
        `为用户 ${userId} 发放认证奖励失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 批量审核认证
   * @param batchReviewDto 批量审核信息
   */
  async batchReviewVerification(
    batchReviewDto: BatchReviewUserVerificationDto,
  ): Promise<{ success: boolean; processed: number; failed: number }> {
    let processed = 0;
    let failed = 0;

    for (const id of batchReviewDto.ids) {
      try {
        await this.reviewVerification(id, {
          status: batchReviewDto.status,
          reviewerId: batchReviewDto.reviewerId,
          reviewRemark: batchReviewDto.reviewRemark,
        });
        processed++;
      } catch (error) {
        this.logger.error(`批量审核失败 - ID: ${id}, 错误: ${error.message}`);
        failed++;
      }
    }

    this.logger.log(
      `批量审核完成: 成功=${processed}, 失败=${failed}, 审核人=${batchReviewDto.reviewerId}`,
    );

    return {
      success: true,
      processed,
      failed,
    };
  }

  /**
   * 查询认证列表
   * @param queryDto 查询条件
   */
  async findVerificationList(queryDto: QueryUserVerificationDto) {
    const {
      page = 1,
      limit = 10,
      status,
      userId,
      realName,
      submittedStartDate,
      submittedEndDate,
    } = queryDto;

    const queryBuilder = this.verificationRepository
      .createQueryBuilder('verification')
      .leftJoinAndSelect('verification.user', 'user')
      .orderBy('verification.createdAt', 'DESC');

    // 添加筛选条件
    if (status) {
      queryBuilder.andWhere('verification.reviewStatus = :status', { status });
    }

    if (userId) {
      queryBuilder.andWhere('verification.userId = :userId', { userId });
    }

    if (realName) {
      queryBuilder.andWhere('verification.realName LIKE :realName', {
        realName: `%${realName}%`,
      });
    }

    if (submittedStartDate) {
      queryBuilder.andWhere('verification.createdAt >= :submittedStartDate', {
        submittedStartDate,
      });
    }

    if (submittedEndDate) {
      queryBuilder.andWhere('verification.createdAt <= :submittedEndDate', {
        submittedEndDate,
      });
    }

    // 分页
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * 获取认证统计信息
   */
  async getVerificationStats() {
    const [totalApplications, pendingCount, approvedCount, rejectedCount] =
      await Promise.all([
        this.verificationRepository.count(),
        this.verificationRepository.count({
          where: { reviewStatus: 'pending' },
        }),
        this.verificationRepository.count({
          where: { reviewStatus: 'approved' },
        }),
        this.verificationRepository.count({
          where: { reviewStatus: 'rejected' },
        }),
      ]);

    return {
      totalApplications,
      pendingCount,
      approvedCount,
      rejectedCount,
      approvalRate:
        totalApplications > 0
          ? ((approvedCount / totalApplications) * 100).toFixed(2)
          : '0.00',
    };
  }
}
