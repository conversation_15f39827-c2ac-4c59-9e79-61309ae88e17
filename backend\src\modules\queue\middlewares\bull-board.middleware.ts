import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { BullBoardService } from '../services/bull-board.service';

/**
 * Bull面板认证中间件
 * 用于保护Bull面板访问，需要用户名密码认证
 */
@Injectable()
export class BullBoardAuthMiddleware implements NestMiddleware {
  constructor(private readonly bullBoardService: BullBoardService) {}
  
  /**
   * 中间件处理函数
   */
  use(req: Request, res: Response, next: NextFunction): void {
    const { username, password } = this.bullBoardService.getCredentials();
    const basePath = this.bullBoardService.getBasePath();
    
    // 检查请求是否为Bull面板路由
    if (req.url.startsWith(basePath)) {
      // 检查认证头
      const authHeader = req.headers.authorization || '';
      
      if (this.isAuthorized(authHeader, username, password)) {
        // 认证成功，继续处理
        next();
      } else {
        // 认证失败，返回401响应
        res.set('WWW-Authenticate', 'Basic realm="Bull Queue Dashboard"');
        res.status(401).send('未授权访问');
      }
    } else {
      // 非Bull面板路由，不需要认证
      next();
    }
  }
  
  /**
   * 检查认证头是否有效
   */
  private isAuthorized(authHeader: string, username: string, password: string): boolean {
    if (!authHeader || !authHeader.startsWith('Basic ')) {
      return false;
    }
    
    // 解析Basic认证头
    const base64Credentials = authHeader.split(' ')[1];
    const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii');
    const [authUsername, authPassword] = credentials.split(':');
    
    // 验证用户名密码
    return authUsername === username && authPassword === password;
  }
} 