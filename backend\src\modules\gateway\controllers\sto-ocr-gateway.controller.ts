import {
  Controller,
  Post,
  Body,
  Req,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Logger,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Request } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBearerAuth } from '@nestjs/swagger';
import { UseAuthStrategy, AuthStrategy } from '@/common/decorators/auth-strategy.decorator';
import { ThrottleConfig } from '@/common/guards/custom-throttler.guard';
import { GatewayService } from '../services/gateway.service';
import { 
  OcrRequestDto,
  FileUploadRequestDto,
} from '../dto/gateway-request.dto';
import {
  SyncResponseDto,
  AsyncResponseDto,
  ProxyAsyncSuccessResponseDto,
  ProxyAsyncTimeoutResponseDto,
  StoOcrResponseDto,
  ErrorResponseDto,
} from '../dto/gateway-response.dto';
import { IGatewayRequest } from '../interfaces/gateway.interfaces';
import { ProcessingMode } from '../config/gateway.constants';
import { getClientIp } from '@/common/utils/ip.utils';
import { API_PATHS } from '@/shared/constants/api.constants';

/**
 * 申通面单OCR网关控制器
 * 负责处理申通面单OCR识别相关的网关请求
 * 遵循控制器职责，专注于HTTP请求处理和参数验证
 */
@ApiTags('申通面单OCR网关')
@Controller('ocr/sto')
@UseAuthStrategy(AuthStrategy.API_KEY)
@ApiBearerAuth()
export class StoOcrGatewayController {
  private readonly logger = new Logger(StoOcrGatewayController.name);

  constructor(private readonly gatewayService: GatewayService) {}

  /**
   * 申通面单OCR文件上传识别
   */
  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ThrottleConfig(20, 60) // 每分钟最多20次文件上传（3秒一次的频率）
  @ApiOperation({ summary: '申通面单OCR文件上传识别' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ 
    status: 200, 
    description: '同步处理成功',
    type: SyncResponseDto,
  })
  @ApiResponse({ 
    status: 202, 
    description: '异步处理已接受',
    type: AsyncResponseDto,
  })
  @ApiResponse({ 
    status: 400, 
    description: '请求参数错误',
    type: ErrorResponseDto,
  })
  @ApiResponse({ 
    status: 401, 
    description: '认证失败',
    type: ErrorResponseDto,
  })
  @ApiResponse({ 
    status: 429, 
    description: '请求过于频繁',
    type: ErrorResponseDto,
  })
  async uploadStoOcr(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: FileUploadRequestDto,
    @Req() request: Request,
  ) {
    this.logger.log(`申通面单OCR文件上传请求: ${request.headers['x-request-id']}`);

    const gatewayRequest: IGatewayRequest = {
      path: API_PATHS.STO_OCR.UPLOAD,
      method: 'POST',
      headers: request.headers as Record<string, string>,
      body: {
        file,
        mode: body.mode || ProcessingMode.SYNC,
      },
      user: (request as any).user ? {
        id: (request as any).user.id,
        username: (request as any).user.username,
        email: (request as any).user.email,
      } : undefined,
      clientIp: getClientIp(request),
      requestId: request.headers['x-request-id'] as string,
    };

    return this.gatewayService.handleRequest(gatewayRequest);
  }

  /**
   * 申通面单OCR Base64识别
   */
  @Post('recognize')
  @ThrottleConfig(20, 60) // 每分钟最多20次Base64识别（3秒一次的频率）
  @ApiOperation({ summary: '申通面单OCR Base64识别' })
  @ApiResponse({ 
    status: 200, 
    description: '同步处理成功',
    type: StoOcrResponseDto,
  })
  @ApiResponse({ 
    status: 202, 
    description: '异步处理已接受',
    type: AsyncResponseDto,
  })
  @ApiResponse({ 
    status: 400, 
    description: '请求参数错误',
    type: ErrorResponseDto,
  })
  @ApiResponse({ 
    status: 401, 
    description: '认证失败',
    type: ErrorResponseDto,
  })
  @ApiResponse({ 
    status: 429, 
    description: '请求过于频繁',
    type: ErrorResponseDto,
  })
  async recognizeStoOcr(
    @Body() body: OcrRequestDto,
    @Req() request: Request,
  ) {
    this.logger.log(`申通面单OCR Base64识别请求: ${request.headers['x-request-id']}`);

    const gatewayRequest: IGatewayRequest = {
      path: API_PATHS.STO_OCR.RECOGNIZE,
      method: 'POST',
      headers: request.headers as Record<string, string>,
      body: {
        image: body.imageBase64,
        mode: body.mode || ProcessingMode.SYNC,
      },
      user: (request as any).user ? {
        id: (request as any).user.id,
        username: (request as any).user.username,
        email: (request as any).user.email,
      } : undefined,
      clientIp: getClientIp(request),
      requestId: request.headers['x-request-id'] as string,
    };

    return this.gatewayService.handleRequest(gatewayRequest);
  }
}
