import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { 
  AddressInfo, 
  ExtractionResult, 
  ReverseGeocodeDto, 
  BatchReverseGeocodeDto, 
  ReverseGeocodeResult, 
  BatchReverseGeocodeResult 
} from '../dto/address-extraction.dto';

// 地图API配置接口
interface MapApiConfig {
  provider: string;
  url: string;
  key: string;
  parseResponse: (data: any) => any;
}

/**
 * 地址执行器服务
 * 负责执行具体的地址提取和地理编码业务操作，不包含权限验证等
 */
@Injectable()
export class AddressExecutorService {
  private readonly logger = new Logger(AddressExecutorService.name);
  private readonly pythonServiceUrl: string;
  private readonly mapApiConfigs: MapApiConfig[];

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    const configuredUrl = this.configService.get<string>('PYTHON_SERVICE_URL');
    
    if (!configuredUrl) {
      this.logger.warn('PYTHON_SERVICE_URL未配置，使用默认值');
      this.pythonServiceUrl = 'http://python-service:8866';
    } else {
      this.pythonServiceUrl = configuredUrl;
    }
    
    this.logger.log(`Python服务URL配置为: ${this.pythonServiceUrl}`);

    // 初始化地图API配置
    this.mapApiConfigs = [
      // 高德地图配置
      {
        provider: '高德地图',
        url: 'https://restapi.amap.com/v3/geocode/regeo',
        key: this.configService.get<string>('AMAP_KEY') || '941c8903ad31b2acf1b20118d2a54bb1', // 测试密钥
        parseResponse: (data: any): ReverseGeocodeResult => {
          if (data.status === '1' && data.regeocode) {
            const regeocode = data.regeocode;
            const addressComponent = regeocode.addressComponent || {};
            
            return {
              success: true,
              coordinates: {
                latitude: data.latitude,
                longitude: data.longitude
              },
              address: {
                full_address: regeocode.formatted_address || '',
                province: addressComponent.province || '',
                city: addressComponent.city || '',
                district: addressComponent.district || '',
                street: addressComponent.township || addressComponent.street || '',
                house_number: addressComponent.streetNumber || '',
                postcode: addressComponent.postcode || '',
                confidence: 0.9,
                resolution_method: '高德地图API'
              },
              processing_time: '0ms'
            };
          }
          
          throw new Error('高德地图API返回无效数据');
        }
      },
      // 腾讯地图配置
      {
        provider: '腾讯地图',
        url: 'https://apis.map.qq.com/ws/geocoder/v1/',
        key: this.configService.get<string>('TENCENT_MAP_KEY') || 'GT7BZ-QV2RD-KH74P-HKCNM-5AF76-3VBHL', // 测试密钥
        parseResponse: (data: any): ReverseGeocodeResult => {
          if (data.status === 0 && data.result) {
            const result = data.result;
            const addressComponent = result.address_component || {};
            
            return {
              success: true,
              coordinates: {
                latitude: data.latitude,
                longitude: data.longitude
              },
              address: {
                full_address: result.address || '',
                province: addressComponent.province || '',
                city: addressComponent.city || '',
                district: addressComponent.district || '',
                street: addressComponent.street || '',
                house_number: '',
                postcode: '',
                confidence: 0.9,
                resolution_method: '腾讯地图API'
              },
              processing_time: '0ms'
            };
          }
          
          throw new Error('腾讯地图API返回无效数据');
        }
      },
      // 百度地图配置
      {
        provider: '百度地图',
        url: 'https://api.map.baidu.com/reverse_geocoding/v3/',
        key: this.configService.get<string>('BAIDU_MAP_KEY') || 'V4GVHuRD0T0TImlunB7UoehFiFY4Rbme', // 测试密钥
        parseResponse: (data: any): ReverseGeocodeResult => {
          if (data.status === 0 && data.result) {
            const result = data.result;
            const addressComponent = result.addressComponent || {};
            
            return {
              success: true,
              coordinates: {
                latitude: data.latitude,
                longitude: data.longitude
              },
              address: {
                full_address: result.formatted_address || '',
                province: addressComponent.province || '',
                city: addressComponent.city || '',
                district: addressComponent.district || '',
                street: addressComponent.street || '',
                house_number: addressComponent.street_number || '',
                postcode: '',
                confidence: 0.9,
                resolution_method: '百度地图API'
              },
              processing_time: '0ms'
            };
          }
          
          throw new Error('百度地图API返回无效数据');
        }
      }
    ];
  }

  /**
   * 从单个文本中提取地址信息
   * @param text 文本内容
   * @param mode 提取模式
   * @param requestInfo 请求信息（可选）
   * @returns 提取结果
   */
  async extractFromText(
    text: string, 
    mode: 'single' | 'multiple' = 'single',
    requestInfo?: { userId?: number; apiKey?: string; serviceCode?: string }
  ): Promise<ExtractionResult> {
    const startTime = Date.now();
    
    try {
      this.logger.log(`开始提取地址信息，文本长度: ${text.length}`);
      
      // 第一层：正则表达式快速预处理
      const regexResults = this.extractWithRegex(text);
      
      // 第二层：调用Python服务进行NLP处理
      const nlpResults = await this.extractWithNLP(text, mode);
      
      // 第三层：融合结果并优化
      const finalResults = this.mergeAndOptimizeResults(regexResults, nlpResults, text);
      console.log('finalResults:::', finalResults, regexResults, nlpResults )
      // 确保每个地址对象都有detail_address字段
      const enhancedResults = finalResults.map((addr:any) => {
        // 如果有detailAddress但没有detail_address，添加detail_address
        if (addr.detailAddress && !addr.detail_address) {
          return { ...addr, detail_address: addr.detailAddress };
        }
        // 如果有detail_address但没有detailAddress，添加detailAddress
        else if (!addr.detailAddress && addr.detail_address) {
          return { ...addr, detailAddress: addr.detail_address };
        }
        // 如果都没有，从原始文本中尝试提取详细地址
        else if (!addr.detailAddress && !addr.detail_address) {
          // 提取详细地址的逻辑
          const detailRegex = /([\u4e00-\u9fa5\d]+路[\u4e00-\u9fa5\d]+号[\u4e00-\u9fa5\d]*|[\u4e00-\u9fa5\d]+街[\u4e00-\u9fa5\d]+号[\u4e00-\u9fa5\d]*|[\u4e00-\u9fa5\d]+巷[\u4e00-\u9fa5\d]+号[\u4e00-\u9fa5\d]*)/;
          const detailMatch = text.match(detailRegex);
          const extractedDetail = detailMatch ? detailMatch[1] : '';
          
          if (extractedDetail) {
            return { ...addr, detailAddress: extractedDetail, detail_address: extractedDetail };
          }
        }
        return addr;
      });
      
      const processingTime = Date.now() - startTime;
      
      return {
        success: true,
        addresses: enhancedResults,
        processingTime,
        originalText: text,
      };
    } catch (error) {
      this.logger.error(`地址提取失败: ${error.message}`, error.stack);
      return {
        success: false,
        addresses: [],
        error: error.message,
        processingTime: Date.now() - startTime,
        originalText: text,
      };
    }
  }

  /**
   * 从多个文本中提取地址信息
   * @param texts 文本内容数组
   * @param requestInfo 请求信息（可选）
   * @returns 提取结果数组
   */
  async extractFromMultipleTexts(
    texts: string[],
    requestInfo?: { userId?: number; apiKey?: string; serviceCode?: string }
  ): Promise<ExtractionResult[]> {
    const promises = texts.map(text => this.extractFromText(text, 'single', requestInfo));
    return Promise.all(promises);
  }

  /**
   * 地理坐标逆解析
   * @param coordinates 坐标信息
   * @param requestInfo 请求信息（可选）
   * @returns 逆解析结果
   */
  async reverseGeocode(
    coordinates: ReverseGeocodeDto,
    requestInfo?: { userId?: number; apiKey?: string; serviceCode?: string }
  ): Promise<ReverseGeocodeResult> {
    const startTime = Date.now();
    
    try {
      this.logger.log(`开始地理坐标逆解析，坐标: ${coordinates.latitude},${coordinates.longitude}`);
      
      // 验证坐标有效性
      if (!coordinates.latitude || !coordinates.longitude || 
          isNaN(Number(coordinates.latitude)) || isNaN(Number(coordinates.longitude))) {
        throw new Error('无效的坐标参数');
      }
      
      // 标准化坐标值为数字类型
      const lat = Number(coordinates.latitude);
      const lng = Number(coordinates.longitude);
      
      // 检查坐标范围有效性
      if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
        throw new Error('坐标超出有效范围');
      }
      
      // 按顺序尝试调用国内地图API
      for (const apiConfig of this.mapApiConfigs) {
        try {
          this.logger.log(`尝试使用${apiConfig.provider}进行逆地理编码，API Key: ${apiConfig.key.substring(0, 5)}...`);
          
          // 根据不同地图API构建请求参数
          const params: Record<string, string> = {};
          
          if (apiConfig.provider === '高德地图') {
            params.key = apiConfig.key;
            params.location = `${lng},${lat}`; // 高德要求经度在前，纬度在后
            params.output = 'json';
            params.radius = '1000';
            params.extensions = 'all';
            params.roadlevel = '0';
            params.homeorcorp = '1';
            params.poitype = 'all';
          } else if (apiConfig.provider === '腾讯地图') {
            params.key = apiConfig.key;
            params.location = `${lat},${lng}`; // 腾讯要求纬度在前，经度在后
            params.output = 'json';
            params.get_poi = '0';
          } else if (apiConfig.provider === '百度地图') {
            params.ak = apiConfig.key; // 百度使用ak而不是key
            params.output = 'json';
            params.coordtype = 'wgs84ll'; // 使用WGS84坐标系
            params.location = `${lat},${lng}`; // 百度要求纬度在前，经度在后
            params.extensions_poi = '0';
            params.language = 'zh-CN';
            params.ret_coordtype = 'gcj02ll'; // 返回坐标类型为国测局坐标
          }
          
          // 发送请求，设置较短的超时时间
          this.logger.debug(`请求${apiConfig.provider} API: ${apiConfig.url} 参数: ${JSON.stringify(params)}`);
          const response = await firstValueFrom(
            this.httpService.get(apiConfig.url, {
              params,
              timeout: 3000, // 减少超时时间，以便快速失败并尝试下一个
              headers: {
                'User-Agent': 'OpenPlatform-NestJS/1.0',
                'Accept': 'application/json'
              }
            })
          );
          
          // 处理响应
          if (response.status === 200) {
            this.logger.debug(`${apiConfig.provider}返回成功: ${JSON.stringify(response.data).substring(0, 200)}...`);
            
            // 检查API返回状态
            if (apiConfig.provider === '高德地图' && response.data.status !== '1') {
              throw new Error(`API返回错误: ${response.data.info || '未知错误'}`);
            } else if (apiConfig.provider === '腾讯地图' && response.data.status !== 0) {
              throw new Error(`API返回错误: ${response.data.message || '未知错误'}`);
            } else if (apiConfig.provider === '百度地图' && response.data.status !== 0) {
              throw new Error(`API返回错误: ${response.data.message || '未知错误'}`);
            }
            
            const result = apiConfig.parseResponse({
              ...response.data,
              latitude: coordinates.latitude,
              longitude: coordinates.longitude
            });
            
            const processingTime = Date.now() - startTime;
            result.processing_time = `${processingTime}ms`;
            
            this.logger.log(`成功使用${apiConfig.provider}解析坐标，耗时: ${processingTime}ms`);
            return result;
          } else {
            throw new Error(`API返回状态码: ${response.status}`);
          }
        } catch (error) {
          const errorMsg = error.response?.data 
            ? `${error.message}, 响应: ${JSON.stringify(error.response.data)}` 
            : error.message;
          
          this.logger.warn(`${apiConfig.provider}逆地理编码失败: ${errorMsg}`);
          // 继续尝试下一个API
        }
      }
      
      // 所有地图API都失败，尝试使用Python服务作为兜底
      this.logger.warn('所有地图API调用失败，尝试使用Python服务作为兜底');
      
      try {
        // 先检查Python服务是否健康
        const isHealthy = await this.checkPythonServiceHealth();
        
        if (!isHealthy) {
          this.logger.warn('Python服务不健康，返回默认结果');
          throw new Error('Python服务不可用');
        }
        
        // 尝试使用Python服务进行逆地理编码
        const response = await firstValueFrom(
          this.httpService.post(`${this.pythonServiceUrl}/rev-geo`, coordinates, {
            headers: {
              'Content-Type': 'application/json',
            },
            timeout: 5000, // 适当增加超时时间
          })
        );
        
        if (response.status === 200 && response.data.success) {
          const result = response.data;
          const processingTime = Date.now() - startTime;
          
          // 确保返回的结果符合预期格式
          return {
            ...result,
            processing_time: `${processingTime}ms`,
            address: {
              ...result.address,
              resolution_method: 'python_service_fallback'
            }
          };
        } else {
          throw new Error(`Python服务返回错误: ${response.data.error || '未知错误'}`);
        }
      } catch (pythonError) {
        this.logger.error(`Python服务兜底也失败: ${pythonError.message}`, pythonError.stack);
        
        // 返回默认结果，避免整个流程失败
        const processingTime = Date.now() - startTime;
        return {
          success: false,
          error: '所有地理解析服务均不可用',
          coordinates: coordinates,
          address: {
            full_address: `坐标(${coordinates.latitude},${coordinates.longitude})无法解析`,
            province: '',
            city: '',
            district: '',
            street: '',
            house_number: '',
            postcode: '',
            confidence: 0,
            resolution_method: 'default_fallback'
          },
          processing_time: `${processingTime}ms`
        };
      }
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(`地理坐标逆解析失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        coordinates: coordinates,
        processing_time: `${processingTime}ms`,
        address: {
          full_address: `坐标(${coordinates.latitude},${coordinates.longitude})无法解析`,
          province: '',
          city: '',
          district: '',
          street: '',
          house_number: '',
          postcode: '',
          confidence: 0,
          resolution_method: 'error_fallback'
        }
      };
    }
  }

  /**
   * 批量地理坐标逆解析
   * @param batchData 批量坐标信息
   * @param requestInfo 请求信息（可选）
   * @returns 批量逆解析结果
   */
  async batchReverseGeocode(
    batchData: BatchReverseGeocodeDto,
    requestInfo?: { userId?: number; apiKey?: string; serviceCode?: string }
  ): Promise<BatchReverseGeocodeResult> {
    const startTime = Date.now();
    
    try {
      this.logger.log(`开始批量地理坐标逆解析，数量: ${batchData.coordinates.length}`);
      
      // 调用Python服务进行批量逆地理编码
      const response = await firstValueFrom(
        this.httpService.post(`${this.pythonServiceUrl}/b-rev-geo`, batchData, {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 30000,
        })
      );
      
      const result = response.data;
      const processingTime = Date.now() - startTime;
      
      return {
        ...result,
        processing_time: `${processingTime}ms`,
      };
    } catch (error) {
      this.logger.error(`批量地理坐标逆解析失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 检查Python服务健康状态
   * @returns 服务是否健康
   */
  async checkPythonServiceHealth(): Promise<boolean> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.pythonServiceUrl}/health`, {
          timeout: 5000,
        })
      );
      
      return response.status === 200;
    } catch (error) {
      this.logger.error(`Python服务健康检查失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 第一层：规则引擎快速匹配（处理80%常规格式）
   * 使用正则表达式提取地址信息
   */
  private extractWithRegex(text: string): AddressInfo[] {
    // 简化版本，实际应用中会有更复杂的规则
    const results: AddressInfo[] = [];
    
    // 手机号正则
    const phoneRegex = /([1][3-9]\d{9})/g;
    const phones = text.match(phoneRegex) || [];
    
    // 姓名正则（简化版）
    const nameRegex = /([\u4e00-\u9fa5]{2,4})(?:先生|女士|$)/;
    const nameMatch = text.match(nameRegex);
    const name = nameMatch ? nameMatch[1] : undefined;
    
    // 省份正则（简化版）
    const provinceRegex = /(北京|天津|上海|重庆|河北|山西|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|海南|四川|贵州|云南|陕西|甘肃|青海|台湾|内蒙古|广西|西藏|宁夏|新疆|香港|澳门)(?:省|市|自治区|特别行政区)?/;
    const provinceMatch = text.match(provinceRegex);
    const province = provinceMatch ? provinceMatch[0] : undefined;
    
    // 城市正则（简化版）
    const cityRegex = /([^省]+市|[^省]+地区|[^市]+盟|[^省]+自治州)/;
    const cityMatch = text.match(cityRegex);
    const city = cityMatch ? cityMatch[1] : undefined;
    
    // 区县正则（简化版）
    const districtRegex = /([^市]+区|[^市]+县|[^区]+市|[^县]+旗)/;
    const districtMatch = text.match(districtRegex);
    const district = districtMatch ? districtMatch[1] : undefined;
    
    // 详细地址（简化版）
    const detailRegex = /([\u4e00-\u9fa5\d]+路[\u4e00-\u9fa5\d]+号[\u4e00-\u9fa5\d]*|[\u4e00-\u9fa5\d]+街[\u4e00-\u9fa5\d]+号[\u4e00-\u9fa5\d]*|[\u4e00-\u9fa5\d]+巷[\u4e00-\u9fa5\d]+号[\u4e00-\u9fa5\d]*)/;
    const detailMatch = text.match(detailRegex);
    const detailAddress = detailMatch ? detailMatch[1] : undefined;
    
    // 构建结果
    if (phones.length > 0 || name || province || city || district || detailAddress) {
      const result: AddressInfo = {
        name,
        phone: phones[0],
        province,
        city,
        district,
        detailAddress,
        confidence: 0.7, // 简单估计的置信度
        extractMethod: 'regex',
      };
      
      // 构建完整地址
      result.fullAddress = this.buildFullAddress(result);
      
      results.push(result);
    }
    
    return results;
  }

  /**
   * 第二层：调用Python服务进行NLP处理
   */
  private async extractWithNLP(text: string, mode: 'single' | 'multiple'): Promise<AddressInfo[]> {
    try {
      const response = await firstValueFrom(
        this.httpService.post(`${this.pythonServiceUrl}/extract-address`, {
          text,
          mode,
        }, {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 15000,
        })
      );
      
      const result = response.data;
      
      if (!result.success) {
        throw new Error(result.error || '调用NLP服务失败');
      }
      
      return result.addresses || [];
    } catch (error) {
      this.logger.warn(`NLP地址提取失败，使用备用方法: ${error.message}`);
      return []; // 在实际应用中，可以实现备用方法
    }
  }

  /**
   * 第三层：融合结果并优化
   */
  private mergeAndOptimizeResults(
    regexResults: AddressInfo[],
    nlpResults: AddressInfo[],
    originalText: string
  ): AddressInfo[] {
    // 如果正则表达式没有结果，直接返回NLP结果
    if (regexResults.length === 0) {
      return nlpResults;
    }
    
    // 如果NLP没有结果，直接返回正则表达式结果
    if (nlpResults.length === 0) {
      return regexResults;
    }
    
    // 融合结果
    const mergedResults: AddressInfo[] = [];
    
    // 简单融合逻辑：取两者的最佳结果
    const regexResult = regexResults[0];
    const nlpResult = nlpResults[0];
    
    const mergedResult: AddressInfo = {
      name: nlpResult.name || regexResult.name,
      phone: regexResult.phone || nlpResult.phone, // 正则表达式对手机号识别通常更准确
      province: nlpResult.province || regexResult.province,
      city: nlpResult.city || regexResult.city,
      district: nlpResult.district || regexResult.district,
      street: nlpResult?.street || regexResult?.street,
      detailAddress: nlpResult.detailAddress || regexResult.detailAddress,
      detail_address: nlpResult.detail_address || regexResult.detail_address,
      confidence: Math.max(nlpResult.confidence || 0, regexResult.confidence || 0),
      extractMethod: nlpResult.extract_method || regexResult.extract_method,
    };
    
    // 构建完整地址
    mergedResult.fullAddress = this.buildFullAddress(mergedResult);
    
    mergedResults.push(mergedResult);
    
    return mergedResults;
  }

  /**
   * 构建完整地址
   */
  private buildFullAddress(result: AddressInfo): string {
    const parts = [
      result.province,
      result.city,
      result.district,
      result.street,
      result.detailAddress,
    ].filter(Boolean);
    
    return parts.join('');
  }
} 