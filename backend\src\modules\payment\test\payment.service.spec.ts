import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { DataSource, Repository } from 'typeorm';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { PaymentService } from '../payment.service';
import { PaymentEntity } from '../entities/payment.entity';
import { OrderService } from '../../order/order.service';
import { UserService } from '../../user/user.service';
import { AlipayService } from '../services/alipay.service';
import { WechatPayService } from '../services/wechat-pay.service';
import { BalancePayService } from '../services/balance-pay.service';
import { PaymentMethod, PaymentStatus, OrderStatus } from '../../order/enums/order.enum';

describe('PaymentService', () => {
  let service: PaymentService;
  let paymentRepository: Repository<PaymentEntity>;
  let orderService: OrderService;
  let userService: UserService;
  let alipayService: AlipayService;
  let wechatPayService: WechatPayService;
  let balancePayService: BalancePayService;
  let eventEmitter: EventEmitter2;
  let dataSource: DataSource;

  const mockOrder = {
    id: 1,
    orderNo: 'ORD20231201001',
    userId: 1,
    totalAmount: 100.00,
    status: OrderStatus.PENDING,
    canPay: true,
  };

  const mockPayment = {
    id: 1,
    paymentNo: 'PAY20231201001',
    orderId: 1,
    userId: 1,
    paymentMethod: PaymentMethod.ALIPAY,
    status: PaymentStatus.PENDING,
    amount: 100.00,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockPaymentResult = {
    success: true,
    paymentUrl: 'https://example.com/pay',
    qrCode: 'https://example.com/qr',
    thirdPartyNo: 'third_party_123',
    expiresAt: new Date(Date.now() + 30 * 60 * 1000),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentService,
        {
          provide: getRepositoryToken(PaymentEntity),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            findOne: jest.fn(),
            find: jest.fn(),
            createQueryBuilder: jest.fn(() => ({
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              orderBy: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              take: jest.fn().mockReturnThis(),
              getManyAndCount: jest.fn(),
            })),
          },
        },
        {
          provide: OrderService,
          useValue: {
            findById: jest.fn(),
          },
        },
        {
          provide: UserService,
          useValue: {
            findById: jest.fn(),
          },
        },
        {
          provide: AlipayService,
          useValue: {
            getPaymentMethod: jest.fn().mockReturnValue(PaymentMethod.ALIPAY),
            createPayment: jest.fn(),
            verifyCallback: jest.fn(),
          },
        },
        {
          provide: WechatPayService,
          useValue: {
            getPaymentMethod: jest.fn().mockReturnValue(PaymentMethod.WECHAT),
            createPayment: jest.fn(),
            verifyCallback: jest.fn(),
          },
        },
        {
          provide: BalancePayService,
          useValue: {
            getPaymentMethod: jest.fn().mockReturnValue(PaymentMethod.BALANCE),
            createPayment: jest.fn(),
            verifyCallback: jest.fn(),
          },
        },
        {
          provide: EventEmitter2,
          useValue: {
            emit: jest.fn(),
          },
        },
        {
          provide: DataSource,
          useValue: {
            createQueryRunner: jest.fn(() => ({
              connect: jest.fn(),
              startTransaction: jest.fn(),
              commitTransaction: jest.fn(),
              rollbackTransaction: jest.fn(),
              release: jest.fn(),
              manager: {
                create: jest.fn(),
                save: jest.fn(),
                findOne: jest.fn(),
                update: jest.fn(),
              },
            })),
          },
        },
      ],
    }).compile();

    service = module.get<PaymentService>(PaymentService);
    paymentRepository = module.get<Repository<PaymentEntity>>(getRepositoryToken(PaymentEntity));
    orderService = module.get<OrderService>(OrderService);
    userService = module.get<UserService>(UserService);
    alipayService = module.get<AlipayService>(AlipayService);
    wechatPayService = module.get<WechatPayService>(WechatPayService);
    balancePayService = module.get<BalancePayService>(BalancePayService);
    eventEmitter = module.get<EventEmitter2>(EventEmitter2);
    dataSource = module.get<DataSource>(DataSource);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createPayment', () => {
    const createPaymentDto = {
      orderId: 1,
      paymentMethod: PaymentMethod.ALIPAY,
      amount: 100.00,
      callbackUrl: 'https://example.com/callback',
      returnUrl: 'https://example.com/return',
    };

    it('应该成功创建支付宝支付', async () => {
      // Arrange
      jest.spyOn(orderService, 'findById').mockResolvedValue(mockOrder as any);
      jest.spyOn(alipayService, 'createPayment').mockResolvedValue(mockPaymentResult);
      
      const mockQueryRunner = {
        connect: jest.fn(),
        startTransaction: jest.fn(),
        commitTransaction: jest.fn(),
        rollbackTransaction: jest.fn(),
        release: jest.fn(),
        manager: {
          create: jest.fn().mockReturnValue(mockPayment),
          save: jest.fn().mockResolvedValue(mockPayment),
        },
      };
      
      jest.spyOn(dataSource, 'createQueryRunner').mockReturnValue(mockQueryRunner as any);

      // Act
      const result = await service.createPayment(createPaymentDto);

      // Assert
      expect(orderService.findById).toHaveBeenCalledWith(1);
      expect(alipayService.createPayment).toHaveBeenCalled();
      expect(mockQueryRunner.manager.create).toHaveBeenCalledWith(PaymentEntity, expect.any(Object));
      expect(eventEmitter.emit).toHaveBeenCalledWith('payment.created', expect.any(Object));
      expect(result).toHaveProperty('paymentId');
      expect(result).toHaveProperty('paymentUrl');
    });

    it('当订单不存在时应该抛出异常', async () => {
      // Arrange
      jest.spyOn(orderService, 'findById').mockRejectedValue(new NotFoundException('订单不存在'));

      // Act & Assert
      await expect(service.createPayment(createPaymentDto))
        .rejects.toThrow(NotFoundException);
    });

    it('当订单不允许支付时应该抛出异常', async () => {
      // Arrange
      const unpayableOrder = { ...mockOrder, canPay: false };
      jest.spyOn(orderService, 'findById').mockResolvedValue(unpayableOrder as any);

      // Act & Assert
      await expect(service.createPayment(createPaymentDto))
        .rejects.toThrow(BadRequestException);
    });

    it('当支付金额与订单金额不符时应该抛出异常', async () => {
      // Arrange
      const invalidDto = { ...createPaymentDto, amount: 200.00 };
      jest.spyOn(orderService, 'findById').mockResolvedValue(mockOrder as any);

      // Act & Assert
      await expect(service.createPayment(invalidDto))
        .rejects.toThrow(BadRequestException);
    });

    it('当支付方式不支持时应该抛出异常', async () => {
      // Arrange
      const invalidDto = { ...createPaymentDto, paymentMethod: 'INVALID' as PaymentMethod };
      jest.spyOn(orderService, 'findById').mockResolvedValue(mockOrder as any);
      
      const mockQueryRunner = {
        connect: jest.fn(),
        startTransaction: jest.fn(),
        rollbackTransaction: jest.fn(),
        release: jest.fn(),
        manager: {
          create: jest.fn().mockReturnValue(mockPayment),
          save: jest.fn().mockResolvedValue(mockPayment),
        },
      };
      
      jest.spyOn(dataSource, 'createQueryRunner').mockReturnValue(mockQueryRunner as any);

      // Act & Assert
      await expect(service.createPayment(invalidDto))
        .rejects.toThrow(BadRequestException);
    });
  });

  describe('handleCallback', () => {
    const callbackData = {
      paymentNo: 'PAY20231201001',
      thirdPartyNo: 'third_party_123',
      status: 'success',
      amount: 100.00,
    };

    it('应该成功处理支付宝回调', async () => {
      // Arrange
      const verifyResult = {
        success: true,
        paymentNo: 'PAY20231201001',
        thirdPartyNo: 'third_party_123',
        status: 'success' as const,
        amount: 100.00,
        paidAt: new Date(),
        rawData: callbackData,
      };
      
      jest.spyOn(alipayService, 'verifyCallback').mockResolvedValue(verifyResult);
      jest.spyOn(paymentRepository, 'findOne').mockResolvedValue(mockPayment as any);
      jest.spyOn(service, 'handlePaymentSuccess' as any).mockResolvedValue(undefined);

      // Act
      const result = await service.handleCallback(PaymentMethod.ALIPAY, callbackData);

      // Assert
      expect(alipayService.verifyCallback).toHaveBeenCalledWith(callbackData);
      expect(paymentRepository.findOne).toHaveBeenCalledWith({
        where: { paymentNo: 'PAY20231201001' },
      });
      expect(result.success).toBe(true);
    });

    it('当回调验证失败时应该返回失败', async () => {
      // Arrange
      const verifyResult = {
        success: false,
        errorMessage: '签名验证失败',
      };
      
      jest.spyOn(alipayService, 'verifyCallback').mockResolvedValue(verifyResult);

      // Act
      const result = await service.handleCallback(PaymentMethod.ALIPAY, callbackData);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('签名验证失败');
    });

    it('当支付记录不存在时应该返回失败', async () => {
      // Arrange
      const verifyResult = {
        success: true,
        paymentNo: 'PAY20231201001',
        thirdPartyNo: 'third_party_123',
        status: 'success' as const,
        amount: 100.00,
        paidAt: new Date(),
        rawData: callbackData,
      };
      
      jest.spyOn(alipayService, 'verifyCallback').mockResolvedValue(verifyResult);
      jest.spyOn(paymentRepository, 'findOne').mockResolvedValue(null);

      // Act
      const result = await service.handleCallback(PaymentMethod.ALIPAY, callbackData);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('支付记录不存在');
    });

    it('当支付已处理时应该返回成功', async () => {
      // Arrange
      const verifyResult = {
        success: true,
        paymentNo: 'PAY20231201001',
        thirdPartyNo: 'third_party_123',
        status: 'success' as const,
        amount: 100.00,
        paidAt: new Date(),
        rawData: callbackData,
      };
      
      const successPayment = { ...mockPayment, status: PaymentStatus.SUCCESS };
      
      jest.spyOn(alipayService, 'verifyCallback').mockResolvedValue(verifyResult);
      jest.spyOn(paymentRepository, 'findOne').mockResolvedValue(successPayment as any);

      // Act
      const result = await service.handleCallback(PaymentMethod.ALIPAY, callbackData);

      // Assert
      expect(result.success).toBe(true);
      expect(result.message).toBe('支付已处理');
    });
  });

  describe('findById', () => {
    it('应该成功查找支付记录', async () => {
      // Arrange
      jest.spyOn(paymentRepository, 'findOne').mockResolvedValue(mockPayment as any);

      // Act
      const result = await service.findById(1);

      // Assert
      expect(paymentRepository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
      expect(result).toBeDefined();
    });

    it('当支付记录不存在时应该抛出异常', async () => {
      // Arrange
      jest.spyOn(paymentRepository, 'findOne').mockResolvedValue(null);

      // Act & Assert
      await expect(service.findById(1)).rejects.toThrow(NotFoundException);
    });
  });

  describe('findByPaymentNo', () => {
    it('应该成功根据支付单号查找支付记录', async () => {
      // Arrange
      jest.spyOn(paymentRepository, 'findOne').mockResolvedValue(mockPayment as any);

      // Act
      const result = await service.findByPaymentNo('PAY20231201001');

      // Assert
      expect(paymentRepository.findOne).toHaveBeenCalledWith({ 
        where: { paymentNo: 'PAY20231201001' } 
      });
      expect(result).toBeDefined();
    });

    it('当支付记录不存在时应该抛出异常', async () => {
      // Arrange
      jest.spyOn(paymentRepository, 'findOne').mockResolvedValue(null);

      // Act & Assert
      await expect(service.findByPaymentNo('INVALID')).rejects.toThrow(NotFoundException);
    });
  });
});
