import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, Length, Matches } from 'class-validator';
import { ReviewStatus } from '../entities/user-verification.entity';

// 个人实名认证DTO
export class PersonalVerificationDto {
  @ApiProperty({ description: '真实姓名' })
  @IsNotEmpty({ message: '真实姓名不能为空' })
  @IsString({ message: '真实姓名必须是字符串' })
  @Length(2, 30, { message: '真实姓名长度必须在2-30之间' })
  realName: string;

  @ApiProperty({ description: '身份证号' })
  @IsNotEmpty({ message: '身份证号不能为空' })
  @IsString({ message: '身份证号必须是字符串' })
  @Matches(/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, { message: '身份证号格式不正确' })
  idCard: string;

  @ApiProperty({ description: '身份证正面照片URL' })
  @IsNotEmpty({ message: '身份证正面照片不能为空' })
  @IsString({ message: '身份证正面照片URL必须是字符串' })
  idCardFront: string;

  @ApiProperty({ description: '身份证背面照片URL' })
  @IsNotEmpty({ message: '身份证背面照片不能为空' })
  @IsString({ message: '身份证背面照片URL必须是字符串' })
  idCardBack: string;
}

// 企业实名认证DTO
export class EnterpriseVerificationDto {
  @ApiProperty({ description: '企业名称' })
  @IsNotEmpty({ message: '企业名称不能为空' })
  @IsString({ message: '企业名称必须是字符串' })
  @Length(2, 100, { message: '企业名称长度必须在2-100之间' })
  enterprise: string;

  @ApiProperty({ description: '营业执照号' })
  @IsNotEmpty({ message: '营业执照号不能为空' })
  @IsString({ message: '营业执照号必须是字符串' })
  @Length(15, 18, { message: '营业执照号长度必须在15-18之间' })
  licenseNo: string;

  @ApiProperty({ description: '营业执照照片URL' })
  @IsNotEmpty({ message: '营业执照照片不能为空' })
  @IsString({ message: '营业执照照片URL必须是字符串' })
  licenseImage: string;

  @ApiProperty({ description: '法人姓名' })
  @IsNotEmpty({ message: '法人姓名不能为空' })
  @IsString({ message: '法人姓名必须是字符串' })
  @Length(2, 30, { message: '法人姓名长度必须在2-30之间' })
  legalPerson: string;

  @ApiProperty({ description: '企业地址' })
  @IsNotEmpty({ message: '企业地址不能为空' })
  @IsString({ message: '企业地址必须是字符串' })
  @Length(5, 200, { message: '企业地址长度必须在5-200之间' })
  address: string;

  @ApiProperty({ description: '联系人姓名' })
  @IsNotEmpty({ message: '联系人姓名不能为空' })
  @IsString({ message: '联系人姓名必须是字符串' })
  @Length(2, 30, { message: '联系人姓名长度必须在2-30之间' })
  contactName: string;

  @ApiProperty({ description: '联系人电话' })
  @IsNotEmpty({ message: '联系人电话不能为空' })
  @IsString({ message: '联系人电话必须是字符串' })
  contactNumber: string;

  @ApiProperty({ description: '联系人手机' })
  @IsNotEmpty({ message: '联系人手机不能为空' })
  @IsString({ message: '联系人手机必须是字符串' })
  @Matches(/^1[3-9]\d{9}$/, { message: '手机号格式不正确' })
  contactPhone: string;
}

// 认证审核DTO
export class ReviewVerificationDto {
  @ApiProperty({ description: '审核状态', enum: ReviewStatus })
  @IsNotEmpty({ message: '审核状态不能为空' })
  status: ReviewStatus;

  @ApiProperty({ description: '审核备注', required: false })
  @IsOptional()
  @IsString({ message: '审核备注必须是字符串' })
  remark?: string;

  @ApiProperty({ description: '审核人ID' })
  @IsNotEmpty({ message: '审核人ID不能为空' })
  @IsNumber({}, { message: '审核人ID必须是数字' })
  reviewerId: number;
} 