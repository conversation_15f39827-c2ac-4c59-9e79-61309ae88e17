/**
 * API签名测试工具
 * 用于验证前后端签名算法的一致性
 */
import CryptoJS from 'crypto-js';

/**
 * 构建规范化请求字符串
 * @param method HTTP方法
 * @param path 请求路径
 * @param queryParams 查询参数
 * @param bodyData 请求体数据
 * @param timestamp 时间戳
 * @returns 规范化请求字符串
 */
export function buildCanonicalRequest(
  method: string,
  path: string,
  queryParams: Record<string, any>,
  bodyData: any,
  timestamp: string
): string {
  // 确保路径以/开头
  if (path && !path.startsWith('/')) {
    path = '/' + path;
  }
  
  // 移除路径中可能存在的域名部分
  try {
    // 如果是完整URL，提取路径部分
    if (path.includes('://')) {
      const urlObj = new URL(path);
      path = urlObj.pathname;
    }
  } catch (error) {
    console.log('路径解析失败，使用原始路径');
  }
  
  console.log('处理后的路径:', path);
  
  // 确保路径有/v1前缀（但避免重复添加）
  // 如果路径已经以 /v1 开头，说明已经是完整的API路径，不需要再添加 /v1
  if (!path.startsWith('/v1')) {
    path = '/v1' + path;
  }
  console.log('签名使用的路径:', path);
  
  // 处理查询参数
  console.log('原始查询参数:', JSON.stringify(queryParams));
  
  // 确保queryParams是对象
  const params = { ...queryParams };
  
  // 如果请求体中有mode参数，确保它也在查询参数中
  if (bodyData && typeof bodyData === 'object' && !(bodyData instanceof FormData) && bodyData.mode) {
    params.mode = bodyData.mode;
  }
  
  // 排序查询参数
  const sortedQuery = Object.keys(params)
    .sort()
    .map(key => `${key}=${encodeURIComponent(String(params[key]))}`)
    .join('&');
  
  console.log('排序后的查询参数:', sortedQuery);

  // 处理请求体
  let bodyString = '';
  if (bodyData) {
    if (typeof bodyData === 'string') {
      bodyString = bodyData;
    } else if (bodyData instanceof FormData) {
      // 简化处理，与后端保持一致
      bodyString = 'multipart-form-data';
    } else {
      try {
        bodyString = JSON.stringify(bodyData);
        console.log('JSON序列化请求体:', bodyString);
      } catch (error) {
        console.error('JSON序列化失败:', error);
        bodyString = String(bodyData); // 降级处理
      }
    }
  }

  const result = [
    method.toUpperCase(),
    path,
    sortedQuery,
    bodyString,
    timestamp
  ].join('\n');

  console.log('规范化请求字符串:', result);
  return result;
}

/**
 * 生成签名
 * @param canonicalRequest 规范化请求字符串
 * @param secretKey 密钥
 * @returns 签名
 */
export function generateSignature(canonicalRequest: string, secretKey: string): string {
  try {
    if (!secretKey) {
      console.error('Secret Key不能为空');
      throw new Error('Secret Key不能为空');
    }

    if (!canonicalRequest) {
      console.error('规范化请求字符串不能为空');
      throw new Error('规范化请求字符串不能为空');
    }

    console.log(`生成签名，使用密钥: ${secretKey.substring(0, 3)}...${secretKey.substring(secretKey.length - 3)}`);
    console.log(`完整规范化请求字符串: ${canonicalRequest}`);
    
    // 确保使用正确的编码
    const signature = CryptoJS.HmacSHA256(canonicalRequest, secretKey).toString(CryptoJS.enc.Base64);
    console.log(`生成的签名: ${signature}`);
    return signature;
  } catch (error) {
    console.error('生成签名失败:', error);
    throw error;
  }
}

/**
 * 生成nonce
 * @returns 随机nonce字符串
 */
export function generateNonce(): string {
  return CryptoJS.lib.WordArray.random(16).toString();
}

/**
 * 测试签名生成
 * 用于验证前后端签名算法的一致性
 */
export function testSignature(): void {
  console.log('===== 开始签名测试 =====');
  
  // 测试用例1: GET请求，带查询参数
  const test1 = {
    method: 'GET',
    path: '/op/api/tasks/123',
    queryParams: { service: 'ocr' },
    bodyData: {},
    timestamp: '1626345678',
    secretKey: 'test-secret-key'
  };
  
  console.log('\n测试用例1: GET请求，带查询参数');
  const canonicalRequest1 = buildCanonicalRequest(
    test1.method,
    test1.path,
    test1.queryParams,
    test1.bodyData,
    test1.timestamp
  );
  const signature1 = generateSignature(canonicalRequest1, test1.secretKey);
  console.log('测试用例1签名结果:', signature1);
  
  // 测试用例2: POST请求，带JSON请求体
  const test2 = {
    method: 'POST',
    path: '/op/api/address/extract',
    queryParams: { mode: 'async' },
    bodyData: { 
      text: '张三，13800138000，广东省深圳市南山区科技园',
      mode: 'async'
    },
    timestamp: '1626345678',
    secretKey: 'test-secret-key'
  };
  
  console.log('\n测试用例2: POST请求，带JSON请求体');
  const canonicalRequest2 = buildCanonicalRequest(
    test2.method,
    test2.path,
    test2.queryParams,
    test2.bodyData,
    test2.timestamp
  );
  const signature2 = generateSignature(canonicalRequest2, test2.secretKey);
  console.log('测试用例2签名结果:', signature2);
  
  // 测试用例3: SSE连接请求
  const test3 = {
    method: 'GET',
    path: '/op/api/tasks/123/stream',
    queryParams: { service: 'address' },
    bodyData: {},
    timestamp: '1626345678',
    secretKey: 'test-secret-key'
  };
  
  console.log('\n测试用例3: SSE连接请求');
  const canonicalRequest3 = buildCanonicalRequest(
    test3.method,
    test3.path,
    test3.queryParams,
    test3.bodyData,
    test3.timestamp
  );
  const signature3 = generateSignature(canonicalRequest3, test3.secretKey);
  console.log('测试用例3签名结果:', signature3);
  
  console.log('===== 签名测试完成 =====');
}

// 添加一个测试SSE连接签名的函数
export function testSSESignature(apiKey: string, secretKey: string, jobId: string, service: string): any {
  console.log('===== 测试SSE连接签名 =====');
  
  // 构建查询参数
  const queryParams: Record<string, string> = {};
  if (service) {
    queryParams.service = service;
  }
  
  // 生成时间戳和nonce
  const timestamp = Math.floor(Date.now() / 1000).toString();
  const nonce = generateNonce();
  
  // 构建规范化请求字符串
  const path = `/op/api/tasks/${jobId}/stream`;
  const canonicalRequest = buildCanonicalRequest(
    'GET',
    path,
    queryParams,
    {},
    timestamp
  );
  
  // 生成签名
  const signature = generateSignature(canonicalRequest, secretKey);
  
  console.log('===== SSE连接签名测试完成 =====');
  
  // 返回签名信息
  return {
    apiKey,
    timestamp,
    nonce,
    signature,
    canonicalRequest
  };
}

// 自动执行测试
// testSignature(); 