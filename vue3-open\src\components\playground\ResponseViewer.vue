<template>
  <div class="response-viewer">
    <div class="viewer-header">
      <h3>响应结果</h3>
      <div class="header-actions" v-if="(status && data) || error">
        <el-button size="small" @click="copyResponse">
          <el-icon><CopyDocument /></el-icon>
          复制
        </el-button>
        <el-button size="small" @click="downloadResponse">
          <el-icon><Download /></el-icon>
          下载
        </el-button>
        <el-button size="small" @click="$emit('clear')">
          <el-icon><Delete /></el-icon>
          清空
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <el-skeleton :rows="8" animated />
      <div class="loading-text">
        <el-icon class="is-loading"><Loading /></el-icon>
        正在发送请求...
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <el-alert
        title="请求失败"
        :description="error"
        type="error"
        show-icon
        :closable="false"
      />
    </div>

    <!-- 成功响应 -->
    <div v-else-if="status && data" class="response-content">
      <!-- 异步任务状态 -->
      <div v-if="isAsyncTask" class="async-task-status">
        <el-alert
          :title="getAsyncTaskTitle()"
          :description="getAsyncTaskDescription()"
          :type="getAsyncTaskType()"
          show-icon
          :closable="false"
        />
        <div v-if="taskProgress" class="task-progress">
          <el-progress
            :percentage="Math.round(taskProgress.progress * 100)"
            :status="taskProgress.status === 'failed' ? 'exception' : undefined"
          />
          <div class="progress-info">
            <span>{{ taskProgress.message || '处理中...' }}</span>
            <span v-if="taskProgress.estimatedTime" class="estimated-time">
              预计剩余: {{ Math.round(taskProgress.estimatedTime / 1000) }}秒
            </span>
          </div>
        </div>
      </div>

      <!-- 响应状态 -->
      <div class="response-status">
        <div class="status-info">
          <el-tag
            :type="getStatusType(status)"
            size="large"
            class="status-tag"
          >
            {{ status }} {{ statusText }}
          </el-tag>
          <span class="duration">{{ duration }}ms</span>
        </div>
      </div>

      <!-- 响应标签页 -->
      <el-tabs v-model="activeTab" class="response-tabs">
        <!-- 响应体 -->
        <el-tab-pane label="响应体" name="body">
          <div class="response-body">
            <div class="body-header">
              <div class="body-info">
                <span class="content-type">{{ getContentType() }}</span>
                <span class="body-size">{{ getBodySize() }}</span>
              </div>
              <div class="body-actions">
                <el-button-group size="small">
                  <el-button 
                    :type="viewMode === 'formatted' ? 'primary' : ''"
                    @click="viewMode = 'formatted'"
                  >
                    格式化
                  </el-button>
                  <el-button 
                    :type="viewMode === 'raw' ? 'primary' : ''"
                    @click="viewMode = 'raw'"
                  >
                    原始
                  </el-button>
                </el-button-group>
              </div>
            </div>
            
            <div class="body-content">
              <pre v-if="viewMode === 'formatted'" class="formatted-content">{{ formattedBody }}</pre>
              <pre v-else class="raw-content">{{ rawBody }}</pre>
            </div>
          </div>
        </el-tab-pane>

        <!-- 响应头 -->
        <el-tab-pane label="响应头" name="headers">
          <div class="response-headers">
            <el-table :data="headersList" stripe>
              <el-table-column prop="key" label="名称" width="200" />
              <el-table-column prop="value" label="值" show-overflow-tooltip />
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 请求信息 -->
        <el-tab-pane label="请求信息" name="request">
          <div class="request-info">
            <div class="info-section">
              <h4>请求详情</h4>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="请求方法">{{ requestMethod }}</el-descriptions-item>
                <el-descriptions-item label="请求URL">{{ requestUrl }}</el-descriptions-item>
                <el-descriptions-item label="响应时间">{{ duration }}ms</el-descriptions-item>
                <el-descriptions-item label="状态码">{{ status }}</el-descriptions-item>
              </el-descriptions>
            </div>
            
            <div class="info-section" v-if="requestHeaders">
              <h4>请求头</h4>
              <pre class="headers-content">{{ requestHeaders }}</pre>
            </div>
            
            <div class="info-section" v-if="requestBody">
              <h4>请求体</h4>
              <pre class="body-content">{{ requestBody }}</pre>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-empty description="暂无响应数据">
        <template #image>
          <el-icon size="60" color="#c0c4cc">
            <DataAnalysis />
          </el-icon>
        </template>
        <template #description>
          <p>发送请求后，响应结果将在这里显示</p>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  CopyDocument,
  Download,
  Delete,
  Loading,
  DataAnalysis
} from '@element-plus/icons-vue'
import type { RequestHistory } from '@/stores/playground'

interface Props {
  loading?: boolean
  status?: number
  statusText?: string
  duration?: number
  data?: any
  headers?: Record<string, string>
  error?: string
  requestMethod?: string
  requestUrl?: string
  requestHeaders?: string
  requestBody?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  status: 0,
  statusText: '',
  duration: 0,
  data: null,
  headers: () => ({}),
  error: '',
  requestMethod: '',
  requestUrl: '',
  requestHeaders: '',
  requestBody: ''
})

// 为了兼容现有代码，创建一个response计算属性
const response = computed(() => {
  if (!props.status && !props.data) return null
  return {
    status: props.status,
    statusText: props.statusText,
    duration: props.duration,
    data: props.data,
    headers: props.headers
  }
})

defineEmits<{
  'clear': []
}>()

const activeTab = ref('body')
const viewMode = ref<'formatted' | 'raw'>('formatted')

// 异步任务相关状态
const taskProgress = ref<{
  progress: number
  status: string
  message?: string
  estimatedTime?: number
} | null>(null)
const eventSource = ref<EventSource | null>(null)

// 检测是否为异步任务
const isAsyncTask = computed(() => {
  return props.data?.data?.taskId && props.data?.data?.status === 'queued'
})

// 异步任务相关方法
const getAsyncTaskTitle = () => {
  if (!taskProgress.value) return '任务已提交'

  switch (taskProgress.value.status) {
    case 'queued': return '任务排队中'
    case 'active': return '任务处理中'
    case 'completed': return '任务已完成'
    case 'failed': return '任务处理失败'
    default: return '任务状态未知'
  }
}

const getAsyncTaskDescription = () => {
  if (!taskProgress.value) {
    return `任务ID: ${props.data?.data?.taskId}，正在连接任务状态监听...`
  }

  return taskProgress.value.message || '正在处理您的请求，请稍候...'
}

const getAsyncTaskType = () => {
  if (!taskProgress.value) return 'info'

  switch (taskProgress.value.status) {
    case 'completed': return 'success'
    case 'failed': return 'error'
    default: return 'info'
  }
}

// 启动SSE监听
const startTaskMonitoring = (taskId: string) => {
  if (eventSource.value) {
    eventSource.value.close()
  }

  const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
  const url = `${baseUrl}/v1/op/tasks/${taskId}/events`

  eventSource.value = new EventSource(url)

  eventSource.value.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data)
      taskProgress.value = data

      // 如果任务完成或失败，关闭连接
      if (data.status === 'completed' || data.status === 'failed') {
        eventSource.value?.close()
        eventSource.value = null

        if (data.status === 'completed') {
          ElMessage.success('任务处理完成')
        } else {
          ElMessage.error('任务处理失败')
        }
      }
    } catch (error) {
      console.error('解析SSE数据失败:', error)
    }
  }

  eventSource.value.onerror = (error) => {
    console.error('SSE连接错误:', error)
    eventSource.value?.close()
    eventSource.value = null
    ElMessage.warning('任务状态监听连接中断')
  }
}

// 监听数据变化，如果是异步任务则启动监听
watch(() => props.data, (newData) => {
  if (newData?.data?.taskId && newData?.data?.status === 'queued') {
    startTaskMonitoring(newData.data.taskId)
  }
}, { immediate: true })

// 组件卸载时关闭SSE连接
onUnmounted(() => {
  if (eventSource.value) {
    eventSource.value.close()
    eventSource.value = null
  }
})

// 计算属性
const formattedBody = computed(() => {
  if (!props.data) return ''
  
  try {
    if (typeof props.data === 'string') {
      // 尝试解析JSON字符串
      const parsed = JSON.parse(props.data)
      return JSON.stringify(parsed, null, 2)
    } else {
      // 对象直接格式化
      return JSON.stringify(props.data, null, 2)
    }
  } catch {
    // 如果不是JSON，直接返回
    return props.data
  }
})

const rawBody = computed(() => {
  if (!props.data) return ''
  
  if (typeof props.data === 'string') {
    return props.data
  } else {
    return JSON.stringify(props.data)
  }
})

const headersList = computed(() => {
  if (!props.headers) return []
  
  return Object.entries(props.headers).map(([key, value]) => ({
    key,
    value: String(value)
  }))
})

// 获取状态类型
const getStatusType = (status: number) => {
  if (status >= 200 && status < 300) return 'success'
  if (status >= 300 && status < 400) return 'warning'
  if (status >= 400) return 'danger'
  return 'info'
}

// 获取内容类型
const getContentType = () => {
  const headers = props.headers || {}
  return headers['content-type'] || headers['Content-Type'] || '未知'
}

// 获取响应体大小
const getBodySize = () => {
  const bodyStr = rawBody.value
  const bytes = new Blob([bodyStr]).size
  
  if (bytes < 1024) return `${bytes} B`
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
}

// 复制响应
const copyResponse = async () => {
  try {
    const content = viewMode.value === 'formatted' ? formattedBody.value : rawBody.value
    await navigator.clipboard.writeText(content)
    ElMessage.success('响应内容已复制到剪贴板')
  } catch {
    ElMessage.error('复制失败')
  }
}

// 下载响应
const downloadResponse = () => {
  try {
    const content = viewMode.value === 'formatted' ? formattedBody.value : rawBody.value
    const blob = new Blob([content], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = `response_${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    
    URL.revokeObjectURL(url)
    ElMessage.success('响应文件下载成功')
  } catch {
    ElMessage.error('下载失败')
  }
}
</script>

<style scoped>
.response-viewer {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f2f5;
  background: #fafbfc;
}

.viewer-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.loading-state {
  padding: 24px;
}

.loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 16px;
  color: #909399;
  font-size: 14px;
}

.error-state {
  padding: 24px;
}

.response-content {
  padding: 0;
}

.response-status {
  padding: 16px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-tag {
  font-weight: 600;
}

.duration {
  color: #6c757d;
  font-size: 14px;
}

.async-task-status {
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.task-progress {
  margin-top: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 13px;
  color: #666;
}

.estimated-time {
  color: #409eff;
  font-weight: 500;
}

.response-tabs {
  padding: 0 24px;
}

.response-tabs :deep(.el-tabs__content) {
  padding: 0;
}

.response-body {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.body-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.body-info {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #6c757d;
}

.body-content {
  max-height: 400px;
  overflow: auto;
}

.formatted-content,
.raw-content {
  margin: 0;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  background: #fff;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
}

.response-headers {
  padding: 16px 0;
}

.request-info {
  padding: 16px 0;
}

.info-section {
  margin-bottom: 24px;
}

.info-section:last-child {
  margin-bottom: 0;
}

.info-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.headers-content,
.body-content {
  margin: 0;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #495057;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow: auto;
}

.empty-state {
  padding: 60px 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .viewer-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
  }
  
  .response-status {
    padding: 12px 16px;
  }
  
  .status-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .response-tabs {
    padding: 0 16px;
  }
  
  .body-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .body-info {
    flex-direction: column;
    gap: 4px;
  }
}
</style>