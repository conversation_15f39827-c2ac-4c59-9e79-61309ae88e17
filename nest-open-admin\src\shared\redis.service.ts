import { Injectable } from '@nestjs/common';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';

/**
 * Redis服务类
 * 提供统一的Redis操作方法
 */
@Injectable()
export class RedisService {
  constructor(
    @InjectRedis() private readonly redis: Redis
  ) {}

  /**
   * 设置键值对
   * @param key 键
   * @param value 值
   * @param ttl 过期时间（秒）
   * @param mode 设置模式（如 'NX' 表示仅当键不存在时设置）
   */
  async set(key: string, value: string | number | object, ttl?: number, mode?: string): Promise<string | null> {
    const val = typeof value === 'object' ? JSON.stringify(value) : String(value);
    if (ttl && mode === 'NX') {
      // 使用 SET key value EX seconds NX 命令
      const result = await this.redis.call('SET', key, val, 'EX', ttl, 'NX');
      return result as string | null;
    } else if (ttl) {
      await this.redis.setex(key, ttl, val);
      return 'OK';
    } else if (mode === 'NX') {
      return await this.redis.setnx(key, val) ? 'OK' : null;
    } else {
      await this.redis.set(key, val);
      return 'OK';
    }
  }

  /**
   * 设置键值对并指定过期时间
   * @param key 键
   * @param ttl 过期时间（秒）
   * @param value 值
   */
  async setex(key: string, ttl: number, value: string): Promise<string> {
    return await this.redis.setex(key, ttl, value);
  }

  /**
   * 获取值
   * @param key 键
   * @returns 值
   */
  async get(key: string): Promise<string | null> {
    return await this.redis.get(key);
  }

  /**
   * 获取JSON对象
   * @param key 键
   * @returns 解析后的对象
   */
  async getJson<T = any>(key: string): Promise<T | null> {
    const value = await this.redis.get(key);
    if (!value) return null;
    try {
      return JSON.parse(value);
    } catch {
      return null;
    }
  }

  /**
   * 删除键
   * @param key 键
   */
  async del(key: string): Promise<number> {
    return await this.redis.del(key);
  }

  /**
   * 批量删除键
   * @param keys 键数组
   */
  async delMany(keys: string[]): Promise<number> {
    if (keys.length === 0) return 0;
    return await this.redis.del(...keys);
  }

  /**
   * 检查键是否存在
   * @param key 键
   */
  async exists(key: string): Promise<boolean> {
    const result = await this.redis.exists(key);
    return result === 1;
  }

  /**
   * 设置过期时间
   * @param key 键
   * @param ttl 过期时间（秒）
   */
  async expire(key: string, ttl: number): Promise<boolean> {
    const result = await this.redis.expire(key, ttl);
    return result === 1;
  }

  /**
   * 获取剩余过期时间
   * @param key 键
   * @returns 剩余秒数，-1表示永不过期，-2表示键不存在
   */
  async ttl(key: string): Promise<number> {
    return await this.redis.ttl(key);
  }

  /**
   * 递增
   * @param key 键
   * @param increment 递增值，默认为1
   */
  async incr(key: string, increment: number = 1): Promise<number> {
    if (increment === 1) {
      return await this.redis.incr(key);
    }
    return await this.redis.incrby(key, increment);
  }

  /**
   * 递减
   * @param key 键
   * @param decrement 递减值，默认为1
   */
  async decr(key: string, decrement: number = 1): Promise<number> {
    if (decrement === 1) {
      return await this.redis.decr(key);
    }
    return await this.redis.decrby(key, decrement);
  }

  /**
   * 获取匹配模式的所有键
   * @param pattern 匹配模式
   */
  async keys(pattern: string): Promise<string[]> {
    return await this.redis.keys(pattern);
  }

  /**
   * 哈希表操作 - 设置字段
   * @param key 哈希表键
   * @param field 字段
   * @param value 值
   */
  async hset(key: string, field: string, value: string | number | object): Promise<number> {
    const val = typeof value === 'object' ? JSON.stringify(value) : String(value);
    return await this.redis.hset(key, field, val);
  }

  /**
   * 哈希表操作 - 设置多个字段
   * @param key 哈希表键
   * @param hash 字段值对象
   */
  async hmset(key: string, hash: Record<string, string | number | object>): Promise<string> {
    const processedHash: Record<string, string> = {};
    for (const [field, value] of Object.entries(hash)) {
      processedHash[field] = typeof value === 'object' ? JSON.stringify(value) : String(value);
    }
    return await this.redis.hmset(key, processedHash);
  }

  /**
   * 哈希表操作 - 获取字段值
   * @param key 哈希表键
   * @param field 字段
   */
  async hget(key: string, field: string): Promise<string | null> {
    return await this.redis.hget(key, field);
  }

  /**
   * 哈希表操作 - 获取所有字段和值
   * @param key 哈希表键
   */
  async hgetall(key: string): Promise<Record<string, string>> {
    return await this.redis.hgetall(key);
  }

  /**
   * 哈希表操作 - 删除字段
   * @param key 哈希表键
   * @param field 字段
   */
  async hdel(key: string, field: string): Promise<number> {
    return await this.redis.hdel(key, field);
  }

  /**
   * 哈希表操作 - 字段值递增
   * @param key 哈希表键
   * @param field 字段
   * @param increment 递增值，默认为1
   */
  async hincrby(key: string, field: string, increment: number = 1): Promise<number> {
    return await this.redis.hincrby(key, field, increment);
  }

  /**
   * 列表操作 - 左侧推入
   * @param key 列表键
   * @param value 值
   */
  async lpush(key: string, value: string | number | object): Promise<number> {
    const val = typeof value === 'object' ? JSON.stringify(value) : String(value);
    return await this.redis.lpush(key, val);
  }

  /**
   * 列表操作 - 右侧推入
   * @param key 列表键
   * @param value 值
   */
  async rpush(key: string, value: string | number | object): Promise<number> {
    const val = typeof value === 'object' ? JSON.stringify(value) : String(value);
    return await this.redis.rpush(key, val);
  }

  /**
   * 列表操作 - 左侧弹出
   * @param key 列表键
   */
  async lpop(key: string): Promise<string | null> {
    return await this.redis.lpop(key);
  }

  /**
   * 列表操作 - 右侧弹出
   * @param key 列表键
   */
  async rpop(key: string): Promise<string | null> {
    return await this.redis.rpop(key);
  }

  /**
   * 列表操作 - 获取列表长度
   * @param key 列表键
   */
  async llen(key: string): Promise<number> {
    return await this.redis.llen(key);
  }

  /**
   * 列表操作 - 修剪列表
   * @param key 列表键
   * @param start 开始索引
   * @param stop 结束索引
   */
  async ltrim(key: string, start: number, stop: number): Promise<string> {
    return await this.redis.ltrim(key, start, stop);
  }

  /**
   * 列表操作 - 获取范围内的元素
   * @param key 列表键
   * @param start 开始索引
   * @param stop 结束索引
   */
  async lrange(key: string, start: number, stop: number): Promise<string[]> {
    return await this.redis.lrange(key, start, stop);
  }

  /**
   * 集合操作 - 添加成员
   * @param key 集合键
   * @param member 成员
   */
  async sadd(key: string, member: string | number): Promise<number> {
    return await this.redis.sadd(key, String(member));
  }

  /**
   * 集合操作 - 移除成员
   * @param key 集合键
   * @param member 成员
   */
  async srem(key: string, member: string | number): Promise<number> {
    return await this.redis.srem(key, String(member));
  }

  /**
   * 集合操作 - 获取所有成员
   * @param key 集合键
   */
  async smembers(key: string): Promise<string[]> {
    return await this.redis.smembers(key);
  }

  /**
   * 集合操作 - 检查成员是否存在
   * @param key 集合键
   * @param member 成员
   */
  async sismember(key: string, member: string | number): Promise<boolean> {
    const result = await this.redis.sismember(key, String(member));
    return result === 1;
  }

  /**
   * 有序集合操作 - 添加成员
   * @param key 有序集合键
   * @param score 分数
   * @param member 成员
   */
  async zadd(key: string, score: number, member: string | number): Promise<number> {
    return await this.redis.zadd(key, score, String(member));
  }

  /**
   * 有序集合操作 - 按分数范围获取成员
   * @param key 有序集合键
   * @param min 最小分数
   * @param max 最大分数
   */
  async zrangebyscore(key: string, min: number, max: number): Promise<string[]> {
    return await this.redis.zrangebyscore(key, min, max);
  }

  /**
   * 获取Redis信息
   */
  async info(): Promise<string> {
    return await this.redis.info();
  }

  /**
   * 执行原始Redis命令
   * @param command 命令
   * @param args 参数
   */
  async execute(command: string, ...args: any[]): Promise<any> {
    return await this.redis.call(command, ...args);
  }

  /**
   * 执行Lua脚本
   * @param script Lua脚本
   * @param numKeys 键的数量
   * @param keys 键数组
   * @param args 参数数组
   */
  async eval(script: string, numKeys: number, ...keysAndArgs: (string | number)[]): Promise<any> {
    return await this.redis.eval(script, numKeys, ...keysAndArgs);
  }

  /**
   * 创建Redis管道
   * 用于批量执行命令
   */
  pipeline() {
    return this.redis.pipeline();
  }

  /**
   * 获取原始Redis客户端实例
   * 用于执行更复杂的操作
   */
  getClient(): Redis {
    return this.redis;
  }

  /**
   * 检查Redis连接是否正常
   */
  async ping(): Promise<string> {
    return await this.redis.ping();
  }

  /**
   * 有序集合操作 - 计算指定分数范围内的成员数量
   * @param key 有序集合键
   * @param min 最小分数
   * @param max 最大分数
   */
  async zcount(key: string, min: number | string, max: number | string): Promise<number> {
    return await this.redis.zcount(key, min, max);
  }

  /**
   * 有序集合操作 - 获取指定范围内的成员（按分数从高到低）
   * @param key 有序集合键
   * @param start 开始位置
   * @param stop 结束位置
   * @param withScores 是否返回分数
   */
  async zrevrange(key: string, start: number, stop: number, withScores?: 'WITHSCORES'): Promise<string[]> {
    if (withScores) {
      return await this.redis.zrevrange(key, start, stop, 'WITHSCORES');
    }
    return await this.redis.zrevrange(key, start, stop);
  }
}