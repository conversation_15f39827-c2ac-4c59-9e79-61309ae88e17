/**
 * 登录用户 redis key 过期时间
 * 24h
 */
export const LOGIN_TOKEN_EXPIRESIN = 1000 * 60 * 60 * 24;

export const LOGIN_TOKEN_UMS_EXPIRESIN = 1000 * 60 * 60 * 8;

export const OPEN_PREFIX = 'op';

export const ENTITY_MANAGER_KEY = 'ENTITY_MANAGER';

/**
 * token守卫常量
 * */ 
export const ALLOW_ANON = 'allowAnon';
/**
 * 权限守卫常量*/ 
export const AUTH_PERMISSION = 'authPermission';
/**
 * 角色守卫常量*/ 
export const AUTH_ROLE = 'authRole';

export const IS_PUBLIC_KEY = 'IS_PUBLIC';


/* 记录日志 */
export const LOG_KEY_METADATA = 'common:log';


// 日志业务 类型
export enum BusinessTypeEnum {
  /* 其他 */
  other = '1',

  /* 插入 */
  insert = '2',

  /* 更新 */
  update = '3',

  /* 删除 */
  delete = '4',

  /* 授权 */
  grant = '5',

  /* 导出 */
  export = '6',

  /* 导入 */
  import = '7',

  /* 强退 */
  force = '8',

  /* 清除 */
  clean = '9',
}