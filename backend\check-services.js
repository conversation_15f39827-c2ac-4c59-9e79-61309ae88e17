const mysql = require('mysql2/promise');

async function checkServices() {
  const connection = await mysql.createConnection({
    host: 'localhost', 
    user: 'root', 
    password: '123456', 
    database: 'openapidb'
  });
  
  try {
    console.log('=== 查询所有服务信息 ===');
    const [services] = await connection.execute('SELECT * FROM service ORDER BY id');
    
    services.forEach(service => {
      console.log(`ID: ${service.id}, 名称: ${service.name}, 描述: ${service.description}`);
    });
    
    console.log('\n=== 查询路由配置 ===');
    // 查看是否有路由配置表
    try {
      const [routes] = await connection.execute('SELECT * FROM route_config ORDER BY id');
      routes.forEach(route => {
        console.log(`路径: ${route.path}, 服务ID: ${route.service_id}, 方法: ${route.method}`);
      });
    } catch (error) {
      console.log('没有找到route_config表，可能路由配置在代码中');
    }
    
  } catch (error) {
    console.error('查询失败:', error.message);
  } finally {
    await connection.end();
  }
}

checkServices();
