<template>
  <div class="trend-chart" ref="chartContainer"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

interface TrendData {
  date: string
  value: number
  label?: string
}

interface Props {
  data: TrendData[]
  title?: string
  height?: number
  color?: string
  showGrid?: boolean
  showArea?: boolean
  smooth?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '趋势图',
  height: 300,
  color: '#409EFF',
  showGrid: true,
  showArea: true,
  smooth: true,
  loading: false
})

const chartContainer = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

const initChart = () => {
  if (!chartContainer.value) return

  chartInstance = echarts.init(chartContainer.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance) return

  const option: echarts.EChartsOption = {
    title: {
      text: props.title,
      left: 'left',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
        color: '#303133'
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff'
      },
      formatter: (params: any) => {
        const data = params[0]
        return `
          <div style="padding: 8px;">
            <div style="margin-bottom: 4px; font-weight: bold;">${data.name}</div>
            <div style="display: flex; align-items: center;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${props.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span>${data.seriesName}: ${data.value.toLocaleString()}</span>
            </div>
          </div>
        `
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
      show: props.showGrid,
      borderColor: '#E4E7ED'
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.data.map(item => item.date),
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      },
      axisLabel: {
        color: '#909399',
        fontSize: 12
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisLabel: {
        color: '#909399',
        fontSize: 12,
        formatter: (value: number) => {
          if (value >= 1000000) {
            return (value / 1000000).toFixed(1) + 'M'
          } else if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'K'
          }
          return value.toString()
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#F2F6FC',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: props.title,
        type: 'line',
        smooth: props.smooth,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          color: props.color,
          width: 2
        },
        itemStyle: {
          color: props.color,
          borderColor: '#fff',
          borderWidth: 2
        },
        areaStyle: props.showArea ? {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: props.color + '40'
              },
              {
                offset: 1,
                color: props.color + '10'
              }
            ]
          }
        } : undefined,
        emphasis: {
          focus: 'series',
          itemStyle: {
            shadowBlur: 10,
            shadowColor: props.color + '80'
          }
        },
        data: props.data.map(item => item.value)
      }
    ],
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }

  chartInstance.setOption(option, true)

  if (props.loading) {
    chartInstance.showLoading({
      text: '加载中...',
      color: props.color,
      textColor: '#909399',
      maskColor: 'rgba(255, 255, 255, 0.8)',
      zlevel: 0
    })
  } else {
    chartInstance.hideLoading()
  }
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 监听数据变化
watch(
  () => [props.data, props.title, props.color, props.showGrid, props.showArea, props.smooth, props.loading],
  () => {
    nextTick(() => {
      updateChart()
    })
  },
  { deep: true }
)

// 监听容器高度变化
watch(
  () => props.height,
  () => {
    if (chartContainer.value) {
      chartContainer.value.style.height = props.height + 'px'
      nextTick(() => {
        resizeChart()
      })
    }
  }
)

onMounted(() => {
  nextTick(() => {
    if (chartContainer.value) {
      chartContainer.value.style.height = props.height + 'px'
      initChart()
    }
  })

  // 监听窗口大小变化
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', resizeChart)
})

// 暴露方法给父组件
defineExpose({
  resize: resizeChart,
  getInstance: () => chartInstance
})
</script>

<style scoped>
.trend-chart {
  width: 100%;
  min-height: 200px;
}
</style>