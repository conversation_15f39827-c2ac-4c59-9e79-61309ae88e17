# 定时任务模块最佳实践

## 1. 任务执行记录存储策略

### 问题
定时任务执行记录会随时间积累大量非业务数据，增加数据库负担。

### 解决方案

#### 方案一：Redis存储（推荐）
- **优点**：自动过期、性能好、不占用业务数据库空间
- **缺点**：重启后数据丢失、内存占用
- **适用场景**：对历史记录要求不高的场景

```typescript
// 使用TaskExecutionCacheService
const executionId = await this.taskExecutionCacheService.createExecution(
  'taskName', 
  'taskType'
);
```

#### 方案二：数据库存储 + 激进清理
- **优点**：数据持久化、可查询历史
- **缺点**：需要定期清理、占用数据库空间
- **适用场景**：需要长期保存执行历史的场景

**清理策略**：
- 只保留7天内的记录
- 每个任务只保留最近3条成功记录
- 保留所有失败记录用于问题排查
- 每天凌晨4点自动清理

#### 方案三：混合存储
- 最近的记录存储在Redis中（快速访问）
- 重要的记录（失败、关键任务）存储在数据库中（持久化）

### 配置示例

```yaml
# src/config/schedule.yml
schedule:
  execution_storage:
    type: 'database'  # 或 'redis' 或 'hybrid'
    database:
      retention_days: 7
      max_success_records_per_task: 3
      cleanup_cron: '0 0 4 * * *'
```

## 2. 日志优化

### 问题
频繁的DEBUG日志会影响性能和可读性。

### 解决方案

#### 控制日志频率
```typescript
private lastHealthLogTime = 0;

// 只在必要时记录日志（每小时最多一次）
const now = Date.now();
if (now - this.lastHealthLogTime > 3600000) {
  this.logger.log('服务健康状态缓存已更新');
  this.lastHealthLogTime = now;
}
```

#### 调整健康检查间隔
```typescript
// 从30秒调整为5分钟
HEALTH_CHECK_INTERVAL: 300000, // 5分钟
```

#### 使用合适的日志级别
- `DEBUG`: 开发调试信息
- `LOG`: 正常运行信息
- `WARN`: 警告信息
- `ERROR`: 错误信息

## 3. 性能优化

### 批量操作
```typescript
// 分批删除，避免长时间锁表
const batchSize = 1000;
while (true) {
  const recordsToDelete = await this.repository.find({
    take: batchSize,
    // ...
  });
  
  if (recordsToDelete.length === 0) break;
  
  await this.repository.delete(recordsToDelete.map(r => r.id));
  
  // 短暂休息，避免对数据库造成过大压力
  await new Promise(resolve => setTimeout(resolve, 100));
}
```

### 索引优化
```sql
-- 为清理任务添加索引
CREATE INDEX idx_task_executions_created_at ON task_executions(createdAt);
CREATE INDEX idx_task_executions_task_name_status ON task_executions(taskName, status);
```

### 异步处理
```typescript
// 使用队列处理耗时任务
@Cron('0 0 4 * * *')
async scheduleCleanup() {
  await this.queueService.add('cleanup-task-executions', {});
}
```

## 4. 监控和告警

### 任务执行监控
```typescript
// 监控任务执行状态
const stats = await this.scheduleService.getScheduleStats();
if (stats.todayFailureCount > 5) {
  await this.alertService.sendAlert('定时任务失败次数过多');
}
```

### 健康检查
```typescript
// 定期检查任务是否正常执行
@Cron('0 */10 * * * *') // 每10分钟检查
async checkTaskHealth() {
  const lastExecution = await this.getLastExecution('critical-task');
  const timeSinceLastRun = Date.now() - lastExecution.createdAt.getTime();
  
  if (timeSinceLastRun > 3600000) { // 超过1小时未执行
    await this.alertService.sendAlert('关键任务长时间未执行');
  }
}
```

## 5. 配置管理

### 环境配置
```yaml
# 开发环境
development:
  schedule:
    health_check:
      enabled: true
      interval: 60000  # 1分钟
    execution_storage:
      type: 'redis'

# 生产环境
production:
  schedule:
    health_check:
      enabled: true
      interval: 300000  # 5分钟
    execution_storage:
      type: 'database'
      retention_days: 30
```

### 动态配置
```typescript
// 支持运行时调整配置
@Post('config/update')
async updateConfig(@Body() config: ScheduleConfig) {
  await this.configService.updateScheduleConfig(config);
  await this.scheduleService.reloadConfig();
}
```

## 6. 错误处理

### 重试策略
```typescript
// 指数退避重试
async executeWithRetry(task: () => Promise<void>, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      await task();
      return;
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      
      const delay = Math.pow(2, i) * 1000; // 1s, 2s, 4s
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}
```

### 任务隔离
```typescript
// 确保单个任务失败不影响其他任务
@Cron('0 0 * * *')
async dailyTasks() {
  const tasks = [
    () => this.resetFreeQuota(),
    () => this.syncApiKeys(),
    () => this.cleanupData(),
  ];
  
  // 并行执行，互不影响
  await Promise.allSettled(tasks.map(task => task()));
}
```

## 7. 部署建议

### 集群环境
```typescript
// 使用分布式锁避免重复执行
@Cron('0 0 * * *')
async dailyTask() {
  const lockKey = 'daily-task-lock';
  const lock = await this.redisService.acquireLock(lockKey, 3600);
  
  if (!lock) {
    this.logger.warn('任务已在其他实例执行，跳过');
    return;
  }
  
  try {
    await this.executeTask();
  } finally {
    await this.redisService.releaseLock(lockKey);
  }
}
```

### 资源限制
```yaml
# Docker配置
services:
  app:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
```

## 8. 测试策略

### 单元测试
```typescript
describe('ScheduleService', () => {
  it('should cleanup old executions', async () => {
    // 创建测试数据
    await createTestExecutions();
    
    // 执行清理
    const deleted = await service.cleanupOldExecutions(7);
    
    // 验证结果
    expect(deleted).toBeGreaterThan(0);
  });
});
```

### 集成测试
```typescript
describe('Schedule Integration', () => {
  it('should execute all daily tasks', async () => {
    const stats = await service.getScheduleStats();
    expect(stats.activeTasks).toBeGreaterThan(0);
  });
});
```

## 总结

1. **存储策略**：根据业务需求选择合适的存储方案
2. **日志优化**：控制日志频率和级别
3. **性能优化**：使用批量操作和合适的索引
4. **监控告警**：及时发现和处理问题
5. **配置管理**：支持环境差异化配置
6. **错误处理**：实现重试和任务隔离
7. **部署优化**：考虑集群环境和资源限制
8. **测试覆盖**：确保功能正确性
