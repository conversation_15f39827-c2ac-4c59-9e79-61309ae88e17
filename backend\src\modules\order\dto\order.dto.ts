import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsEnum,
  IsNumber,
  IsString,
  IsOptional,
  IsArray,
  ValidateNested,
  IsPositive,
  Min,
  Max,
  Length,
  IsDateString,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { OrderStatus, OrderType, PaymentMethod } from '../enums/order.enum';

/**
 * 创建订单项DTO
 */
export class CreateOrderItemDto {
  @ApiPropertyOptional({ description: '服务ID', example: 1 })
  @IsOptional()
  @IsNumber()
  serviceId?: number;

  @ApiProperty({ description: '商品名称', example: 'OCR识别服务' })
  @IsString()
  @Length(1, 200)
  itemName: string;

  @ApiPropertyOptional({ description: '商品描述', example: '提供高精度OCR识别服务' })
  @IsOptional()
  @IsString()
  itemDescription?: string;

  @ApiProperty({ description: '单价', example: 0.10 })
  @IsNumber({ maxDecimalPlaces: 4 })
  @IsPositive()
  unitPrice: number;

  @ApiProperty({ description: '数量', example: 1000 })
  @IsNumber()
  @IsPositive()
  @Min(1)
  @Max(1000000)
  quantity: number;

  @ApiPropertyOptional({ description: '商品配置' })
  @IsOptional()
  itemConfig?: Record<string, any>;
}

/**
 * 创建服务订单DTO
 */
export class CreateServiceOrderDto {
  @ApiProperty({ description: '订单项列表', type: [CreateOrderItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateOrderItemDto)
  orderItems: CreateOrderItemDto[];

  @ApiPropertyOptional({ description: '优惠金额', example: 10.00 })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  discountAmount?: number;

  @ApiPropertyOptional({ description: '备注', example: '购买OCR服务' })
  @IsOptional()
  @IsString()
  @Length(0, 500)
  remark?: string;
}

/**
 * 查询订单DTO
 */
export class QueryOrderDto {
  @ApiPropertyOptional({ description: '页码', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', example: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({ description: '用户ID', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  userId?: number;

  @ApiPropertyOptional({ description: '订单号', example: 'ORD202312010001' })
  @IsOptional()
  @IsString()
  orderNo?: string;

  @ApiPropertyOptional({ description: '订单类型', enum: OrderType })
  @IsOptional()
  @IsEnum(OrderType)
  orderType?: OrderType;

  @ApiPropertyOptional({ description: '订单状态', enum: OrderStatus })
  @IsOptional()
  @IsEnum(OrderStatus)
  status?: OrderStatus;

  @ApiPropertyOptional({ description: '支付方式', enum: PaymentMethod })
  @IsOptional()
  @IsEnum(PaymentMethod)
  paymentMethod?: PaymentMethod;

  @ApiPropertyOptional({ description: '开始时间', example: '2023-12-01T00:00:00Z' })
  @IsOptional()
  @IsDateString()
  startTime?: string;

  @ApiPropertyOptional({ description: '结束时间', example: '2023-12-31T23:59:59Z' })
  @IsOptional()
  @IsDateString()
  endTime?: string;
}

/**
 * 更新订单DTO
 */
export class UpdateOrderDto extends PartialType(CreateServiceOrderDto) {
  @ApiPropertyOptional({ description: '订单状态', enum: OrderStatus })
  @IsOptional()
  @IsEnum(OrderStatus)
  status?: OrderStatus;

  @ApiPropertyOptional({ description: '支付方式', enum: PaymentMethod })
  @IsOptional()
  @IsEnum(PaymentMethod)
  paymentMethod?: PaymentMethod;

  @ApiPropertyOptional({ description: '已支付金额', example: 100.00 })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  paidAmount?: number;
}

/**
 * 订单项响应DTO
 */
export class OrderItemResponseDto {
  @ApiProperty({ description: '订单项ID', example: 1 })
  id: number;

  @ApiProperty({ description: '订单ID', example: 1 })
  orderId: number;

  @ApiPropertyOptional({ description: '服务ID', example: 1 })
  serviceId?: number;

  @ApiProperty({ description: '商品名称', example: 'OCR识别服务' })
  itemName: string;

  @ApiPropertyOptional({ description: '商品描述', example: '提供高精度OCR识别服务' })
  itemDescription?: string;

  @ApiProperty({ description: '单价', example: 0.10 })
  unitPrice: number;

  @ApiProperty({ description: '数量', example: 1000 })
  quantity: number;

  @ApiProperty({ description: '小计', example: 100.00 })
  totalPrice: number;

  @ApiPropertyOptional({ description: '商品配置' })
  itemConfig?: Record<string, any>;

  @ApiProperty({ description: '创建时间', example: '2023-12-01T10:00:00Z' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间', example: '2023-12-01T10:30:00Z' })
  updatedAt: Date;

  @ApiPropertyOptional({ description: '服务信息' })
  service?: {
    id: number;
    code: string;
    name: string;
    type: string;
    status: string;
  };
}

/**
 * 订单响应DTO
 */
export class OrderResponseDto {
  @ApiProperty({ description: '订单ID', example: 1 })
  id: number;

  @ApiProperty({ description: '用户ID', example: 1 })
  userId: number;

  @ApiProperty({ description: '订单号', example: 'ORD202312010001' })
  orderNo: string;

  @ApiProperty({ description: '订单类型', enum: OrderType, example: OrderType.SERVICE })
  orderType: OrderType;

  @ApiProperty({ description: '订单状态', enum: OrderStatus, example: OrderStatus.PENDING })
  status: OrderStatus;

  @ApiProperty({ description: '订单总金额', example: 100.00 })
  totalAmount: number;

  @ApiProperty({ description: '已支付金额', example: 100.00 })
  paidAmount: number;

  @ApiProperty({ description: '优惠金额', example: 10.00 })
  discountAmount: number;

  @ApiPropertyOptional({ description: '支付方式', enum: PaymentMethod })
  paymentMethod?: PaymentMethod;

  @ApiPropertyOptional({ description: '订单过期时间', example: '2023-12-01T12:00:00Z' })
  expiresAt?: Date;

  @ApiPropertyOptional({ description: '备注', example: '购买OCR服务' })
  remark?: string;

  @ApiProperty({ description: '创建时间', example: '2023-12-01T10:00:00Z' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间', example: '2023-12-01T10:30:00Z' })
  updatedAt: Date;

  @ApiProperty({ description: '订单项列表', type: [OrderItemResponseDto] })
  orderItems: OrderItemResponseDto[];

  @ApiPropertyOptional({ description: '用户信息' })
  user?: {
    id: number;
    username: string;
    email: string;
    realName?: string;
  };

  // 计算属性
  @ApiProperty({ description: '是否已过期', example: false })
  isExpired: boolean;

  @ApiProperty({ description: '是否可以支付', example: true })
  canPay: boolean;

  @ApiProperty({ description: '是否可以取消', example: true })
  canCancel: boolean;

  @ApiProperty({ description: '是否可以退款', example: false })
  canRefund: boolean;
}

/**
 * 订单列表响应DTO
 */
export class OrderListResponseDto {
  @ApiProperty({ description: '订单列表', type: [OrderResponseDto] })
  items: OrderResponseDto[];

  @ApiProperty({ description: '分页信息' })
  meta: {
    page: number;
    limit: number;
    totalItems: number;
    totalPages: number;
  };
}

/**
 * 订单统计DTO
 */
export class OrderStatsDto {
  @ApiProperty({ description: '总订单数', example: 1000 })
  totalOrders: number;

  @ApiProperty({ description: '待支付订单数', example: 50 })
  pendingOrders: number;

  @ApiProperty({ description: '已支付订单数', example: 800 })
  paidOrders: number;

  @ApiProperty({ description: '已完成订单数', example: 750 })
  completedOrders: number;

  @ApiProperty({ description: '已取消订单数', example: 100 })
  cancelledOrders: number;

  @ApiProperty({ description: '总交易金额', example: 50000.00 })
  totalAmount: number;

  @ApiProperty({ description: '今日订单数', example: 20 })
  todayOrders: number;

  @ApiProperty({ description: '今日交易金额', example: 1000.00 })
  todayAmount: number;

  @ApiProperty({ description: '按订单类型分组统计' })
  byOrderType: Record<string, {
    count: number;
    amount: number;
  }>;

  @ApiProperty({ description: '按支付方式分组统计' })
  byPaymentMethod: Record<string, {
    count: number;
    amount: number;
  }>;
}
