import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, IsDateString, MaxLength } from 'class-validator';

/**
 * 创建API密钥DTO
 */
export class CreateApiKeyDto {
  @ApiProperty({ 
    description: 'API密钥名称', 
    example: '开发环境密钥',
    required: false
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  name?: string;

  @ApiProperty({ 
    description: 'API密钥权限范围', 
    example: ['read:user', 'write:order'],
    required: false,
    type: [String]
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  scopes?: string[];

  @ApiProperty({ 
    description: '过期时间', 
    example: '2023-12-31T23:59:59Z',
    required: false
  })
  @IsDateString()
  @IsOptional()
  expiresAt?: string;

  @ApiProperty({ 
    description: '允许的IP地址', 
    example: ['***********', '10.0.0.*', '**********/16'],
    required: false,
    type: [String]
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  allowedIps?: string[];

  @ApiProperty({
    description: '关联的服务ID',
    example: 1,
    required: false
  })
  @IsOptional()
  serviceId?: number;

  @ApiProperty({
    description: '密钥类型',
    example: 'user',
    required: false,
    default: 'user'
  })
  @IsString()
  @IsOptional()
  keyType?: string;
} 