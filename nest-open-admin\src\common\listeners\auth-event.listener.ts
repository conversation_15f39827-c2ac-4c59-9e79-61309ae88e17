import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ApiKeyValidationRequestEvent, ApiKeyUsageUpdateEvent, UserInfoRequestEvent } from '../events/auth.events';
import { AuthService } from '../../modules/auth/auth.service';
import { UserService } from '../../modules/user/user.service';
import { ApiKeyService } from '../../modules/api-key/api-key.service';
import { EnhancedCacheManagerService } from '../../shared/enhanced-cache-manager.service';
import { RedisService } from '../../shared/redis.service';
import { UserRolePermissionUpdateEvent } from '../events/auth.events';
import { AuthSuccessEvent, AuthFailureEvent } from '../events/auth.events';

/**
 * 认证事件监听器
 * 处理认证相关的事件，实现业务逻辑与守卫的解耦
 */
@Injectable()
export class AuthEventListener {
  private readonly logger = new Logger(AuthEventListener.name);

  constructor(
    private readonly authService: AuthService,
    private readonly userService: UserService,
    private readonly apiKeyService: ApiKeyService,
    private readonly redisService: RedisService,
    private readonly cacheManager: EnhancedCacheManagerService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * 处理API密钥验证请求
   */
  @OnEvent('api-key.validation.request')
  async handleApiKeyValidationRequest(event: ApiKeyValidationRequestEvent): Promise<void> {
    try {
      // 从事件中获取API密钥和密钥秘钥
      const apiKey = event.apiKey;
      const secretKey = event.secretKey || null;
      
      // 验证API密钥
      const result = await this.authService.validateApiKey(apiKey, secretKey as string);

      if (result.isValid && result.apiKey) {
        // 获取用户信息
        const user = await this.userService.findById(result.apiKey.userId);
        
        // 发布验证成功事件
        this.eventEmitter.emit(
          'auth.success',
          {
            type: 'auth_success',
            data: {
              userId: result.apiKey.userId,
              authType: 'api-key',
              clientIp: event.clientIp
            }
          }
        );
        
        // 更新API密钥使用记录
        if (event.clientIp) {
          await this.apiKeyService.updateLastUsed(result.apiKey.id, event.clientIp);
        }
        
        // 返回结果
        if (event.callback) {
          event.callback({
            ...result,
            user: user || { id: result.apiKey.userId },
          });
        }
      } else {
        // 发布验证失败事件
        this.eventEmitter.emit(
          'auth.failure',
          {
            type: 'auth_failure',
            data: {
              identifier: apiKey,
              reason: result.error || 'API key validation failed',
              clientIp: event.clientIp
            }
          }
        );
        
        // 返回结果
        if (event.callback) {
          event.callback(result);
        }
      }
    } catch (error) {
      this.logger.error('处理API密钥验证请求失败:', error);
      
      // 返回错误结果
      if (event.callback) {
        event.callback({
          isValid: false,
          error: error.message || 'API key validation processing error',
        });
      }
    }
  }

  /**
   * 处理API密钥使用记录更新事件
   */
  @OnEvent('api-key.usage.update')
  async handleApiKeyUsageUpdate(event: ApiKeyUsageUpdateEvent): Promise<void> {
    try {
      await this.authService.updateApiKeyLastUsed(event.data.apiKey, event.data.clientIp);
      this.logger.debug(`API密钥使用记录已更新: ${event.data.apiKey}`);
    } catch (error) {
      this.logger.error('更新API密钥使用记录失败:', error);
    }
  }

  /**
   * 处理用户信息请求事件
   */
  @OnEvent('user.info.request')
  async handleUserInfoRequest(event: UserInfoRequestEvent): Promise<void> {
    try {
      const user = await this.userService.findById(event.userId);
      
      if (user) {
        event.callback({
          success: true,
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            roles: user.role ? [user.role] : [],
            permissions: [],
            isActive: user.userStatus === 'active',
            lastLoginAt: user.lastLoginAt,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
          },
        });
      } else {
        event.callback({
          success: false,
          error: 'User not found',
        });
      }
    } catch (error) {
      this.logger.error('获取用户信息失败:', error);
      event.callback({
        success: false,
        error: 'Failed to get user info',
      });
    }
  }

  /**
   * 处理用户角色权限更新事件
   */
  @OnEvent('user.role-permission.update')
  async handleUserRolePermissionUpdate(event: UserRolePermissionUpdateEvent): Promise<void> {
    try {
      const { userId, action } = event;
      
      if (action === 'clear') {
        // 清除用户角色权限相关缓存
        await this.clearUserRolePermissionCache(userId);
        this.logger.log(`用户 ${userId} 角色权限缓存已清除`);
      } else if (action === 'refresh') {
        // 刷新用户角色权限缓存
        await this.refreshUserRolePermissionCache(userId);
        this.logger.log(`用户 ${userId} 角色权限缓存已刷新`);
      }
    } catch (error) {
      this.logger.error(`处理用户角色权限更新失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 清除用户角色权限缓存
   */
  private async clearUserRolePermissionCache(userId: number): Promise<void> {
    try {
      const cacheKeys = [
        `user_roles:${userId}`,
        `user_permissions:${userId}`,
        `user:${userId}`, // 用户基本信息缓存也需要清除
      ];
      
      await Promise.all(cacheKeys.map(key => this.redisService.del(key)));
      
      // 清除用户相关的所有缓存
      await this.cacheManager.clearUserRelatedCache(userId);
    } catch (error) {
      this.logger.error(`清除用户 ${userId} 角色权限缓存失败: ${error.message}`);
    }
  }

  /**
   * 刷新用户角色权限缓存
   */
  private async refreshUserRolePermissionCache(userId: number): Promise<void> {
    try {
      // 先清除旧缓存
      await this.clearUserRolePermissionCache(userId);
      
      // 这里可以预加载用户的角色权限信息到缓存
      // 具体实现需要根据实际的角色权限系统来完成
      this.logger.debug(`用户 ${userId} 角色权限缓存刷新完成`);
    } catch (error) {
      this.logger.error(`刷新用户 ${userId} 角色权限缓存失败: ${error.message}`);
    }
  }
}