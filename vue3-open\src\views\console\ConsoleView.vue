<template>
  <div class="console-layout">
    <!-- 侧边栏 -->
    <div class="console-sidebar">
      <div class="sidebar-header">
        <h2>数智云链开放平台</h2>
      </div>

      <el-menu :default-active="activeMenu" mode="vertical" @select="handleMenuSelect" class="console-menu" router>
        <el-menu-item index="/console/dashboard">
          <el-icon>
            <Odometer />
          </el-icon>
          <span>仪表盘</span>
        </el-menu-item>

        <el-menu-item index="/console/api-keys">
          <el-icon>
            <Key />
          </el-icon>
          <span>API密钥</span>
        </el-menu-item>

        <el-menu-item index="/console/usage">
          <el-icon>
            <DataAnalysis />
          </el-icon>
          <span>使用统计</span>
        </el-menu-item>
        <!--         
        <el-menu-item index="/console/billing">
          <el-icon><Money /></el-icon>
          <span>账单管理</span>
        </el-menu-item>
        
        <el-menu-item index="/console/orders">
          <el-icon><Document /></el-icon>
          <span>订单管理</span>
        </el-menu-item>
        
        <el-menu-item index="/console/packages">
          <el-icon><Box /></el-icon>
          <span>我的套餐包</span>
        </el-menu-item>
        
        <el-menu-item index="/console/service-management">
          <el-icon><Setting /></el-icon>
          <span>服务管理</span>
        </el-menu-item>
        
        <!-- <el-menu-item index="/console/monitoring">
          <el-icon><Monitor /></el-icon>
          <span>系统监控</span>
        </el-menu-item> -->
        
        <el-menu-item index="/console/api-test">
          <el-icon><Promotion /></el-icon>
          <span>API测试工具</span>
        </el-menu-item>

        <el-menu-item index="/console/profile">
          <el-icon>
            <User />
          </el-icon>
          <span>个人资料</span>
        </el-menu-item>
      </el-menu>

      <div class="sidebar-footer">
        <el-button type="primary" @click="goToPlayground" style="width: 100%">
          <el-icon>
            <Position />
          </el-icon>
          在线测试
        </el-button>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="console-main">
      <div class="console-header">
        <div class="header-left">
          <el-breadcrumb separator=">">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>开放平台</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <div class="header-right">
          <el-dropdown @command="handleUserAction">
            <span class="user-info">
              <el-avatar :size="32" :src="userStore.userInfo?.avatar">
                {{ userStore.userInfo?.username?.charAt(0).toUpperCase() }}
              </el-avatar>
              <span class="username">{{ userStore.userInfo?.username }}</span>
              <el-icon>
                <ArrowDown />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon>
                    <User />
                  </el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon>
                    <Setting />
                  </el-icon>
                  账号设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon>
                    <SwitchButton />
                  </el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <div class="console-content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Odometer,
  Key,
  DataAnalysis,
  Money,
  User,
  Promotion,
  ArrowDown,
  Setting,
  SwitchButton,
  Document,
  Monitor,
  Box
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const activeMenu = computed(() => route.path)

// 当前页面标题
const currentPageTitle = computed(() => {
  const titleMap: Record<string, string> = {
    '/console/dashboard': '仪表盘',
    '/console/api-keys': 'API密钥',
    '/console/usage': '使用统计',
    '/console/billing': '账单管理',
    '/console/profile': '个人资料',
            // '/console/monitoring': '系统监控',
        '/console/api-test': 'API测试工具',
    '/console/orders': '订单管理',
    '/console/packages': '我的套餐包',
    '/console/service-management': '服务管理',
  }
  return titleMap[route.path] || '控制台'
})

// 处理菜单选择
const handleMenuSelect = (index: string) => {
  router.push(index)
}

// 前往API测试
const goToPlayground = () => {
  router.push('/playground')
}

// 处理用户操作
const handleUserAction = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/console/profile')
      break
    case 'settings':
      ElMessage.info('账号设置功能开发中')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        await userStore.logout()
        router.push('/login')
        ElMessage.success('已退出登录')
      } catch {
        // 用户取消
      }
      break
  }
}

// 初始化
onMounted(() => {
  // 检查用户登录状态
  if (!userStore.isLoggedIn) {
    router.push('/login')
    return
  }

  // 如果是控制台根路径，重定向到仪表盘
  if (route.path === '/console' || route.path === '/console/') {
    router.replace('/console/dashboard')
  }
})
</script>

<style scoped>
.console-layout {
  display: flex;
  min-height: 100vh;
  background: #f5f7fa;
}

.console-sidebar {
  width: 250px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  z-index: 1000;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
  height: 60px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.sidebar-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.console-menu {
  flex: 1;
  border: none;
  padding: 20px 0;
}

.console-menu .el-menu-item {
  margin: 0 16px 8px 16px;
  border-radius: 8px;
  height: 48px;
  line-height: 48px;
}

.console-menu .el-menu-item:hover {
  background: #f0f2f5;
}

.console-menu .el-menu-item.is-active {
  background: #409eff;
  color: white;
}

.console-menu .el-menu-item.is-active .el-icon {
  color: white;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

.console-main {
  flex: 1;
  margin-left: 250px;
  display: flex;
  flex-direction: column;
}

.console-header {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  top: 0;
  left: 250px;
  right: 0;
  height: 60px;
  z-index: 999;
  box-sizing: border-box;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background: #f0f2f5;
}

.username {
  font-weight: 500;
  color: #303133;
}

.console-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .console-sidebar {
    width: 200px;
    transform: translateX(-100%);
    transition: transform 0.3s;
  }

  .console-sidebar.mobile-open {
    transform: translateX(0);
  }

  .console-main {
    margin-left: 0;
  }

  .console-header {
    left: 0;
    padding: 12px 16px;
    height: 56px;
  }

  .console-content {
    padding: 16px;
    margin-top: 56px;
  }

  .sidebar-header {
    height: 56px;
  }

  .sidebar-header h2 {
    font-size: 1.25rem;
  }

  .console-menu .el-menu-item {
    margin: 0 12px 6px 12px;
    height: 44px;
    line-height: 44px;
  }
}

@media (max-width: 480px) {
  .console-sidebar {
    width: 100%;
  }

  .console-header {
    padding: 12px 16px;
    height: 52px;
  }

  .console-content {
    margin-top: 52px;
  }

  .sidebar-header {
    height: 52px;
  }

  .user-info {
    padding: 6px 8px;
  }

  .username {
    display: none;
  }
}
</style>