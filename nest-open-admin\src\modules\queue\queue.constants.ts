/**
 * 队列常量定义
 * 定义队列名称、任务类型等常量
 */

// 队列名称
export const QUEUE_NAMES = {
  API_CALL: 'api-call',
  ALERT: 'alert', // 告警
  OCR: 'ocr', // 面单OCR识别
  EXTRACT_ADDRESS: 'extract-address', // 提取地址
  REV_GEO: 'rev-geo', // 坐标逆解析地址
} as const;

// 任务类型
export const TASK_TYPES = {
  API_CALL: 'api_call',
  USAGE_ALERT: 'usage_alert',
  OCR_RECOGNITION: 'ocr_recognition_task',
  ADDRESS_EXTRACTION: 'address_extraction',
  REVERSE_GEOCODING: 'reverse_geocoding',
} as const;

// 任务优先级
export const TASK_PRIORITY = {
  LOW: 1,
  NORMAL: 5,
  HIGH: 10,
  CRITICAL: 15,
} as const;

// 任务状态
export const TASK_STATUS = {
  WAITING: 'waiting',
  ACTIVE: 'active',
  COMPLETED: 'completed',
  FAILED: 'failed',
  DELAYED: 'delayed',
  PAUSED: 'paused',
} as const;

// 告警级别
export const ALERT_LEVELS = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
  CRITICAL: 'critical',
} as const;

// 告警类型
export const ALERT_TYPES = {
  USAGE_WARNING: 'usage_warning',
  SYSTEM_ERROR: 'system_error',
  API_LIMIT: 'api_limit',
} as const;

// 优先级常量
export const PRIORITY_LEVELS = {
  LOW: 1,
  NORMAL: 5,
  HIGH: 10,
  CRITICAL: 15,
} as const;

export type QueueName = typeof QUEUE_NAMES[keyof typeof QUEUE_NAMES];
export type TaskType = typeof TASK_TYPES[keyof typeof TASK_TYPES];
export type TaskPriority = typeof TASK_PRIORITY[keyof typeof TASK_PRIORITY];
export type TaskStatus = typeof TASK_STATUS[keyof typeof TASK_STATUS];
export type AlertLevel = typeof ALERT_LEVELS[keyof typeof ALERT_LEVELS];
export type AlertType = typeof ALERT_TYPES[keyof typeof ALERT_TYPES];