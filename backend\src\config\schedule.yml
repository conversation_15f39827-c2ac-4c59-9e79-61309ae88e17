# 定时任务配置
schedule:
  # 任务执行记录存储策略
  execution_storage:
    # 存储类型: 'database' | 'redis' | 'hybrid'
    type: 'database'
    
    # 数据库存储配置
    database:
      # 保留天数（只保留最近N天的记录）
      retention_days: 7
      # 每个任务保留的成功记录数量
      max_success_records_per_task: 3
      # 是否保留所有失败记录
      keep_all_failed_records: true
      # 清理任务执行间隔（cron表达式）
      cleanup_cron: '0 0 4 * * *'  # 每天凌晨4点
    
    # Redis存储配置
    redis:
      # 执行记录TTL（秒）
      execution_ttl: 604800  # 7天
      # 统计信息TTL（秒）
      stats_ttl: 2592000     # 30天
      # 最近执行记录TTL（秒）
      recent_ttl: 86400      # 1天
      # 最近执行记录保留数量
      recent_limit: 20

  # 健康检查配置
  health_check:
    # 是否启用健康检查
    enabled: true
    # 检查间隔（毫秒）
    interval: 300000  # 5分钟
    # 日志记录间隔（毫秒）
    log_interval: 3600000  # 1小时

  # 任务执行配置
  tasks:
    # 每日免费额度重置
    reset_daily_free_quota:
      enabled: true
      cron: '0 0 * * *'
      max_retries: 3
      
    # API密钥缓存同步
    sync_api_keys_to_cache:
      enabled: true
      cron: '0 */30 * * * *'
      max_retries: 3
      
    # 过期数据清理
    cleanup_expired_data:
      enabled: true
      cron: '0 2 * * *'
      max_retries: 3
      
    # 每日调用统计生成
    generate_daily_statistics:
      enabled: true
      cron: '0 0 2 * * *'
      max_retries: 3
      
    # 调用记录清理
    cleanup_call_records:
      enabled: true
      cron: '0 30 3 1 1,3,5,7,9,11 *'
      max_retries: 3
      
    # 任务执行记录清理
    cleanup_task_executions:
      enabled: true
      cron: '0 0 4 * * *'
      max_retries: 3

  # 性能优化配置
  performance:
    # 批量操作大小
    batch_size: 1000
    # 批量操作间隔（毫秒）
    batch_interval: 100
    # 最大并发任务数
    max_concurrent_tasks: 5
