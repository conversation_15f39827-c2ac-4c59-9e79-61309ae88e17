@import './base.css';

#app {
  width: 100%;
  min-height: 100vh;
  font-weight: normal;
}

a {
  text-decoration: none;
  color: var(--color-primary);
  transition: all 0.3s ease;
}

a:hover {
  color: var(--color-primary-dark);
}

/* Element Plus 主题定制 */
:root {
  --el-color-primary: #3b82f6;
  --el-color-primary-light-3: #93c5fd;
  --el-color-primary-light-5: #bfdbfe;
  --el-color-primary-light-7: #dbeafe;
  --el-color-primary-light-8: #eff6ff;
  --el-color-primary-light-9: #f8fafc;
  --el-color-primary-dark-2: #1e40af;
}

/* 通用工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}
