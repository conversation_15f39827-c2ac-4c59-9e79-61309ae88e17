/**
 * 告警处理器
 * 处理调用次数预警等告警任务
 */

import { Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bull';
import { QUEUE_NAMES } from '../queue.constants';
import { AlertTaskData, TaskResult } from '../interfaces/queue.interface';

@Injectable()
@Processor(QUEUE_NAMES.ALERT)
export class AlertProcessor {
  private readonly logger = new Logger(AlertProcessor.name);

  @Process('alert')
  async handleAlert(job: Job<AlertTaskData>): Promise<TaskResult> {
    const { data } = job;
    const startTime = Date.now();

    this.logger.log(`开始处理告警任务: ${job.id}, 类型: ${data.alertType}, 级别: ${data.level}`);

    try {
      // 更新任务进度
      await job.progress(10);

      // 验证告警数据
      this.validateAlertData(data);
      await job.progress(20);

      // 处理告警逻辑
      const result = await this.processAlert(data, job);
      await job.progress(100);

      const duration = Date.now() - startTime;
      this.logger.log(`告警任务完成: ${job.id}, 耗时: ${duration}ms`);

      return {
        success: true,
        data: result,
        duration,
        retryCount: job.attemptsMade,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`告警任务失败: ${job.id}, 错误: ${error.message}`, error.stack);

      return {
        success: false,
        error: error.message,
        duration,
        retryCount: job.attemptsMade,
      };
    }
  }

  /**
   * 验证告警数据
   */
  private validateAlertData(data: AlertTaskData): void {
    // 如果数据为空或不完整，使用默认值
    if (!data) {
      throw new Error('告警数据不能为空');
    }

    // 设置默认值
    data.userId = data.userId || 0; // 使用0表示系统用户
    data.alertType = data.alertType || 'system_error';
    data.level = data.level || 'info';
    data.title = data.title || '系统告警';
    data.message = data.message || '未知告警';

    const validLevels = ['info', 'warning', 'error', 'critical'];
    if (!validLevels.includes(data.level)) {
      this.logger.warn(`无效的告警级别: ${data.level}，使用默认级别: info`);
      data.level = 'info';
    }

    const validTypes = ['usage_warning', 'system_error', 'api_limit'];
    if (!validTypes.includes(data.alertType)) {
      this.logger.warn(`无效的告警类型: ${data.alertType}，使用默认类型: system_error`);
      data.alertType = 'system_error';
    }
  }

  /**
   * 处理告警
   */
  private async processAlert(data: AlertTaskData, job: Job): Promise<any> {
    await job.progress(30);

    // 根据告警类型处理
    switch (data.alertType) {
      case 'usage_warning':
        return await this.handleUsageWarning(data, job);
      case 'system_error':
        return await this.handleSystemError(data, job);
      case 'api_limit':
        return await this.handleApiLimit(data, job);
      default:
        throw new Error(`未支持的告警类型: ${data.alertType}`);
    }
  }

  /**
   * 处理使用量预警
   */
  private async handleUsageWarning(data: AlertTaskData, job: Job): Promise<any> {
    this.logger.log(`处理使用量预警: 用户${data.userId}, 级别: ${data.level}`);
    
    await job.progress(50);

    // 记录告警信息
    const alertRecord = {
      userId: data.userId,
      type: data.alertType,
      level: data.level,
      title: data.title,
      message: data.message,
      metadata: data.metadata,
      createdAt: new Date(),
    };

    await job.progress(70);

    // 发送通知
    const notifications = await this.sendNotifications(data, job);

    await job.progress(90);

    return {
      alertRecord,
      notifications,
      processed: true,
    };
  }

  /**
   * 处理系统错误告警
   */
  private async handleSystemError(data: AlertTaskData, job: Job): Promise<any> {
    this.logger.error(`系统错误告警: ${data.title} - ${data.message}`);
    
    await job.progress(50);

    // 记录系统错误
    const errorRecord = {
      userId: data.userId,
      type: data.alertType,
      level: data.level,
      title: data.title,
      message: data.message,
      metadata: data.metadata,
      createdAt: new Date(),
    };

    await job.progress(70);

    // 发送紧急通知
    const notifications = await this.sendNotifications(data, job);

    await job.progress(90);

    return {
      errorRecord,
      notifications,
      processed: true,
    };
  }

  /**
   * 处理API限制告警
   */
  private async handleApiLimit(data: AlertTaskData, job: Job): Promise<any> {
    this.logger.warn(`API限制告警: 用户${data.userId}, ${data.message}`);
    
    await job.progress(50);

    // 记录API限制信息
    const limitRecord = {
      userId: data.userId,
      type: data.alertType,
      level: data.level,
      title: data.title,
      message: data.message,
      metadata: data.metadata,
      createdAt: new Date(),
    };

    await job.progress(70);

    // 发送限制通知
    const notifications = await this.sendNotifications(data, job);

    await job.progress(90);

    return {
      limitRecord,
      notifications,
      processed: true,
    };
  }

  /**
   * 发送通知
   */
  private async sendNotifications(data: AlertTaskData, job: Job): Promise<any[]> {
    const notifications:Record<string,any>[] = [];
    const channels = data.channels || ['email']; // 默认邮件通知

    for (const channel of channels) {
      try {
        const notification = await this.sendNotification(channel, data);
        notifications.push({
          channel,
          success: true,
          data: notification,
        });
      } catch (error) {
        this.logger.error(`发送${channel}通知失败: ${error.message}`);
        notifications.push({
          channel,
          success: false,
          error: error.message,
        });
      }
    }

    return notifications;
  }

  /**
   * 发送单个通知
   */
  private async sendNotification(channel: string, data: AlertTaskData): Promise<any> {
    switch (channel) {
      case 'email':
        return await this.sendEmailNotification(data);
      case 'sms':
        return await this.sendSmsNotification(data);
      case 'webhook':
        return await this.sendWebhookNotification(data);
      default:
        throw new Error(`不支持的通知渠道: ${channel}`);
    }
  }

  /**
   * 发送邮件通知
   */
  private async sendEmailNotification(data: AlertTaskData): Promise<any> {
    // 模拟邮件发送
    this.logger.log(`发送邮件通知给用户${data.userId}: ${data.title}`);
    
    // 这里应该调用实际的邮件服务
    return {
      type: 'email',
      recipient: `user_${data.userId}@example.com`,
      subject: data.title,
      content: data.message,
      sentAt: new Date(),
    };
  }

  /**
   * 发送短信通知
   */
  private async sendSmsNotification(data: AlertTaskData): Promise<any> {
    // 模拟短信发送
    this.logger.log(`发送短信通知给用户${data.userId}: ${data.title}`);
    
    // 这里应该调用实际的短信服务
    return {
      type: 'sms',
      recipient: `user_${data.userId}`,
      content: `${data.title}: ${data.message}`,
      sentAt: new Date(),
    };
  }

  /**
   * 发送Webhook通知
   */
  private async sendWebhookNotification(data: AlertTaskData): Promise<any> {
    // 模拟Webhook发送
    this.logger.log(`发送Webhook通知: ${data.title}`);
    
    // 这里应该调用实际的Webhook服务
    return {
      type: 'webhook',
      payload: {
        userId: data.userId,
        alertType: data.alertType,
        level: data.level,
        title: data.title,
        message: data.message,
        metadata: data.metadata,
      },
      sentAt: new Date(),
    };
  }
}