<template>
  <div class="monitor-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">系统监控</h1>
      <p class="page-subtitle">实时监控系统状态和性能指标</p>
      
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        
        <el-select v-model="timeRange" @change="handleTimeRangeChange" style="width: 150px;">
          <el-option label="最近1小时" value="1h" />
          <el-option label="最近6小时" value="6h" />
          <el-option label="最近24小时" value="24h" />
          <el-option label="最近7天" value="7d" />
        </el-select>
      </div>
    </div>

    <!-- 系统状态概览 -->
    <div class="status-overview">
      <div class="status-cards">
        <div class="status-card" :class="{ error: systemStatus.overall !== 'healthy' }">
          <div class="status-icon">
            <el-icon :size="32" :color="getStatusColor(systemStatus.overall)">
              <component :is="getStatusIcon(systemStatus.overall)" />
            </el-icon>
          </div>
          <div class="status-info">
            <h3>系统状态</h3>
            <p class="status-text">{{ getStatusText(systemStatus.overall) }}</p>
            <span class="status-time">最后更新：{{ formatTime(systemStatus.lastUpdate) }}</span>
          </div>
        </div>
        
        <div class="status-card">
          <div class="status-icon">
            <el-icon :size="32" color="#10b981">
              <Monitor />
            </el-icon>
          </div>
          <div class="status-info">
            <h3>在线服务</h3>
            <p class="status-number">{{ systemStatus.activeServices }}/{{ systemStatus.totalServices }}</p>
            <span class="status-desc">服务正常运行</span>
          </div>
        </div>
        
        <div class="status-card">
          <div class="status-icon">
            <el-icon :size="32" color="#3b82f6">
              <Connection />
            </el-icon>
          </div>
          <div class="status-info">
            <h3>API调用</h3>
            <p class="status-number">{{ formatNumber(systemStatus.apiCalls) }}</p>
            <span class="status-desc">今日总调用量</span>
          </div>
        </div>
        
        <div class="status-card">
          <div class="status-icon">
            <el-icon :size="32" color="#f59e0b">
              <Warning />
            </el-icon>
          </div>
          <div class="status-info">
            <h3>告警数量</h3>
            <p class="status-number">{{ systemStatus.alerts }}</p>
            <span class="status-desc">需要关注的问题</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 性能指标 -->
    <div class="metrics-section">
      <div class="section-header">
        <h2 class="section-title">性能指标</h2>
        <div class="metric-tabs">
          <el-tabs v-model="activeMetricTab" @tab-change="handleMetricTabChange">
            <el-tab-pane label="系统资源" name="system" />
            <el-tab-pane label="API性能" name="api" />
            <el-tab-pane label="数据库" name="database" />
            <el-tab-pane label="缓存" name="cache" />
          </el-tabs>
        </div>
      </div>
      
      <!-- 系统资源指标 -->
      <div v-if="activeMetricTab === 'system'" class="metrics-grid">
        <div class="metric-card">
          <div class="metric-header">
            <h3>CPU使用率</h3>
            <span class="metric-value">{{ metrics.system.cpu }}%</span>
          </div>
          <div class="metric-chart">
            <el-progress 
              :percentage="metrics.system.cpu" 
              :color="getProgressColor(metrics.system.cpu)"
              :stroke-width="8"
            />
          </div>
          <div class="metric-details">
            <div class="detail-item">
              <span>平均负载：{{ metrics.system.loadAverage }}</span>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <h3>内存使用率</h3>
            <span class="metric-value">{{ metrics.system.memory }}%</span>
          </div>
          <div class="metric-chart">
            <el-progress 
              :percentage="metrics.system.memory" 
              :color="getProgressColor(metrics.system.memory)"
              :stroke-width="8"
            />
          </div>
          <div class="metric-details">
            <div class="detail-item">
              <span>已用：{{ formatBytes(metrics.system.memoryUsed) }}</span>
            </div>
            <div class="detail-item">
              <span>总计：{{ formatBytes(metrics.system.memoryTotal) }}</span>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <h3>磁盘使用率</h3>
            <span class="metric-value">{{ metrics.system.disk }}%</span>
          </div>
          <div class="metric-chart">
            <el-progress 
              :percentage="metrics.system.disk" 
              :color="getProgressColor(metrics.system.disk)"
              :stroke-width="8"
            />
          </div>
          <div class="metric-details">
            <div class="detail-item">
              <span>可用：{{ formatBytes(metrics.system.diskFree) }}</span>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <h3>网络流量</h3>
            <span class="metric-value">{{ formatBytes(metrics.system.networkIn + metrics.system.networkOut) }}/s</span>
          </div>
          <div class="metric-details">
            <div class="detail-item">
              <span>入站：{{ formatBytes(metrics.system.networkIn) }}/s</span>
            </div>
            <div class="detail-item">
              <span>出站：{{ formatBytes(metrics.system.networkOut) }}/s</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- API性能指标 -->
      <div v-if="activeMetricTab === 'api'" class="metrics-grid">
        <div class="metric-card">
          <div class="metric-header">
            <h3>请求响应时间</h3>
            <span class="metric-value">{{ metrics.api.avgResponseTime }}ms</span>
          </div>
          <div class="metric-details">
            <div class="detail-item">
              <span>P95：{{ metrics.api.p95ResponseTime }}ms</span>
            </div>
            <div class="detail-item">
              <span>P99：{{ metrics.api.p99ResponseTime }}ms</span>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <h3>请求成功率</h3>
            <span class="metric-value">{{ metrics.api.successRate }}%</span>
          </div>
          <div class="metric-chart">
            <el-progress 
              :percentage="metrics.api.successRate" 
              :color="getProgressColor(metrics.api.successRate, true)"
              :stroke-width="8"
            />
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <h3>QPS</h3>
            <span class="metric-value">{{ formatNumber(metrics.api.qps) }}</span>
          </div>
          <div class="metric-details">
            <div class="detail-item">
              <span>峰值：{{ formatNumber(metrics.api.peakQps) }}</span>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <h3>错误率</h3>
            <span class="metric-value">{{ metrics.api.errorRate }}%</span>
          </div>
          <div class="metric-chart">
            <el-progress 
              :percentage="metrics.api.errorRate" 
              :color="getProgressColor(metrics.api.errorRate, false, true)"
              :stroke-width="8"
            />
          </div>
        </div>
      </div>
      
      <!-- 数据库指标 -->
      <div v-if="activeMetricTab === 'database'" class="metrics-grid">
        <div class="metric-card">
          <div class="metric-header">
            <h3>连接数</h3>
            <span class="metric-value">{{ metrics.database.connections }}/{{ metrics.database.maxConnections }}</span>
          </div>
          <div class="metric-chart">
            <el-progress 
              :percentage="(metrics.database.connections / metrics.database.maxConnections) * 100" 
              :color="getProgressColor((metrics.database.connections / metrics.database.maxConnections) * 100)"
              :stroke-width="8"
            />
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <h3>查询响应时间</h3>
            <span class="metric-value">{{ metrics.database.avgQueryTime }}ms</span>
          </div>
          <div class="metric-details">
            <div class="detail-item">
              <span>慢查询：{{ metrics.database.slowQueries }}</span>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <h3>缓存命中率</h3>
            <span class="metric-value">{{ metrics.database.cacheHitRate }}%</span>
          </div>
          <div class="metric-chart">
            <el-progress 
              :percentage="metrics.database.cacheHitRate" 
              :color="getProgressColor(metrics.database.cacheHitRate, true)"
              :stroke-width="8"
            />
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <h3>锁等待</h3>
            <span class="metric-value">{{ metrics.database.lockWaits }}</span>
          </div>
          <div class="metric-details">
            <div class="detail-item">
              <span>死锁：{{ metrics.database.deadlocks }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 缓存指标 -->
      <div v-if="activeMetricTab === 'cache'" class="metrics-grid">
        <div class="metric-card">
          <div class="metric-header">
            <h3>Redis连接</h3>
            <span class="metric-value">{{ metrics.cache.connections }}</span>
          </div>
          <div class="metric-details">
            <div class="detail-item">
              <span>状态：{{ metrics.cache.status }}</span>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <h3>内存使用</h3>
            <span class="metric-value">{{ formatBytes(metrics.cache.memoryUsed) }}</span>
          </div>
          <div class="metric-details">
            <div class="detail-item">
              <span>峰值：{{ formatBytes(metrics.cache.memoryPeak) }}</span>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <h3>命中率</h3>
            <span class="metric-value">{{ metrics.cache.hitRate }}%</span>
          </div>
          <div class="metric-chart">
            <el-progress 
              :percentage="metrics.cache.hitRate" 
              :color="getProgressColor(metrics.cache.hitRate, true)"
              :stroke-width="8"
            />
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-header">
            <h3>操作/秒</h3>
            <span class="metric-value">{{ formatNumber(metrics.cache.opsPerSec) }}</span>
          </div>
          <div class="metric-details">
            <div class="detail-item">
              <span>读：{{ formatNumber(metrics.cache.reads) }}</span>
            </div>
            <div class="detail-item">
              <span>写：{{ formatNumber(metrics.cache.writes) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 服务状态 -->
    <div class="services-section">
      <div class="section-header">
        <h2 class="section-title">服务状态</h2>
        <el-button @click="checkAllServices" :loading="checkingServices">
          <el-icon><Refresh /></el-icon>
          检查所有服务
        </el-button>
      </div>
      
      <div class="services-grid">
        <div 
          v-for="service in services" 
          :key="service.name"
          class="service-card"
          :class="{ error: service.status !== 'healthy' }"
        >
          <div class="service-header">
            <div class="service-info">
              <h3 class="service-name">{{ service.name }}</h3>
              <span class="service-version">v{{ service.version }}</span>
            </div>
            <div class="service-status">
              <el-tag :type="getServiceStatusType(service.status)" size="small">
                {{ getServiceStatusText(service.status) }}
              </el-tag>
            </div>
          </div>
          
          <div class="service-metrics">
            <div class="service-metric">
              <span class="metric-label">响应时间</span>
              <span class="metric-value">{{ service.responseTime }}ms</span>
            </div>
            <div class="service-metric">
              <span class="metric-label">CPU</span>
              <span class="metric-value">{{ service.cpu }}%</span>
            </div>
            <div class="service-metric">
              <span class="metric-label">内存</span>
              <span class="metric-value">{{ formatBytes(service.memory) }}</span>
            </div>
          </div>
          
          <div class="service-actions">
            <el-button size="small" @click="viewServiceLogs(service.name)">
              查看日志
            </el-button>
            <el-button size="small" @click="restartService(service.name)" :disabled="service.status === 'restarting'">
              重启服务
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 告警列表 -->
    <div class="alerts-section">
      <div class="section-header">
        <h2 class="section-title">系统告警</h2>
        <div class="alert-filters">
          <el-select v-model="alertFilter" @change="filterAlerts" style="width: 120px;">
            <el-option label="全部" value="all" />
            <el-option label="严重" value="critical" />
            <el-option label="警告" value="warning" />
            <el-option label="信息" value="info" />
          </el-select>
        </div>
      </div>
      
      <div class="alerts-list">
        <div 
          v-for="alert in filteredAlerts" 
          :key="alert.id"
          class="alert-item"
          :class="alert.level"
        >
          <div class="alert-icon">
            <el-icon :size="20">
              <component :is="getAlertIcon(alert.level)" />
            </el-icon>
          </div>
          
          <div class="alert-content">
            <div class="alert-header">
              <h4 class="alert-title">{{ alert.title }}</h4>
              <span class="alert-time">{{ formatTime(alert.timestamp) }}</span>
            </div>
            <p class="alert-message">{{ alert.message }}</p>
            <div class="alert-meta">
              <span class="alert-source">来源：{{ alert.source }}</span>
              <span class="alert-level">级别：{{ getAlertLevelText(alert.level) }}</span>
            </div>
          </div>
          
          <div class="alert-actions">
            <el-button size="small" @click="acknowledgeAlert(alert.id)" v-if="!alert.acknowledged">
              确认
            </el-button>
            <el-button size="small" @click="resolveAlert(alert.id)" v-if="!alert.resolved">
              解决
            </el-button>
          </div>
        </div>
        
        <div v-if="filteredAlerts.length === 0" class="empty-alerts">
          <el-empty description="暂无告警信息" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  Monitor,
  Connection,
  Warning,
  CircleCheck,
  CircleClose,
  QuestionFilled,
  InfoFilled,
  WarningFilled,
  CircleCloseFilled
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const checkingServices = ref(false)
const timeRange = ref('24h')
const activeMetricTab = ref('system')
const alertFilter = ref('all')
const refreshInterval = ref<any | null>(null)

// 系统状态
const systemStatus = reactive({
  overall: 'healthy',
  lastUpdate: new Date(),
  activeServices: 12,
  totalServices: 15,
  apiCalls: 1234567,
  alerts: 3
})

// 性能指标
const metrics = reactive({
  system: {
    cpu: 45,
    memory: 68,
    disk: 32,
    loadAverage: 1.2,
    memoryUsed: 6.8 * 1024 * 1024 * 1024,
    memoryTotal: 10 * 1024 * 1024 * 1024,
    diskFree: 500 * 1024 * 1024 * 1024,
    networkIn: 1024 * 1024,
    networkOut: 2 * 1024 * 1024
  },
  api: {
    avgResponseTime: 120,
    p95ResponseTime: 250,
    p99ResponseTime: 500,
    successRate: 99.5,
    qps: 1500,
    peakQps: 3000,
    errorRate: 0.5
  },
  database: {
    connections: 45,
    maxConnections: 100,
    avgQueryTime: 15,
    slowQueries: 2,
    cacheHitRate: 95,
    lockWaits: 0,
    deadlocks: 0
  },
  cache: {
    connections: 25,
    status: '正常',
    memoryUsed: 512 * 1024 * 1024,
    memoryPeak: 800 * 1024 * 1024,
    hitRate: 98,
    opsPerSec: 5000,
    reads: 3000,
    writes: 2000
  }
})

// 服务列表
const services = ref([
  {
    name: 'API Gateway',
    version: '1.2.3',
    status: 'healthy',
    responseTime: 45,
    cpu: 25,
    memory: 512 * 1024 * 1024
  },
  {
    name: 'OCR Service',
    version: '2.1.0',
    status: 'healthy',
    responseTime: 120,
    cpu: 60,
    memory: 1024 * 1024 * 1024
  },
  {
    name: 'NLP Service',
    version: '1.5.2',
    status: 'warning',
    responseTime: 200,
    cpu: 80,
    memory: 2048 * 1024 * 1024
  },
  {
    name: 'Database',
    version: '8.0.32',
    status: 'healthy',
    responseTime: 15,
    cpu: 35,
    memory: 4096 * 1024 * 1024
  },
  {
    name: 'Redis Cache',
    version: '7.0.8',
    status: 'healthy',
    responseTime: 2,
    cpu: 10,
    memory: 256 * 1024 * 1024
  },
  {
    name: 'File Storage',
    version: '1.0.5',
    status: 'error',
    responseTime: 0,
    cpu: 0,
    memory: 0
  }
])

// 告警列表
const alerts = ref([
  {
    id: '1',
    title: 'NLP服务CPU使用率过高',
    message: 'NLP服务CPU使用率达到80%，建议检查服务负载',
    level: 'warning',
    source: 'NLP Service',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    acknowledged: false,
    resolved: false
  },
  {
    id: '2',
    title: '文件存储服务离线',
    message: '文件存储服务无法连接，请检查服务状态',
    level: 'critical',
    source: 'File Storage',
    timestamp: new Date(Date.now() - 15 * 60 * 1000),
    acknowledged: false,
    resolved: false
  },
  {
    id: '3',
    title: '数据库慢查询增加',
    message: '检测到2个慢查询，建议优化SQL语句',
    level: 'info',
    source: 'Database',
    timestamp: new Date(Date.now() - 60 * 60 * 1000),
    acknowledged: true,
    resolved: false
  }
])

// 计算属性
const filteredAlerts = computed(() => {
  if (alertFilter.value === 'all') {
    return alerts.value
  }
  return alerts.value.filter(alert => alert.level === alertFilter.value)
})

// 方法
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    healthy: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    unknown: '#6b7280'
  }
  return colors[status] || colors.unknown
}

const getStatusIcon = (status: string) => {
  const icons: Record<string, any> = {
    healthy: CircleCheck,
    warning: Warning,
    error: CircleClose,
    unknown: QuestionFilled
  }
  return icons[status] || icons.unknown
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    healthy: '系统正常',
    warning: '存在警告',
    error: '系统异常',
    unknown: '状态未知'
  }
  return texts[status] || texts.unknown
}

const getProgressColor = (value: number, isSuccessRate = false, isErrorRate = false) => {
  if (isErrorRate) {
    if (value <= 1) return '#10b981'
    if (value <= 5) return '#f59e0b'
    return '#ef4444'
  }
  
  if (isSuccessRate) {
    if (value >= 99) return '#10b981'
    if (value >= 95) return '#f59e0b'
    return '#ef4444'
  }
  
  if (value <= 60) return '#10b981'
  if (value <= 80) return '#f59e0b'
  return '#ef4444'
}

const getServiceStatusType = (status: string) => {
  const types: Record<string, string> = {
    healthy: 'success',
    warning: 'warning',
    error: 'danger',
    restarting: 'info'
  }
  return types[status] || 'info'
}

const getServiceStatusText = (status: string) => {
  const texts: Record<string, string> = {
    healthy: '正常',
    warning: '警告',
    error: '异常',
    restarting: '重启中'
  }
  return texts[status] || '未知'
}

const getAlertIcon = (level: string) => {
  const icons: Record<string, any> = {
    critical: CircleCloseFilled,
    warning: WarningFilled,
    info: InfoFilled
  }
  return icons[level] || InfoFilled
}

const getAlertLevelText = (level: string) => {
  const texts: Record<string, string> = {
    critical: '严重',
    warning: '警告',
    info: '信息'
  }
  return texts[level] || level
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (date: Date) => {
  return date.toLocaleString('zh-CN')
}

const refreshData = async () => {
  loading.value = true
  try {
    // 模拟数据刷新
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新系统状态
    systemStatus.lastUpdate = new Date()
    
    // 随机更新一些指标
    metrics.system.cpu = Math.floor(Math.random() * 100)
    metrics.system.memory = Math.floor(Math.random() * 100)
    metrics.api.avgResponseTime = Math.floor(Math.random() * 200) + 50
    
    ElMessage.success('数据已刷新')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const handleTimeRangeChange = () => {
  refreshData()
}

const handleMetricTabChange = () => {
  // 切换指标标签页时可以加载对应数据
}

const checkAllServices = async () => {
  checkingServices.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('服务检查完成')
  } catch (error) {
    ElMessage.error('服务检查失败')
  } finally {
    checkingServices.value = false
  }
}

const viewServiceLogs = (serviceName: string) => {
  ElMessage.info(`查看 ${serviceName} 日志`)
}

const restartService = async (serviceName: string) => {
  try {
    const service = services.value.find(s => s.name === serviceName)
    if (service) {
      service.status = 'restarting'
      await new Promise(resolve => setTimeout(resolve, 3000))
      service.status = 'healthy'
      ElMessage.success(`${serviceName} 重启成功`)
    }
  } catch (error) {
    ElMessage.error(`${serviceName} 重启失败`)
  }
}

const filterAlerts = () => {
  // 筛选告警
}

const acknowledgeAlert = (alertId: string) => {
  const alert = alerts.value.find(a => a.id === alertId)
  if (alert) {
    alert.acknowledged = true
    ElMessage.success('告警已确认')
  }
}

const resolveAlert = (alertId: string) => {
  const alert = alerts.value.find(a => a.id === alertId)
  if (alert) {
    alert.resolved = true
    ElMessage.success('告警已解决')
  }
}

const startAutoRefresh = () => {
  refreshInterval.value = setInterval(() => {
    refreshData()
  }, 30000) // 30秒自动刷新
}

const stopAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
}

// 生命周期
onMounted(() => {
  refreshData()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.monitor-page {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: #1f2937;
}

.page-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.status-overview {
  margin-bottom: 32px;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.status-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-card.error {
  border-color: #ef4444;
  background: #fef2f2;
}

.status-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.status-info h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #374151;
}

.status-text {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #1f2937;
}

.status-number {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 4px 0;
  color: #3b82f6;
}

.status-time,
.status-desc {
  font-size: 12px;
  color: #9ca3af;
  margin: 0;
}

.metrics-section,
.services-section,
.alerts-section {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: #1f2937;
}

.metric-tabs {
  flex: 1;
  max-width: 400px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.metric-card {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  background: #fafbfc;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.metric-header h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #374151;
}

.metric-value {
  font-size: 18px;
  font-weight: 700;
  color: #3b82f6;
}

.metric-chart {
  margin-bottom: 12px;
}

.metric-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item {
  font-size: 13px;
  color: #6b7280;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
}

.service-card {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  background: white;
  transition: all 0.3s ease;
}

.service-card.error {
  border-color: #ef4444;
  background: #fef2f2;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.service-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.service-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #1f2937;
}

.service-version {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.service-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.service-metric {
  text-align: center;
  padding: 8px;
  background: #f9fafb;
  border-radius: 6px;
}

.metric-label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.service-metric .metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.service-actions {
  display: flex;
  gap: 8px;
}

.alert-filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.alert-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  transition: all 0.3s ease;
}

.alert-item.critical {
  border-color: #ef4444;
  background: #fef2f2;
}

.alert-item.warning {
  border-color: #f59e0b;
  background: #fffbeb;
}

.alert-item.info {
  border-color: #3b82f6;
  background: #eff6ff;
}

.alert-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alert-item.critical .alert-icon {
  background: #fee2e2;
  color: #dc2626;
}

.alert-item.warning .alert-icon {
  background: #fef3c7;
  color: #d97706;
}

.alert-item.info .alert-icon {
  background: #dbeafe;
  color: #2563eb;
}

.alert-content {
  flex: 1;
  min-width: 0;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.alert-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #1f2937;
}

.alert-time {
  font-size: 12px;
  color: #9ca3af;
}

.alert-message {
  color: #374151;
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.alert-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #6b7280;
}

.alert-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

.empty-alerts {
  text-align: center;
  padding: 40px 0;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .status-cards {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .services-grid {
    grid-template-columns: 1fr;
  }
  
  .service-metrics {
    grid-template-columns: 1fr;
  }
  
  .alert-item {
    flex-direction: column;
    gap: 12px;
  }
  
  .alert-actions {
    flex-direction: row;
  }
}
</style>