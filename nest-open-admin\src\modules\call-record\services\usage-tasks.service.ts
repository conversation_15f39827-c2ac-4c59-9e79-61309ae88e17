import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ApiUsageTrackerService } from './api-usage-tracker.service';

/**
 * 使用量相关定时任务服务
 * 包含预警检查、免费额度重置等定时任务
 */
@Injectable()
export class UsageTasksService {
  private readonly logger = new Logger(UsageTasksService.name);

  constructor(
    private readonly apiUsageTracker: ApiUsageTrackerService,
  ) {}

  /**
   * 每天凌晨0点5分自动重置每日免费额度
   * 使用Cron表达式：分 时 日 月 星期
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleDailyFreeQuotaReset() {
    this.logger.log('执行每日免费额度重置任务');
    try {
      await this.apiUsageTracker.resetDailyFreeQuota();
    } catch (error) {
      this.logger.error('每日免费额度重置任务失败', error);
    }
  }

  /**
   * 每小时自动检查用户服务使用预警
   */
  @Cron(CronExpression.EVERY_HOUR)
  async handleUsageAlertCheck() {
    this.logger.log('执行用户服务使用预警检查任务');
    try {
      await this.apiUsageTracker.checkAllUsageAlerts();
    } catch (error) {
      this.logger.error('用户服务使用预警检查任务失败', error);
    }
  }

  /**
   * 启动时自动执行一次免费额度重置检查
   * 避免系统重启后需要等到下一个定时触发
   */
  async onApplicationBootstrap() {
    // 延迟10秒执行，确保应用完全启动
    setTimeout(async () => {
      this.logger.log('系统启动: 执行初始免费额度重置检查');
      try {
        await this.apiUsageTracker.resetDailyFreeQuota();
      } catch (error) {
        this.logger.error('初始免费额度重置检查失败', error);
      }
    }, 10000);
  }
} 