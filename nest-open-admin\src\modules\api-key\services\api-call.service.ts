import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { RedisService } from '../../../shared/redis.service';
import { ApiUsageTrackerService } from '../../call-record/services/api-usage-tracker.service';
import { firstValueFrom } from 'rxjs';

export interface ApiCallPayload {
  userId: number;
  serviceId: number;
  apiKeyId: number;
  url: string;
  method: string;
  headers?: Record<string, string>;
  data?: any;
  timeout?: number;
  retries?: number;
  callbackUrl?: string;
}

export interface ApiCallRecord {
  id: string;
  userId: number;
  serviceId: number;
  apiKeyId: number;
  url: string;
  method: string;
  status: 'SUCCESS' | 'FAILED' | 'TIMEOUT' | 'RETRY';
  statusCode?: number;
  response?: any;
  error?: string;
  responseTime: number;
  timestamp: Date;
  retryCount?: number;
}

/**
 * API调用处理服务
 * 直接处理API调用，不使用队列
 */
@Injectable()
export class ApiCallService {
  private readonly logger = new Logger(ApiCallService.name);
  private readonly API_CALL_RESULT_PREFIX = 'api_call_result:';
  private readonly API_CALL_RETRY_PREFIX = 'api_call_retry:';
  private readonly RESULT_TTL = 86400; // 24小时
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 1000; // 1秒

  constructor(
    private readonly httpService: HttpService,
    private readonly redisService: RedisService,
    @Inject(forwardRef(() => ApiUsageTrackerService))
    private readonly apiUsageTracker: ApiUsageTrackerService,
  ) {}

  /**
   * 提交API调用
   */
  async submitApiCall(payload: ApiCallPayload): Promise<string> {
    try {
      const callId = `api_call_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      // 直接处理API调用，不再使用队列
      await this.handleApiCall({
        callId,
        userId: payload.userId,
        serviceId: payload.serviceId,
        apiKeyId: payload.apiKeyId,
        url: payload.url,
        method: payload.method,
        headers: payload.headers,
        data: payload.data,
        timeout: payload.timeout || 30000,
        retries: payload.retries || this.MAX_RETRIES,
        callbackUrl: payload.callbackUrl,
      });

      this.logger.log(`API调用已处理完成: ${callId}`);
      return callId;
    } catch (error) {
      this.logger.error(`提交API调用失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 批量提交API调用
   */
  async submitBatchApiCalls(payloads: ApiCallPayload[]): Promise<string[]> {
    const callIds: string[] = [];

    for (const payload of payloads) {
      try {
        const callId = await this.submitApiCall(payload);
        callIds.push(callId);
      } catch (error) {
        this.logger.error(`批量提交API调用失败: ${error.message}`);
        // 继续处理其他调用
      }
    }

    this.logger.log(
      `批量提交API调用完成，成功: ${callIds.length}/${payloads.length}`,
    );
    return callIds;
  }

  /**
   * 处理API调用（直接处理，不再使用队列）
   */
  async handleApiCall(apiCallData: {
    callId: string;
    userId: number;
    serviceId: number;
    apiKeyId: number;
    url: string;
    method: string;
    headers?: Record<string, string>;
    data?: any;
    timeout?: number;
    retries?: number;
    callbackUrl?: string;
  }): Promise<void> {
    const startTime = Date.now();
    let apiCallRecord: ApiCallRecord;

    try {
      this.logger.log(`开始处理API调用: ${apiCallData.callId}`);

      // 执行API调用
      const result = await this.executeApiCall(apiCallData);
      const responseTime = Date.now() - startTime;

      // 创建API调用记录
      apiCallRecord = {
        id: apiCallData.callId,
        userId: apiCallData.userId,
        serviceId: apiCallData.serviceId,
        apiKeyId: apiCallData.apiKeyId,
        url: apiCallData.url,
        method: apiCallData.method,
        status: result.success ? 'SUCCESS' : 'FAILED',
        statusCode: result.statusCode,
        response: result.response,
        error: result.error,
        responseTime,
        timestamp: new Date(),
        retryCount: 0,
      };

      // 保存API调用记录
      await this.saveApiCallRecord(apiCallRecord);

      // 异步处理计费
      if (result.success) {
        await this.processBillingAsync({
          userId: apiCallData.userId,
          serviceId: apiCallData.serviceId,
          apiKeyId: apiCallData.apiKeyId,
          apiCallId: apiCallData.callId,
          requestCount: 1,
          responseTime,
        });
      }

      // 如果有回调URL，发送回调
      if (apiCallData.callbackUrl) {
        await this.sendCallback(apiCallData.callbackUrl, apiCallRecord);
      }

      this.logger.log(
        `API调用处理完成: ${apiCallData.callId}, 状态: ${apiCallRecord.status}`,
      );
    } catch (error) {
      this.logger.error(`处理API调用失败: ${apiCallData.callId}`, error);

      // 检查是否需要重试
      const shouldRetry = await this.shouldRetryApiCall(
        apiCallData.callId,
        error,
      );
      if (shouldRetry) {
        await this.scheduleApiCallRetry(apiCallData.callId, apiCallData);
        return;
      }

      // 创建失败记录
      const responseTime = Date.now() - startTime;
      apiCallRecord = {
        id: apiCallData.callId,
        userId: apiCallData.userId,
        serviceId: apiCallData.serviceId,
        apiKeyId: apiCallData.apiKeyId,
        url: apiCallData.url,
        method: apiCallData.method,
        status: 'FAILED',
        error: error.message,
        responseTime,
        timestamp: new Date(),
      };

      await this.saveApiCallRecord(apiCallRecord);
    }
  }

  /**
   * 执行API调用
   */
  private async executeApiCall(apiCallData: {
    url: string;
    method: string;
    headers?: Record<string, string>;
    data?: any;
    timeout?: number;
  }): Promise<{
    success: boolean;
    statusCode?: number;
    response?: any;
    error?: string;
  }> {
    try {
      const config = {
        method: apiCallData.method.toLowerCase() as any,
        url: apiCallData.url,
        headers: apiCallData.headers || {},
        timeout: apiCallData.timeout || 30000,
        ...(apiCallData.data && { data: apiCallData.data }),
      };

      const response = await firstValueFrom(this.httpService.request(config));

      return {
        success: true,
        statusCode: response.status,
        response: response.data,
      };
    } catch (error) {
      return {
        success: false,
        statusCode: error.response?.status,
        error: error.message,
      };
    }
  }

  /**
   * 判断是否应该重试API调用
   */
  private async shouldRetryApiCall(
    callId: string,
    error: any,
  ): Promise<boolean> {
    try {
      const retryKey = `${this.API_CALL_RETRY_PREFIX}${callId}`;
      const retryCount = await this.getRetryCount(retryKey);

      if (retryCount >= this.MAX_RETRIES) {
        return false;
      }

      // 只对特定类型的错误进行重试
      const retryableErrors = [
        'ECONNRESET',
        'ECONNREFUSED',
        'ETIMEDOUT',
        'ENOTFOUND',
        'EAI_AGAIN',
      ];

      const isRetryableError = retryableErrors.some(
        (code) => error.code === code || error.message.includes(code),
      );

      // HTTP 5xx错误也可以重试
      const isServerError =
        error.response &&
        error.response.status >= 500 &&
        error.response.status < 600;

      return isRetryableError || isServerError;
    } catch (err) {
      this.logger.error(`检查重试条件失败: ${err.message}`);
      return false;
    }
  }

  /**
   * 安排API调用重试
   */
  private async scheduleApiCallRetry(
    callId: string,
    apiCallData: any,
  ): Promise<void> {
    try {
      const retryKey = `${this.API_CALL_RETRY_PREFIX}${callId}`;
      const retryCount = await this.getRetryCount(retryKey);
      const newRetryCount = retryCount + 1;

      // 更新重试次数
      await this.redisService.setex(retryKey, 3600, newRetryCount.toString());

      // 计算延迟时间（指数退避）
      const delay = this.RETRY_DELAY * Math.pow(2, retryCount);

      this.logger.log(
        `安排API调用重试: ${callId}, 第${newRetryCount}次重试, 延迟${delay}ms`,
      );

      // 直接重新调度API调用
      setTimeout(async () => {
        await this.handleApiCall({
          ...apiCallData,
          retryCount: newRetryCount,
        });
      }, delay);
    } catch (error) {
      this.logger.error(`安排API调用重试失败: ${error.message}`);
    }
  }

  /**
   * 异步处理计费
   */
  private async processBillingAsync(billingData: {
    userId: number;
    serviceId: number;
    apiKeyId: number;
    apiCallId: string;
    requestCount: number;
    responseTime: number;
  }): Promise<void> {
    try {
      // 记录API使用情况
      await this.apiUsageTracker.trackApiUsage(
        billingData.apiCallId,
        billingData.userId,
        billingData.serviceId,
        { callId: billingData.apiCallId },
        { result: 'success' },
        true,
        billingData.requestCount
      );

      this.logger.log(`API调用计费处理完成: ${billingData.apiCallId}`);
    } catch (error) {
      this.logger.error(`API调用计费处理失败: ${billingData.apiCallId}`, error);
      // 计费失败不影响API调用响应
    }
  }

  /**
   * 处理API调用的回调
   */
  private async sendCallback(
    callbackUrl: string,
    record: ApiCallRecord,
  ): Promise<void> {
    try {
      if (!callbackUrl) return;

      const callbackData = {
        callId: record.id,
        status: record.status,
        statusCode: record.statusCode,
        response: record.response,
        error: record.error,
        timestamp: record.timestamp,
      };

      await firstValueFrom(
        this.httpService.post(callbackUrl, callbackData, {
          headers: { 'Content-Type': 'application/json' },
        }),
      );

      this.logger.log(`回调发送成功: ${record.id} -> ${callbackUrl}`);
    } catch (error) {
      this.logger.error(`回调发送失败: ${record.id} -> ${callbackUrl}`, error);
      // 回调失败不影响API调用结果
    }
  }

  /**
   * 保存API调用记录
   */
  private async saveApiCallRecord(record: ApiCallRecord): Promise<void> {
    try {
      const resultKey = `${this.API_CALL_RESULT_PREFIX}${record.id}`;
      await this.redisService.setex(
        resultKey,
        this.RESULT_TTL,
        JSON.stringify(record),
      );

      this.logger.log(`API调用记录已保存: ${record.id}`);
    } catch (error) {
      this.logger.error(`保存API调用记录失败: ${error.message}`);
    }
  }

  /**
   * 获取API调用结果
   */
  async getApiCallResult(callId: string): Promise<ApiCallRecord | null> {
    try {
      const resultKey = `${this.API_CALL_RESULT_PREFIX}${callId}`;
      const recordData = await this.redisService.get(resultKey);

      if (recordData) {
        return JSON.parse(recordData);
      }

      return null;
    } catch (error) {
      this.logger.error(`获取API调用结果失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 获取重试次数
   */
  private async getRetryCount(retryKey: string): Promise<number> {
    try {
      const count = await this.redisService.get(retryKey);
      return count ? parseInt(count, 10) : 0;
    } catch (error) {
      this.logger.error(`获取重试次数失败: ${error.message}`);
      return 0;
    }
  }

  /**
   * 获取API调用统计
   */
  async getApiCallStats(
    userId?: number,
    serviceId?: number,
    days: number = 7,
  ): Promise<{
    totalCalls: number;
    successfulCalls: number;
    failedCalls: number;
    averageResponseTime: number;
    errorRate: number;
    dailyStats: any[];
  }> {
    // 这里可以实现API调用统计逻辑
    // 从Redis或数据库中获取统计数据
    const stats = {
      totalCalls: 0,
      successfulCalls: 0,
      failedCalls: 0,
      averageResponseTime: 0,
      errorRate: 0,
      dailyStats: [],
    };

    return stats;
  }

  /**
   * 清理过期的API调用记录
   */
  async cleanupExpiredRecords(): Promise<void> {
    try {
      const patterns = [
        `${this.API_CALL_RETRY_PREFIX}*`,
        `${this.API_CALL_RESULT_PREFIX}*`,
      ];

      for (const pattern of patterns) {
        const keys = await this.redisService.keys(pattern);
        if (keys.length > 0) {
          await this.redisService.delMany(keys);
          this.logger.log(`清理了 ${keys.length} 个过期的API调用记录`);
        }
      }
    } catch (error) {
      this.logger.error(`清理过期API调用记录失败: ${error.message}`);
    }
  }
}
