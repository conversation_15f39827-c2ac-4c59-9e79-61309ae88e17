lockfileVersion: '6.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

dependencies:
  '@element-plus/icons-vue':
    specifier: ^2.3.1
    version: registry.npmmirror.com/@element-plus/icons-vue@2.3.1(vue@3.5.16)
  '@fingerprintjs/fingerprintjs':
    specifier: ^4.6.2
    version: registry.npmmirror.com/@fingerprintjs/fingerprintjs@4.6.2
  '@types/event-source-polyfill':
    specifier: ^1.0.5
    version: registry.npmmirror.com/@types/event-source-polyfill@1.0.5
  '@types/markdown-it':
    specifier: ^14.1.2
    version: registry.npmmirror.com/@types/markdown-it@14.1.2
  '@vueuse/core':
    specifier: ^13.3.0
    version: registry.npmmirror.com/@vueuse/core@13.3.0(vue@3.5.16)
  axios:
    specifier: ^1.9.0
    version: registry.npmmirror.com/axios@1.9.0
  cropperjs:
    specifier: ^2.0.0
    version: registry.npmmirror.com/cropperjs@2.0.0
  crypto-js:
    specifier: ^4.2.0
    version: registry.npmmirror.com/crypto-js@4.2.0
  dayjs:
    specifier: ^1.11.13
    version: registry.npmmirror.com/dayjs@1.11.13
  echarts:
    specifier: ^5.6.0
    version: registry.npmmirror.com/echarts@5.6.0
  element-plus:
    specifier: ^2.9.11
    version: registry.npmmirror.com/element-plus@2.9.11(vue@3.5.16)
  event-source-polyfill:
    specifier: ^1.0.31
    version: registry.npmmirror.com/event-source-polyfill@1.0.31
  highlight.js:
    specifier: ^11.11.1
    version: registry.npmmirror.com/highlight.js@11.11.1
  markdown-it:
    specifier: ^14.1.0
    version: registry.npmmirror.com/markdown-it@14.1.0
  pinia:
    specifier: ^3.0.1
    version: registry.npmmirror.com/pinia@3.0.2(typescript@5.8.3)(vue@3.5.16)
  vue:
    specifier: ^3.5.13
    version: registry.npmmirror.com/vue@3.5.16(typescript@5.8.3)
  vue-router:
    specifier: ^4.5.0
    version: registry.npmmirror.com/vue-router@4.5.1(vue@3.5.16)
  vue3-slide-verify:
    specifier: ^1.1.7
    version: registry.npmmirror.com/vue3-slide-verify@1.1.7(typescript@5.8.3)

devDependencies:
  '@tsconfig/node22':
    specifier: ^22.0.1
    version: registry.npmmirror.com/@tsconfig/node22@22.0.2
  '@types/crypto-js':
    specifier: ^4.2.2
    version: registry.npmmirror.com/@types/crypto-js@4.2.2
  '@types/node':
    specifier: ^22.14.0
    version: registry.npmmirror.com/@types/node@22.15.29
  '@vitejs/plugin-vue':
    specifier: ^5.2.3
    version: registry.npmmirror.com/@vitejs/plugin-vue@5.2.4(vite@6.3.5)(vue@3.5.16)
  '@vue/eslint-config-prettier':
    specifier: ^10.2.0
    version: registry.npmmirror.com/@vue/eslint-config-prettier@10.2.0(eslint@9.28.0)(prettier@3.5.3)
  '@vue/eslint-config-typescript':
    specifier: ^14.5.0
    version: registry.npmmirror.com/@vue/eslint-config-typescript@14.5.0(eslint-plugin-vue@10.0.1)(eslint@9.28.0)(typescript@5.8.3)
  '@vue/tsconfig':
    specifier: ^0.7.0
    version: registry.npmmirror.com/@vue/tsconfig@0.7.0(typescript@5.8.3)(vue@3.5.16)
  eslint:
    specifier: ^9.22.0
    version: registry.npmmirror.com/eslint@9.28.0(jiti@2.4.2)
  eslint-plugin-vue:
    specifier: ~10.0.0
    version: registry.npmmirror.com/eslint-plugin-vue@10.0.1(eslint@9.28.0)(vue-eslint-parser@10.1.3)
  jiti:
    specifier: ^2.4.2
    version: registry.npmmirror.com/jiti@2.4.2
  npm-run-all2:
    specifier: ^7.0.2
    version: registry.npmmirror.com/npm-run-all2@7.0.2
  prettier:
    specifier: 3.5.3
    version: registry.npmmirror.com/prettier@3.5.3
  typescript:
    specifier: ~5.8.0
    version: registry.npmmirror.com/typescript@5.8.3
  vite:
    specifier: ^6.2.4
    version: registry.npmmirror.com/vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)
  vite-plugin-vue-devtools:
    specifier: ^7.7.2
    version: registry.npmmirror.com/vite-plugin-vue-devtools@7.7.6(vite@6.3.5)(vue@3.5.16)
  vue-tsc:
    specifier: ^2.2.8
    version: registry.npmmirror.com/vue-tsc@2.2.10(typescript@5.8.3)

packages:

  registry.npmmirror.com/@ampproject/remapping@2.3.0:
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@ampproject/remapping/-/remapping-2.3.0.tgz}
    name: '@ampproject/remapping'
    version: 2.3.0
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/gen-mapping': registry.npmmirror.com/@jridgewell/gen-mapping@0.3.8
      '@jridgewell/trace-mapping': registry.npmmirror.com/@jridgewell/trace-mapping@0.3.25
    dev: true

  registry.npmmirror.com/@antfu/utils@0.7.10:
    resolution: {integrity: sha512-+562v9k4aI80m1+VuMHehNJWLOFjBnXn3tdOitzD0il5b7smkSBal4+a3oKiQTbrwMmN/TBUMDvbdoWDehgOww==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@antfu/utils/-/utils-0.7.10.tgz}
    name: '@antfu/utils'
    version: 0.7.10
    dev: true

  registry.npmmirror.com/@babel/code-frame@7.27.1:
    resolution: {integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.27.1.tgz}
    name: '@babel/code-frame'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': registry.npmmirror.com/@babel/helper-validator-identifier@7.27.1
      js-tokens: registry.npmmirror.com/js-tokens@4.0.0
      picocolors: registry.npmmirror.com/picocolors@1.1.1
    dev: true

  registry.npmmirror.com/@babel/compat-data@7.27.3:
    resolution: {integrity: sha512-V42wFfx1ymFte+ecf6iXghnnP8kWTO+ZLXIyZq+1LAXHHvTZdVxicn4yiVYdYMGaCO3tmqub11AorKkv+iodqw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.27.3.tgz}
    name: '@babel/compat-data'
    version: 7.27.3
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmmirror.com/@babel/core@7.27.4:
    resolution: {integrity: sha512-bXYxrXFubeYdvB0NhD/NBB3Qi6aZeV20GOWVI47t2dkecCEoneR4NPVcb7abpXDEvejgrUfFtG6vG/zxAKmg+g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/core/-/core-7.27.4.tgz}
    name: '@babel/core'
    version: 7.27.4
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': registry.npmmirror.com/@ampproject/remapping@2.3.0
      '@babel/code-frame': registry.npmmirror.com/@babel/code-frame@7.27.1
      '@babel/generator': registry.npmmirror.com/@babel/generator@7.27.3
      '@babel/helper-compilation-targets': registry.npmmirror.com/@babel/helper-compilation-targets@7.27.2
      '@babel/helper-module-transforms': registry.npmmirror.com/@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4)
      '@babel/helpers': registry.npmmirror.com/@babel/helpers@7.27.4
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.27.4
      '@babel/template': registry.npmmirror.com/@babel/template@7.27.2
      '@babel/traverse': registry.npmmirror.com/@babel/traverse@7.27.4
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.3
      convert-source-map: registry.npmmirror.com/convert-source-map@2.0.0
      debug: registry.npmmirror.com/debug@4.4.1
      gensync: registry.npmmirror.com/gensync@1.0.0-beta.2
      json5: registry.npmmirror.com/json5@2.2.3
      semver: registry.npmmirror.com/semver@6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@babel/generator@7.27.3:
    resolution: {integrity: sha512-xnlJYj5zepml8NXtjkG0WquFUv8RskFqyFcVgTBp5k+NaA/8uw/K+OSVf8AMGw5e9HKP2ETd5xpK5MLZQD6b4Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/generator/-/generator-7.27.3.tgz}
    name: '@babel/generator'
    version: 7.27.3
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.27.4
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.3
      '@jridgewell/gen-mapping': registry.npmmirror.com/@jridgewell/gen-mapping@0.3.8
      '@jridgewell/trace-mapping': registry.npmmirror.com/@jridgewell/trace-mapping@0.3.25
      jsesc: registry.npmmirror.com/jsesc@3.1.0
    dev: true

  registry.npmmirror.com/@babel/helper-annotate-as-pure@7.27.3:
    resolution: {integrity: sha512-fXSwMQqitTGeHLBC08Eq5yXz2m37E4pJX1qAU1+2cNedz/ifv/bVXft90VeSav5nFO61EcNgwr0aJxbyPaWBPg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.3.tgz}
    name: '@babel/helper-annotate-as-pure'
    version: 7.27.3
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.3
    dev: true

  registry.npmmirror.com/@babel/helper-compilation-targets@7.27.2:
    resolution: {integrity: sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz}
    name: '@babel/helper-compilation-targets'
    version: 7.27.2
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/compat-data': registry.npmmirror.com/@babel/compat-data@7.27.3
      '@babel/helper-validator-option': registry.npmmirror.com/@babel/helper-validator-option@7.27.1
      browserslist: registry.npmmirror.com/browserslist@4.25.0
      lru-cache: registry.npmmirror.com/lru-cache@5.1.1
      semver: registry.npmmirror.com/semver@6.3.1
    dev: true

  registry.npmmirror.com/@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.27.4):
    resolution: {integrity: sha512-QwGAmuvM17btKU5VqXfb+Giw4JcN0hjuufz3DYnpeVDvZLAObloM77bhMXiqry3Iio+Ai4phVRDwl6WU10+r5A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.1.tgz}
    id: registry.npmmirror.com/@babel/helper-create-class-features-plugin/7.27.1
    name: '@babel/helper-create-class-features-plugin'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.4
      '@babel/helper-annotate-as-pure': registry.npmmirror.com/@babel/helper-annotate-as-pure@7.27.3
      '@babel/helper-member-expression-to-functions': registry.npmmirror.com/@babel/helper-member-expression-to-functions@7.27.1
      '@babel/helper-optimise-call-expression': registry.npmmirror.com/@babel/helper-optimise-call-expression@7.27.1
      '@babel/helper-replace-supers': registry.npmmirror.com/@babel/helper-replace-supers@7.27.1(@babel/core@7.27.4)
      '@babel/helper-skip-transparent-expression-wrappers': registry.npmmirror.com/@babel/helper-skip-transparent-expression-wrappers@7.27.1
      '@babel/traverse': registry.npmmirror.com/@babel/traverse@7.27.4
      semver: registry.npmmirror.com/semver@6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@babel/helper-member-expression-to-functions@7.27.1:
    resolution: {integrity: sha512-E5chM8eWjTp/aNoVpcbfM7mLxu9XGLWYise2eBKGQomAk/Mb4XoxyqXTZbuTohbsl8EKqdlMhnDI2CCLfcs9wA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.27.1.tgz}
    name: '@babel/helper-member-expression-to-functions'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': registry.npmmirror.com/@babel/traverse@7.27.4
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@babel/helper-module-imports@7.27.1:
    resolution: {integrity: sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz}
    name: '@babel/helper-module-imports'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': registry.npmmirror.com/@babel/traverse@7.27.4
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4):
    resolution: {integrity: sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz}
    id: registry.npmmirror.com/@babel/helper-module-transforms/7.27.3
    name: '@babel/helper-module-transforms'
    version: 7.27.3
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.4
      '@babel/helper-module-imports': registry.npmmirror.com/@babel/helper-module-imports@7.27.1
      '@babel/helper-validator-identifier': registry.npmmirror.com/@babel/helper-validator-identifier@7.27.1
      '@babel/traverse': registry.npmmirror.com/@babel/traverse@7.27.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@babel/helper-optimise-call-expression@7.27.1:
    resolution: {integrity: sha512-URMGH08NzYFhubNSGJrpUEphGKQwMQYBySzat5cAByY1/YgIRkULnIy3tAMeszlL/so2HbeilYloUmSpd7GdVw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.27.1.tgz}
    name: '@babel/helper-optimise-call-expression'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.3
    dev: true

  registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1:
    resolution: {integrity: sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz}
    name: '@babel/helper-plugin-utils'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmmirror.com/@babel/helper-replace-supers@7.27.1(@babel/core@7.27.4):
    resolution: {integrity: sha512-7EHz6qDZc8RYS5ElPoShMheWvEgERonFCs7IAonWLLUTXW59DP14bCZt89/GKyreYn8g3S83m21FelHKbeDCKA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-replace-supers/-/helper-replace-supers-7.27.1.tgz}
    id: registry.npmmirror.com/@babel/helper-replace-supers/7.27.1
    name: '@babel/helper-replace-supers'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.4
      '@babel/helper-member-expression-to-functions': registry.npmmirror.com/@babel/helper-member-expression-to-functions@7.27.1
      '@babel/helper-optimise-call-expression': registry.npmmirror.com/@babel/helper-optimise-call-expression@7.27.1
      '@babel/traverse': registry.npmmirror.com/@babel/traverse@7.27.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@babel/helper-skip-transparent-expression-wrappers@7.27.1:
    resolution: {integrity: sha512-Tub4ZKEXqbPjXgWLl2+3JpQAYBJ8+ikpQ2Ocj/q/r0LwE3UhENh7EUabyHjz2kCEsrRY83ew2DQdHluuiDQFzg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.27.1.tgz}
    name: '@babel/helper-skip-transparent-expression-wrappers'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': registry.npmmirror.com/@babel/traverse@7.27.4
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@babel/helper-string-parser@7.27.1:
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz}
    name: '@babel/helper-string-parser'
    version: 7.27.1
    engines: {node: '>=6.9.0'}

  registry.npmmirror.com/@babel/helper-validator-identifier@7.27.1:
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz}
    name: '@babel/helper-validator-identifier'
    version: 7.27.1
    engines: {node: '>=6.9.0'}

  registry.npmmirror.com/@babel/helper-validator-option@7.27.1:
    resolution: {integrity: sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz}
    name: '@babel/helper-validator-option'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmmirror.com/@babel/helpers@7.27.4:
    resolution: {integrity: sha512-Y+bO6U+I7ZKaM5G5rDUZiYfUvQPUibYmAFe7EnKdnKBbVXDZxvp+MWOH5gYciY0EPk4EScsuFMQBbEfpdRKSCQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/helpers/-/helpers-7.27.4.tgz}
    name: '@babel/helpers'
    version: 7.27.4
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': registry.npmmirror.com/@babel/template@7.27.2
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.3
    dev: true

  registry.npmmirror.com/@babel/parser@7.27.4:
    resolution: {integrity: sha512-BRmLHGwpUqLFR2jzx9orBuX/ABDkj2jLKOXrHDTN2aOKL+jFDDKaRNo9nyYsIl9h/UE/7lMKdDjKQQyxKKDZ7g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/parser/-/parser-7.27.4.tgz}
    name: '@babel/parser'
    version: 7.27.4
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.3

  registry.npmmirror.com/@babel/plugin-proposal-decorators@7.27.1(@babel/core@7.27.4):
    resolution: {integrity: sha512-DTxe4LBPrtFdsWzgpmbBKevg3e9PBy+dXRt19kSbucbZvL2uqtdqwwpluL1jfxYE0wIDTFp1nTy/q6gNLsxXrg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.27.1.tgz}
    id: registry.npmmirror.com/@babel/plugin-proposal-decorators/7.27.1
    name: '@babel/plugin-proposal-decorators'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.4
      '@babel/helper-create-class-features-plugin': registry.npmmirror.com/@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
      '@babel/plugin-syntax-decorators': registry.npmmirror.com/@babel/plugin-syntax-decorators@7.27.1(@babel/core@7.27.4)
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-decorators@7.27.1(@babel/core@7.27.4):
    resolution: {integrity: sha512-YMq8Z87Lhl8EGkmb0MwYkt36QnxC+fzCgrl66ereamPlYToRpIk5nUjKUY3QKLWq8mwUB1BgbeXcTJhZOCDg5A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.27.1.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-decorators/7.27.1
    name: '@babel/plugin-syntax-decorators'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.4
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.4):
    resolution: {integrity: sha512-oFT0FrKHgF53f4vOsZGi2Hh3I35PfSmVs4IBFLFj4dnafP+hIWDLg3VyKmUHfLoLHlyxY4C7DGtmHuJgn+IGww==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-import-attributes/7.27.1
    name: '@babel/plugin-syntax-import-attributes'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.4
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.27.4):
    resolution: {integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-import-meta/7.10.4
    name: '@babel/plugin-syntax-import-meta'
    version: 7.10.4
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.4
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.4):
    resolution: {integrity: sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-jsx/7.27.1
    name: '@babel/plugin-syntax-jsx'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.4
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.4):
    resolution: {integrity: sha512-xfYCBMxveHrRMnAWl1ZlPXOZjzkN82THFvLhQhFXFt81Z5HnN+EtUkZhv/zcKpmT3fzmWZB0ywiBrbC3vogbwQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz}
    id: registry.npmmirror.com/@babel/plugin-syntax-typescript/7.27.1
    name: '@babel/plugin-syntax-typescript'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.4
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
    dev: true

  registry.npmmirror.com/@babel/plugin-transform-typescript@7.27.1(@babel/core@7.27.4):
    resolution: {integrity: sha512-Q5sT5+O4QUebHdbwKedFBEwRLb02zJ7r4A5Gg2hUoLuU3FjdMcyqcywqUrLCaDsFCxzokf7u9kuy7qz51YUuAg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.27.1.tgz}
    id: registry.npmmirror.com/@babel/plugin-transform-typescript/7.27.1
    name: '@babel/plugin-transform-typescript'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.4
      '@babel/helper-annotate-as-pure': registry.npmmirror.com/@babel/helper-annotate-as-pure@7.27.3
      '@babel/helper-create-class-features-plugin': registry.npmmirror.com/@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': registry.npmmirror.com/@babel/helper-skip-transparent-expression-wrappers@7.27.1
      '@babel/plugin-syntax-typescript': registry.npmmirror.com/@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.4)
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@babel/template@7.27.2:
    resolution: {integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/template/-/template-7.27.2.tgz}
    name: '@babel/template'
    version: 7.27.2
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': registry.npmmirror.com/@babel/code-frame@7.27.1
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.27.4
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.3
    dev: true

  registry.npmmirror.com/@babel/traverse@7.27.4:
    resolution: {integrity: sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/traverse/-/traverse-7.27.4.tgz}
    name: '@babel/traverse'
    version: 7.27.4
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': registry.npmmirror.com/@babel/code-frame@7.27.1
      '@babel/generator': registry.npmmirror.com/@babel/generator@7.27.3
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.27.4
      '@babel/template': registry.npmmirror.com/@babel/template@7.27.2
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.3
      debug: registry.npmmirror.com/debug@4.4.1
      globals: registry.npmmirror.com/globals@11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@babel/types@7.27.3:
    resolution: {integrity: sha512-Y1GkI4ktrtvmawoSq+4FCVHNryea6uR+qUQy0AGxLSsjCX0nVmkYQMBLHDkXZuo5hGx7eYdnIaslsdBFm7zbUw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@babel/types/-/types-7.27.3.tgz}
    name: '@babel/types'
    version: 7.27.3
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': registry.npmmirror.com/@babel/helper-string-parser@7.27.1
      '@babel/helper-validator-identifier': registry.npmmirror.com/@babel/helper-validator-identifier@7.27.1

  registry.npmmirror.com/@cropper/element-canvas@2.0.0:
    resolution: {integrity: sha512-GPtGJgSm92crJhhhwUsaMw3rz2KfJWWSz7kRAlufFEV/EHTP5+6r6/Z1BCGRna830i+Avqbm435XLOtA7PVJwA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@cropper/element-canvas/-/element-canvas-2.0.0.tgz}
    name: '@cropper/element-canvas'
    version: 2.0.0
    dependencies:
      '@cropper/element': registry.npmmirror.com/@cropper/element@2.0.0
      '@cropper/utils': registry.npmmirror.com/@cropper/utils@2.0.0
    dev: false

  registry.npmmirror.com/@cropper/element-crosshair@2.0.0:
    resolution: {integrity: sha512-KfPfyrdeFvUC31Ws7ATtcalWWSaMtrC6bMoCipZhqbUOE7wZoL4ecDSL6BUOZxPa74awZUqfzirCDjHvheBfyw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@cropper/element-crosshair/-/element-crosshair-2.0.0.tgz}
    name: '@cropper/element-crosshair'
    version: 2.0.0
    dependencies:
      '@cropper/element': registry.npmmirror.com/@cropper/element@2.0.0
      '@cropper/utils': registry.npmmirror.com/@cropper/utils@2.0.0
    dev: false

  registry.npmmirror.com/@cropper/element-grid@2.0.0:
    resolution: {integrity: sha512-i78SQ0IJTLFveKX6P7svkfMYVdgHrQ8ZmmEw8keFy9n1ZVbK+SK0UHK5FNMRNI/gtVhKJOGEnK/zeyjUdj4Iyw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@cropper/element-grid/-/element-grid-2.0.0.tgz}
    name: '@cropper/element-grid'
    version: 2.0.0
    dependencies:
      '@cropper/element': registry.npmmirror.com/@cropper/element@2.0.0
      '@cropper/utils': registry.npmmirror.com/@cropper/utils@2.0.0
    dev: false

  registry.npmmirror.com/@cropper/element-handle@2.0.0:
    resolution: {integrity: sha512-ZJvW+0MkK9E8xYymGdoruaQn2kwjSHFpNSWinjyq6csuVQiCPxlX5ovAEDldmZ9MWePPtWEi3vLKQOo2Yb0T8g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@cropper/element-handle/-/element-handle-2.0.0.tgz}
    name: '@cropper/element-handle'
    version: 2.0.0
    dependencies:
      '@cropper/element': registry.npmmirror.com/@cropper/element@2.0.0
      '@cropper/utils': registry.npmmirror.com/@cropper/utils@2.0.0
    dev: false

  registry.npmmirror.com/@cropper/element-image@2.0.0:
    resolution: {integrity: sha512-9BxiTS/aHRmrjopaFQb9mQQXmx4ruhYHGkDZMVz24AXpMFjUY6OpqrWse/WjzD9tfhMFvEdu17b3VAekcAgpeg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@cropper/element-image/-/element-image-2.0.0.tgz}
    name: '@cropper/element-image'
    version: 2.0.0
    dependencies:
      '@cropper/element': registry.npmmirror.com/@cropper/element@2.0.0
      '@cropper/element-canvas': registry.npmmirror.com/@cropper/element-canvas@2.0.0
      '@cropper/utils': registry.npmmirror.com/@cropper/utils@2.0.0
    dev: false

  registry.npmmirror.com/@cropper/element-selection@2.0.0:
    resolution: {integrity: sha512-ensNnbIfJsJ8bhbJTH/RXtk2URFvTOO4TvfRk461n2FPEC588D7rwBmUJxQg74IiTi4y1JbCI+6j+4LyzYBLCQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@cropper/element-selection/-/element-selection-2.0.0.tgz}
    name: '@cropper/element-selection'
    version: 2.0.0
    dependencies:
      '@cropper/element': registry.npmmirror.com/@cropper/element@2.0.0
      '@cropper/element-canvas': registry.npmmirror.com/@cropper/element-canvas@2.0.0
      '@cropper/element-image': registry.npmmirror.com/@cropper/element-image@2.0.0
      '@cropper/utils': registry.npmmirror.com/@cropper/utils@2.0.0
    dev: false

  registry.npmmirror.com/@cropper/element-shade@2.0.0:
    resolution: {integrity: sha512-jv/2bbNZnhU4W+T4G0c8ADocLIZvQFTXgCf2RFDNhI5UVxurzWBnDdb8Mx8LnVplnkTqO+xUmHZYve0CwgWo+Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@cropper/element-shade/-/element-shade-2.0.0.tgz}
    name: '@cropper/element-shade'
    version: 2.0.0
    dependencies:
      '@cropper/element': registry.npmmirror.com/@cropper/element@2.0.0
      '@cropper/element-canvas': registry.npmmirror.com/@cropper/element-canvas@2.0.0
      '@cropper/element-selection': registry.npmmirror.com/@cropper/element-selection@2.0.0
      '@cropper/utils': registry.npmmirror.com/@cropper/utils@2.0.0
    dev: false

  registry.npmmirror.com/@cropper/element-viewer@2.0.0:
    resolution: {integrity: sha512-zY+3VRN5TvpM8twlphYtXw0tzJL2VgzeK7ufhL1BixVqOdRxwP13TprYIhqwGt9EW/SyJZUiaIu396T89kRX8A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@cropper/element-viewer/-/element-viewer-2.0.0.tgz}
    name: '@cropper/element-viewer'
    version: 2.0.0
    dependencies:
      '@cropper/element': registry.npmmirror.com/@cropper/element@2.0.0
      '@cropper/element-canvas': registry.npmmirror.com/@cropper/element-canvas@2.0.0
      '@cropper/element-image': registry.npmmirror.com/@cropper/element-image@2.0.0
      '@cropper/element-selection': registry.npmmirror.com/@cropper/element-selection@2.0.0
      '@cropper/utils': registry.npmmirror.com/@cropper/utils@2.0.0
    dev: false

  registry.npmmirror.com/@cropper/element@2.0.0:
    resolution: {integrity: sha512-lsthn0nQq73GExUE7Mg/ss6Q3RXADGDv055hxoLFwvl/wGHgy6ZkYlfLZ/VmgBHC6jDK5IgPBFnqrPqlXWSGBA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@cropper/element/-/element-2.0.0.tgz}
    name: '@cropper/element'
    version: 2.0.0
    dependencies:
      '@cropper/utils': registry.npmmirror.com/@cropper/utils@2.0.0
    dev: false

  registry.npmmirror.com/@cropper/elements@2.0.0:
    resolution: {integrity: sha512-PQkPo1nUjxLFUQuHYu+6atfHxpX9B41Xribao6wpvmvmNIFML6LQdNqqWYb6LyM7ujsu71CZdBiMT5oetjJVoQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@cropper/elements/-/elements-2.0.0.tgz}
    name: '@cropper/elements'
    version: 2.0.0
    dependencies:
      '@cropper/element': registry.npmmirror.com/@cropper/element@2.0.0
      '@cropper/element-canvas': registry.npmmirror.com/@cropper/element-canvas@2.0.0
      '@cropper/element-crosshair': registry.npmmirror.com/@cropper/element-crosshair@2.0.0
      '@cropper/element-grid': registry.npmmirror.com/@cropper/element-grid@2.0.0
      '@cropper/element-handle': registry.npmmirror.com/@cropper/element-handle@2.0.0
      '@cropper/element-image': registry.npmmirror.com/@cropper/element-image@2.0.0
      '@cropper/element-selection': registry.npmmirror.com/@cropper/element-selection@2.0.0
      '@cropper/element-shade': registry.npmmirror.com/@cropper/element-shade@2.0.0
      '@cropper/element-viewer': registry.npmmirror.com/@cropper/element-viewer@2.0.0
    dev: false

  registry.npmmirror.com/@cropper/utils@2.0.0:
    resolution: {integrity: sha512-cprLYr+7kK3faGgoOsTW9gIn5sefDr2KwOmgyjzIXk+8PLpW8FgFKEg5FoWfRD5zMAmkCBuX6rGKDK3VdUEGrg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@cropper/utils/-/utils-2.0.0.tgz}
    name: '@cropper/utils'
    version: 2.0.0
    dev: false

  registry.npmmirror.com/@ctrl/tinycolor@3.6.1:
    resolution: {integrity: sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz}
    name: '@ctrl/tinycolor'
    version: 3.6.1
    engines: {node: '>=10'}
    dev: false

  registry.npmmirror.com/@element-plus/icons-vue@2.3.1(vue@3.5.16):
    resolution: {integrity: sha512-XxVUZv48RZAd87ucGS48jPf6pKu0yV5UCg9f4FFwtrYxXOwWuVJo6wOvSLKEoMQKjv8GsX/mhP6UsC1lRwbUWg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@element-plus/icons-vue/-/icons-vue-2.3.1.tgz}
    id: registry.npmmirror.com/@element-plus/icons-vue/2.3.1
    name: '@element-plus/icons-vue'
    version: 2.3.1
    peerDependencies:
      vue: ^3.2.0
    dependencies:
      vue: registry.npmmirror.com/vue@3.5.16(typescript@5.8.3)
    dev: false

  registry.npmmirror.com/@esbuild/aix-ppc64@0.25.5:
    resolution: {integrity: sha512-9o3TMmpmftaCMepOdA5k/yDw8SfInyzWWTjYTFCX3kPSDJMROQTb8jg+h9Cnwnmm1vOzvxN7gIfB5V2ewpjtGA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/aix-ppc64/-/aix-ppc64-0.25.5.tgz}
    name: '@esbuild/aix-ppc64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/android-arm64@0.25.5:
    resolution: {integrity: sha512-VGzGhj4lJO+TVGV1v8ntCZWJktV7SGCs3Pn1GRWI1SBFtRALoomm8k5E9Pmwg3HOAal2VDc2F9+PM/rEY6oIDg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/android-arm64/-/android-arm64-0.25.5.tgz}
    name: '@esbuild/android-arm64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/android-arm@0.25.5:
    resolution: {integrity: sha512-AdJKSPeEHgi7/ZhuIPtcQKr5RQdo6OO2IL87JkianiMYMPbCtot9fxPbrMiBADOWWm3T2si9stAiVsGbTQFkbA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/android-arm/-/android-arm-0.25.5.tgz}
    name: '@esbuild/android-arm'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/android-x64@0.25.5:
    resolution: {integrity: sha512-D2GyJT1kjvO//drbRT3Hib9XPwQeWd9vZoBJn+bu/lVsOZ13cqNdDeqIF/xQ5/VmWvMduP6AmXvylO/PIc2isw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/android-x64/-/android-x64-0.25.5.tgz}
    name: '@esbuild/android-x64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/darwin-arm64@0.25.5:
    resolution: {integrity: sha512-GtaBgammVvdF7aPIgH2jxMDdivezgFu6iKpmT+48+F8Hhg5J/sfnDieg0aeG/jfSvkYQU2/pceFPDKlqZzwnfQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/darwin-arm64/-/darwin-arm64-0.25.5.tgz}
    name: '@esbuild/darwin-arm64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/darwin-x64@0.25.5:
    resolution: {integrity: sha512-1iT4FVL0dJ76/q1wd7XDsXrSW+oLoquptvh4CLR4kITDtqi2e/xwXwdCVH8hVHU43wgJdsq7Gxuzcs6Iq/7bxQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.25.5.tgz}
    name: '@esbuild/darwin-x64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/freebsd-arm64@0.25.5:
    resolution: {integrity: sha512-nk4tGP3JThz4La38Uy/gzyXtpkPW8zSAmoUhK9xKKXdBCzKODMc2adkB2+8om9BDYugz+uGV7sLmpTYzvmz6Sw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.5.tgz}
    name: '@esbuild/freebsd-arm64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/freebsd-x64@0.25.5:
    resolution: {integrity: sha512-PrikaNjiXdR2laW6OIjlbeuCPrPaAl0IwPIaRv+SMV8CiM8i2LqVUHFC1+8eORgWyY7yhQY+2U2fA55mBzReaw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/freebsd-x64/-/freebsd-x64-0.25.5.tgz}
    name: '@esbuild/freebsd-x64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-arm64@0.25.5:
    resolution: {integrity: sha512-Z9kfb1v6ZlGbWj8EJk9T6czVEjjq2ntSYLY2cw6pAZl4oKtfgQuS4HOq41M/BcoLPzrUbNd+R4BXFyH//nHxVg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-arm64/-/linux-arm64-0.25.5.tgz}
    name: '@esbuild/linux-arm64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-arm@0.25.5:
    resolution: {integrity: sha512-cPzojwW2okgh7ZlRpcBEtsX7WBuqbLrNXqLU89GxWbNt6uIg78ET82qifUy3W6OVww6ZWobWub5oqZOVtwolfw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-arm/-/linux-arm-0.25.5.tgz}
    name: '@esbuild/linux-arm'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-ia32@0.25.5:
    resolution: {integrity: sha512-sQ7l00M8bSv36GLV95BVAdhJ2QsIbCuCjh/uYrWiMQSUuV+LpXwIqhgJDcvMTj+VsQmqAHL2yYaasENvJ7CDKA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.25.5.tgz}
    name: '@esbuild/linux-ia32'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-loong64@0.25.5:
    resolution: {integrity: sha512-0ur7ae16hDUC4OL5iEnDb0tZHDxYmuQyhKhsPBV8f99f6Z9KQM02g33f93rNH5A30agMS46u2HP6qTdEt6Q1kg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-loong64/-/linux-loong64-0.25.5.tgz}
    name: '@esbuild/linux-loong64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-mips64el@0.25.5:
    resolution: {integrity: sha512-kB/66P1OsHO5zLz0i6X0RxlQ+3cu0mkxS3TKFvkb5lin6uwZ/ttOkP3Z8lfR9mJOBk14ZwZ9182SIIWFGNmqmg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-mips64el/-/linux-mips64el-0.25.5.tgz}
    name: '@esbuild/linux-mips64el'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-ppc64@0.25.5:
    resolution: {integrity: sha512-UZCmJ7r9X2fe2D6jBmkLBMQetXPXIsZjQJCjgwpVDz+YMcS6oFR27alkgGv3Oqkv07bxdvw7fyB71/olceJhkQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.25.5.tgz}
    name: '@esbuild/linux-ppc64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-riscv64@0.25.5:
    resolution: {integrity: sha512-kTxwu4mLyeOlsVIFPfQo+fQJAV9mh24xL+y+Bm6ej067sYANjyEw1dNHmvoqxJUCMnkBdKpvOn0Ahql6+4VyeA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-riscv64/-/linux-riscv64-0.25.5.tgz}
    name: '@esbuild/linux-riscv64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-s390x@0.25.5:
    resolution: {integrity: sha512-K2dSKTKfmdh78uJ3NcWFiqyRrimfdinS5ErLSn3vluHNeHVnBAFWC8a4X5N+7FgVE1EjXS1QDZbpqZBjfrqMTQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-s390x/-/linux-s390x-0.25.5.tgz}
    name: '@esbuild/linux-s390x'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-x64@0.25.5:
    resolution: {integrity: sha512-uhj8N2obKTE6pSZ+aMUbqq+1nXxNjZIIjCjGLfsWvVpy7gKCOL6rsY1MhRh9zLtUtAI7vpgLMK6DxjO8Qm9lJw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-x64/-/linux-x64-0.25.5.tgz}
    name: '@esbuild/linux-x64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/netbsd-arm64@0.25.5:
    resolution: {integrity: sha512-pwHtMP9viAy1oHPvgxtOv+OkduK5ugofNTVDilIzBLpoWAM16r7b/mxBvfpuQDpRQFMfuVr5aLcn4yveGvBZvw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.5.tgz}
    name: '@esbuild/netbsd-arm64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/netbsd-x64@0.25.5:
    resolution: {integrity: sha512-WOb5fKrvVTRMfWFNCroYWWklbnXH0Q5rZppjq0vQIdlsQKuw6mdSihwSo4RV/YdQ5UCKKvBy7/0ZZYLBZKIbwQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/netbsd-x64/-/netbsd-x64-0.25.5.tgz}
    name: '@esbuild/netbsd-x64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/openbsd-arm64@0.25.5:
    resolution: {integrity: sha512-7A208+uQKgTxHd0G0uqZO8UjK2R0DDb4fDmERtARjSHWxqMTye4Erz4zZafx7Di9Cv+lNHYuncAkiGFySoD+Mw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.5.tgz}
    name: '@esbuild/openbsd-arm64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/openbsd-x64@0.25.5:
    resolution: {integrity: sha512-G4hE405ErTWraiZ8UiSoesH8DaCsMm0Cay4fsFWOOUcz8b8rC6uCvnagr+gnioEjWn0wC+o1/TAHt+It+MpIMg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/openbsd-x64/-/openbsd-x64-0.25.5.tgz}
    name: '@esbuild/openbsd-x64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/sunos-x64@0.25.5:
    resolution: {integrity: sha512-l+azKShMy7FxzY0Rj4RCt5VD/q8mG/e+mDivgspo+yL8zW7qEwctQ6YqKX34DTEleFAvCIUviCFX1SDZRSyMQA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/sunos-x64/-/sunos-x64-0.25.5.tgz}
    name: '@esbuild/sunos-x64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/win32-arm64@0.25.5:
    resolution: {integrity: sha512-O2S7SNZzdcFG7eFKgvwUEZ2VG9D/sn/eIiz8XRZ1Q/DO5a3s76Xv0mdBzVM5j5R639lXQmPmSo0iRpHqUUrsxw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/win32-arm64/-/win32-arm64-0.25.5.tgz}
    name: '@esbuild/win32-arm64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/win32-ia32@0.25.5:
    resolution: {integrity: sha512-onOJ02pqs9h1iMJ1PQphR+VZv8qBMQ77Klcsqv9CNW2w6yLqoURLcgERAIurY6QE63bbLuqgP9ATqajFLK5AMQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/win32-ia32/-/win32-ia32-0.25.5.tgz}
    name: '@esbuild/win32-ia32'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/win32-x64@0.25.5:
    resolution: {integrity: sha512-TXv6YnJ8ZMVdX+SXWVBo/0p8LTcrUYngpWjvm91TMjjBQii7Oz11Lw5lbDV5Y0TzuhSJHwiH4hEtC1I42mMS0g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/win32-x64/-/win32-x64-0.25.5.tgz}
    name: '@esbuild/win32-x64'
    version: 0.25.5
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@eslint-community/eslint-utils@4.7.0(eslint@9.28.0):
    resolution: {integrity: sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz}
    id: registry.npmmirror.com/@eslint-community/eslint-utils/4.7.0
    name: '@eslint-community/eslint-utils'
    version: 4.7.0
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
    dependencies:
      eslint: registry.npmmirror.com/eslint@9.28.0(jiti@2.4.2)
      eslint-visitor-keys: registry.npmmirror.com/eslint-visitor-keys@3.4.3
    dev: true

  registry.npmmirror.com/@eslint-community/regexpp@4.12.1:
    resolution: {integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint-community/regexpp/-/regexpp-4.12.1.tgz}
    name: '@eslint-community/regexpp'
    version: 4.12.1
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}
    dev: true

  registry.npmmirror.com/@eslint/config-array@0.20.0:
    resolution: {integrity: sha512-fxlS1kkIjx8+vy2SjuCB94q3htSNrufYTXubwiBFeaQHbH6Ipi43gFJq2zCMt6PHhImH3Xmr0NksKDvchWlpQQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint/config-array/-/config-array-0.20.0.tgz}
    name: '@eslint/config-array'
    version: 0.20.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@eslint/object-schema': registry.npmmirror.com/@eslint/object-schema@2.1.6
      debug: registry.npmmirror.com/debug@4.4.1
      minimatch: registry.npmmirror.com/minimatch@3.1.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@eslint/config-helpers@0.2.2:
    resolution: {integrity: sha512-+GPzk8PlG0sPpzdU5ZvIRMPidzAnZDl/s9L+y13iodqvb8leL53bTannOrQ/Im7UkpsmFU5Ily5U60LWixnmLg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint/config-helpers/-/config-helpers-0.2.2.tgz}
    name: '@eslint/config-helpers'
    version: 0.2.2
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  registry.npmmirror.com/@eslint/core@0.14.0:
    resolution: {integrity: sha512-qIbV0/JZr7iSDjqAc60IqbLdsj9GDt16xQtWD+B78d/HAlvysGdZZ6rpJHGAc2T0FQx1X6thsSPdnoiGKdNtdg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint/core/-/core-0.14.0.tgz}
    name: '@eslint/core'
    version: 0.14.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@types/json-schema': registry.npmmirror.com/@types/json-schema@7.0.15
    dev: true

  registry.npmmirror.com/@eslint/eslintrc@3.3.1:
    resolution: {integrity: sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint/eslintrc/-/eslintrc-3.3.1.tgz}
    name: '@eslint/eslintrc'
    version: 3.3.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      ajv: registry.npmmirror.com/ajv@6.12.6
      debug: registry.npmmirror.com/debug@4.4.1
      espree: registry.npmmirror.com/espree@10.3.0
      globals: registry.npmmirror.com/globals@14.0.0
      ignore: registry.npmmirror.com/ignore@5.3.2
      import-fresh: registry.npmmirror.com/import-fresh@3.3.1
      js-yaml: registry.npmmirror.com/js-yaml@4.1.0
      minimatch: registry.npmmirror.com/minimatch@3.1.2
      strip-json-comments: registry.npmmirror.com/strip-json-comments@3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@eslint/js@9.28.0:
    resolution: {integrity: sha512-fnqSjGWd/CoIp4EXIxWVK/sHA6DOHN4+8Ix2cX5ycOY7LG0UY8nHCU5pIp2eaE1Mc7Qd8kHspYNzYXT2ojPLzg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint/js/-/js-9.28.0.tgz}
    name: '@eslint/js'
    version: 9.28.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  registry.npmmirror.com/@eslint/object-schema@2.1.6:
    resolution: {integrity: sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint/object-schema/-/object-schema-2.1.6.tgz}
    name: '@eslint/object-schema'
    version: 2.1.6
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  registry.npmmirror.com/@eslint/plugin-kit@0.3.1:
    resolution: {integrity: sha512-0J+zgWxHN+xXONWIyPWKFMgVuJoZuGiIFu8yxk7RJjxkzpGmyja5wRFqZIVtjDVOQpV+Rw0iOAjYPE2eQyjr0w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@eslint/plugin-kit/-/plugin-kit-0.3.1.tgz}
    name: '@eslint/plugin-kit'
    version: 0.3.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@eslint/core': registry.npmmirror.com/@eslint/core@0.14.0
      levn: registry.npmmirror.com/levn@0.4.1
    dev: true

  registry.npmmirror.com/@fingerprintjs/fingerprintjs@4.6.2:
    resolution: {integrity: sha512-g8mXuqcFKbgH2CZKwPfVtsUJDHyvcgIABQI7Y0tzWEFXpGxJaXuAuzlifT2oTakjDBLTK4Gaa9/5PERDhqUjtw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@fingerprintjs/fingerprintjs/-/fingerprintjs-4.6.2.tgz}
    name: '@fingerprintjs/fingerprintjs'
    version: 4.6.2
    dependencies:
      tslib: registry.npmmirror.com/tslib@2.8.1
    dev: false

  registry.npmmirror.com/@floating-ui/core@1.7.0:
    resolution: {integrity: sha512-FRdBLykrPPA6P76GGGqlex/e7fbe0F1ykgxHYNXQsH/iTEtjMj/f9bpY5oQqbjt5VgZvgz/uKXbGuROijh3VLA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@floating-ui/core/-/core-1.7.0.tgz}
    name: '@floating-ui/core'
    version: 1.7.0
    dependencies:
      '@floating-ui/utils': registry.npmmirror.com/@floating-ui/utils@0.2.9
    dev: false

  registry.npmmirror.com/@floating-ui/dom@1.7.0:
    resolution: {integrity: sha512-lGTor4VlXcesUMh1cupTUTDoCxMb0V6bm3CnxHzQcw8Eaf1jQbgQX4i02fYgT0vJ82tb5MZ4CZk1LRGkktJCzg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@floating-ui/dom/-/dom-1.7.0.tgz}
    name: '@floating-ui/dom'
    version: 1.7.0
    dependencies:
      '@floating-ui/core': registry.npmmirror.com/@floating-ui/core@1.7.0
      '@floating-ui/utils': registry.npmmirror.com/@floating-ui/utils@0.2.9
    dev: false

  registry.npmmirror.com/@floating-ui/utils@0.2.9:
    resolution: {integrity: sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@floating-ui/utils/-/utils-0.2.9.tgz}
    name: '@floating-ui/utils'
    version: 0.2.9
    dev: false

  registry.npmmirror.com/@humanfs/core@0.19.1:
    resolution: {integrity: sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@humanfs/core/-/core-0.19.1.tgz}
    name: '@humanfs/core'
    version: 0.19.1
    engines: {node: '>=18.18.0'}
    dev: true

  registry.npmmirror.com/@humanfs/node@0.16.6:
    resolution: {integrity: sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@humanfs/node/-/node-0.16.6.tgz}
    name: '@humanfs/node'
    version: 0.16.6
    engines: {node: '>=18.18.0'}
    dependencies:
      '@humanfs/core': registry.npmmirror.com/@humanfs/core@0.19.1
      '@humanwhocodes/retry': registry.npmmirror.com/@humanwhocodes/retry@0.3.1
    dev: true

  registry.npmmirror.com/@humanwhocodes/module-importer@1.0.1:
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz}
    name: '@humanwhocodes/module-importer'
    version: 1.0.1
    engines: {node: '>=12.22'}
    dev: true

  registry.npmmirror.com/@humanwhocodes/retry@0.3.1:
    resolution: {integrity: sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@humanwhocodes/retry/-/retry-0.3.1.tgz}
    name: '@humanwhocodes/retry'
    version: 0.3.1
    engines: {node: '>=18.18'}
    dev: true

  registry.npmmirror.com/@humanwhocodes/retry@0.4.3:
    resolution: {integrity: sha512-bV0Tgo9K4hfPCek+aMAn81RppFKv2ySDQeMoSZuvTASywNTnVJCArCZE2FWqpvIatKu7VMRLWlR1EazvVhDyhQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@humanwhocodes/retry/-/retry-0.4.3.tgz}
    name: '@humanwhocodes/retry'
    version: 0.4.3
    engines: {node: '>=18.18'}
    dev: true

  registry.npmmirror.com/@jridgewell/gen-mapping@0.3.8:
    resolution: {integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz}
    name: '@jridgewell/gen-mapping'
    version: 0.3.8
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': registry.npmmirror.com/@jridgewell/set-array@1.2.1
      '@jridgewell/sourcemap-codec': registry.npmmirror.com/@jridgewell/sourcemap-codec@1.5.0
      '@jridgewell/trace-mapping': registry.npmmirror.com/@jridgewell/trace-mapping@0.3.25
    dev: true

  registry.npmmirror.com/@jridgewell/resolve-uri@3.1.2:
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz}
    name: '@jridgewell/resolve-uri'
    version: 3.1.2
    engines: {node: '>=6.0.0'}
    dev: true

  registry.npmmirror.com/@jridgewell/set-array@1.2.1:
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/set-array/-/set-array-1.2.1.tgz}
    name: '@jridgewell/set-array'
    version: 1.2.1
    engines: {node: '>=6.0.0'}
    dev: true

  registry.npmmirror.com/@jridgewell/sourcemap-codec@1.5.0:
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz}
    name: '@jridgewell/sourcemap-codec'
    version: 1.5.0

  registry.npmmirror.com/@jridgewell/trace-mapping@0.3.25:
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz}
    name: '@jridgewell/trace-mapping'
    version: 0.3.25
    dependencies:
      '@jridgewell/resolve-uri': registry.npmmirror.com/@jridgewell/resolve-uri@3.1.2
      '@jridgewell/sourcemap-codec': registry.npmmirror.com/@jridgewell/sourcemap-codec@1.5.0
    dev: true

  registry.npmmirror.com/@nodelib/fs.scandir@2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz}
    name: '@nodelib/fs.scandir'
    version: 2.1.5
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': registry.npmmirror.com/@nodelib/fs.stat@2.0.5
      run-parallel: registry.npmmirror.com/run-parallel@1.2.0
    dev: true

  registry.npmmirror.com/@nodelib/fs.stat@2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz}
    name: '@nodelib/fs.stat'
    version: 2.0.5
    engines: {node: '>= 8'}
    dev: true

  registry.npmmirror.com/@nodelib/fs.walk@1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz}
    name: '@nodelib/fs.walk'
    version: 1.2.8
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': registry.npmmirror.com/@nodelib/fs.scandir@2.1.5
      fastq: registry.npmmirror.com/fastq@1.19.1
    dev: true

  registry.npmmirror.com/@pkgr/core@0.2.4:
    resolution: {integrity: sha512-ROFF39F6ZrnzSUEmQQZUar0Jt4xVoP9WnDRdWwF4NNcXs3xBTLgBUDoOwW141y1jP+S8nahIbdxbFC7IShw9Iw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@pkgr/core/-/core-0.2.4.tgz}
    name: '@pkgr/core'
    version: 0.2.4
    engines: {node: ^12.20.0 || ^14.18.0 || >=16.0.0}
    dev: true

  registry.npmmirror.com/@polka/url@1.0.0-next.29:
    resolution: {integrity: sha512-wwQAWhWSuHaag8c4q/KN/vCoeOJYshAIvMQwD4GpSb3OiZklFfvAgmj0VCBBImRpuF/aFgIRzllXlVX93Jevww==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@polka/url/-/url-1.0.0-next.29.tgz}
    name: '@polka/url'
    version: 1.0.0-next.29
    dev: true

  registry.npmmirror.com/@rollup/pluginutils@5.1.4:
    resolution: {integrity: sha512-USm05zrsFxYLPdWWq+K3STlWiT/3ELn3RcV5hJMghpeAIhxfsUIg6mt12CBJBInWMV4VneoV7SfGv8xIwo2qNQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/pluginutils/-/pluginutils-5.1.4.tgz}
    name: '@rollup/pluginutils'
    version: 5.1.4
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true
    dependencies:
      '@types/estree': registry.npmmirror.com/@types/estree@1.0.7
      estree-walker: registry.npmmirror.com/estree-walker@2.0.2
      picomatch: registry.npmmirror.com/picomatch@4.0.2
    dev: true

  registry.npmmirror.com/@rollup/rollup-android-arm-eabi@4.41.1:
    resolution: {integrity: sha512-NELNvyEWZ6R9QMkiytB4/L4zSEaBC03KIXEghptLGLZWJ6VPrL63ooZQCOnlx36aQPGhzuOMwDerC1Eb2VmrLw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.41.1.tgz}
    name: '@rollup/rollup-android-arm-eabi'
    version: 4.41.1
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-android-arm64@4.41.1:
    resolution: {integrity: sha512-DXdQe1BJ6TK47ukAoZLehRHhfKnKg9BjnQYUu9gzhI8Mwa1d2fzxA1aw2JixHVl403bwp1+/o/NhhHtxWJBgEA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.41.1.tgz}
    name: '@rollup/rollup-android-arm64'
    version: 4.41.1
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-darwin-arm64@4.41.1:
    resolution: {integrity: sha512-5afxvwszzdulsU2w8JKWwY8/sJOLPzf0e1bFuvcW5h9zsEg+RQAojdW0ux2zyYAz7R8HvvzKCjLNJhVq965U7w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.41.1.tgz}
    name: '@rollup/rollup-darwin-arm64'
    version: 4.41.1
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-darwin-x64@4.41.1:
    resolution: {integrity: sha512-egpJACny8QOdHNNMZKf8xY0Is6gIMz+tuqXlusxquWu3F833DcMwmGM7WlvCO9sB3OsPjdC4U0wHw5FabzCGZg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.41.1.tgz}
    name: '@rollup/rollup-darwin-x64'
    version: 4.41.1
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-freebsd-arm64@4.41.1:
    resolution: {integrity: sha512-DBVMZH5vbjgRk3r0OzgjS38z+atlupJ7xfKIDJdZZL6sM6wjfDNo64aowcLPKIx7LMQi8vybB56uh1Ftck/Atg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.41.1.tgz}
    name: '@rollup/rollup-freebsd-arm64'
    version: 4.41.1
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-freebsd-x64@4.41.1:
    resolution: {integrity: sha512-3FkydeohozEskBxNWEIbPfOE0aqQgB6ttTkJ159uWOFn42VLyfAiyD9UK5mhu+ItWzft60DycIN1Xdgiy8o/SA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.41.1.tgz}
    name: '@rollup/rollup-freebsd-x64'
    version: 4.41.1
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-arm-gnueabihf@4.41.1:
    resolution: {integrity: sha512-wC53ZNDgt0pqx5xCAgNunkTzFE8GTgdZ9EwYGVcg+jEjJdZGtq9xPjDnFgfFozQI/Xm1mh+D9YlYtl+ueswNEg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.41.1.tgz}
    name: '@rollup/rollup-linux-arm-gnueabihf'
    version: 4.41.1
    cpu: [arm]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-arm-musleabihf@4.41.1:
    resolution: {integrity: sha512-jwKCca1gbZkZLhLRtsrka5N8sFAaxrGz/7wRJ8Wwvq3jug7toO21vWlViihG85ei7uJTpzbXZRcORotE+xyrLA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.41.1.tgz}
    name: '@rollup/rollup-linux-arm-musleabihf'
    version: 4.41.1
    cpu: [arm]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-arm64-gnu@4.41.1:
    resolution: {integrity: sha512-g0UBcNknsmmNQ8V2d/zD2P7WWfJKU0F1nu0k5pW4rvdb+BIqMm8ToluW/eeRmxCared5dD76lS04uL4UaNgpNA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.41.1.tgz}
    name: '@rollup/rollup-linux-arm64-gnu'
    version: 4.41.1
    cpu: [arm64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-arm64-musl@4.41.1:
    resolution: {integrity: sha512-XZpeGB5TKEZWzIrj7sXr+BEaSgo/ma/kCgrZgL0oo5qdB1JlTzIYQKel/RmhT6vMAvOdM2teYlAaOGJpJ9lahg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.41.1.tgz}
    name: '@rollup/rollup-linux-arm64-musl'
    version: 4.41.1
    cpu: [arm64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu@4.41.1:
    resolution: {integrity: sha512-bkCfDJ4qzWfFRCNt5RVV4DOw6KEgFTUZi2r2RuYhGWC8WhCA8lCAJhDeAmrM/fdiAH54m0mA0Vk2FGRPyzI+tw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.41.1.tgz}
    name: '@rollup/rollup-linux-loongarch64-gnu'
    version: 4.41.1
    cpu: [loong64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-powerpc64le-gnu@4.41.1:
    resolution: {integrity: sha512-3mr3Xm+gvMX+/8EKogIZSIEF0WUu0HL9di+YWlJpO8CQBnoLAEL/roTCxuLncEdgcfJcvA4UMOf+2dnjl4Ut1A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.41.1.tgz}
    name: '@rollup/rollup-linux-powerpc64le-gnu'
    version: 4.41.1
    cpu: [ppc64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-riscv64-gnu@4.41.1:
    resolution: {integrity: sha512-3rwCIh6MQ1LGrvKJitQjZFuQnT2wxfU+ivhNBzmxXTXPllewOF7JR1s2vMX/tWtUYFgphygxjqMl76q4aMotGw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.41.1.tgz}
    name: '@rollup/rollup-linux-riscv64-gnu'
    version: 4.41.1
    cpu: [riscv64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-riscv64-musl@4.41.1:
    resolution: {integrity: sha512-LdIUOb3gvfmpkgFZuccNa2uYiqtgZAz3PTzjuM5bH3nvuy9ty6RGc/Q0+HDFrHrizJGVpjnTZ1yS5TNNjFlklw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.41.1.tgz}
    name: '@rollup/rollup-linux-riscv64-musl'
    version: 4.41.1
    cpu: [riscv64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu@4.41.1:
    resolution: {integrity: sha512-oIE6M8WC9ma6xYqjvPhzZYk6NbobIURvP/lEbh7FWplcMO6gn7MM2yHKA1eC/GvYwzNKK/1LYgqzdkZ8YFxR8g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.41.1.tgz}
    name: '@rollup/rollup-linux-s390x-gnu'
    version: 4.41.1
    cpu: [s390x]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-x64-gnu@4.41.1:
    resolution: {integrity: sha512-cWBOvayNvA+SyeQMp79BHPK8ws6sHSsYnK5zDcsC3Hsxr1dgTABKjMnMslPq1DvZIp6uO7kIWhiGwaTdR4Og9A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.41.1.tgz}
    name: '@rollup/rollup-linux-x64-gnu'
    version: 4.41.1
    cpu: [x64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-linux-x64-musl@4.41.1:
    resolution: {integrity: sha512-y5CbN44M+pUCdGDlZFzGGBSKCA4A/J2ZH4edTYSSxFg7ce1Xt3GtydbVKWLlzL+INfFIZAEg1ZV6hh9+QQf9YQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.41.1.tgz}
    name: '@rollup/rollup-linux-x64-musl'
    version: 4.41.1
    cpu: [x64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc@4.41.1:
    resolution: {integrity: sha512-lZkCxIrjlJlMt1dLO/FbpZbzt6J/A8p4DnqzSa4PWqPEUUUnzXLeki/iyPLfV0BmHItlYgHUqJe+3KiyydmiNQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.41.1.tgz}
    name: '@rollup/rollup-win32-arm64-msvc'
    version: 4.41.1
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-win32-ia32-msvc@4.41.1:
    resolution: {integrity: sha512-+psFT9+pIh2iuGsxFYYa/LhS5MFKmuivRsx9iPJWNSGbh2XVEjk90fmpUEjCnILPEPJnikAU6SFDiEUyOv90Pg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.41.1.tgz}
    name: '@rollup/rollup-win32-ia32-msvc'
    version: 4.41.1
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@rollup/rollup-win32-x64-msvc@4.41.1:
    resolution: {integrity: sha512-Wq2zpapRYLfi4aKxf2Xff0tN+7slj2d4R87WEzqw7ZLsVvO5zwYCIuEGSZYiK41+GlwUo1HiR+GdkLEJnCKTCw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.41.1.tgz}
    name: '@rollup/rollup-win32-x64-msvc'
    version: 4.41.1
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@sec-ant/readable-stream@0.4.1:
    resolution: {integrity: sha512-831qok9r2t8AlxLko40y2ebgSDhenenCatLVeW/uBtnHPyhHOvG0C7TvfgecV+wHzIm5KUICgzmVpWS+IMEAeg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@sec-ant/readable-stream/-/readable-stream-0.4.1.tgz}
    name: '@sec-ant/readable-stream'
    version: 0.4.1
    dev: true

  registry.npmmirror.com/@sindresorhus/merge-streams@4.0.0:
    resolution: {integrity: sha512-tlqY9xq5ukxTUZBmoOp+m61cqwQD5pHJtFY3Mn8CA8ps6yghLH/Hw8UPdqg4OLmFW3IFlcXnQNmo/dh8HzXYIQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@sindresorhus/merge-streams/-/merge-streams-4.0.0.tgz}
    name: '@sindresorhus/merge-streams'
    version: 4.0.0
    engines: {node: '>=18'}
    dev: true

  registry.npmmirror.com/@sxzz/popperjs-es@2.11.7:
    resolution: {integrity: sha512-Ccy0NlLkzr0Ex2FKvh2X+OyERHXJ88XJ1MXtsI9y9fGexlaXaVTPzBCRBwIxFkORuOb+uBqeu+RqnpgYTEZRUQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@sxzz/popperjs-es/-/popperjs-es-2.11.7.tgz}
    name: '@sxzz/popperjs-es'
    version: 2.11.7
    dev: false

  registry.npmmirror.com/@tsconfig/node22@22.0.2:
    resolution: {integrity: sha512-Kmwj4u8sDRDrMYRoN9FDEcXD8UpBSaPQQ24Gz+Gamqfm7xxn+GBR7ge/Z7pK8OXNGyUzbSwJj+TH6B+DS/epyA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@tsconfig/node22/-/node22-22.0.2.tgz}
    name: '@tsconfig/node22'
    version: 22.0.2
    dev: true

  registry.npmmirror.com/@types/crypto-js@4.2.2:
    resolution: {integrity: sha512-sDOLlVbHhXpAUAL0YHDUUwDZf3iN4Bwi4W6a0W0b+QcAezUbRtH4FVb+9J4h+XFPW7l/gQ9F8qC7P+Ec4k8QVQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/crypto-js/-/crypto-js-4.2.2.tgz}
    name: '@types/crypto-js'
    version: 4.2.2
    dev: true

  registry.npmmirror.com/@types/estree@1.0.7:
    resolution: {integrity: sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/estree/-/estree-1.0.7.tgz}
    name: '@types/estree'
    version: 1.0.7
    dev: true

  registry.npmmirror.com/@types/event-source-polyfill@1.0.5:
    resolution: {integrity: sha512-iaiDuDI2aIFft7XkcwMzDWLqo7LVDixd2sR6B4wxJut9xcp/Ev9bO4EFg4rm6S9QxATLBj5OPxdeocgmhjwKaw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/event-source-polyfill/-/event-source-polyfill-1.0.5.tgz}
    name: '@types/event-source-polyfill'
    version: 1.0.5
    dev: false

  registry.npmmirror.com/@types/json-schema@7.0.15:
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.15.tgz}
    name: '@types/json-schema'
    version: 7.0.15
    dev: true

  registry.npmmirror.com/@types/linkify-it@5.0.0:
    resolution: {integrity: sha512-sVDA58zAw4eWAffKOaQH5/5j3XeayukzDk+ewSsnv3p4yJEZHCCzMDiZM8e0OUrRvmpGZ85jf4yDHkHsgBNr9Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/linkify-it/-/linkify-it-5.0.0.tgz}
    name: '@types/linkify-it'
    version: 5.0.0
    dev: false

  registry.npmmirror.com/@types/lodash-es@4.17.12:
    resolution: {integrity: sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/lodash-es/-/lodash-es-4.17.12.tgz}
    name: '@types/lodash-es'
    version: 4.17.12
    dependencies:
      '@types/lodash': registry.npmmirror.com/@types/lodash@4.17.17
    dev: false

  registry.npmmirror.com/@types/lodash@4.17.17:
    resolution: {integrity: sha512-RRVJ+J3J+WmyOTqnz3PiBLA501eKwXl2noseKOrNo/6+XEHjTAxO4xHvxQB6QuNm+s4WRbn6rSiap8+EA+ykFQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/lodash/-/lodash-4.17.17.tgz}
    name: '@types/lodash'
    version: 4.17.17
    dev: false

  registry.npmmirror.com/@types/markdown-it@14.1.2:
    resolution: {integrity: sha512-promo4eFwuiW+TfGxhi+0x3czqTYJkG8qB17ZUJiVF10Xm7NLVRSLUsfRTU/6h1e24VvRnXCx+hG7li58lkzog==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/markdown-it/-/markdown-it-14.1.2.tgz}
    name: '@types/markdown-it'
    version: 14.1.2
    dependencies:
      '@types/linkify-it': registry.npmmirror.com/@types/linkify-it@5.0.0
      '@types/mdurl': registry.npmmirror.com/@types/mdurl@2.0.0
    dev: false

  registry.npmmirror.com/@types/mdurl@2.0.0:
    resolution: {integrity: sha512-RGdgjQUZba5p6QEFAVx2OGb8rQDL/cPRG7GiedRzMcJ1tYnUANBncjbSB1NRGwbvjcPeikRABz2nshyPk1bhWg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/mdurl/-/mdurl-2.0.0.tgz}
    name: '@types/mdurl'
    version: 2.0.0
    dev: false

  registry.npmmirror.com/@types/node@22.15.29:
    resolution: {integrity: sha512-LNdjOkUDlU1RZb8e1kOIUpN1qQUlzGkEtbVNo53vbrwDg5om6oduhm4SiUaPW5ASTXhAiP0jInWG8Qx9fVlOeQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/node/-/node-22.15.29.tgz}
    name: '@types/node'
    version: 22.15.29
    dependencies:
      undici-types: registry.npmmirror.com/undici-types@6.21.0
    dev: true

  registry.npmmirror.com/@types/web-bluetooth@0.0.16:
    resolution: {integrity: sha512-oh8q2Zc32S6gd/j50GowEjKLoOVOwHP/bWVjKJInBwQqdOYMdPrf1oVlelTlyfFK3CKxL1uahMDAr+vy8T7yMQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/web-bluetooth/-/web-bluetooth-0.0.16.tgz}
    name: '@types/web-bluetooth'
    version: 0.0.16
    dev: false

  registry.npmmirror.com/@types/web-bluetooth@0.0.21:
    resolution: {integrity: sha512-oIQLCGWtcFZy2JW77j9k8nHzAOpqMHLQejDA48XXMWH6tjCQHz5RCFz1bzsmROyL6PUm+LLnUiI4BCn221inxA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/web-bluetooth/-/web-bluetooth-0.0.21.tgz}
    name: '@types/web-bluetooth'
    version: 0.0.21
    dev: false

  registry.npmmirror.com/@typescript-eslint/eslint-plugin@8.33.0(@typescript-eslint/parser@8.33.0)(eslint@9.28.0)(typescript@5.8.3):
    resolution: {integrity: sha512-CACyQuqSHt7ma3Ns601xykeBK/rDeZa3w6IS6UtMQbixO5DWy+8TilKkviGDH6jtWCo8FGRKEK5cLLkPvEammQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.33.0.tgz}
    id: registry.npmmirror.com/@typescript-eslint/eslint-plugin/8.33.0
    name: '@typescript-eslint/eslint-plugin'
    version: 8.33.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      '@typescript-eslint/parser': ^8.33.0
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'
    dependencies:
      '@eslint-community/regexpp': registry.npmmirror.com/@eslint-community/regexpp@4.12.1
      '@typescript-eslint/parser': registry.npmmirror.com/@typescript-eslint/parser@8.33.0(eslint@9.28.0)(typescript@5.8.3)
      '@typescript-eslint/scope-manager': registry.npmmirror.com/@typescript-eslint/scope-manager@8.33.0
      '@typescript-eslint/type-utils': registry.npmmirror.com/@typescript-eslint/type-utils@8.33.0(eslint@9.28.0)(typescript@5.8.3)
      '@typescript-eslint/utils': registry.npmmirror.com/@typescript-eslint/utils@8.33.0(eslint@9.28.0)(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': registry.npmmirror.com/@typescript-eslint/visitor-keys@8.33.0
      eslint: registry.npmmirror.com/eslint@9.28.0(jiti@2.4.2)
      graphemer: registry.npmmirror.com/graphemer@1.4.0
      ignore: registry.npmmirror.com/ignore@7.0.4
      natural-compare: registry.npmmirror.com/natural-compare@1.4.0
      ts-api-utils: registry.npmmirror.com/ts-api-utils@2.1.0(typescript@5.8.3)
      typescript: registry.npmmirror.com/typescript@5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@typescript-eslint/parser@8.33.0(eslint@9.28.0)(typescript@5.8.3):
    resolution: {integrity: sha512-JaehZvf6m0yqYp34+RVnihBAChkqeH+tqqhS0GuX1qgPpwLvmTPheKEs6OeCK6hVJgXZHJ2vbjnC9j119auStQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/parser/-/parser-8.33.0.tgz}
    id: registry.npmmirror.com/@typescript-eslint/parser/8.33.0
    name: '@typescript-eslint/parser'
    version: 8.33.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'
    dependencies:
      '@typescript-eslint/scope-manager': registry.npmmirror.com/@typescript-eslint/scope-manager@8.33.0
      '@typescript-eslint/types': registry.npmmirror.com/@typescript-eslint/types@8.33.0
      '@typescript-eslint/typescript-estree': registry.npmmirror.com/@typescript-eslint/typescript-estree@8.33.0(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': registry.npmmirror.com/@typescript-eslint/visitor-keys@8.33.0
      debug: registry.npmmirror.com/debug@4.4.1
      eslint: registry.npmmirror.com/eslint@9.28.0(jiti@2.4.2)
      typescript: registry.npmmirror.com/typescript@5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@typescript-eslint/project-service@8.33.0(typescript@5.8.3):
    resolution: {integrity: sha512-d1hz0u9l6N+u/gcrk6s6gYdl7/+pp8yHheRTqP6X5hVDKALEaTn8WfGiit7G511yueBEL3OpOEpD+3/MBdoN+A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/project-service/-/project-service-8.33.0.tgz}
    id: registry.npmmirror.com/@typescript-eslint/project-service/8.33.0
    name: '@typescript-eslint/project-service'
    version: 8.33.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@typescript-eslint/tsconfig-utils': registry.npmmirror.com/@typescript-eslint/tsconfig-utils@8.33.0(typescript@5.8.3)
      '@typescript-eslint/types': registry.npmmirror.com/@typescript-eslint/types@8.33.0
      debug: registry.npmmirror.com/debug@4.4.1
    transitivePeerDependencies:
      - supports-color
      - typescript
    dev: true

  registry.npmmirror.com/@typescript-eslint/scope-manager@8.33.0:
    resolution: {integrity: sha512-LMi/oqrzpqxyO72ltP+dBSP6V0xiUb4saY7WLtxSfiNEBI8m321LLVFU9/QDJxjDQG9/tjSqKz/E3380TEqSTw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/scope-manager/-/scope-manager-8.33.0.tgz}
    name: '@typescript-eslint/scope-manager'
    version: 8.33.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@typescript-eslint/types': registry.npmmirror.com/@typescript-eslint/types@8.33.0
      '@typescript-eslint/visitor-keys': registry.npmmirror.com/@typescript-eslint/visitor-keys@8.33.0
    dev: true

  registry.npmmirror.com/@typescript-eslint/tsconfig-utils@8.33.0(typescript@5.8.3):
    resolution: {integrity: sha512-sTkETlbqhEoiFmGr1gsdq5HyVbSOF0145SYDJ/EQmXHtKViCaGvnyLqWFFHtEXoS0J1yU8Wyou2UGmgW88fEug==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.0.tgz}
    id: registry.npmmirror.com/@typescript-eslint/tsconfig-utils/8.33.0
    name: '@typescript-eslint/tsconfig-utils'
    version: 8.33.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'
    dependencies:
      typescript: registry.npmmirror.com/typescript@5.8.3
    dev: true

  registry.npmmirror.com/@typescript-eslint/type-utils@8.33.0(eslint@9.28.0)(typescript@5.8.3):
    resolution: {integrity: sha512-lScnHNCBqL1QayuSrWeqAL5GmqNdVUQAAMTaCwdYEdWfIrSrOGzyLGRCHXcCixa5NK6i5l0AfSO2oBSjCjf4XQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/type-utils/-/type-utils-8.33.0.tgz}
    id: registry.npmmirror.com/@typescript-eslint/type-utils/8.33.0
    name: '@typescript-eslint/type-utils'
    version: 8.33.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'
    dependencies:
      '@typescript-eslint/typescript-estree': registry.npmmirror.com/@typescript-eslint/typescript-estree@8.33.0(typescript@5.8.3)
      '@typescript-eslint/utils': registry.npmmirror.com/@typescript-eslint/utils@8.33.0(eslint@9.28.0)(typescript@5.8.3)
      debug: registry.npmmirror.com/debug@4.4.1
      eslint: registry.npmmirror.com/eslint@9.28.0(jiti@2.4.2)
      ts-api-utils: registry.npmmirror.com/ts-api-utils@2.1.0(typescript@5.8.3)
      typescript: registry.npmmirror.com/typescript@5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@typescript-eslint/types@8.33.0:
    resolution: {integrity: sha512-DKuXOKpM5IDT1FA2g9x9x1Ug81YuKrzf4mYX8FAVSNu5Wo/LELHWQyM1pQaDkI42bX15PWl0vNPt1uGiIFUOpg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/types/-/types-8.33.0.tgz}
    name: '@typescript-eslint/types'
    version: 8.33.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  registry.npmmirror.com/@typescript-eslint/typescript-estree@8.33.0(typescript@5.8.3):
    resolution: {integrity: sha512-vegY4FQoB6jL97Tu/lWRsAiUUp8qJTqzAmENH2k59SJhw0Th1oszb9Idq/FyyONLuNqT1OADJPXfyUNOR8SzAQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/typescript-estree/-/typescript-estree-8.33.0.tgz}
    id: registry.npmmirror.com/@typescript-eslint/typescript-estree/8.33.0
    name: '@typescript-eslint/typescript-estree'
    version: 8.33.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'
    dependencies:
      '@typescript-eslint/project-service': registry.npmmirror.com/@typescript-eslint/project-service@8.33.0(typescript@5.8.3)
      '@typescript-eslint/tsconfig-utils': registry.npmmirror.com/@typescript-eslint/tsconfig-utils@8.33.0(typescript@5.8.3)
      '@typescript-eslint/types': registry.npmmirror.com/@typescript-eslint/types@8.33.0
      '@typescript-eslint/visitor-keys': registry.npmmirror.com/@typescript-eslint/visitor-keys@8.33.0
      debug: registry.npmmirror.com/debug@4.4.1
      fast-glob: registry.npmmirror.com/fast-glob@3.3.3
      is-glob: registry.npmmirror.com/is-glob@4.0.3
      minimatch: registry.npmmirror.com/minimatch@9.0.5
      semver: registry.npmmirror.com/semver@7.7.2
      ts-api-utils: registry.npmmirror.com/ts-api-utils@2.1.0(typescript@5.8.3)
      typescript: registry.npmmirror.com/typescript@5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@typescript-eslint/utils@8.33.0(eslint@9.28.0)(typescript@5.8.3):
    resolution: {integrity: sha512-lPFuQaLA9aSNa7D5u2EpRiqdAUhzShwGg/nhpBlc4GR6kcTABttCuyjFs8BcEZ8VWrjCBof/bePhP3Q3fS+Yrw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/utils/-/utils-8.33.0.tgz}
    id: registry.npmmirror.com/@typescript-eslint/utils/8.33.0
    name: '@typescript-eslint/utils'
    version: 8.33.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'
    dependencies:
      '@eslint-community/eslint-utils': registry.npmmirror.com/@eslint-community/eslint-utils@4.7.0(eslint@9.28.0)
      '@typescript-eslint/scope-manager': registry.npmmirror.com/@typescript-eslint/scope-manager@8.33.0
      '@typescript-eslint/types': registry.npmmirror.com/@typescript-eslint/types@8.33.0
      '@typescript-eslint/typescript-estree': registry.npmmirror.com/@typescript-eslint/typescript-estree@8.33.0(typescript@5.8.3)
      eslint: registry.npmmirror.com/eslint@9.28.0(jiti@2.4.2)
      typescript: registry.npmmirror.com/typescript@5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@typescript-eslint/visitor-keys@8.33.0:
    resolution: {integrity: sha512-7RW7CMYoskiz5OOGAWjJFxgb7c5UNjTG292gYhWeOAcFmYCtVCSqjqSBj5zMhxbXo2JOW95YYrUWJfU0zrpaGQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@typescript-eslint/visitor-keys/-/visitor-keys-8.33.0.tgz}
    name: '@typescript-eslint/visitor-keys'
    version: 8.33.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@typescript-eslint/types': registry.npmmirror.com/@typescript-eslint/types@8.33.0
      eslint-visitor-keys: registry.npmmirror.com/eslint-visitor-keys@4.2.0
    dev: true

  registry.npmmirror.com/@vitejs/plugin-vue@5.2.4(vite@6.3.5)(vue@3.5.16):
    resolution: {integrity: sha512-7Yx/SXSOcQq5HiiV3orevHUFn+pmMB4cgbEkDYgnkUWb0WfeQ/wa2yFv6D5ICiCQOVpjA7vYDXrC7AGO8yjDHA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vitejs/plugin-vue/-/plugin-vue-5.2.4.tgz}
    id: registry.npmmirror.com/@vitejs/plugin-vue/5.2.4
    name: '@vitejs/plugin-vue'
    version: 5.2.4
    engines: {node: ^18.0.0 || >=20.0.0}
    peerDependencies:
      vite: ^5.0.0 || ^6.0.0
      vue: ^3.2.25
    dependencies:
      vite: registry.npmmirror.com/vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)
      vue: registry.npmmirror.com/vue@3.5.16(typescript@5.8.3)
    dev: true

  registry.npmmirror.com/@volar/language-core@2.4.14:
    resolution: {integrity: sha512-X6beusV0DvuVseaOEy7GoagS4rYHgDHnTrdOj5jeUb49fW5ceQyP9Ej5rBhqgz2wJggl+2fDbbojq1XKaxDi6w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@volar/language-core/-/language-core-2.4.14.tgz}
    name: '@volar/language-core'
    version: 2.4.14
    dependencies:
      '@volar/source-map': registry.npmmirror.com/@volar/source-map@2.4.14
    dev: true

  registry.npmmirror.com/@volar/source-map@2.4.14:
    resolution: {integrity: sha512-5TeKKMh7Sfxo8021cJfmBzcjfY1SsXsPMMjMvjY7ivesdnybqqS+GxGAoXHAOUawQTwtdUxgP65Im+dEmvWtYQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@volar/source-map/-/source-map-2.4.14.tgz}
    name: '@volar/source-map'
    version: 2.4.14
    dev: true

  registry.npmmirror.com/@volar/typescript@2.4.14:
    resolution: {integrity: sha512-p8Z6f/bZM3/HyCdRNFZOEEzts51uV8WHeN8Tnfnm2EBv6FDB2TQLzfVx7aJvnl8ofKAOnS64B2O8bImBFaauRw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@volar/typescript/-/typescript-2.4.14.tgz}
    name: '@volar/typescript'
    version: 2.4.14
    dependencies:
      '@volar/language-core': registry.npmmirror.com/@volar/language-core@2.4.14
      path-browserify: registry.npmmirror.com/path-browserify@1.0.1
      vscode-uri: registry.npmmirror.com/vscode-uri@3.1.0
    dev: true

  registry.npmmirror.com/@vue/babel-helper-vue-transform-on@1.4.0:
    resolution: {integrity: sha512-mCokbouEQ/ocRce/FpKCRItGo+013tHg7tixg3DUNS+6bmIchPt66012kBMm476vyEIJPafrvOf4E5OYj3shSw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/babel-helper-vue-transform-on/-/babel-helper-vue-transform-on-1.4.0.tgz}
    name: '@vue/babel-helper-vue-transform-on'
    version: 1.4.0
    dev: true

  registry.npmmirror.com/@vue/babel-plugin-jsx@1.4.0(@babel/core@7.27.4):
    resolution: {integrity: sha512-9zAHmwgMWlaN6qRKdrg1uKsBKHvnUU+Py+MOCTuYZBoZsopa90Di10QRjB+YPnVss0BZbG/H5XFwJY1fTxJWhA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/babel-plugin-jsx/-/babel-plugin-jsx-1.4.0.tgz}
    id: registry.npmmirror.com/@vue/babel-plugin-jsx/1.4.0
    name: '@vue/babel-plugin-jsx'
    version: 1.4.0
    peerDependencies:
      '@babel/core': ^7.0.0-0
    peerDependenciesMeta:
      '@babel/core':
        optional: true
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.4
      '@babel/helper-module-imports': registry.npmmirror.com/@babel/helper-module-imports@7.27.1
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
      '@babel/plugin-syntax-jsx': registry.npmmirror.com/@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.4)
      '@babel/template': registry.npmmirror.com/@babel/template@7.27.2
      '@babel/traverse': registry.npmmirror.com/@babel/traverse@7.27.4
      '@babel/types': registry.npmmirror.com/@babel/types@7.27.3
      '@vue/babel-helper-vue-transform-on': registry.npmmirror.com/@vue/babel-helper-vue-transform-on@1.4.0
      '@vue/babel-plugin-resolve-type': registry.npmmirror.com/@vue/babel-plugin-resolve-type@1.4.0(@babel/core@7.27.4)
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.16
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@vue/babel-plugin-resolve-type@1.4.0(@babel/core@7.27.4):
    resolution: {integrity: sha512-4xqDRRbQQEWHQyjlYSgZsWj44KfiF6D+ktCuXyZ8EnVDYV3pztmXJDf1HveAjUAXxAnR8daCQT51RneWWxtTyQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/babel-plugin-resolve-type/-/babel-plugin-resolve-type-1.4.0.tgz}
    id: registry.npmmirror.com/@vue/babel-plugin-resolve-type/1.4.0
    name: '@vue/babel-plugin-resolve-type'
    version: 1.4.0
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/code-frame': registry.npmmirror.com/@babel/code-frame@7.27.1
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.4
      '@babel/helper-module-imports': registry.npmmirror.com/@babel/helper-module-imports@7.27.1
      '@babel/helper-plugin-utils': registry.npmmirror.com/@babel/helper-plugin-utils@7.27.1
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.27.4
      '@vue/compiler-sfc': registry.npmmirror.com/@vue/compiler-sfc@3.5.16
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@vue/compiler-core@3.5.16:
    resolution: {integrity: sha512-AOQS2eaQOaaZQoL1u+2rCJIKDruNXVBZSiUD3chnUrsoX5ZTQMaCvXlWNIfxBJuU15r1o7+mpo5223KVtIhAgQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.5.16.tgz}
    name: '@vue/compiler-core'
    version: 3.5.16
    dependencies:
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.27.4
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.16
      entities: registry.npmmirror.com/entities@4.5.0
      estree-walker: registry.npmmirror.com/estree-walker@2.0.2
      source-map-js: registry.npmmirror.com/source-map-js@1.2.1

  registry.npmmirror.com/@vue/compiler-dom@3.5.16:
    resolution: {integrity: sha512-SSJIhBr/teipXiXjmWOVWLnxjNGo65Oj/8wTEQz0nqwQeP75jWZ0n4sF24Zxoht1cuJoWopwj0J0exYwCJ0dCQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.5.16.tgz}
    name: '@vue/compiler-dom'
    version: 3.5.16
    dependencies:
      '@vue/compiler-core': registry.npmmirror.com/@vue/compiler-core@3.5.16
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.16

  registry.npmmirror.com/@vue/compiler-sfc@3.5.16:
    resolution: {integrity: sha512-rQR6VSFNpiinDy/DVUE0vHoIDUF++6p910cgcZoaAUm3POxgNOOdS/xgoll3rNdKYTYPnnbARDCZOyZ+QSe6Pw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.5.16.tgz}
    name: '@vue/compiler-sfc'
    version: 3.5.16
    dependencies:
      '@babel/parser': registry.npmmirror.com/@babel/parser@7.27.4
      '@vue/compiler-core': registry.npmmirror.com/@vue/compiler-core@3.5.16
      '@vue/compiler-dom': registry.npmmirror.com/@vue/compiler-dom@3.5.16
      '@vue/compiler-ssr': registry.npmmirror.com/@vue/compiler-ssr@3.5.16
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.16
      estree-walker: registry.npmmirror.com/estree-walker@2.0.2
      magic-string: registry.npmmirror.com/magic-string@0.30.17
      postcss: registry.npmmirror.com/postcss@8.5.4
      source-map-js: registry.npmmirror.com/source-map-js@1.2.1

  registry.npmmirror.com/@vue/compiler-ssr@3.5.16:
    resolution: {integrity: sha512-d2V7kfxbdsjrDSGlJE7my1ZzCXViEcqN6w14DOsDrUCHEA6vbnVCpRFfrc4ryCP/lCKzX2eS1YtnLE/BuC9f/A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/compiler-ssr/-/compiler-ssr-3.5.16.tgz}
    name: '@vue/compiler-ssr'
    version: 3.5.16
    dependencies:
      '@vue/compiler-dom': registry.npmmirror.com/@vue/compiler-dom@3.5.16
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.16

  registry.npmmirror.com/@vue/compiler-vue2@2.7.16:
    resolution: {integrity: sha512-qYC3Psj9S/mfu9uVi5WvNZIzq+xnXMhOwbTFKKDD7b1lhpnn71jXSFdTQ+WsIEk0ONCd7VV2IMm7ONl6tbQ86A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/compiler-vue2/-/compiler-vue2-2.7.16.tgz}
    name: '@vue/compiler-vue2'
    version: 2.7.16
    dependencies:
      de-indent: registry.npmmirror.com/de-indent@1.0.2
      he: registry.npmmirror.com/he@1.2.0
    dev: true

  registry.npmmirror.com/@vue/devtools-api@6.6.4:
    resolution: {integrity: sha512-sGhTPMuXqZ1rVOk32RylztWkfXTRhuS7vgAKv0zjqk8gbsHkJ7xfFf+jbySxt7tWObEJwyKaHMikV/WGDiQm8g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.6.4.tgz}
    name: '@vue/devtools-api'
    version: 6.6.4
    dev: false

  registry.npmmirror.com/@vue/devtools-api@7.7.6:
    resolution: {integrity: sha512-b2Xx0KvXZObePpXPYHvBRRJLDQn5nhKjXh7vUhMEtWxz1AYNFOVIsh5+HLP8xDGL7sy+Q7hXeUxPHB/KgbtsPw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-7.7.6.tgz}
    name: '@vue/devtools-api'
    version: 7.7.6
    dependencies:
      '@vue/devtools-kit': registry.npmmirror.com/@vue/devtools-kit@7.7.6
    dev: false

  registry.npmmirror.com/@vue/devtools-core@7.7.6(vite@6.3.5)(vue@3.5.16):
    resolution: {integrity: sha512-ghVX3zjKPtSHu94Xs03giRIeIWlb9M+gvDRVpIZ/cRIxKHdW6HE/sm1PT3rUYS3aV92CazirT93ne+7IOvGUWg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/devtools-core/-/devtools-core-7.7.6.tgz}
    id: registry.npmmirror.com/@vue/devtools-core/7.7.6
    name: '@vue/devtools-core'
    version: 7.7.6
    peerDependencies:
      vue: ^3.0.0
    dependencies:
      '@vue/devtools-kit': registry.npmmirror.com/@vue/devtools-kit@7.7.6
      '@vue/devtools-shared': registry.npmmirror.com/@vue/devtools-shared@7.7.6
      mitt: registry.npmmirror.com/mitt@3.0.1
      nanoid: registry.npmmirror.com/nanoid@5.1.5
      pathe: registry.npmmirror.com/pathe@2.0.3
      vite-hot-client: registry.npmmirror.com/vite-hot-client@2.0.4(vite@6.3.5)
      vue: registry.npmmirror.com/vue@3.5.16(typescript@5.8.3)
    transitivePeerDependencies:
      - vite
    dev: true

  registry.npmmirror.com/@vue/devtools-kit@7.7.6:
    resolution: {integrity: sha512-geu7ds7tem2Y7Wz+WgbnbZ6T5eadOvozHZ23Atk/8tksHMFOFylKi1xgGlQlVn0wlkEf4hu+vd5ctj1G4kFtwA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/devtools-kit/-/devtools-kit-7.7.6.tgz}
    name: '@vue/devtools-kit'
    version: 7.7.6
    dependencies:
      '@vue/devtools-shared': registry.npmmirror.com/@vue/devtools-shared@7.7.6
      birpc: registry.npmmirror.com/birpc@2.3.0
      hookable: registry.npmmirror.com/hookable@5.5.3
      mitt: registry.npmmirror.com/mitt@3.0.1
      perfect-debounce: registry.npmmirror.com/perfect-debounce@1.0.0
      speakingurl: registry.npmmirror.com/speakingurl@14.0.1
      superjson: registry.npmmirror.com/superjson@2.2.2

  registry.npmmirror.com/@vue/devtools-shared@7.7.6:
    resolution: {integrity: sha512-yFEgJZ/WblEsojQQceuyK6FzpFDx4kqrz2ohInxNj5/DnhoX023upTv4OD6lNPLAA5LLkbwPVb10o/7b+Y4FVA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/devtools-shared/-/devtools-shared-7.7.6.tgz}
    name: '@vue/devtools-shared'
    version: 7.7.6
    dependencies:
      rfdc: registry.npmmirror.com/rfdc@1.4.1

  registry.npmmirror.com/@vue/eslint-config-prettier@10.2.0(eslint@9.28.0)(prettier@3.5.3):
    resolution: {integrity: sha512-GL3YBLwv/+b86yHcNNfPJxOTtVFJ4Mbc9UU3zR+KVoG7SwGTjPT+32fXamscNumElhcpXW3mT0DgzS9w32S7Bw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/eslint-config-prettier/-/eslint-config-prettier-10.2.0.tgz}
    id: registry.npmmirror.com/@vue/eslint-config-prettier/10.2.0
    name: '@vue/eslint-config-prettier'
    version: 10.2.0
    peerDependencies:
      eslint: '>= 8.21.0'
      prettier: '>= 3.0.0'
    dependencies:
      eslint: registry.npmmirror.com/eslint@9.28.0(jiti@2.4.2)
      eslint-config-prettier: registry.npmmirror.com/eslint-config-prettier@10.1.5(eslint@9.28.0)
      eslint-plugin-prettier: registry.npmmirror.com/eslint-plugin-prettier@5.4.1(eslint-config-prettier@10.1.5)(eslint@9.28.0)(prettier@3.5.3)
      prettier: registry.npmmirror.com/prettier@3.5.3
    transitivePeerDependencies:
      - '@types/eslint'
    dev: true

  registry.npmmirror.com/@vue/eslint-config-typescript@14.5.0(eslint-plugin-vue@10.0.1)(eslint@9.28.0)(typescript@5.8.3):
    resolution: {integrity: sha512-5oPOyuwkw++AP5gHDh5YFmST50dPfWOcm3/W7Nbh42IK5O3H74ytWAw0TrCRTaBoD/02khnWXuZf1Bz1xflavQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/eslint-config-typescript/-/eslint-config-typescript-14.5.0.tgz}
    id: registry.npmmirror.com/@vue/eslint-config-typescript/14.5.0
    name: '@vue/eslint-config-typescript'
    version: 14.5.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^9.10.0
      eslint-plugin-vue: ^9.28.0 || ^10.0.0
      typescript: '>=4.8.4'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/utils': registry.npmmirror.com/@typescript-eslint/utils@8.33.0(eslint@9.28.0)(typescript@5.8.3)
      eslint: registry.npmmirror.com/eslint@9.28.0(jiti@2.4.2)
      eslint-plugin-vue: registry.npmmirror.com/eslint-plugin-vue@10.0.1(eslint@9.28.0)(vue-eslint-parser@10.1.3)
      fast-glob: registry.npmmirror.com/fast-glob@3.3.3
      typescript: registry.npmmirror.com/typescript@5.8.3
      typescript-eslint: registry.npmmirror.com/typescript-eslint@8.33.0(eslint@9.28.0)(typescript@5.8.3)
      vue-eslint-parser: registry.npmmirror.com/vue-eslint-parser@10.1.3(eslint@9.28.0)
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@vue/language-core@2.2.10(typescript@5.8.3):
    resolution: {integrity: sha512-+yNoYx6XIKuAO8Mqh1vGytu8jkFEOH5C8iOv3i8Z/65A7x9iAOXA97Q+PqZ3nlm2lxf5rOJuIGI/wDtx/riNYw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/language-core/-/language-core-2.2.10.tgz}
    id: registry.npmmirror.com/@vue/language-core/2.2.10
    name: '@vue/language-core'
    version: 2.2.10
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@volar/language-core': registry.npmmirror.com/@volar/language-core@2.4.14
      '@vue/compiler-dom': registry.npmmirror.com/@vue/compiler-dom@3.5.16
      '@vue/compiler-vue2': registry.npmmirror.com/@vue/compiler-vue2@2.7.16
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.16
      alien-signals: registry.npmmirror.com/alien-signals@1.0.13
      minimatch: registry.npmmirror.com/minimatch@9.0.5
      muggle-string: registry.npmmirror.com/muggle-string@0.4.1
      path-browserify: registry.npmmirror.com/path-browserify@1.0.1
      typescript: registry.npmmirror.com/typescript@5.8.3
    dev: true

  registry.npmmirror.com/@vue/reactivity@3.5.16:
    resolution: {integrity: sha512-FG5Q5ee/kxhIm1p2bykPpPwqiUBV3kFySsHEQha5BJvjXdZTUfmya7wP7zC39dFuZAcf/PD5S4Lni55vGLMhvA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.5.16.tgz}
    name: '@vue/reactivity'
    version: 3.5.16
    dependencies:
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.16

  registry.npmmirror.com/@vue/runtime-core@3.5.16:
    resolution: {integrity: sha512-bw5Ykq6+JFHYxrQa7Tjr+VSzw7Dj4ldR/udyBZbq73fCdJmyy5MPIFR9IX/M5Qs+TtTjuyUTCnmK3lWWwpAcFQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/runtime-core/-/runtime-core-3.5.16.tgz}
    name: '@vue/runtime-core'
    version: 3.5.16
    dependencies:
      '@vue/reactivity': registry.npmmirror.com/@vue/reactivity@3.5.16
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.16

  registry.npmmirror.com/@vue/runtime-dom@3.5.16:
    resolution: {integrity: sha512-T1qqYJsG2xMGhImRUV9y/RseB9d0eCYZQ4CWca9ztCuiPj/XWNNN+lkNBuzVbia5z4/cgxdL28NoQCvC0Xcfww==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/runtime-dom/-/runtime-dom-3.5.16.tgz}
    name: '@vue/runtime-dom'
    version: 3.5.16
    dependencies:
      '@vue/reactivity': registry.npmmirror.com/@vue/reactivity@3.5.16
      '@vue/runtime-core': registry.npmmirror.com/@vue/runtime-core@3.5.16
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.16
      csstype: registry.npmmirror.com/csstype@3.1.3

  registry.npmmirror.com/@vue/server-renderer@3.5.16(vue@3.5.16):
    resolution: {integrity: sha512-BrX0qLiv/WugguGsnQUJiYOE0Fe5mZTwi6b7X/ybGB0vfrPH9z0gD/Y6WOR1sGCgX4gc25L1RYS5eYQKDMoNIg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/server-renderer/-/server-renderer-3.5.16.tgz}
    id: registry.npmmirror.com/@vue/server-renderer/3.5.16
    name: '@vue/server-renderer'
    version: 3.5.16
    peerDependencies:
      vue: 3.5.16
    dependencies:
      '@vue/compiler-ssr': registry.npmmirror.com/@vue/compiler-ssr@3.5.16
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.16
      vue: registry.npmmirror.com/vue@3.5.16(typescript@5.8.3)

  registry.npmmirror.com/@vue/shared@3.5.16:
    resolution: {integrity: sha512-c/0fWy3Jw6Z8L9FmTyYfkpM5zklnqqa9+a6dz3DvONRKW2NEbh46BP0FHuLFSWi2TnQEtp91Z6zOWNrU6QiyPg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/shared/-/shared-3.5.16.tgz}
    name: '@vue/shared'
    version: 3.5.16

  registry.npmmirror.com/@vue/tsconfig@0.7.0(typescript@5.8.3)(vue@3.5.16):
    resolution: {integrity: sha512-ku2uNz5MaZ9IerPPUyOHzyjhXoX2kVJaVf7hL315DC17vS6IiZRmmCPfggNbU16QTvM80+uYYy3eYJB59WCtvg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vue/tsconfig/-/tsconfig-0.7.0.tgz}
    id: registry.npmmirror.com/@vue/tsconfig/0.7.0
    name: '@vue/tsconfig'
    version: 0.7.0
    peerDependencies:
      typescript: 5.x
      vue: ^3.4.0
    peerDependenciesMeta:
      typescript:
        optional: true
      vue:
        optional: true
    dependencies:
      typescript: registry.npmmirror.com/typescript@5.8.3
      vue: registry.npmmirror.com/vue@3.5.16(typescript@5.8.3)
    dev: true

  registry.npmmirror.com/@vueuse/core@13.3.0(vue@3.5.16):
    resolution: {integrity: sha512-uYRz5oEfebHCoRhK4moXFM3NSCd5vu2XMLOq/Riz5FdqZMy2RvBtazdtL3gEcmDyqkztDe9ZP/zymObMIbiYSg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vueuse/core/-/core-13.3.0.tgz}
    id: registry.npmmirror.com/@vueuse/core/13.3.0
    name: '@vueuse/core'
    version: 13.3.0
    peerDependencies:
      vue: ^3.5.0
    dependencies:
      '@types/web-bluetooth': registry.npmmirror.com/@types/web-bluetooth@0.0.21
      '@vueuse/metadata': registry.npmmirror.com/@vueuse/metadata@13.3.0
      '@vueuse/shared': registry.npmmirror.com/@vueuse/shared@13.3.0(vue@3.5.16)
      vue: registry.npmmirror.com/vue@3.5.16(typescript@5.8.3)
    dev: false

  registry.npmmirror.com/@vueuse/core@9.13.0(vue@3.5.16):
    resolution: {integrity: sha512-pujnclbeHWxxPRqXWmdkKV5OX4Wk4YeK7wusHqRwU0Q7EFusHoqNA/aPhB6KCh9hEqJkLAJo7bb0Lh9b+OIVzw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vueuse/core/-/core-9.13.0.tgz}
    id: registry.npmmirror.com/@vueuse/core/9.13.0
    name: '@vueuse/core'
    version: 9.13.0
    dependencies:
      '@types/web-bluetooth': registry.npmmirror.com/@types/web-bluetooth@0.0.16
      '@vueuse/metadata': registry.npmmirror.com/@vueuse/metadata@9.13.0
      '@vueuse/shared': registry.npmmirror.com/@vueuse/shared@9.13.0(vue@3.5.16)
      vue-demi: registry.npmmirror.com/vue-demi@0.14.10(vue@3.5.16)
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue
    dev: false

  registry.npmmirror.com/@vueuse/metadata@13.3.0:
    resolution: {integrity: sha512-42IzJIOYCKIb0Yjv1JfaKpx8JlCiTmtCWrPxt7Ja6Wzoq0h79+YVXmBV03N966KEmDEESTbp5R/qO3AB5BDnGw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vueuse/metadata/-/metadata-13.3.0.tgz}
    name: '@vueuse/metadata'
    version: 13.3.0
    dev: false

  registry.npmmirror.com/@vueuse/metadata@9.13.0:
    resolution: {integrity: sha512-gdU7TKNAUVlXXLbaF+ZCfte8BjRJQWPCa2J55+7/h+yDtzw3vOoGQDRXzI6pyKyo6bXFT5/QoPE4hAknExjRLQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vueuse/metadata/-/metadata-9.13.0.tgz}
    name: '@vueuse/metadata'
    version: 9.13.0
    dev: false

  registry.npmmirror.com/@vueuse/shared@13.3.0(vue@3.5.16):
    resolution: {integrity: sha512-L1QKsF0Eg9tiZSFXTgodYnu0Rsa2P0En2LuLrIs/jgrkyiDuJSsPZK+tx+wU0mMsYHUYEjNsuE41uqqkuR8VhA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vueuse/shared/-/shared-13.3.0.tgz}
    id: registry.npmmirror.com/@vueuse/shared/13.3.0
    name: '@vueuse/shared'
    version: 13.3.0
    peerDependencies:
      vue: ^3.5.0
    dependencies:
      vue: registry.npmmirror.com/vue@3.5.16(typescript@5.8.3)
    dev: false

  registry.npmmirror.com/@vueuse/shared@9.13.0(vue@3.5.16):
    resolution: {integrity: sha512-UrnhU+Cnufu4S6JLCPZnkWh0WwZGUp72ktOF2DFptMlOs3TOdVv8xJN53zhHGARmVOsz5KqOls09+J1NR6sBKw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@vueuse/shared/-/shared-9.13.0.tgz}
    id: registry.npmmirror.com/@vueuse/shared/9.13.0
    name: '@vueuse/shared'
    version: 9.13.0
    dependencies:
      vue-demi: registry.npmmirror.com/vue-demi@0.14.10(vue@3.5.16)
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue
    dev: false

  registry.npmmirror.com/acorn-jsx@5.3.2(acorn@8.14.1):
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz}
    id: registry.npmmirror.com/acorn-jsx/5.3.2
    name: acorn-jsx
    version: 5.3.2
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: registry.npmmirror.com/acorn@8.14.1
    dev: true

  registry.npmmirror.com/acorn@8.14.1:
    resolution: {integrity: sha512-OvQ/2pUDKmgfCg++xsTX1wGxfTaszcHVcTctW4UJB4hibJx2HXxxO5UmVgyjMa+ZDsiaf5wWLXYpRWMmBI0QHg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/acorn/-/acorn-8.14.1.tgz}
    name: acorn
    version: 8.14.1
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  registry.npmmirror.com/ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz}
    name: ajv
    version: 6.12.6
    dependencies:
      fast-deep-equal: registry.npmmirror.com/fast-deep-equal@3.1.3
      fast-json-stable-stringify: registry.npmmirror.com/fast-json-stable-stringify@2.1.0
      json-schema-traverse: registry.npmmirror.com/json-schema-traverse@0.4.1
      uri-js: registry.npmmirror.com/uri-js@4.4.1
    dev: true

  registry.npmmirror.com/alien-signals@1.0.13:
    resolution: {integrity: sha512-OGj9yyTnJEttvzhTUWuscOvtqxq5vrhF7vL9oS0xJ2mK0ItPYP1/y+vCFebfxoEyAz0++1AIwJ5CMr+Fk3nDmg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/alien-signals/-/alien-signals-1.0.13.tgz}
    name: alien-signals
    version: 1.0.13
    dev: true

  registry.npmmirror.com/ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz}
    name: ansi-styles
    version: 4.3.0
    engines: {node: '>=8'}
    dependencies:
      color-convert: registry.npmmirror.com/color-convert@2.0.1
    dev: true

  registry.npmmirror.com/ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ansi-styles/-/ansi-styles-6.2.1.tgz}
    name: ansi-styles
    version: 6.2.1
    engines: {node: '>=12'}
    dev: true

  registry.npmmirror.com/argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/argparse/-/argparse-2.0.1.tgz}
    name: argparse
    version: 2.0.1

  registry.npmmirror.com/async-validator@4.2.5:
    resolution: {integrity: sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/async-validator/-/async-validator-4.2.5.tgz}
    name: async-validator
    version: 4.2.5
    dev: false

  registry.npmmirror.com/asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz}
    name: asynckit
    version: 0.4.0
    dev: false

  registry.npmmirror.com/axios@1.9.0:
    resolution: {integrity: sha512-re4CqKTJaURpzbLHtIi6XpDv20/CnpXOtjRY5/CU32L8gU8ek9UIivcfvSWvmKEngmVbrUtPpdDwWDWL7DNHvg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/axios/-/axios-1.9.0.tgz}
    name: axios
    version: 1.9.0
    dependencies:
      follow-redirects: registry.npmmirror.com/follow-redirects@1.15.9
      form-data: registry.npmmirror.com/form-data@4.0.2
      proxy-from-env: registry.npmmirror.com/proxy-from-env@1.1.0
    transitivePeerDependencies:
      - debug
    dev: false

  registry.npmmirror.com/balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz}
    name: balanced-match
    version: 1.0.2
    dev: true

  registry.npmmirror.com/birpc@2.3.0:
    resolution: {integrity: sha512-ijbtkn/F3Pvzb6jHypHRyve2QApOCZDR25D/VnkY2G/lBNcXCTsnsCxgY4k4PkVB7zfwzYbY3O9Lcqe3xufS5g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/birpc/-/birpc-2.3.0.tgz}
    name: birpc
    version: 2.3.0

  registry.npmmirror.com/boolbase@1.0.0:
    resolution: {integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/boolbase/-/boolbase-1.0.0.tgz}
    name: boolbase
    version: 1.0.0
    dev: true

  registry.npmmirror.com/brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz}
    name: brace-expansion
    version: 1.1.11
    dependencies:
      balanced-match: registry.npmmirror.com/balanced-match@1.0.2
      concat-map: registry.npmmirror.com/concat-map@0.0.1
    dev: true

  registry.npmmirror.com/brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/brace-expansion/-/brace-expansion-2.0.1.tgz}
    name: brace-expansion
    version: 2.0.1
    dependencies:
      balanced-match: registry.npmmirror.com/balanced-match@1.0.2
    dev: true

  registry.npmmirror.com/braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/braces/-/braces-3.0.3.tgz}
    name: braces
    version: 3.0.3
    engines: {node: '>=8'}
    dependencies:
      fill-range: registry.npmmirror.com/fill-range@7.1.1
    dev: true

  registry.npmmirror.com/browserslist@4.25.0:
    resolution: {integrity: sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/browserslist/-/browserslist-4.25.0.tgz}
    name: browserslist
    version: 4.25.0
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: registry.npmmirror.com/caniuse-lite@1.0.30001720
      electron-to-chromium: registry.npmmirror.com/electron-to-chromium@1.5.161
      node-releases: registry.npmmirror.com/node-releases@2.0.19
      update-browserslist-db: registry.npmmirror.com/update-browserslist-db@1.1.3(browserslist@4.25.0)
    dev: true

  registry.npmmirror.com/bundle-name@4.1.0:
    resolution: {integrity: sha512-tjwM5exMg6BGRI+kNmTntNsvdZS1X8BFYS6tnJ2hdH0kVxM6/eVZ2xy+FqStSWvYmtfFMDLIxurorHwDKfDz5Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/bundle-name/-/bundle-name-4.1.0.tgz}
    name: bundle-name
    version: 4.1.0
    engines: {node: '>=18'}
    dependencies:
      run-applescript: registry.npmmirror.com/run-applescript@7.0.0
    dev: true

  registry.npmmirror.com/call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz}
    name: call-bind-apply-helpers
    version: 1.0.2
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: registry.npmmirror.com/es-errors@1.3.0
      function-bind: registry.npmmirror.com/function-bind@1.1.2
    dev: false

  registry.npmmirror.com/callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/callsites/-/callsites-3.1.0.tgz}
    name: callsites
    version: 3.1.0
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/caniuse-lite@1.0.30001720:
    resolution: {integrity: sha512-Ec/2yV2nNPwb4DnTANEV99ZWwm3ZWfdlfkQbWSDDt+PsXEVYwlhPH8tdMaPunYTKKmz7AnHi2oNEi1GcmKCD8g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001720.tgz}
    name: caniuse-lite
    version: 1.0.30001720
    dev: true

  registry.npmmirror.com/chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz}
    name: chalk
    version: 4.1.2
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: registry.npmmirror.com/ansi-styles@4.3.0
      supports-color: registry.npmmirror.com/supports-color@7.2.0
    dev: true

  registry.npmmirror.com/color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz}
    name: color-convert
    version: 2.0.1
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: registry.npmmirror.com/color-name@1.1.4
    dev: true

  registry.npmmirror.com/color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz}
    name: color-name
    version: 1.1.4
    dev: true

  registry.npmmirror.com/combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz}
    name: combined-stream
    version: 1.0.8
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: registry.npmmirror.com/delayed-stream@1.0.0
    dev: false

  registry.npmmirror.com/concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/concat-map/-/concat-map-0.0.1.tgz}
    name: concat-map
    version: 0.0.1
    dev: true

  registry.npmmirror.com/convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/convert-source-map/-/convert-source-map-2.0.0.tgz}
    name: convert-source-map
    version: 2.0.0
    dev: true

  registry.npmmirror.com/copy-anything@3.0.5:
    resolution: {integrity: sha512-yCEafptTtb4bk7GLEQoM8KVJpxAfdBJYaXyzQEgQQQgYrZiDp8SJmGKlYza6CYjEDNstAdNdKA3UuoULlEbS6w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/copy-anything/-/copy-anything-3.0.5.tgz}
    name: copy-anything
    version: 3.0.5
    engines: {node: '>=12.13'}
    dependencies:
      is-what: registry.npmmirror.com/is-what@4.1.16

  registry.npmmirror.com/cropperjs@2.0.0:
    resolution: {integrity: sha512-TO2j0Qre01kPHbow4FuTrbdEB4jTmGRySxW49jyEIqlJZuEBfrvCTT0vC3eRB2WBXudDfKi1Onako6DKWKxeAQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cropperjs/-/cropperjs-2.0.0.tgz}
    name: cropperjs
    version: 2.0.0
    dependencies:
      '@cropper/elements': registry.npmmirror.com/@cropper/elements@2.0.0
      '@cropper/utils': registry.npmmirror.com/@cropper/utils@2.0.0
    dev: false

  registry.npmmirror.com/cross-spawn@7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.6.tgz}
    name: cross-spawn
    version: 7.0.6
    engines: {node: '>= 8'}
    dependencies:
      path-key: registry.npmmirror.com/path-key@3.1.1
      shebang-command: registry.npmmirror.com/shebang-command@2.0.0
      which: registry.npmmirror.com/which@2.0.2
    dev: true

  registry.npmmirror.com/crypto-js@4.2.0:
    resolution: {integrity: sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/crypto-js/-/crypto-js-4.2.0.tgz}
    name: crypto-js
    version: 4.2.0
    dev: false

  registry.npmmirror.com/cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/*****************************/Vg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cssesc/-/cssesc-3.0.0.tgz}
    name: cssesc
    version: 3.0.0
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  registry.npmmirror.com/csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/csstype/-/csstype-3.1.3.tgz}
    name: csstype
    version: 3.1.3

  registry.npmmirror.com/dayjs@1.11.13:
    resolution: {integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/dayjs/-/dayjs-1.11.13.tgz}
    name: dayjs
    version: 1.11.13
    dev: false

  registry.npmmirror.com/de-indent@1.0.2:
    resolution: {integrity: sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/de-indent/-/de-indent-1.0.2.tgz}
    name: de-indent
    version: 1.0.2
    dev: true

  registry.npmmirror.com/debug@4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz}
    name: debug
    version: 4.4.1
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: registry.npmmirror.com/ms@2.1.3
    dev: true

  registry.npmmirror.com/deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/deep-is/-/deep-is-0.1.4.tgz}
    name: deep-is
    version: 0.1.4
    dev: true

  registry.npmmirror.com/default-browser-id@5.0.0:
    resolution: {integrity: sha512-A6p/pu/6fyBcA1TRz/GqWYPViplrftcW2gZC9q79ngNCKAeR/X3gcEdXQHl4KNXV+3wgIJ1CPkJQ3IHM6lcsyA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/default-browser-id/-/default-browser-id-5.0.0.tgz}
    name: default-browser-id
    version: 5.0.0
    engines: {node: '>=18'}
    dev: true

  registry.npmmirror.com/default-browser@5.2.1:
    resolution: {integrity: sha512-WY/3TUME0x3KPYdRRxEJJvXRHV4PyPoUsxtZa78lwItwRQRHhd2U9xOscaT/YTf8uCXIAjeJOFBVEh/7FtD8Xg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/default-browser/-/default-browser-5.2.1.tgz}
    name: default-browser
    version: 5.2.1
    engines: {node: '>=18'}
    dependencies:
      bundle-name: registry.npmmirror.com/bundle-name@4.1.0
      default-browser-id: registry.npmmirror.com/default-browser-id@5.0.0
    dev: true

  registry.npmmirror.com/define-lazy-prop@3.0.0:
    resolution: {integrity: sha512-N+MeXYoqr3pOgn8xfyRPREN7gHakLYjhsHhWGT3fWAiL4IkAt0iDw14QiiEm2bE30c5XX5q0FtAA3CK5f9/BUg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/define-lazy-prop/-/define-lazy-prop-3.0.0.tgz}
    name: define-lazy-prop
    version: 3.0.0
    engines: {node: '>=12'}
    dev: true

  registry.npmmirror.com/delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz}
    name: delayed-stream
    version: 1.0.0
    engines: {node: '>=0.4.0'}
    dev: false

  registry.npmmirror.com/dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/dunder-proto/-/dunder-proto-1.0.1.tgz}
    name: dunder-proto
    version: 1.0.1
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: registry.npmmirror.com/call-bind-apply-helpers@1.0.2
      es-errors: registry.npmmirror.com/es-errors@1.3.0
      gopd: registry.npmmirror.com/gopd@1.2.0
    dev: false

  registry.npmmirror.com/echarts@5.6.0:
    resolution: {integrity: sha512-oTbVTsXfKuEhxftHqL5xprgLoc0k7uScAwtryCgWF6hPYFLRwOUHiFmHGCBKP5NPFNkDVopOieyUqYGH8Fa3kA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/echarts/-/echarts-5.6.0.tgz}
    name: echarts
    version: 5.6.0
    dependencies:
      tslib: registry.npmmirror.com/tslib@2.3.0
      zrender: registry.npmmirror.com/zrender@5.6.1
    dev: false

  registry.npmmirror.com/electron-to-chromium@1.5.161:
    resolution: {integrity: sha512-hwtetwfKNZo/UlwHIVBlKZVdy7o8bIZxxKs0Mv/ROPiQQQmDgdm5a+KvKtBsxM8ZjFzTaCeLoodZ8jiBE3o9rA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.5.161.tgz}
    name: electron-to-chromium
    version: 1.5.161
    dev: true

  registry.npmmirror.com/element-plus@2.9.11(vue@3.5.16):
    resolution: {integrity: sha512-x4L/6YC8de4JtuE3vpaEugJdQIeHQaHtIYKyk67IeF6dTIiVax45aX4nWOygnh+xX+0gTvL6xO+9BZhPA3G82w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/element-plus/-/element-plus-2.9.11.tgz}
    id: registry.npmmirror.com/element-plus/2.9.11
    name: element-plus
    version: 2.9.11
    peerDependencies:
      vue: ^3.2.0
    dependencies:
      '@ctrl/tinycolor': registry.npmmirror.com/@ctrl/tinycolor@3.6.1
      '@element-plus/icons-vue': registry.npmmirror.com/@element-plus/icons-vue@2.3.1(vue@3.5.16)
      '@floating-ui/dom': registry.npmmirror.com/@floating-ui/dom@1.7.0
      '@popperjs/core': registry.npmmirror.com/@sxzz/popperjs-es@2.11.7
      '@types/lodash': registry.npmmirror.com/@types/lodash@4.17.17
      '@types/lodash-es': registry.npmmirror.com/@types/lodash-es@4.17.12
      '@vueuse/core': registry.npmmirror.com/@vueuse/core@9.13.0(vue@3.5.16)
      async-validator: registry.npmmirror.com/async-validator@4.2.5
      dayjs: registry.npmmirror.com/dayjs@1.11.13
      escape-html: registry.npmmirror.com/escape-html@1.0.3
      lodash: registry.npmmirror.com/lodash@4.17.21
      lodash-es: registry.npmmirror.com/lodash-es@4.17.21
      lodash-unified: registry.npmmirror.com/lodash-unified@1.0.3(@types/lodash-es@4.17.12)(lodash-es@4.17.21)(lodash@4.17.21)
      memoize-one: registry.npmmirror.com/memoize-one@6.0.0
      normalize-wheel-es: registry.npmmirror.com/normalize-wheel-es@1.2.0
      vue: registry.npmmirror.com/vue@3.5.16(typescript@5.8.3)
    transitivePeerDependencies:
      - '@vue/composition-api'
    dev: false

  registry.npmmirror.com/entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/entities/-/entities-4.5.0.tgz}
    name: entities
    version: 4.5.0
    engines: {node: '>=0.12'}

  registry.npmmirror.com/error-stack-parser-es@0.1.5:
    resolution: {integrity: sha512-xHku1X40RO+fO8yJ8Wh2f2rZWVjqyhb1zgq1yZ8aZRQkv6OOKhKWRUaht3eSCUbAOBaKIgM+ykwFLE+QUxgGeg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/error-stack-parser-es/-/error-stack-parser-es-0.1.5.tgz}
    name: error-stack-parser-es
    version: 0.1.5
    dev: true

  registry.npmmirror.com/es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/es-define-property/-/es-define-property-1.0.1.tgz}
    name: es-define-property
    version: 1.0.1
    engines: {node: '>= 0.4'}
    dev: false

  registry.npmmirror.com/es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/es-errors/-/es-errors-1.3.0.tgz}
    name: es-errors
    version: 1.3.0
    engines: {node: '>= 0.4'}
    dev: false

  registry.npmmirror.com/es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/es-object-atoms/-/es-object-atoms-1.1.1.tgz}
    name: es-object-atoms
    version: 1.1.1
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: registry.npmmirror.com/es-errors@1.3.0
    dev: false

  registry.npmmirror.com/es-set-tostringtag@2.1.0:
    resolution: {integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz}
    name: es-set-tostringtag
    version: 2.1.0
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: registry.npmmirror.com/es-errors@1.3.0
      get-intrinsic: registry.npmmirror.com/get-intrinsic@1.3.0
      has-tostringtag: registry.npmmirror.com/has-tostringtag@1.0.2
      hasown: registry.npmmirror.com/hasown@2.0.2
    dev: false

  registry.npmmirror.com/esbuild@0.25.5:
    resolution: {integrity: sha512-P8OtKZRv/5J5hhz0cUAdu/cLuPIKXpQl1R9pZtvmHWQvrAUVd0UNIPT4IB4W3rNOqVO0rlqHmCIbSwxh/c9yUQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild/-/esbuild-0.25.5.tgz}
    name: esbuild
    version: 0.25.5
    engines: {node: '>=18'}
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      '@esbuild/aix-ppc64': registry.npmmirror.com/@esbuild/aix-ppc64@0.25.5
      '@esbuild/android-arm': registry.npmmirror.com/@esbuild/android-arm@0.25.5
      '@esbuild/android-arm64': registry.npmmirror.com/@esbuild/android-arm64@0.25.5
      '@esbuild/android-x64': registry.npmmirror.com/@esbuild/android-x64@0.25.5
      '@esbuild/darwin-arm64': registry.npmmirror.com/@esbuild/darwin-arm64@0.25.5
      '@esbuild/darwin-x64': registry.npmmirror.com/@esbuild/darwin-x64@0.25.5
      '@esbuild/freebsd-arm64': registry.npmmirror.com/@esbuild/freebsd-arm64@0.25.5
      '@esbuild/freebsd-x64': registry.npmmirror.com/@esbuild/freebsd-x64@0.25.5
      '@esbuild/linux-arm': registry.npmmirror.com/@esbuild/linux-arm@0.25.5
      '@esbuild/linux-arm64': registry.npmmirror.com/@esbuild/linux-arm64@0.25.5
      '@esbuild/linux-ia32': registry.npmmirror.com/@esbuild/linux-ia32@0.25.5
      '@esbuild/linux-loong64': registry.npmmirror.com/@esbuild/linux-loong64@0.25.5
      '@esbuild/linux-mips64el': registry.npmmirror.com/@esbuild/linux-mips64el@0.25.5
      '@esbuild/linux-ppc64': registry.npmmirror.com/@esbuild/linux-ppc64@0.25.5
      '@esbuild/linux-riscv64': registry.npmmirror.com/@esbuild/linux-riscv64@0.25.5
      '@esbuild/linux-s390x': registry.npmmirror.com/@esbuild/linux-s390x@0.25.5
      '@esbuild/linux-x64': registry.npmmirror.com/@esbuild/linux-x64@0.25.5
      '@esbuild/netbsd-arm64': registry.npmmirror.com/@esbuild/netbsd-arm64@0.25.5
      '@esbuild/netbsd-x64': registry.npmmirror.com/@esbuild/netbsd-x64@0.25.5
      '@esbuild/openbsd-arm64': registry.npmmirror.com/@esbuild/openbsd-arm64@0.25.5
      '@esbuild/openbsd-x64': registry.npmmirror.com/@esbuild/openbsd-x64@0.25.5
      '@esbuild/sunos-x64': registry.npmmirror.com/@esbuild/sunos-x64@0.25.5
      '@esbuild/win32-arm64': registry.npmmirror.com/@esbuild/win32-arm64@0.25.5
      '@esbuild/win32-ia32': registry.npmmirror.com/@esbuild/win32-ia32@0.25.5
      '@esbuild/win32-x64': registry.npmmirror.com/@esbuild/win32-x64@0.25.5
    dev: true

  registry.npmmirror.com/escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/escalade/-/escalade-3.2.0.tgz}
    name: escalade
    version: 3.2.0
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz}
    name: escape-html
    version: 1.0.3
    dev: false

  registry.npmmirror.com/escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz}
    name: escape-string-regexp
    version: 4.0.0
    engines: {node: '>=10'}
    dev: true

  registry.npmmirror.com/eslint-config-prettier@10.1.5(eslint@9.28.0):
    resolution: {integrity: sha512-zc1UmCpNltmVY34vuLRV61r1K27sWuX39E+uyUnY8xS2Bex88VV9cugG+UZbRSRGtGyFboj+D8JODyme1plMpw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/eslint-config-prettier/-/eslint-config-prettier-10.1.5.tgz}
    id: registry.npmmirror.com/eslint-config-prettier/10.1.5
    name: eslint-config-prettier
    version: 10.1.5
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'
    dependencies:
      eslint: registry.npmmirror.com/eslint@9.28.0(jiti@2.4.2)
    dev: true

  registry.npmmirror.com/eslint-plugin-prettier@5.4.1(eslint-config-prettier@10.1.5)(eslint@9.28.0)(prettier@3.5.3):
    resolution: {integrity: sha512-9dF+KuU/Ilkq27A8idRP7N2DH8iUR6qXcjF3FR2wETY21PZdBrIjwCau8oboyGj9b7etWmTGEeM8e7oOed6ZWg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-5.4.1.tgz}
    id: registry.npmmirror.com/eslint-plugin-prettier/5.4.1
    name: eslint-plugin-prettier
    version: 5.4.1
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      '@types/eslint': '>=8.0.0'
      eslint: '>=8.0.0'
      eslint-config-prettier: '>= 7.0.0 <10.0.0 || >=10.1.0'
      prettier: '>=3.0.0'
    peerDependenciesMeta:
      '@types/eslint':
        optional: true
      eslint-config-prettier:
        optional: true
    dependencies:
      eslint: registry.npmmirror.com/eslint@9.28.0(jiti@2.4.2)
      eslint-config-prettier: registry.npmmirror.com/eslint-config-prettier@10.1.5(eslint@9.28.0)
      prettier: registry.npmmirror.com/prettier@3.5.3
      prettier-linter-helpers: registry.npmmirror.com/prettier-linter-helpers@1.0.0
      synckit: registry.npmmirror.com/synckit@0.11.8
    dev: true

  registry.npmmirror.com/eslint-plugin-vue@10.0.1(eslint@9.28.0)(vue-eslint-parser@10.1.3):
    resolution: {integrity: sha512-A5dRYc3eQ5i2rJFBW8J6F69ur/H7YfYg+5SCg6v829FU0BhM4fUTrRVR2d4MdZgzw0ioJEk6otYHEAnoGFqO4A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/eslint-plugin-vue/-/eslint-plugin-vue-10.0.1.tgz}
    id: registry.npmmirror.com/eslint-plugin-vue/10.0.1
    name: eslint-plugin-vue
    version: 10.0.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      vue-eslint-parser: ^10.0.0
    dependencies:
      '@eslint-community/eslint-utils': registry.npmmirror.com/@eslint-community/eslint-utils@4.7.0(eslint@9.28.0)
      eslint: registry.npmmirror.com/eslint@9.28.0(jiti@2.4.2)
      natural-compare: registry.npmmirror.com/natural-compare@1.4.0
      nth-check: registry.npmmirror.com/nth-check@2.1.1
      postcss-selector-parser: registry.npmmirror.com/postcss-selector-parser@6.1.2
      semver: registry.npmmirror.com/semver@7.7.2
      vue-eslint-parser: registry.npmmirror.com/vue-eslint-parser@10.1.3(eslint@9.28.0)
      xml-name-validator: registry.npmmirror.com/xml-name-validator@4.0.0
    dev: true

  registry.npmmirror.com/eslint-scope@8.3.0:
    resolution: {integrity: sha512-pUNxi75F8MJ/GdeKtVLSbYg4ZI34J6C0C7sbL4YOp2exGwen7ZsuBqKzUhXd0qMQ362yET3z+uPwKeg/0C2XCQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/eslint-scope/-/eslint-scope-8.3.0.tgz}
    name: eslint-scope
    version: 8.3.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      esrecurse: registry.npmmirror.com/esrecurse@4.3.0
      estraverse: registry.npmmirror.com/estraverse@5.3.0
    dev: true

  registry.npmmirror.com/eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz}
    name: eslint-visitor-keys
    version: 3.4.3
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  registry.npmmirror.com/eslint-visitor-keys@4.2.0:
    resolution: {integrity: sha512-UyLnSehNt62FFhSwjZlHmeokpRK59rcz29j+F1/aDgbkbRTk7wIc9XzdoasMUbRNKDM0qQt/+BJ4BrpFeABemw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-4.2.0.tgz}
    name: eslint-visitor-keys
    version: 4.2.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  registry.npmmirror.com/eslint@9.28.0(jiti@2.4.2):
    resolution: {integrity: sha512-ocgh41VhRlf9+fVpe7QKzwLj9c92fDiqOj8Y3Sd4/ZmVA4Btx4PlUYPq4pp9JDyupkf1upbEXecxL2mwNV7jPQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/eslint/-/eslint-9.28.0.tgz}
    id: registry.npmmirror.com/eslint/9.28.0
    name: eslint
    version: 9.28.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true
    dependencies:
      '@eslint-community/eslint-utils': registry.npmmirror.com/@eslint-community/eslint-utils@4.7.0(eslint@9.28.0)
      '@eslint-community/regexpp': registry.npmmirror.com/@eslint-community/regexpp@4.12.1
      '@eslint/config-array': registry.npmmirror.com/@eslint/config-array@0.20.0
      '@eslint/config-helpers': registry.npmmirror.com/@eslint/config-helpers@0.2.2
      '@eslint/core': registry.npmmirror.com/@eslint/core@0.14.0
      '@eslint/eslintrc': registry.npmmirror.com/@eslint/eslintrc@3.3.1
      '@eslint/js': registry.npmmirror.com/@eslint/js@9.28.0
      '@eslint/plugin-kit': registry.npmmirror.com/@eslint/plugin-kit@0.3.1
      '@humanfs/node': registry.npmmirror.com/@humanfs/node@0.16.6
      '@humanwhocodes/module-importer': registry.npmmirror.com/@humanwhocodes/module-importer@1.0.1
      '@humanwhocodes/retry': registry.npmmirror.com/@humanwhocodes/retry@0.4.3
      '@types/estree': registry.npmmirror.com/@types/estree@1.0.7
      '@types/json-schema': registry.npmmirror.com/@types/json-schema@7.0.15
      ajv: registry.npmmirror.com/ajv@6.12.6
      chalk: registry.npmmirror.com/chalk@4.1.2
      cross-spawn: registry.npmmirror.com/cross-spawn@7.0.6
      debug: registry.npmmirror.com/debug@4.4.1
      escape-string-regexp: registry.npmmirror.com/escape-string-regexp@4.0.0
      eslint-scope: registry.npmmirror.com/eslint-scope@8.3.0
      eslint-visitor-keys: registry.npmmirror.com/eslint-visitor-keys@4.2.0
      espree: registry.npmmirror.com/espree@10.3.0
      esquery: registry.npmmirror.com/esquery@1.6.0
      esutils: registry.npmmirror.com/esutils@2.0.3
      fast-deep-equal: registry.npmmirror.com/fast-deep-equal@3.1.3
      file-entry-cache: registry.npmmirror.com/file-entry-cache@8.0.0
      find-up: registry.npmmirror.com/find-up@5.0.0
      glob-parent: registry.npmmirror.com/glob-parent@6.0.2
      ignore: registry.npmmirror.com/ignore@5.3.2
      imurmurhash: registry.npmmirror.com/imurmurhash@0.1.4
      is-glob: registry.npmmirror.com/is-glob@4.0.3
      jiti: registry.npmmirror.com/jiti@2.4.2
      json-stable-stringify-without-jsonify: registry.npmmirror.com/json-stable-stringify-without-jsonify@1.0.1
      lodash.merge: registry.npmmirror.com/lodash.merge@4.6.2
      minimatch: registry.npmmirror.com/minimatch@3.1.2
      natural-compare: registry.npmmirror.com/natural-compare@1.4.0
      optionator: registry.npmmirror.com/optionator@0.9.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/espree@10.3.0:
    resolution: {integrity: sha512-0QYC8b24HWY8zjRnDTL6RiHfDbAWn63qb4LMj1Z4b076A4une81+z03Kg7l7mn/48PUTqoLptSXez8oknU8Clg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/espree/-/espree-10.3.0.tgz}
    name: espree
    version: 10.3.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      acorn: registry.npmmirror.com/acorn@8.14.1
      acorn-jsx: registry.npmmirror.com/acorn-jsx@5.3.2(acorn@8.14.1)
      eslint-visitor-keys: registry.npmmirror.com/eslint-visitor-keys@4.2.0
    dev: true

  registry.npmmirror.com/esquery@1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esquery/-/esquery-1.6.0.tgz}
    name: esquery
    version: 1.6.0
    engines: {node: '>=0.10'}
    dependencies:
      estraverse: registry.npmmirror.com/estraverse@5.3.0
    dev: true

  registry.npmmirror.com/esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esrecurse/-/esrecurse-4.3.0.tgz}
    name: esrecurse
    version: 4.3.0
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: registry.npmmirror.com/estraverse@5.3.0
    dev: true

  registry.npmmirror.com/estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz}
    name: estraverse
    version: 5.3.0
    engines: {node: '>=4.0'}
    dev: true

  registry.npmmirror.com/estree-walker@2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz}
    name: estree-walker
    version: 2.0.2

  registry.npmmirror.com/esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esutils/-/esutils-2.0.3.tgz}
    name: esutils
    version: 2.0.3
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/event-source-polyfill@1.0.31:
    resolution: {integrity: sha512-4IJSItgS/41IxN5UVAVuAyczwZF7ZIEsM1XAoUzIHA6A+xzusEZUutdXz2Nr+MQPLxfTiCvqE79/C8HT8fKFvA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/event-source-polyfill/-/event-source-polyfill-1.0.31.tgz}
    name: event-source-polyfill
    version: 1.0.31
    dev: false

  registry.npmmirror.com/execa@9.6.0:
    resolution: {integrity: sha512-jpWzZ1ZhwUmeWRhS7Qv3mhpOhLfwI+uAX4e5fOcXqwMR7EcJ0pj2kV1CVzHVMX/LphnKWD3LObjZCoJ71lKpHw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/execa/-/execa-9.6.0.tgz}
    name: execa
    version: 9.6.0
    engines: {node: ^18.19.0 || >=20.5.0}
    dependencies:
      '@sindresorhus/merge-streams': registry.npmmirror.com/@sindresorhus/merge-streams@4.0.0
      cross-spawn: registry.npmmirror.com/cross-spawn@7.0.6
      figures: registry.npmmirror.com/figures@6.1.0
      get-stream: registry.npmmirror.com/get-stream@9.0.1
      human-signals: registry.npmmirror.com/human-signals@8.0.1
      is-plain-obj: registry.npmmirror.com/is-plain-obj@4.1.0
      is-stream: registry.npmmirror.com/is-stream@4.0.1
      npm-run-path: registry.npmmirror.com/npm-run-path@6.0.0
      pretty-ms: registry.npmmirror.com/pretty-ms@9.2.0
      signal-exit: registry.npmmirror.com/signal-exit@4.1.0
      strip-final-newline: registry.npmmirror.com/strip-final-newline@4.0.0
      yoctocolors: registry.npmmirror.com/yoctocolors@2.1.1
    dev: true

  registry.npmmirror.com/fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz}
    name: fast-deep-equal
    version: 3.1.3
    dev: true

  registry.npmmirror.com/fast-diff@1.3.0:
    resolution: {integrity: sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fast-diff/-/fast-diff-1.3.0.tgz}
    name: fast-diff
    version: 1.3.0
    dev: true

  registry.npmmirror.com/fast-glob@3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fast-glob/-/fast-glob-3.3.3.tgz}
    name: fast-glob
    version: 3.3.3
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': registry.npmmirror.com/@nodelib/fs.stat@2.0.5
      '@nodelib/fs.walk': registry.npmmirror.com/@nodelib/fs.walk@1.2.8
      glob-parent: registry.npmmirror.com/glob-parent@5.1.2
      merge2: registry.npmmirror.com/merge2@1.4.1
      micromatch: registry.npmmirror.com/micromatch@4.0.8
    dev: true

  registry.npmmirror.com/fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz}
    name: fast-json-stable-stringify
    version: 2.1.0
    dev: true

  registry.npmmirror.com/fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz}
    name: fast-levenshtein
    version: 2.0.6
    dev: true

  registry.npmmirror.com/fastq@1.19.1:
    resolution: {integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fastq/-/fastq-1.19.1.tgz}
    name: fastq
    version: 1.19.1
    dependencies:
      reusify: registry.npmmirror.com/reusify@1.1.0
    dev: true

  registry.npmmirror.com/fdir@6.4.5(picomatch@4.0.2):
    resolution: {integrity: sha512-4BG7puHpVsIYxZUbiUE3RqGloLaSSwzYie5jvasC4LWuBWzZawynvYouhjbQKw2JuIGYdm0DzIxl8iVidKlUEw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fdir/-/fdir-6.4.5.tgz}
    id: registry.npmmirror.com/fdir/6.4.5
    name: fdir
    version: 6.4.5
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true
    dependencies:
      picomatch: registry.npmmirror.com/picomatch@4.0.2
    dev: true

  registry.npmmirror.com/figures@6.1.0:
    resolution: {integrity: sha512-d+l3qxjSesT4V7v2fh+QnmFnUWv9lSpjarhShNTgBOfA0ttejbQUAlHLitbjkoRiDulW0OPoQPYIGhIC8ohejg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/figures/-/figures-6.1.0.tgz}
    name: figures
    version: 6.1.0
    engines: {node: '>=18'}
    dependencies:
      is-unicode-supported: registry.npmmirror.com/is-unicode-supported@2.1.0
    dev: true

  registry.npmmirror.com/file-entry-cache@8.0.0:
    resolution: {integrity: sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/file-entry-cache/-/file-entry-cache-8.0.0.tgz}
    name: file-entry-cache
    version: 8.0.0
    engines: {node: '>=16.0.0'}
    dependencies:
      flat-cache: registry.npmmirror.com/flat-cache@4.0.1
    dev: true

  registry.npmmirror.com/fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fill-range/-/fill-range-7.1.1.tgz}
    name: fill-range
    version: 7.1.1
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: registry.npmmirror.com/to-regex-range@5.0.1
    dev: true

  registry.npmmirror.com/find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/find-up/-/find-up-5.0.0.tgz}
    name: find-up
    version: 5.0.0
    engines: {node: '>=10'}
    dependencies:
      locate-path: registry.npmmirror.com/locate-path@6.0.0
      path-exists: registry.npmmirror.com/path-exists@4.0.0
    dev: true

  registry.npmmirror.com/flat-cache@4.0.1:
    resolution: {integrity: sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/flat-cache/-/flat-cache-4.0.1.tgz}
    name: flat-cache
    version: 4.0.1
    engines: {node: '>=16'}
    dependencies:
      flatted: registry.npmmirror.com/flatted@3.3.3
      keyv: registry.npmmirror.com/keyv@4.5.4
    dev: true

  registry.npmmirror.com/flatted@3.3.3:
    resolution: {integrity: sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/flatted/-/flatted-3.3.3.tgz}
    name: flatted
    version: 3.3.3
    dev: true

  registry.npmmirror.com/follow-redirects@1.15.9:
    resolution: {integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.9.tgz}
    name: follow-redirects
    version: 1.15.9
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true
    dev: false

  registry.npmmirror.com/form-data@4.0.2:
    resolution: {integrity: sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/form-data/-/form-data-4.0.2.tgz}
    name: form-data
    version: 4.0.2
    engines: {node: '>= 6'}
    dependencies:
      asynckit: registry.npmmirror.com/asynckit@0.4.0
      combined-stream: registry.npmmirror.com/combined-stream@1.0.8
      es-set-tostringtag: registry.npmmirror.com/es-set-tostringtag@2.1.0
      mime-types: registry.npmmirror.com/mime-types@2.1.35
    dev: false

  registry.npmmirror.com/fs-extra@11.3.0:
    resolution: {integrity: sha512-Z4XaCL6dUDHfP/jT25jJKMmtxvuwbkrD1vNSMFlo9lNLY2c5FHYSQgHPRZUjAB26TpDEoW9HCOgplrdbaPV/ew==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fs-extra/-/fs-extra-11.3.0.tgz}
    name: fs-extra
    version: 11.3.0
    engines: {node: '>=14.14'}
    dependencies:
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
      jsonfile: registry.npmmirror.com/jsonfile@6.1.0
      universalify: registry.npmmirror.com/universalify@2.0.1
    dev: true

  registry.npmmirror.com/fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz}
    name: fsevents
    version: 2.3.3
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/function-bind/-/function-bind-1.1.2.tgz}
    name: function-bind
    version: 1.1.2
    dev: false

  registry.npmmirror.com/gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/gensync/-/gensync-1.0.0-beta.2.tgz}
    name: gensync
    version: 1.0.0-beta.2
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmmirror.com/get-intrinsic@1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.3.0.tgz}
    name: get-intrinsic
    version: 1.3.0
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: registry.npmmirror.com/call-bind-apply-helpers@1.0.2
      es-define-property: registry.npmmirror.com/es-define-property@1.0.1
      es-errors: registry.npmmirror.com/es-errors@1.3.0
      es-object-atoms: registry.npmmirror.com/es-object-atoms@1.1.1
      function-bind: registry.npmmirror.com/function-bind@1.1.2
      get-proto: registry.npmmirror.com/get-proto@1.0.1
      gopd: registry.npmmirror.com/gopd@1.2.0
      has-symbols: registry.npmmirror.com/has-symbols@1.1.0
      hasown: registry.npmmirror.com/hasown@2.0.2
      math-intrinsics: registry.npmmirror.com/math-intrinsics@1.1.0
    dev: false

  registry.npmmirror.com/get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/get-proto/-/get-proto-1.0.1.tgz}
    name: get-proto
    version: 1.0.1
    engines: {node: '>= 0.4'}
    dependencies:
      dunder-proto: registry.npmmirror.com/dunder-proto@1.0.1
      es-object-atoms: registry.npmmirror.com/es-object-atoms@1.1.1
    dev: false

  registry.npmmirror.com/get-stream@9.0.1:
    resolution: {integrity: sha512-kVCxPF3vQM/N0B1PmoqVUqgHP+EeVjmZSQn+1oCRPxd2P21P2F19lIgbR3HBosbB1PUhOAoctJnfEn2GbN2eZA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/get-stream/-/get-stream-9.0.1.tgz}
    name: get-stream
    version: 9.0.1
    engines: {node: '>=18'}
    dependencies:
      '@sec-ant/readable-stream': registry.npmmirror.com/@sec-ant/readable-stream@0.4.1
      is-stream: registry.npmmirror.com/is-stream@4.0.1
    dev: true

  registry.npmmirror.com/glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz}
    name: glob-parent
    version: 5.1.2
    engines: {node: '>= 6'}
    dependencies:
      is-glob: registry.npmmirror.com/is-glob@4.0.3
    dev: true

  registry.npmmirror.com/glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/glob-parent/-/glob-parent-6.0.2.tgz}
    name: glob-parent
    version: 6.0.2
    engines: {node: '>=10.13.0'}
    dependencies:
      is-glob: registry.npmmirror.com/is-glob@4.0.3
    dev: true

  registry.npmmirror.com/globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/globals/-/globals-11.12.0.tgz}
    name: globals
    version: 11.12.0
    engines: {node: '>=4'}
    dev: true

  registry.npmmirror.com/globals@14.0.0:
    resolution: {integrity: sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/globals/-/globals-14.0.0.tgz}
    name: globals
    version: 14.0.0
    engines: {node: '>=18'}
    dev: true

  registry.npmmirror.com/gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/gopd/-/gopd-1.2.0.tgz}
    name: gopd
    version: 1.2.0
    engines: {node: '>= 0.4'}
    dev: false

  registry.npmmirror.com/graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz}
    name: graceful-fs
    version: 4.2.11
    dev: true

  registry.npmmirror.com/graphemer@1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/graphemer/-/graphemer-1.4.0.tgz}
    name: graphemer
    version: 1.4.0
    dev: true

  registry.npmmirror.com/has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz}
    name: has-flag
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/has-symbols/-/has-symbols-1.1.0.tgz}
    name: has-symbols
    version: 1.1.0
    engines: {node: '>= 0.4'}
    dev: false

  registry.npmmirror.com/has-tostringtag@1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/has-tostringtag/-/has-tostringtag-1.0.2.tgz}
    name: has-tostringtag
    version: 1.0.2
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: registry.npmmirror.com/has-symbols@1.1.0
    dev: false

  registry.npmmirror.com/hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/hasown/-/hasown-2.0.2.tgz}
    name: hasown
    version: 2.0.2
    engines: {node: '>= 0.4'}
    dependencies:
      function-bind: registry.npmmirror.com/function-bind@1.1.2
    dev: false

  registry.npmmirror.com/he@1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/he/-/he-1.2.0.tgz}
    name: he
    version: 1.2.0
    hasBin: true
    dev: true

  registry.npmmirror.com/highlight.js@11.11.1:
    resolution: {integrity: sha512-Xwwo44whKBVCYoliBQwaPvtd/2tYFkRQtXDWj1nackaV2JPXx3L0+Jvd8/qCJ2p+ML0/XVkJ2q+Mr+UVdpJK5w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/highlight.js/-/highlight.js-11.11.1.tgz}
    name: highlight.js
    version: 11.11.1
    engines: {node: '>=12.0.0'}
    dev: false

  registry.npmmirror.com/hookable@5.5.3:
    resolution: {integrity: sha512-Yc+BQe8SvoXH1643Qez1zqLRmbA5rCL+sSmk6TVos0LWVfNIB7PGncdlId77WzLGSIB5KaWgTaNTs2lNVEI6VQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/hookable/-/hookable-5.5.3.tgz}
    name: hookable
    version: 5.5.3

  registry.npmmirror.com/human-signals@8.0.1:
    resolution: {integrity: sha512-eKCa6bwnJhvxj14kZk5NCPc6Hb6BdsU9DZcOnmQKSnO1VKrfV0zCvtttPZUsBvjmNDn8rpcJfpwSYnHBjc95MQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/human-signals/-/human-signals-8.0.1.tgz}
    name: human-signals
    version: 8.0.1
    engines: {node: '>=18.18.0'}
    dev: true

  registry.npmmirror.com/ignore@5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ignore/-/ignore-5.3.2.tgz}
    name: ignore
    version: 5.3.2
    engines: {node: '>= 4'}
    dev: true

  registry.npmmirror.com/ignore@7.0.4:
    resolution: {integrity: sha512-gJzzk+PQNznz8ysRrC0aOkBNVRBDtE1n53IqyqEf3PXrYwomFs5q4pGMizBMJF+ykh03insJ27hB8gSrD2Hn8A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ignore/-/ignore-7.0.4.tgz}
    name: ignore
    version: 7.0.4
    engines: {node: '>= 4'}
    dev: true

  registry.npmmirror.com/import-fresh@3.3.1:
    resolution: {integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.1.tgz}
    name: import-fresh
    version: 3.3.1
    engines: {node: '>=6'}
    dependencies:
      parent-module: registry.npmmirror.com/parent-module@1.0.1
      resolve-from: registry.npmmirror.com/resolve-from@4.0.0
    dev: true

  registry.npmmirror.com/imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.4.tgz}
    name: imurmurhash
    version: 0.1.4
    engines: {node: '>=0.8.19'}
    dev: true

  registry.npmmirror.com/is-docker@3.0.0:
    resolution: {integrity: sha512-eljcgEDlEns/7AXFosB5K/2nCM4P7FQPkGc/DWLy5rmFEWvZayGrik1d9/QIY5nJ4f9YsVvBkA6kJpHn9rISdQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-docker/-/is-docker-3.0.0.tgz}
    name: is-docker
    version: 3.0.0
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    hasBin: true
    dev: true

  registry.npmmirror.com/is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz}
    name: is-extglob
    version: 2.1.1
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz}
    name: is-glob
    version: 4.0.3
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: registry.npmmirror.com/is-extglob@2.1.1
    dev: true

  registry.npmmirror.com/is-inside-container@1.0.0:
    resolution: {integrity: sha512-KIYLCCJghfHZxqjYBE7rEy0OBuTd5xCHS7tHVgvCLkx7StIoaxwNW3hCALgEUjFfeRk+MG/Qxmp/vtETEF3tRA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-inside-container/-/is-inside-container-1.0.0.tgz}
    name: is-inside-container
    version: 1.0.0
    engines: {node: '>=14.16'}
    hasBin: true
    dependencies:
      is-docker: registry.npmmirror.com/is-docker@3.0.0
    dev: true

  registry.npmmirror.com/is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz}
    name: is-number
    version: 7.0.0
    engines: {node: '>=0.12.0'}
    dev: true

  registry.npmmirror.com/is-plain-obj@4.1.0:
    resolution: {integrity: sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-plain-obj/-/is-plain-obj-4.1.0.tgz}
    name: is-plain-obj
    version: 4.1.0
    engines: {node: '>=12'}
    dev: true

  registry.npmmirror.com/is-stream@4.0.1:
    resolution: {integrity: sha512-Dnz92NInDqYckGEUJv689RbRiTSEHCQ7wOVeALbkOz999YpqT46yMRIGtSNl2iCL1waAZSx40+h59NV/EwzV/A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-stream/-/is-stream-4.0.1.tgz}
    name: is-stream
    version: 4.0.1
    engines: {node: '>=18'}
    dev: true

  registry.npmmirror.com/is-unicode-supported@2.1.0:
    resolution: {integrity: sha512-mE00Gnza5EEB3Ds0HfMyllZzbBrmLOX3vfWoj9A9PEnTfratQ/BcaJOuMhnkhjXvb2+FkY3VuHqtAGpTPmglFQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-unicode-supported/-/is-unicode-supported-2.1.0.tgz}
    name: is-unicode-supported
    version: 2.1.0
    engines: {node: '>=18'}
    dev: true

  registry.npmmirror.com/is-what@4.1.16:
    resolution: {integrity: sha512-ZhMwEosbFJkA0YhFnNDgTM4ZxDRsS6HqTo7qsZM08fehyRYIYa0yHu5R6mgo1n/8MgaPBXiPimPD77baVFYg+A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-what/-/is-what-4.1.16.tgz}
    name: is-what
    version: 4.1.16
    engines: {node: '>=12.13'}

  registry.npmmirror.com/is-wsl@3.1.0:
    resolution: {integrity: sha512-UcVfVfaK4Sc4m7X3dUSoHoozQGBEFeDC+zVo06t98xe8CzHSZZBekNXH+tu0NalHolcJ/QAGqS46Hef7QXBIMw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-wsl/-/is-wsl-3.1.0.tgz}
    name: is-wsl
    version: 3.1.0
    engines: {node: '>=16'}
    dependencies:
      is-inside-container: registry.npmmirror.com/is-inside-container@1.0.0
    dev: true

  registry.npmmirror.com/isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz}
    name: isexe
    version: 2.0.0
    dev: true

  registry.npmmirror.com/isexe@3.1.1:
    resolution: {integrity: sha512-LpB/54B+/2J5hqQ7imZHfdU31OlgQqx7ZicVlkm9kzg9/w8GKLEcFfJl/t7DCEDueOyBAD6zCCwTO6Fzs0NoEQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/isexe/-/isexe-3.1.1.tgz}
    name: isexe
    version: 3.1.1
    engines: {node: '>=16'}
    dev: true

  registry.npmmirror.com/jiti@2.4.2:
    resolution: {integrity: sha512-rg9zJN+G4n2nfJl5MW3BMygZX56zKPNVEYYqq7adpmMh4Jn2QNEwhvQlFy6jPVdcod7txZtKHWnyZiA3a0zP7A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jiti/-/jiti-2.4.2.tgz}
    name: jiti
    version: 2.4.2
    hasBin: true
    dev: true

  registry.npmmirror.com/js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz}
    name: js-tokens
    version: 4.0.0
    dev: true

  registry.npmmirror.com/js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/js-yaml/-/js-yaml-4.1.0.tgz}
    name: js-yaml
    version: 4.1.0
    hasBin: true
    dependencies:
      argparse: registry.npmmirror.com/argparse@2.0.1
    dev: true

  registry.npmmirror.com/jsesc@3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jsesc/-/jsesc-3.1.0.tgz}
    name: jsesc
    version: 3.1.0
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  registry.npmmirror.com/json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/json-buffer/-/json-buffer-3.0.1.tgz}
    name: json-buffer
    version: 3.0.1
    dev: true

  registry.npmmirror.com/json-parse-even-better-errors@4.0.0:
    resolution: {integrity: sha512-lR4MXjGNgkJc7tkQ97kb2nuEMnNCyU//XYVH0MKTGcXEiSudQ5MKGKen3C5QubYy0vmq+JGitUg92uuywGEwIA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/json-parse-even-better-errors/-/json-parse-even-better-errors-4.0.0.tgz}
    name: json-parse-even-better-errors
    version: 4.0.0
    engines: {node: ^18.17.0 || >=20.5.0}
    dev: true

  registry.npmmirror.com/json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz}
    name: json-schema-traverse
    version: 0.4.1
    dev: true

  registry.npmmirror.com/json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz}
    name: json-stable-stringify-without-jsonify
    version: 1.0.1
    dev: true

  registry.npmmirror.com/json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/json5/-/json5-2.2.3.tgz}
    name: json5
    version: 2.2.3
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  registry.npmmirror.com/jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jsonfile/-/jsonfile-6.1.0.tgz}
    name: jsonfile
    version: 6.1.0
    dependencies:
      universalify: registry.npmmirror.com/universalify@2.0.1
    optionalDependencies:
      graceful-fs: registry.npmmirror.com/graceful-fs@4.2.11
    dev: true

  registry.npmmirror.com/keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/keyv/-/keyv-4.5.4.tgz}
    name: keyv
    version: 4.5.4
    dependencies:
      json-buffer: registry.npmmirror.com/json-buffer@3.0.1
    dev: true

  registry.npmmirror.com/kolorist@1.8.0:
    resolution: {integrity: sha512-Y+60/zizpJ3HRH8DCss+q95yr6145JXZo46OTpFvDZWLfRCE4qChOyk1b26nMaNpfHHgxagk9dXT5OP0Tfe+dQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/kolorist/-/kolorist-1.8.0.tgz}
    name: kolorist
    version: 1.8.0
    dev: true

  registry.npmmirror.com/levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/levn/-/levn-0.4.1.tgz}
    name: levn
    version: 0.4.1
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: registry.npmmirror.com/prelude-ls@1.2.1
      type-check: registry.npmmirror.com/type-check@0.4.0
    dev: true

  registry.npmmirror.com/linkify-it@5.0.0:
    resolution: {integrity: sha512-5aHCbzQRADcdP+ATqnDuhhJ/MRIqDkZX5pyjFHRRysS8vZ5AbqGEoFIb6pYHPZ+L/OC2Lc+xT8uHVVR5CAK/wQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/linkify-it/-/linkify-it-5.0.0.tgz}
    name: linkify-it
    version: 5.0.0
    dependencies:
      uc.micro: registry.npmmirror.com/uc.micro@2.1.0
    dev: false

  registry.npmmirror.com/locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/locate-path/-/locate-path-6.0.0.tgz}
    name: locate-path
    version: 6.0.0
    engines: {node: '>=10'}
    dependencies:
      p-locate: registry.npmmirror.com/p-locate@5.0.0
    dev: true

  registry.npmmirror.com/lodash-es@4.17.21:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lodash-es/-/lodash-es-4.17.21.tgz}
    name: lodash-es
    version: 4.17.21
    dev: false

  registry.npmmirror.com/lodash-unified@1.0.3(@types/lodash-es@4.17.12)(lodash-es@4.17.21)(lodash@4.17.21):
    resolution: {integrity: sha512-WK9qSozxXOD7ZJQlpSqOT+om2ZfcT4yO+03FuzAHD0wF6S0l0090LRPDx3vhTTLZ8cFKpBn+IOcVXK6qOcIlfQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lodash-unified/-/lodash-unified-1.0.3.tgz}
    id: registry.npmmirror.com/lodash-unified/1.0.3
    name: lodash-unified
    version: 1.0.3
    peerDependencies:
      '@types/lodash-es': '*'
      lodash: '*'
      lodash-es: '*'
    dependencies:
      '@types/lodash-es': registry.npmmirror.com/@types/lodash-es@4.17.12
      lodash: registry.npmmirror.com/lodash@4.17.21
      lodash-es: registry.npmmirror.com/lodash-es@4.17.21
    dev: false

  registry.npmmirror.com/lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lodash.merge/-/lodash.merge-4.6.2.tgz}
    name: lodash.merge
    version: 4.6.2
    dev: true

  registry.npmmirror.com/lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz}
    name: lodash
    version: 4.17.21

  registry.npmmirror.com/lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lru-cache/-/lru-cache-5.1.1.tgz}
    name: lru-cache
    version: 5.1.1
    dependencies:
      yallist: registry.npmmirror.com/yallist@3.1.1
    dev: true

  registry.npmmirror.com/magic-string@0.30.17:
    resolution: {integrity: sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/magic-string/-/magic-string-0.30.17.tgz}
    name: magic-string
    version: 0.30.17
    dependencies:
      '@jridgewell/sourcemap-codec': registry.npmmirror.com/@jridgewell/sourcemap-codec@1.5.0

  registry.npmmirror.com/markdown-it@14.1.0:
    resolution: {integrity: sha512-a54IwgWPaeBCAAsv13YgmALOF1elABB08FxO9i+r4VFk5Vl4pKokRPeX8u5TCgSsPi6ec1otfLjdOpVcgbpshg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/markdown-it/-/markdown-it-14.1.0.tgz}
    name: markdown-it
    version: 14.1.0
    hasBin: true
    dependencies:
      argparse: registry.npmmirror.com/argparse@2.0.1
      entities: registry.npmmirror.com/entities@4.5.0
      linkify-it: registry.npmmirror.com/linkify-it@5.0.0
      mdurl: registry.npmmirror.com/mdurl@2.0.0
      punycode.js: registry.npmmirror.com/punycode.js@2.3.1
      uc.micro: registry.npmmirror.com/uc.micro@2.1.0
    dev: false

  registry.npmmirror.com/math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/math-intrinsics/-/math-intrinsics-1.1.0.tgz}
    name: math-intrinsics
    version: 1.1.0
    engines: {node: '>= 0.4'}
    dev: false

  registry.npmmirror.com/mdurl@2.0.0:
    resolution: {integrity: sha512-Lf+9+2r+Tdp5wXDXC4PcIBjTDtq4UKjCPMQhKIuzpJNW0b96kVqSwW0bT7FhRSfmAiFYgP+SCRvdrDozfh0U5w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mdurl/-/mdurl-2.0.0.tgz}
    name: mdurl
    version: 2.0.0
    dev: false

  registry.npmmirror.com/memoize-one@6.0.0:
    resolution: {integrity: sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/memoize-one/-/memoize-one-6.0.0.tgz}
    name: memoize-one
    version: 6.0.0
    dev: false

  registry.npmmirror.com/memorystream@0.3.1:
    resolution: {integrity: sha512-S3UwM3yj5mtUSEfP41UZmt/0SCoVYUcU1rkXv+BQ5Ig8ndL4sPoJNBUJERafdPb5jjHJGuMgytgKvKIf58XNBw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/memorystream/-/memorystream-0.3.1.tgz}
    name: memorystream
    version: 0.3.1
    engines: {node: '>= 0.10.0'}
    dev: true

  registry.npmmirror.com/merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz}
    name: merge2
    version: 1.4.1
    engines: {node: '>= 8'}
    dev: true

  registry.npmmirror.com/micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/micromatch/-/micromatch-4.0.8.tgz}
    name: micromatch
    version: 4.0.8
    engines: {node: '>=8.6'}
    dependencies:
      braces: registry.npmmirror.com/braces@3.0.3
      picomatch: registry.npmmirror.com/picomatch@2.3.1
    dev: true

  registry.npmmirror.com/mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz}
    name: mime-db
    version: 1.52.0
    engines: {node: '>= 0.6'}
    dev: false

  registry.npmmirror.com/mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz}
    name: mime-types
    version: 2.1.35
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: registry.npmmirror.com/mime-db@1.52.0
    dev: false

  registry.npmmirror.com/minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz}
    name: minimatch
    version: 3.1.2
    dependencies:
      brace-expansion: registry.npmmirror.com/brace-expansion@1.1.11
    dev: true

  registry.npmmirror.com/minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/minimatch/-/minimatch-9.0.5.tgz}
    name: minimatch
    version: 9.0.5
    engines: {node: '>=16 || 14 >=14.17'}
    dependencies:
      brace-expansion: registry.npmmirror.com/brace-expansion@2.0.1
    dev: true

  registry.npmmirror.com/mitt@3.0.1:
    resolution: {integrity: sha512-vKivATfr97l2/QBCYAkXYDbrIWPM2IIKEl7YPhjCvKlG3kE2gm+uBo6nEXK3M5/Ffh/FLpKExzOQ3JJoJGFKBw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mitt/-/mitt-3.0.1.tgz}
    name: mitt
    version: 3.0.1

  registry.npmmirror.com/mrmime@2.0.1:
    resolution: {integrity: sha512-Y3wQdFg2Va6etvQ5I82yUhGdsKrcYox6p7FfL1LbK2J4V01F9TGlepTIhnK24t7koZibmg82KGglhA1XK5IsLQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mrmime/-/mrmime-2.0.1.tgz}
    name: mrmime
    version: 2.0.1
    engines: {node: '>=10'}
    dev: true

  registry.npmmirror.com/ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz}
    name: ms
    version: 2.1.3
    dev: true

  registry.npmmirror.com/muggle-string@0.4.1:
    resolution: {integrity: sha512-VNTrAak/KhO2i8dqqnqnAHOa3cYBwXEZe9h+D5h/1ZqFSTEFHdM65lR7RoIqq3tBBYavsOXV84NoHXZ0AkPyqQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/muggle-string/-/muggle-string-0.4.1.tgz}
    name: muggle-string
    version: 0.4.1
    dev: true

  registry.npmmirror.com/nanoid@3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/nanoid/-/nanoid-3.3.11.tgz}
    name: nanoid
    version: 3.3.11
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  registry.npmmirror.com/nanoid@5.1.5:
    resolution: {integrity: sha512-Ir/+ZpE9fDsNH0hQ3C68uyThDXzYcim2EqcZ8zn8Chtt1iylPT9xXJB0kPCnqzgcEGikO9RxSrh63MsmVCU7Fw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/nanoid/-/nanoid-5.1.5.tgz}
    name: nanoid
    version: 5.1.5
    engines: {node: ^18 || >=20}
    hasBin: true
    dev: true

  registry.npmmirror.com/natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/natural-compare/-/natural-compare-1.4.0.tgz}
    name: natural-compare
    version: 1.4.0
    dev: true

  registry.npmmirror.com/node-releases@2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/node-releases/-/node-releases-2.0.19.tgz}
    name: node-releases
    version: 2.0.19
    dev: true

  registry.npmmirror.com/normalize-wheel-es@1.2.0:
    resolution: {integrity: sha512-Wj7+EJQ8mSuXr2iWfnujrimU35R2W4FAErEyTmJoJ7ucwTn2hOUSsRehMb5RSYkxXGTM7Y9QpvPmp++w5ftoJw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/normalize-wheel-es/-/normalize-wheel-es-1.2.0.tgz}
    name: normalize-wheel-es
    version: 1.2.0
    dev: false

  registry.npmmirror.com/npm-normalize-package-bin@4.0.0:
    resolution: {integrity: sha512-TZKxPvItzai9kN9H/TkmCtx/ZN/hvr3vUycjlfmH0ootY9yFBzNOpiXAdIn1Iteqsvk4lQn6B5PTrt+n6h8k/w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/npm-normalize-package-bin/-/npm-normalize-package-bin-4.0.0.tgz}
    name: npm-normalize-package-bin
    version: 4.0.0
    engines: {node: ^18.17.0 || >=20.5.0}
    dev: true

  registry.npmmirror.com/npm-run-all2@7.0.2:
    resolution: {integrity: sha512-7tXR+r9hzRNOPNTvXegM+QzCuMjzUIIq66VDunL6j60O4RrExx32XUhlrS7UK4VcdGw5/Wxzb3kfNcFix9JKDA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/npm-run-all2/-/npm-run-all2-7.0.2.tgz}
    name: npm-run-all2
    version: 7.0.2
    engines: {node: ^18.17.0 || >=20.5.0, npm: '>= 9'}
    hasBin: true
    dependencies:
      ansi-styles: registry.npmmirror.com/ansi-styles@6.2.1
      cross-spawn: registry.npmmirror.com/cross-spawn@7.0.6
      memorystream: registry.npmmirror.com/memorystream@0.3.1
      minimatch: registry.npmmirror.com/minimatch@9.0.5
      pidtree: registry.npmmirror.com/pidtree@0.6.0
      read-package-json-fast: registry.npmmirror.com/read-package-json-fast@4.0.0
      shell-quote: registry.npmmirror.com/shell-quote@1.8.2
      which: registry.npmmirror.com/which@5.0.0
    dev: true

  registry.npmmirror.com/npm-run-path@6.0.0:
    resolution: {integrity: sha512-9qny7Z9DsQU8Ou39ERsPU4OZQlSTP47ShQzuKZ6PRXpYLtIFgl/DEBYEXKlvcEa+9tHVcK8CF81Y2V72qaZhWA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/npm-run-path/-/npm-run-path-6.0.0.tgz}
    name: npm-run-path
    version: 6.0.0
    engines: {node: '>=18'}
    dependencies:
      path-key: registry.npmmirror.com/path-key@4.0.0
      unicorn-magic: registry.npmmirror.com/unicorn-magic@0.3.0
    dev: true

  registry.npmmirror.com/nth-check@2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/nth-check/-/nth-check-2.1.1.tgz}
    name: nth-check
    version: 2.1.1
    dependencies:
      boolbase: registry.npmmirror.com/boolbase@1.0.0
    dev: true

  registry.npmmirror.com/open@10.1.2:
    resolution: {integrity: sha512-cxN6aIDPz6rm8hbebcP7vrQNhvRcveZoJU72Y7vskh4oIm+BZwBECnx5nTmrlres1Qapvx27Qo1Auukpf8PKXw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/open/-/open-10.1.2.tgz}
    name: open
    version: 10.1.2
    engines: {node: '>=18'}
    dependencies:
      default-browser: registry.npmmirror.com/default-browser@5.2.1
      define-lazy-prop: registry.npmmirror.com/define-lazy-prop@3.0.0
      is-inside-container: registry.npmmirror.com/is-inside-container@1.0.0
      is-wsl: registry.npmmirror.com/is-wsl@3.1.0
    dev: true

  registry.npmmirror.com/optionator@0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/optionator/-/optionator-0.9.4.tgz}
    name: optionator
    version: 0.9.4
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: registry.npmmirror.com/deep-is@0.1.4
      fast-levenshtein: registry.npmmirror.com/fast-levenshtein@2.0.6
      levn: registry.npmmirror.com/levn@0.4.1
      prelude-ls: registry.npmmirror.com/prelude-ls@1.2.1
      type-check: registry.npmmirror.com/type-check@0.4.0
      word-wrap: registry.npmmirror.com/word-wrap@1.2.5
    dev: true

  registry.npmmirror.com/p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/p-limit/-/p-limit-3.1.0.tgz}
    name: p-limit
    version: 3.1.0
    engines: {node: '>=10'}
    dependencies:
      yocto-queue: registry.npmmirror.com/yocto-queue@0.1.0
    dev: true

  registry.npmmirror.com/p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/p-locate/-/p-locate-5.0.0.tgz}
    name: p-locate
    version: 5.0.0
    engines: {node: '>=10'}
    dependencies:
      p-limit: registry.npmmirror.com/p-limit@3.1.0
    dev: true

  registry.npmmirror.com/parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/parent-module/-/parent-module-1.0.1.tgz}
    name: parent-module
    version: 1.0.1
    engines: {node: '>=6'}
    dependencies:
      callsites: registry.npmmirror.com/callsites@3.1.0
    dev: true

  registry.npmmirror.com/parse-ms@4.0.0:
    resolution: {integrity: sha512-TXfryirbmq34y8QBwgqCVLi+8oA3oWx2eAnSn62ITyEhEYaWRlVZ2DvMM9eZbMs/RfxPu/PK/aBLyGj4IrqMHw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/parse-ms/-/parse-ms-4.0.0.tgz}
    name: parse-ms
    version: 4.0.0
    engines: {node: '>=18'}
    dev: true

  registry.npmmirror.com/path-browserify@1.0.1:
    resolution: {integrity: sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-browserify/-/path-browserify-1.0.1.tgz}
    name: path-browserify
    version: 1.0.1
    dev: true

  registry.npmmirror.com/path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz}
    name: path-exists
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz}
    name: path-key
    version: 3.1.1
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/path-key@4.0.0:
    resolution: {integrity: sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-key/-/path-key-4.0.0.tgz}
    name: path-key
    version: 4.0.0
    engines: {node: '>=12'}
    dev: true

  registry.npmmirror.com/pathe@2.0.3:
    resolution: {integrity: sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/pathe/-/pathe-2.0.3.tgz}
    name: pathe
    version: 2.0.3
    dev: true

  registry.npmmirror.com/perfect-debounce@1.0.0:
    resolution: {integrity: sha512-xCy9V055GLEqoFaHoC1SoLIaLmWctgCUaBaWxDZ7/Zx4CTyX7cJQLJOok/orfjZAh9kEYpjJa4d0KcJmCbctZA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/perfect-debounce/-/perfect-debounce-1.0.0.tgz}
    name: perfect-debounce
    version: 1.0.0

  registry.npmmirror.com/picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/picocolors/-/picocolors-1.1.1.tgz}
    name: picocolors
    version: 1.1.1

  registry.npmmirror.com/picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz}
    name: picomatch
    version: 2.3.1
    engines: {node: '>=8.6'}
    dev: true

  registry.npmmirror.com/picomatch@4.0.2:
    resolution: {integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/picomatch/-/picomatch-4.0.2.tgz}
    name: picomatch
    version: 4.0.2
    engines: {node: '>=12'}
    dev: true

  registry.npmmirror.com/pidtree@0.6.0:
    resolution: {integrity: sha512-eG2dWTVw5bzqGRztnHExczNxt5VGsE6OwTeCG3fdUf9KBsZzO3R5OIIIzWR+iZA0NtZ+RDVdaoE2dK1cn6jH4g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/pidtree/-/pidtree-0.6.0.tgz}
    name: pidtree
    version: 0.6.0
    engines: {node: '>=0.10'}
    hasBin: true
    dev: true

  registry.npmmirror.com/pinia@3.0.2(typescript@5.8.3)(vue@3.5.16):
    resolution: {integrity: sha512-sH2JK3wNY809JOeiiURUR0wehJ9/gd9qFN2Y828jCbxEzKEmEt0pzCXwqiSTfuRsK9vQsOflSdnbdBOGrhtn+g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/pinia/-/pinia-3.0.2.tgz}
    id: registry.npmmirror.com/pinia/3.0.2
    name: pinia
    version: 3.0.2
    peerDependencies:
      typescript: '>=4.4.4'
      vue: ^2.7.0 || ^3.5.11
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@vue/devtools-api': registry.npmmirror.com/@vue/devtools-api@7.7.6
      typescript: registry.npmmirror.com/typescript@5.8.3
      vue: registry.npmmirror.com/vue@3.5.16(typescript@5.8.3)
    dev: false

  registry.npmmirror.com/postcss-selector-parser@6.1.2:
    resolution: {integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz}
    name: postcss-selector-parser
    version: 6.1.2
    engines: {node: '>=4'}
    dependencies:
      cssesc: registry.npmmirror.com/cssesc@3.0.0
      util-deprecate: registry.npmmirror.com/util-deprecate@1.0.2
    dev: true

  registry.npmmirror.com/postcss@8.5.4:
    resolution: {integrity: sha512-QSa9EBe+uwlGTFmHsPKokv3B/oEMQZxfqW0QqNCyhpa6mB1afzulwn8hihglqAb2pOw+BJgNlmXQ8la2VeHB7w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/postcss/-/postcss-8.5.4.tgz}
    name: postcss
    version: 8.5.4
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: registry.npmmirror.com/nanoid@3.3.11
      picocolors: registry.npmmirror.com/picocolors@1.1.1
      source-map-js: registry.npmmirror.com/source-map-js@1.2.1

  registry.npmmirror.com/prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.2.1.tgz}
    name: prelude-ls
    version: 1.2.1
    engines: {node: '>= 0.8.0'}
    dev: true

  registry.npmmirror.com/prettier-linter-helpers@1.0.0:
    resolution: {integrity: sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz}
    name: prettier-linter-helpers
    version: 1.0.0
    engines: {node: '>=6.0.0'}
    dependencies:
      fast-diff: registry.npmmirror.com/fast-diff@1.3.0
    dev: true

  registry.npmmirror.com/prettier@3.5.3:
    resolution: {integrity: sha512-QQtaxnoDJeAkDvDKWCLiwIXkTgRhwYDEQCghU9Z6q03iyek/rxRh/2lC3HB7P8sWT2xC/y5JDctPLBIGzHKbhw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/prettier/-/prettier-3.5.3.tgz}
    name: prettier
    version: 3.5.3
    engines: {node: '>=14'}
    hasBin: true
    dev: true

  registry.npmmirror.com/pretty-ms@9.2.0:
    resolution: {integrity: sha512-4yf0QO/sllf/1zbZWYnvWw3NxCQwLXKzIj0G849LSufP15BXKM0rbD2Z3wVnkMfjdn/CB0Dpp444gYAACdsplg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/pretty-ms/-/pretty-ms-9.2.0.tgz}
    name: pretty-ms
    version: 9.2.0
    engines: {node: '>=18'}
    dependencies:
      parse-ms: registry.npmmirror.com/parse-ms@4.0.0
    dev: true

  registry.npmmirror.com/proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz}
    name: proxy-from-env
    version: 1.1.0
    dev: false

  registry.npmmirror.com/punycode.js@2.3.1:
    resolution: {integrity: sha512-uxFIHU0YlHYhDQtV4R9J6a52SLx28BCjT+4ieh7IGbgwVJWO+km431c4yRlREUAsAmt/uMjQUyQHNEPf0M39CA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/punycode.js/-/punycode.js-2.3.1.tgz}
    name: punycode.js
    version: 2.3.1
    engines: {node: '>=6'}
    dev: false

  registry.npmmirror.com/punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/punycode/-/punycode-2.3.1.tgz}
    name: punycode
    version: 2.3.1
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz}
    name: queue-microtask
    version: 1.2.3
    dev: true

  registry.npmmirror.com/read-package-json-fast@4.0.0:
    resolution: {integrity: sha512-qpt8EwugBWDw2cgE2W+/3oxC+KTez2uSVR8JU9Q36TXPAGCaozfQUs59v4j4GFpWTaw0i6hAZSvOmu1J0uOEUg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/read-package-json-fast/-/read-package-json-fast-4.0.0.tgz}
    name: read-package-json-fast
    version: 4.0.0
    engines: {node: ^18.17.0 || >=20.5.0}
    dependencies:
      json-parse-even-better-errors: registry.npmmirror.com/json-parse-even-better-errors@4.0.0
      npm-normalize-package-bin: registry.npmmirror.com/npm-normalize-package-bin@4.0.0
    dev: true

  registry.npmmirror.com/resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/resolve-from/-/resolve-from-4.0.0.tgz}
    name: resolve-from
    version: 4.0.0
    engines: {node: '>=4'}
    dev: true

  registry.npmmirror.com/reusify@1.1.0:
    resolution: {integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/reusify/-/reusify-1.1.0.tgz}
    name: reusify
    version: 1.1.0
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/rfdc@1.4.1:
    resolution: {integrity: sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/rfdc/-/rfdc-1.4.1.tgz}
    name: rfdc
    version: 1.4.1

  registry.npmmirror.com/rollup@4.41.1:
    resolution: {integrity: sha512-cPmwD3FnFv8rKMBc1MxWCwVQFxwf1JEmSX3iQXrRVVG15zerAIXRjMFVWnd5Q5QvgKF7Aj+5ykXFhUl+QGnyOw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/rollup/-/rollup-4.41.1.tgz}
    name: rollup
    version: 4.41.1
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true
    dependencies:
      '@types/estree': registry.npmmirror.com/@types/estree@1.0.7
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': registry.npmmirror.com/@rollup/rollup-android-arm-eabi@4.41.1
      '@rollup/rollup-android-arm64': registry.npmmirror.com/@rollup/rollup-android-arm64@4.41.1
      '@rollup/rollup-darwin-arm64': registry.npmmirror.com/@rollup/rollup-darwin-arm64@4.41.1
      '@rollup/rollup-darwin-x64': registry.npmmirror.com/@rollup/rollup-darwin-x64@4.41.1
      '@rollup/rollup-freebsd-arm64': registry.npmmirror.com/@rollup/rollup-freebsd-arm64@4.41.1
      '@rollup/rollup-freebsd-x64': registry.npmmirror.com/@rollup/rollup-freebsd-x64@4.41.1
      '@rollup/rollup-linux-arm-gnueabihf': registry.npmmirror.com/@rollup/rollup-linux-arm-gnueabihf@4.41.1
      '@rollup/rollup-linux-arm-musleabihf': registry.npmmirror.com/@rollup/rollup-linux-arm-musleabihf@4.41.1
      '@rollup/rollup-linux-arm64-gnu': registry.npmmirror.com/@rollup/rollup-linux-arm64-gnu@4.41.1
      '@rollup/rollup-linux-arm64-musl': registry.npmmirror.com/@rollup/rollup-linux-arm64-musl@4.41.1
      '@rollup/rollup-linux-loongarch64-gnu': registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu@4.41.1
      '@rollup/rollup-linux-powerpc64le-gnu': registry.npmmirror.com/@rollup/rollup-linux-powerpc64le-gnu@4.41.1
      '@rollup/rollup-linux-riscv64-gnu': registry.npmmirror.com/@rollup/rollup-linux-riscv64-gnu@4.41.1
      '@rollup/rollup-linux-riscv64-musl': registry.npmmirror.com/@rollup/rollup-linux-riscv64-musl@4.41.1
      '@rollup/rollup-linux-s390x-gnu': registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu@4.41.1
      '@rollup/rollup-linux-x64-gnu': registry.npmmirror.com/@rollup/rollup-linux-x64-gnu@4.41.1
      '@rollup/rollup-linux-x64-musl': registry.npmmirror.com/@rollup/rollup-linux-x64-musl@4.41.1
      '@rollup/rollup-win32-arm64-msvc': registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc@4.41.1
      '@rollup/rollup-win32-ia32-msvc': registry.npmmirror.com/@rollup/rollup-win32-ia32-msvc@4.41.1
      '@rollup/rollup-win32-x64-msvc': registry.npmmirror.com/@rollup/rollup-win32-x64-msvc@4.41.1
      fsevents: registry.npmmirror.com/fsevents@2.3.3
    dev: true

  registry.npmmirror.com/run-applescript@7.0.0:
    resolution: {integrity: sha512-9by4Ij99JUr/MCFBUkDKLWK3G9HVXmabKz9U5MlIAIuvuzkiOicRYs8XJLxX+xahD+mLiiCYDqF9dKAgtzKP1A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/run-applescript/-/run-applescript-7.0.0.tgz}
    name: run-applescript
    version: 7.0.0
    engines: {node: '>=18'}
    dev: true

  registry.npmmirror.com/run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz}
    name: run-parallel
    version: 1.2.0
    dependencies:
      queue-microtask: registry.npmmirror.com/queue-microtask@1.2.3
    dev: true

  registry.npmmirror.com/semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/semver/-/semver-6.3.1.tgz}
    name: semver
    version: 6.3.1
    hasBin: true
    dev: true

  registry.npmmirror.com/semver@7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/semver/-/semver-7.7.2.tgz}
    name: semver
    version: 7.7.2
    engines: {node: '>=10'}
    hasBin: true
    dev: true

  registry.npmmirror.com/shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz}
    name: shebang-command
    version: 2.0.0
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: registry.npmmirror.com/shebang-regex@3.0.0
    dev: true

  registry.npmmirror.com/shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz}
    name: shebang-regex
    version: 3.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/shell-quote@1.8.2:
    resolution: {integrity: sha512-AzqKpGKjrj7EM6rKVQEPpB288oCfnrEIuyoT9cyF4nmGa7V8Zk6f7RRqYisX8X9m+Q7bd632aZW4ky7EhbQztA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/shell-quote/-/shell-quote-1.8.2.tgz}
    name: shell-quote
    version: 1.8.2
    engines: {node: '>= 0.4'}
    dev: true

  registry.npmmirror.com/signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/signal-exit/-/signal-exit-4.1.0.tgz}
    name: signal-exit
    version: 4.1.0
    engines: {node: '>=14'}
    dev: true

  registry.npmmirror.com/sirv@3.0.1:
    resolution: {integrity: sha512-FoqMu0NCGBLCcAkS1qA+XJIQTR6/JHfQXl+uGteNCQ76T91DMUjPa9xfmeqMY3z80nLSg9yQmNjK0Px6RWsH/A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/sirv/-/sirv-3.0.1.tgz}
    name: sirv
    version: 3.0.1
    engines: {node: '>=18'}
    dependencies:
      '@polka/url': registry.npmmirror.com/@polka/url@1.0.0-next.29
      mrmime: registry.npmmirror.com/mrmime@2.0.1
      totalist: registry.npmmirror.com/totalist@3.0.1
    dev: true

  registry.npmmirror.com/source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/source-map-js/-/source-map-js-1.2.1.tgz}
    name: source-map-js
    version: 1.2.1
    engines: {node: '>=0.10.0'}

  registry.npmmirror.com/speakingurl@14.0.1:
    resolution: {integrity: sha512-1POYv7uv2gXoyGFpBCmpDVSNV74IfsWlDW216UPjbWufNf+bSU6GdbDsxdcxtfwb4xlI3yxzOTKClUosxARYrQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/speakingurl/-/speakingurl-14.0.1.tgz}
    name: speakingurl
    version: 14.0.1
    engines: {node: '>=0.10.0'}

  registry.npmmirror.com/strip-final-newline@4.0.0:
    resolution: {integrity: sha512-aulFJcD6YK8V1G7iRB5tigAP4TsHBZZrOV8pjV++zdUwmeV8uzbY7yn6h9MswN62adStNZFuCIx4haBnRuMDaw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/strip-final-newline/-/strip-final-newline-4.0.0.tgz}
    name: strip-final-newline
    version: 4.0.0
    engines: {node: '>=18'}
    dev: true

  registry.npmmirror.com/strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz}
    name: strip-json-comments
    version: 3.1.1
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/superjson@2.2.2:
    resolution: {integrity: sha512-5JRxVqC8I8NuOUjzBbvVJAKNM8qoVuH0O77h4WInc/qC2q5IreqKxYwgkga3PfA22OayK2ikceb/B26dztPl+Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/superjson/-/superjson-2.2.2.tgz}
    name: superjson
    version: 2.2.2
    engines: {node: '>=16'}
    dependencies:
      copy-anything: registry.npmmirror.com/copy-anything@3.0.5

  registry.npmmirror.com/supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz}
    name: supports-color
    version: 7.2.0
    engines: {node: '>=8'}
    dependencies:
      has-flag: registry.npmmirror.com/has-flag@4.0.0
    dev: true

  registry.npmmirror.com/synckit@0.11.8:
    resolution: {integrity: sha512-+XZ+r1XGIJGeQk3VvXhT6xx/VpbHsRzsTkGgF6E5RX9TTXD0118l87puaEBZ566FhqblC6U0d4XnubznJDm30A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/synckit/-/synckit-0.11.8.tgz}
    name: synckit
    version: 0.11.8
    engines: {node: ^14.18.0 || >=16.0.0}
    dependencies:
      '@pkgr/core': registry.npmmirror.com/@pkgr/core@0.2.4
    dev: true

  registry.npmmirror.com/tinyglobby@0.2.14:
    resolution: {integrity: sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/tinyglobby/-/tinyglobby-0.2.14.tgz}
    name: tinyglobby
    version: 0.2.14
    engines: {node: '>=12.0.0'}
    dependencies:
      fdir: registry.npmmirror.com/fdir@6.4.5(picomatch@4.0.2)
      picomatch: registry.npmmirror.com/picomatch@4.0.2
    dev: true

  registry.npmmirror.com/to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz}
    name: to-regex-range
    version: 5.0.1
    engines: {node: '>=8.0'}
    dependencies:
      is-number: registry.npmmirror.com/is-number@7.0.0
    dev: true

  registry.npmmirror.com/totalist@3.0.1:
    resolution: {integrity: sha512-sf4i37nQ2LBx4m3wB74y+ubopq6W/dIzXg0FDGjsYnZHVa1Da8FH853wlL2gtUhg+xJXjfk3kUZS3BRoQeoQBQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/totalist/-/totalist-3.0.1.tgz}
    name: totalist
    version: 3.0.1
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/ts-api-utils@2.1.0(typescript@5.8.3):
    resolution: {integrity: sha512-CUgTZL1irw8u29bzrOD/nH85jqyc74D6SshFgujOIA7osm2Rz7dYH77agkx7H4FBNxDq7Cjf+IjaX/8zwFW+ZQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ts-api-utils/-/ts-api-utils-2.1.0.tgz}
    id: registry.npmmirror.com/ts-api-utils/2.1.0
    name: ts-api-utils
    version: 2.1.0
    engines: {node: '>=18.12'}
    peerDependencies:
      typescript: '>=4.8.4'
    dependencies:
      typescript: registry.npmmirror.com/typescript@5.8.3
    dev: true

  registry.npmmirror.com/tslib@2.3.0:
    resolution: {integrity: sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/tslib/-/tslib-2.3.0.tgz}
    name: tslib
    version: 2.3.0
    dev: false

  registry.npmmirror.com/tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/tslib/-/tslib-2.8.1.tgz}
    name: tslib
    version: 2.8.1
    dev: false

  registry.npmmirror.com/type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz}
    name: type-check
    version: 0.4.0
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: registry.npmmirror.com/prelude-ls@1.2.1
    dev: true

  registry.npmmirror.com/typescript-eslint@8.33.0(eslint@9.28.0)(typescript@5.8.3):
    resolution: {integrity: sha512-5YmNhF24ylCsvdNW2oJwMzTbaeO4bg90KeGtMjUw0AGtHksgEPLRTUil+coHwCfiu4QjVJFnjp94DmU6zV7DhQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/typescript-eslint/-/typescript-eslint-8.33.0.tgz}
    id: registry.npmmirror.com/typescript-eslint/8.33.0
    name: typescript-eslint
    version: 8.33.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'
    dependencies:
      '@typescript-eslint/eslint-plugin': registry.npmmirror.com/@typescript-eslint/eslint-plugin@8.33.0(@typescript-eslint/parser@8.33.0)(eslint@9.28.0)(typescript@5.8.3)
      '@typescript-eslint/parser': registry.npmmirror.com/@typescript-eslint/parser@8.33.0(eslint@9.28.0)(typescript@5.8.3)
      '@typescript-eslint/utils': registry.npmmirror.com/@typescript-eslint/utils@8.33.0(eslint@9.28.0)(typescript@5.8.3)
      eslint: registry.npmmirror.com/eslint@9.28.0(jiti@2.4.2)
      typescript: registry.npmmirror.com/typescript@5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/typescript@5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/typescript/-/typescript-5.8.3.tgz}
    name: typescript
    version: 5.8.3
    engines: {node: '>=14.17'}
    hasBin: true

  registry.npmmirror.com/uc.micro@2.1.0:
    resolution: {integrity: sha512-ARDJmphmdvUk6Glw7y9DQ2bFkKBHwQHLi2lsaH6PPmz/Ka9sFOBsBluozhDltWmnv9u/cF6Rt87znRTPV+yp/A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/uc.micro/-/uc.micro-2.1.0.tgz}
    name: uc.micro
    version: 2.1.0
    dev: false

  registry.npmmirror.com/undici-types@6.21.0:
    resolution: {integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/undici-types/-/undici-types-6.21.0.tgz}
    name: undici-types
    version: 6.21.0
    dev: true

  registry.npmmirror.com/unicorn-magic@0.3.0:
    resolution: {integrity: sha512-+QBBXBCvifc56fsbuxZQ6Sic3wqqc3WWaqxs58gvJrcOuN83HGTCwz3oS5phzU9LthRNE9VrJCFCLUgHeeFnfA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/unicorn-magic/-/unicorn-magic-0.3.0.tgz}
    name: unicorn-magic
    version: 0.3.0
    engines: {node: '>=18'}
    dev: true

  registry.npmmirror.com/universalify@2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/universalify/-/universalify-2.0.1.tgz}
    name: universalify
    version: 2.0.1
    engines: {node: '>= 10.0.0'}
    dev: true

  registry.npmmirror.com/update-browserslist-db@1.1.3(browserslist@4.25.0):
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz}
    id: registry.npmmirror.com/update-browserslist-db/1.1.3
    name: update-browserslist-db
    version: 1.1.3
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: registry.npmmirror.com/browserslist@4.25.0
      escalade: registry.npmmirror.com/escalade@3.2.0
      picocolors: registry.npmmirror.com/picocolors@1.1.1
    dev: true

  registry.npmmirror.com/uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz}
    name: uri-js
    version: 4.4.1
    dependencies:
      punycode: registry.npmmirror.com/punycode@2.3.1
    dev: true

  registry.npmmirror.com/util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz}
    name: util-deprecate
    version: 1.0.2
    dev: true

  registry.npmmirror.com/vite-hot-client@2.0.4(vite@6.3.5):
    resolution: {integrity: sha512-W9LOGAyGMrbGArYJN4LBCdOC5+Zwh7dHvOHC0KmGKkJhsOzaKbpo/jEjpPKVHIW0/jBWj8RZG0NUxfgA8BxgAg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vite-hot-client/-/vite-hot-client-2.0.4.tgz}
    id: registry.npmmirror.com/vite-hot-client/2.0.4
    name: vite-hot-client
    version: 2.0.4
    peerDependencies:
      vite: ^2.6.0 || ^3.0.0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0
    dependencies:
      vite: registry.npmmirror.com/vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)
    dev: true

  registry.npmmirror.com/vite-plugin-inspect@0.8.9(vite@6.3.5):
    resolution: {integrity: sha512-22/8qn+LYonzibb1VeFZmISdVao5kC22jmEKm24vfFE8siEn47EpVcCLYMv6iKOYMJfjSvSJfueOwcFCkUnV3A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vite-plugin-inspect/-/vite-plugin-inspect-0.8.9.tgz}
    id: registry.npmmirror.com/vite-plugin-inspect/0.8.9
    name: vite-plugin-inspect
    version: 0.8.9
    engines: {node: '>=14'}
    peerDependencies:
      '@nuxt/kit': '*'
      vite: ^3.1.0 || ^4.0.0 || ^5.0.0-0 || ^6.0.1
    peerDependenciesMeta:
      '@nuxt/kit':
        optional: true
    dependencies:
      '@antfu/utils': registry.npmmirror.com/@antfu/utils@0.7.10
      '@rollup/pluginutils': registry.npmmirror.com/@rollup/pluginutils@5.1.4
      debug: registry.npmmirror.com/debug@4.4.1
      error-stack-parser-es: registry.npmmirror.com/error-stack-parser-es@0.1.5
      fs-extra: registry.npmmirror.com/fs-extra@11.3.0
      open: registry.npmmirror.com/open@10.1.2
      perfect-debounce: registry.npmmirror.com/perfect-debounce@1.0.0
      picocolors: registry.npmmirror.com/picocolors@1.1.1
      sirv: registry.npmmirror.com/sirv@3.0.1
      vite: registry.npmmirror.com/vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)
    transitivePeerDependencies:
      - rollup
      - supports-color
    dev: true

  registry.npmmirror.com/vite-plugin-vue-devtools@7.7.6(vite@6.3.5)(vue@3.5.16):
    resolution: {integrity: sha512-L7nPVM5a7lgit/Z+36iwoqHOaP3wxqVi1UvaDJwGCfblS9Y6vNqf32ILlzJVH9c47aHu90BhDXeZc+rgzHRHcw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vite-plugin-vue-devtools/-/vite-plugin-vue-devtools-7.7.6.tgz}
    id: registry.npmmirror.com/vite-plugin-vue-devtools/7.7.6
    name: vite-plugin-vue-devtools
    version: 7.7.6
    engines: {node: '>=v14.21.3'}
    peerDependencies:
      vite: ^3.1.0 || ^4.0.0-0 || ^5.0.0-0 || ^6.0.0-0
    dependencies:
      '@vue/devtools-core': registry.npmmirror.com/@vue/devtools-core@7.7.6(vite@6.3.5)(vue@3.5.16)
      '@vue/devtools-kit': registry.npmmirror.com/@vue/devtools-kit@7.7.6
      '@vue/devtools-shared': registry.npmmirror.com/@vue/devtools-shared@7.7.6
      execa: registry.npmmirror.com/execa@9.6.0
      sirv: registry.npmmirror.com/sirv@3.0.1
      vite: registry.npmmirror.com/vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)
      vite-plugin-inspect: registry.npmmirror.com/vite-plugin-inspect@0.8.9(vite@6.3.5)
      vite-plugin-vue-inspector: registry.npmmirror.com/vite-plugin-vue-inspector@5.3.1(vite@6.3.5)
    transitivePeerDependencies:
      - '@nuxt/kit'
      - rollup
      - supports-color
      - vue
    dev: true

  registry.npmmirror.com/vite-plugin-vue-inspector@5.3.1(vite@6.3.5):
    resolution: {integrity: sha512-cBk172kZKTdvGpJuzCCLg8lJ909wopwsu3Ve9FsL1XsnLBiRT9U3MePcqrgGHgCX2ZgkqZmAGR8taxw+TV6s7A==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vite-plugin-vue-inspector/-/vite-plugin-vue-inspector-5.3.1.tgz}
    id: registry.npmmirror.com/vite-plugin-vue-inspector/5.3.1
    name: vite-plugin-vue-inspector
    version: 5.3.1
    peerDependencies:
      vite: ^3.0.0-0 || ^4.0.0-0 || ^5.0.0-0 || ^6.0.0-0
    dependencies:
      '@babel/core': registry.npmmirror.com/@babel/core@7.27.4
      '@babel/plugin-proposal-decorators': registry.npmmirror.com/@babel/plugin-proposal-decorators@7.27.1(@babel/core@7.27.4)
      '@babel/plugin-syntax-import-attributes': registry.npmmirror.com/@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.4)
      '@babel/plugin-syntax-import-meta': registry.npmmirror.com/@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.27.4)
      '@babel/plugin-transform-typescript': registry.npmmirror.com/@babel/plugin-transform-typescript@7.27.1(@babel/core@7.27.4)
      '@vue/babel-plugin-jsx': registry.npmmirror.com/@vue/babel-plugin-jsx@1.4.0(@babel/core@7.27.4)
      '@vue/compiler-dom': registry.npmmirror.com/@vue/compiler-dom@3.5.16
      kolorist: registry.npmmirror.com/kolorist@1.8.0
      magic-string: registry.npmmirror.com/magic-string@0.30.17
      vite: registry.npmmirror.com/vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2):
    resolution: {integrity: sha512-cZn6NDFE7wdTpINgs++ZJ4N49W2vRp8LCKrn3Ob1kYNtOo21vfDoaV5GzBfLU4MovSAB8uNRm4jgzVQZ+mBzPQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vite/-/vite-6.3.5.tgz}
    id: registry.npmmirror.com/vite/6.3.5
    name: vite
    version: 6.3.5
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || ^20.0.0 || >=22.0.0
      jiti: '>=1.21.0'
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true
    dependencies:
      '@types/node': registry.npmmirror.com/@types/node@22.15.29
      esbuild: registry.npmmirror.com/esbuild@0.25.5
      fdir: registry.npmmirror.com/fdir@6.4.5(picomatch@4.0.2)
      jiti: registry.npmmirror.com/jiti@2.4.2
      picomatch: registry.npmmirror.com/picomatch@4.0.2
      postcss: registry.npmmirror.com/postcss@8.5.4
      rollup: registry.npmmirror.com/rollup@4.41.1
      tinyglobby: registry.npmmirror.com/tinyglobby@0.2.14
    optionalDependencies:
      fsevents: registry.npmmirror.com/fsevents@2.3.3
    dev: true

  registry.npmmirror.com/vscode-uri@3.1.0:
    resolution: {integrity: sha512-/BpdSx+yCQGnCvecbyXdxHDkuk55/G3xwnC0GqY4gmQ3j+A+g8kzzgB4Nk/SINjqn6+waqw3EgbVF2QKExkRxQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vscode-uri/-/vscode-uri-3.1.0.tgz}
    name: vscode-uri
    version: 3.1.0
    dev: true

  registry.npmmirror.com/vue-demi@0.14.10(vue@3.5.16):
    resolution: {integrity: sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.10.tgz}
    id: registry.npmmirror.com/vue-demi/0.14.10
    name: vue-demi
    version: 0.14.10
    engines: {node: '>=12'}
    hasBin: true
    requiresBuild: true
    peerDependencies:
      '@vue/composition-api': ^1.0.0-rc.1
      vue: ^3.0.0-0 || ^2.6.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true
    dependencies:
      vue: registry.npmmirror.com/vue@3.5.16(typescript@5.8.3)
    dev: false

  registry.npmmirror.com/vue-eslint-parser@10.1.3(eslint@9.28.0):
    resolution: {integrity: sha512-dbCBnd2e02dYWsXoqX5yKUZlOt+ExIpq7hmHKPb5ZqKcjf++Eo0hMseFTZMLKThrUk61m+Uv6A2YSBve6ZvuDQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-10.1.3.tgz}
    id: registry.npmmirror.com/vue-eslint-parser/10.1.3
    name: vue-eslint-parser
    version: 10.1.3
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
    dependencies:
      debug: registry.npmmirror.com/debug@4.4.1
      eslint: registry.npmmirror.com/eslint@9.28.0(jiti@2.4.2)
      eslint-scope: registry.npmmirror.com/eslint-scope@8.3.0
      eslint-visitor-keys: registry.npmmirror.com/eslint-visitor-keys@4.2.0
      espree: registry.npmmirror.com/espree@10.3.0
      esquery: registry.npmmirror.com/esquery@1.6.0
      lodash: registry.npmmirror.com/lodash@4.17.21
      semver: registry.npmmirror.com/semver@7.7.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/vue-router@4.5.1(vue@3.5.16):
    resolution: {integrity: sha512-ogAF3P97NPm8fJsE4by9dwSYtDwXIY1nFY9T6DyQnGHd1E2Da94w9JIolpe42LJGIl0DwOHBi8TcRPlPGwbTtw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vue-router/-/vue-router-4.5.1.tgz}
    id: registry.npmmirror.com/vue-router/4.5.1
    name: vue-router
    version: 4.5.1
    peerDependencies:
      vue: ^3.2.0
    dependencies:
      '@vue/devtools-api': registry.npmmirror.com/@vue/devtools-api@6.6.4
      vue: registry.npmmirror.com/vue@3.5.16(typescript@5.8.3)
    dev: false

  registry.npmmirror.com/vue-tsc@2.2.10(typescript@5.8.3):
    resolution: {integrity: sha512-jWZ1xSaNbabEV3whpIDMbjVSVawjAyW+x1n3JeGQo7S0uv2n9F/JMgWW90tGWNFRKya4YwKMZgCtr0vRAM7DeQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vue-tsc/-/vue-tsc-2.2.10.tgz}
    id: registry.npmmirror.com/vue-tsc/2.2.10
    name: vue-tsc
    version: 2.2.10
    hasBin: true
    peerDependencies:
      typescript: '>=5.0.0'
    dependencies:
      '@volar/typescript': registry.npmmirror.com/@volar/typescript@2.4.14
      '@vue/language-core': registry.npmmirror.com/@vue/language-core@2.2.10(typescript@5.8.3)
      typescript: registry.npmmirror.com/typescript@5.8.3
    dev: true

  registry.npmmirror.com/vue3-slide-verify@1.1.7(typescript@5.8.3):
    resolution: {integrity: sha512-EP1Ddr5N8wiF5HIEeF6+pKyu3jqWdBkhM0tB1gr/qU6J40IgklcqBBg9YAVaHv7uV4OoRmE5hlhrobPZArvzkw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vue3-slide-verify/-/vue3-slide-verify-1.1.7.tgz}
    id: registry.npmmirror.com/vue3-slide-verify/1.1.7
    name: vue3-slide-verify
    version: 1.1.7
    dependencies:
      vue: registry.npmmirror.com/vue@3.5.16(typescript@5.8.3)
    transitivePeerDependencies:
      - typescript
    dev: false

  registry.npmmirror.com/vue@3.5.16(typescript@5.8.3):
    resolution: {integrity: sha512-rjOV2ecxMd5SiAmof2xzh2WxntRcigkX/He4YFJ6WdRvVUrbt6DxC1Iujh10XLl8xCDRDtGKMeO3D+pRQ1PP9w==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vue/-/vue-3.5.16.tgz}
    id: registry.npmmirror.com/vue/3.5.16
    name: vue
    version: 3.5.16
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@vue/compiler-dom': registry.npmmirror.com/@vue/compiler-dom@3.5.16
      '@vue/compiler-sfc': registry.npmmirror.com/@vue/compiler-sfc@3.5.16
      '@vue/runtime-dom': registry.npmmirror.com/@vue/runtime-dom@3.5.16
      '@vue/server-renderer': registry.npmmirror.com/@vue/server-renderer@3.5.16(vue@3.5.16)
      '@vue/shared': registry.npmmirror.com/@vue/shared@3.5.16
      typescript: registry.npmmirror.com/typescript@5.8.3

  registry.npmmirror.com/which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/which/-/which-2.0.2.tgz}
    name: which
    version: 2.0.2
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: registry.npmmirror.com/isexe@2.0.0
    dev: true

  registry.npmmirror.com/which@5.0.0:
    resolution: {integrity: sha512-JEdGzHwwkrbWoGOlIHqQ5gtprKGOenpDHpxE9zVR1bWbOtYRyPPHMe9FaP6x61CmNaTThSkb0DAJte5jD+DmzQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/which/-/which-5.0.0.tgz}
    name: which
    version: 5.0.0
    engines: {node: ^18.17.0 || >=20.5.0}
    hasBin: true
    dependencies:
      isexe: registry.npmmirror.com/isexe@3.1.1
    dev: true

  registry.npmmirror.com/word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/word-wrap/-/word-wrap-1.2.5.tgz}
    name: word-wrap
    version: 1.2.5
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/xml-name-validator@4.0.0:
    resolution: {integrity: sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/xml-name-validator/-/xml-name-validator-4.0.0.tgz}
    name: xml-name-validator
    version: 4.0.0
    engines: {node: '>=12'}
    dev: true

  registry.npmmirror.com/yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/yallist/-/yallist-3.1.1.tgz}
    name: yallist
    version: 3.1.1
    dev: true

  registry.npmmirror.com/yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/yocto-queue/-/yocto-queue-0.1.0.tgz}
    name: yocto-queue
    version: 0.1.0
    engines: {node: '>=10'}
    dev: true

  registry.npmmirror.com/yoctocolors@2.1.1:
    resolution: {integrity: sha512-GQHQqAopRhwU8Kt1DDM8NjibDXHC8eoh1erhGAJPEyveY9qqVeXvVikNKrDz69sHowPMorbPUrH/mx8c50eiBQ==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/yoctocolors/-/yoctocolors-2.1.1.tgz}
    name: yoctocolors
    version: 2.1.1
    engines: {node: '>=18'}
    dev: true

  registry.npmmirror.com/zrender@5.6.1:
    resolution: {integrity: sha512-OFXkDJKcrlx5su2XbzJvj/34Q3m6PvyCZkVPHGYpcCJ52ek4U/ymZyfuV1nKE23AyBJ51E/6Yr0mhZ7xGTO4ag==, registry: http://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/zrender/-/zrender-5.6.1.tgz}
    name: zrender
    version: 5.6.1
    dependencies:
      tslib: registry.npmmirror.com/tslib@2.3.0
    dev: false
