import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsBoolean,
  <PERSON><PERSON><PERSON>th,
  <PERSON><PERSON><PERSON>th,
  Min,
  IsInt,
  Max,
  IsIn,
  IsDateString,
  IsObject,
  IsArray,
} from 'class-validator';

/**
 * 告警类型枚举
 */
export enum AlertType {
  QUOTA_EXCEEDED = 'quota_exceeded',
  LOW_BALANCE = 'low_balance',
  RATE_LIMIT = 'rate_limit',
  ERROR_RATE = 'error_rate',
  SERVICE_ERROR = 'service_error',
  HIGH_FREQUENCY = 'high_frequency',
  RESPONSE_TIME = 'response_time',
  SYSTEM_ERROR = 'system_error',
  SECURITY = 'security',
  PAYMENT = 'payment',
  OTHER = 'other',
}

/**
 * 告警级别枚举
 */
export enum AlertLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * 告警状态枚举
 */
export enum AlertStatus {
  PENDING = 'pending',
  ACKNOWLEDGED = 'acknowledged',
  RESOLVED = 'resolved',
  IGNORED = 'ignored',
}

/**
 * 创建告警记录DTO
 */
export class CreateAlertRecordDto {
  @ApiProperty({ description: '用户ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  userId: number;

  @ApiProperty({ description: '服务ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  serviceId: number;

  @ApiProperty({ description: '告警类型', enum: AlertType, example: AlertType.QUOTA_EXCEEDED })
  @IsEnum(AlertType)
  @IsNotEmpty()
  type: AlertType;

  @ApiProperty({ description: '告警级别', enum: AlertLevel, example: AlertLevel.HIGH })
  @IsEnum(AlertLevel)
  @IsNotEmpty()
  level: AlertLevel;

  @ApiProperty({ description: '告警标题', example: '配额超限告警' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(200)
  title: string;

  @ApiProperty({ description: '告警内容', example: '用户当前配额已超过限制，请及时充值' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(1000)
  content: string;

  @ApiPropertyOptional({ description: '触发条件', example: '调用次数 > 1000' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  triggerCondition?: string;

  @ApiPropertyOptional({ description: '当前值', example: '1050' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  currentValue?: string;

  @ApiPropertyOptional({ description: '阈值', example: '1000' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  threshold?: string;

  @ApiPropertyOptional({ description: '相关数据' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({ description: '是否已通知', example: false })
  @IsOptional()
  @IsBoolean()
  notified?: boolean = false;

  @ApiPropertyOptional({ description: '通知方式', example: ['email', 'sms'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  notificationMethods?: string[];

  @ApiPropertyOptional({ description: '告警状态', enum: AlertStatus, example: AlertStatus.PENDING })
  @IsOptional()
  @IsEnum(AlertStatus)
  status?: AlertStatus = AlertStatus.PENDING;
}

/**
 * 更新告警记录DTO
 */
export class UpdateAlertRecordDto {
  @ApiPropertyOptional({ description: '告警级别', enum: AlertLevel })
  @IsOptional()
  @IsEnum(AlertLevel)
  level?: AlertLevel;

  @ApiPropertyOptional({ description: '告警标题' })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  title?: string;

  @ApiPropertyOptional({ description: '告警内容' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  content?: string;

  @ApiPropertyOptional({ description: '告警状态', enum: AlertStatus })
  @IsOptional()
  @IsEnum(AlertStatus)
  status?: AlertStatus;

  @ApiPropertyOptional({ description: '是否已通知' })
  @IsOptional()
  @IsBoolean()
  notified?: boolean;

  @ApiPropertyOptional({ description: '通知方式' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  notificationMethods?: string[];

  @ApiPropertyOptional({ description: '处理备注' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  resolveNote?: string;

  @ApiPropertyOptional({ description: '处理人ID' })
  @IsOptional()
  @IsInt()
  resolvedBy?: number;

  @ApiPropertyOptional({ description: '处理时间' })
  @IsOptional()
  @IsDateString()
  resolvedAt?: string;
}

/**
 * 告警记录查询DTO
 */
export class QueryAlertRecordDto {
  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', example: 10, minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: '用户ID', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  userId?: number;

  @ApiPropertyOptional({ description: '服务ID', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  serviceId?: number;

  @ApiPropertyOptional({ description: '告警类型', enum: AlertType })
  @IsOptional()
  @IsEnum(AlertType)
  type?: AlertType;

  @ApiPropertyOptional({ description: '告警级别', enum: AlertLevel })
  @IsOptional()
  @IsEnum(AlertLevel)
  level?: AlertLevel;

  @ApiPropertyOptional({ description: '告警状态', enum: AlertStatus })
  @IsOptional()
  @IsEnum(AlertStatus)
  status?: AlertStatus;

  @ApiPropertyOptional({ description: '是否已通知' })
  @IsOptional()
  @IsBoolean()
  notified?: boolean;

  @ApiPropertyOptional({ description: '标题搜索', example: '配额' })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({ description: '内容搜索', example: '超限' })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiPropertyOptional({ description: '开始时间' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: '结束时间' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: '排序字段', example: 'createdAt' })
  @IsOptional()
  @IsString()
  @IsIn(['id', 'type', 'level', 'status', 'notified', 'createdAt', 'resolvedAt'])
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', example: 'DESC' })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}

/**
 * 批量处理告警DTO
 */
export class BatchProcessAlertDto {
  @ApiProperty({ description: '告警记录ID列表', example: [1, 2, 3] })
  @IsArray()
  @IsInt({ each: true })
  @IsNotEmpty()
  ids: number[];

  @ApiProperty({ description: '操作类型', example: 'acknowledge' })
  @IsString()
  @IsIn(['acknowledge', 'resolve', 'ignore'])
  action: 'acknowledge' | 'resolve' | 'ignore';

  @ApiPropertyOptional({ description: '处理备注' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  note?: string;
}

/**
 * 告警记录响应DTO
 */
export class AlertRecordResponseDto {
  @ApiProperty({ description: '记录ID', example: 1 })
  id: number;

  @ApiProperty({ description: '用户ID', example: 1 })
  userId: number;

  @ApiProperty({ description: '服务ID', example: 1 })
  serviceId: number;

  @ApiProperty({ description: '告警类型', enum: AlertType })
  type: AlertType;

  @ApiProperty({ description: '告警级别', enum: AlertLevel })
  level: AlertLevel;

  @ApiProperty({ description: '告警标题' })
  title: string;

  @ApiProperty({ description: '告警内容' })
  content: string;

  @ApiPropertyOptional({ description: '触发条件' })
  triggerCondition?: string;

  @ApiPropertyOptional({ description: '当前值' })
  currentValue?: string;

  @ApiPropertyOptional({ description: '阈值' })
  threshold?: string;

  @ApiPropertyOptional({ description: '相关数据' })
  metadata?: Record<string, any>;

  @ApiProperty({ description: '告警状态', enum: AlertStatus })
  status: AlertStatus;

  @ApiProperty({ description: '是否已通知' })
  notified: boolean;

  @ApiPropertyOptional({ description: '通知方式' })
  notificationMethods?: string[];

  @ApiPropertyOptional({ description: '处理备注' })
  resolveNote?: string;

  @ApiPropertyOptional({ description: '处理人ID' })
  resolvedBy?: number;

  @ApiPropertyOptional({ description: '处理时间' })
  resolvedAt?: Date;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 关联信息
  @ApiPropertyOptional({ description: '用户信息' })
  user?: {
    id: number;
    username: string;
    email: string;
  };

  @ApiPropertyOptional({ description: '服务信息' })
  service?: {
    id: number;
    code: string;
    name: string;
    type: string;
  };

  @ApiPropertyOptional({ description: '处理人信息' })
  resolver?: {
    id: number;
    username: string;
    email: string;
  };
}

/**
 * 告警记录列表响应DTO
 */
export class AlertRecordListResponseDto {
  @ApiProperty({ description: '告警记录列表', type: [AlertRecordResponseDto] })
  data: AlertRecordResponseDto[];

  @ApiProperty({ description: '总数', example: 200 })
  total: number;

  @ApiProperty({ description: '当前页', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  limit: number;

  @ApiProperty({ description: '总页数', example: 20 })
  totalPages: number;
}

/**
 * 告警统计DTO
 */
export class AlertStatsDto {
  @ApiProperty({ description: '总告警数', example: 1000 })
  totalAlerts: number;

  @ApiProperty({ description: '待处理告警数', example: 50 })
  pendingAlerts: number;

  @ApiProperty({ description: '已确认告警数', example: 200 })
  acknowledgedAlerts: number;

  @ApiProperty({ description: '已解决告警数', example: 700 })
  resolvedAlerts: number;

  @ApiProperty({ description: '已忽略告警数', example: 50 })
  ignoredAlerts: number;

  @ApiProperty({ description: '按类型分组统计' })
  byType: Record<AlertType, number>;

  @ApiProperty({ description: '按级别分组统计' })
  byLevel: Record<AlertLevel, number>;

  @ApiProperty({ description: '按状态分组统计' })
  byStatus: Record<AlertStatus, number>;

  @ApiProperty({ description: '按服务分组统计' })
  byService: Record<string, {
    total: number;
    pending: number;
    resolved: number;
  }>;

  @ApiProperty({ description: '按用户分组统计' })
  byUser: Record<string, {
    total: number;
    pending: number;
    resolved: number;
  }>;

  @ApiProperty({ description: '按时间分组统计（天）' })
  byDay: Record<string, {
    total: number;
    byLevel: Record<AlertLevel, number>;
  }>;

  @ApiProperty({ description: '平均处理时间（小时）', example: 2.5 })
  avgResolutionTime: number;

  @ApiProperty({ description: '通知成功率', example: 95.5 })
  notificationSuccessRate: number;
}

/**
 * 告警规则配置DTO
 */
export class AlertRuleDto {
  @ApiProperty({ description: '规则名称', example: '配额告警规则' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  @ApiProperty({ description: '告警类型', enum: AlertType })
  @IsEnum(AlertType)
  type: AlertType;

  @ApiProperty({ description: '告警级别', enum: AlertLevel })
  @IsEnum(AlertLevel)
  level: AlertLevel;

  @ApiProperty({ description: '触发条件', example: 'usage_rate > 0.9' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(500)
  condition: string;

  @ApiProperty({ description: '阈值', example: '0.9' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  threshold: string;

  @ApiProperty({ description: '是否启用', example: true })
  @IsBoolean()
  enabled: boolean;

  @ApiPropertyOptional({ description: '通知方式', example: ['email', 'sms'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  notificationMethods?: string[];

  @ApiPropertyOptional({ description: '冷却时间（分钟）', example: 60 })
  @IsOptional()
  @IsInt()
  @Min(1)
  cooldownMinutes?: number;

  @ApiPropertyOptional({ description: '规则描述' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;
}