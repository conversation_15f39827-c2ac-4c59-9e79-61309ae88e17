import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsArray, IsNumber, <PERSON>, <PERSON> } from 'class-validator';

export class ExtractAddressDto {
  @ApiProperty({
    description: '需要提取地址信息的文本内容',
    example: '张三 13812345678 北京市朝阳区建国门外大街1号国贸大厦A座1001室',
  })
  @IsString()
  @IsNotEmpty()
  text: string;

  @ApiProperty({
    description: '提取模式：single(单个地址) 或 multiple(多个地址)',
    example: 'single',
    enum: ['single', 'multiple'],
    default: 'single',
  })
  @IsOptional()
  @IsString()
  mode?: 'single' | 'multiple' = 'single';
}

export class ExtractMultipleAddressDto {
  @ApiProperty({
    description: '需要提取地址信息的文本数组',
    example: [
      '张三 13812345678 北京市朝阳区建国门外大街1号国贸大厦A座1001室',
      '李四 13987654321 上海市浦东新区陆家嘴环路1000号上海中心大厦50楼'
    ],
  })
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  texts: string[];
}

export class AddressInfo {
  @ApiProperty({ description: '姓名', example: '张三' })
  name?: string;

  @ApiProperty({ description: '手机号', example: '13812345678' })
  phone?: string;

  @ApiProperty({ description: '省份', example: '北京市' })
  province?: string;

  @ApiProperty({ description: '城市', example: '北京市' })
  city?: string;

  @ApiProperty({ description: '区/县', example: '朝阳区' })
  district?: string;

  @ApiProperty({ description: '街道', example: '建国门外大街' })
  street?: string;

  @ApiProperty({ description: '详细地址', example: '建国门外大街1号国贸大厦A座1001室' })
  detailAddress?: string;

  @ApiProperty({ description: '详细地址', example: '建国门外大街1号国贸大厦A座1001室' })
  detail_address?: string;

  @ApiProperty({ description: '完整地址', example: '北京市朝阳区建国门外大街1号国贸大厦A座1001室' })
  fullAddress?: string;

  @ApiProperty({ description: '置信度分数 (0-1)', example: 0.95 })
  confidence?: number;

  @ApiProperty({ description: '提取方法', example: 'regex_nlp' })
  extractMethod?: string;

  @ApiProperty({ description: '提取方法', example: 'regex_nlp' })
  extract_method?: string;
}

export class ExtractionResult {
  @ApiProperty({ description: '是否成功提取', example: true })
  success: boolean;

  @ApiProperty({ description: '提取到的地址信息', type: [AddressInfo] })
  addresses: AddressInfo[];

  @ApiProperty({ description: '错误信息', example: null })
  error?: string;

  @ApiProperty({ description: '处理时间(毫秒)', example: 150 })
  processingTime?: number;

  @ApiProperty({ description: '原始文本', example: '张三 13812345678 北京市朝阳区...' })
  originalText?: string;
}

// 地理坐标逆解析相关DTO
export class ReverseGeocodeDto {
  @ApiProperty({
    description: '纬度',
    example: 39.9042,
    minimum: -90,
    maximum: 90
  })
  @IsNumber()
  @Min(-90)
  @Max(90)
  latitude: number;

  @ApiProperty({
    description: '经度',
    example: 116.4074,
    minimum: -180,
    maximum: 180
  })
  @IsNumber()
  @Min(-180)
  @Max(180)
  longitude: number;
}

export class BatchReverseGeocodeDto {
  @ApiProperty({
    description: '坐标点数组',
    example: [
      { latitude: 39.9042, longitude: 116.4074 },
      { latitude: 31.2304, longitude: 121.4737 }
    ],
    type: [ReverseGeocodeDto]
  })
  @IsArray()
  coordinates: ReverseGeocodeDto[];
}

export class GeocodeAddressInfo {
  @ApiProperty({ description: '省份', example: '北京市' })
  province?: string;

  @ApiProperty({ description: '城市', example: '北京市' })
  city?: string;

  @ApiProperty({ description: '区/县', example: '东城区' })
  district?: string;

  @ApiProperty({ description: '街道', example: '长安街' })
  street?: string;

  @ApiProperty({ description: '门牌号', example: '' })
  house_number?: string;

  @ApiProperty({ description: '邮政编码', example: '100006' })
  postcode?: string;

  @ApiProperty({ description: '完整地址', example: '北京市北京市东城区长安街（靠近天安门广场）' })
  full_address?: string;

  @ApiProperty({ description: '最近的POI', example: '天安门广场' })
  nearest_poi?: string;

  @ApiProperty({ description: 'POI类型', example: 'tourism' })
  poi_type?: string;

  @ApiProperty({ description: 'POI距离(米)', example: 50 })
  poi_distance?: number;

  @ApiProperty({ description: '置信度分数 (0-1)', example: 0.95 })
  confidence?: number;

  @ApiProperty({ description: '解析方法', example: '高德地图API' })
  resolution_method?: string;
}

export class ReverseGeocodeResult {
  @ApiProperty({ description: '是否成功解析', example: true })
  success: boolean;

  @ApiProperty({ description: '输入坐标', example: { latitude: 39.9042, longitude: 116.4074 } })
  coordinates?: { latitude: number; longitude: number };

  @ApiProperty({ description: '解析得到的地址信息', type: GeocodeAddressInfo })
  address?: GeocodeAddressInfo;

  @ApiProperty({ description: '总体置信度', example: 0.95 })
  confidence?: number;

  @ApiProperty({ description: '解析方法', example: 'layered_analysis' })
  resolution_method?: string;

  @ApiProperty({ description: '处理层级详情' })
  processing_layers?: any;

  @ApiProperty({ description: '处理时间', example: '1.234s' })
  processing_time?: string;

  @ApiProperty({ description: '错误信息', example: null })
  error?: string;
}

export class BatchReverseGeocodeResult {
  @ApiProperty({ description: '是否成功处理', example: true })
  success: boolean;

  @ApiProperty({ description: '批量解析结果', type: 'array' })
  results?: Array<{
    index: number;
    input_coordinates: { latitude: number; longitude: number };
    result: ReverseGeocodeResult;
  }>;

  @ApiProperty({ description: '总处理数量', example: 2 })
  total_count?: number;

  @ApiProperty({ description: '处理时间', example: '2.456s' })
  processing_time?: string;

  @ApiProperty({ description: '错误信息', example: null })
  error?: string;
}