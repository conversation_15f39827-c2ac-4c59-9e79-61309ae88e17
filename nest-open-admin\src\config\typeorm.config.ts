import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { config } from 'dotenv';
import * as path from 'path';

// 加载配置
config();

// 导入配置
const configData = require('./index').default();

const AppDataSource = new DataSource({
  type: 'mysql',
  host: configData.database.host,
  port: configData.database.port,
  username: configData.database.username,
  password: configData.database.password,
  database: configData.database.database,
  entities: [path.join(__dirname, '../**/*.entity{.ts,.js}')],
  migrations: [path.join(__dirname, '../database/migrations/*{.ts,.js}')],
  synchronize: false,
  logging: true,
  migrationsTableName: 'migrations',
  migrationsRun: false,
});

export default AppDataSource;