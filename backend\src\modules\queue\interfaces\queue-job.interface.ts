/**
 * 队列作业基础数据接口
 * 定义所有队列作业共同的数据结构
 */
export interface BaseJobData {
  taskId: string;
  // 用于队列完成后扣减调用次数
  userId?: number;
  serviceId?: number;
}

/**
 * OCR作业数据接口
 * OCR任务特定的数据结构
 */
export interface OcrJobData extends BaseJobData {
  imageData: string | Buffer; // Base64编码的图片数据或Buffer
  options?: {
    type?: string;
    language?: string;
    [key: string]: any;
  };
}

/**
 * 申通OCR作业数据接口
 * 申通面单OCR任务特定的数据结构
 */
export interface StoOcrJobData extends BaseJobData {
  imageData: string | Buffer; // Base64编码的图片数据或Buffer
  options?: {
    enhanceAccuracy?: boolean; // 是否启用精度增强
    extractStoFields?: boolean; // 是否提取申通特有字段
    [key: string]: any;
  };
}

/**
 * 地址解析作业数据接口
 * 地址解析任务特定的数据结构
 */
export interface AddressJobData extends BaseJobData {
  addressText?: string; // 可选，兼容旧版本
  body?: any; // 兼容通用任务数据格式
  options?: {
    format?: boolean;
    extractComponents?: boolean;
    [key: string]: any;
  };
}

/**
 * 地理坐标作业数据接口
 * 地理坐标处理任务特定的数据结构
 */
export interface GeoJobData extends BaseJobData {
  // 经纬度坐标或地址文本，取决于操作类型
  data: string | {
    latitude: number;
    longitude: number;
  };
  // 操作类型：geocode（地址转坐标）或 reverse_geocode（坐标转地址）
  operation: 'geocode' | 'reverse_geocode';
  options?: {
    coordType?: string; // 坐标系类型，如 'wgs84', 'gcj02', 'bd09'
    [key: string]: any;
  };
}

/**
 * OCR结果接口
 */
export interface OcrResult {
  sender?: {
    name?: string;
    phone?: string;
    address?: string;
  };
  receiver?: {
    name?: string;
    phone?: string;
    address?: string;
  };
  expressInfo?: {
    company?: string;
    trackingNumber?: string;
  };
  rawText?: string;
  confidence?: number;
}

/**
 * 申通OCR结果接口
 */
export interface StoOcrResult {
  sender?: {
    name?: string;
    phone?: string;
    address?: string;
  };
  receiver?: {
    name?: string;
    phone?: string;
    address?: string;
  };
  expressInfo?: {
    company?: string;
    trackingNumber?: string;
    serviceType?: string;
    weight?: string;
  };
  stoSpecific?: {
    sortingCode?: string; // 分拣码
    routeCode?: string;   // 路由码
    packageType?: string; // 包装类型
  };
  rawText?: string;
  confidence?: number;
}

/**
 * 地理坐标处理结果接口
 */
export interface GeoResult {
  // 正向地理编码结果（地址转坐标）
  location?: {
    latitude: number;
    longitude: number;
  };
  // 反向地理编码结果（坐标转地址）
  address?: {
    formatted: string;
    province?: string;
    city?: string;
    district?: string;
    street?: string;
    streetNumber?: string;
    adcode?: string; // 行政区划代码
  };
  confidence?: number; // 结果可信度
} 