/**
 * Bull队列配置
 * 配置Redis连接和默认任务选项
 */

import { BullModuleOptions } from '@nestjs/bull';
import { ConfigService } from '@nestjs/config';
import { QUEUE_NAMES } from '../queue.constants';

/**
 * 创建Bull队列配置
 * @param configService 配置服务
 * @returns Bull模块配置
 */
export const createBullConfig = (configService: ConfigService): BullModuleOptions => ({
  redis: {
    host: configService.get<string>('redis.host', 'localhost'),
    port: configService.get<number>('redis.port', 6379),
    password: configService.get<string>('redis.password'),
    db: configService.get<number>('redis.db', 0),
    keyPrefix: configService.get<string>('redis.keyPrefix', ''),
    maxRetriesPerRequest: configService.get<number>('redis.maxRetriesPerRequest', 3),
    retryDelayOnFailover: configService.get<number>('redis.retryDelayOnFailover', 100),
    enableReadyCheck: configService.get<boolean>('redis.enableReadyCheck', false),
    maxLoadingTimeout: configService.get<number>('redis.maxLoadingTimeout', 1000),
    connectTimeout: configService.get<number>('redis.connectTimeout', 10000),
    // 断线重连设置
    reconnectOnError: (err) => {
      const targetError = 'READONLY';
      if (err.message.includes(targetError)) {
        // 只有在 READONLY 错误时重连
        return true;
      }
      return false;
    },
  } as any,
  defaultJobOptions: {
    removeOnComplete: configService.get<number>('BULL_REMOVE_ON_COMPLETE', 10),
    removeOnFail: configService.get<number>('BULL_REMOVE_ON_FAIL', 5),
    attempts: configService.get<number>('BULL_ATTEMPTS', 3),
    backoff: {
      type: 'exponential',
      delay: configService.get<number>('BULL_BACKOFF_DELAY', 2000),
    },
    // 任务超时设置
    timeout: configService.get<number>('BULL_JOB_TIMEOUT', 30000),
  },
  // 全局设置
  settings: {
    lockDuration: configService.get<number>('BULL_LOCK_DURATION', 30000),
    lockRenewTime: configService.get<number>('BULL_LOCK_RENEW_TIME', 15000),
    stalledInterval: configService.get<number>('BULL_STALLED_INTERVAL', 30000),
    maxStalledCount: configService.get<number>('BULL_MAX_STALLED_COUNT', 1),
    guardInterval: configService.get<number>('BULL_GUARD_INTERVAL', 5000),
  },
  // 事件订阅
  limiter: {
    max: configService.get<number>('BULL_LIMITER_MAX', 50),
    duration: configService.get<number>('BULL_LIMITER_DURATION', 5000),
  },
});

/**
 * 队列特定配置
 * 为不同的队列提供特定的配置
 */
export const queueConfigs = {
  // API调用队列配置
  [QUEUE_NAMES.API_CALL]: {
    concurrency: 10,
    removeOnComplete: 20,
    removeOnFail: 10,
    defaultJobOptions: {
      attempts: 3,
      backoff: {
        type: 'exponential' as const,
        delay: 1000,
      },
      timeout: 30000,
    },
  },
  
  // 告警队列配置
  [QUEUE_NAMES.ALERT]: {
    concurrency: 20,
    removeOnComplete: 50,
    removeOnFail: 20,
    defaultJobOptions: {
      attempts: 2,
      backoff: {
        type: 'fixed' as const,
        delay: 500,
      },
      timeout: 10000,
    },
  },
  
  // OCR队列配置
  [QUEUE_NAMES.OCR]: {
    concurrency: 5,
    removeOnComplete: false,
    removeOnFail: false,
    defaultJobOptions: {
      attempts: 3,
      backoff: {
        type: 'exponential' as const,
        delay: 2000,
      },
      timeout: 180000, // 增加到3分钟
    },
  },
  
  // 地址提取队列配置
  [QUEUE_NAMES.EXTRACT_ADDRESS]: {
    concurrency: 8,
    removeOnComplete: 100,
    removeOnFail: 50,
    defaultJobOptions: {
      attempts: 3,
      backoff: {
        type: 'exponential' as const,
        delay: 1500,
      },
      timeout: 60000, // 增加到1分钟
    },
  },
  
  // 逆地理编码队列配置
  [QUEUE_NAMES.REV_GEO]: {
    concurrency: 10,
    removeOnComplete: 100,
    removeOnFail: 50,
    defaultJobOptions: {
      attempts: 3,
      backoff: {
        type: 'exponential' as const,
        delay: 1500,
      },
      timeout: 60000, // 增加到1分钟
    },
  },
};