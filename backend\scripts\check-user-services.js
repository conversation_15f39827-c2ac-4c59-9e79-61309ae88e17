const mysql = require('mysql2/promise');

async function checkUserServices() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '123456',
    database: 'openapidb'
  });

  try {
    console.log('=== 查询用户ID为4的信息 ===');
    
    // 查询用户信息
    const [users] = await connection.execute('SELECT * FROM open_user WHERE id = ?', [4]);
    console.log('用户信息:', JSON.stringify(users[0], null, 2));

    // 查看所有表
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('\n数据库中的所有表:');
    tables.forEach(table => {
      console.log(`- ${Object.values(table)[0]}`);
    });

    // 查询用户服务关联（尝试不同的表名）
    try {
      const [userServices] = await connection.execute('SELECT * FROM user_services WHERE userId = ?', [4]);
      console.log('\n用户服务关联数量:', userServices.length);
      if (userServices.length > 0) {
        console.log('用户服务关联:', JSON.stringify(userServices, null, 2));
      } else {
        console.log('用户没有任何服务关联记录！');
      }
    } catch (error) {
      console.log('\nuser_services表不存在，尝试其他表名...');

      // 尝试其他可能的表名
      const possibleTables = ['open_user_service', 'user_service', 'open_user_services'];
      for (const tableName of possibleTables) {
        try {
          const [userServices] = await connection.execute(`SELECT * FROM ${tableName} WHERE userId = ?`, [4]);
          console.log(`\n在${tableName}表中找到用户服务关联数量:`, userServices.length);
          if (userServices.length > 0) {
            console.log('用户服务关联:', JSON.stringify(userServices, null, 2));
          }
          break;
        } catch (e) {
          console.log(`${tableName}表不存在`);
        }
      }
    }

    // 查询所有服务
    const [services] = await connection.execute('SELECT * FROM open_service ORDER BY id');
    console.log('\n所有服务:');
    services.forEach(service => {
      console.log(`- ID: ${service.id}, Code: ${service.code}, Name: ${service.name}, Status: ${service.status}`);
    });

    // 查询user_service表结构和数据
    const [userServiceDesc] = await connection.execute('DESCRIBE user_service');
    console.log('\nuser_service表结构:');
    userServiceDesc.forEach(field => {
      console.log(`- ${field.Field}: ${field.Type} ${field.Null === 'YES' ? '(可空)' : '(非空)'} ${field.Key ? `[${field.Key}]` : ''}`);
    });

    const [userServiceData] = await connection.execute('SELECT * FROM user_service WHERE user_id = ?', [4]);
    console.log('\nuser_service表中用户4的数据数量:', userServiceData.length);
    if (userServiceData.length > 0) {
      console.log('用户服务关联:', JSON.stringify(userServiceData, null, 2));
    } else {
      console.log('用户在user_service表中没有任何记录！');
    }

    // 查询API密钥（尝试不同表名）
    try {
      const [apiKeys] = await connection.execute('SELECT * FROM api_keys WHERE user_id = ?', [4]);
      console.log('\n用户API密钥数量:', apiKeys.length);
      if (apiKeys.length > 0) {
        apiKeys.forEach(key => {
          console.log(`- ID: ${key.id}, Key: ${key.key}, Name: ${key.name}, Status: ${key.status}`);
        });
      }
    } catch (error) {
      console.log('\napi_keys表查询失败:', error.message);
    }

  } catch (error) {
    console.error('查询失败:', error.message);
  } finally {
    await connection.end();
  }
}

checkUserServices();
