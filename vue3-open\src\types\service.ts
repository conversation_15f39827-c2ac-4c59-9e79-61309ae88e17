// 服务状态
export type ServiceStatus = 'active' | 'inactive' | 'maintenance' | 'deprecated'

// 服务类型 - 与后端ServiceType枚举保持一致
export type ServiceType = 'ai_service' | 'ocr' | 'nlp' | 'cv' | 'geo' | 'data' | 'other'

// 服务定价模式
export type PricingModel = 'free' | 'pay_per_use' | 'subscription' | 'tiered'

// 服务信息
export interface Service {
  id: string | number
  name: string
  code: string
  description: string
  type: ServiceType
  status: ServiceStatus
  serviceStatus?: ServiceStatus // 兼容字段
  version?: string
  endpoint?: string
  icon?: string
  color?: string
  price?: number
  unitPrice?: number // 兼容字段
  unit?: string
  accuracy?: number
  responseTime?: number
  supportedFormats?: string[]
  freeQuota?: number
  pricing?: {
    type: 'free' | 'pay_per_use' | 'subscription'
    price: number
    unit: string
  }
  pricingModel?: string // 兼容字段
  pricingTiers?: {
    min: number
    max?: number
    price: number
  }[]
  features?: string | string[]
  limitations?: {
    rateLimit: number
    maxFileSize?: number
    supportedFormats?: string[]
  }
  documentation?: {
    overview: string
    parameters: Record<string, unknown>[]
    examples: Record<string, unknown>[]
  }
  createdAt: string
  updatedAt: string
  callCount?: number
  errorRate?: number
  averageResponseTime?: number
  sortOrder?: number // 兼容字段
}

// 服务版本信息
export interface ServiceVersion {
  id: string
  serviceId: string
  version: string
  description?: string
  endpoint: string
  apiSchema?: Record<string, any>
  configuration?: Record<string, any>
  isCurrent: boolean
  isCompatible: boolean
  releaseDate?: string
  deprecationDate?: string
  createdAt: string
  updatedAt: string
}

// 服务详情（扩展服务信息）
export interface ServiceDetail extends Service {
  versions?: ServiceVersion[]
  currentVersion?: ServiceVersion
  usage?: {
    totalCalls: number
    successCalls: number
    errorCalls: number
    avgResponseTime: number
    peakCalls: number
    peakTime: string
  }
  examples?: {
    name: string
    description: string
    request: any
    response: any
  }[]
  changelog?: {
    version: string
    changes: string[]
    date: string
  }[]
  dependencies?: {
    name: string
    version: string
    required: boolean
  }[]
}

// 服务统计
export interface ServiceStats {
  totalServices: number
  activeServices: number
  totalCalls: number
  errorRate: number
  averageResponseTime: number
  popularServices: Service[]
  recentlyAdded: Service[]
}

// 服务查询参数
export interface ServiceQueryParams {
  page?: number
  pageSize?: number
  type?: ServiceType
  status?: ServiceStatus
  keyword?: string
  category?: string
  tags?: string[]
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 创建服务表单
export interface CreateServiceForm {
  name: string
  code: string
  description: string
  type: ServiceType
  endpoint: string
  documentation?: string
  pricing: {
    model: PricingModel
    basePrice?: number
    currency?: string
    unit?: string
  }
  features: string[]
  limitations?: {
    rateLimit?: number
    maxFileSize?: number
    supportedFormats?: string[]
  }
  metadata?: {
    category: string
    tags: string[]
  }
}

// 服务测试请求
export interface ServiceTestRequest {
  serviceId: string
  method: string
  headers?: Record<string, string>
  params?: Record<string, unknown>
  body?: unknown
  files?: File[]
}

// 服务测试响应
export interface ServiceTestResponse {
  success: boolean
  statusCode: number
  headers: Record<string, string>
  data: unknown
  responseTime: number
  cost?: number
  error?: string
}