import { Controller, Post, Get, UseGuards, Query, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { ScheduleService } from './schedule.service';
import { TaskHistoryService } from './services/task-history.service';
import { Roles } from '../../common/decorators/roles.decorator';
import { RoleEnum } from '../user/entities/user.entity';
import {
  ScheduleStatsDto,
  TaskStatusDto,
  ManualExecutionResultDto,
  TaskExecutionQueryDto,
  TaskExecutionPageDto
} from './dto';

@ApiTags('定时任务管理')
@Controller('schedule')
@ApiBearerAuth()
export class ScheduleController {
  constructor(
    private readonly scheduleService: ScheduleService,
    private readonly taskHistoryService: TaskHistoryService,
  ) {}

  @Post('reset-free-quota')
  @Roles(RoleEnum.ADMIN)
  @ApiOperation({ summary: '手动重置每日免费次数' })
  @ApiResponse({ status: 200, description: '重置成功', type: ManualExecutionResultDto })
  async manualResetFreeQuota(): Promise<ManualExecutionResultDto> {
    const startTime = new Date();
    try {
      await this.scheduleService.resetDailyFreeQuota();
      return {
        success: true,
        message: '每日免费次数重置成功',
        startTime,
        endTime: new Date(),
        duration: Date.now() - startTime.getTime()
      };
    } catch (error) {
      return {
        success: false,
        message: '每日免费次数重置失败',
        startTime,
        endTime: new Date(),
        duration: Date.now() - startTime.getTime(),
        error: error.message
      };
    }
  }

  @Post('sync-api-keys')
  @Roles(RoleEnum.ADMIN)
  @ApiOperation({ summary: '手动同步API密钥到缓存' })
  @ApiResponse({ status: 200, description: '同步成功', type: ManualExecutionResultDto })
  async manualSyncApiKeys(): Promise<ManualExecutionResultDto> {
    const startTime = new Date();
    try {
      await this.scheduleService.syncApiKeysToCache();
      return {
        success: true,
        message: 'API密钥同步成功',
        startTime,
        endTime: new Date(),
        duration: Date.now() - startTime.getTime()
      };
    } catch (error) {
      return {
        success: false,
        message: 'API密钥同步失败',
        startTime,
        endTime: new Date(),
        duration: Date.now() - startTime.getTime(),
        error: error.message
      };
    }
  }

  @Post('cleanup-data')
  @Roles(RoleEnum.ADMIN)
  @ApiOperation({ summary: '手动清理过期数据' })
  @ApiResponse({ status: 200, description: '清理成功', type: ManualExecutionResultDto })
  async manualCleanupData(): Promise<ManualExecutionResultDto> {
    const startTime = new Date();
    try {
      await this.scheduleService.cleanupExpiredData();
      return {
        success: true,
        message: '过期数据清理成功',
        startTime,
        endTime: new Date(),
        duration: Date.now() - startTime.getTime()
      };
    } catch (error) {
      return {
        success: false,
        message: '过期数据清理失败',
        startTime,
        endTime: new Date(),
        duration: Date.now() - startTime.getTime(),
        error: error.message
      };
    }
  }

  @Get('status')
  @Roles(RoleEnum.ADMIN)
  @ApiOperation({ summary: '获取定时任务状态' })
  @ApiResponse({ status: 200, description: '获取成功', type: [TaskStatusDto] })
  async getScheduleStatus(): Promise<TaskStatusDto[]> {
    return await this.scheduleService.getTaskStatusList();
  }

  @Get('stats')
  @Roles(RoleEnum.ADMIN)
  @ApiOperation({ summary: '获取定时任务统计信息' })
  @ApiResponse({ status: 200, description: '获取成功', type: ScheduleStatsDto })
  async getScheduleStats(): Promise<ScheduleStatsDto> {
    return await this.scheduleService.getScheduleStats();
  }

  @Get('executions')
  @Roles(RoleEnum.ADMIN)
  @ApiOperation({ summary: '获取任务执行历史' })
  @ApiResponse({ status: 200, description: '获取成功', type: TaskExecutionPageDto })
  async getTaskExecutions(@Query() query: TaskExecutionQueryDto): Promise<TaskExecutionPageDto> {
    return await this.taskHistoryService.findExecutions(query);
  }

  @Get('executions/stats')
  @Roles(RoleEnum.ADMIN)
  @ApiOperation({ summary: '获取任务执行统计' })
  @ApiQuery({ name: 'taskName', required: false, description: '任务名称' })
  async getTaskExecutionStats(@Query('taskName') taskName?: string) {
    return await this.taskHistoryService.getTaskStats(taskName);
  }
}