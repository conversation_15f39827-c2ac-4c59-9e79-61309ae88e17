import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  BeforeInsert,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { OrderEntity } from '../../order/entities/order.entity';
import { UserEntity } from '../../user/entities/user.entity';
import { PaymentMethod, PaymentStatus } from '../../order/enums/order.enum';

@Entity('payments')
@Index(['orderId'])
@Index(['userId'])
@Index(['thirdPartyNo'])
@Index(['status'])
@Index(['createdAt'])
export class PaymentEntity {
  @ApiProperty({ description: '支付ID', example: 1 })
  @PrimaryGeneratedColumn({ comment: '支付ID' })
  id: number;

  @ApiProperty({ description: '订单ID', example: 1 })
  @Column({ type: 'int', name: 'order_id', comment: '订单ID' })
  orderId: number;

  @ApiProperty({ description: '用户ID', example: 1 })
  @Column({ type: 'int', name: 'user_id', comment: '用户ID' })
  userId: number;

  @ApiProperty({ description: '支付单号', example: 'PAY202312010001' })
  @Column({ type: 'varchar', length: 32, name: 'payment_no', unique: true, comment: '支付单号' })
  paymentNo: string;

  @ApiProperty({ description: '第三方支付单号', example: '2023120122001234567890123456' })
  @Column({ type: 'varchar', length: 64, name: 'third_party_no', nullable: true, comment: '第三方支付单号' })
  thirdPartyNo?: string;

  @ApiProperty({ description: '支付方式', enum: PaymentMethod, example: PaymentMethod.ALIPAY })
  @Column({
    type: 'enum',
    enum: PaymentMethod,
    name: 'payment_method',
    comment: '支付方式'
  })
  paymentMethod: PaymentMethod;

  @ApiProperty({ description: '支付状态', enum: PaymentStatus, example: PaymentStatus.PENDING })
  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
    comment: '支付状态'
  })
  status: PaymentStatus;

  @ApiProperty({ description: '支付金额', example: 100.00 })
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: '支付金额'
  })
  amount: number;

  @ApiProperty({ description: '支付数据', example: '{"trade_no": "xxx", "buyer_id": "xxx"}' })
  @Column({
    type: 'json',
    name: 'payment_data',
    nullable: true,
    comment: '支付数据'
  })
  paymentData?: Record<string, any>;

  @ApiProperty({ description: '支付时间', example: '2023-12-01T10:30:00Z' })
  @Column({
    type: 'timestamp',
    name: 'paid_at',
    nullable: true,
    comment: '支付时间'
  })
  paidAt?: Date;

  @ApiProperty({ description: '失败原因', example: '余额不足' })
  @Column({
    type: 'varchar',
    length: 500,
    name: 'failure_reason',
    nullable: true,
    comment: '失败原因'
  })
  failureReason?: string;

  @ApiProperty({ description: '回调数据', example: '{"notify_id": "xxx", "trade_status": "TRADE_SUCCESS"}' })
  @Column({
    type: 'json',
    name: 'callback_data',
    nullable: true,
    comment: '回调数据'
  })
  callbackData?: Record<string, any>;

  @ApiProperty({ description: '创建时间', example: '2023-12-01T10:00:00Z' })
  @CreateDateColumn({ name: 'created_at', comment: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间', example: '2023-12-01T10:30:00Z' })
  @UpdateDateColumn({ name: 'updated_at', comment: '更新时间' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => OrderEntity, { lazy: true })
  @JoinColumn({ name: 'order_id' })
  order: Promise<OrderEntity>;

  @ManyToOne(() => UserEntity, { lazy: true })
  @JoinColumn({ name: 'user_id' })
  user: Promise<UserEntity>;

  /**
   * 生成支付单号
   */
  @BeforeInsert()
  generatePaymentNo() {
    if (!this.paymentNo) {
      const timestamp = Date.now().toString();
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      this.paymentNo = `PAY${timestamp}${random}`;
    }
  }

  /**
   * 检查支付是否成功
   */
  isSuccess(): boolean {
    return this.status === PaymentStatus.SUCCESS;
  }

  /**
   * 检查支付是否失败
   */
  isFailed(): boolean {
    return this.status === PaymentStatus.FAILED;
  }

  /**
   * 检查支付是否可以重试
   */
  canRetry(): boolean {
    return [PaymentStatus.FAILED, PaymentStatus.CANCELLED].includes(this.status);
  }
}
