<template>
  <div class="package-card">
    <!-- 套餐包头部 -->
    <div class="package-header">
      <div class="package-icon">
        <el-icon :size="32">
          <component :is="getPackageIcon(package.type)" />
        </el-icon>
      </div>
      <div class="package-meta">
        <h3 class="package-name">{{ package.name }}</h3>
        <div class="package-status">
          <el-tag :type="getStatusType(package.status)" size="small">
            {{ getStatusLabel(package.status) }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 套餐包描述 -->
    <div class="package-description">
      <p>{{ package.description }}</p>
    </div>

    <!-- 价格信息 -->
    <div class="package-pricing">
      <div class="price-main">
        <span class="price">¥{{ (package.price / 100).toFixed(2) }}</span>
        <span v-if="package.originalPrice" class="original-price">
          ¥{{ (package.originalPrice / 100).toFixed(2) }}
        </span>
      </div>
      <div class="price-billing">
        <span class="billing-type">{{ getBillingTypeLabel(package.billingType) }}</span>
        <span v-if="package.discount" class="discount">{{ package.discount }}折</span>
      </div>
    </div>

    <!-- 套餐包特性 -->
    <div class="package-features">
      <div class="features-title">包含功能：</div>
      <ul class="features-list">
        <li v-for="feature in package.features.slice(0, 4)" :key="feature.name" class="feature-item">
          <el-icon class="feature-icon" :class="{ unlimited: feature.unlimited }">
            <Check v-if="feature.included" />
            <Close v-else />
          </el-icon>
          <span class="feature-text">
            {{ feature.name }}
            <span v-if="feature.limit && !feature.unlimited" class="feature-limit">
              ({{ feature.limit }}{{ feature.unit }})
            </span>
            <span v-if="feature.unlimited" class="feature-unlimited">不限量</span>
          </span>
        </li>
      </ul>
      <div v-if="package.features.length > 4" class="more-features">
        +{{ package.features.length - 4 }} 更多功能
      </div>
    </div>

    <!-- 服务配额 -->
    <div v-if="package.serviceQuotas.length > 0" class="package-quotas">
      <div class="quotas-title">服务配额：</div>
      <div class="quotas-grid">
        <div v-for="quota in package.serviceQuotas.slice(0, 3)" :key="quota.serviceId" class="quota-item">
          <span class="quota-service">{{ quota.serviceName }}</span>
          <span class="quota-limit">
            {{ quota.unlimited ? '不限量' : `${quota.limit}次` }}
          </span>
        </div>
      </div>
    </div>

    <!-- 标签 -->
    <div v-if="package.tags.length > 0" class="package-tags">
      <el-tag
        v-for="tag in package.tags.slice(0, 3)"
        :key="tag"
        size="small"
        effect="plain"
        class="package-tag"
      >
        {{ tag }}
      </el-tag>
    </div>

    <!-- 统计信息 -->
    <div class="package-stats">
      <div class="stat-item">
        <span class="stat-label">销量：</span>
        <span class="stat-value">{{ package.salesCount }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">评分：</span>
        <span class="stat-value">
          <el-rate
            v-model="package.rating"
            disabled
            show-score
            text-color="#ff9900"
            score-template="{value}"
            size="small"
          />
        </span>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div v-if="showActions" class="package-actions">
      <el-button
        type="primary"
        size="large"
        :disabled="package.status !== 'active'"
        @click.stop="handlePurchase"
        class="purchase-btn"
      >
        <el-icon><ShoppingCart /></el-icon>
        立即购买
      </el-button>
      
      <el-button
        size="large"
        @click.stop="handleViewDetail"
        class="detail-btn"
      >
        查看详情
      </el-button>
    </div>

    <!-- 有效期信息 -->
    <div v-if="package.validDays" class="package-validity">
      <el-icon><Clock /></el-icon>
      <span>有效期：{{ package.validDays }}天</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  Check,
  Close,
  ShoppingCart,
  Clock,
  Box,
  Star,
  Medal,
  Trophy
} from '@element-plus/icons-vue'
import type { Package } from '../../types/package'

interface Props {
  package: Package
  showActions?: boolean
}

interface Emits {
  purchase: [pkg: Package]
  viewDetail: [pkg: Package]
}

const props = withDefaults(defineProps<Props>(), {
  showActions: false
})

const emit = defineEmits<Emits>()
const router = useRouter()

// 计算属性
const getPackageIcon = (type: string) => {
  const icons: Record<string, any> = {
    basic: Box,
    standard: Star,
    premium: Medal,
    enterprise: Trophy
  }
  return icons[type] || Box
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    active: 'success',
    inactive: 'info',
    deprecated: 'warning',
    discontinued: 'danger'
  }
  return types[status] || 'info'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    active: '可用',
    inactive: '暂停',
    deprecated: '即将下线',
    discontinued: '已下线'
  }
  return labels[status] || status
}

const getBillingTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    one_time: '一次性',
    monthly: '/ 月',
    yearly: '/ 年',
    usage_based: '按量付费'
  }
  return labels[type] || type
}

// 方法
const handlePurchase = () => {
  emit('purchase', props.package)
}

const handleViewDetail = () => {
  emit('viewDetail', props.package)
  router.push(`/packages/${props.package.id}`)
}
</script>

<style scoped>
.package-card {
  padding: 24px;
  border-radius: 12px;
  background: white;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.package-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.package-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.package-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.package-meta {
  flex: 1;
  min-width: 0;
}

.package-name {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1f2937;
  line-height: 1.4;
}

.package-status {
  display: flex;
  align-items: center;
}

.package-description {
  margin-bottom: 20px;
}

.package-description p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.package-pricing {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.price-main {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 4px;
}

.price {
  font-size: 28px;
  font-weight: 700;
  color: #3b82f6;
}

.original-price {
  font-size: 16px;
  color: #9ca3af;
  text-decoration: line-through;
}

.price-billing {
  display: flex;
  align-items: center;
  gap: 8px;
}

.billing-type {
  font-size: 14px;
  color: #6b7280;
}

.discount {
  background: #fef3c7;
  color: #d97706;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.package-features {
  margin-bottom: 20px;
}

.features-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
}

.feature-icon {
  width: 16px;
  height: 16px;
  color: #10b981;
}

.feature-icon:not(.unlimited) {
  color: #ef4444;
}

.feature-text {
  color: #374151;
  line-height: 1.4;
}

.feature-limit {
  color: #6b7280;
  font-size: 12px;
}

.feature-unlimited {
  color: #10b981;
  font-size: 12px;
  font-weight: 600;
}

.more-features {
  font-size: 12px;
  color: #6b7280;
  margin-top: 8px;
  text-align: center;
}

.package-quotas {
  margin-bottom: 20px;
}

.quotas-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.quotas-grid {
  display: grid;
  gap: 8px;
}

.quota-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f9fafb;
  border-radius: 6px;
  font-size: 13px;
}

.quota-service {
  color: #374151;
  font-weight: 500;
}

.quota-limit {
  color: #6b7280;
}

.package-tags {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.package-tag {
  font-size: 12px;
}

.package-stats {
  margin-bottom: 20px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-label {
  color: #6b7280;
}

.stat-value {
  color: #374151;
  font-weight: 500;
}

.package-actions {
  margin-top: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.purchase-btn {
  width: 100%;
  height: 44px;
  font-weight: 600;
}

.detail-btn {
  width: 100%;
  height: 40px;
}

.package-validity {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
  font-size: 13px;
  color: #6b7280;
}

.package-validity .el-icon {
  width: 14px;
  height: 14px;
}
</style>