import { DataSource } from 'typeorm';
import { ServiceEntity } from '../../modules/service/entities/service.entity';
import { ServiceType, ServiceStatus, PricingModel } from '../../modules/service/dto/service.dto';

export class ServiceSeed {
  async run(dataSource: DataSource): Promise<void> {
    const serviceRepository = dataSource.getRepository(ServiceEntity);

    // 检查是否已存在服务数据
    const existingServices = await serviceRepository.count();
    if (existingServices > 0) {
      console.log('服务数据已存在，跳过创建');
      return;
    }

    // 创建基础服务数据
    const services = [
      {
        code: 'OCR_EXPRESS',
        name: '物流面单OCR识别',
        description: '智能识别物流面单信息，提取收发件人、地址、电话等关键信息，支持多种面单格式，准确率高达95%以上。适用于物流企业、电商平台等自动化数据录入场景。',
        type: ServiceType.OCR,
        serviceStatus: ServiceStatus.ACTIVE,
        pricingModel: PricingModel.PAY_PER_USE,
        currentVersion: 'v1.0.0',
        features: '高精度识别,多格式支持,实时处理,结构化输出,批量处理',
        endpoint: '/op/ocr/upload',
        price: 0.05,
        dailyLimit: 10000,
        minuteLimit: 100,
        requireAuth: 1,
        isAsync: 0,
        timeout: 30,
        config: {
          supportedFormats: ['jpg', 'jpeg', 'png', 'pdf', 'bmp', 'tiff'],
          maxFileSize: '10MB',
          accuracy: 0.95,
          supportedCouriers: ['顺丰', '圆通', '中通', '申通', '韵达', '百世', '邮政', '京东', '天天', '德邦'],
          outputFields: ['sender_name', 'sender_phone', 'sender_address', 'receiver_name', 'receiver_phone', 'receiver_address', 'tracking_number', 'courier_name', 'weight', 'package_count'],
          batchLimit: 10,
          preProcessing: {
            autoRotate: true,
            enhanceImage: true,
            cropToContent: true
          },
          returnOriginalImage: false,
          accessMethods: [
            {
              type: 'file_upload',
              endpoint: '/op/ocr/upload',
              method: 'POST',
              contentType: 'multipart/form-data',
              maxFileSize: '5MB',
              supportedFormats: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
            },
            {
              type: 'base64',
              endpoint: '/op/ocr/recognize',
              method: 'POST',
              contentType: 'application/json',
              maxBase64Length: 7000000 // 约5MB的base64编码长度
            }
          ]
        }
      },
      {
        code: 'ADDRESS_EXTRACT',
        name: '物流文本精确提取省市区详细地址',
        description: '从文本中精确提取省市区详细地址信息，支持非结构化文本解析，可处理各种格式的地址信息，并进行标准化输出，支持省市区到街道社区的精确识别。',
        type: ServiceType.NLP,
        serviceStatus: ServiceStatus.ACTIVE,
        pricingModel: PricingModel.PAY_PER_USE,
        currentVersion: 'v1.0.0',
        features: '智能分词,地址标准化,高准确率,支持模糊地址,批量处理,多维度纠错',
        endpoint: '/op/address/extract',
        price: 0.05,
        dailyLimit: 20000,
        minuteLimit: 200,
        requireAuth: 1,
        isAsync: 0,
        timeout: 15,
        config: {
          supportedRegions: ['全国省市区县'],
          outputFormat: 'standard',
          accuracy: 0.98,
          maxTextLength: 2000,
          batchLimit: 50,
          supportedOutputFields: {
            province: true,
            city: true,
            district: true,
            street: true,
            community: true,
            building: true,
            room: true,
            detail: true,
            postalCode: true,
            formatted: true
          },
          addressNormalization: true,
          fuzzyMatching: true,
          confidenceScoreThreshold: 0.7,
          returnMultipleCandidates: true,
          candidateLimit: 3
        }
      },
      {
        code: 'GEO_REVERSE',
        name: '地理坐标逆解析出地址',
        description: '根据经纬度坐标逆向解析出详细地址信息，支持多种坐标系，可输出丰富的POI信息，精确到建筑物级别。适用于物流配送、位置服务、用户轨迹分析等场景。',
        type: ServiceType.DATA,
        serviceStatus: ServiceStatus.ACTIVE,
        pricingModel: PricingModel.PAY_PER_USE,
        currentVersion: 'v1.0.0',
        features: '高精度定位,实时解析,多坐标系支持,POI识别,跨平台兼容,批量处理',
        endpoint: '/op/address/rev-geo',
        price: 0.05,
        dailyLimit: 50000,
        minuteLimit: 500,
        requireAuth: 1,
        isAsync: 0,
        timeout: 10,
        config: {
          supportedCoordSystems: ['WGS84', 'GCJ02', 'BD09'],
          precision: 'building',
          language: ['zh-CN', 'en-US'],
          radius: 50,
          batchLimit: 100,
          includePoiInfo: true,
          maxPois: 10,
          poiTypes: ['餐饮', '住宿', '购物', '交通', '教育', '医疗', '金融', '休闲娱乐', '旅游景点', '商务办公'],
          includeRoadInfo: true,
          includeAddressComponents: true,
          includeBusinessAreas: true,
          formatOptions: {
            formatted: true,
            short: true,
            long: true
          },
          distanceCalculation: true
        }
      }
    ];

    for (const serviceData of services) {
      const service = new ServiceEntity();
      
      // 直接使用实体中的transformer将对象转换为JSON字符串
      Object.assign(service, serviceData);
      service.userId = 1; // 关联到管理员用户
      service.serviceId = 0; // 系统服务
      service.callCount = 0;
      
      try {
        await serviceRepository.save(service);
        console.log(`✅ 创建服务: ${service.name}`);
      } catch (error) {
        console.error(`❌ 创建服务失败: ${service.name}`, error);
        throw error;
      }
    }

    console.log('✅ 基础服务数据创建完成');
  }
}