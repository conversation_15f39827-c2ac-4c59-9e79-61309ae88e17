import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsIn, IsOptional, IsDateString, IsObject } from 'class-validator';

export class PaymentCallbackDto {
  @ApiProperty({
    description: '订单号',
    example: 'TOP20231201123456ABCDEF',
  })
  @IsString({ message: '订单号必须是字符串' })
  orderId: string;

  @ApiProperty({
    description: '支付平台的支付ID',
    example: 'pay_1234567890',
  })
  @IsString({ message: '支付ID必须是字符串' })
  paymentId: string;

  @ApiProperty({
    description: '支付金额',
    example: 100.00,
  })
  @IsNumber({}, { message: '支付金额必须是数字' })
  amount: number;

  @ApiProperty({
    description: '支付状态',
    example: 'success',
    enum: ['success', 'failed', 'cancelled'],
  })
  @IsString({ message: '支付状态必须是字符串' })
  @IsIn(['success', 'failed', 'cancelled'], { 
    message: '支付状态必须是 success、failed 或 cancelled' 
  })
  status: 'success' | 'failed' | 'cancelled';

  @ApiProperty({
    description: '支付方式',
    example: 'alipay',
    enum: ['alipay', 'wechat', 'bank'],
  })
  @IsString({ message: '支付方式必须是字符串' })
  @IsIn(['alipay', 'wechat', 'bank'], { 
    message: '支付方式必须是 alipay、wechat 或 bank' 
  })
  paymentMethod: 'alipay' | 'wechat' | 'bank';

  @ApiProperty({
    description: '第三方支付平台的交易号',
    example: '2023120122001234567890123456',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '交易号必须是字符串' })
  transactionId?: string;

  @ApiProperty({
    description: '支付完成时间',
    example: '2023-12-01T12:34:56.789Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: '支付时间必须是有效的日期字符串' })
  paidAt?: string;

  @ApiProperty({
    description: '额外的元数据',
    example: {
      buyer_id: '****************',
      buyer_logon_id: '138****1234',
      trade_status: 'TRADE_SUCCESS'
    },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: '元数据必须是对象' })
  metadata?: Record<string, any>;
}