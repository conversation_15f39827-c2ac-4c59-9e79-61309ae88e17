import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, FindManyOptions } from 'typeorm';
import { TaskExecution, TaskExecutionStatus } from '../entities/task-execution.entity';
import { TaskExecutionQueryDto, TaskExecutionPageDto, TaskExecutionStatsDto } from '../dto';

/**
 * 任务历史服务
 * 负责管理任务执行记录的CRUD操作和统计分析
 */
@Injectable()
export class TaskHistoryService {
  private readonly logger = new Logger(TaskHistoryService.name);

  constructor(
    @InjectRepository(TaskExecution)
    private readonly taskExecutionRepository: Repository<TaskExecution>,
  ) {}

  /**
   * 创建任务执行记录
   */
  async createExecution(
    taskName: string,
    taskType: string,
    parameters?: any,
    maxRetries: number = 3,
  ): Promise<TaskExecution> {
    const execution = this.taskExecutionRepository.create({
      taskName,
      taskType,
      parameters,
      maxRetries,
      status: TaskExecutionStatus.PENDING,
    });

    return await this.taskExecutionRepository.save(execution);
  }

  /**
   * 开始执行任务
   */
  async startExecution(executionId: number): Promise<void> {
    await this.taskExecutionRepository.update(executionId, {
      status: TaskExecutionStatus.RUNNING,
      startTime: new Date(),
    });
  }

  /**
   * 完成任务执行
   */
  async completeExecution(
    executionId: number,
    success: boolean,
    result?: any,
    errorMessage?: string,
    errorStack?: string,
  ): Promise<void> {
    const execution = await this.taskExecutionRepository.findOne({
      where: { id: executionId },
    });

    if (!execution) {
      this.logger.error(`任务执行记录不存在: ${executionId}`);
      return;
    }

    const endTime = new Date();
    execution.endTime = endTime;
    execution.status = success ? TaskExecutionStatus.SUCCESS : TaskExecutionStatus.FAILED;
    execution.result = result ? JSON.stringify(result) : null;
    execution.errorMessage = errorMessage || null;
    execution.errorStack = errorStack || null;

    // 计算执行耗时
    execution.calculateDuration();

    // 如果失败且可以重试，设置重试时间
    if (!success && execution.canRetry()) {
      execution.status = TaskExecutionStatus.RETRYING;
      execution.setNextRetryTime();
    }

    await this.taskExecutionRepository.save(execution);
  }

  /**
   * 重试任务执行
   */
  async retryExecution(executionId: number): Promise<void> {
    const execution = await this.taskExecutionRepository.findOne({
      where: { id: executionId },
    });

    if (!execution || !execution.canRetry()) {
      this.logger.error(`任务不能重试: ${executionId}`);
      return;
    }

    execution.incrementRetryCount();
    execution.status = TaskExecutionStatus.PENDING;
    execution.startTime = null;
    execution.endTime = null;
    execution.duration = null;
    execution.nextRetryTime = null;

    await this.taskExecutionRepository.save(execution);
  }

  /**
   * 获取需要重试的任务
   */
  async getTasksToRetry(): Promise<TaskExecution[]> {
    return await this.taskExecutionRepository.find({
      where: {
        status: TaskExecutionStatus.RETRYING,
        nextRetryTime: Between(new Date(0), new Date()),
      },
      order: { nextRetryTime: 'ASC' },
    });
  }

  /**
   * 分页查询任务执行历史
   */
  async findExecutions(query: TaskExecutionQueryDto): Promise<TaskExecutionPageDto> {
    const { taskName, taskType, status, startDate, endDate, page, limit } = query;

    const where: any = {};
    if (taskName) where.taskName = taskName;
    if (taskType) where.taskType = taskType;
    if (status) where.status = status;
    if (startDate && endDate) {
      where.createdAt = Between(startDate, endDate);
    }

    const options: FindManyOptions<TaskExecution> = {
      where,
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    };

    const [items, total] = await this.taskExecutionRepository.findAndCount(options);

    return {
      items,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * 获取任务执行统计
   */
  async getTaskStats(taskName?: string): Promise<TaskExecutionStatsDto[]> {
    const query = this.taskExecutionRepository
      .createQueryBuilder('execution')
      .select([
        'execution.taskName as taskName',
        'COUNT(*) as totalExecutions',
        'SUM(CASE WHEN execution.status = :success THEN 1 ELSE 0 END) as successCount',
        'SUM(CASE WHEN execution.status = :failed THEN 1 ELSE 0 END) as failureCount',
        'AVG(execution.duration) as averageDuration',
        'MAX(execution.createdAt) as lastExecutionTime',
      ])
      .setParameter('success', TaskExecutionStatus.SUCCESS)
      .setParameter('failed', TaskExecutionStatus.FAILED)
      .groupBy('execution.taskName');

    if (taskName) {
      query.where('execution.taskName = :taskName', { taskName });
    }

    const rawResults = await query.getRawMany();

    const stats: TaskExecutionStatsDto[] = [];
    for (const raw of rawResults) {
      // 获取最后执行状态
      const lastExecution = await this.taskExecutionRepository.findOne({
        where: { taskName: raw.taskName },
        order: { createdAt: 'DESC' },
      });

      stats.push({
        taskName: raw.taskName,
        totalExecutions: parseInt(raw.totalExecutions),
        successCount: parseInt(raw.successCount),
        failureCount: parseInt(raw.failureCount),
        successRate: raw.totalExecutions > 0 ? (raw.successCount / raw.totalExecutions) * 100 : 0,
        averageDuration: raw.averageDuration ? Math.round(raw.averageDuration) : 0,
        lastExecutionTime: raw.lastExecutionTime,
        lastExecutionStatus: lastExecution?.status || TaskExecutionStatus.PENDING,
      });
    }

    return stats;
  }

  /**
   * 获取今日执行统计
   */
  async getTodayStats(): Promise<{
    totalExecutions: number;
    successCount: number;
    failureCount: number;
  }> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const result = await this.taskExecutionRepository
      .createQueryBuilder('execution')
      .select([
        'COUNT(*) as totalExecutions',
        'SUM(CASE WHEN execution.status = :success THEN 1 ELSE 0 END) as successCount',
        'SUM(CASE WHEN execution.status = :failed THEN 1 ELSE 0 END) as failureCount',
      ])
      .where('execution.createdAt >= :today AND execution.createdAt < :tomorrow')
      .setParameter('success', TaskExecutionStatus.SUCCESS)
      .setParameter('failed', TaskExecutionStatus.FAILED)
      .setParameter('today', today)
      .setParameter('tomorrow', tomorrow)
      .getRawOne();

    return {
      totalExecutions: parseInt(result.totalExecutions) || 0,
      successCount: parseInt(result.successCount) || 0,
      failureCount: parseInt(result.failureCount) || 0,
    };
  }

  /**
   * 获取最近执行记录
   */
  async getRecentExecutions(limit: number = 10): Promise<TaskExecution[]> {
    return await this.taskExecutionRepository.find({
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  /**
   * 清理过期的执行记录（更激进的清理策略）
   */
  async cleanupOldExecutions(daysToKeep: number = 7): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    // 分批删除，避免一次性删除太多数据影响性能
    const batchSize = 1000;
    let totalDeleted = 0;

    while (true) {
      // 先查询要删除的记录ID
      const recordsToDelete = await this.taskExecutionRepository.find({
        where: { createdAt: Between(new Date(0), cutoffDate) },
        select: ['id'],
        take: batchSize,
      });

      if (recordsToDelete.length === 0) {
        break; // 没有更多数据需要删除
      }

      const idsToDelete = recordsToDelete.map(record => record.id);
      const result = await this.taskExecutionRepository
        .createQueryBuilder()
        .delete()
        .where('id IN (:...ids)', { ids: idsToDelete })
        .execute();

      const deletedCount = result.affected || 0;
      totalDeleted += deletedCount;

      if (deletedCount < batchSize) {
        break;
      }

      // 短暂休息，避免对数据库造成过大压力
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.logger.log(`清理了 ${totalDeleted} 条过期的任务执行记录（保留${daysToKeep}天）`);
    return totalDeleted;
  }

  /**
   * 只保留必要的执行记录（每个任务只保留最近N条成功记录和所有失败记录）
   */
  async cleanupExecutionsByCount(keepSuccessCount: number = 10): Promise<number> {
    const taskNames = await this.taskExecutionRepository
      .createQueryBuilder('execution')
      .select('DISTINCT execution.taskName')
      .getRawMany();

    let totalDeleted = 0;

    for (const { execution_taskName: taskName } of taskNames) {
      // 保留最近的成功记录
      const successRecordsToKeep = await this.taskExecutionRepository.find({
        where: {
          taskName,
          status: TaskExecutionStatus.SUCCESS
        },
        order: { createdAt: 'DESC' },
        take: keepSuccessCount,
        select: ['id']
      });

      if (successRecordsToKeep.length > 0) {
        const keepIds = successRecordsToKeep.map(r => r.id);

        const result = await this.taskExecutionRepository
          .createQueryBuilder()
          .delete()
          .where('taskName = :taskName', { taskName })
          .andWhere('status = :status', { status: TaskExecutionStatus.SUCCESS })
          .andWhere('id NOT IN (:...keepIds)', { keepIds })
          .execute();

        totalDeleted += result.affected || 0;
      }
    }

    this.logger.log(`按数量清理了 ${totalDeleted} 条任务执行记录（每个任务保留${keepSuccessCount}条成功记录）`);
    return totalDeleted;
  }

  /**
   * 获取任务的最后执行记录
   */
  async getLastExecution(taskName: string): Promise<TaskExecution | null> {
    return await this.taskExecutionRepository.findOne({
      where: { taskName },
      order: { createdAt: 'DESC' },
    });
  }
}
