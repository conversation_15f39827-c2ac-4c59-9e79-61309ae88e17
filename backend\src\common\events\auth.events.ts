/**
 * 认证相关事件定义
 * 用于解耦守卫和业务逻辑，通过事件驱动的方式获取用户信息
 */

/**
 * 用户信息请求事件
 * 当守卫需要完整用户信息时触发
 */
export class UserInfoRequestEvent {
  constructor(
    public readonly userId: number,
    public readonly options: {
      includeRoles?: boolean;
      includePermissions?: boolean;
      fromCache?: boolean;
    } = {},
    public readonly callback: (user: any) => void
  ) {}
}

/**
 * API密钥验证请求事件
 * 当需要从数据库验证API密钥时触发
 */
export class ApiKeyValidationRequestEvent {
  constructor(
    public readonly apiKey: string,
    public readonly secretKey: string,
    public readonly clientIp: string,
    public readonly callback: (result: {
      isValid: boolean;
      apiKey?: any;
      user?: any;
      error?: string;
    }) => void
  ) {}
}

/**
 * 认证失败事件
 * 用于记录和处理认证失败
 */
export class AuthFailureEvent {
  constructor(
    public readonly data: {
      identifier: string;
      reason: string;
      authType: 'jwt' | 'api-key';
      clientIp?: string;
      userAgent?: string;
      timestamp: Date;
    }
  ) {}
}

/**
 * 认证成功事件
 * 用于记录和处理认证成功
 */
export class AuthSuccessEvent {
  constructor(
    public readonly data: {
      userId: number;
      authType: 'jwt' | 'api-key';
      clientIp?: string;
      userAgent?: string;
      timestamp: Date;
      apiKeyId?: number;
    }
  ) {}
}

/**
 * API密钥使用更新事件
 * 用于异步更新API密钥的使用记录
 */
export class ApiKeyUsageUpdateEvent {
  constructor(
    public readonly data: {
      apiKeyId: number;
      clientIp: string;
      timestamp: Date;
    }
  ) {}
}

/**
 * 用户角色权限缓存更新事件
 * 用于更新用户的角色和权限缓存
 */
export class UserRolePermissionUpdateEvent {
  constructor(
    public readonly userId: number,
    public readonly action: 'refresh' | 'clear'
  ) {}
}