# 微信登录功能说明

## 功能概述

本项目已集成微信快捷注册和登录功能，支持以下两种使用场景：

1. **微信浏览器内登录**：在微信内置浏览器中，直接跳转到微信授权页面
2. **普通浏览器登录**：在其他浏览器中，显示微信二维码供用户扫码登录

## 配置要求

### 1. 环境变量配置

在 `.env` 文件中配置微信相关参数：

```env
# 微信公众号/小程序 AppID（用于微信浏览器内授权）
VITE_WECHAT_APP_ID=your_wechat_app_id

# 微信开放平台 AppID（用于网站应用扫码登录）
VITE_WECHAT_WEB_APP_ID=your_wechat_web_app_id
```

### 2. 微信开发者平台配置

#### 微信公众号配置
1. 登录 [微信公众平台](https://mp.weixin.qq.com/)
2. 在「开发」-「接口权限」-「网页服务」-「网页帐号」中设置授权回调域名
3. 回调域名设置为：`your-domain.com`（不包含协议和路径）

#### 微信开放平台配置
1. 登录 [微信开放平台](https://open.weixin.qq.com/)
2. 创建网站应用
3. 设置授权回调域名为：`your-domain.com`

## 后端API接口

前端微信登录功能需要后端提供以下API接口：

### 1. 生成微信登录二维码

```http
POST /api/auth/wechat/qrcode
Content-Type: application/json

{
  "type": "login" | "register"
}
```

**响应：**
```json
{
  "success": true,
  "qrCodeUrl": "https://api.qrserver.com/v1/create-qr-code/?data=...",
  "ticket": "unique_ticket_id",
  "expiresIn": 300
}
```

### 2. 检查微信授权状态

```http
GET /api/auth/wechat/status?ticket={ticket}&type={type}
```

**响应：**
```json
{
  "success": true,
  "status": "pending" | "completed" | "expired",
  "token": "jwt_token",
  "userInfo": {
    "id": "user_id",
    "username": "username",
    "email": "<EMAIL>",
    "avatar": "avatar_url"
  }
}
```

### 3. 处理微信授权回调

```http
POST /api/auth/wechat/callback
Content-Type: application/json

{
  "code": "authorization_code",
  "state": "random_state",
  "type": "login" | "register"
}
```

**响应：**
```json
{
  "success": true,
  "token": "jwt_token",
  "userInfo": {
    "id": "user_id",
    "username": "username",
    "email": "<EMAIL>",
    "avatar": "avatar_url"
  }
}
```

## 使用流程

### 微信浏览器内登录流程

1. 用户在微信内置浏览器中访问登录页面
2. 点击「微信登录」按钮
3. 系统检测到微信环境，直接跳转到微信授权页面
4. 用户确认授权后，微信跳转到回调页面
5. 回调页面处理授权结果，完成登录

### 普通浏览器扫码登录流程

1. 用户在普通浏览器中访问登录页面
2. 点击「微信登录」按钮
3. 系统调用后端API生成微信登录二维码
4. 显示二维码弹窗，用户使用微信扫码
5. 系统轮询检查授权状态
6. 授权完成后，自动关闭弹窗并完成登录

## 安全考虑

1. **State参数验证**：使用随机生成的state参数防止CSRF攻击
2. **授权码时效性**：微信授权码有效期为10分钟
3. **二维码时效性**：登录二维码有效期为5分钟
4. **HTTPS要求**：生产环境必须使用HTTPS协议

## 错误处理

系统会处理以下常见错误情况：

- 授权被用户拒绝
- 授权码过期
- 网络连接失败
- 后端API错误
- 参数验证失败

所有错误都会通过友好的提示信息告知用户，并提供重试或返回登录页面的选项。

## 开发调试

### 本地开发配置

1. 使用内网穿透工具（如ngrok）将本地服务暴露到公网
2. 在微信开发者平台配置回调域名为穿透后的域名
3. 更新 `.env` 文件中的相关配置

### 测试建议

1. 在微信开发者工具中测试微信浏览器内登录
2. 在普通浏览器中测试扫码登录
3. 测试各种错误情况的处理
4. 验证登录状态的持久化

## 注意事项

1. 微信登录功能需要企业认证的微信公众号或开放平台账号
2. 个人开发者可以使用微信测试号进行开发调试
3. 生产环境部署时，确保所有域名配置正确
4. 定期检查微信平台的政策更新，及时调整实现方案