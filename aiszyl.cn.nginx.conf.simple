server {
    server_name aiszyl.cn www.aiszyl.cn;

    # 访问日志
    access_log /var/log/nginx/aiszyl.cn.access.log;
    error_log /var/log/nginx/aiszyl.cn.error.log;

    # 所有请求直接代理到前端服务
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
        
        # 关闭缓存
        proxy_no_cache 1;
        proxy_cache_bypass 1;
        
        # 增加超时时间
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/aiszyl.cn/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/aiszyl.cn/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
}

server {
    if ($host = www.aiszyl.cn) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    if ($host = aiszyl.cn) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    listen 80;
    server_name aiszyl.cn www.aiszyl.cn;
    return 404; # managed by Certbot
} 