import { IsString, IsNotEmpty, IsNumberString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eng<PERSON>, IsOptional, IsArray, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 签名认证DTO
 */
export class SignatureAuthDto {
  @ApiProperty({
    description: 'API密钥',
    example: 'ak-abcdef123456',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(5)
  apiKey: string;

  @ApiProperty({
    description: '请求时间戳（Unix时间戳，秒）',
    example: '1634567890',
  })
  @IsString()
  @IsNotEmpty()
  @IsNumberString()
  timestamp: string;

  @ApiProperty({
    description: '随机字符串（防重放）',
    example: 'abc123def456',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  @MaxLength(36)
  nonce: string;

  @ApiProperty({
    description: '请求签名',
    example: 'abcdef123456abcdef123456abcdef123456',
  })
  @IsString()
  @IsNotEmpty()
  signature: string;
}

/**
 * JWT认证DTO
 */
export class JwtAuthDto {
  @ApiProperty({
    description: 'JWT令牌',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString()
  @IsNotEmpty()
  token: string;
}

/**
 * API密钥DTO
 */
export class ApiKeyDto {
  @ApiProperty({
    description: 'API密钥ID',
    example: '1',
  })
  id: number | string;

  @ApiProperty({
    description: '用户ID',
    example: '1',
  })
  userId: number | string;

  @ApiProperty({
    description: '服务ID',
    example: '1',
  })
  @IsOptional()
  serviceId?: number | string;

  @ApiProperty({
    description: 'API密钥名称',
    example: '测试密钥',
  })
  name: string;

  @ApiProperty({
    description: 'API密钥状态',
    example: 'active',
    enum: ['active', 'inactive', 'expired', 'revoked'],
  })
  keyStatus: string;

  @ApiProperty({
    description: 'API密钥类型',
    example: 'service',
    enum: ['service', 'user', 'admin'],
  })
  keyType: string;

  @ApiProperty({
    description: '权限列表',
    example: ['read', 'write'],
    type: [String],
  })
  @IsArray()
  permissions: string[];

  @ApiProperty({
    description: '过期时间',
    example: '2023-12-31T23:59:59.999Z',
  })
  @IsOptional()
  expiresAt?: Date | string;

  @ApiProperty({
    description: '最后使用时间',
    example: '2023-01-01T12:00:00.000Z',
  })
  @IsOptional()
  lastUsedAt?: Date | string;

  @ApiProperty({
    description: '创建时间',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date | string;

  @ApiProperty({
    description: '更新时间',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date | string;
}

/**
 * 认证用户DTO
 */
export class AuthUserDto {
  @ApiProperty({
    description: '用户ID',
    example: '1',
  })
  id: number | string;

  @ApiProperty({
    description: '用户名',
    example: 'admin',
  })
  @IsOptional()
  username?: string;

  @ApiProperty({
    description: '电子邮箱',
    example: '<EMAIL>',
  })
  @IsOptional()
  email?: string;

  @ApiProperty({
    description: '角色列表',
    example: ['admin', 'user'],
    type: [String],
  })
  @IsArray()
  @IsOptional()
  roles?: string[];

  @ApiProperty({
    description: '权限列表',
    example: ['read:users', 'write:users'],
    type: [String],
  })
  @IsArray()
  @IsOptional()
  permissions?: string[];

  @ApiProperty({
    description: '是否为管理员',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  isAdmin?: boolean;
}

/**
 * 认证结果DTO
 */
export class AuthResultDto {
  @ApiProperty({
    description: '认证是否有效',
    example: true,
  })
  @IsBoolean()
  isValid: boolean;

  @ApiProperty({
    description: '认证类型',
    example: 'jwt',
    enum: ['jwt', 'api-key', 'api-key-signature'],
  })
  @IsOptional()
  authType?: string;

  @ApiProperty({
    description: '认证用户信息',
    type: AuthUserDto,
  })
  @IsOptional()
  user?: AuthUserDto;

  @ApiProperty({
    description: 'API密钥信息',
    type: ApiKeyDto,
  })
  @IsOptional()
  apiKey?: ApiKeyDto;

  @ApiProperty({
    description: '错误信息',
    example: '认证失败',
  })
  @IsString()
  @IsOptional()
  error?: string;
} 