import { Injectable, Logger } from '@nestjs/common';
import { RegisterDto } from '../dto/register.dto';
import { LoginDto } from '../dto/login.dto';
import { ResetPasswordDto } from '../dto/reset-password.dto';
import { ChangePasswordDto } from '../dto/change-password.dto';
import { RefreshTokenDto } from '../dto/refresh-token.dto';
import { SendEmailCodeDto } from '../dto/captcha/send-email-code.dto';
import { SendSmsCodeDto } from '../dto/captcha/send-sms-code.dto';
import { VerifyCodeDto } from '../dto/verify-code.dto';
import { TokenResponseDto, RegisterResponseDto, LoginResponseDto, VerificationResponseDto } from '../dto/auth-response.dto';
import { AuthCoreService } from './auth-core.service';
import { VerificationCodeService } from './verification-code.service';

/**
 * 认证服务门面
 * 提供向后兼容的API接口，内部委托给专门的服务处理
 * 遵循门面模式，简化客户端调用
 */
@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly authCore: AuthCoreService,
    private readonly verificationCode: VerificationCodeService,
  ) {}

  /**
   * 用户注册
   * @param registerDto 注册参数
   * @returns 注册结果
   */
  async register(registerDto: RegisterDto): Promise<RegisterResponseDto> {
    return this.authCore.register(registerDto);
  }

  /**
   * 用户登录
   * @param loginDto 登录参数
   * @param request HTTP请求对象（用于获取客户端IP）
   * @returns 登录结果
   */
  async login(loginDto: LoginDto, request?: any): Promise<LoginResponseDto> {
    return this.authCore.login(loginDto, request);
  }

  /**
   * 刷新令牌
   * @param refreshTokenDto 刷新令牌参数
   * @returns 新的令牌信息
   */
  async refreshToken(refreshTokenDto: RefreshTokenDto): Promise<TokenResponseDto> {
    return this.authCore.refreshToken(refreshTokenDto);
  }

  /**
   * 用户登出
   * @param accessToken 访问令牌
   * @param refreshToken 刷新令牌
   * @returns 登出结果
   */
  async logout(accessToken: string, refreshToken: string): Promise<{ message: string }> {
    return this.authCore.logout(accessToken, refreshToken);
  }

  /**
   * 重置密码
   * @param resetPasswordDto 重置密码参数
   * @returns 重置结果
   */
  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<{ message: string }> {
    return this.authCore.resetPassword(resetPasswordDto);
  }

  /**
   * 修改密码
   * @param userId 用户ID
   * @param changePasswordDto 修改密码参数
   * @returns 修改结果
   */
  async changePassword(userId: number, changePasswordDto: ChangePasswordDto): Promise<{ message: string }> {
    return this.authCore.changePassword(userId, changePasswordDto);
  }

  /**
   * 发送邮箱验证码
   * @param dto 发送邮箱验证码参数
   * @returns 发送结果
   */
  async sendEmailCode(dto: SendEmailCodeDto): Promise<VerificationResponseDto> {
    await this.verificationCode.sendEmailCode(dto.email, dto.type, dto.securityVerification);
    return {
      email: this.maskEmail(dto.email),
      expireIn: 300, // 默认5分钟
    };
  }

  /**
   * 发送短信验证码
   * @param dto 发送短信验证码参数
   * @returns 发送结果
   */
  async sendSmsCode(dto: SendSmsCodeDto): Promise<VerificationResponseDto> {
    await this.verificationCode.sendSmsCode(dto.phone, dto.type, dto.securityVerification);
    return {
      phone: this.maskPhone(dto.phone),
      expireIn: 300, // 默认5分钟
    };
  }

  /**
   * 验证验证码
   * @param dto 验证码验证参数
   * @returns 验证结果
   */
  async verifyCode(dto: VerifyCodeDto): Promise<{ valid: boolean }> {
    const identifier = dto.email || dto.phone!;
    const isEmail = !!dto.email;
    const isValid = await this.verificationCode.verifyCode(identifier, dto.code, dto.type, isEmail);
    return { valid: isValid };
  }

  /**
   * 验证滑动拼图验证码
   * @param moveDistance 移动距离
   * @param duration 移动耗时
   * @param securityData 安全数据
   * @returns 验证结果
   */
  async verifySlideVerify(
    moveDistance: number,
    duration: number,
    securityData?: any
  ): Promise<{
    success: boolean;
    message: string;
    riskScore?: number;
  }> {
    // 这里需要注入CaptchaService，暂时使用简单的验证逻辑
    // 基本的滑动验证逻辑
    const isValidDistance = moveDistance > 50 && moveDistance < 300;
    const isValidDuration = duration > 500 && duration < 10000;

    if (isValidDistance && isValidDuration) {
      return {
        success: true,
        message: '验证成功',
        riskScore: 0.1
      };
    } else {
      return {
        success: false,
        message: '验证失败',
        riskScore: 0.9
      };
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 掩码邮箱地址
   */
  private maskEmail(email: string): string {
    const [username, domain] = email.split('@');
    const maskedUsername = username.length <= 2
      ? '*'.repeat(username.length)
      : username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);
    return `${maskedUsername}@${domain}`;
  }

  /**
   * 掩码手机号
   */
  private maskPhone(phone: string): string {
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }



  // ==================== 兼容性方法 ====================
  // 以下方法保留是为了向后兼容，建议新代码直接使用专门的服务

  /**
   * @deprecated 请使用 VerificationCodeService.sendEmailCode
   */
  async sendVerificationCode(email: string, type: string): Promise<VerificationResponseDto> {
    this.logger.warn('sendVerificationCode方法已废弃，请使用sendEmailCode');
    await this.verificationCode.sendEmailCode(email, type as any);
    return {
      email: this.maskEmail(email),
      expireIn: 300,
    };
  }

  /**
   * @deprecated 请使用 VerificationCodeService.sendSmsCode
   */
  async sendSmsVerificationCode(phone: string, type: string): Promise<VerificationResponseDto> {
    this.logger.warn('sendSmsVerificationCode方法已废弃，请使用sendSmsCode');
    await this.verificationCode.sendSmsCode(phone, type as any);
    return {
      phone: this.maskPhone(phone),
      expireIn: 300,
    };
  }

  /**
   * @deprecated 请使用 VerificationCodeService.verifyCode
   */
  async validateVerificationCode(identifier: string, code: string, type: string, isEmail: boolean = true): Promise<boolean> {
    this.logger.warn('validateVerificationCode方法已废弃，请使用verifyCode');
    return this.verificationCode.verifyCode(identifier, code, type as any, isEmail);
  }

  /**
   * @deprecated 请使用 TokenManagementService.generateTokens
   */
  async generateTokens(_user: any): Promise<TokenResponseDto> {
    this.logger.warn('generateTokens方法已废弃，请使用TokenManagementService');
    // 为了兼容性，这里返回一个基本的令牌结构
    return {
      token: '',
      refreshToken: '',
      expiresIn: 0,
    };
  }

  /**
   * @deprecated 请使用 PasswordPolicyService.validatePasswordComplexity
   */
  validatePasswordPolicy(_password: string): { isValid: boolean; errors: string[] } {
    this.logger.warn('validatePasswordPolicy方法已废弃，请使用PasswordPolicyService');
    return { isValid: true, errors: [] };
  }

  // ==================== 健康检查方法 ====================

  /**
   * 检查认证服务健康状态
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    services: Record<string, boolean>;
    timestamp: string;
  }> {
    try {
      // 检查各个服务的健康状态
      const services = {
        authCore: !!this.authCore,
        verificationCode: !!this.verificationCode,
      };

      const allHealthy = Object.values(services).every(status => status);

      return {
        status: allHealthy ? 'healthy' : 'unhealthy',
        services,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('健康检查失败', error);
      return {
        status: 'unhealthy',
        services: {},
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * 获取认证服务统计信息
   */
  async getStats(): Promise<{
    totalRegistrations: number;
    totalLogins: number;
    activeUsers: number;
    timestamp: string;
  }> {
    // 这里可以添加统计逻辑
    return {
      totalRegistrations: 0,
      totalLogins: 0,
      activeUsers: 0,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 清理过期数据
   */
  async cleanup(): Promise<{ message: string; cleaned: number }> {
    try {
      // 委托给验证码服务清理过期验证码
      await this.verificationCode.cleanupExpiredCodes();
      
      this.logger.log('认证服务数据清理完成');
      return {
        message: '数据清理完成',
        cleaned: 0, // 实际清理的数量
      };
    } catch (error) {
      this.logger.error('数据清理失败', error);
      throw error;
    }
  }
}
