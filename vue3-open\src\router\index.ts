import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: { title: '首页' },
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/auth/LoginView.vue'),
      meta: { title: '登录', requiresGuest: true },
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/auth/RegisterView.vue'),
      meta: { title: '注册', requiresGuest: true },
    },
    {
      path: '/forgot-password',
      name: 'forgot-password',
      component: () => import('../views/auth/ForgotPasswordView.vue'),
      meta: { title: '找回密码', requiresGuest: true },
    },
    {
      path: '/auth/wechat/callback',
      name: 'wechat-callback',
      component: () => import('../views/auth/WechatCallback.vue'),
      meta: { title: '微信授权回调' },
    },
    {
      path: '/auth/oauth/callback',
      name: 'oauth-callback',
      component: () => import('../views/auth/OAuthCallbackView.vue'),
      meta: { title: '第三方授权回调' },
    },
    {
      path: '/test/response',
      name: 'response-test',
      component: () => import('../views/test/ResponseTestView.vue'),
      meta: { title: '响应格式测试' },
    },
    {
      path: '/services',
      name: 'services',
      component: () => import('../views/services/ServiceListView.vue'),
      meta: { title: '服务列表' },
    },
    {
      path: '/services/:code',
      name: 'service-detail',
      component: () => import('../views/services/ServiceDetailView.vue'),
      meta: { title: '服务详情' },
    },
    {
      path: '/docs',
      name: 'docs',
      component: () => import('../views/DocsView.vue'),
      meta: { title: '开发文档' },
    },
    {
      path: '/faq',
      name: 'faq',
      component: () => import('../views/FaqView.vue'),
      meta: { title: '常见问题' },
    },
    {
      path: '/console',
      name: 'console',
      component: () => import('../views/console/ConsoleView.vue'),
      meta: { title: '控制台', requiresAuth: true },
      children: [
        {
          path: 'dashboard',
          name: 'dashboard',
          component: () => import('../views/console/DashboardView.vue'),
          meta: { title: '控制台首页' },
        },
        {
          path: 'api-keys',
          name: 'console-api-keys',
          component: () => import('../views/console/ApiKeysView.vue'),
          meta: { title: 'API密钥管理' },
        },
        {
          path: 'usage',
          name: 'console-usage',
          component: () => import('../views/console/UsageView.vue'),
          meta: { title: '使用统计' },
        },
        {
          path: 'profile',
          name: 'Profile',
          component: () => import('@/views/console/ProfileView.vue'),
          meta: { title: '个人资料' },
        },
        // {
        //   path: 'billing',
        //   name: 'console-billing',
        //   component: () => import('../views/console/BillingView.vue'),
        //   meta: { title: '账单管理' },
        // },
        // {
        //   path: 'monitoring',
        //   name: 'console-monitoring',
        //   component: () => import('../views/console/MonitoringView.vue'),
        //   meta: { title: '系统监控' },
        // },
        {
          path: 'api-test',
          name: 'console-api-test',
          component: () => import('../views/console/ApiTestView.vue'),
          meta: { title: 'API测试工具' },
        },
        // {
        //   path: 'orders',
        //   name: 'Orders',
        //   component: () => import('@/views/console/OrdersView.vue'),
        //   meta: { title: '订单管理' },
        // },
        // {
        //   path: 'packages',
        //   name: 'UserPackages',
        //   component: () => import('@/views/console/UserPackagesView.vue'),
        //   meta: { title: '我的套餐包' },
        // },
        // {
        //   path: 'service-management',
        //   name: 'ServiceManagement',
        //   component: () => import('@/views/console/ServiceManagementView.vue'),
        //   meta: { title: '服务管理' },
        // },
      ],
    },
    {
      path: '/playground',
      name: 'playground',
      component: () => import('../views/console/ApiTestView.vue'),
      meta: { title: 'API测试' },
    },
    {
      path: '/about',
      name: 'About',
      component: () => import('@/views/AboutView.vue'),
      meta: { title: '关于我们' },
    },
    {
      path: '/packages',
      name: 'Packages',
      component: () => import('@/views/packages/PackageListView.vue'),
      meta: { title: '套餐包' },
    },
    {
      path: '/packages/:id',
      name: 'PackageDetail',
      component: () => import('@/views/packages/PackageDetailView.vue'),
      meta: { title: '套餐包详情' },
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('../views/NotFoundView.vue'),
      meta: { title: '页面不存在' },
    },
  ],
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - ${import.meta.env.VITE_APP_TITLE}`
  }

  // 如果有token但没有用户信息，尝试获取用户信息
  if (userStore.token && !userStore.userInfo) {
    try {
      console.log('路由守卫：检测到token但无用户信息，尝试获取用户信息')
      await userStore.checkAndFetchUserInfo()
    } catch (error) {
      console.error('路由守卫：获取用户信息失败，可能token已过期:', error)
      // 获取用户信息失败，清除token并继续路由导航
    }
  }

  // 检查是否需要登录
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    console.log('路由守卫：需要登录，跳转到登录页')
    next({ name: 'login', query: { redirect: to.fullPath } })
    return
  }

  // 检查是否需要游客状态（已登录用户不能访问登录/注册页）
  if (to.meta.requiresGuest && userStore.isLoggedIn) {
    console.log('路由守卫：已登录用户访问游客页面，跳转到控制台')
    next({ name: 'dashboard' })
    return
  }

  next()
})

export default router
