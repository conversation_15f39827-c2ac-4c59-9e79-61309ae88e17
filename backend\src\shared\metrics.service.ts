import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { RedisService } from './redis.service';
import { ConfigService } from '@nestjs/config';

/**
 * 性能指标收集服务
 * 使用缓冲区批量处理指标数据，减轻Redis压力
 */
@Injectable()
export class MetricsService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(MetricsService.name);
  private metricsBuffer: Array<{ key: string; value: string | number; type: 'lpush' | 'hincrby' | 'setex' }> = [];
  private slowQueriesBuffer: Array<any> = [];
  private flushInterval: NodeJS.Timeout;
  private readonly bufferSize: number;
  private readonly flushIntervalMs: number;

  constructor(
    private readonly redisService: RedisService,
    private readonly configService: ConfigService,
  ) {
    // 从配置中读取缓冲区大小和刷新间隔，或使用默认值
    this.bufferSize = this.configService.get<number>('metrics.bufferSize', 100);
    this.flushIntervalMs = this.configService.get<number>('metrics.flushInterval', 5000);
  }

  onModuleInit() {
    // 启动定时刷新任务
    this.flushInterval = setInterval(() => this.flushBuffers(), this.flushIntervalMs);
    this.logger.log(`指标收集服务已启动，缓冲区大小: ${this.bufferSize}, 刷新间隔: ${this.flushIntervalMs}ms`);
  }

  onModuleDestroy() {
    // 模块销毁前确保所有数据都已写入
    clearInterval(this.flushInterval);
    this.flushBuffers();
  }

  /**
   * 记录响应时间
   */
  recordResponseTime(duration: number, dateKey: string): void {
    const metricsKey = `performance_metrics:${dateKey}`;
    
    // 添加到缓冲区
    this.metricsBuffer.push({
      key: `${metricsKey}:response_times`,
      value: duration.toString(),
      type: 'lpush'
    });
    
    this.metricsBuffer.push({
      key: metricsKey,
      value: duration,
      type: 'hincrby'
    });
    
    this.metricsBuffer.push({
      key: metricsKey,
      value: 1,
      type: 'hincrby'
    });
    
    // 如果是慢查询（超过1秒），记录到慢查询缓冲区
    if (duration > 1000) {
      this.recordSlowQuery({
        duration,
        timestamp: new Date().toISOString(),
      });
    }
    
    // 如果缓冲区已满，立即刷新
    if (this.metricsBuffer.length >= this.bufferSize) {
      this.flushBuffers();
    }
  }

  /**
   * 记录请求统计
   */
  recordRequestStat(dateKey: string, authType?: string): void {
    const statsKey = `api_stats:${dateKey}`;
    
    // 添加总请求计数
    this.metricsBuffer.push({
      key: statsKey,
      value: 1,
      type: 'hincrby'
    });
    
    // 根据认证类型添加分类计数
    if (authType) {
      this.metricsBuffer.push({
        key: statsKey,
        value: 1,
        type: 'hincrby'
      });
    }
  }

  /**
   * 记录API分类统计
   * @param dateKey 日期键
   * @param apiCategory API分类
   * @param serviceCode 服务代码
   */
  recordApiCategoryStat(dateKey: string, apiCategory: string, serviceCode?: string): void {
    const categoryKey = `api_category_stats:${dateKey}`;
    
    // 记录API分类统计
    this.metricsBuffer.push({
      key: categoryKey,
      value: 1,
      type: 'hincrby'
    });
    
    // 如果提供了服务代码，记录服务级别统计
    if (serviceCode) {
      const serviceKey = `service_stats:${dateKey}:${serviceCode}`;
      this.metricsBuffer.push({
        key: serviceKey,
        value: 1,
        type: 'hincrby'
      });
    }
  }

  /**
   * 记录慢查询
   */
  recordSlowQuery(data: any): void {
    this.slowQueriesBuffer.push(data);
    
    // 如果慢查询缓冲区已满，立即刷新
    if (this.slowQueriesBuffer.length >= Math.min(20, this.bufferSize / 5)) {
      this.flushSlowQueries();
    }
  }

  /**
   * 刷新所有缓冲区
   */
  private async flushBuffers(): Promise<void> {
    await Promise.all([
      this.flushMetricsBuffer(),
      this.flushSlowQueries()
    ]);
  }

  /**
   * 刷新指标缓冲区
   */
  private async flushMetricsBuffer(): Promise<void> {
    if (this.metricsBuffer.length === 0) return;
    
    const bufferCopy = [...this.metricsBuffer];
    this.metricsBuffer = [];
    
    try {
      const pipeline = this.redisService.pipeline();
      
      // 按类型分组处理
      const lpushOps = bufferCopy.filter(item => item.type === 'lpush');
      const hincrbyOps = bufferCopy.filter(item => item.type === 'hincrby');
      const setexOps = bufferCopy.filter(item => item.type === 'setex');
      
      // 处理lpush操作
      for (const op of lpushOps) {
        pipeline.lpush(op.key, op.value as string);
      }
      
      // 对相同键的hincrby操作进行合并
      const hincrbyMap = new Map<string, Map<string, number>>();
      for (const op of hincrbyOps) {
        const [key, field] = op.key.split(':');
        if (!hincrbyMap.has(key)) {
          hincrbyMap.set(key, new Map<string, number>());
        }
        const fieldMap = hincrbyMap.get(key)!; // 使用非空断言，因为我们刚刚确保了它存在
        fieldMap.set(field, (fieldMap.get(field) || 0) + (op.value as number));
      }
      
      // 添加合并后的hincrby操作
      for (const [key, fieldMap] of hincrbyMap.entries()) {
        for (const [field, value] of fieldMap.entries()) {
          pipeline.hincrby(key, field, value);
        }
      }
      
      // 处理setex操作
      for (const op of setexOps) {
        const [key, ttl] = op.key.split(':');
        pipeline.setex(key, parseInt(ttl), op.value as string);
      }
      
      // 设置过期时间（7天）
      const keys = new Set(bufferCopy.map(item => item.key.split(':')[0]));
      for (const key of keys) {
        if (key.startsWith('performance_metrics') || key.startsWith('api_stats') || key.startsWith('api_category_stats')) {
          pipeline.expire(key, 86400 * 7);
        }
      }
      
      // 执行批量操作
      await pipeline.exec();
      
      // 对response_times列表进行修剪，防止过长
      for (const op of lpushOps) {
        if (op.key.endsWith(':response_times')) {
          await this.redisService.ltrim(op.key, 0, 9999);
        }
      }
      
      this.logger.debug(`成功刷新指标缓冲区，处理了 ${bufferCopy.length} 条记录`);
    } catch (error) {
      this.logger.error(`刷新指标缓冲区失败: ${error.message}`, error.stack);
      // 重要数据，放回缓冲区重试
      this.metricsBuffer = [...this.metricsBuffer, ...bufferCopy];
    }
  }

  /**
   * 刷新慢查询缓冲区
   */
  private async flushSlowQueries(): Promise<void> {
    if (this.slowQueriesBuffer.length === 0) return;
    
    const bufferCopy = [...this.slowQueriesBuffer];
    this.slowQueriesBuffer = [];
    
    try {
      const pipeline = this.redisService.pipeline();
      
      // 批量添加慢查询记录
      for (const data of bufferCopy) {
        pipeline.lpush('slow_queries', JSON.stringify(data));
      }
      
      // 执行批量操作
      await pipeline.exec();
      
      // 修剪列表长度
      await this.redisService.ltrim('slow_queries', 0, 999);
      
      this.logger.debug(`成功刷新慢查询缓冲区，处理了 ${bufferCopy.length} 条记录`);
    } catch (error) {
      this.logger.error(`刷新慢查询缓冲区失败: ${error.message}`, error.stack);
      // 重要数据，放回缓冲区重试
      this.slowQueriesBuffer = [...this.slowQueriesBuffer, ...bufferCopy];
    }
  }
} 