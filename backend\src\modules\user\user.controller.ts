
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  BadRequestException,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserResponseDto, UserListResponseDto } from './dto/user-response.dto';
import { PersonalVerificationDto, EnterpriseVerificationDto, ReviewVerificationDto } from './dto/user-verification.dto';
import { UserStatus, UserType, UserVerificationStatus } from './entities/user.entity';
import { UnifiedAuthGuard } from '@/common/guards/unified-auth.guard';
import { AuthPermission } from '@/common/decorators/auth-permission.decorator';
import { ApiResult } from '@/common/decorators/api-result.decorator';

@ApiTags('用户管理')
@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  @ApiOperation({ summary: '创建用户' })
  @ApiResponse({ status: 201, type: UserResponseDto })
  @ApiResult()
  async create(@Body() createUserDto: CreateUserDto): Promise<UserResponseDto> {
    return await this.userService.create(createUserDto);
  }

  @Get()
  @AuthPermission('user:list')
  @ApiOperation({ summary: '获取用户列表' })
  @ApiQuery({ name: 'page', required: false, description: '页码', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: '每页条数', type: Number })
  @ApiQuery({ name: 'username', required: false, description: '用户名', type: String })
  @ApiQuery({ name: 'email', required: false, description: '邮箱', type: String })
  @ApiQuery({ name: 'phone', required: false, description: '手机号', type: String })
  @ApiQuery({ name: 'userType', required: false, description: '用户类型', enum: UserType })
  @ApiQuery({ name: 'verificationStatus', required: false, description: '认证状态', enum: UserVerificationStatus })
  @ApiQuery({ name: 'userStatus', required: false, description: '账户状态', enum: UserStatus })
  @ApiResponse({ status: 200, type: UserListResponseDto })
  @ApiResult()
  async findAll(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('username') username?: string,
    @Query('email') email?: string,
    @Query('phone') phone?: string,
    @Query('userType') userType?: UserType,
    @Query('verificationStatus') verificationStatus?: UserVerificationStatus,
    @Query('userStatus') userStatus?: UserStatus,
  ): Promise<UserListResponseDto> {
    return await this.userService.findAll(page, limit, {
      username,
      email,
      phone,
      userType,
      verificationStatus,
      userStatus,
    });
  }

  @Get(':id')
  @AuthPermission('user:detail')
  @ApiOperation({ summary: '获取用户详情' })
  @ApiParam({ name: 'id', description: '用户ID', type: Number })
  @ApiResponse({ status: 200, type: UserResponseDto })
  @ApiResult()
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<UserResponseDto> {
    return await this.userService.findOne(id);
  }

  @Patch(':id')
  @AuthPermission('user:update')
  @ApiOperation({ summary: '更新用户信息' })
  @ApiParam({ name: 'id', description: '用户ID', type: Number })
  @ApiResponse({ status: 200, type: UserResponseDto })
  @ApiResult()
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<UserResponseDto> {
    return await this.userService.update(id, updateUserDto);
  }

  @Patch(':id/status')
  @AuthPermission('user:update-status')
  @ApiOperation({ summary: '更新用户状态' })
  @ApiParam({ name: 'id', description: '用户ID', type: Number })
  @ApiResponse({ status: 200, type: UserResponseDto })
  @ApiResult()
  async updateStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body('status') status: UserStatus,
  ): Promise<UserResponseDto> {
    return await this.userService.updateStatus(id, status);
  }

  @Delete(':id')
  @AuthPermission('user:delete')
  @ApiOperation({ summary: '删除用户' })
  @ApiParam({ name: 'id', description: '用户ID', type: Number })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResult()
  async remove(@Param('id', ParseIntPipe) id: number): Promise<{ success: boolean; message: string }> {
    return await this.userService.remove(id);
  }

  @Post(':id/verification/personal')
  @ApiOperation({ summary: '提交个人实名认证' })
  @ApiParam({ name: 'id', description: '用户ID', type: Number })
  @ApiResponse({ status: 200, description: '提交成功' })
  @ApiResult()
  async submitPersonalVerification(
    @Param('id', ParseIntPipe) id: number,
    @Body() personalVerificationDto: PersonalVerificationDto,
    @Request() req,
  ): Promise<{ success: boolean; message: string; data: any }> {
    // 检查用户是否有权限提交
    if (req.user && req.user.id !== id && req.user.role !== 'admin') {
      throw new BadRequestException('您没有权限为其他用户提交实名认证');
    }
    
    return await this.userService.submitPersonalVerification(id, personalVerificationDto);
  }

  @Post(':id/verification/enterprise')
  @ApiOperation({ summary: '提交企业实名认证' })
  @ApiParam({ name: 'id', description: '用户ID', type: Number })
  @ApiResponse({ status: 200, description: '提交成功' })
  @ApiResult()
  async submitEnterpriseVerification(
    @Param('id', ParseIntPipe) id: number,
    @Body() enterpriseVerificationDto: EnterpriseVerificationDto,
    @Request() req,
  ): Promise<{ success: boolean; message: string; data: any }> {
    // 检查用户是否有权限提交
    if (req.user && req.user.id !== id && req.user.role !== 'admin') {
      throw new BadRequestException('您没有权限为其他用户提交企业认证');
    }
    
    return await this.userService.submitEnterpriseVerification(id, enterpriseVerificationDto);
  }

  @Post('verification/:id/review')
  @AuthPermission('user:verification-review')
  @ApiOperation({ summary: '审核实名认证' })
  @ApiParam({ name: 'id', description: '认证记录ID', type: Number })
  @ApiResponse({ status: 200, description: '审核成功' })
  @ApiResult()
  async reviewVerification(
    @Param('id', ParseIntPipe) id: number,
    @Body() reviewDto: ReviewVerificationDto & { verificationType: 'personal' | 'enterprise' },
  ): Promise<{ success: boolean; message: string }> {
    return await this.userService.reviewVerification(
      id,
      reviewDto.verificationType,
      {
        status: reviewDto.status,
        remark: reviewDto.remark,
        reviewerId: reviewDto.reviewerId,
      },
    );
  }

  @Get(':id/verification')
  @ApiOperation({ summary: '获取用户认证信息' })
  @ApiParam({ name: 'id', description: '用户ID', type: Number })
  @ApiResponse({ status: 200, description: '用户认证信息' })
  @ApiResult()
  async getVerificationInfo(
    @Param('id', ParseIntPipe) id: number,
    @Request() req,
  ): Promise<any> {
    // 检查用户是否有权限获取
    if (req.user && req.user.id !== id && req.user.role !== 'admin') {
      throw new BadRequestException('您没有权限查看其他用户的认证信息');
    }
    
    return await this.userService.getVerificationInfo(id);
  }

  @Post('daily-quota/reset')
  @AuthPermission('user:quota-reset')
  @ApiOperation({ summary: '重置每日免费额度（定时任务接口）' })
  @ApiResponse({ status: 200, description: '重置成功' })
  @ApiResult()
  @HttpCode(HttpStatus.OK)
  async resetDailyFreeQuota(): Promise<{ success: boolean; message: string; count: number }> {
    return await this.userService.resetDailyFreeQuota();
  }

  @Post('free-quota/check-eligibility')
  @AuthPermission('user:quota-check')
  @ApiOperation({ summary: '检查免费额度资格（定时任务接口）' })
  @ApiResponse({ status: 200, description: '检查成功' })
  @ApiResult()
  @HttpCode(HttpStatus.OK)
  async checkFreeQuotaEligibility(): Promise<{ success: boolean; message: string; count: number }> {
    return await this.userService.checkFreeQuotaEligibility();
  }
}