import { Injectable, Logger, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, FindOptionsWhere } from 'typeorm';
import { format } from 'date-fns';
import * as crypto from 'crypto';
import { ApiKeyEntity } from '../entities/api-key.entity';
import { ApiKeyDto, PaginatedApiKeyDto } from '../dto/api-key.dto';
import { CreateApiKeyDto } from '../dto/create-api-key.dto';
import { ApiKeyQueryDto } from '../dto/api-key-query.dto';
import { ApiKeyStatus } from '../enums/api-key-status.enum';
import { ApiKeyCacheService } from './api-key-cache.service';
import { CallRecordService } from '../../call-record/call-record.service';
import { AuthCacheService } from '../../../shared/auth-cache.service';

/**
 * API密钥服务
 * 负责API密钥的管理
 */
@Injectable()
export class ApiKeyService {
  private readonly logger = new Logger(ApiKeyService.name);

  constructor(
    @InjectRepository(ApiKeyEntity)
    private readonly apiKeyRepository: Repository<ApiKeyEntity>,
    private readonly callRecordService: CallRecordService,
    private readonly apiKeyCacheService: ApiKeyCacheService,
    private readonly authCacheService: AuthCacheService
  ) {}

  /**
   * 创建API密钥
   * @param userId 用户ID
   * @param createApiKeyDto 创建API密钥DTO
   */
  async createApiKey(userId: number, createApiKeyDto: CreateApiKeyDto): Promise<ApiKeyDto> {
    try {
      const { name, scopes, expiresAt, allowedIps, serviceId, keyType } = createApiKeyDto;
      
      // 生成API密钥
      const keyPrefix = 'ak-';
      const secretKeyPrefix = 'sk-';
      const apiKey = keyPrefix + crypto.randomBytes(16).toString('hex');
      const secretKey = secretKeyPrefix + crypto.randomBytes(32).toString('hex');
      
      // 加密密钥秘钥
      const encryptedSecretKey = await this.encryptSecretKey(secretKey);
      
      // 创建API密钥实体
      const newApiKey = this.apiKeyRepository.create({
        key: apiKey,
        userId,
        name: name || `API Key ${format(new Date(), 'yyyy-MM-dd')}`,
        status: ApiKeyStatus.ACTIVE,
        scopes: scopes || ['*'],
        encryptedSecretKey,
        expiresAt: expiresAt ? new Date(expiresAt) : undefined,
        allowedIps,
        serviceId,
        keyType: keyType || 'user',
        isViewed: false
      });
      
      // 保存到数据库
      const savedApiKey = await this.apiKeyRepository.save(newApiKey);
      
      // 缓存API密钥
      await this.apiKeyCacheService.cacheApiKey(savedApiKey);
      
      // 转换为DTO并返回
      const apiKeyDto = await this.toApiKeyDto(savedApiKey, true);
      apiKeyDto.secretKey = secretKey; // 仅在创建时返回密钥秘钥

      return apiKeyDto;
    } catch (error) {
      this.logger.error(`创建API密钥失败: ${error.message}`, error.stack);
      throw new BadRequestException(`创建API密钥失败: ${error.message}`);
    }
  }

  /**
   * 获取API密钥列表
   * @param userId 用户ID
   * @param queryDto 查询条件
   */
  async findAll(userId: number, queryDto: ApiKeyQueryDto): Promise<PaginatedApiKeyDto> {
    try {
      const { page = 1, limit = 10, status, sortBy = 'createdAt', sortDirection = 'DESC', keyword, serviceId, keyType } = queryDto;
      
      // 构建查询条件
      const where: FindOptionsWhere<ApiKeyEntity> = { userId };
      
      if (status) {
        where.status = status;
      }
      
      if (keyword) {
        where.name = Like(`%${keyword}%`);
      }
      
      if (serviceId) {
        where.serviceId = serviceId;
      }
      
      if (keyType) {
        where.keyType = keyType;
      }
      
      // 执行查询
      const [items, totalItems] = await this.apiKeyRepository.findAndCount({
        where,
        order: { [sortBy]: sortDirection },
        skip: (page - 1) * limit,
        take: limit
      });
      
      // 计算总页数
      const totalPages = Math.ceil(totalItems / limit);
      
      // 返回分页结果
      const itemsWithStats = await Promise.all(
        items.map(item => this.toApiKeyDto(item, true))
      );

      return {
        items: itemsWithStats,
        meta: {
          totalItems,
          itemsPerPage: limit,
          totalPages,
          currentPage: page
        }
      };
    } catch (error) {
      this.logger.error(`获取API密钥列表失败: ${error.message}`, error.stack);
      throw new BadRequestException(`获取API密钥列表失败: ${error.message}`);
    }
  }

  /**
   * 获取单个API密钥
   * @param id API密钥ID
   * @param userId 用户ID
   */
  async findOne(id: number, userId: number): Promise<ApiKeyDto> {
    try {
      // 从数据库获取（不依赖缓存，确保能获取到完整数据）
      const apiKey = await this.apiKeyRepository.findOne({
        where: { id, userId }
      });
      
      if (!apiKey) {
        throw new NotFoundException('API密钥不存在');
      }
      
      // 缓存API密钥（用于其他查询）
      await this.apiKeyCacheService.cacheApiKey(apiKey);

      // 返回DTO
      const apiKeyDto = await this.toApiKeyDto(apiKey, true);

      // 如果密钥还未被查看过，返回解密的密钥凭证
      if (!apiKey.isViewed && apiKey.encryptedSecretKey) {
        try {
          const decryptedSecretKey = await this.decryptSecretKey(apiKey.encryptedSecretKey);
          apiKeyDto.secretKey = decryptedSecretKey;
          this.logger.log(`返回未查看密钥的secretKey: ${apiKey.id}`);
        } catch (error) {
          this.logger.warn(`解密密钥失败: ${apiKey.id}, ${error.message}`);
        }
      } else {
        this.logger.log(`不返回secretKey: ${apiKey.id}, isViewed: ${apiKey.isViewed}, hasEncryptedSecretKey: ${!!apiKey.encryptedSecretKey}`);
      }

      return apiKeyDto;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      this.logger.error(`获取API密钥失败: ${error.message}`, error.stack);
      throw new BadRequestException(`获取API密钥失败: ${error.message}`);
    }
  }

  /**
   * 重新生成API密钥
   * @param id API密钥ID
   * @param userId 用户ID
   */
  async regenerateApiKey(id: number, userId: number): Promise<ApiKeyDto> {
    try {
      // 查找现有密钥
      const apiKey = await this.apiKeyRepository.findOne({
        where: { id, userId }
      });
      
      if (!apiKey) {
        throw new NotFoundException('API密钥不存在');
      }
      
      // 生成新密钥
      const keyPrefix = 'ak-';
      const secretKeyPrefix = 'sk-';
      const newApiKey = keyPrefix + crypto.randomBytes(16).toString('hex');
      const newSecretKey = secretKeyPrefix + crypto.randomBytes(32).toString('hex');
      
      // 加密密钥秘钥
      const encryptedSecretKey = await this.encryptSecretKey(newSecretKey);
      
      // 更新密钥
      apiKey.key = newApiKey;
      apiKey.encryptedSecretKey = encryptedSecretKey;
      apiKey.isViewed = false;
      apiKey.updatedAt = new Date();
      
      // 保存到数据库
      const savedApiKey = await this.apiKeyRepository.save(apiKey);
      
      // 缓存API密钥
      await this.apiKeyCacheService.cacheApiKey(savedApiKey);
      
      // 返回更新后的密钥信息
      const apiKeyDto = await this.toApiKeyDto(savedApiKey, true);
      apiKeyDto.secretKey = newSecretKey; // 仅在重新生成时返回密钥秘钥

      return apiKeyDto;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      this.logger.error(`重新生成API密钥失败: ${error.message}`, error.stack);
      throw new BadRequestException(`重新生成API密钥失败: ${error.message}`);
    }
  }

  /**
   * 标记API密钥已查看
   * @param id API密钥ID
   * @param userId 用户ID
   */
  async markAsViewed(id: number, userId: number): Promise<void> {
    try {
      // 查找密钥
      const apiKey = await this.apiKeyRepository.findOne({
        where: { id, userId }
      });
      
      if (!apiKey) {
        throw new NotFoundException('API密钥不存在');
      }
      
      // 更新查看状态
      apiKey.isViewed = true;
      apiKey.updatedAt = new Date();
      
      // 保存到数据库
      await this.apiKeyRepository.save(apiKey);
      
      // 更新缓存
      await this.apiKeyCacheService.cacheApiKey(apiKey);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      this.logger.error(`标记API密钥已查看失败: ${error.message}`, error.stack);
      throw new BadRequestException(`标记API密钥已查看失败: ${error.message}`);
    }
  }

  /**
   * 删除API密钥
   * @param id API密钥ID
   * @param userId 用户ID
   */
  async removeApiKey(id: number, userId: number): Promise<void> {
    try {
      // 查找密钥
      const apiKey = await this.apiKeyRepository.findOne({
        where: { id, userId }
      });
      
      if (!apiKey) {
        throw new NotFoundException('API密钥不存在');
      }
      
      // 标记为已撤销
      apiKey.status = ApiKeyStatus.REVOKED;
      apiKey.updatedAt = new Date();
      
      // 保存到数据库
      await this.apiKeyRepository.save(apiKey);
      
      // 从缓存中移除
      await this.apiKeyCacheService.removeApiKeyFromCache(id, apiKey.key);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      this.logger.error(`删除API密钥失败: ${error.message}`, error.stack);
      throw new BadRequestException(`删除API密钥失败: ${error.message}`);
    }
  }

  /**
   * 同步所有API密钥到缓存
   */
  async syncAllApiKeysToCache(): Promise<void> {
    try {
      // 获取所有活跃的API密钥
      const apiKeys = await this.apiKeyRepository.find({
        where: { status: ApiKeyStatus.ACTIVE }
      });
      
      // 并行同步所有密钥到缓存
      await Promise.all(apiKeys.map(apiKey => this.apiKeyCacheService.cacheApiKey(apiKey)));
      
      this.logger.log(`已同步${apiKeys.length}个API密钥到缓存`);
    } catch (error) {
      this.logger.error(`同步API密钥到缓存失败: ${error.message}`, error.stack);
      throw new BadRequestException(`同步API密钥到缓存失败: ${error.message}`);
    }
  }

  /**
   * 根据用户ID获取API密钥列表
   * @param userId 用户ID
   */
  async findByUserId(userId: number): Promise<ApiKeyDto[]> {
    try {
      // 获取用户的所有API密钥
      const apiKeys = await this.apiKeyRepository.find({
        where: { userId }
      });
      
      // 返回DTO列表
      return await Promise.all(
        apiKeys.map(apiKey => this.toApiKeyDto(apiKey, true))
      );
    } catch (error) {
      this.logger.error(`获取用户API密钥列表失败: ${error.message}`, error.stack);
      throw new BadRequestException(`获取用户API密钥列表失败: ${error.message}`);
    }
  }

  /**
   * 验证API密钥
   * @param key API密钥
   * @param secretKey 密钥秘钥
   */
  async validateApiKey(key: string, secretKey: string): Promise<ApiKeyDto> {
    try {
      // 先从缓存获取
      const cachedApiKey = await this.apiKeyCacheService.getApiKeyByValue(key);
      
      if (cachedApiKey) {
        // 验证密钥状态
        if (cachedApiKey.status !== ApiKeyStatus.ACTIVE) {
          throw new ForbiddenException(`API密钥状态无效: ${cachedApiKey.status}`);
        }
        
        // 验证过期时间
        if (cachedApiKey.expiresAt && new Date(cachedApiKey.expiresAt) < new Date()) {
          throw new ForbiddenException('API密钥已过期');
        }
        
        // 获取加密的密钥秘钥
        const encryptedSecretKey = await this.apiKeyCacheService.getApiKeySecret(cachedApiKey.id);
        
        if (!encryptedSecretKey) {
          throw new ForbiddenException('无法验证API密钥');
        }
        
        // 验证密钥秘钥
        const isValid = await this.verifySecretKey(secretKey, encryptedSecretKey);
        
        if (!isValid) {
          throw new ForbiddenException('API密钥验证失败');
        }
        
        return cachedApiKey;
      }
      
      // 缓存未命中，从数据库获取
      const apiKey = await this.apiKeyRepository.findOne({
        where: { key }
      });
      
      if (!apiKey) {
        throw new NotFoundException('API密钥不存在');
      }
      
      // 验证密钥状态
      if (apiKey.status !== ApiKeyStatus.ACTIVE) {
        throw new ForbiddenException(`API密钥状态无效: ${apiKey.status}`);
      }
      
      // 验证过期时间
      if (apiKey.expiresAt && apiKey.expiresAt < new Date()) {
        throw new ForbiddenException('API密钥已过期');
      }
      
      // 验证密钥秘钥
      const isValid = await this.verifySecretKey(secretKey, apiKey.encryptedSecretKey);
      
      if (!isValid) {
        throw new ForbiddenException('API密钥验证失败');
      }
      
      // 缓存API密钥
      await this.apiKeyCacheService.cacheApiKey(apiKey);
      
      // 返回DTO
      return this.toApiKeyDto(apiKey);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }
      
      this.logger.error(`验证API密钥失败: ${error.message}`, error.stack);
      throw new BadRequestException(`验证API密钥失败: ${error.message}`);
    }
  }

  /**
   * 更新API密钥最后使用时间
   * @param id API密钥ID
   * @param ip 客户端IP
   */
  async updateLastUsed(id: number, ip: string): Promise<void> {
    try {
      // 更新数据库
      await this.apiKeyRepository.update(
        { id },
        { lastUsedAt: new Date() }
      );
      
      // 更新缓存
      await this.apiKeyCacheService.updateApiKeyLastUsed(id, ip);
    } catch (error) {
      this.logger.error(`更新API密钥最后使用时间失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 加密密钥秘钥
   * @param secretKey 密钥秘钥
   */
  private async encryptSecretKey(secretKey: string): Promise<string> {
    try {
      // 使用AES加密，这样可以解密
      const algorithm = 'aes-256-cbc';
      const key = crypto.scryptSync('api-key-secret', 'salt', 32);
      const iv = crypto.randomBytes(16);

      const cipher = crypto.createCipheriv(algorithm, key, iv);
      let encrypted = cipher.update(secretKey, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      // 将iv和加密数据组合
      return iv.toString('hex') + ':' + encrypted;
    } catch (error) {
      this.logger.error(`加密密钥秘钥失败: ${error.message}`, error.stack);
      throw new BadRequestException('加密密钥秘钥失败');
    }
  }

  /**
   * 解密密钥秘钥
   * @param encryptedSecretKey 加密的密钥秘钥
   */
  private async decryptSecretKey(encryptedSecretKey: string): Promise<string> {
    try {
      const algorithm = 'aes-256-cbc';
      const key = crypto.scryptSync('api-key-secret', 'salt', 32);

      // 分离iv和加密数据
      const parts = encryptedSecretKey.split(':');
      if (parts.length !== 2) {
        throw new Error('Invalid encrypted data format');
      }

      const iv = Buffer.from(parts[0], 'hex');
      const encrypted = parts[1];

      const decipher = crypto.createDecipheriv(algorithm, key, iv);
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      this.logger.error(`解密密钥秘钥失败: ${error.message}`, error.stack);
      throw new BadRequestException('解密密钥秘钥失败');
    }
  }

  /**
   * 验证密钥秘钥
   * @param secretKey 密钥秘钥
   * @param encryptedSecretKey 加密的密钥秘钥
   */
  private async verifySecretKey(secretKey: string, encryptedSecretKey: string): Promise<boolean> {
    try {
      // 解密后比较
      const decryptedSecretKey = await this.decryptSecretKey(encryptedSecretKey);
      return decryptedSecretKey === secretKey;
    } catch (error) {
      this.logger.error(`验证密钥秘钥失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 转换为DTO
   * @param apiKey API密钥实体
   */
  /**
   * 同步所有API密钥到缓存
   * 用于定时任务批量同步密钥数据
   */
  async syncAllKeysToCache(): Promise<{ syncedCount: number; errors: string[] }> {
    try {
      this.logger.log('开始同步所有API密钥到缓存');

      // 获取所有活跃的API密钥，包括加密的密钥秘钥
      const apiKeys = await this.apiKeyRepository.find({
        where: { status: ApiKeyStatus.ACTIVE },
        select: ['id', 'key', 'userId', 'status', 'scopes', 'expiresAt', 'allowedIps', 'serviceId', 'keyType', 'encryptedSecretKey']
      });

      let syncedCount = 0;
      const errors: string[] = [];

      // 批量同步到缓存
      for (const apiKey of apiKeys) {
        try {
          // 1. 使用ApiKeyCacheService缓存（分层缓存）
          await this.apiKeyCacheService.cacheApiKey(apiKey);

          // 2. 使用AuthCacheService缓存（统一认证缓存）
          await this.authCacheService.setApiKey(apiKey.key, {
            id: apiKey.id,
            userId: apiKey.userId,
            serviceId: apiKey.serviceId,
            name: apiKey.name,
            keyStatus: apiKey.status,
            keyType: apiKey.keyType,
            description: apiKey.name,
            permissions: apiKey.scopes,
            expiresAt: apiKey.expiresAt,
            lastUsedAt: apiKey.lastUsedAt,
            createdAt: apiKey.createdAt,
            updatedAt: apiKey.updatedAt,
            isSecretViewed: apiKey.isViewed
          } as any);

          // 3. 缓存解密后的Secret Key
          if (apiKey.encryptedSecretKey) {
            const decryptedSecretKey = await this.decryptSecretKey(apiKey.encryptedSecretKey);
            if (decryptedSecretKey) {
              await this.authCacheService.setApiKeySecret(apiKey.id, decryptedSecretKey);
            }
          }

          syncedCount++;
        } catch (error) {
          const errorMsg = `同步密钥 ${apiKey.key} 失败: ${error.message}`;
          this.logger.error(errorMsg);
          errors.push(errorMsg);
        }
      }

      this.logger.log(`API密钥同步完成，成功同步 ${syncedCount}/${apiKeys.length} 个密钥`);

      return {
        syncedCount,
        errors
      };
    } catch (error) {
      this.logger.error(`同步API密钥到缓存失败: ${error.message}`, error.stack);
      throw error;
    }
  }



  /**
   * 更新API密钥使用统计
   * @param apiKeyId API密钥ID
   * @param ip 客户端IP
   */
  async updateUsageStats(apiKeyId: number, ip?: string): Promise<void> {
    try {
      const apiKey = await this.apiKeyRepository.findOne({
        where: { id: apiKeyId }
      });

      if (!apiKey) {
        return; // 密钥不存在，忽略统计更新
      }

      // 更新使用统计
      apiKey.lastUsedAt = new Date();

      // 保存到数据库
      await this.apiKeyRepository.save(apiKey);

      // 更新缓存
      await this.apiKeyCacheService.cacheApiKey(apiKey);
    } catch (error) {
      this.logger.warn(`更新API密钥使用统计失败: ${error.message}`);
    }
  }

  /**
   * 计算API密钥的使用统计
   * @param apiKeyId API密钥ID
   */
  private async calculateUsageStats(apiKeyId: number): Promise<{
    todayCalls: number;
    monthlyCalls: number;
    totalCalls: number;
  }> {
    try {
      // 通过调用记录服务获取使用统计
      return await this.callRecordService.getApiKeyUsageStats(apiKeyId.toString());
    } catch (error) {
      this.logger.warn(`计算API密钥使用统计失败: ${error.message}`);
      return {
        todayCalls: 0,
        monthlyCalls: 0,
        totalCalls: 0
      };
    }
  }

  private async toApiKeyDto(apiKey: ApiKeyEntity, includeUsageStats = false): Promise<ApiKeyDto> {
    const dto: ApiKeyDto = {
      id: apiKey.id,
      key: apiKey.key,
      name: apiKey.name,
      status: apiKey.status,
      scopes: apiKey.scopes || [],
      createdAt: apiKey.createdAt,
      updatedAt: apiKey.updatedAt,
      expiresAt: apiKey.expiresAt,
      lastUsedAt: apiKey.lastUsedAt,
      isViewed: apiKey.isViewed,
      allowedIps: apiKey.allowedIps,
      serviceId: apiKey.serviceId,
      keyType: apiKey.keyType,
      userId: apiKey.userId
    };

    // 如果需要包含使用统计
    if (includeUsageStats) {
      const usageStats = await this.calculateUsageStats(apiKey.id);
      dto.todayCalls = usageStats.todayCalls;
      dto.monthlyCalls = usageStats.monthlyCalls;
      dto.totalCalls = usageStats.totalCalls;

      // 设置默认配额限制（可以从配置或用户套餐中获取）
      dto.quotaLimit = 10000; // 默认1万次/月
      dto.remainingCalls = Math.max(0, dto.quotaLimit - usageStats.monthlyCalls);
    }

    return dto;
  }
}