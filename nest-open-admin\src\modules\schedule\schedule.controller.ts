import { Controller, Post, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ScheduleService } from './schedule.service';
import { Roles } from '../../common/decorators/roles.decorator';
import { RoleEnum } from '../user/entities/user.entity';

@ApiTags('定时任务管理')
@Controller('schedule')
@ApiBearerAuth()
export class ScheduleController {
  constructor(private readonly scheduleService: ScheduleService) {}

  @Post('reset-free-quota')
  @Roles(RoleEnum.ADMIN, RoleEnum.SUPER)
  @ApiOperation({ summary: '手动重置免费额度' })
  @ApiResponse({ status: 200, description: '重置成功' })
  @ApiResponse({ status: 403, description: '权限不足' })
  async manualResetFreeQuota() {
    return await this.scheduleService.manualResetFreeQuota();
  }

  @Post('sync-api-keys')
  @Roles(RoleEnum.ADMIN, RoleEnum.SUPER)
  @ApiOperation({ summary: '手动同步API密钥到缓存' })
  @ApiResponse({ status: 200, description: '同步成功' })
  @ApiResponse({ status: 403, description: '权限不足' })
  async manualSyncApiKeys() {
    return await this.scheduleService.manualSyncApiKeys();
  }

  @Post('check-alerts')
  @Roles(RoleEnum.ADMIN, RoleEnum.SUPER)
  @ApiOperation({ summary: '手动检查预警' })
  @ApiResponse({ status: 200, description: '检查完成' })
  @ApiResponse({ status: 403, description: '权限不足' })
  async manualCheckAlerts() {
    return await this.scheduleService.manualCheckAlerts();
  }

  @Post('cleanup-data')
  @Roles(RoleEnum.ADMIN, RoleEnum.SUPER)
  @ApiOperation({ summary: '手动清理过期数据' })
  @ApiResponse({ status: 200, description: '清理完成' })
  @ApiResponse({ status: 403, description: '权限不足' })
  async manualCleanupData() {
    return await this.scheduleService.manualCleanupData();
  }

  @Get('status')
  @Roles(RoleEnum.ADMIN, RoleEnum.SUPER)
  @ApiOperation({ summary: '获取定时任务状态' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getScheduleStatus() {
    return {
      success: true,
      message: '定时任务系统运行正常',
      tasks: [
        {
          name: '免费额度重置',
          cron: '0 0 0 * * *',
          description: '每日凌晨0点重置用户免费额度',
          status: 'active'
        },
        {
          name: '余额预警检查',
          cron: '0 0 * * * *',
          description: '每小时检查用户余额预警',
          status: 'active'
        },
        {
          name: 'API密钥同步',
          cron: '0 */30 * * * *',
          description: '每30分钟同步API密钥到缓存',
          status: 'active'
        },
        {
          name: '服务异常预警',
          cron: '0 */30 * * * *',
          description: '每30分钟检查服务异常预警',
          status: 'active'
        },
        {
          name: 'API频率异常预警',
          cron: '0 */15 * * * *',
          description: '每15分钟检查API调用频率异常',
          status: 'active'
        },
        {
          name: '过期数据清理',
          cron: '0 0 2 * * *',
          description: '每日凌晨2点清理过期数据',
          status: 'active'
        }
      ]
    };
  }

  @Get('stats')
  @Roles(RoleEnum.ADMIN, RoleEnum.SUPER)
  @ApiOperation({ summary: '获取定时任务统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getScheduleStats() {
    return await this.scheduleService.getScheduleStats();
  }
}