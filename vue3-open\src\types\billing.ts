/**
 * 账单状态
 */
export type BillStatus = 'unpaid' | 'paid' | 'overdue' | 'cancelled'

/**
 * 账单类型
 */
export type BillType = 'usage' | 'subscription' | 'one_time' | 'refund'

/**
 * 充值状态
 */
export type RechargeStatus = 'pending' | 'success' | 'failed' | 'cancelled'

/**
 * 账户信息
 */
export interface AccountInfo {
  balance: number
  totalSpent: number
  monthlySpent: number
  currency: string
  creditLimit: number
  lastRechargeAt: string
  accountStatus: 'active' | 'suspended' | 'frozen'
  paymentMethods?: {
    id: string
    type: string
    name: string
    isDefault: boolean
    lastUsed?: string
  }[]
}

/**
 * 账单基本信息
 */
export interface Bill {
  id: string
  userId: string
  type: BillType
  status: BillStatus
  amount: number
  currency: string
  description: string
  billingPeriod: {
    start: string
    end: string
  }
  dueDate: string
  paidAt?: string
  createdAt: string
  updatedAt: string
}

/**
 * 账单详细信息
 */
export interface BillDetail extends Bill {
  items: {
    id: string
    serviceId: string
    serviceName: string
    description: string
    quantity: number
    unitPrice: number
    totalPrice: number
    usagePeriod: {
      start: string
      end: string
    }
    metadata?: Record<string, any>
  }[]
  summary: {
    subtotal: number
    tax: number
    discount: number
    total: number
  }
  paymentInfo?: {
    method: string
    transactionId: string
    paidAt: string
    amount: number
  }
  invoiceInfo?: {
    invoiceNumber: string
    issuedAt: string
    downloadUrl?: string
  }
}

/**
 * 充值记录
 */
export interface RechargeRecord {
  id: string
  amount: number
  paymentMethod: string
  status: RechargeStatus
  transactionId?: string
  createdAt: string
  completedAt: string
  failReason?: string
  metadata?: Record<string, any>
}

/**
 * 使用统计
 */
export interface UsageStats {
  totalCalls: number
  totalCost: number
  services: {
    serviceId: string
    serviceName: string
    calls: number
    cost: number
    percentage: number
  }[]
  timeline: {
    date: string
    calls: number
    cost: number
  }[]
  topServices?: {
    serviceId: string
    serviceName: string
    calls: number
    cost: number
    trend: 'up' | 'down' | 'stable'
  }[]
}

/**
 * 消费预测
 */
export interface CostPrediction {
  predictedCost: number
  confidence: number
  period: {
    start: string
    end: string
  }
  factors: {
    name: string
    impact: number
    description: string
  }[]
  recommendations?: {
    type: 'optimization' | 'alert' | 'upgrade'
    title: string
    description: string
    potentialSavings?: number
  }[]
}

/**
 * 消费预警设置
 */
export interface CostAlert {
  threshold: number
  type: 'daily' | 'monthly'
  enabled: boolean
  notificationMethods: string[]
  lastTriggered?: string
  triggerCount?: number
}

/**
 * 价格信息
 */
export interface PricingInfo {
  services: {
    serviceId: string
    serviceName: string
    pricing: {
      type: 'per_call' | 'per_minute' | 'per_mb' | 'per_request'
      price: number
      unit: string
      description?: string
      tiers?: {
        min: number
        max?: number
        price: number
      }[]
    }[]
  }[]
  currency: string
  lastUpdated: string
}