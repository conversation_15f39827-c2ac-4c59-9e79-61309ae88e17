import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsBoolean,
  MinLength,
  MaxLength,
  Min,
  IsInt,
  Max,
  IsIn,
  IsUrl,
  IsObject,
  IsArray,
} from 'class-validator';

/**
 * 服务类型枚举
 */
export enum ServiceType {
  OCR = 'OCR', // 光学字符识别
  NLP = 'NLP', // 自然语言处理
  CV = 'CV', // 计算机视觉
  AI = 'AI', // 人工智能
  DATA = 'DATA', // 数据处理
  OTHER = 'OTHER', // 其他
}

/**
 * 服务状态枚举
 */
export enum ServiceStatus {
  ACTIVE = 'active', // 活跃
  INACTIVE = 'inactive', // 未激活
  MAINTENANCE = 'maintenance', // 维护中
  DEPRECATED = 'deprecated', // 已弃用
}

/**
 * 定价模式枚举
 */
export enum PricingModel {
  FREE = 'free', // 免费
  PAY_PER_USE = 'pay_per_use', // 按次付费
  SUBSCRIPTION = 'subscription', // 订阅制
  TIERED = 'tiered', // 分层定价
}

/**
 * 创建服务DTO
 */
export class CreateServiceDto {
  @ApiProperty({ description: '服务代码', example: 'ocr-invoice' })
  @IsString()
  @IsNotEmpty()
  @MinLength(2)
  @MaxLength(100)
  code: string;

  @ApiProperty({ description: '服务名称', example: '发票OCR识别' })
  @IsString()
  @IsNotEmpty()
  @MinLength(2)
  @MaxLength(200)
  name: string;

  @ApiPropertyOptional({ description: '服务描述', example: '智能识别发票信息，支持增值税发票、普通发票等' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiProperty({ description: '服务类型', enum: ServiceType })
  @IsEnum(ServiceType)
  @IsNotEmpty()
  type: ServiceType;

  @ApiProperty({ description: '服务状态', enum: ServiceStatus, default: ServiceStatus.ACTIVE })
  @IsOptional()
  @IsEnum(ServiceStatus)
  serviceStatus?: ServiceStatus;

  @ApiProperty({ description: '定价模式', enum: PricingModel })
  @IsEnum(PricingModel)
  @IsNotEmpty()
  pricingModel: PricingModel;

  @ApiPropertyOptional({ description: '当前版本', example: 'v1.0.0' })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  currentVersion?: string;

  @ApiPropertyOptional({ description: '服务特性', example: '高精度,快速识别,支持多格式' })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  features?: string;

  @ApiPropertyOptional({ description: '服务端点URL', example: 'https://api.example.com/ocr/invoice' })
  @IsOptional()
  @IsUrl()
  @MaxLength(500)
  endpoint?: string;

  @ApiPropertyOptional({ description: '请求配置', example: { timeout: 30000, retries: 3 } })
  @IsOptional()
  @IsObject()
  requestConfig?: Record<string, any>;

  @ApiPropertyOptional({ description: '响应配置', example: { format: 'json', encoding: 'utf-8' } })
  @IsOptional()
  @IsObject()
  responseConfig?: Record<string, any>;

  @ApiPropertyOptional({ description: '单次调用价格', example: 0.1, minimum: 0 })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 4 })
  @Min(0)
  pricePerCall?: number;

  @ApiPropertyOptional({ description: '月订阅价格', example: 99.99, minimum: 0 })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  monthlyPrice?: number;

  @ApiPropertyOptional({ description: '年订阅价格', example: 999.99, minimum: 0 })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  yearlyPrice?: number;

  @ApiPropertyOptional({ description: '免费调用次数', example: 100, minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  freeCallsPerDay?: number;

  @ApiPropertyOptional({ description: '每日调用限制', example: 10000, minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  dailyLimit?: number;

  @ApiPropertyOptional({ description: '每月调用限制', example: 300000, minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  monthlyLimit?: number;

  @ApiPropertyOptional({ description: '服务标签', example: ['OCR', '发票', '财务'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: '服务图标URL', example: 'https://example.com/icon.png' })
  @IsOptional()
  @IsUrl()
  iconUrl?: string;

  @ApiPropertyOptional({ description: '服务文档URL', example: 'https://docs.example.com/ocr' })
  @IsOptional()
  @IsUrl()
  documentationUrl?: string;

  @ApiPropertyOptional({ description: '是否需要认证', example: true })
  @IsOptional()
  @IsBoolean()
  requiresAuth?: boolean;

  @ApiPropertyOptional({ description: '是否支持批量处理', example: false })
  @IsOptional()
  @IsBoolean()
  supportsBatch?: boolean;
}

/**
 * 更新服务DTO
 */
/**
 * 更新服务DTO
 */
export class UpdateServiceDto {
  @ApiPropertyOptional({ description: '服务名称', example: 'OCR 快递单识别服务' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: '服务描述', example: '基于深度学习的快递单识别服务' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: '服务特性', example: '支持多种快递公司单据格式;高精度文字识别' })
  @IsOptional()
  @IsString()
  features?: string;

  @ApiPropertyOptional({ description: '服务端点URL', example: '/v1/api/ocr/upload' })
  @IsOptional()
  @IsString()  // 改为 IsString，移除 IsUrl 验证，因为可能是相对路径
  endpoint?: string;

  @ApiPropertyOptional({ description: '单次调用价格', example: '0.05' })
  @IsOptional()
  @IsNumber()
  price?: number;

  @ApiPropertyOptional({ description: '服务类型', enum: ServiceType })
  @IsOptional()  // 改为可选
  @IsEnum(ServiceType)
  type?: ServiceType;  // 改为可选

  @ApiPropertyOptional({ description: '服务状态', enum: ServiceStatus })
  @IsOptional()
  @IsEnum(ServiceStatus)
  serviceStatus?: ServiceStatus;

  [key:string]: any
}

/**
 * 服务查询DTO
 */
export class QueryServiceDto {
  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', example: 10, minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: '服务代码搜索', example: 'ocr' })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiPropertyOptional({ description: '服务名称搜索', example: 'OCR' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: '服务类型', enum: ServiceType })
  @IsOptional()
  @IsEnum(ServiceType)
  type?: ServiceType;

  @ApiPropertyOptional({ description: '服务状态', enum: ServiceStatus })
  @IsOptional()
  @IsEnum(ServiceStatus)
  serviceStatus?: ServiceStatus;

  @ApiPropertyOptional({ description: '定价模式', enum: PricingModel })
  @IsOptional()
  @IsEnum(PricingModel)
  pricingModel?: PricingModel;

  @ApiPropertyOptional({ description: '是否需要认证' })
  @IsOptional()
  @IsBoolean()
  requiresAuth?: boolean;

  @ApiPropertyOptional({ description: '是否支持批量处理' })
  @IsOptional()
  @IsBoolean()
  supportsBatch?: boolean;

  @ApiPropertyOptional({ description: '排序字段', example: 'createdAt' })
  @IsOptional()
  @IsString()
  @IsIn(['id', 'code', 'name', 'type', 'serviceStatus', 'createdAt', 'updatedAt'])
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', example: 'DESC' })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}

/**
 * 服务响应DTO
 */
export class ServiceResponseDto {
  @ApiProperty({ description: '服务ID', example: 1 })
  id: number;

  @ApiProperty({ description: '服务代码', example: 'ocr-invoice' })
  code: string;

  @ApiProperty({ description: '服务名称', example: '发票OCR识别' })
  name: string;

  @ApiPropertyOptional({ description: '服务描述' })
  description?: string;

  @ApiProperty({ description: '服务类型', enum: ServiceType })
  type: ServiceType;

  @ApiProperty({ description: '服务状态', enum: ServiceStatus })
  serviceStatus: ServiceStatus;

  @ApiProperty({ description: '定价模式', enum: PricingModel })
  pricingModel: PricingModel;

  @ApiProperty({ description: '当前版本', example: 'v1.0.0' })
  currentVersion: string;

  @ApiPropertyOptional({ description: '服务特性' })
  features?: string;

  @ApiPropertyOptional({ description: '服务端点URL' })
  endpoint?: string;

  @ApiPropertyOptional({ description: '请求配置' })
  requestConfig?: Record<string, any>;

  @ApiPropertyOptional({ description: '响应配置' })
  responseConfig?: Record<string, any>;

  @ApiPropertyOptional({ description: '单次调用价格' })
  price?: number;

  @ApiPropertyOptional({ description: '月订阅价格' })
  monthlyPrice?: number;

  @ApiPropertyOptional({ description: '年订阅价格' })
  yearlyPrice?: number;

  @ApiPropertyOptional({ description: '免费调用次数' })
  freeCallsPerDay?: number;

  @ApiPropertyOptional({ description: '每日调用限制' })
  dailyLimit?: number;

  @ApiPropertyOptional({ description: '每分调用限制' })
  minuteLimit?: number;

  @ApiPropertyOptional({ description: '服务标签' })
  tags?: string[];

  @ApiPropertyOptional({ description: '服务图标URL' })
  iconUrl?: string;

  @ApiPropertyOptional({ description: '服务文档URL' })
  documentationUrl?: string;

  @ApiProperty({ description: '是否需要认证' })
  requiresAuth: boolean;

  @ApiProperty({ description: '是否支持批量处理' })
  supportsBatch: boolean;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

/**
 * 服务列表响应DTO
 */
export class ServiceListResponseDto {
  @ApiProperty({ description: '服务列表', type: [ServiceResponseDto] })
  data: ServiceResponseDto[];

  @ApiProperty({ description: '总数', example: 50 })
  total: number;

  @ApiProperty({ description: '当前页', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  limit: number;

  @ApiProperty({ description: '总页数', example: 5 })
  totalPages: number;
}