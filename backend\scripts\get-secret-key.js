const { createConnection } = require('typeorm');
const crypto = require('crypto');

/**
 * 获取API密钥的Secret Key脚本
 */
async function getSecretKey() {
  console.log('获取API密钥的Secret Key...');
  
  // 创建数据库连接
  const connection = await createConnection({
    type: 'mysql',
    host: 'localhost',
    port: 3306,
    username: 'root',
    password: '123456',
    database: 'openapidb',
    entities: [],
    synchronize: false,
  });

  try {
    const apiKey = 'ak-579b27759508f152525a4e5a567efd5a';
    
    // 查询API密钥的加密Secret Key
    const result = await connection.query(`
      SELECT id, api_key, encrypted_secret_key 
      FROM api_keys 
      WHERE api_key = ?
    `, [apiKey]);

    if (result.length === 0) {
      console.log('❌ 未找到API密钥');
      return;
    }

    const keyData = result[0];
    console.log(`找到API密钥: ID=${keyData.id}, Key=${keyData.api_key}`);
    console.log(`加密的Secret Key: ${keyData.encrypted_secret_key}`);
    
    // 注意：这里需要使用与后端相同的解密方法
    // 由于我们不知道具体的加密密钥，我们直接使用加密后的值进行测试
    console.log('\n请在前端测试页面中使用以下信息:');
    console.log(`API Key: ${keyData.api_key}`);
    console.log(`Secret Key (加密): ${keyData.encrypted_secret_key}`);
    
  } catch (error) {
    console.error('❌ 获取失败:', error.message);
  } finally {
    await connection.close();
    console.log('连接已关闭');
  }
}

// 运行脚本
getSecretKey().catch(console.error);
