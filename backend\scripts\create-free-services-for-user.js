const mysql = require('mysql2/promise');

async function createFreeServicesForUser() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '123456',
    database: 'openapidb'
  });

  try {
    const userId = 4;
    console.log(`=== 为用户ID ${userId} 创建免费服务记录 ===`);
    
    // 查询所有活跃服务
    const [services] = await connection.execute('SELECT * FROM open_service WHERE status = ?', ['active']);
    console.log(`找到 ${services.length} 个活跃服务`);

    // 根据需求文档，新用户默认每日免费调用次数：5次
    const freeCount = 5;
    const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式

    for (const service of services) {
      console.log(`为服务 ${service.name} (ID: ${service.id}) 创建免费记录...`);
      
      // 检查是否已存在记录
      const [existing] = await connection.execute(
        'SELECT * FROM user_service WHERE user_id = ? AND service_id = ?',
        [userId, service.id]
      );

      if (existing.length > 0) {
        console.log(`  - 服务 ${service.name} 的记录已存在，跳过`);
        continue;
      }

      // 创建新的用户服务记录
      const insertResult = await connection.execute(`
        INSERT INTO user_service (
          createdAt, updatedAt, baseStatus, del_flag,
          user_id, service_id, total_count, used_count, remaining_count,
          free_count, purchased_count, free_used_today,
          last_reset_date, alert_sent, enabled
        ) VALUES (
          NOW(), NOW(), 1, '0',
          ?, ?, ?, 0, ?,
          ?, 0, 0,
          ?, 0, 1
        )
      `, [
        userId, service.id, freeCount, freeCount,
        freeCount, currentDate
      ]);

      console.log(`  - ✅ 成功创建服务 ${service.name} 的免费记录，免费次数: ${freeCount}`);
    }

    // 验证创建结果
    const [userServices] = await connection.execute('SELECT * FROM user_service WHERE user_id = ?', [userId]);
    console.log(`\n=== 创建完成，用户现在有 ${userServices.length} 个服务记录 ===`);
    
    userServices.forEach(record => {
      console.log(`- 服务ID ${record.service_id}: 总次数=${record.total_count}, 已用=${record.used_count}, 剩余=${record.remaining_count}, 免费=${record.free_count}`);
    });

  } catch (error) {
    console.error('创建免费服务记录失败:', error.message);
  } finally {
    await connection.end();
  }
}

createFreeServicesForUser();
