# API密钥安全最佳实践方案

## 背景

当前开放平台API测试功能存在安全隐患：前端在API测试页面中直接传输API密钥和Secret密钥，这些敏感信息以明文形式出现在请求头中，容易被中间人攻击截获。本文档提供基于HMAC签名的API认证解决方案，以提高系统安全性。

## 安全风险分析

1. **明文传输密钥**：API密钥和Secret密钥直接在请求头中传输，容易被网络嗅探工具截获
2. **无时效性验证**：请求没有时间戳限制，导致被截获的请求可能被重放
3. **无请求唯一性保证**：缺少nonce机制，无法防止重放攻击
4. **中间人攻击风险**：即使使用HTTPS，在某些场景下仍可能面临中间人攻击风险

## 解决方案：基于HMAC签名的API认证

### 方案概述

使用HMAC-SHA256算法生成请求签名，而不是直接传输密钥：

1. 前端持有API Key和Secret Key
2. 每次请求时，使用Secret Key对请求参数进行签名
3. 请求头中只传输API Key和签名，不再传输Secret Key
4. 后端使用存储的Secret Key验证签名

### 签名机制工作流程

```
┌─────────┐                                      ┌─────────┐
│  前端   │                                      │  后端   │
└────┬────┘                                      └────┬────┘
     │                                                │
     │ 1. 生成时间戳和nonce                           │
     │                                                │
     │ 2. 构建规范化请求字符串                        │
     │    (method, path, query, body, timestamp)      │
     │                                                │
     │ 3. 使用SecretKey计算HMAC-SHA256签名           │
     │                                                │
     │ 4. 发送请求                                    │
     │    Headers: {                                  │
     │      X-API-KEY: apiKey,                        │
     │      X-Timestamp: timestamp,                   │
     │      X-Nonce: nonce,                           │
     │      X-Signature: signature                    │
     │    }                                           │
     │ ─────────────────────────────────────────────> │
     │                                                │
     │                                                │ 5. 验证时间戳是否在有效期内
     │                                                │
     │                                                │ 6. 验证nonce是否已使用
     │                                                │
     │                                                │ 7. 查找apiKey对应的secretKey
     │                                                │
     │                                                │ 8. 重建规范化请求字符串
     │                                                │
     │                                                │ 9. 计算签名并与请求签名比较
     │                                                │
     │                                                │ 10. 验证通过则处理请求
     │                                                │
     │ 11. 接收响应                                   │
     │ <───────────────────────────────────────────── │
     │                                                │
```

## 前端实现方案

### 修改 ApiTestView.vue

```javascript
// 1. 添加签名生成工具函数
import CryptoJS from 'crypto-js'; // 需要添加依赖

// 构建规范化请求字符串
function buildCanonicalRequest(method, path, queryParams, bodyData, timestamp) {
  const sortedQuery = Object.keys(queryParams || {})
    .sort()
    .map(key => `${key}=${encodeURIComponent(queryParams[key])}`)
    .join('&');
  
  let bodyString = '';
  if (bodyData) {
    if (typeof bodyData === 'string') {
      bodyString = bodyData;
    } else if (bodyData instanceof FormData) {
      // 对于文件上传，我们只使用文件名和大小进行签名
      const fileInfo = [];
      bodyData.forEach((value, key) => {
        if (value instanceof File) {
          fileInfo.push(`${key}:${value.name}:${value.size}`);
        } else {
          fileInfo.push(`${key}:${value}`);
        }
      });
      bodyString = fileInfo.join(';');
    } else {
      bodyString = JSON.stringify(bodyData);
    }
  }
  
  return [
    method.toUpperCase(),
    path,
    sortedQuery,
    bodyString,
    timestamp
  ].join('\n');
}

// 生成签名
function generateSignature(canonicalRequest, secretKey) {
  return CryptoJS.HmacSHA256(canonicalRequest, secretKey).toString(CryptoJS.enc.Base64);
}

// 生成nonce
function generateNonce() {
  return CryptoJS.lib.WordArray.random(16).toString();
}

// 2. 修改sendRequest方法
const sendRequest = async () => {
  loading.value = true;
  try {
    // 验证API密钥是否已输入
    const apiKey = requestConfig.apiKey;
    const secretKey = requestConfig.secretKey;
    
    if (!apiKey || !secretKey) {
      ElMessage.warning('请先输入API密钥和Secret密钥');
      loading.value = false;
      return;
    }
    
    const startTime = Date.now();
    // 移除可能存在的/v1/前缀，确保不会重复
    const endpoint = requestConfig.endpoint.replace(/^\/v1\//, '/');
    const url = import.meta.env.VITE_APP_BASE_API + endpoint;
    
    // 生成时间戳和nonce
    const timestamp = Math.floor(Date.now() / 1000).toString();
    const nonce = generateNonce();
    
    // 构建请求数据
    const requestData = {
      method: requestConfig.method,
      url: url,
      headers: {},
      params: Object.fromEntries(
        requestConfig.queryParams
          .filter((p) => p.key && p.value)
          .map((p) => [p.key, p.value])
      ),
      isAsync: requestConfig.isAsync
    };
    
    // 添加Content-Type和其他自定义头
    requestConfig.headers.forEach((header) => {
      if (header.key && header.value) {
        requestData.headers[header.key] = header.value;
      }
    });
    
    // 根据参数类型设置请求数据
    if (requestConfig.method === 'POST') {
      if (paramType.value === 'file') {
        const formData = new FormData();
        requestConfig.files.forEach((file) => {
          formData.append('file', file);
        });
        requestData.data = formData;
        requestData.isFileUpload = true;
      } else if (paramType.value === 'json') {
        try {
          // 解析JSON并添加mode参数
          const jsonData = JSON.parse(requestConfig.jsonBody);
          jsonData.mode = requestConfig.isAsync ? 'async' : 'sync';
          requestData.data = jsonData;
        } catch (error) {
          ElMessage.error('JSON格式错误');
          loading.value = false;
          return;
        }
      }
    } else if (requestConfig.method === 'GET') {
      // 确保GET请求也添加mode参数
      requestData.params = requestData.params || {};
      requestData.params.mode = requestConfig.isAsync ? 'async' : 'sync';
    }
    
    // 构建规范化请求字符串
    const canonicalRequest = buildCanonicalRequest(
      requestData.method,
      new URL(requestData.url).pathname,
      requestData.params,
      requestData.data,
      timestamp
    );
    
    // 生成签名
    const signature = generateSignature(canonicalRequest, secretKey);
    
    // 设置认证头
    requestData.headers['X-API-KEY'] = apiKey;
    requestData.headers['X-Timestamp'] = timestamp;
    requestData.headers['X-Nonce'] = nonce;
    requestData.headers['X-Signature'] = signature;
    
    // 记录请求信息（仅用于调试）
    console.log('请求信息:', {
      url: requestData.url,
      method: requestData.method,
      headers: requestData.headers,
      params: requestData.params,
      isAsync: requestData.isAsync
    });
    
    // 发送请求
    const result = await apiTestService.sendRequest(requestData);
    const endTime = Date.now();
    
    // 处理响应...（保持原有逻辑）
    
  } catch (error) {
    // 错误处理...（保持原有逻辑）
  } finally {
    loading.value = false;
  }
};
```

### 修改 apiTest.ts 服务

```typescript
// 修改sendRequest方法以支持签名认证
export const sendRequest = async (config) => {
  try {
    const axiosConfig = {
      method: config.method,
      url: config.url,
      headers: config.headers,
      params: config.params,
    };
    
    if (config.isFileUpload && config.data) {
      axiosConfig.data = config.data;
    } else if (config.data) {
      axiosConfig.data = config.data;
    }
    
    const response = await axios(axiosConfig);
    
    // 处理响应...（保持原有逻辑）
    return response;
  } catch (error) {
    // 错误处理...（保持原有逻辑）
    throw error;
  }
};
```

## 后端实现方案

### 修改 unified-auth.guard.ts

```typescript
// 添加新的接口定义
interface AuthInfo {
  apiKey: string;
  timestamp: string;
  nonce: string;
  signature: string;
}

@Injectable()
export class UnifiedAuthGuard implements CanActivate {
  // ... 现有代码 ...
  
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const handler = context.getHandler();
    const classRef = context.getClass();
    try {
      // 检查是否为公开接口
      const isPublic = this.reflector.getAllAndOverride<boolean>(
        IS_PUBLIC_KEY,
        [handler, classRef],
      );

      if (isPublic) {
        this.logger.debug(`公开接口访问: ${request.method} ${request.url}`);
        return true;
      }

      // 获取认证策略
      const authStrategy = this.reflector.getAllAndOverride<AuthStrategy>(
        AUTH_STRATEGY_KEY,
        [handler, classRef],
      );
      
      // 执行认证
      let authResult: AuthResult;

      if (authStrategy === AuthStrategy.API_KEY) {
        // 尝试使用签名认证
        const authInfo = this.extractAuthInfo(request);
        
        if (authInfo) {
          // 使用新的签名认证方式
          this.logger.debug(`使用签名认证策略: ${request.method} ${request.url}`);
          authResult = await this.validateSignatureAuth(authInfo, request);
        } else {
          // 回退到旧的API密钥认证方式（过渡期使用）
          this.logger.debug(`使用API密钥认证策略: ${request.method} ${request.url}`);
          const apiKey = this.extractApiKey(request);
          const secretKey = this.extractSecretKey(request);

          if (!apiKey || !secretKey) {
            throw new UnauthorizedException('API密钥认证失败: 缺少必要的认证信息');
          }

          authResult = await this.validateApiKeyWithCircuitBreaker(apiKey, secretKey);
        }
      } else {
        // 默认使用JWT认证策略
        this.logger.debug(`使用JWT认证策略: ${request.method} ${request.url}`);
        const jwtToken = this.extractJwtToken(request);

        if (!jwtToken) {
          throw new UnauthorizedException('JWT认证失败: 缺少令牌');
        }

        authResult = await this.validateJwtWithCircuitBreaker(jwtToken);
      }

      if (!authResult.isValid) {
        this.logger.warn(
          `认证失败: ${authResult.error} - ${request.method} ${request.url}`,
        );
        throw new UnauthorizedException(authResult.error || '认证失败');
      }
      
      // 设置请求上下文
      request.user = authResult.user;
      if (authResult.apiKey) {
        request.apiKey = authResult.apiKey;
      }
      
      this.logger.debug(
        `认证成功: ${authResult.authType} - 用户ID: ${authResult.user!.id}`,
      );
      return true;
    } catch (error) {
      this.logger.error(`认证守卫错误: ${error.message}`, error.stack);
      if (
        error instanceof UnauthorizedException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }
      throw new UnauthorizedException('认证服务异常');
    }
  }
  
  // 从请求中提取签名认证信息
  private extractAuthInfo(request: Request): AuthInfo | null {
    const apiKey = request.headers['x-api-key'] as string;
    const timestamp = request.headers['x-timestamp'] as string;
    const nonce = request.headers['x-nonce'] as string;
    const signature = request.headers['x-signature'] as string;
    
    if (!apiKey || !timestamp || !nonce || !signature) {
      return null;
    }
    
    return { apiKey, timestamp, nonce, signature };
  }
  
  // 验证签名认证
  private async validateSignatureAuth(
    authInfo: AuthInfo,
    request: Request
  ): Promise<AuthResult> {
    try {
      // 1. 验证时间戳是否在有效期内（15分钟）
      const currentTime = Math.floor(Date.now() / 1000);
      const requestTime = parseInt(authInfo.timestamp, 10);
      
      if (isNaN(requestTime) || currentTime - requestTime > 15 * 60) {
        this.logger.warn(`请求已过期: ${currentTime - requestTime}秒`);
        return { isValid: false, error: '请求已过期' };
      }
      
      // 2. 验证nonce是否已使用（防止重放攻击）
      const nonceKey = `auth:nonce:${authInfo.apiKey}:${authInfo.nonce}`;
      const nonceExists = await this.redisService.get(nonceKey);
      
      if (nonceExists) {
        this.logger.warn(`Nonce已被使用: ${authInfo.nonce}`);
        return { isValid: false, error: '重复的请求标识' };
      }
      
      // 3. 查找API密钥对应的Secret Key
      const apiKeyEntity = await this.apiKeyService.findByApiKey(authInfo.apiKey);
      
      if (!apiKeyEntity) {
        this.logger.warn(`API密钥不存在: ${authInfo.apiKey}`);
        return { isValid: false, error: 'API密钥不存在' };
      }
      
      if (apiKeyEntity.keyStatus !== ApiKeyStatus.ACTIVE) {
        this.logger.warn(
          `API密钥非活跃状态: ${authInfo.apiKey}, 状态: ${apiKeyEntity.keyStatus}`,
        );
        return { isValid: false, error: 'API密钥非活跃状态' };
      }
      
      // 4. 获取Secret Key（可能需要解密）
      const secretKey = await this.apiKeyService.getSecretKeyForApiKey(apiKeyEntity.id);
      
      if (!secretKey) {
        this.logger.warn(`无法获取Secret Key: ${authInfo.apiKey}`);
        return { isValid: false, error: '无法获取密钥信息' };
      }
      
      // 5. 重建规范化请求字符串
      const canonicalRequest = this.buildCanonicalRequest(request, authInfo.timestamp);
      
      // 6. 计算签名
      const calculatedSignature = this.generateSignature(canonicalRequest, secretKey);
      
      // 7. 安全比较签名（防止时序攻击）
      const isSignatureValid = this.compareSignatures(authInfo.signature, calculatedSignature);
      
      if (!isSignatureValid) {
        this.logger.warn(`签名验证失败: ${authInfo.apiKey}`);
        return { isValid: false, error: '签名验证失败' };
      }
      
      // 8. 存储nonce（设置15分钟过期）
      await this.redisService.set(nonceKey, '1', 15 * 60);
      
      // 9. 构建API密钥数据
      const apiKeyData: AuthApiKey = {
        id: apiKeyEntity.id,
        userId: apiKeyEntity.userId,
        serviceId: apiKeyEntity.serviceId,
        name: apiKeyEntity.name,
        keyStatus: apiKeyEntity.keyStatus,
        keyType: apiKeyEntity.keyType,
        permissions: apiKeyEntity.permissions,
        expiresAt: apiKeyEntity.expiresAt,
        lastUsedAt: apiKeyEntity.lastUsedAt,
        createdAt: apiKeyEntity.createdAt,
        updatedAt: apiKeyEntity.updatedAt,
      };
      
      // 10. 验证成功
      this.logger.log(
        `签名验证成功: ${authInfo.apiKey}, 用户ID: ${apiKeyEntity.userId}`,
      );
      return {
        isValid: true,
        authType: 'api-key-signature',
        apiKey: apiKeyData,
        user: { id: apiKeyEntity.userId } as AuthUser,
      };
    } catch (error) {
      this.logger.error(`签名验证失败: ${error.message}`, error.stack);
      return { isValid: false, error: '签名验证过程中发生错误' };
    }
  }
  
  // 构建规范化请求字符串
  private buildCanonicalRequest(request: Request, timestamp: string): string {
    const method = request.method;
    const path = request.path;
    
    // 排序查询参数
    const queryParams = { ...request.query };
    const sortedQuery = Object.keys(queryParams)
      .sort()
      .map(key => `${key}=${encodeURIComponent(queryParams[key] as string)}`)
      .join('&');
    
    // 获取请求体
    let bodyString = '';
    if (request.body) {
      if (typeof request.body === 'string') {
        bodyString = request.body;
      } else if (Buffer.isBuffer(request.body)) {
        // 对于二进制数据，使用长度信息
        bodyString = `binary:${request.body.length}`;
      } else if (request.is('multipart/form-data')) {
        // 对于文件上传，我们只使用文件名和大小进行签名
        bodyString = 'multipart-form-data';
      } else {
        bodyString = JSON.stringify(request.body);
      }
    }
    
    return [
      method.toUpperCase(),
      path,
      sortedQuery,
      bodyString,
      timestamp
    ].join('\n');
  }
  
  // 生成签名
  private generateSignature(canonicalRequest: string, secretKey: string): string {
    const hmac = crypto.createHmac('sha256', secretKey);
    hmac.update(canonicalRequest);
    return hmac.digest('base64');
  }
  
  // 安全比较签名（防止时序攻击）
  private compareSignatures(sig1: string, sig2: string): boolean {
    try {
      if (sig1.length !== sig2.length) {
        return false;
      }
      
      return crypto.timingSafeEqual(
        Buffer.from(sig1),
        Buffer.from(sig2)
      );
    } catch (error) {
      this.logger.error(`签名比较失败: ${error.message}`);
      return false;
    }
  }
  
  // ... 现有代码 ...
}
```

### 添加 ApiKeyService 新方法

```typescript
// api-key.service.ts

@Injectable()
export class ApiKeyService {
  // ... 现有代码 ...
  
  /**
   * 获取API密钥对应的Secret Key
   * @param apiKeyId API密钥ID
   * @returns Secret Key或null
   */
  async getSecretKeyForApiKey(apiKeyId: number): Promise<string | null> {
    try {
      const apiKeyEntity = await this.apiKeyRepository.findOne({
        where: { id: apiKeyId }
      });
      
      if (!apiKeyEntity) {
        return null;
      }
      
      // 如果存储了临时明文密钥
      if (apiKeyEntity.tempSecretKey) {
        return apiKeyEntity.tempSecretKey;
      }
      
      // 否则返回null，因为我们无法从哈希中恢复原始密钥
      // 这种情况下需要用户重新生成密钥
      return null;
    } catch (error) {
      this.logger.error(`获取Secret Key失败: ${error.message}`, error.stack);
      return null;
    }
  }
}
```

## 实施建议

### 分阶段实施

1. **第一阶段：双模式支持**
   - 实现签名认证机制，但保留原有API密钥认证方式
   - 前端API测试工具支持新旧两种认证方式
   - 后端同时支持两种认证方式，优先使用签名认证

2. **第二阶段：完全切换**
   - 前端完全切换到签名认证
   - 后端保留旧认证方式一段时间，但发出警告
   - 设定截止日期，之后完全移除旧认证方式

3. **第三阶段：强化安全**
   - 实现密钥定期轮换机制
   - 增加异常检测和告警系统
   - 添加请求频率限制和IP白名单功能

### 特殊场景处理

1. **文件上传请求**
   - 对于文件上传请求，只对元数据进行签名，不包括文件内容
   - 可以使用文件名和大小作为签名的一部分

2. **大型请求体**
   - 对于超大型请求体，可以只对请求体的哈希值进行签名
   - 设置合理的请求体大小限制

3. **WebSocket连接**
   - 对于WebSocket连接，在握手阶段进行签名验证
   - 可以使用JWT作为后续消息的认证方式

### 安全最佳实践

1. **密钥管理**
   - 不在前端存储Secret Key，仅在使用时从安全存储中获取
   - 实现密钥定期轮换机制
   - 支持紧急撤销和重新生成密钥

2. **监控与告警**
   - 记录所有认证失败事件
   - 对异常模式（如短时间内多次失败）进行告警
   - 实现自动阻止可疑IP地址的机制

3. **安全审计**
   - 记录所有API调用的详细日志
   - 定期审查访问模式和异常情况
   - 实现可追溯性，确保每个请求都可以追溯到特定用户

## 结论

基于HMAC签名的API认证方案可以有效解决当前系统中的安全隐患，同时保持API调用的便捷性。通过分阶段实施，可以平稳过渡到新的认证机制，最大限度地减少对现有系统的影响。

该方案符合行业安全最佳实践，可以有效防止API密钥泄露、中间人攻击和重放攻击等常见安全威胁。 