/**
 * 服务注册配置
 * 定义系统中所有可用的服务及其配置
 */

// 服务定义接口
export interface ServiceDefinition {
  id: number;              // 服务ID
  code: string;            // 服务代码
  name: string;            // 服务名称
  baseUrl: string;         // 基础URL
  enabled: boolean;        // 是否启用
  cacheable: boolean;      // 是否可缓存
  cacheTimeout: number;    // 缓存超时时间(秒)
  processingModes: ('sync' | 'async')[];  // 支持的处理模式
  defaultMode: 'sync' | 'async';          // 默认处理模式
  queueName?: string;      // 关联的队列名称(用于异步处理)
  timeout: number;         // 请求超时时间(毫秒)
  healthEndpoint?: string; // 健康检查端点
  rateLimits?: {           // 速率限制
    defaultLimit: number;  // 默认限制(每分钟请求数)
    burstLimit: number;    // 突发限制
  };
}

/**
 * 服务配置映射
 * 按服务代码索引的服务配置
 */
export const SERVICE_REGISTRY: Record<string, ServiceDefinition> = {
  // OCR识别服务
  ocr: {
    id: 1,
    code: 'ocr',
    name: '智能OCR识别',
    baseUrl: process.env.OCR_SERVICE_URL || 'http://127.0.0.1:8866',
    enabled: true,
    cacheable: true,
    cacheTimeout: 300, // 5分钟
    processingModes: ['sync', 'async'],
    defaultMode: 'sync',
    queueName: 'ocr',
    timeout: 600000, // 增加到10分钟，OCR处理大图片可能需要较长时间
    healthEndpoint: '/health',
    rateLimits: {
      defaultLimit: 60,
      burstLimit: 100
    }
  },
  
  // 地址提取服务
  'address': {
    id: 2,
    code: 'address',
    name: '地址提取服务',
    baseUrl: 'http://127.0.0.1:8866',
    enabled: true,
    cacheable: true,
    cacheTimeout: 600, // 10分钟
    processingModes: ['sync', 'async'],
    defaultMode: 'sync',
    queueName: 'extract-address',
    timeout: 45000, // 45秒
    healthEndpoint: '/health',
    rateLimits: {
      defaultLimit: 120,
      burstLimit: 200
    }
  },
  
  // 坐标逆解析服务
  'geo': {
    id: 3,
    code: 'geo',
    name: '坐标逆解析服务',
    baseUrl: 'http://127.0.0.1:8866',
    enabled: true,
    cacheable: true,
    cacheTimeout: 600, // 10分钟
    processingModes: ['sync', 'async'],
    defaultMode: 'sync',
    queueName: 'rev-geo',
    timeout: 30000, // 30秒
    healthEndpoint: '/health',
    rateLimits: {
      defaultLimit: 150,
      burstLimit: 250
    }
  }
};

/**
 * 根据服务代码获取服务ID
 * @param serviceCode 服务代码
 * @returns 服务ID或undefined
 */
export function getServiceIdByCode(serviceCode: string): number | undefined {
  return SERVICE_REGISTRY[serviceCode]?.id;
}

/**
 * 根据服务ID获取服务代码
 * @param serviceId 服务ID
 * @returns 服务代码或undefined
 */
export function getServiceCodeById(serviceId: number): string | undefined {
  const service = Object.values(SERVICE_REGISTRY).find(s => s.id === serviceId);
  return service?.code;
}

/**
 * 获取所有可用服务
 * @returns 可用服务列表
 */
export function getEnabledServices(): ServiceDefinition[] {
  return Object.values(SERVICE_REGISTRY).filter(service => service.enabled);
}

/**
 * 获取服务配置
 * @param serviceCode 服务代码
 * @returns 服务配置或undefined
 */
export function getServiceConfig(serviceCode: string): ServiceDefinition | undefined {
  return SERVICE_REGISTRY[serviceCode];
} 