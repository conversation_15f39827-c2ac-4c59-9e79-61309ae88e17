// 订单状态
export type OrderStatus = 'pending' | 'paid' | 'processing' | 'completed' | 'cancelled' | 'refunded'

// 订单类型
export type OrderType = 'service' | 'package' | 'recharge'

// 支付状态
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded'

// 支付方式
export type PaymentMethod = 'alipay' | 'wechat' | 'bank_card' | 'balance'

// 订单项
export interface OrderItem {
  id: string
  serviceId?: string
  serviceName?: string
  packageId?: string
  packageName?: string
  quantity: number
  unitPrice: number
  totalPrice: number
  description?: string
}

// 订单信息
export interface Order {
  id: string
  orderNo: string
  userId: string
  type: OrderType
  status: OrderStatus
  paymentStatus: PaymentStatus
  paymentMethod?: PaymentMethod
  items: OrderItem[]
  totalAmount: number
  discountAmount?: number
  finalAmount: number
  currency: string
  description?: string
  remark?: string
  createdAt: string
  updatedAt: string
  paidAt?: string
  completedAt?: string
  cancelledAt?: string
  refundedAt?: string
  expiredAt?: string
}

// 订单统计
export interface OrderStats {
  totalOrders: number
  pendingOrders: number
  paidOrders: number
  completedOrders: number
  cancelledOrders: number
  totalAmount: number
  paidAmount: number
  refundedAmount: number
}

// 订单查询参数
export interface OrderQueryParams {
  page?: number
  pageSize?: number
  status?: OrderStatus
  type?: OrderType
  paymentStatus?: PaymentStatus
  paymentMethod?: PaymentMethod
  startDate?: string
  endDate?: string
  keyword?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 创建订单表单
export interface CreateOrderForm {
  type: OrderType
  items: Omit<OrderItem, 'id'>[]
  paymentMethod?: PaymentMethod
  description?: string
  remark?: string
}

// 订单支付信息
export interface OrderPayment {
  orderId: string
  paymentMethod: PaymentMethod
  amount: number
  currency: string
  paymentUrl?: string
  qrCode?: string
  expiredAt?: string
}

// 订单详情（扩展订单信息）
export interface OrderDetail extends Order {
  paymentRecords?: {
    id: string
    paymentMethod: PaymentMethod
    amount: number
    status: PaymentStatus
    createdAt: string
    completedAt?: string
    failureReason?: string
  }[]
  refundRecords?: {
    id: string
    amount: number
    reason: string
    status: 'pending' | 'approved' | 'rejected' | 'completed'
    createdAt: string
    completedAt?: string
  }[]
  deliveryInfo?: {
    status: 'pending' | 'processing' | 'delivered' | 'failed'
    trackingNumber?: string
    estimatedDelivery?: string
    actualDelivery?: string
  }
}

// 订单退款信息
export interface OrderRefund {
  id: string
  orderId: string
  amount: number
  reason: string
  status: 'pending' | 'approved' | 'rejected' | 'completed'
  createdAt: string
  processedAt?: string
  remark?: string
}