#!/usr/bin/env ts-node

import { DataSource } from 'typeorm';
import { createDataSourceConfig } from '../src/database/config/database.config';
import { seedManager } from '../src/database/seeds';
import { Logger } from '@nestjs/common';

/**
 * 种子数据初始化脚本
 * 创建完整的业务数据：用户、服务、API密钥、用户服务关联
 */
class SeedInitializer {
  private readonly logger = new Logger('SeedInitializer');
  private dataSource: DataSource;
  
  constructor() {
    this.dataSource = new DataSource(createDataSourceConfig());
  }
  
  async initialize(): Promise<void> {
    try {
      this.logger.log('开始种子数据初始化...');
      
      // 连接数据库
      await this.connectDatabase();
      
      // 列出所有种子数据
      await this.listSeeds();
      
      // 执行种子数据
      await this.runSeeds();
      
      // 验证数据
      await this.verifyData();
      
      this.logger.log('种子数据初始化完成');
      
    } catch (error) {
      this.logger.error(`种子数据初始化失败: ${error.message}`);
      console.error(error);
      process.exit(1);
    } finally {
      if (this.dataSource.isInitialized) {
        await this.dataSource.destroy();
      }
    }
  }
  
  private async connectDatabase(): Promise<void> {
    this.logger.log('连接数据库...');
    await this.dataSource.initialize();
    this.logger.log('数据库连接成功');
  }
  
  private async listSeeds(): Promise<void> {
    const seeds = seedManager.getAll();
    
    console.log('\n📋 种子数据列表:');
    console.log('─'.repeat(100));
    console.log('名称'.padEnd(20) + '描述'.padEnd(40) + '依赖'.padEnd(20) + '版本');
    console.log('─'.repeat(100));
    
    seeds.forEach(seed => {
      const name = seed.config.name.padEnd(20);
      const description = seed.config.description.padEnd(40);
      const dependencies = seed.config.dependencies.join(',').padEnd(20);
      const version = seed.config.version;
      
      console.log(`${name}${description}${dependencies}${version}`);
    });
    
    console.log('─'.repeat(100));
    console.log(`总计: ${seeds.length} 个种子数据\n`);
  }
  
  private async runSeeds(): Promise<void> {
    this.logger.log('执行种子数据...');
    
    const results = await seedManager.run(this.dataSource, {
      environment: process.env.NODE_ENV || 'development',
      verbose: true,
    });
    
    console.log('\n🚀 执行结果:');
    console.log('─'.repeat(80));
    
    results.forEach(result => {
      const status = result.success ? '✅ 成功' : '❌ 失败';
      console.log(`${status} ${result.message}`);
      
      if (result.affectedRows !== undefined) {
        console.log(`   影响行数: ${result.affectedRows}`);
      }
      
      if (result.executionTime !== undefined) {
        console.log(`   执行时间: ${result.executionTime}ms`);
      }
      
      if (result.error) {
        console.log(`   错误信息: ${result.error}`);
      }
      
      console.log('');
    });
    
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log('─'.repeat(80));
    console.log(`总计: 成功 ${successful}, 失败 ${failed}\n`);
  }
  
  private async verifyData(): Promise<void> {
    this.logger.log('验证创建的数据...');
    
    try {
      // 验证用户数据
      const userCount = await this.dataSource.query('SELECT COUNT(*) as count FROM open_user');
      console.log(`✅ 用户数据: ${userCount[0].count} 个用户`);

      // 验证服务数据
      const serviceCount = await this.dataSource.query('SELECT COUNT(*) as count FROM open_service');
      console.log(`✅ 服务数据: ${serviceCount[0].count} 个服务`);

      // 验证API密钥数据
      const apiKeyCount = await this.dataSource.query('SELECT COUNT(*) as count FROM api_keys');
      console.log(`✅ API密钥数据: ${apiKeyCount[0].count} 个密钥`);

      // 验证用户服务关联数据
      const userServiceCount = await this.dataSource.query('SELECT COUNT(*) as count FROM user_service');
      console.log(`✅ 用户服务关联: ${userServiceCount[0].count} 个关联`);
      
      // 显示用户详情
      const users = await this.dataSource.query(`
        SELECT username, email, role, tier, balance,
               (SELECT COUNT(*) FROM api_keys WHERE user_id = open_user.id) as api_key_count,
               (SELECT COUNT(*) FROM user_service WHERE user_id = open_user.id) as service_count
        FROM open_user
      `);
      
      console.log('\n👥 用户详情:');
      console.log('─'.repeat(100));
      console.log('用户名'.padEnd(15) + '邮箱'.padEnd(25) + '角色'.padEnd(10) + '等级'.padEnd(12) + '余额'.padEnd(10) + 'API密钥'.padEnd(8) + '服务数');
      console.log('─'.repeat(100));
      
      users.forEach(user => {
        const username = user.username.padEnd(15);
        const email = user.email.padEnd(25);
        const role = user.role.padEnd(10);
        const tier = user.tier.padEnd(12);
        const balance = Number(user.balance || 0).toFixed(2).padEnd(10);
        const apiKeyCount = user.api_key_count.toString().padEnd(8);
        const serviceCount = user.service_count.toString();
        
        console.log(`${username}${email}${role}${tier}${balance}${apiKeyCount}${serviceCount}`);
      });
      
      console.log('─'.repeat(100));
      
      // 显示服务详情
      const services = await this.dataSource.query(`
        SELECT code, name, type, status, unitPrice,
               (SELECT COUNT(*) FROM user_service WHERE service_id = open_service.id) as user_count
        FROM open_service
        ORDER BY sortOrder
      `);
      
      console.log('\n🔧 服务详情:');
      console.log('─'.repeat(100));
      console.log('代码'.padEnd(20) + '名称'.padEnd(20) + '类型'.padEnd(15) + '状态'.padEnd(10) + '单价'.padEnd(10) + '用户数');
      console.log('─'.repeat(100));
      
      services.forEach(service => {
        const code = service.code.padEnd(20);
        const name = service.name.padEnd(20);
        const type = service.type.padEnd(15);
        const status = service.status.padEnd(10);
        const unitPrice = Number(service.unitPrice || 0).toFixed(3).padEnd(10);
        const userCount = service.user_count.toString();
        
        console.log(`${code}${name}${type}${status}${unitPrice}${userCount}`);
      });
      
      console.log('─'.repeat(100));
      
    } catch (error) {
      this.logger.error(`验证数据失败: ${error.message}`);
    }
  }
}

async function main() {
  const initializer = new SeedInitializer();
  await initializer.initialize();
}

if (require.main === module) {
  main().catch(console.error);
}
