import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallH<PERSON>ler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Reflector } from '@nestjs/core';
import { ApiResponseDto } from '../dto/api-response.dto';

/**
 * 统一响应格式拦截器
 * 将所有成功响应包装为统一格式
 */
@Injectable()
export class ResponseInterceptor implements NestInterceptor {
  private readonly logger = new Logger(ResponseInterceptor.name);

  constructor(private reflector: Reflector) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const requestId = request.headers['x-request-id'] || request.id;

    // 检查是否跳过响应包装
    const skipWrapper = this.reflector.get<boolean>(
      'skip-response-wrapper',
      context.getHandler(),
    );

    if (skipWrapper) {
      return next.handle();
    }

    return next.handle().pipe(
      map((data) => {
        // 如果已经是ApiResponseDto格式，直接返回
        if (data instanceof ApiResponseDto) {
          return data;
        }

        // 如果是SSE响应或文件下载，不包装
        const response = context.switchToHttp().getResponse();
        const contentType = response.getHeader('content-type');
        if (
          contentType &&
          (contentType.includes('text/event-stream') ||
            contentType.includes('application/octet-stream') ||
            contentType.includes('application/pdf') ||
            contentType.includes('image/'))
        ) {
          return data;
        }

        // 如果已经是标准响应格式（包含success、code、message、data字段），直接返回
        if (
          data &&
          typeof data === 'object' &&
          'success' in data &&
          'code' in data &&
          'message' in data &&
          'data' in data
        ) {
          return data;
        }

        // 如果已经是标准响应格式（包含success、code、message字段，但没有data字段），也直接返回
        if (
          data &&
          typeof data === 'object' &&
          'success' in data &&
          'code' in data &&
          'message' in data &&
          'timestamp' in data &&
          'requestId' in data
        ) {
          return data;
        }

        // 包装为统一响应格式
        return ApiResponseDto.success(data, '操作成功', requestId);
      }),
    );
  }
}

/**
 * 跳过响应包装装饰器
 * 用于标记不需要包装的端点（如文件下载、SSE等）
 */
import { SetMetadata } from '@nestjs/common';

export const SkipResponseWrapper = () => SetMetadata('skip-response-wrapper', true);

/**
 * 自定义响应消息装饰器
 */
export const ResponseMessage = (message: string) =>
  SetMetadata('response-message', message);

/**
 * 增强版响应拦截器，支持自定义消息
 */
@Injectable()
export class EnhancedResponseInterceptor implements NestInterceptor {
  private readonly logger = new Logger(EnhancedResponseInterceptor.name);

  constructor(private reflector: Reflector) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const requestId = request.headers['x-request-id'] || request.id;

    // 检查是否跳过响应包装
    const skipWrapper = this.reflector.get<boolean>(
      'skip-response-wrapper',
      context.getHandler(),
    );

    if (skipWrapper) {
      return next.handle();
    }

    // 获取自定义响应消息
    const customMessage = this.reflector.get<string>(
      'response-message',
      context.getHandler(),
    );

    return next.handle().pipe(
      map((data) => {
        // 如果已经是ApiResponseDto格式，直接返回
        if (data instanceof ApiResponseDto) {
          return data;
        }

        // 如果是SSE响应或文件下载，不包装
        const response = context.switchToHttp().getResponse();
        const contentType = response.getHeader('content-type');
        if (
          contentType &&
          (contentType.includes('text/event-stream') ||
            contentType.includes('application/octet-stream') ||
            contentType.includes('application/pdf') ||
            contentType.includes('image/'))
        ) {
          return data;
        }

        // 根据HTTP方法确定默认消息
        const method = request.method;
        let defaultMessage = '操作成功';
        
        switch (method) {
          case 'POST':
            defaultMessage = '创建成功';
            break;
          case 'PUT':
          case 'PATCH':
            defaultMessage = '更新成功';
            break;
          case 'DELETE':
            defaultMessage = '删除成功';
            break;
          case 'GET':
            defaultMessage = '查询成功';
            break;
        }

        const message = customMessage || defaultMessage;

        // 包装为统一响应格式
        return ApiResponseDto.success(data, message, requestId);
      }),
    );
  }
}

/**
 * 性能监控拦截器
 * 记录请求处理时间
 */
@Injectable()
export class PerformanceInterceptor implements NestInterceptor {
  private readonly logger = new Logger(PerformanceInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const startTime = Date.now();
    const { method, url } = request;

    return next.handle().pipe(
      map((data) => {
        const duration = Date.now() - startTime;
        
        // 记录慢请求（超过1秒）
        if (duration > 1000) {
          this.logger.warn(
            `慢请求检测: ${method} ${url} 耗时 ${duration}ms`,
            {
              method,
              url,
              duration,
              userAgent: request.headers['user-agent'],
              ip: request.ip,
            },
          );
        }

        // 在开发环境记录所有请求时间
        if (process.env.NODE_ENV === 'development') {
          this.logger.debug(`${method} ${url} - ${duration}ms`);
        }

        return data;
      }),
    );
  }
}

/**
 * 请求ID生成拦截器
 * 为每个请求生成唯一ID用于追踪
 */
@Injectable()
export class RequestIdInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    // 生成请求ID（如果请求头中没有）
    if (!request.headers['x-request-id']) {
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      request.headers['x-request-id'] = requestId;
      request.id = requestId;
    }

    // 在响应头中返回请求ID
    response.setHeader('X-Request-ID', request.headers['x-request-id']);

    return next.handle();
  }
}
