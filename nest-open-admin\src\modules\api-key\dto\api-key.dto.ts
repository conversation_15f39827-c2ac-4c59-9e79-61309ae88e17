import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsBoolean,
  MinLength,
  MaxLength,
  Min,
  IsInt,
  Max,
  IsIn,
  IsArray,
  IsDateString,
  IsDate,
  ValidateIf,
  IsUUID,
} from 'class-validator';
import { ApiKeyStatus } from '../entities/api-key.entity';
import { PaginatedResponseDto } from '@/common/dto/index.dto';

/**
 * 创建API密钥DTO
 */
export class CreateApiKeyDto {
  @ApiProperty({ description: '用户ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  userId: number;

  @ApiProperty({ description: '服务ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  serviceId: number;

  @ApiProperty({ description: '密钥名称', example: 'My API Key' })
  @IsString()
  @IsNotEmpty()
  @MinLength(2)
  @MaxLength(100)
  name: string;

  @ApiPropertyOptional({ description: '密钥描述', example: '用于生产环境的API密钥' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({ 
    description: '密钥类型', 
    enum: ['trial', 'basic', 'premium', 'enterprise'],
    default: 'trial'
  })
  @IsOptional()
  @IsEnum(['trial', 'basic', 'premium', 'enterprise'])
  keyType?: string;

  @ApiPropertyOptional({ description: '权限范围', example: ['read', 'write'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  permissions?: string[];

  @ApiPropertyOptional({ description: '过期时间' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  expiresAt?: Date;
}

/**
 * 更新API密钥DTO
 */
export class UpdateApiKeyDto {
  @ApiPropertyOptional({ description: '密钥名称', example: 'Updated API Key' })
  @IsOptional()
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  name?: string;

  @ApiPropertyOptional({ description: '密钥描述', example: '更新后的API密钥描述' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({ description: '密钥状态', enum: ApiKeyStatus })
  @IsOptional()
  @IsEnum(ApiKeyStatus)
  keyStatus?: ApiKeyStatus;

  @ApiPropertyOptional({ 
    description: '密钥类型', 
    enum: ['trial', 'basic', 'premium', 'enterprise']
  })
  @IsOptional()
  @IsEnum(['trial', 'basic', 'premium', 'enterprise'])
  keyType?: string;

  @ApiPropertyOptional({ description: '权限范围', example: ['read', 'write', 'admin'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  permissions?: string[];

  @ApiPropertyOptional({ description: '过期时间' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  expiresAt?: Date;
}

/**
 * API密钥查询DTO
 */
export class QueryApiKeyDto {
  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', example: 10, minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: '用户ID', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  userId?: number;

  @ApiPropertyOptional({ description: '服务ID', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  serviceId?: number;

  @ApiPropertyOptional({ description: '密钥名称搜索', example: 'API' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: '密钥状态', enum: ApiKeyStatus })
  @IsOptional()
  @IsEnum(ApiKeyStatus)
  keyStatus?: ApiKeyStatus;

  @ApiPropertyOptional({ 
    description: '密钥类型', 
    enum: ['trial', 'basic', 'premium', 'enterprise']
  })
  @IsOptional()
  @IsEnum(['trial', 'basic', 'premium', 'enterprise'])
  keyType?: string;

  @ApiPropertyOptional({ description: '是否已过期' })
  @IsOptional()
  @IsBoolean()
  isExpired?: boolean;

  @ApiPropertyOptional({ description: '排序字段', example: 'createdAt' })
  @IsOptional()
  @IsString()
  @IsIn(['id', 'name', 'keyStatus', 'keyType', 'createdAt', 'updatedAt', 'lastUsedAt', 'expiresAt'])
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', example: 'DESC' })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}

/**
 * 重新生成API密钥DTO
 */
export class RegenerateApiKeyDto {
  @ApiProperty({ description: 'API密钥ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  id: number;

  @ApiPropertyOptional({ description: '新的过期时间' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  expiresAt?: Date;
}

/**
 * API密钥响应DTO
 */
export class ApiKeyResponseDto {
  @ApiProperty({ description: 'API密钥ID', example: 1 })
  id: number;

  @ApiProperty({ description: '用户ID', example: 1 })
  userId: number;

  @ApiProperty({ description: '服务ID', example: 1 })
  serviceId: number;

  @ApiProperty({ description: '密钥名称', example: 'My API Key' })
  name: string;

  @ApiProperty({ description: 'API密钥（仅创建时返回）', example: 'ak_1234567890abcdef' })
  apiKey?: string;

  @ApiProperty({ description: 'API密钥秘钥（仅创建时返回）', example: 'sk_abcdef1234567890' })
  secretKey?: string;

  @ApiProperty({ description: '密钥状态', enum: ApiKeyStatus })
  keyStatus: ApiKeyStatus;

  @ApiProperty({ description: '密钥类型', example: 'basic' })
  keyType: string;

  @ApiPropertyOptional({ description: '密钥描述' })
  description?: string;

  @ApiPropertyOptional({ description: '权限范围' })
  permissions?: string[];

  @ApiPropertyOptional({ description: '过期时间' })
  expiresAt?: Date;

  @ApiPropertyOptional({ description: '最后使用时间' })
  lastUsedAt?: Date;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiProperty({ description: '密钥是否已被查看过', example: false })
  isSecretViewed: boolean;

  // 关联信息
  @ApiPropertyOptional({ description: '用户信息' })
  user?: {
    id: number;
    username: string;
    email: string;
  };

  @ApiPropertyOptional({ description: '服务信息' })
  service?: {
    id: number;
    code: string;
    name: string;
    type: string;
  };
}

/**
 * API密钥列表响应DTO
 */
export class ApiKeyListResponseDto extends PaginatedResponseDto<ApiKeyResponseDto> {
  @ApiProperty({
    description: 'API密钥列表',
    type: [ApiKeyResponseDto],
  })
  items: ApiKeyResponseDto[];
}

/**
 * API密钥统计DTO
 */
export class ApiKeyStatsDto {
  @ApiProperty({ description: '用户ID' })
  userId: number;

  @ApiProperty({ description: 'API密钥总数' })
  totalCount: number;

  @ApiProperty({ description: '活跃的API密钥数量' })
  activeCount: number;

  @ApiProperty({ description: '过期的API密钥数量' })
  expiredCount: number;

  @ApiProperty({ description: '已撤销的API密钥数量' })
  revokedCount: number;
}