import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  UseGuards,
  Request,
  ForbiddenException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { OrderService } from './order.service';
import { RechargeService } from './services/recharge.service';
import {
  CreateServiceOrderDto,
  QueryOrderDto,
  UpdateOrderDto,
  OrderResponseDto,
  OrderListResponseDto,
  OrderStatsDto,
} from './dto/order.dto';
import {
  CreateRechargeOrderDto,
  RechargeConfigDto,
  RechargeStatsDto,
  UserRechargeRecordDto,
} from './dto/recharge.dto';
import { UnifiedAuthGuard } from '@/common/guards/unified-auth.guard';
import { AuthPermission } from '@/common/decorators/auth-permission.decorator';
import { ApiResult } from '@/common/decorators/api-result.decorator';
import { Public } from '@/common/decorators/public.decorator';

@ApiTags('订单管理')
@Controller('orders')
@UseGuards(UnifiedAuthGuard)
@ApiBearerAuth()
export class OrderController {
  constructor(
    private readonly orderService: OrderService,
    private readonly rechargeService: RechargeService,
  ) {}

  @Post('service')
  @ApiOperation({ summary: '创建服务订单' })
  @ApiBody({ type: CreateServiceOrderDto })
  @ApiResponse({ status: 201, type: OrderResponseDto })
  @ApiResult()
  async createServiceOrder(
    @Request() req,
    @Body() createOrderDto: CreateServiceOrderDto,
  ): Promise<OrderResponseDto> {
    const userId = req.user?.id;
    if (!userId) {
      throw new ForbiddenException('用户未登录');
    }
    return this.orderService.createServiceOrder(userId, createOrderDto);
  }

  @Post('recharge')
  @ApiOperation({ summary: '创建充值订单' })
  @ApiBody({ type: CreateRechargeOrderDto })
  @ApiResponse({ status: 201, type: OrderResponseDto })
  @ApiResult()
  async createRechargeOrder(
    @Request() req,
    @Body() createOrderDto: CreateRechargeOrderDto,
  ): Promise<OrderResponseDto> {
    const userId = req.user?.id;
    if (!userId) {
      throw new ForbiddenException('用户未登录');
    }
    const order = await this.rechargeService.createRechargeOrder(userId, createOrderDto);
    return this.orderService.toOrderResponseDto(order);
  }

  @Get()
  @ApiOperation({ summary: '查询订单列表' })
  @ApiQuery({ type: QueryOrderDto })
  @ApiResponse({ status: 200, type: OrderListResponseDto })
  @ApiResult()
  async findAll(
    @Request() req,
    @Query() queryDto: QueryOrderDto,
  ): Promise<OrderListResponseDto> {
    // 普通用户只能查看自己的订单
    if (!req.user?.permissions?.includes('order:read-all')) {
      queryDto.userId = req.user?.id;
    }
    return this.orderService.findAll(queryDto);
  }

  @Get('stats')
  @AuthPermission('order:read-stats')
  @ApiOperation({ summary: '获取订单统计信息' })
  @ApiResponse({ status: 200, type: OrderStatsDto })
  @ApiResult()
  async getStats(): Promise<OrderStatsDto> {
    return this.orderService.getStats();
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID查询订单详情' })
  @ApiParam({ name: 'id', description: '订单ID', type: Number })
  @ApiResponse({ status: 200, type: OrderResponseDto })
  @ApiResult()
  async findOne(
    @Request() req,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<OrderResponseDto> {
    const order = await this.orderService.findById(id);
    
    // 普通用户只能查看自己的订单
    if (!req.user?.permissions?.includes('order:read-all') && order.userId !== req.user?.id) {
      throw new ForbiddenException('无权查看此订单');
    }
    
    return order;
  }

  @Get('order-no/:orderNo')
  @ApiOperation({ summary: '根据订单号查询订单详情' })
  @ApiParam({ name: 'orderNo', description: '订单号', type: String })
  @ApiResponse({ status: 200, type: OrderResponseDto })
  @ApiResult()
  async findByOrderNo(
    @Request() req,
    @Param('orderNo') orderNo: string,
  ): Promise<OrderResponseDto> {
    const order = await this.orderService.findByOrderNo(orderNo);
    
    // 普通用户只能查看自己的订单
    if (!req.user?.permissions?.includes('order:read-all') && order.userId !== req.user?.id) {
      throw new ForbiddenException('无权查看此订单');
    }
    
    return order;
  }

  @Patch(':id')
  @AuthPermission('order:update')
  @ApiOperation({ summary: '更新订单信息' })
  @ApiParam({ name: 'id', description: '订单ID', type: Number })
  @ApiBody({ type: UpdateOrderDto })
  @ApiResponse({ status: 200, type: OrderResponseDto })
  @ApiResult()
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateOrderDto: UpdateOrderDto,
  ): Promise<OrderResponseDto> {
    return this.orderService.update(id, updateOrderDto);
  }

  @Post(':id/cancel')
  @ApiOperation({ summary: '取消订单' })
  @ApiParam({ name: 'id', description: '订单ID', type: Number })
  @ApiResponse({ status: 200, type: OrderResponseDto })
  @ApiResult()
  async cancelOrder(
    @Request() req,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<OrderResponseDto> {
    const userId = req.user?.permissions?.includes('order:cancel-all') ? undefined : req.user?.id;
    return this.orderService.cancelOrder(id, userId);
  }

  @Post(':id/complete')
  @AuthPermission('order:complete')
  @ApiOperation({ summary: '完成订单' })
  @ApiParam({ name: 'id', description: '订单ID', type: Number })
  @ApiResponse({ status: 200, type: OrderResponseDto })
  @ApiResult()
  async completeOrder(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<OrderResponseDto> {
    return this.orderService.completeOrder(id);
  }

  @Get('recharge/config')
  @ApiOperation({ summary: '获取充值配置' })
  @ApiResponse({ status: 200, type: RechargeConfigDto })
  @ApiResult()
  async getRechargeConfig(): Promise<RechargeConfigDto> {
    return this.rechargeService.getRechargeConfig();
  }

  @Get('recharge/stats')
  @AuthPermission('order:read-all')
  @ApiOperation({ summary: '获取充值统计' })
  @ApiResponse({ status: 200, type: RechargeStatsDto })
  @ApiResult()
  async getRechargeStats(): Promise<RechargeStatsDto> {
    return this.rechargeService.getRechargeStats();
  }

  @Get('recharge/records')
  @ApiOperation({ summary: '获取用户充值记录' })
  @ApiResponse({ status: 200, type: UserRechargeRecordDto, isArray: true })
  @ApiResult()
  async getUserRechargeRecords(
    @Request() req,
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('limit', ParseIntPipe) limit: number = 20,
  ): Promise<{
    items: UserRechargeRecordDto[];
    meta: {
      page: number;
      limit: number;
      totalItems: number;
      totalPages: number;
    };
  }> {
    const userId = req.user?.id;
    if (!userId) {
      throw new ForbiddenException('用户未登录');
    }
    return this.rechargeService.getUserRechargeRecords(userId, page, limit);
  }
}
