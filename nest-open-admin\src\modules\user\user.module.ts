import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { UserVerificationService } from './user-verification.service';
import { UserVerificationController } from './user-verification.controller';
import { UserEntity } from './entities/user.entity';
import { UserVerificationEntity } from './entities/user-verification.entity';
import { UserCompanyEntity } from './entities/user-company.entity';
import { UserPaymentStatusEntity } from './entities/user-payment-status.entity';
import { OrderModule } from '../order/order.module';
import { UserServiceModule } from '../user-service/user-service.module';
import { QueueModule } from '../queue/queue.module';
import { EnhancedUserService } from './enhanced-user.service';
import { SharedModule } from '@/shared/shared.module';
import { ApiKeyModule } from '../api-key/api-key.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      UserVerificationEntity,
      UserCompanyEntity,
      UserPaymentStatusEntity,
    ]),
    forwardRef(() => OrderModule),
    forwardRef(() => UserServiceModule),
    forwardRef(() => QueueModule),
    SharedModule,
    forwardRef(() => ApiKeyModule),
  ],
  controllers: [UserController, UserVerificationController],
  providers: [UserService, UserVerificationService, EnhancedUserService],
  exports: [UserService, UserVerificationService, EnhancedUserService],
})
export class UserModule {}
