import { readFileSync } from 'fs';
import { load } from 'js-yaml';
import { join } from 'path';

const configFileNameObj = {
  development: 'dev',
  test: 'test',
  production: 'prod',
};

const env = process.env.NODE_ENV || 'development';
const YAML_CONFIG_FILENAME = `${configFileNameObj[env] || 'dev'}.yml`;
const EMAIL_CONFIG_FILENAME = 'email.yml';
const PAYMENT_CONFIG_FILENAME = 'payment.yml';
const RATE_LIMIT_CONFIG_FILENAME = 'rate-limit.yml';


// 递归处理环境变量替换
function replaceEnvVariables(obj: any): any {
  if (typeof obj === 'string') {
    // 匹配 ${VAR_NAME:-default_value} 或 ${VAR_NAME} 格式
    return obj.replace(/\$\{([^}]+)\}/g, (match, varExpression) => {
      const [varName, defaultValue] = varExpression.split(':-');
      return process.env[varName] || defaultValue || match;
    });
  } else if (Array.isArray(obj)) {
    return obj.map(replaceEnvVariables);
  } else if (obj && typeof obj === 'object') {
    const result = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = replaceEnvVariables(value);
    }
    return result;
  }
  return obj;
}

export default () => {
  // 确定配置文件目录 - 简化逻辑
  // 根据当前目录结构，配置文件在dist/config目录
  const configDir = __dirname.includes('dist') 
    ? join(__dirname.includes('config') ? __dirname : join(__dirname, 'config'))
    : join(__dirname);
  
  try {
    console.log(`加载配置文件目录: ${configDir}`);
    
    // 加载主配置文件
    const mainConfigContent = readFileSync(join(configDir, YAML_CONFIG_FILENAME), 'utf8');
    const mainConfig = load(mainConfigContent) as Record<string, any>;
    
    // 加载邮件配置文件
    const emailConfigContent = readFileSync(join(configDir, EMAIL_CONFIG_FILENAME), 'utf8');
    const emailConfig = load(emailConfigContent) as Record<string, any>;
    
    // 加载支付配置文件
    const paymentConfigContent = readFileSync(join(configDir, PAYMENT_CONFIG_FILENAME), 'utf8');
    const paymentConfig = load(paymentConfigContent) as Record<string, any>;
    
    // 加载限流配置文件
    const rateLimitConfigContent = readFileSync(join(configDir, RATE_LIMIT_CONFIG_FILENAME), 'utf8');
    const rateLimitConfig = load(rateLimitConfigContent) as Record<string, any>;
    
    // 合并配置
    const mergedConfig = {
      ...mainConfig,
      ...emailConfig,
      ...paymentConfig,
      ...rateLimitConfig,
    };
    
    // 处理环境变量替换
    return replaceEnvVariables(mergedConfig);
  } catch (error) {
    console.error(`配置加载错误: ${error.message}`);
    console.error(`尝试从路径: ${join(configDir, YAML_CONFIG_FILENAME)}`);
    throw error;
  }
};
