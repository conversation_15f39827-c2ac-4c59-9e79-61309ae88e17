/**
 * 订单状态枚举
 */
export enum OrderStatus {
  /** 待支付 */
  PENDING = 'pending',
  /** 已支付 */
  PAID = 'paid',
  /** 已完成 */
  COMPLETED = 'completed',
  /** 已取消 */
  CANCELLED = 'cancelled',
  /** 已过期 */
  EXPIRED = 'expired',
  /** 退款中 */
  REFUNDING = 'refunding',
  /** 已退款 */
  REFUNDED = 'refunded',
}

/**
 * 订单类型枚举
 */
export enum OrderType {
  /** 服务购买 */
  SERVICE = 'service',
  /** 账户充值 */
  RECHARGE = 'recharge',
}

/**
 * 支付方式枚举
 */
export enum PaymentMethod {
  /** 支付宝 */
  ALIPAY = 'alipay',
  /** 微信支付 */
  WECHAT = 'wechat',
  /** 账户余额 */
  BALANCE = 'balance',
  /** 银行卡 */
  BANK_CARD = 'bank_card',
}

/**
 * 支付状态枚举
 */
export enum PaymentStatus {
  /** 待支付 */
  PENDING = 'pending',
  /** 支付中 */
  PROCESSING = 'processing',
  /** 支付成功 */
  SUCCESS = 'success',
  /** 支付失败 */
  FAILED = 'failed',
  /** 已取消 */
  CANCELLED = 'cancelled',
  /** 已退款 */
  REFUNDED = 'refunded',
}
