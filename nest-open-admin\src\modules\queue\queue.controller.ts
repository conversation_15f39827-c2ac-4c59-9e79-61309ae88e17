import { Controller, Get, Param, Post, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam } from '@nestjs/swagger';
import { EnhancedQueueManagerService } from './services/enhanced-queue-manager.service';

/**
 * 队列控制器
 * 提供队列管理相关的API
 */
@ApiTags('队列管理')
@Controller('queues')
export class QueueController {
  constructor(
    private readonly queueManager: EnhancedQueueManagerService,
  ) {}

  /**
   * 获取所有队列
   */
  @Get()
  @ApiOperation({ summary: '获取所有队列' })
  async getAllQueues() {
    const queueNames = this.queueManager.getQueueNames();
    const queues = await Promise.all(
      queueNames.map(async (queueName) => {
        const status = await this.queueManager.getQueueStatus(queueName);
        return {
          queueName,
          ...status,
        };
      }),
    );
    return { queues };
  }

  /**
   * 获取队列状态
   */
  @Get(':queueName')
  @ApiOperation({ summary: '获取队列状态' })
  @ApiParam({ name: 'queueName', description: '队列名称' })
  async getQueueStatus(@Param('queueName') queueName: string) {
    const status = await this.queueManager.getQueueStatus(queueName);
    return status;
  }

  /**
   * 暂停队列
   */
  @Post(':queueName/pause')
  @ApiOperation({ summary: '暂停队列' })
  @ApiParam({ name: 'queueName', description: '队列名称' })
  async pauseQueue(@Param('queueName') queueName: string) {
    const result = await this.queueManager.pauseQueue(queueName);
    return { success: result };
  }

  /**
   * 恢复队列
   */
  @Post(':queueName/resume')
  @ApiOperation({ summary: '恢复队列' })
  @ApiParam({ name: 'queueName', description: '队列名称' })
  async resumeQueue(@Param('queueName') queueName: string) {
    const result = await this.queueManager.resumeQueue(queueName);
    return { success: result };
  }

  /**
   * 获取任务状态
   */
  @Get(':queueName/jobs/:jobId')
  @ApiOperation({ summary: '获取任务状态' })
  @ApiParam({ name: 'queueName', description: '队列名称' })
  @ApiParam({ name: 'jobId', description: '任务ID' })
  async getJobStatus(
    @Param('queueName') queueName: string,
    @Param('jobId') jobId: string,
  ) {
    const status = await this.queueManager.getJobStatus(queueName, jobId);
    return status;
  }

  /**
   * 删除任务
   */
  @Delete(':queueName/jobs/:jobId')
  @ApiOperation({ summary: '删除任务' })
  @ApiParam({ name: 'queueName', description: '队列名称' })
  @ApiParam({ name: 'jobId', description: '任务ID' })
  async removeJob(
    @Param('queueName') queueName: string,
    @Param('jobId') jobId: string,
  ) {
    const result = await this.queueManager.removeJob(queueName, jobId);
    return { success: result };
  }
} 