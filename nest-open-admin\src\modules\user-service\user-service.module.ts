import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserServiceService } from './user-service.service';
import { UserServiceController } from './user-service.controller';
import { UserServiceEntity } from './entities/user-service.entity';
import { UserModule } from '../user/user.module';
import { ServiceModule } from '../service/service.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserServiceEntity]),
    forwardRef(() => UserModule),
    ServiceModule,
  ],
  controllers: [UserServiceController],
  providers: [UserServiceService],
  exports: [UserServiceService],
})
export class UserServiceModule {}
