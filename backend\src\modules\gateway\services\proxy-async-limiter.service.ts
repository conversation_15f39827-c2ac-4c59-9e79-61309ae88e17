import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';

/**
 * 代理异步限制器配置
 */
interface ProxyAsyncLimiterConfig {
  maxConcurrentRequests: number;
  maxQueueSize: number;
  requestTimeout: number;
  cleanupInterval: number;
  resourceThresholds: {
    cpu: number;
    memory: number;
    queueLength: number;
  };
}

/**
 * 等待中的请求信息
 */
interface PendingRequest {
  id: string;
  userId?: number;
  apiKeyId?: number;
  taskType: string;
  priority: number;
  createdAt: Date;
  timeout: NodeJS.Timeout;
  resolve: (value: any) => void;
  reject: (reason: any) => void;
}

/**
 * 资源使用统计
 */
interface ResourceStats {
  activeTasks: number;
  queuedTasks: number;
  completedTasks: number;
  failedTasks: number;
  avgResponseTime: number;
  lastUpdated: Date;
}

/**
 * 代理异步限制器服务
 * 负责管理代理异步模式的并发控制、资源管理和请求排队
 */
@Injectable()
export class ProxyAsyncLimiterService {
  private readonly logger = new Logger(ProxyAsyncLimiterService.name);
  private readonly config: ProxyAsyncLimiterConfig;
  private readonly pendingRequests = new Map<string, PendingRequest>();
  private readonly activeRequests = new Set<string>();
  private readonly userRequestCounts = new Map<string, number>();
  private readonly resourceStats: ResourceStats = {
    activeTasks: 0,
    queuedTasks: 0,
    completedTasks: 0,
    failedTasks: 0,
    avgResponseTime: 0,
    lastUpdated: new Date(),
  };

  private cleanupTimer?: NodeJS.Timeout;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.config = {
      maxConcurrentRequests: this.configService.get('GATEWAY_MAX_CONCURRENT_REQUESTS', 50),
      maxQueueSize: this.configService.get('GATEWAY_MAX_QUEUE_SIZE', 200),
      requestTimeout: this.configService.get('GATEWAY_REQUEST_TIMEOUT', 300000), // 5分钟
      cleanupInterval: this.configService.get('GATEWAY_CLEANUP_INTERVAL', 60000), // 1分钟
      resourceThresholds: {
        cpu: this.configService.get('GATEWAY_CPU_THRESHOLD', 80),
        memory: this.configService.get('GATEWAY_MEMORY_THRESHOLD', 80),
        queueLength: this.configService.get('GATEWAY_QUEUE_THRESHOLD', 100),
      },
    };

    this.startCleanupTimer();
    this.logger.log('代理异步限制器服务已启动', this.config);
  }

  /**
   * 请求执行权限
   * @param requestId 请求ID
   * @param taskType 任务类型
   * @param userId 用户ID
   * @param apiKeyId API密钥ID
   * @param priority 优先级（数字越小优先级越高）
   */
  async requestExecution(
    requestId: string,
    taskType: string,
    userId?: number,
    apiKeyId?: number,
    priority: number = 5,
  ): Promise<void> {
    // 检查是否已经在处理中
    if (this.activeRequests.has(requestId) || this.pendingRequests.has(requestId)) {
      throw new Error(`请求 ${requestId} 已在处理中`);
    }

    // 检查资源限制
    if (!this.checkResourceLimits(userId, apiKeyId)) {
      throw new Error('系统资源不足，请稍后重试');
    }

    // 检查队列大小
    if (this.pendingRequests.size >= this.config.maxQueueSize) {
      throw new Error('请求队列已满，请稍后重试');
    }

    // 如果有可用槽位，直接执行
    if (this.activeRequests.size < this.config.maxConcurrentRequests) {
      this.activeRequests.add(requestId);
      this.updateUserRequestCount(userId, apiKeyId, 1);
      this.updateResourceStats();
      
      this.logger.debug(`请求 ${requestId} 获得执行权限，当前活跃请求: ${this.activeRequests.size}`);
      return;
    }

    // 加入等待队列
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(requestId);
        this.updateResourceStats();
        reject(new Error(`请求 ${requestId} 等待超时`));
      }, this.config.requestTimeout);

      const pendingRequest: PendingRequest = {
        id: requestId,
        userId,
        apiKeyId,
        taskType,
        priority,
        createdAt: new Date(),
        timeout,
        resolve,
        reject,
      };

      this.pendingRequests.set(requestId, pendingRequest);
      this.updateResourceStats();
      
      this.logger.debug(`请求 ${requestId} 加入等待队列，队列长度: ${this.pendingRequests.size}`);
    });
  }

  /**
   * 释放执行权限
   * @param requestId 请求ID
   * @param success 是否成功完成
   * @param responseTime 响应时间（毫秒）
   */
  async releaseExecution(requestId: string, success: boolean = true, responseTime?: number): Promise<void> {
    // 从活跃请求中移除
    if (this.activeRequests.has(requestId)) {
      this.activeRequests.delete(requestId);
      
      // 更新统计信息
      if (success) {
        this.resourceStats.completedTasks++;
      } else {
        this.resourceStats.failedTasks++;
      }

      if (responseTime) {
        this.updateAverageResponseTime(responseTime);
      }

      this.logger.debug(`请求 ${requestId} 释放执行权限，当前活跃请求: ${this.activeRequests.size}`);
    }

    // 从等待队列中移除（如果存在）
    const pendingRequest = this.pendingRequests.get(requestId);
    if (pendingRequest) {
      clearTimeout(pendingRequest.timeout);
      this.pendingRequests.delete(requestId);
    }

    // 更新用户请求计数
    this.updateUserRequestCount(pendingRequest?.userId, pendingRequest?.apiKeyId, -1);

    // 处理下一个等待的请求
    await this.processNextPendingRequest();
    
    this.updateResourceStats();
  }

  /**
   * 获取资源统计信息
   */
  getResourceStats(): ResourceStats {
    return {
      ...this.resourceStats,
      activeTasks: this.activeRequests.size,
      queuedTasks: this.pendingRequests.size,
      lastUpdated: new Date(),
    };
  }

  /**
   * 获取用户当前请求数
   */
  getUserRequestCount(userId?: number, apiKeyId?: number): number {
    const key = this.getUserKey(userId, apiKeyId);
    return this.userRequestCounts.get(key) || 0;
  }

  /**
   * 检查资源限制
   */
  private checkResourceLimits(userId?: number, apiKeyId?: number): boolean {
    // 检查全局并发限制
    if (this.activeRequests.size >= this.config.maxConcurrentRequests) {
      return false;
    }

    // 检查用户并发限制
    const userRequestCount = this.getUserRequestCount(userId, apiKeyId);
    const maxUserRequests = this.getMaxUserRequests(userId, apiKeyId);
    if (userRequestCount >= maxUserRequests) {
      return false;
    }

    // 检查系统资源（这里可以集成实际的系统监控）
    if (this.isSystemOverloaded()) {
      return false;
    }

    return true;
  }

  /**
   * 获取用户最大请求数
   */
  private getMaxUserRequests(userId?: number, apiKeyId?: number): number {
    // 这里可以根据用户类型、订阅级别等设置不同的限制
    // 暂时返回固定值
    return 10;
  }

  /**
   * 检查系统是否过载
   */
  private isSystemOverloaded(): boolean {
    // 这里可以集成实际的系统监控指标
    // 暂时基于队列长度判断
    return this.pendingRequests.size > this.config.resourceThresholds.queueLength;
  }

  /**
   * 处理下一个等待的请求
   */
  private async processNextPendingRequest(): Promise<void> {
    if (this.activeRequests.size >= this.config.maxConcurrentRequests) {
      return; // 没有可用槽位
    }

    // 按优先级和创建时间排序
    const sortedRequests = Array.from(this.pendingRequests.values()).sort((a, b) => {
      if (a.priority !== b.priority) {
        return a.priority - b.priority; // 数字越小优先级越高
      }
      return a.createdAt.getTime() - b.createdAt.getTime(); // 先进先出
    });

    for (const request of sortedRequests) {
      if (this.checkResourceLimits(request.userId, request.apiKeyId)) {
        // 移除等待队列
        clearTimeout(request.timeout);
        this.pendingRequests.delete(request.id);

        // 添加到活跃请求
        this.activeRequests.add(request.id);
        this.updateUserRequestCount(request.userId, request.apiKeyId, 1);

        // 解析Promise
        request.resolve(undefined);

        this.logger.debug(`请求 ${request.id} 从等待队列获得执行权限`);
        break;
      }
    }
  }

  /**
   * 更新用户请求计数
   */
  private updateUserRequestCount(userId?: number, apiKeyId?: number, delta: number = 0): void {
    const key = this.getUserKey(userId, apiKeyId);
    const currentCount = this.userRequestCounts.get(key) || 0;
    const newCount = Math.max(0, currentCount + delta);
    
    if (newCount === 0) {
      this.userRequestCounts.delete(key);
    } else {
      this.userRequestCounts.set(key, newCount);
    }
  }

  /**
   * 获取用户标识键
   */
  private getUserKey(userId?: number, apiKeyId?: number): string {
    if (userId) return `user:${userId}`;
    if (apiKeyId) return `apikey:${apiKeyId}`;
    return 'anonymous';
  }

  /**
   * 更新平均响应时间
   */
  private updateAverageResponseTime(responseTime: number): void {
    const totalTasks = this.resourceStats.completedTasks + this.resourceStats.failedTasks;
    if (totalTasks === 1) {
      this.resourceStats.avgResponseTime = responseTime;
    } else {
      // 使用指数移动平均
      const alpha = 0.1;
      this.resourceStats.avgResponseTime = 
        alpha * responseTime + (1 - alpha) * this.resourceStats.avgResponseTime;
    }
  }

  /**
   * 更新资源统计
   */
  private updateResourceStats(): void {
    this.resourceStats.lastUpdated = new Date();
    
    // 发送统计事件
    this.eventEmitter.emit('gateway.stats.updated', this.getResourceStats());
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredRequests();
    }, this.config.cleanupInterval);
  }

  /**
   * 清理过期请求
   */
  private cleanupExpiredRequests(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [requestId, request] of this.pendingRequests.entries()) {
      const age = now - request.createdAt.getTime();
      if (age > this.config.requestTimeout) {
        clearTimeout(request.timeout);
        this.pendingRequests.delete(requestId);
        request.reject(new Error('请求已过期'));
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug(`清理了 ${cleanedCount} 个过期请求`);
      this.updateResourceStats();
    }
  }

  /**
   * 服务销毁时清理资源
   */
  onModuleDestroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    // 拒绝所有等待中的请求
    for (const request of this.pendingRequests.values()) {
      clearTimeout(request.timeout);
      request.reject(new Error('服务正在关闭'));
    }

    this.pendingRequests.clear();
    this.activeRequests.clear();
    this.userRequestCounts.clear();
  }
}
