import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { DataSource, Repository } from 'typeorm';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { OrderService } from '../order.service';
import { OrderEntity } from '../entities/order.entity';
import { OrderItemEntity } from '../entities/order-item.entity';
import { UserService } from '../../user/user.service';
import { ServiceService } from '../../service/service.service';
import { UserServiceService } from '../../user-service/user-service.service';
import { OrderType, OrderStatus, PaymentMethod } from '../enums/order.enum';
import {
  createTestUser,
  createTestService,
  createTestOrderData,
} from './order.test-config';

describe('OrderService', () => {
  let service: OrderService;
  let orderRepository: Repository<OrderEntity>;
  let orderItemRepository: Repository<OrderItemEntity>;
  let userService: UserService;
  let serviceService: ServiceService;
  let userServiceService: UserServiceService;
  let eventEmitter: EventEmitter2;
  let dataSource: DataSource;

  const mockUser = { id: 1, ...createTestUser() };
  const mockService = { id: 1, ...createTestService() };
  const mockOrder = {
    id: 1,
    orderNo: 'ORD20231201001',
    ...createTestOrderData(1),
    status: OrderStatus.PENDING,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrderService,
        {
          provide: getRepositoryToken(OrderEntity),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            findOne: jest.fn(),
            find: jest.fn(),
            createQueryBuilder: jest.fn(() => ({
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              orderBy: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              take: jest.fn().mockReturnThis(),
              getManyAndCount: jest.fn(),
              select: jest.fn().mockReturnThis(),
              addSelect: jest.fn().mockReturnThis(),
              groupBy: jest.fn().mockReturnThis(),
              getRawMany: jest.fn(),
              getRawOne: jest.fn(),
            })),
            count: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(OrderItemEntity),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
          },
        },
        {
          provide: UserService,
          useValue: {
            findById: jest.fn(),
          },
        },
        {
          provide: ServiceService,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: UserServiceService,
          useValue: {
            addServiceCount: jest.fn(),
          },
        },
        {
          provide: EventEmitter2,
          useValue: {
            emit: jest.fn(),
          },
        },
        {
          provide: DataSource,
          useValue: {
            createQueryRunner: jest.fn(() => ({
              connect: jest.fn(),
              startTransaction: jest.fn(),
              commitTransaction: jest.fn(),
              rollbackTransaction: jest.fn(),
              release: jest.fn(),
              manager: {
                create: jest.fn(),
                save: jest.fn(),
                findOne: jest.fn(),
                update: jest.fn(),
              },
            })),
          },
        },
      ],
    }).compile();

    service = module.get<OrderService>(OrderService);
    orderRepository = module.get<Repository<OrderEntity>>(getRepositoryToken(OrderEntity));
    orderItemRepository = module.get<Repository<OrderItemEntity>>(getRepositoryToken(OrderItemEntity));
    userService = module.get<UserService>(UserService);
    serviceService = module.get<ServiceService>(ServiceService);
    userServiceService = module.get<UserServiceService>(UserServiceService);
    eventEmitter = module.get<EventEmitter2>(EventEmitter2);
    dataSource = module.get<DataSource>(DataSource);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createServiceOrder', () => {
    const createOrderDto = {
      orderItems: [
        {
          serviceId: 1,
          itemName: 'Test Service',
          quantity: 2,
          unitPrice: 10.00,
        }
      ],
      remark: 'Test order',
    };

    it('应该成功创建服务订单', async () => {
      // Arrange
      jest.spyOn(userService, 'findById').mockResolvedValue(mockUser as any);
      jest.spyOn(serviceService, 'findOne').mockResolvedValue(mockService as any);
      
      const mockQueryRunner = {
        connect: jest.fn(),
        startTransaction: jest.fn(),
        commitTransaction: jest.fn(),
        rollbackTransaction: jest.fn(),
        release: jest.fn(),
        manager: {
          create: jest.fn()
            .mockReturnValueOnce(mockOrder)
            .mockReturnValueOnce({ id: 1, orderId: 1 }),
          save: jest.fn()
            .mockResolvedValueOnce(mockOrder)
            .mockResolvedValueOnce({ id: 1 }),
        },
      };
      
      jest.spyOn(dataSource, 'createQueryRunner').mockReturnValue(mockQueryRunner as any);
      jest.spyOn(service, 'findById').mockResolvedValue(mockOrder as any);

      // Act
      const result = await service.createServiceOrder(1, createOrderDto);

      // Assert
      expect(userService.findById).toHaveBeenCalledWith(1);
      expect(serviceService.findOne).toHaveBeenCalledWith(1);
      expect(mockQueryRunner.manager.create).toHaveBeenCalledTimes(2);
      expect(mockQueryRunner.manager.save).toHaveBeenCalledTimes(2);
      expect(eventEmitter.emit).toHaveBeenCalledWith('order.created', expect.any(Object));
      expect(result).toEqual(mockOrder);
    });

    it('当用户不存在时应该抛出异常', async () => {
      // Arrange
      jest.spyOn(userService, 'findById').mockRejectedValue(new NotFoundException('用户不存在'));

      // Act & Assert
      await expect(service.createServiceOrder(1, createOrderDto))
        .rejects.toThrow(NotFoundException);
    });

    it('当服务不存在时应该抛出异常', async () => {
      // Arrange
      jest.spyOn(userService, 'findById').mockResolvedValue(mockUser as any);
      jest.spyOn(serviceService, 'findOne').mockRejectedValue(new NotFoundException('服务不存在'));

      // Act & Assert
      await expect(service.createServiceOrder(1, createOrderDto))
        .rejects.toThrow(NotFoundException);
    });

    it('当服务不可用时应该抛出异常', async () => {
      // Arrange
      const inactiveService = { ...mockService, isActive: false };
      jest.spyOn(userService, 'findById').mockResolvedValue(mockUser as any);
      jest.spyOn(serviceService, 'findOne').mockResolvedValue(inactiveService as any);

      // Act & Assert
      await expect(service.createServiceOrder(1, createOrderDto))
        .rejects.toThrow(BadRequestException);
    });
  });

  describe('findById', () => {
    it('应该成功查找订单', async () => {
      // Arrange
      const mockOrderWithItems = {
        ...mockOrder,
        orderItems: [{ id: 1, orderId: 1 }],
      };
      jest.spyOn(orderRepository, 'findOne').mockResolvedValue(mockOrderWithItems as any);

      // Act
      const result = await service.findById(1);

      // Assert
      expect(orderRepository.findOne).toHaveBeenCalledWith({
        where: { id: 1 },
        relations: ['orderItems'],
      });
      expect(result).toBeDefined();
    });

    it('当订单不存在时应该抛出异常', async () => {
      // Arrange
      jest.spyOn(orderRepository, 'findOne').mockResolvedValue(null);

      // Act & Assert
      await expect(service.findById(1)).rejects.toThrow(NotFoundException);
    });
  });

  describe('cancelOrder', () => {
    it('应该成功取消订单', async () => {
      // Arrange
      const pendingOrder = { ...mockOrder, status: OrderStatus.PENDING };
      jest.spyOn(orderRepository, 'findOne').mockResolvedValue(pendingOrder as any);
      jest.spyOn(orderRepository, 'save').mockResolvedValue({
        ...pendingOrder,
        status: OrderStatus.CANCELLED,
      } as any);

      // Act
      const result = await service.cancelOrder(1);

      // Assert
      expect(orderRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({ status: OrderStatus.CANCELLED })
      );
      expect(eventEmitter.emit).toHaveBeenCalledWith('order.cancelled', expect.any(Object));
      expect(result.status).toBe(OrderStatus.CANCELLED);
    });

    it('当订单不是待支付状态时应该抛出异常', async () => {
      // Arrange
      const paidOrder = { ...mockOrder, status: OrderStatus.PAID };
      jest.spyOn(orderRepository, 'findOne').mockResolvedValue(paidOrder as any);

      // Act & Assert
      await expect(service.cancelOrder(1)).rejects.toThrow(BadRequestException);
    });
  });

  describe('completeOrder', () => {
    it('应该成功完成服务订单', async () => {
      // Arrange
      const paidOrder = {
        ...mockOrder,
        status: OrderStatus.PAID,
        orderType: OrderType.SERVICE,
        orderItems: [{ serviceId: 1, quantity: 2 }],
      };
      
      const mockQueryRunner = {
        connect: jest.fn(),
        startTransaction: jest.fn(),
        commitTransaction: jest.fn(),
        rollbackTransaction: jest.fn(),
        release: jest.fn(),
        manager: {
          findOne: jest.fn().mockResolvedValue(paidOrder),
          save: jest.fn().mockResolvedValue({
            ...paidOrder,
            status: OrderStatus.COMPLETED,
          }),
        },
      };
      
      jest.spyOn(dataSource, 'createQueryRunner').mockReturnValue(mockQueryRunner as any);
      jest.spyOn(userServiceService, 'addServiceCount').mockResolvedValue({} as any);

      // Act
      const result = await service.completeOrder(1);

      // Assert
      expect(userServiceService.addServiceCount).toHaveBeenCalledWith(
        paidOrder.userId,
        1,
        2,
        '订单购买'
      );
      expect(eventEmitter.emit).toHaveBeenCalledWith('order.completed', expect.any(Object));
      expect(result.status).toBe(OrderStatus.COMPLETED);
    });

    it('当订单不是已支付状态时应该抛出异常', async () => {
      // Arrange
      const pendingOrder = { ...mockOrder, status: OrderStatus.PENDING };
      
      const mockQueryRunner = {
        connect: jest.fn(),
        startTransaction: jest.fn(),
        rollbackTransaction: jest.fn(),
        release: jest.fn(),
        manager: {
          findOne: jest.fn().mockResolvedValue(pendingOrder),
        },
      };
      
      jest.spyOn(dataSource, 'createQueryRunner').mockReturnValue(mockQueryRunner as any);

      // Act & Assert
      await expect(service.completeOrder(1)).rejects.toThrow(BadRequestException);
    });
  });
});
