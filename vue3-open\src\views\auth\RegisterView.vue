<template>
  <div class="register-page">
    <div class="register-container">
      <div class="register-header">
        <h1 class="register-title">创建您的账号</h1>
        <p class="register-subtitle">加入我们，开始您的AI开发之旅</p>
      </div>
      
      <!-- 注册方式切换 -->
      <div class="register-type-switch">
        <el-radio-group v-model="registerType" class="register-type-group">
          <el-radio-button label="email">邮箱注册</el-radio-button>
          <el-radio-button label="phone">手机号注册</el-radio-button>
        </el-radio-group>
      </div>
      
      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        class="register-form"
        size="large"
        @submit.prevent="handleRegister"
      >

        <!-- 邮箱注册表单 -->
        <template v-if="registerType === 'email'">
          <el-form-item prop="email">
            <div class="email-input-container">
              <el-input
                v-model="registerForm.email"
                type="email"
                placeholder="邮箱地址"
                :prefix-icon="Message"
                :disabled="emailInputDisabled"
                :clearable="!emailInputDisabled"
                @blur="validateEmail"
                @input="onEmailInput"
                class="email-input"
              />
              <el-icon 
                v-if="emailInputDisabled && showEditIcon" 
                class="edit-email-icon" 
                @click="handleEditEmail"
              >
                <Edit />
              </el-icon>
            </div>
          </el-form-item>

          <!-- 邮箱验证码输入框 - 默认显示 -->
          <el-form-item prop="emailCode">
            <div class="email-code-container">
              <el-input
                v-model="registerForm.emailCode"
                placeholder="请输入邮箱验证码"
                prefix-icon="Key"
                size="large"
                class="email-code-input"
                :disabled="!emailValid || !emailCodeSent"
              />
              <el-button
                 type="primary"
                 size="large"
                 class="send-code-btn"
                 :disabled="!canSendCode"
                 :loading="sendingCode"
                 @click="sendEmailCodeHandler"
               >
                {{ sendCodeText }}
              </el-button>
            </div>
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="密码"
              :prefix-icon="Lock"
              show-password
              clearable
            />
            <div class="password-hint">
              密码要求：8-20位，包含大小写字母、数字和特殊字符
            </div>
          </el-form-item>
          
          <el-form-item prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="确认密码"
              :prefix-icon="Lock"
              show-password
              clearable
              @keyup.enter="handleRegister"
            />
          </el-form-item>
        </template>

        <!-- 手机号注册表单 -->
        <template v-else>
          <el-form-item prop="phone">
            <el-input
              v-model="registerForm.phone"
              placeholder="请输入手机号"
              prefix-icon="Phone"
              size="large"
              maxlength="11"
            />
          </el-form-item>

          <el-form-item prop="smsCode">
            <div class="code-input-group">
              <el-input
                v-model="registerForm.smsCode"
                placeholder="请输入短信验证码"
                prefix-icon="Key"
                size="large"
                maxlength="6"
              />
              <el-button
                type="primary"
                size="large"
                :disabled="!registerForm.phone || smsCountdown > 0"
                :loading="smsSending"
                @click="sendSmsCodeHandler"
                class="code-button"
              >
                {{ smsCountdown > 0 ? `${smsCountdown}s` : '发送验证码' }}
              </el-button>
            </div>
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="密码"
              :prefix-icon="Lock"
              show-password
              clearable
            />
            <div class="password-hint">
              密码要求：8-20位，包含大小写字母、数字和特殊字符
            </div>
          </el-form-item>
          
          <el-form-item prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="确认密码"
              :prefix-icon="Lock"
              show-password
              clearable
              @keyup.enter="handleRegister"
            />
          </el-form-item>
        </template>
        <!-- <el-form-item prop="agreement">
          <el-checkbox v-model="formHelper.agreement">
            我已阅读并同意
            <el-link type="primary" :underline="false" @click="showTerms">
              《服务条款》
            </el-link>
            和
            <el-link type="primary" :underline="false" @click="showPrivacy">
            </el-link>
          </el-checkbox>
        </el-form-item> -->
        
        <el-form-item>
          <el-button
            type="primary"
            class="register-button"
            :loading="loading"
            :disabled="loading"
            @click="handleRegister"
          >
            {{ loading ? '注册中...' : '创建账号' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="register-footer">
        <p class="login-link">
          已有账号？
          <router-link to="/login" class="link">
            立即登录
          </router-link>
        </p>
      </div>
    </div>
    
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>
    
    <!-- 弹窗验证码 -->
    <CaptchaModal
      v-model="showCaptchaModal"
      :loading="loading"
      @success="onCaptchaSuccess"
      @cancel="onCaptchaCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Lock, Platform, Message, Edit } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useUserStore } from '@/stores/user'
import type { RegisterForm } from '@/stores/user'
import logo from '@/assets/logo.svg'
import CaptchaModal from '@/components/CaptchaModal.vue'
import type { DeviceFingerprint } from '@/utils/deviceFingerprint'
import type { BehaviorPattern } from '@/utils/behaviorTracker'

const router = useRouter()
const userStore = useUserStore()

const {
  register,
  sendEmailCode,
  sendSmsCode,
  loading
} = userStore

// 注册方式
const registerType = ref<'email' | 'phone'>('email')

// 短信验证码相关
const smsSending = ref(false)
const smsCountdown = ref(0)
let smsTimer: any | null = null

const emailValid = ref(false)
const sendingCode = ref(false)
const sendCodeDisabled = ref(false)
const countdown = ref(0)
const sendCodeText = ref('发送验证码')

// 邮箱输入状态管理
const emailInputDisabled = ref(false)
const showEditIcon = ref(false)
const emailChangeCount = ref(0)
const lastEmailChangeTime = ref(0)
const emailCodeSent = ref(false)
const originalEmail = ref('')

// 计算是否可以发送验证码
const canSendCode = computed(() => {
  return emailValid.value && !sendCodeDisabled.value && !sendingCode.value && registerForm.email
})

const appTitle = import.meta.env.VITE_APP_TITLE
const registerFormRef = ref<FormInstance>()
const showCaptchaModal = ref(false)
const isCaptchaVerified = ref(false)
const securityLevel = ref('low')

// 安全验证数据
const securityData = ref<{
  deviceFingerprint?: DeviceFingerprint
  behaviorPattern?: BehaviorPattern
  verificationTimestamp?: number
  humanLikelihood?: number
}>({})

// 注册表单
const registerForm = reactive<RegisterForm>({
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  userType: 'individual',
  captchaId: '',
  captchaCode: '',
  emailCode: '',
  smsCode: ''
})

// 前端表单辅助字段（不发送到后端）
const formHelper = reactive({
  confirmPassword: '',
  agreement: true
})

// 密码强度验证
const validatePassword = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('请输入密码'))
    return
  }

  if (value.length < 8 || value.length > 20) {
    callback(new Error('密码长度必须在8-20个字符之间'))
    return
  }

  // 检查是否包含大写字母
  if (!/[A-Z]/.test(value)) {
    callback(new Error('密码必须包含至少一个大写字母'))
    return
  }

  // 检查是否包含小写字母
  if (!/[a-z]/.test(value)) {
    callback(new Error('密码必须包含至少一个小写字母'))
    return
  }

  // 检查是否包含数字
  if (!/\d/.test(value)) {
    callback(new Error('密码必须包含至少一个数字'))
    return
  }

  // 检查是否包含特殊字符
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value)) {
    callback(new Error('密码必须包含至少一个特殊字符'))
    return
  }

  callback()
}

// 确认密码验证
const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== registerForm.password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const registerRules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { validator: validatePassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ],
  agreement: [
    { 
      validator: (rule: any, value: any, callback: any) => {
        if (!value) {
          callback(new Error('请阅读并同意服务条款和隐私政策'))
        } else {
          callback()
        }
      }, 
      trigger: 'change'
    }
  ],
  emailCode: [
    { required: true, message: '请输入邮箱验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码必须是6位数字', trigger: 'blur' }
  ],
  smsCode: [
    { required: true, message: '请输入短信验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码必须是6位数字', trigger: 'blur' }
  ]
}

// 验证码相关方法
const onCaptchaSuccess = (result: {
  deviceFingerprint?: DeviceFingerprint
  behaviorPattern?: BehaviorPattern
  verificationTimestamp: number
  humanLikelihood: number
  securityLevel: string
}) => {
  isCaptchaVerified.value = true
  securityLevel.value = result.securityLevel
  
  // 保存安全验证数据
  securityData.value = {
    deviceFingerprint: result.deviceFingerprint,
    behaviorPattern: result.behaviorPattern,
    verificationTimestamp: result.verificationTimestamp,
    humanLikelihood: result.humanLikelihood
  }
  
  console.log('弹窗验证成功，开始注册:', {
    securityLevel: securityLevel.value,
    humanLikelihood: result.humanLikelihood,
    result
  })
  
  // 如果是因为频繁修改邮箱触发的验证，重置邮箱输入状态
  if (emailChangeCount.value >= 3) {
    resetEmailCodeState()
    ElMessage.success('安全验证通过，可以重新输入邮箱')
  } else {
    // 验证成功后立即执行注册
    performRegister()
  }
}

// 弹窗取消回调
const onCaptchaCancel = () => {
  isCaptchaVerified.value = false
  securityLevel.value = 'low'
  securityData.value = {}
  showCaptchaModal.value = false
  ElMessage.info('已取消验证')
}

// 处理注册 - 先验证表单，然后弹出验证码
const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  try {
    await registerFormRef.value.validate()
    
    // 检查验证码
    if (registerType.value === 'email') {
      if (registerForm.email && emailValid.value && !registerForm.emailCode) {
        ElMessage.warning('请输入邮箱验证码')
        return
      }
    } else if (registerType.value === 'phone') {
      if (registerForm.phone && !registerForm.smsCode) {
        ElMessage.warning('请输入短信验证码')
        return
      }
    }
    
    // 如果已经通过拼图验证（比如频繁修改邮箱时），直接注册
    if (isCaptchaVerified.value) {
      performRegister()
    } else {
      // 表单验证通过，弹出验证码弹窗
      showCaptchaModal.value = true
    }
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

// 执行实际的注册逻辑
const performRegister = async () => {
  if (!isCaptchaVerified.value) {
    ElMessage.warning('请先完成安全验证')
    return
  }
  try {
    // 构建包含安全验证数据的注册请求
    const registerData: any = {
      password: registerForm.password,
      userType: registerForm.userType,
      securityVerification: {
        level: securityLevel.value,
        deviceFingerprint: securityData.value.deviceFingerprint,
        behaviorPattern: {
          humanLikelihood: securityData.value.humanLikelihood,
          totalEvents: securityData.value.behaviorPattern ? 
            (securityData.value.behaviorPattern.mouseEvents.length + 
             securityData.value.behaviorPattern.keyboardEvents.length + 
             securityData.value.behaviorPattern.touchEvents.length) : 0,
          duration: securityData.value.behaviorPattern?.duration || 0,
          statistics: securityData.value.behaviorPattern?.statistics
        },
        verificationTimestamp: securityData.value.verificationTimestamp,
        clientInfo: {
          userAgent: navigator.userAgent,
          screenResolution: `${screen.width}x${screen.height}`,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          language: navigator.language,
          platform: navigator.platform
        }
      }
    }
    // 根据注册方式添加相应字段
    if (registerType.value === 'email') {
      registerData.email = registerForm.email
      registerData.emailCode = registerForm.emailCode
      registerData.confirmPassword = registerForm.confirmPassword
    } else if (registerType.value === 'phone') {
      registerData.phone = registerForm.phone
      registerData.smsCode = registerForm.smsCode
      registerData.confirmPassword = registerForm.confirmPassword
    }
    const result: any = await register(registerData)

    // 检查注册响应格式
    if (result?.success && result?.data) {
      const responseData = result.data

      // 如果返回了token，说明注册后自动登录
      if (responseData.accessToken || responseData.token) {
        // 保存token和用户信息
        userStore.setToken(responseData.accessToken || responseData.token)
        if (responseData.refreshToken) {
          userStore.setRefreshToken(responseData.refreshToken)
        }
        if (responseData.user) {
          userStore.userInfo.value = responseData.user
          localStorage.setItem('userInfo', JSON.stringify(responseData.user))
        }

        ElMessage.success('注册成功，欢迎使用！')

        // 跳转到控制台首页
        router.push('/console')
      } else {
        ElMessage.success('注册成功，请登录')
        router.push('/login')
      }
    } else if (result?.accessToken || result?.token) {
      // 直接返回token的格式
      userStore.setToken(result.accessToken || result.token)
      if (result.refreshToken) {
        userStore.setRefreshToken(result.refreshToken)
      }
      if (result.user) {
        userStore.userInfo.value = result.user
        localStorage.setItem('userInfo', JSON.stringify(result.user))
      }

      ElMessage.success('注册成功，欢迎使用！')
      router.push('/console')
    } else {
      // 没有返回token，需要手动登录
      ElMessage.success('注册成功，请登录')
      router.push('/login')
    }
  } catch (error: any) {
    console.error('注册失败:', error)
    ElMessage.error(error.message || '注册失败，请稍后重试')
    // 注册失败时重置验证状态
    showCaptchaModal.value = false
    // 清空安全验证数据
    securityData.value = {}
    isCaptchaVerified.value = false
    securityLevel.value = 'low'
  }
}

// 显示服务条款
const showTerms = async () => {
  try {
    const response = await fetch('/docs/terms-of-service.md')
    const content = await response.text()
    
    // 将Markdown转换为HTML显示
    const htmlContent = content
      .replace(/^# (.+)$/gm, '<h1>$1</h1>')
      .replace(/^## (.+)$/gm, '<h2>$1</h2>')
      .replace(/^### (.+)$/gm, '<h3>$1</h3>')
      .replace(/^#### (.+)$/gm, '<h4>$1</h4>')
      .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.+?)\*/g, '<em>$1</em>')
      .replace(/^- (.+)$/gm, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')
      .replace(/\n/g, '<br>')
    
    ElMessageBox({
      title: '服务条款',
      message: `<div style="max-height: 400px; overflow-y: auto; text-align: left; line-height: 1.6;">${htmlContent}</div>`,
      dangerouslyUseHTMLString: true,
      confirmButtonText: '我已阅读',
      showCancelButton: false,
      customClass: 'terms-dialog'
    })
  } catch (error) {
    ElMessageBox.alert(
      '服务条款加载失败，请稍后重试',
      '提示',
      {
        confirmButtonText: '确定',
        type: 'warning'
      }
    )
  }
}

// 显示隐私政策
const showPrivacy = async () => {
  try {
    const response = await fetch('/docs/privacy-policy.md')
    const content = await response.text()
    
    // 将Markdown转换为HTML显示
    const htmlContent = content
      .replace(/^# (.+)$/gm, '<h1>$1</h1>')
      .replace(/^## (.+)$/gm, '<h2>$1</h2>')
      .replace(/^### (.+)$/gm, '<h3>$1</h3>')
      .replace(/^#### (.+)$/gm, '<h4>$1</h4>')
      .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.+?)\*/g, '<em>$1</em>')
      .replace(/^- (.+)$/gm, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')
      .replace(/\n/g, '<br>')
    
    ElMessageBox({
      title: '隐私政策',
      message: `<div style="max-height: 400px; overflow-y: auto; text-align: left; line-height: 1.6;">${htmlContent}</div>`,
      dangerouslyUseHTMLString: true,
      confirmButtonText: '我已阅读',
      showCancelButton: false,
      customClass: 'privacy-dialog'
    })
  } catch (error) {
    ElMessageBox.alert(
      '隐私政策加载失败，请稍后重试',
      '提示',
      {
        confirmButtonText: '确定',
        type: 'warning'
      }
    )
  }
}

// 验证邮箱格式
const validateEmail = () => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  emailValid.value = emailRegex.test(registerForm.email as string)
}

// 邮箱输入处理
const onEmailInput = () => {
  validateEmail()
  
  // 如果邮箱已发送验证码且邮箱发生变化，重置状态
  if (emailCodeSent.value && registerForm.email !== originalEmail.value) {
    resetEmailCodeState()
  } else if (registerForm.email && emailValid.value) {
    // 如果邮箱有效，尝试恢复该邮箱的倒计时状态
    const restored = restoreCountdownState()
    
    // 如果没有恢复成功，确保状态是干净的
    if (!restored) {
      // 清除可能存在的倒计时状态，但保持邮箱输入可用
      if (countdownTimer.value) {
        clearInterval(countdownTimer.value)
        countdownTimer.value = null
      }
      countdown.value = 0
      sendCodeDisabled.value = false
      sendCodeText.value = '发送验证码'
    }
  }
}

// 重置邮箱验证码状态
const resetEmailCodeState = () => {
  emailCodeSent.value = false
  emailInputDisabled.value = false
  showEditIcon.value = false
  sendCodeDisabled.value = false
  sendCodeText.value = '发送验证码'
  registerForm.emailCode = ''
  countdown.value = 0
  // 清除倒计时
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
  // 清除localStorage中的倒计时状态
  clearCountdownState()
}

// 处理编辑邮箱
const handleEditEmail = async () => {
  try {
    await ElMessageBox.confirm(
      '正在发送邮箱验证码，确认要修改邮箱地址吗？修改后需要重新发送验证码。',
      '确认修改邮箱',
      {
        confirmButtonText: '确认修改',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 检查邮箱修改频率
    const now = Date.now()
    if (now - lastEmailChangeTime.value < 60000) { // 1分钟内
      emailChangeCount.value++
    } else {
      emailChangeCount.value = 1
    }
    lastEmailChangeTime.value = now
    
    // 如果频繁修改邮箱，触发拼图验证
    if (emailChangeCount.value >= 3) {
      ElMessage.warning('检测到频繁修改邮箱，需要完成安全验证')
      showCaptchaModal.value = true
      return
    }
    
    // 重置邮箱输入状态
    resetEmailCodeState()
    ElMessage.success('邮箱已重置，可以重新输入')
    
  } catch {
    // 用户取消修改
  }
}

// 发送短信验证码
const sendSmsCodeHandler = async () => {
  if (!registerForm.phone) {
    ElMessage.warning('请先输入手机号')
    return
  }

  // 验证手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(registerForm.phone)) {
    ElMessage.warning('请输入有效的手机号')
    return
  }

  try {
    smsSending.value = true
    const securityVerification = {
      level: 2,
      behaviorPattern: { verified: true },
      verificationTimestamp: Date.now(),
      type: 'behavior'
    }
    await sendSmsCode(registerForm.phone, 'register', securityVerification)

    ElMessage.success('短信验证码发送成功，请查收')
    
    // 开始倒计时
    smsCountdown.value = 60
    smsTimer = setInterval(() => {
      smsCountdown.value--
      if (smsCountdown.value <= 0) {
        clearInterval(smsTimer!)
        smsTimer = null
      }
    }, 1000)
    
  } catch (error: any) {
    console.error('发送短信验证码失败:', error)
    ElMessage.error(error.message || '发送短信验证码失败，请稍后重试')
  } finally {
    smsSending.value = false
  }
}

// 发送邮箱验证码
const sendEmailCodeHandler = async () => {
  if (!registerForm.email) {
    ElMessage.warning('请先输入邮箱地址')
    return
  }
  
  if (!emailValid.value) {
    ElMessage.warning('请输入有效的邮箱地址')
    return
  }
  
  try {
    sendingCode.value = true
    const securityVerification = {
      level: 2,
      behaviorPattern: { verified: true },
      verificationTimestamp: Date.now(),
      type: 'behavior'
    }
    await sendEmailCode(registerForm.email, 'register', securityVerification)

    // 发送成功后的状态设置
    emailCodeSent.value = true
    emailInputDisabled.value = true
    showEditIcon.value = true
    originalEmail.value = registerForm.email
    
    // 开始倒计时
    startCountdown()
    
    ElMessage.success('验证码已发送到您的邮箱')
  } catch (error: any) {
    console.error('发送验证码失败:', error)
    ElMessage.error('发送验证码失败，请稍后重试')
  } finally {
    sendingCode.value = false
  }
}

// 倒计时定时器引用
const countdownTimer = ref<any | null>(null)
const COUNTDOWN_DURATION = 300 // 5分钟 = 300秒
const COUNTDOWN_STORAGE_KEY = 'email_code_countdown'
const EMAIL_STORAGE_KEY = 'email_code_sent_email'

// 格式化时间显示
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 保存倒计时状态到localStorage
const saveCountdownState = (endTime: number, email: string) => {
  localStorage.setItem(COUNTDOWN_STORAGE_KEY, endTime.toString())
  localStorage.setItem(EMAIL_STORAGE_KEY, email)
}

// 清除倒计时状态
const clearCountdownState = () => {
  localStorage.removeItem(COUNTDOWN_STORAGE_KEY)
  localStorage.removeItem(EMAIL_STORAGE_KEY)
}

// 恢复倒计时状态
const restoreCountdownState = () => {
  const endTimeStr = localStorage.getItem(COUNTDOWN_STORAGE_KEY)
  const savedEmail = localStorage.getItem(EMAIL_STORAGE_KEY)
  
  if (endTimeStr && savedEmail) {
    const endTime = parseInt(endTimeStr)
    const now = Date.now()
    const remainingSeconds = Math.max(0, Math.floor((endTime - now) / 1000))
    
    // 检查倒计时是否已过期（超过5分钟）
    if (remainingSeconds <= 0) {
      // 倒计时已结束，清除所有相关状态
      clearCountdownState()
      resetEmailCodeState()
      return false
    }
    
    // 检查邮箱是否匹配
    if (savedEmail === registerForm.email) {
      // 恢复倒计时状态
      countdown.value = remainingSeconds
      sendCodeDisabled.value = true
      emailCodeSent.value = true
      emailInputDisabled.value = true
      showEditIcon.value = true
      sendCodeText.value = `${formatTime(countdown.value)}后重发`
      
      // 继续倒计时
      startCountdownTimer()
      return true
    } else {
      // 邮箱不匹配，清除状态
      clearCountdownState()
    }
  }
  return false
}

// 启动倒计时定时器
const startCountdownTimer = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
  
  countdownTimer.value = setInterval(() => {
    countdown.value--
    sendCodeText.value = `${formatTime(countdown.value)}后重发`
    
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value!)
      countdownTimer.value = null
      sendCodeDisabled.value = false
      sendCodeText.value = '发送验证码'
      clearCountdownState()
    }
  }, 1000)
}

// 倒计时功能
const startCountdown = () => {
  countdown.value = COUNTDOWN_DURATION // 5分钟 = 300秒
  sendCodeDisabled.value = true
  
  // 计算结束时间并保存到localStorage
  const endTime = Date.now() + (countdown.value * 1000)
  saveCountdownState(endTime, registerForm.email as string)
  
  sendCodeText.value = `${formatTime(countdown.value)}后重发`
  
  // 启动定时器
  startCountdownTimer()
}

// 组件挂载时恢复倒计时状态
onMounted(() => {
  // 首先验证当前邮箱
  if (registerForm.email) {
    validateEmail()
  }
  
  // 尝试恢复倒计时状态（无论邮箱是否有效都尝试，因为可能需要清理过期状态）
  const restored = restoreCountdownState()
  
  // 如果没有恢复成功且当前邮箱有效，确保状态正确
  if (!restored && registerForm.email && emailValid.value) {
    // 确保没有遗留的倒计时状态
    resetEmailCodeState()
  }
})

// 组件卸载时清理定时器（但保留localStorage状态）
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
  if (smsTimer) {
    clearInterval(smsTimer)
  }
})
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.register-container {
  width: 100%;
  max-width: 420px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  z-index: 1;
  margin: 20px auto;
}

.email-code-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.email-code-input {
  flex: 1;
}

.send-code-btn {
  min-width: 120px;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.send-code-btn:disabled {
  background-color: #c0c4cc !important;
  border-color: #c0c4cc !important;
  color: #ffffff !important;
  cursor: not-allowed;
  opacity: 0.8;
}

.send-code-btn:disabled:hover {
  background-color: #c0c4cc !important;
  border-color: #c0c4cc !important;
  color: #ffffff !important;
}

/* 邮箱输入容器样式 */
.email-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;
}

.email-input {
  flex: 1;
  min-width: 0; /* 确保输入框可以收缩 */
}

/* 当没有编辑图标时，输入框占满整行 */
.email-input-container:not(:has(.edit-email-icon)) .email-input {
  width: 100%;
}

/* 当有编辑图标时，为图标预留空间 */
.email-input-container:has(.edit-email-icon) .email-input {
  padding-right: 42px; /* 为图标预留空间 */
}

.edit-email-icon {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #409eff;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  border-radius: 6px;
  background: rgba(64, 158, 255, 0.1);
}

.edit-email-icon:hover {
  color: #66b1ff;
  background: rgba(64, 158, 255, 0.2);
  transform: translateY(-50%) scale(1.05);
}

/* 禁用状态的邮箱输入框样式 */
.email-input :deep(.el-input__inner:disabled) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

/* 验证码输入框禁用状态 */
.email-code-input :deep(.el-input__inner:disabled) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

/* 密码提示样式 */
.password-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

/* 注册方式切换样式 */
.register-type-switch {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
}

.register-type-group {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.register-type-group :deep(.el-radio-button__inner) {
  border: none;
  background: transparent;
  color: #606266;
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.register-type-group :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background: #409eff;
  color: white;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.register-type-group :deep(.el-radio-button__inner:hover) {
  color: #409eff;
}

/* 手机号和短信验证码输入框样式 */
.code-input-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.code-input-group .el-input {
  flex: 1;
}

.code-button {
  flex-shrink: 0;
  min-width: 120px;
  height: 40px;
  font-size: 14px;
  border-radius: 6px;
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.logo {
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  color: #409eff;
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 20px;
}

.logo-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.logo-text {
  color: #303133;
}

.register-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 10px 0;
}

.register-subtitle {
  color: #606266;
  margin: 0 0 30px 0;
  font-size: 14px;
}

.register-form {
  margin-bottom: 30px;
}

.register-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s;
}

.register-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.captcha-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.captcha-label span {
  flex: 1;
}

.captcha-label .el-tag {
  margin-left: 8px;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
}

.register-footer {
  text-align: center;
}

.login-link {
  color: #606266;
  font-size: 14px;
  margin: 0 0 20px 0;
}

.link {
  color: #409eff;
  text-decoration: none;
  font-weight: 500;
}

.link:hover {
  text-decoration: underline;
}

.divider {
  position: relative;
  margin: 20px 0;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e4e7ed;
  z-index: 0;
}

.divider span {
  background: rgba(255, 255, 255, 0.95);
  padding: 0 15px;
  position: relative;
  z-index: 1;
}

.social-register {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.social-button {
  width: 100%;
  height: 40px;
  border: 1px solid #e4e7ed;
  background: white;
  color: #606266;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.social-button:not(:first-child) {
    margin-left: 0;
  }

.social-button:hover {
  border-color: #409eff;
  color: #409eff;
}

.social-button .el-icon {
  font-size: 18px;
}

.social-button .el-icon svg {
  width: 18px;
  height: 18px;
  fill: currentColor;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .register-container {
    margin: 20px;
    padding: 30px 20px;
  }
  
  .register-title {
    font-size: 24px;
  }
  
  .social-register {
    gap: 10px;
  }
  
  .social-button {
    height: 36px;
    font-size: 14px;
  }
  .social-button:not(:first-child) {
    margin-left: 0;
  }
}
</style>