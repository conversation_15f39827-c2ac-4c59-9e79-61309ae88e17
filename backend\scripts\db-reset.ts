#!/usr/bin/env ts-node

import { DataSource } from 'typeorm';
import { createDataSourceConfig } from '../src/database/config/database.config';
import { seedManager } from '../src/database/seeds';
import { Logger } from '@nestjs/common';
import * as readline from 'readline';

/**
 * 数据库重置脚本
 * 重置数据库并重新初始化
 */
class DatabaseResetter {
  private readonly logger = new Logger('DatabaseResetter');
  private dataSource: DataSource;
  
  constructor() {
    this.dataSource = new DataSource(createDataSourceConfig());
  }
  
  /**
   * 重置数据库
   */
  async reset(): Promise<void> {
    try {
      // 解析命令行参数
      const options = this.parseArguments();
      
      // 安全确认
      if (!(await this.confirmReset(options.environment))) {
        this.logger.log('操作已取消');
        return;
      }
      
      this.logger.log('开始数据库重置...');
      
      // 连接数据库
      await this.connectDatabase();
      
      // 重置数据库
      if (options.dropTables) {
        await this.dropAllTables();
      } else {
        await this.clearAllData();
      }
      
      // 重新运行迁移
      if (options.migrate) {
        await this.runMigrations();
      }
      
      // 重新执行种子数据
      if (options.seed) {
        await this.runSeeds(options);
      }
      
      this.logger.log('数据库重置完成');
      
    } catch (error) {
      this.logger.error(`数据库重置失败: ${error.message}`);
      process.exit(1);
    } finally {
      if (this.dataSource.isInitialized) {
        await this.dataSource.destroy();
      }
    }
  }
  
  /**
   * 连接数据库
   */
  private async connectDatabase(): Promise<void> {
    this.logger.log('连接数据库...');
    await this.dataSource.initialize();
    this.logger.log('数据库连接成功');
  }
  
  /**
   * 删除所有表
   */
  private async dropAllTables(): Promise<void> {
    this.logger.log('删除所有表...');
    
    // 禁用外键检查
    await this.dataSource.query('SET FOREIGN_KEY_CHECKS = 0');
    
    try {
      // 获取所有表名
      const tables = await this.dataSource.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
      `);
      
      // 删除所有表
      for (const table of tables) {
        const tableName = table.table_name || table.TABLE_NAME;
        await this.dataSource.query(`DROP TABLE IF EXISTS \`${tableName}\``);
        this.logger.log(`删除表: ${tableName}`);
      }
      
      this.logger.log(`成功删除 ${tables.length} 个表`);
      
    } finally {
      // 重新启用外键检查
      await this.dataSource.query('SET FOREIGN_KEY_CHECKS = 1');
    }
  }
  
  /**
   * 清空所有数据
   */
  private async clearAllData(): Promise<void> {
    this.logger.log('清空所有数据...');
    
    // 禁用外键检查
    await this.dataSource.query('SET FOREIGN_KEY_CHECKS = 0');
    
    try {
      // 获取所有表名
      const tables = await this.dataSource.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
        AND table_type = 'BASE TABLE'
      `);
      
      // 清空所有表数据
      for (const table of tables) {
        const tableName = table.table_name || table.TABLE_NAME;
        
        // 跳过系统表
        if (tableName.startsWith('sys_') || tableName === 'migrations') {
          continue;
        }
        
        await this.dataSource.query(`TRUNCATE TABLE \`${tableName}\``);
        this.logger.log(`清空表: ${tableName}`);
      }
      
      this.logger.log(`成功清空 ${tables.length} 个表的数据`);
      
    } finally {
      // 重新启用外键检查
      await this.dataSource.query('SET FOREIGN_KEY_CHECKS = 1');
    }
  }
  
  /**
   * 执行数据库迁移
   */
  private async runMigrations(): Promise<void> {
    this.logger.log('执行数据库迁移...');
    
    await this.dataSource.runMigrations();
    this.logger.log('数据库迁移完成');
  }
  
  /**
   * 执行种子数据
   */
  private async runSeeds(options: any): Promise<void> {
    this.logger.log('执行种子数据...');
    
    const seedOptions = {
      environment: options.environment,
      force: true, // 重置时强制执行
      verbose: options.verbose,
    };
    
    const results = await seedManager.run(this.dataSource, seedOptions);
    
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    this.logger.log(`种子数据执行完成: 成功 ${successful}, 失败 ${failed}`);
  }
  
  /**
   * 确认重置操作
   */
  private async confirmReset(environment: string): Promise<boolean> {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
    
    return new Promise((resolve) => {
      const message = environment === 'production' 
        ? '⚠️  您正在生产环境中重置数据库，这将删除所有数据！是否继续？(y/N): '
        : `您正在 ${environment} 环境中重置数据库，是否继续？(y/N): `;
      
      rl.question(message, (answer) => {
        rl.close();
        resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
      });
    });
  }
  
  /**
   * 解析命令行参数
   */
  private parseArguments(): any {
    const args = process.argv.slice(2);
    const options = {
      environment: process.env.NODE_ENV || 'development',
      dropTables: false,
      migrate: true,
      seed: true,
      verbose: false,
    };
    
    for (let i = 0; i < args.length; i++) {
      const arg = args[i];
      
      switch (arg) {
        case '--env':
        case '--environment':
          options.environment = args[++i];
          break;
        case '--drop-tables':
          options.dropTables = true;
          break;
        case '--no-migrate':
          options.migrate = false;
          break;
        case '--no-seed':
          options.seed = false;
          break;
        case '--verbose':
          options.verbose = true;
          break;
        case '--help':
          this.showHelp();
          process.exit(0);
          break;
      }
    }
    
    return options;
  }
  
  /**
   * 显示帮助信息
   */
  private showHelp(): void {
    console.log(`
数据库重置工具

用法: npm run db:reset [选项]

选项:
  --env <environment>   指定环境 (development|test|production)
  --drop-tables        删除所有表（而不是仅清空数据）
  --no-migrate         跳过数据库迁移
  --no-seed           跳过种子数据
  --verbose           显示详细日志
  --help              显示帮助信息

示例:
  npm run db:reset                      # 重置数据库（清空数据）
  npm run db:reset -- --drop-tables     # 重置数据库（删除表）
  npm run db:reset -- --env test        # 重置测试数据库
    `);
  }
}

/**
 * 主函数
 */
async function main() {
  const resetter = new DatabaseResetter();
  await resetter.reset();
}

// 执行重置
if (require.main === module) {
  main().catch(console.error);
}
