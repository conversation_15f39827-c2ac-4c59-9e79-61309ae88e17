import { Processor, Process, OnQueueActive, OnQueueCompleted, OnQueueFailed } from '@nestjs/bull';
import { Job } from 'bull';
import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { StructuredLogger } from '../../../common/logging/structured-logger';
import { TaskService } from '../services/task.service';
import { TaskStatus } from '../interfaces/task-status.enum';
import { StoOcrJobData, StoOcrResult } from '../interfaces/queue-job.interface';
import { firstValueFrom } from 'rxjs';

/**
 * 申通OCR任务处理器
 * 专门处理申通面单OCR识别任务的执行和状态更新
 * 遵循单一职责原则，专注于申通OCR任务处理
 */
@Injectable()
@Processor('sto-ocr')
export class StoOcrProcessor {
  private readonly stoOcrServiceUrl: string;

  constructor(
    private readonly taskService: TaskService,
    private readonly logger: StructuredLogger,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.stoOcrServiceUrl = this.configService.get<string>(
      'STO_OCR_SERVICE_URL',
      'http://localhost:8867' // 申通OCR专用服务端口
    );
  }

  /**
   * 处理申通OCR任务
   */
  @Process()
  async processStoOcrTask(job: Job<StoOcrJobData>): Promise<StoOcrResult> {
    const { taskId, imageData, options = {} } = job.data;
    
    try {
      // 更新任务状态为处理中
      await this.taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING, {
        progress: 0,
        message: '开始申通面单识别',
      });
      
      // 调用申通OCR服务进行识别
      const result = await this.callStoOcrService(taskId, imageData, options);
      
      // 更新任务状态为完成
      await this.taskService.updateTaskStatus(taskId, TaskStatus.COMPLETED, {
        progress: 100,
        message: '申通面单识别完成',
        result,
      });
      
      this.logger.log(
        `申通OCR任务${taskId}处理完成，置信度: ${result.confidence}`,
        { module: 'StoOcrProcessor' }
      );
      
      return result;
      
    } catch (error) {
      this.logger.error(
        `申通OCR任务${taskId}处理失败: ${error.message}`,
        error,
        { module: 'StoOcrProcessor' }
      );
      
      // 更新任务状态为失败
      await this.taskService.updateTaskStatus(taskId, TaskStatus.FAILED, {
        progress: 0,
        message: `申通面单识别失败: ${error.message}`,
        error: error.message,
      });
      
      throw error;
    }
  }

  /**
   * 调用申通OCR服务
   */
  private async callStoOcrService(
    taskId: string,
    imageData: string | Buffer,
    options: any
  ): Promise<StoOcrResult> {
    try {
      // 更新进度
      await this.taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING, {
        progress: 20,
        message: '正在调用申通OCR服务',
      });

      // 如果配置了实际的申通OCR服务，调用真实服务
      if (this.configService.get<boolean>('USE_REAL_STO_OCR_SERVICE', false)) {
        return await this.callRealStoOcrService(taskId, imageData, options);
      }

      // 否则使用模拟服务（开发和测试环境）
      return await this.simulateStoOcrService(taskId, imageData, options);

    } catch (error) {
      this.logger.error(
        `申通OCR服务调用失败: ${error.message}`,
        error,
        { module: 'StoOcrProcessor' }
      );
      throw error;
    }
  }

  /**
   * 调用真实的申通OCR服务
   */
  private async callRealStoOcrService(
    taskId: string,
    imageData: string | Buffer,
    options: any
  ): Promise<StoOcrResult> {
    try {
      // 准备请求数据
      const requestData = {
        image: imageData,
        options: {
          enhanceAccuracy: options.enhanceAccuracy ?? true,
          extractStoFields: options.extractStoFields ?? true,
          ...options,
        },
      };

      // 更新进度
      await this.taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING, {
        progress: 40,
        message: '正在进行申通面单识别',
      });

      // 调用申通OCR服务
      const response = await firstValueFrom(
        this.httpService.post(`${this.stoOcrServiceUrl}/api/sto-ocr/recognize`, requestData, {
          timeout: 180000, // 3分钟超时
          headers: {
            'Content-Type': 'application/json',
            'X-Task-ID': taskId,
          },
        })
      );

      // 更新进度
      await this.taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING, {
        progress: 80,
        message: '申通面单识别完成，正在处理结果',
      });

      return response.data;

    } catch (error) {
      this.logger.error(
        `真实申通OCR服务调用失败: ${error.message}`,
        error,
        { module: 'StoOcrProcessor' }
      );
      throw new Error(`申通OCR服务调用失败: ${error.message}`);
    }
  }

  /**
   * 模拟申通OCR服务（开发和测试环境）
   */
  private async simulateStoOcrService(
    taskId: string,
    imageData: string | Buffer,
    options: any
  ): Promise<StoOcrResult> {
    // 模拟处理时间
    await this.simulateProcessingSteps(taskId);

    // 生成模拟的申通OCR结果
    const result: StoOcrResult = {
      sender: {
        name: '张三',
        phone: '13800138000',
        address: '北京市海淀区中关村大街1号',
      },
      receiver: {
        name: '李四',
        phone: '13900139000',
        address: '上海市浦东新区张江高科技园区',
      },
      expressInfo: {
        company: '申通快递',
        trackingNumber: `STO${Date.now().toString().slice(-8)}`,
        serviceType: '标准快递',
        weight: '1.2kg',
      },
      stoSpecific: {
        sortingCode: '021-PD-001',
        routeCode: 'SH-BJ-001',
        packageType: '标准包装',
      },
      rawText: '申通快递面单原始文本内容...',
      confidence: 0.95 + Math.random() * 0.04, // 95-99%的置信度
    };

    this.logger.log(
      `申通OCR模拟识别完成，任务ID: ${taskId}，置信度: ${result.confidence}`,
      { module: 'StoOcrProcessor' }
    );

    return result;
  }

  /**
   * 模拟处理步骤
   */
  private async simulateProcessingSteps(taskId: string): Promise<void> {
    const steps = [
      { progress: 30, message: '正在预处理申通面单图像', delay: 1000 },
      { progress: 50, message: '正在识别申通面单文字', delay: 2000 },
      { progress: 70, message: '正在提取申通特有字段', delay: 1500 },
      { progress: 90, message: '正在验证识别结果', delay: 800 },
    ];

    for (const step of steps) {
      await new Promise(resolve => setTimeout(resolve, step.delay));
      await this.taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING, {
        progress: step.progress,
        message: step.message,
      });
    }
  }

  /**
   * 任务开始处理时的回调
   */
  @OnQueueActive()
  onActive(job: Job<StoOcrJobData>) {
    this.logger.log(
      `申通OCR任务开始处理，任务ID: ${job.data.taskId}，作业ID: ${job.id}`,
      { module: 'StoOcrProcessor' }
    );
  }

  /**
   * 任务完成时的回调
   */
  @OnQueueCompleted()
  onCompleted(job: Job<StoOcrJobData>, result: StoOcrResult) {
    const processingTime = Date.now() - job.timestamp;
    this.logger.log(
      `申通OCR任务处理完成，任务ID: ${job.data.taskId}，处理时间: ${processingTime}ms`,
      { module: 'StoOcrProcessor' }
    );
  }

  /**
   * 任务失败时的回调
   */
  @OnQueueFailed()
  onFailed(job: Job<StoOcrJobData>, error: Error) {
    this.logger.error(
      `申通OCR任务处理失败，任务ID: ${job.data.taskId}，尝试次数: ${job.attemptsMade}/${job.opts.attempts}`,
      error,
      { module: 'StoOcrProcessor' }
    );
  }
}
