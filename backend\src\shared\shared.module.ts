import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RedisModule } from '@nestjs-modules/ioredis';
import { ServeStaticModule } from '@nestjs/serve-static';
import { JwtModule } from '@nestjs/jwt';
import { HttpModule } from '@nestjs/axios';
import { ThrottlerModule } from '@nestjs/throttler';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { join } from 'path';
import { RedisService } from './redis.service';
import { ErrorHandlerService } from './error-handler.service';
import { EnhancedCacheManagerService } from './enhanced-cache-manager.service';
import { EmailService } from './email.service';
import { SmsService } from './sms.service'
import { CaptchaService } from './captcha.service'
import { UserCacheService } from './user-cache.service';
import { AuthCacheService } from './auth-cache.service';
import { LayeredCacheService } from './layered-cache.service';
import { CoreAuthService } from './core-auth.service';
import { StructuredLogger } from '../common/logging/structured-logger';
import { ResponseFormatterService } from './response-formatter.service';
import { MetricsService } from './metrics.service';
import { ApiCategorizerService } from './api-categorizer.service';

/**
 * 共享模块
 * 提供全局公共服务，包括Redis、静态文件服务、JWT、邮件发送、短信发送、验证码、错误码服务、缓存封装服务、认证封装、用户封装处理服务、http等
 */
@Global()
@Module({
  imports: [
    // 配置模块 - 全局配置服务
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
    }),
    // 事件模块配置
    EventEmitterModule.forRoot({
      // 设置为true以使用通配符
      wildcard: false,
      // 分隔符
      delimiter: '.',
      // 最大监听器数量
      maxListeners: 10,
      // 是否详细输出内存泄漏警告
      verboseMemoryLeak: false,
    }),
    // Redis模块配置 - 可选连接
    RedisModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        // 检查是否启用Redis
        const redisEnabled = configService.get('REDIS_ENABLED', 'false') === 'true';
        if (!redisEnabled) {
          console.log('Redis is disabled, skipping connection');
          return {
            type: 'single',
            options: {
              host: 'localhost',
              port: 6379,
              lazyConnect: true,
              retryDelayOnFailover: 100,
              enableReadyCheck: false,
              maxRetriesPerRequest: 0, // 禁用重试
              connectTimeout: 1000,
              commandTimeout: 1000,
            },
          };
        }

        // 支持URL配置或分离配置
        const redisUrl = configService.get('REDIS_URL');
        if (redisUrl) {
          return {
            type: 'single',
            url: redisUrl,
            options: {
              retryDelayOnFailover: 100,
              enableReadyCheck: false,
              maxRetriesPerRequest: 3,
              lazyConnect: true,
              connectTimeout: 10000,
              commandTimeout: 5000,
            },
          };
        }
        // 分离配置方式
        const password = configService.get('redis.password');
        return {
          type: 'single',
          options: {
            host: configService.get('redis.host', 'localhost'),
            port: configService.get('redis.port', 6379),
            password: password || undefined, // 如果密码为空字符串，设置为undefined
            db: configService.get('redis.db', 0),
            retryDelayOnFailover: 100,
            enableReadyCheck: false,
            maxRetriesPerRequest: 3,
            lazyConnect: true,
            connectTimeout: 10000,
            commandTimeout: 5000,
          },
        };
      },
      inject: [ConfigService],
    }),
    // 静态文件服务模块
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', '..', 'docs'),
      serveRoot: '/docs',
    }),
    // JWT模块配置
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('jwt.secretkey', 'zANDwNQVFzxlfG9myPxVWAkq4iXJEPhy'),
        signOptions: {
          expiresIn: configService.get('jwt.expiresin', '2h'),
        },
        global: true,
      }),
      inject: [ConfigService],
    }),
    HttpModule.register({
      timeout: 8000,
      maxRedirects: 5,
    }),
    // 限流模块配置
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        throttlers: [
          {
            name: 'short',
            ttl: 1000, // 1秒
            limit: configService.get('THROTTLE_SHORT_LIMIT', 10),
          },
          {
            name: 'medium',
            ttl: 10000, // 10秒
            limit: configService.get('THROTTLE_MEDIUM_LIMIT', 20),
          },
          {
            name: 'long',
            ttl: 60000, // 60秒
            limit: configService.get('THROTTLE_LONG_LIMIT', 100),
          },
        ],
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [
    RedisService,
    LayeredCacheService,
    CoreAuthService,
    AuthCacheService,
    ErrorHandlerService,
    EnhancedCacheManagerService,
    UserCacheService,
    EmailService,
    SmsService,
    CaptchaService,
    {
      provide: StructuredLogger,
      useFactory: (configService: ConfigService) => {
        return new StructuredLogger(configService, 'SharedModule');
      },
      inject: [ConfigService],
    },
    ResponseFormatterService,
    MetricsService,
    ApiCategorizerService,
  ],
  exports: [
    ConfigModule,
    RedisModule,
    ServeStaticModule,
    JwtModule,
    HttpModule,
    EventEmitterModule,
    RedisService,
    LayeredCacheService,
    CoreAuthService,
    AuthCacheService,
    ErrorHandlerService,
    EnhancedCacheManagerService,
    UserCacheService,
    EmailService,
    SmsService,
    CaptchaService,
    StructuredLogger,
    ResponseFormatterService,
    MetricsService,
    ApiCategorizerService,
  ],
})
export class SharedModule {}