<template>
  <div class="request-config">
    <div class="config-header">
      <h3>请求配置</h3>
      <div class="header-actions">
        <el-button size="small" @click="$emit('load-example')">
          <el-icon><Document /></el-icon>
          加载示例
        </el-button>
        <el-button size="small" @click="$emit('reset')">
          <el-icon><RefreshLeft /></el-icon>
          重置
        </el-button>
      </div>
    </div>

    <el-form :model="config" label-width="100px" class="config-form">
      <!-- 请求URL -->
      <el-form-item label="请求URL" required>
        <el-input :model-value="config.url" @update:model-value="$emit('update:url', $event)" placeholder="API请求地址">
          <template #prepend>
            <el-select :model-value="config.method" @update:model-value="$emit('update:method', $event)" style="width: 100px;">
              <el-option label="GET" value="GET" />
              <el-option label="POST" value="POST" />
              <el-option label="PUT" value="PUT" />
              <el-option label="DELETE" value="DELETE" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <!-- API密钥 -->
      <el-form-item label="API密钥" required>
        <el-input 
          :model-value="config.apiKey" 
          @update:model-value="$emit('update:api-key', $event)"
          placeholder="请输入或选择API密钥"
          type="password"
          show-password
        >
          <template #append>
            <el-button @click="showApiKeySelector">
              选择
            </el-button>
          </template>
        </el-input>
      </el-form-item>

      <!-- 请求头 -->
      <el-form-item label="请求头">
        <div class="headers-section">
          <div 
            v-for="(header, index) in config.headers" 
            :key="index"
            class="header-row"
          >
            <el-input 
              :model-value="header.key" 
              @update:model-value="updateHeaderKey(index, $event)"
              placeholder="Header名称"
              class="header-key"
            />
            <el-input 
              :model-value="header.value" 
              @update:model-value="updateHeaderValue(index, $event)"
              placeholder="Header值"
              class="header-value"
            />
            <el-button 
              type="danger" 
              size="small" 
              @click="removeHeader(index)"
              :disabled="config.headers.length <= 1"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
          <el-button 
            type="primary" 
            size="small" 
            @click="addHeader"
            class="add-btn"
          >
            <el-icon><Plus /></el-icon>
            添加请求头
          </el-button>
        </div>
      </el-form-item>

      <!-- 请求参数 (GET请求或表单数据) -->
      <el-form-item label="请求参数" v-if="config.method === 'GET' || config.bodyType === 'form-data'">
        <div class="params-section">
          <div 
            v-for="(param, index) in config.params" 
            :key="index"
            class="param-row"
          >
            <el-input 
              v-model="param.key" 
              placeholder="参数名"
              class="param-key"
            />
            <el-input 
              v-model="param.value" 
              placeholder="参数值"
              class="param-value"
            />
            <el-select v-model="param.type" class="param-type">
              <el-option label="字符串" value="string" />
              <el-option label="数字" value="number" />
              <el-option label="布尔" value="boolean" />
              <el-option label="文件" value="file" />
            </el-select>
            <el-button 
              type="danger" 
              size="small" 
              @click="$emit('remove-param', index)"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
          <el-button 
            type="primary" 
            size="small" 
            @click="$emit('add-param')"
            class="add-btn"
          >
            <el-icon><Plus /></el-icon>
            添加参数
          </el-button>
        </div>
      </el-form-item>

      <!-- 请求体 -->
      <el-form-item label="请求体" v-if="config.method !== 'GET'">
        <div class="body-section">
          <div class="body-type-selector">
            <el-radio-group :model-value="config.bodyType" @update:model-value="$emit('update:body-type', $event)" size="small">
              <el-radio-button label="json">JSON</el-radio-button>
              <el-radio-button label="form-data">Form Data</el-radio-button>
              <el-radio-button label="x-www-form-urlencoded">URL Encoded</el-radio-button>
            </el-radio-group>
          </div>
          
          <div class="body-editor" v-if="config.bodyType === 'json'">
            <el-input 
              :model-value="config.body"
              @update:model-value="$emit('update:body', $event)"
              type="textarea"
              :rows="8"
              placeholder="请输入JSON格式的请求体"
              class="json-editor"
            />
            <div class="editor-actions">
              <el-button size="small" @click="formatJson">
                <el-icon><MagicStick /></el-icon>
                格式化
              </el-button>
              <el-button size="small" @click="validateJson">
                <el-icon><CircleCheck /></el-icon>
                验证
              </el-button>
            </div>
          </div>
          
          <div class="form-data-editor" v-else>
            <el-alert 
              title="表单数据将使用上面的请求参数配置"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
        </div>
      </el-form-item>
      
      <!-- 发送请求按钮 -->
      <el-form-item>
        <div class="action-buttons">
          <el-button 
            type="primary" 
            size="large"
            @click="$emit('send-request')"
            :disabled="!config.url || !config.apiKey"
          >
            <el-icon><Position /></el-icon>
            发送请求
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Document,
  RefreshLeft,
  Delete,
  Plus,
  MagicStick,
  CircleCheck,
  Position
} from '@element-plus/icons-vue'

interface Props {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  url: string
  headers: Record<string, string>
  body: string
  apiKey?: string
  availableKeys?: any[]
  bodyType?: 'json' | 'form-data' | 'x-www-form-urlencoded'
  params?: Array<{ key: string; value: string; type: string }>
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:method': [method: 'GET' | 'POST' | 'PUT' | 'DELETE']
  'update:url': [url: string]
  'update:headers': [headers: Record<string, string>]
  'update:body': [body: string]
  'update:api-key': [apiKey: string]
  'update:body-type': [bodyType: 'json' | 'form-data' | 'x-www-form-urlencoded']
  'add-param': []
  'remove-param': [index: number]
  'send-request': []
  'load-example': []
  'reset': []
}>()

// 转换为组件内部使用的格式
const config = computed(() => ({
  method: props.method,
  url: props.url,
  headers: Object.entries(props.headers || {}).map(([key, value]) => ({ key, value })),
  body: props.body,
  apiKey: props.apiKey || '',
  bodyType: props.bodyType || 'json',
  params: props.params || []
}))

// 更新headers的方法
const updateHeaderKey = (index: number, key: string) => {
  const newHeaders = { ...props.headers }
  const oldKey = config.value.headers[index]?.key
  const value = config.value.headers[index]?.value || ''
  
  if (oldKey && oldKey !== key) {
    delete newHeaders[oldKey]
  }
  if (key) {
    newHeaders[key] = value
  }
  
  emit('update:headers', newHeaders)
}

const updateHeaderValue = (index: number, value: string) => {
  const newHeaders = { ...props.headers }
  const key = config.value.headers[index]?.key
  
  if (key) {
    newHeaders[key] = value
  }
  
  emit('update:headers', newHeaders)
}

const addHeader = () => {
  const newHeaders = { ...props.headers }
  newHeaders[''] = ''
  emit('update:headers', newHeaders)
}

const removeHeader = (index: number) => {
  const newHeaders = { ...props.headers }
  const key = config.value.headers[index]?.key
  
  if (key) {
    delete newHeaders[key]
  }
  
  emit('update:headers', newHeaders)
}

const showApiKeySelector = () => {
  // 这里可以显示API密钥选择对话框
  ElMessage.info('API密钥选择功能开发中...')
}

// JSON格式化和验证方法
const formatJson = () => {
  try {
    const parsed = JSON.parse(config.value.body)
    emit('update:body', JSON.stringify(parsed, null, 2))
    ElMessage.success('JSON格式化成功')
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化')
  }
}

const validateJson = () => {
  try {
    JSON.parse(config.value.body)
    ElMessage.success('JSON格式正确')
  } catch (error) {
    ElMessage.error('JSON格式错误')
  }
}
</script>

<style scoped>
.request-config {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f2f5;
}

.config-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.config-form {
  max-width: none;
}

.headers-section,
.params-section {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  background: #fafbfc;
}

.header-row,
.param-row {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  align-items: center;
}

.header-row:last-of-type,
.param-row:last-of-type {
  margin-bottom: 0;
}

.header-key,
.param-key {
  flex: 1;
}

.header-value,
.param-value {
  flex: 2;
}

.param-type {
  width: 100px;
}

.add-btn {
  margin-top: 8px;
}

.body-section {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.body-type-selector {
  padding: 12px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.body-editor {
  position: relative;
}

.json-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.json-editor :deep(.el-textarea__inner) {
  border: none;
  border-radius: 0;
  font-family: inherit;
  font-size: 13px;
  line-height: 1.5;
}

.editor-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
}

.form-data-editor {
  padding: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .request-config {
    padding: 16px;
  }
  
  .config-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .header-row,
  .param-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .param-type {
    width: 100%;
  }
}
</style>