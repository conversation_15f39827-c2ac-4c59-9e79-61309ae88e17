import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';

import { APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { UnifiedAuthGuard } from './common/guards/unified-auth.guard';
import { CustomThrottlerGuard } from './common/guards/custom-throttler.guard';
import { UnifiedInterceptor } from './common/interceptors/unified.interceptor';
import { StructuredLogger } from './common/logging/structured-logger';
import { SharedModule } from './shared/shared.module';
// import { RateLimitModule } from './common/rate-limit/rate-limit.module';

import { OpenModule } from './modules/open.module';
import { QueueModule } from './modules/queue/queue.module';
import { GatewayModule } from './modules/gateway/gateway.module';
import configuration from './config';

/**
 * 应用程序主模块
 */
@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      ignoreEnvFile: true, // 完全忽略 .env 文件
      cache: true,
      load: [() => require('./config/index').default()], // 直接使用
      isGlobal: true,
    }),
    // 数据库模块
    TypeOrmModule.forRootAsync({
      useFactory: (configService: ConfigService): TypeOrmModuleOptions => {
        const dbConfig = configService.get('database');
        if (!dbConfig) {
          throw new Error('Database configuration not found');
        }
        return dbConfig;
      },
      inject: [ConfigService],
    }),
    // 静态文件服务
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'public'),
      serveRoot: '/static',
    }),
    // 业务模块
    SharedModule, // 共享模块，包含Redis、限流、静态文件服务等
    // RateLimitModule,
    OpenModule,
    QueueModule, // 队列模块
    GatewayModule, // 网关模块
  ],
  controllers: [],
  providers: [
    // 全局限流守卫（优先级最高）
    {
      provide: APP_GUARD,
      useClass: CustomThrottlerGuard,
    },
    // 全局统一鉴权守卫
    {
      provide: APP_GUARD,
      useClass: UnifiedAuthGuard,
    },
    // 全局统一响应拦截器
    {
      provide: APP_INTERCEPTOR,
      useClass: UnifiedInterceptor,
    },
    {
      provide: StructuredLogger,
      useFactory: (configService: ConfigService) => {
        return new StructuredLogger(configService, 'AppModule');
      },
      inject: [ConfigService],
    },
  ],
})
export class AppModule {}
