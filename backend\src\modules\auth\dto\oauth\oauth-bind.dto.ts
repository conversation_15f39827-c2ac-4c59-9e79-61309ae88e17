import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { ThirdPartyType } from '@/modules/user/entities/user.entity';

export class OAuthBindDto {
  @IsEnum(ThirdPartyType, { message: '无效的第三方平台类型' })
  @ApiProperty({ description: '第三方平台类型', enum: ThirdPartyType })
  providerType: ThirdPartyType;
  
  @IsString()
  @IsNotEmpty({ message: '授权码不能为空' })
  @ApiProperty({ description: '授权码' })
  code: string;
} 