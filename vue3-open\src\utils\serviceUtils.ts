import type { ServiceType, ServiceStatus } from '@/types/service'

// 服务类型映射 - 与后端ServiceType枚举保持一致
export const SERVICE_TYPE_MAP = {
  AI_SERVICE: 'ai_service',
  OCR: 'ocr',
  NLP: 'nlp',
  CV: 'cv',
  GEO: 'geo',
  DATA: 'data',
  OTHER: 'other'
} as const

// 服务类型显示名称
export const SERVICE_TYPE_LABELS: Record<ServiceType, string> = {
  ai_service: 'AI服务',
  ocr: 'OCR识别',
  nlp: 'NLP处理',
  cv: '计算机视觉',
  geo: '地理服务',
  data: '数据服务',
  other: '其他服务'
}

// 服务状态映射
export const SERVICE_STATUS_MAP = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  MAINTENANCE: 'maintenance',
  DEPRECATED: 'deprecated'
} as const

// 服务状态显示名称
export const SERVICE_STATUS_LABELS: Record<ServiceStatus, string> = {
  active: '可用',
  inactive: '不可用',
  maintenance: '维护中',
  deprecated: '已废弃'
}

// 服务状态标签类型
export const SERVICE_STATUS_TYPES: Record<ServiceStatus, string> = {
  active: 'success',
  inactive: 'info',
  maintenance: 'warning',
  deprecated: 'danger'
}

// 服务类型颜色映射
export const SERVICE_TYPE_COLORS: Record<ServiceType, string> = {
  ai_service: '#8b5cf6',
  ocr: '#3b82f6',
  nlp: '#10b981',
  cv: '#f59e0b',
  geo: '#ef4444',
  data: '#6b7280',
  other: '#9ca3af'
}

/**
 * 获取服务类型显示名称
 */
export function getServiceTypeLabel(type: ServiceType): string {
  return SERVICE_TYPE_LABELS[type] || '未知类型'
}

/**
 * 获取服务状态显示名称
 */
export function getServiceStatusLabel(status: ServiceStatus): string {
  return SERVICE_STATUS_LABELS[status] || '未知状态'
}

/**
 * 获取服务状态标签类型
 */
export function getServiceStatusType(status: ServiceStatus): string {
  return SERVICE_STATUS_TYPES[status] || 'info'
}

/**
 * 获取服务类型颜色
 */
export function getServiceTypeColor(type: ServiceType): string {
  return SERVICE_TYPE_COLORS[type] || '#6b7280'
}

/**
 * 验证服务类型是否有效
 */
export function isValidServiceType(type: string): type is ServiceType {
  return Object.values(SERVICE_TYPE_MAP).includes(type as ServiceType)
}

/**
 * 验证服务状态是否有效
 */
export function isValidServiceStatus(status: string): status is ServiceStatus {
  return Object.values(SERVICE_STATUS_MAP).includes(status as ServiceStatus)
}

/**
 * 获取服务价格
 */
export function getServicePrice(service: any): number {
  // 优先使用unitPrice字段
  if (service.unitPrice !== undefined) {
    return service.unitPrice
  }
  // 其次使用price字段
  if (service.price !== undefined) {
    return service.price
  }
  // 最后使用pricing字段
  if (service.pricing?.price !== undefined) {
    return service.pricing.price
  }
  return 0
}

/**
 * 将特性字符串转换为数组
 */
export function getFeaturesList(features: string | string[]): string[] {
  if (typeof features === 'string') {
    return features ? features.split(',').map(f => f.trim()) : []
  }
  return features || []
}

/**
 * 格式化服务价格显示
 */
export function formatServicePrice(price: number): string {
  return `¥${price}/次`
} 