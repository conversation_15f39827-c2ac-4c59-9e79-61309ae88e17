<template>
  <div class="service-detail-view">
    <!-- 面包屑导航 -->
    <el-breadcrumb class="breadcrumb" separator=">">
      <el-breadcrumb-item :to="{ name: 'home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ name: 'services' }">服务列表</el-breadcrumb-item>
      <el-breadcrumb-item>{{ service?.name || '服务详情' }}</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <el-result
        icon="error"
        title="加载失败"
        :sub-title="error"
      >
        <template #extra>
          <el-button type="primary" @click="loadServiceDetail">
            重新加载
          </el-button>
        </template>
      </el-result>
    </div>

    <!-- 服务详情内容 -->
    <div v-else-if="service" class="service-content">
      <!-- 服务头部信息 -->
      <div class="service-header">
        <div class="service-info">
          <div class="service-icon">
            <el-icon :size="48" :color="service.color">
              <component :is="getServiceIcon(service.icon)" />
            </el-icon>
          </div>
          <div class="service-meta">
            <div class="service-title">
              <h1>{{ service.name }}</h1>
              <el-tag
                :type="getStatusTagType(service.serviceStatus || service.status)"
                size="large"
              >
                {{ getStatusText(service.serviceStatus || service.status) }}
              </el-tag>
            </div>
            <p class="service-description">{{ service.description }}</p>
            <div class="service-stats">
              <div class="stat-item">
                <span class="stat-label">价格:</span>
                <span class="stat-value">¥{{ getServicePriceValue(service) }}/次</span>
              </div>
              <div class="stat-item" v-if="service.accuracy">
                <span class="stat-label">准确率:</span>
                <span class="stat-value">{{ service.accuracy }}%</span>
              </div>
              <div class="stat-item" v-if="service.responseTime">
                <span class="stat-label">响应时间:</span>
                <span class="stat-value">{{ service.responseTime }}ms</span>
              </div>
              <div class="stat-item" v-if="service.supportedFormats">
                <span class="stat-label">支持格式:</span>
                <span class="stat-value">{{ Array.isArray(service.supportedFormats) ? service.supportedFormats.join(', ') : service.supportedFormats }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="service-actions">
          <el-button
            type="primary"
            size="large"
            @click="tryService"
            :disabled="(service.serviceStatus || service.status) !== 'active'"
          >
            立即体验
          </el-button>
          <el-button
            size="large"
            @click="viewDocs"
          >
            查看文档
          </el-button>
          <!-- <el-button
            size="large"
            @click="viewPricing"
          >
            查看定价
          </el-button> -->
        </div>
      </div>

      <!-- 服务详细信息 -->
      <el-row :gutter="24" class="detail-content" v-if="service?.features && service.features?.includes(',')">
        <!-- 左侧主要内容 -->
        <el-col :lg="16" :md="24">
          <!-- 功能特性 -->
          <el-card class="feature-card">
            <template #header>
              <h3>功能特性</h3>
            </template>
            <div class="features-grid">
              <div
                v-for="feature in getFeaturesListValue(service.features)"
                :key="feature"
                class="feature-item"
              >
                <div class="feature-icon">
                  <el-icon :color="'#409eff'">
                    <component :is="getFeatureIcon()" />
                  </el-icon>
                </div>
                <div class="feature-content">
                  <h4>{{ feature }}</h4>
                </div>
              </div>
            </div>
          </el-card>

          <!-- API 示例 -->
          <el-card class="api-example-card">
            <template #header>
              <div class="card-header">
                <h3>API 示例</h3>
                <el-radio-group v-model="activeLanguage" size="small">
                  <el-radio-button label="curl">cURL</el-radio-button>
                  <el-radio-button label="javascript">JavaScript</el-radio-button>
                  <el-radio-button label="python">Python</el-radio-button>
                  <el-radio-button label="java">Java</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="api-example">
              <div class="example-header">
                <span class="example-title">{{ getExampleTitle(activeLanguage) }}</span>
                <el-button
                  type="text"
                  size="small"
                  @click="copyCode"
                >
                  复制代码
                </el-button>
              </div>
              <pre class="code-block"><code :class="`language-${activeLanguage}`">{{ getCodeExample(activeLanguage) }}</code></pre>
            </div>
          </el-card>

          <!-- 使用说明 -->
          <el-card class="usage-guide-card">
            <template #header>
              <h3>使用说明</h3>
            </template>
            <div class="usage-guide">
              <el-steps :active="0" direction="vertical">
                <el-step
                  v-for="(step, index) in usageSteps"
                  :key="index"
                  :title="step.title"
                  :description="step.description"
                />
              </el-steps>
            </div>
          </el-card>

          <!-- 常见问题 -->
          <el-card class="faq-card">
            <template #header>
              <h3>常见问题</h3>
            </template>
            <el-collapse v-model="activeFaq">
              <el-collapse-item
                v-for="(faq, index) in faqs"
                :key="index"
                :title="faq.question"
                :name="index"
              >
                <div v-html="faq.answer"></div>
              </el-collapse-item>
            </el-collapse>
          </el-card>
        </el-col>

        <!-- 右侧边栏 -->
        <el-col :lg="8" :md="24">
          <!-- 快速测试 -->
          <el-card class="test-card">
            <template #header>
              <h3>快速测试</h3>
            </template>
            <div class="test-content">
              <div v-if="service.type === 'ocr'" class="ocr-test">
                <FileUploader
                  v-model="testFiles"
                  :max-count="1"
                  :max-size="10"
                  :allowed-types="['image']"
                  :auto-upload="false"
                  tip="上传图片进行OCR识别测试"
                />
                <el-button
                  type="primary"
                  :loading="testing"
                  :disabled="testFiles.length === 0"
                  @click="runOcrTest"
                  block
                >
                  开始识别
                </el-button>
              </div>
              <div v-else-if="service.type === 'nlp'" class="nlp-test">
                <el-input
                  v-model="testText"
                  type="textarea"
                  :rows="4"
                  placeholder="输入要分析的文本内容"
                  maxlength="1000"
                  show-word-limit
                />
                <el-button
                  type="primary"
                  :loading="testing"
                  :disabled="!testText.trim()"
                  @click="runNlpTest"
                  block
                >
                  开始分析
                </el-button>
              </div>
              <div v-else-if="service.type === 'ai_service'" class="ai-test">
                <el-input
                  v-model="testPrompt"
                  type="textarea"
                  :rows="3"
                  placeholder="输入生成提示词"
                  maxlength="500"
                  show-word-limit
                />
                <el-button
                  type="primary"
                  :loading="testing"
                  :disabled="!testPrompt.trim()"
                  @click="runAiTest"
                  block
                >
                  开始生成
                </el-button>
              </div>
              <div v-else class="general-test">
                <el-input
                  v-model="testText"
                  type="textarea"
                  :rows="4"
                  placeholder="输入测试数据"
                  maxlength="1000"
                  show-word-limit
                />
                <el-button
                  type="primary"
                  :loading="testing"
                  :disabled="!testText.trim()"
                  @click="runGeneralTest"
                  block
                >
                  开始测试
                </el-button>
              </div>
              
              <!-- 测试结果 -->
              <div v-if="testResult" class="test-result">
                <h4>测试结果</h4>
                <div class="result-content">
                  <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 定价信息 -->
          <el-card class="pricing-card">
            <template #header>
              <h3>定价信息</h3>
            </template>
            <div class="pricing-content">
              <div class="price-item">
                <div class="price-label">基础价格</div>
                <div class="price-value">¥{{ getServicePriceValue(service) }}/次</div>
              </div>
              <div v-if="service.pricingTiers" class="pricing-tiers">
                <h4>阶梯定价</h4>
                <div
                  v-for="tier in service.pricingTiers"
                  :key="tier.min"
                  class="tier-item"
                >
                  <span class="tier-range">
                    {{ tier.min }}{{ tier.max ? `-${tier.max}` : '+' }} 次
                  </span>
                  <span class="tier-price">¥{{ tier.price }}/次</span>
                </div>
              </div>
              <div class="pricing-note">
                <el-icon><InfoFilled /></el-icon>
                <span>首次使用可免费体验 {{ service.freeQuota || 100 }} 次</span>
              </div>
            </div>
          </el-card>

          <!-- 相关服务 -->
          <el-card class="related-services-card">
            <template #header>
              <h3>相关服务</h3>
            </template>
            <div class="related-services">
              <div
                v-for="relatedService in relatedServices"
                :key="relatedService.id"
                class="related-service-item"
                @click="goToService(relatedService.code)"
              >
                <div class="service-icon">
                  <el-icon :color="relatedService.color">
                    <component :is="getServiceIcon(relatedService.icon)" />
                  </el-icon>
                </div>
                <div class="service-info">
                  <div class="service-name">{{ relatedService.name }}</div>
                  <div class="service-price">¥{{ getServicePriceValue(relatedService) }}/次</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Document,
  Picture,
  ChatDotRound,
  MagicStick,
  Key,
  Timer,
  Check,
  Star,
  InfoFilled,
  DocumentCopy,
  View,
  Setting
} from '@element-plus/icons-vue'
import FileUploader from '@/components/upload/FileUploader.vue'
import { useServiceStore, type Service } from '@/stores/service'
import {
  getServiceTypeColor,
  getServiceStatusLabel,
  getServiceStatusType,
  getServicePrice,
  getFeaturesList
} from '@/utils/serviceUtils'

const route = useRoute()
const router = useRouter()
const serviceStore = useServiceStore()

// 响应式数据
const loading = ref(false)
const error = ref('')
const service = ref<Service | null>(null)
const relatedServices = ref<Service[]>([])
const activeLanguage = ref('curl')
const activeFaq = ref<number[]>([])
const testing = ref(false)
const testResult = ref<any>(null)
const testFiles = ref<File[]>([])
const testText = ref('')
const testPrompt = ref('')

// 计算属性
const serviceCode = computed(() => route.params.code as string)

// 使用步骤
const usageSteps = computed(() => {
  if (!service.value) return []
  
  const baseSteps = [
    {
      title: '获取API密钥',
      description: '在控制台中创建并获取您的API密钥'
    },
    {
      title: '配置请求头',
      description: '在请求中添加Authorization头，值为"Bearer your-api-key"'
    }
  ]
  
  if (service.value.category === 'ocr') {
    baseSteps.push(
      {
        title: '准备图片文件',
        description: '支持JPG、PNG、PDF等格式，文件大小不超过10MB'
      },
      {
        title: '发送POST请求',
        description: '将图片文件以multipart/form-data格式发送到API端点'
      },
      {
        title: '解析识别结果',
        description: '从响应中获取识别出的文字内容和位置信息'
      }
    )
  } else if (service.value.category === 'nlp') {
    baseSteps.push(
      {
        title: '准备文本内容',
        description: '准备需要分析的文本，支持中英文混合'
      },
      {
        title: '发送POST请求',
        description: '将文本内容以JSON格式发送到API端点'
      },
      {
        title: '解析分析结果',
        description: '从响应中获取分析结果，如情感倾向、关键词等'
      }
    )
  } else if (service.value.category === 'ai') {
    baseSteps.push(
      {
        title: '编写提示词',
        description: '编写清晰的提示词，描述您希望生成的内容'
      },
      {
        title: '发送POST请求',
        description: '将提示词和参数以JSON格式发送到API端点'
      },
      {
        title: '获取生成结果',
        description: '从响应中获取AI生成的内容'
      }
    )
  }
  
  return baseSteps
})

// 常见问题
const faqs = computed(() => {
  if (!service.value) return []
  
  const baseFaqs = [
    {
      question: '如何获取API密钥？',
      answer: '登录控制台，在"API密钥管理"页面中创建新的密钥。请妥善保管您的密钥，不要在客户端代码中暴露。'
    },
    {
      question: '有请求频率限制吗？',
      answer: '是的，不同套餐有不同的频率限制。免费用户每分钟最多100次请求，付费用户可根据套餐享受更高的频率限制。'
    },
    {
      question: '如何处理错误响应？',
      answer: 'API会返回标准的HTTP状态码。4xx表示客户端错误，5xx表示服务器错误。请根据错误码和错误信息进行相应处理。'
    }
  ]
  
  if (service.value.category === 'ocr') {
    baseFaqs.push(
      {
        question: '支持哪些图片格式？',
        answer: '支持JPG、JPEG、PNG、BMP、TIFF、PDF等常见格式。建议使用JPG或PNG格式以获得最佳识别效果。'
      },
      {
        question: '图片大小有限制吗？',
        answer: '单个文件大小不能超过10MB，图片分辨率建议在300-3000像素之间，过小或过大都可能影响识别准确率。'
      },
      {
        question: '识别准确率如何？',
        answer: `当前服务的识别准确率可达${service.value.accuracy || 95}%以上。清晰度越高的图片识别效果越好。`
      }
    )
  } else if (service.value.category === 'nlp') {
    baseFaqs.push(
      {
        question: '支持哪些语言？',
        answer: '主要支持中文和英文，对于其他语言的支持程度可能有所不同。'
      },
      {
        question: '文本长度有限制吗？',
        answer: '单次请求的文本长度建议不超过10000字符，过长的文本可能影响分析准确性。'
      }
    )
  } else if (service.value.category === 'ai') {
    baseFaqs.push(
      {
        question: '生成内容的质量如何？',
        answer: '我们使用先进的AI模型，生成内容质量较高。提示词越清晰具体，生成效果越好。'
      },
      {
        question: '生成速度如何？',
        answer: '一般情况下，短文本生成在3-10秒内完成，长文本可能需要更长时间。'
      }
    )
  }
  
  return baseFaqs
})







function getExampleTitle(language: string): string {
  const titleMap: Record<string, string> = {
    'curl': 'cURL 示例',
    'javascript': 'JavaScript 示例',
    'python': 'Python 示例',
    'java': 'Java 示例'
  }
  return titleMap[language] || language
}

function getCodeExample(language: string): string {
  if (!service.value) return ''
  
  const apiUrl = `https://api.example.com/v1/${service.value.code}`
  
  if (language === 'curl') {
    if (service.value.category === 'ocr') {
      return `curl -X POST "${apiUrl}" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: multipart/form-data" \\
  -F "file=@/path/to/image.jpg"`
    } else {
      return `curl -X POST "${apiUrl}" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "text": "要分析的文本内容"
  }'`
    }
  } else if (language === 'javascript') {
    if (service.value.category === 'ocr') {
      return `const formData = new FormData();
formData.append('file', fileInput.files[0]);

fetch('${apiUrl}', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: formData
})
.then(response => response.json())
.then(data => console.log(data));`
    } else {
      return `fetch('${apiUrl}', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    text: '要分析的文本内容'
  })
})
.then(response => response.json())
.then(data => console.log(data));`
    }
  } else if (language === 'python') {
    if (service.value.category === 'ocr') {
      return `import requests

url = '${apiUrl}'
headers = {
    'Authorization': 'Bearer YOUR_API_KEY'
}
files = {
    'file': open('/path/to/image.jpg', 'rb')
}

response = requests.post(url, headers=headers, files=files)
print(response.json())`
    } else {
      return `import requests
import json

url = '${apiUrl}'
headers = {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
}
data = {
    'text': '要分析的文本内容'
}

response = requests.post(url, headers=headers, json=data)
print(response.json())`
    }
  } else if (language === 'java') {
    return `// Java 示例代码
// 请使用 OkHttp 或其他 HTTP 客户端库

OkHttpClient client = new OkHttpClient();

RequestBody requestBody = new MultipartBody.Builder()
    .setType(MultipartBody.FORM)
    .addFormDataPart("file", "image.jpg", 
        RequestBody.create(MediaType.parse("image/jpeg"), file))
    .build();

Request request = new Request.Builder()
    .url("${apiUrl}")
    .addHeader("Authorization", "Bearer YOUR_API_KEY")
    .post(requestBody)
    .build();

Response response = client.newCall(request).execute();`
  }
  
  return ''
}

async function loadServiceDetail() {
  if (!serviceCode.value) {
    error.value = '服务ID不能为空'
    return
  }
  
  try {
    loading.value = true
    error.value = ''
    
    // 获取服务详情
    await serviceStore.fetchServiceDetail(serviceCode.value)
    service.value = serviceStore.currentService
    
    if (!service.value) {
      error.value = '服务不存在或已被删除'
      return
    }
    
    // 获取相关服务
    // await loadRelatedServices()
    
  } catch (error: any) {
    console.error('加载服务详情失败:', error)
    error.value = error.message || '加载服务详情失败'
  } finally {
    loading.value = false
  }
}

async function loadRelatedServices() {
  if (!service.value) return
  
  try {
    // 获取同类别的其他服务
    await serviceStore.fetchServices({
      category: service.value.category,
      limit: 5
    })
    
    relatedServices.value = serviceStore.services
      .filter(s => s.id !== service.value?.id)
      .slice(0, 3)
  } catch (err) {
    console.error('加载相关服务失败:', err)
  }
}

function tryService() {
  if (!service.value) return
  
  // 跳转到API测试页面，并预设当前服务
  router.push({
    name: 'playground',
    query: {
      service: service.value.code
    }
  })
}

function viewDocs() {
  if (!service.value) return
  
  // 跳转到文档页面
  router.push({
    name: 'docs',
    query: {
      service: service.value.code
    }
  })
}

function viewPricing() {
  // 跳转到定价页面
  router.push({ name: 'pricing' })
}

function goToService(serviceCode?: string | number) {
  if (!serviceCode) return
  router.push({
    name: 'service-detail',
    params: { code: String(serviceCode) }
  })
}

function copyCode() {
  const code = getCodeExample(activeLanguage.value)
  navigator.clipboard.writeText(code).then(() => {
    ElMessage.success('代码已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

async function runOcrTest() {
  if (testFiles.value.length === 0) return
  
  try {
    testing.value = true
    testResult.value = null
    
    // 模拟OCR识别
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    testResult.value = {
      success: true,
      data: {
        text: '这是识别出的文字内容示例',
        confidence: 0.95,
        regions: [
          {
            text: '这是识别出的文字内容示例',
            bbox: [100, 100, 300, 150],
            confidence: 0.95
          }
        ]
      }
    }
    
    ElMessage.success('OCR识别完成')
  } catch (error) {
    console.error('OCR识别失败:', error)
    ElMessage.error('OCR识别失败')
  } finally {
    testing.value = false
  }
}

async function runNlpTest() {
  if (!testText.value.trim()) return
  
  try {
    testing.value = true
    testResult.value = null
    
    // 模拟NLP分析
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    testResult.value = {
      success: true,
      data: {
        sentiment: {
          label: 'positive',
          confidence: 0.85
        },
        keywords: ['示例', '文本', '分析'],
        entities: [
          {
            text: '示例',
            type: 'NOUN',
            start: 0,
            end: 2
          }
        ]
      }
    }
    
    ElMessage.success('文本分析完成')
  } catch (error) {
    console.error('文本分析失败:', error)
    ElMessage.error('文本分析失败')
  } finally {
    testing.value = false
  }
}

async function runAiTest() {
  if (!testPrompt.value.trim()) return
  
  try {
    testing.value = true
    testResult.value = null
    
    // 模拟AI生成
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    testResult.value = {
      success: true,
      data: {
        generated_text: '这是根据您的提示词生成的示例内容。AI会根据输入的提示词生成相关的文本内容，质量取决于提示词的清晰度和具体性。',
        tokens_used: 45,
        finish_reason: 'stop'
      }
    }
    
    ElMessage.success('AI生成完成')
  } catch (error) {
    console.error('AI生成失败:', error)
    ElMessage.error('AI生成失败')
  } finally {
    testing.value = false
  }
}

// 获取服务价格
const getServicePriceValue = (service: Service) => {
  return getServicePrice(service)
}

// 获取服务状态文本
const getStatusText = (status: string) => {
  if (!status) return '未知状态'
  return getServiceStatusLabel(status as any)
}

// 获取服务状态标签类型
const getStatusTagType = (status: string) => {
  if (!status) return 'info'
  return getServiceStatusType(status as any)
}

// 获取服务图标
const getServiceIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    'ai_service': MagicStick,
    'ocr': Picture,
    'nlp': ChatDotRound,
    'cv': Picture,
    'geo': MagicStick,
    'data': Document,
    'other': Document
  }
  return iconMap[type] || Document
}

// 获取特性图标
const getFeatureIcon = () => {
  return Check
}

// 将特性字符串转换为数组
const getFeaturesListValue = (features: string | string[]) => {
  return getFeaturesList(features)
}

// 生命周期
onMounted(() => {
  loadServiceDetail()
})
</script>

<style scoped>
.service-detail-view {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.breadcrumb {
  margin-bottom: 24px;
}

.loading-container,
.error-container {
  margin: 48px 0;
}

.service-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  margin-bottom: 24px;
}

.service-info {
  display: flex;
  gap: 24px;
  flex: 1;
}

.service-icon {
  flex-shrink: 0;
}

.service-meta {
  flex: 1;
}

.service-title {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.service-title h1 {
  margin: 0;
  font-size: 32px;
  font-weight: 600;
}

.service-description {
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 16px 0;
  opacity: 0.9;
}

.service-stats {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
}

.service-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex-shrink: 0;
}

.detail-content {
  margin-top: 24px;
}

.feature-card,
.api-example-card,
.usage-guide-card,
.faq-card,
.test-card,
.pricing-card,
.related-services-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.feature-item {
  display: flex;
  gap: 16px;
  padding: 20px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  transition: all 0.3s;
}

.feature-item:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  flex-shrink: 0;
  font-size: 24px;
}

.feature-content h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.feature-content p {
  margin: 0;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.api-example {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  overflow: hidden;
}

.example-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--el-fill-color-lighter);
  border-bottom: 1px solid var(--el-border-color);
}

.example-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.code-block {
  margin: 0;
  padding: 16px;
  background-color: #f8f9fa;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  overflow-x: auto;
}

.usage-guide {
  padding: 16px 0;
}

.test-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.test-result {
  margin-top: 16px;
  padding: 16px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 6px;
}

.test-result h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}

.result-content {
  max-height: 200px;
  overflow-y: auto;
}

.result-content pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

.pricing-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 6px;
}

.price-label {
  font-weight: 500;
}

.price-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-color-primary);
}

.pricing-tiers h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}

.tier-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.tier-item:last-child {
  border-bottom: none;
}

.tier-range {
  color: var(--el-text-color-regular);
}

.tier-price {
  font-weight: 500;
  color: var(--el-color-primary);
}

.pricing-note {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: var(--el-color-info-light-9);
  border-radius: 6px;
  font-size: 14px;
  color: var(--el-color-info);
}

.related-services {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.related-service-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.related-service-item:hover {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.related-service-item .service-icon {
  flex-shrink: 0;
}

.related-service-item .service-info {
  flex: 1;
}

.service-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.service-price {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .service-detail-view {
    padding: 16px;
  }
  
  .service-header {
    flex-direction: column;
    gap: 24px;
    padding: 24px;
  }
  
  .service-info {
    flex-direction: column;
    gap: 16px;
  }
  
  .service-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .service-title h1 {
    font-size: 24px;
  }
  
  .service-stats {
    gap: 16px;
  }
  
  .service-actions {
    width: 100%;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .feature-item {
    padding: 16px;
  }
}

/* 滚动条样式 */
.result-content::-webkit-scrollbar {
  width: 6px;
}

.result-content::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
}

.result-content::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
}

.result-content::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-darker);
}
</style>