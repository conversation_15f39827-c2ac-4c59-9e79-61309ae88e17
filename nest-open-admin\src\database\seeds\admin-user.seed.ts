import { DataSource } from 'typeorm';
import { UserEntity, RoleEnum, UserStatus, UserType, UserVerificationStatus, TierEnum } from '../../modules/user/entities/user.entity';
import * as bcrypt from 'bcrypt';

export class AdminUserSeed {
  async run(dataSource: DataSource): Promise<void> {
    const userRepository = dataSource.getRepository(UserEntity);

    // 检查是否已存在管理员用户
    const existingAdmin = await userRepository.findOne({
      where: { username: 'admin' }
    });

    if (existingAdmin) {
      console.log('管理员用户已存在，跳过创建');
    } else {
      // 创建默认管理员用户
      const adminUser = new UserEntity();
      adminUser.username = 'admin';
      adminUser.email = '<EMAIL>';
      adminUser.nickname = '系统管理员';
      adminUser.realName = '系统管理员';
      adminUser.password = 'admin123456'; // 默认密码
      adminUser.role = RoleEnum.ADMIN;
      adminUser.userStatus = UserStatus.ACTIVE;
      adminUser.userType = UserType.ORGANIZATION;
      adminUser.verificationStatus = UserVerificationStatus.ENTERPRISE_VERIFIED;
      adminUser.tier = TierEnum.ENTERPRISE;
      adminUser.emailVerified = true;
      adminUser.phoneVerified = false;
      adminUser.bio = '系统默认管理员账户';
      adminUser.gender = 'unknown';
      adminUser.loginFailCount = 0;
      adminUser.remainingUsage = 999999999; // 管理员无限调用次数
      adminUser.totalUsage = 0;
      adminUser.dailyFreeUsageRemaining = 999999; // 管理员无限每日免费调用
      adminUser.balance = 10000.00; // 管理员有足够的余额
      adminUser.preferences = JSON.stringify({
        language: 'zh-CN',
        theme: 'light',
        timezone: 'Asia/Shanghai'
      });
      adminUser.settings = JSON.stringify({
        notifications: {
          email: true,
          sms: false,
          push: true
        },
        security: {
          twoFactorAuth: false,
          loginNotification: true
        }
      });

      await userRepository.save(adminUser);
      console.log('✅ 默认管理员用户创建成功');
      console.log('   用户名: admin');
      console.log('   密码: admin123456');
      console.log('   邮箱: <EMAIL>');
      console.log('⚠️  请在生产环境中修改默认密码!');
    }

    // 检查是否已存在测试用户
    const existingTestUser = await userRepository.findOne({
      where: { username: 'testuser' }
    });

    if (existingTestUser) {
      console.log('测试用户已存在，跳过创建');
    } else {
      // 创建测试用户
      const testUser = new UserEntity();
      testUser.username = 'testuser';
      testUser.email = '<EMAIL>';
      testUser.nickname = '测试用户';
      testUser.realName = '张测试';
      testUser.password = 'test123456';
      testUser.role = RoleEnum.USER;
      testUser.userStatus = UserStatus.ACTIVE;
      testUser.userType = UserType.INDIVIDUAL;
      testUser.verificationStatus = UserVerificationStatus.PERSONAL_VERIFIED;
      testUser.tier = TierEnum.BASIC;
      testUser.emailVerified = true;
      testUser.phoneVerified = true;
      testUser.phone = '13800138000';
      testUser.bio = '测试账户，用于演示和测试';
      testUser.gender = 'male';
      testUser.loginFailCount = 0;
      testUser.remainingUsage = 1000;
      testUser.totalUsage = 0;
      testUser.dailyFreeUsageRemaining = 100;
      testUser.balance = 100.00;
      testUser.preferences = JSON.stringify({
        language: 'zh-CN',
        theme: 'light',
        timezone: 'Asia/Shanghai'
      });
      testUser.settings = JSON.stringify({
        notifications: {
          email: true,
          sms: true,
          push: true
        },
        security: {
          twoFactorAuth: false,
          loginNotification: true
        }
      });

      await userRepository.save(testUser);
      console.log('✅ 测试用户创建成功');
      console.log('   用户名: testuser');
      console.log('   密码: test123456');
      console.log('   邮箱: <EMAIL>');
    }
  }
}