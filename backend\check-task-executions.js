// 检查任务执行记录
const mysql = require('mysql2/promise');

async function checkTaskExecutions() {
  const taskId = '05d6de1d-c760-4d1a-8345-b47b327e6e89';
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '123456',
    database: 'openapidb'
  });
  
  try {
    console.log('=== 检查task_executions表结构 ===');
    const [columns] = await connection.execute('DESCRIBE task_executions');
    console.log('表字段:');
    columns.forEach(col => {
      console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });
    
    console.log('\n=== 查找任务执行记录 ===');
    const [executions] = await connection.execute(
      'SELECT * FROM task_executions WHERE task_id = ? OR task_id LIKE ?',
      [taskId, `%${taskId}%`]
    );
    
    console.log(`任务 ${taskId} 的执行记录: ${executions.length}`);
    executions.forEach((record, index) => {
      console.log(`  ${index + 1}. ID: ${record.id}, TaskID: ${record.task_id}, Status: ${record.status}, Created: ${record.created_at}`);
    });
    
    console.log('\n=== 最近的任务执行记录 ===');
    const [recentExecutions] = await connection.execute(
      'SELECT * FROM task_executions ORDER BY created_at DESC LIMIT 5'
    );
    
    console.log(`最近的任务执行记录: ${recentExecutions.length}`);
    recentExecutions.forEach((record, index) => {
      console.log(`  ${index + 1}. TaskID: ${record.task_id}, Status: ${record.status}, Type: ${record.task_type}, Created: ${record.created_at}`);
    });
    
    console.log('\n=== 检查call_records表 ===');
    const [callRecords] = await connection.execute(
      'SELECT * FROM call_records ORDER BY created_at DESC LIMIT 5'
    );
    
    console.log(`最近的调用记录: ${callRecords.length}`);
    callRecords.forEach((record, index) => {
      console.log(`  ${index + 1}. RequestID: ${record.request_id}, Service: ${record.service_id}, Status: ${record.status}, Created: ${record.created_at}`);
    });
    
  } catch (error) {
    console.error('数据库操作失败:', error.message);
  } finally {
    await connection.end();
  }
}

checkTaskExecutions().catch(console.error);
