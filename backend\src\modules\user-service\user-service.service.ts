import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
  Logger,
  Inject,
  forwardRef,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, Between, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, QueryRunner } from 'typeorm';
import { UserServiceEntity } from './entities/user-service.entity';
import { UserService } from '../user/user.service';
import { ServiceService } from '../service/service.service';
import { BaseCrudService } from '../../common/base/base-crud.service';
import {
  CreateUserServiceDto,
  UpdateUserServiceDto,
  QueryUserServiceDto,
  BatchAssignServiceDto,
  BatchUpdateCountDto,
  ResetCountDto,
  UserServiceResponseDto,
  UserServiceListResponseDto,
  UserServiceStatsDto,
  UserServiceUsageDto,
  UpdateTotalCountDto,
} from './dto/user-service.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class UserServiceService extends BaseCrudService<
  UserServiceEntity,
  CreateUserServiceDto,
  UpdateUserServiceDto,
  QueryUserServiceDto,
  UserServiceResponseDto,
  UserServiceListResponseDto
> {
  protected readonly repository: Repository<UserServiceEntity>;
  protected readonly entityName = 'UserService';
  protected readonly logger = new Logger(UserServiceService.name);

  constructor(
    @InjectRepository(UserServiceEntity)
    userServiceRepository: Repository<UserServiceEntity>,
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    private readonly dataSource: DataSource,
    private readonly serviceService: ServiceService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    super();
    this.repository = userServiceRepository;
  }

  /**
   * 验证创建前的业务逻辑
   */
  protected async validateBeforeCreate(createDto: CreateUserServiceDto): Promise<void> {
    const { userId, serviceId } = createDto;

    // 验证用户是否存在
    await this.userService.findById(userId);

    // 验证服务是否存在
    const service = await this.serviceService.findOne(serviceId);
    if (!service) {
      throw new NotFoundException('服务不存在');
    }

    // 检查是否已存在关联
    const existingRelation = await this.repository.findOne({
      where: { user: { id: userId }, service: { id: serviceId } },
    });

    if (existingRelation) {
      throw new ConflictException('用户服务关联已存在');
    }
  }

  /**
   * 转换创建DTO为实体
   */
  protected transformCreateDto(createDto: CreateUserServiceDto): Partial<UserServiceEntity> {
    const { userId, serviceId, freeCount = 0, purchasedCount = 0, callCount = 0 } = createDto;
    const totalCount = freeCount + purchasedCount;
    const remainingCount = totalCount - callCount;

    return {
      userId,
      serviceId,
      totalCount,
      usedCount: callCount,
      remainingCount,
      freeCount,
      purchasedCount,
      freeUsedToday: 0,
      lastResetDate: new Date(),
      alertSent: false,
      enabled: true,
      notes: createDto.notes
    };
  }

  /**
   * 转换实体为响应DTO
   */
  protected transformToResponseDto(entity: UserServiceEntity): UserServiceResponseDto {
    return this.formatUserServiceResponse(entity, {
      freeCount: 0,
      purchasedCount: entity.totalCount,
      usedCount: entity.usedCount,
      remainingCount: entity.totalCount - entity.usedCount,
      enabled: true,
    });
  }

  /**
   * 更新前验证
   */
  protected async validateBeforeUpdate(
    id: number,
    updateDto: UpdateUserServiceDto,
    entity: UserServiceEntity,
  ): Promise<void> {
    // 可以添加更新前的验证逻辑
    // 例如：检查是否有权限更新、验证业务规则等
  }

  /**
   * 删除前验证
   */
  protected async validateBeforeDelete(id: number, entity: UserServiceEntity): Promise<void> {
    // 可以添加删除前的验证逻辑
    // 例如：检查是否有关联数据、是否有权限删除等
  }

  /**
   * 转换更新DTO
   */
  protected transformUpdateDto(updateDto: UpdateUserServiceDto, entity: UserServiceEntity): Partial<UserServiceEntity> {
    return {
      ...updateDto,
      updatedAt: new Date(),
    };
  }

  /**
   * 应用查询条件
   */
  protected applyQueryConditions(
    queryBuilder: any,
    queryDto: QueryUserServiceDto,
  ): void {
    const { userId, serviceId, enabled } = queryDto;

    if (userId) {
      queryBuilder.andWhere('entity.user.id = :userId', { userId });
    }

    if (serviceId) {
      queryBuilder.andWhere('entity.service.id = :serviceId', { serviceId });
    }

    if (enabled !== undefined) {
      queryBuilder.andWhere('entity.enabled = :enabled', { enabled });
    }


  }

  /**
   * 转换实体列表为响应DTO
   */
  protected transformToListResponseDto(
    data: UserServiceResponseDto[],
    total: number,
    page: number,
    limit: number,
    totalPages: number,
  ): UserServiceListResponseDto {
    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * 查询用户服务关联列表
   */
  async findAll(queryDto: QueryUserServiceDto = {}): Promise<UserServiceListResponseDto> {
    const {
      page = 1,
      limit = 10,
      userId,
      serviceId,
      enabled,
      expired,
      minRemainingCount,
      maxRemainingCount,
      minCallCount,
      maxCallCount,
      serviceType,
      serviceCode,
      username,
      userEmail,
      startDate,
      endDate,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = queryDto;

    const queryBuilder = this.repository
      .createQueryBuilder('userService')
      .leftJoinAndSelect('userService.user', 'user')
      .leftJoinAndSelect('userService.service', 'service');

    // 添加查询条件
    if (userId) {
      queryBuilder.andWhere('user.id = :userId', { userId });
    }
    if (serviceId) {
      queryBuilder.andWhere('service.id = :serviceId', { serviceId });
    }
    if (serviceType) {
      queryBuilder.andWhere('service.type = :serviceType', { serviceType });
    }
    if (serviceCode) {
      queryBuilder.andWhere('service.code LIKE :serviceCode', { serviceCode: `%${serviceCode}%` });
    }
    if (username) {
      queryBuilder.andWhere('user.username LIKE :username', { username: `%${username}%` });
    }
    if (userEmail) {
      queryBuilder.andWhere('user.email LIKE :userEmail', { userEmail: `%${userEmail}%` });
    }
    if (startDate) {
      queryBuilder.andWhere('userService.createdAt >= :startDate', { startDate });
    }
    if (endDate) {
      queryBuilder.andWhere('userService.createdAt <= :endDate', { endDate });
    }
    if (minCallCount !== undefined) {
      queryBuilder.andWhere('userService.usedCount >= :minCallCount', { minCallCount });
    }
    if (maxCallCount !== undefined) {
      queryBuilder.andWhere('userService.usedCount <= :maxCallCount', { maxCallCount });
    }

    // 排序
    queryBuilder.orderBy(`userService.${sortBy}`, sortOrder);

    // 分页
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    const [userServices, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    const data = userServices.map(userService => this.formatUserServiceResponse(userService));

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * 根据ID查询用户服务关联
   */
  async findOne(id: number): Promise<UserServiceResponseDto> {
    const userService = await this.repository.findOne({
      where: { id },
      relations: ['user', 'service'],
    });

    if (!userService) {
      throw new NotFoundException('用户服务关联不存在');
    }

    return this.formatUserServiceResponse(userService);
  }

  /**
   * 根据用户ID和服务ID查询关联
   */
  async findByUserAndService(userId: number, serviceId: number): Promise<UserServiceResponseDto> {
    const userService = await this.repository.findOne({
      where: { userId, serviceId },
      relations: ['user', 'service'],
    });

    if (!userService) {
      throw new NotFoundException(`用户 ${userId} 的服务 ${serviceId} 不存在`);
    }

    return this.formatUserServiceResponse(userService);
  }

  /**
   * 根据用户ID查找所有用户服务关联
   */
  async findByUserId(userId: number): Promise<UserServiceResponseDto[]> {
    const userServices = await this.repository.find({
      where: { userId },
      relations: ['user', 'service'],
    });

    return userServices.map(userService => this.formatUserServiceResponse(userService));
  }

  /**
   * 更新用户服务关联
   */
  async update(id: number, updateUserServiceDto: UpdateUserServiceDto): Promise<UserServiceResponseDto> {
    const userService = await this.repository.findOne({
      where: { id },
      relations: ['user', 'service'],
    });

    if (!userService) {
      throw new NotFoundException('用户服务关联不存在');
    }

    // 更新次数相关字段
    if (updateUserServiceDto.freeCount !== undefined || updateUserServiceDto.purchasedCount !== undefined) {
      const freeCount = updateUserServiceDto.freeCount ?? 0;
      const purchasedCount = updateUserServiceDto.purchasedCount ?? 0;
      const totalCount = freeCount + purchasedCount;
      
      userService.totalCount = totalCount;
    }

    const savedUserService = await this.repository.save(userService);
    
    this.logger.log(`更新用户服务关联成功: ID=${id}`);
    
    // 转换类型以匹配formatUserServiceResponse的参数
    const additionalData: Partial<UserServiceResponseDto> = {
      ...updateUserServiceDto,
      expiresAt: updateUserServiceDto.expiresAt ? new Date(updateUserServiceDto.expiresAt) : undefined,
    };
    
    return this.formatUserServiceResponse(savedUserService, additionalData);
  }

  /**
   * 删除用户服务关联
   */
  async remove(id: number): Promise<{ message: string }> {
    const userService = await this.repository.findOne({
      where: { id },
    });

    if (!userService) {
      throw new NotFoundException('用户服务关联不存在');
    }

    // 检查是否有调用记录
    if (userService.usedCount > 0) {
      throw new BadRequestException('该关联存在调用记录，无法删除');
    }

    await this.repository.softDelete(id);
    
    this.logger.log(`删除用户服务关联成功: ID=${id}`);
    
    return { message: '用户服务关联已成功删除' };
  }

  /**
   * 批量分配服务
   */
  async batchAssignService(batchAssignDto: BatchAssignServiceDto): Promise<UserServiceResponseDto[]> {
    const { userIds, serviceId, freeCount = 0, purchasedCount = 0, expiresAt, config, notes } = batchAssignDto;
    
    // 验证服务是否存在
    const service = await this.serviceService.findOne(serviceId);
    if (!service) {
      this.logger.error(`服务不存在: ID=${serviceId}`);
      throw new NotFoundException('服务不存在');
    }
    
    this.logger.log(`开始为用户 ${userIds.join(',')} 分配服务 ${service.name} (ID=${serviceId})`);
    const results: UserServiceResponseDto[] = [];
    
    for (const userId of userIds) {
      try {
        // 验证用户是否存在
        await this.userService.findById(userId);

        // 检查是否已存在关联
        const existingRelation = await this.repository.findOne({
          where: { user: { id: userId }, service: { id: serviceId } },
        });

        if (existingRelation) {
          this.logger.warn(`用户 ${userId} 已经有服务 ${service.name} 的访问权限，跳过分配`);
          continue;
        }

        const createDto: CreateUserServiceDto = {
          userId,
          serviceId,
          freeCount,
          purchasedCount,
          expiresAt,
          config,
          notes,
        };

        const result = await this.create(createDto);
        this.logger.log(`成功为用户 ${userId} 分配服务 ${service.name}，总次数: ${freeCount + purchasedCount}`);
        results.push(result);
      } catch (error) {
        this.logger.error(`为用户 ${userId} 分配服务 ${service.name} 失败: ${error.message}`);
      }
    }

    this.logger.log(`批量分配服务完成: 成功=${results.length}, 总数=${userIds.length}, 服务=${service.name}`);
    
    return results;
  }

  /**
   * 批量更新次数
   */
  async batchUpdateCount(batchUpdateDto: BatchUpdateCountDto): Promise<void> {
    const { ids, operation, countType, amount, reason } = batchUpdateDto;

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      for (const id of ids) {
        const userService = await queryRunner.manager.findOne(UserServiceEntity, {
          where: { id },
        });

        if (!userService) {
          this.logger.warn(`批量更新次数时关联不存在: ID=${id}`);
          continue;
        }

        // 根据操作类型更新次数
        if (countType === 'free') {
          // 这里需要根据实际业务逻辑实现免费次数的更新
          // 由于实体中没有直接的免费次数字段，需要通过其他方式处理
        } else if (countType === 'purchased') {
          // 同样需要根据实际业务逻辑实现购买次数的更新
        }

        if (operation === 'add') {
          userService.totalCount += amount;
        } else if (operation === 'subtract') {
          userService.totalCount = Math.max(0, userService.totalCount - amount);
        } else if (operation === 'set') {
          userService.totalCount = amount;
        }

        await queryRunner.manager.save(userService);
      }

      await queryRunner.commitTransaction();
      
      this.logger.log(`批量更新次数完成: 操作=${operation}, 类型=${countType}, 数量=${amount}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`批量更新次数失败: ${error.message}`);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 重置次数
   */
  async resetCount(resetDto: ResetCountDto): Promise<UserServiceResponseDto> {
    const { userId, serviceId, resetType = 'call_count', reason } = resetDto;

    const userService = await this.repository.findOne({
      where: { user: { id: userId }, service: { id: serviceId } },
      relations: ['user', 'service'],
    });

    if (!userService) {
      throw new NotFoundException('用户服务关联不存在');
    }

    if (resetType === 'call_count') {
      userService.usedCount = 0;
      userService.freeUsedToday = 0;
    } else if (resetType === 'all') {
      userService.usedCount = 0;
      userService.freeUsedToday = 0;
      userService.totalCount = 0;
      userService.lastResetDate = new Date();
      userService.alertSent = false;
    }

    const savedUserService = await this.repository.save(userService);
    
    this.logger.log(`重置次数成功: 用户ID=${userId}, 服务ID=${serviceId}, 类型=${resetType}`);
    
    return this.formatUserServiceResponse(savedUserService);
  }

  /**
   * 获取统计信息
   */
  async getStats(): Promise<UserServiceStatsDto> {
    const [totalAssociations, activeAssociations] = await Promise.all([
      this.repository.count(),
      this.repository.count({
        where: {
          // enabled: true, // 需要根据实际字段调整
        },
      }),
    ]);

    const callStats = await this.repository
      .createQueryBuilder('userService')
      .select('SUM(userService.usedCount)', 'totalCalls')
      .addSelect('SUM(userService.totalCount)', 'totalCount')
      .addSelect('AVG(userService.usedCount * 100.0 / NULLIF(userService.totalCount, 0))', 'avgUsageRate')
      .getRawOne();

    return {
      totalAssociations,
      activeAssociations,
      expiredAssociations: 0, // 需要根据实际业务逻辑计算
      disabledAssociations: 0, // 需要根据实际业务逻辑计算
      totalCalls: parseInt(callStats.totalCalls) || 0,
      totalFreeCount: 0, // 需要根据实际业务逻辑计算
      totalPurchasedCount: 0, // 需要根据实际业务逻辑计算
      totalRemainingCount: (parseInt(callStats.totalCount) || 0) - (parseInt(callStats.totalCalls) || 0),
      avgUsageRate: parseFloat(callStats.avgUsageRate) || 0,
      byService: {}, // 需要根据实际需求实现
      byUserType: {}, // 需要根据实际需求实现
      byUserTier: {}, // 需要根据实际需求实现
      expiringSoon: 0, // 需要根据实际业务逻辑计算
      lowCount: 0, // 需要根据实际业务逻辑计算
    };
  }

  /**
   * 格式化用户服务响应数据
   */
  private formatUserServiceResponse(
    userService: UserServiceEntity,
    additionalData?: Partial<UserServiceResponseDto>
  ): UserServiceResponseDto {
    const remainingCount = userService.totalCount - userService.usedCount;
    const usageRate = userService.totalCount > 0 ? (userService.usedCount / userService.totalCount) * 100 : 0;

    return {
      id: userService.id,
      userId: userService.user?.id || 0,
      serviceId: userService.service?.id || 0,
      usedCount: userService.usedCount,
      freeCount: additionalData?.freeCount || 0,
      purchasedCount: additionalData?.purchasedCount || 0,
      totalCount: userService.totalCount,
      alertSent: userService.alertSent,
      remainingCount,
      enabled: additionalData?.enabled ?? true,
      expiresAt: additionalData?.expiresAt,
      config: additionalData?.config,
      notes: additionalData?.notes,
      createdAt: userService.createdAt,
      updatedAt: userService.updatedAt,
      user: userService.user ? {
        id: userService.user.id,
        username: userService.user.username,
        email: userService.user.email,
        realName: userService.user.realName,
        phone: userService.user.phone,
        type: userService.user.userType,
        status: userService.user.userStatus,
        tier: userService.user.tier,
      } : undefined,
      service: userService.service ? {
        id: userService.service.id,
        code: userService.service.code,
        name: userService.service.name,
        type: userService.service.type,
        status: userService.service.serviceStatus,
        description: userService.service.description,
        pricingModel: userService.service.pricingModel,
        unitPrice: userService.service.price,
        endpoint: userService.service.endpoint,
      } : undefined,
      usageRate,
      isExpired: additionalData?.expiresAt ? new Date() > additionalData.expiresAt : false,
      remainingDays: additionalData?.expiresAt ? Math.max(0, Math.ceil((additionalData.expiresAt.getTime() - Date.now()) / (1000 * 60 * 60 * 24))) : undefined,
    };
  }

  /**
   * 更新用户服务的使用次数 - 使用悲观锁确保原子性
   * @param userId 用户ID
   * @param serviceId 服务ID
   * @param increment 增量，默认为1
   * @param queryRunner 可选的查询运行器，用于事务
   */
  async updateUsedCount(
    userId: number,
    serviceId: number,
    increment = 1,
    queryRunner?: QueryRunner
  ): Promise<void> {
    // 确定是否需要创建和管理事务
    const shouldManageTransaction = !queryRunner;
    queryRunner = queryRunner || this.dataSource.createQueryRunner();
    
    if (shouldManageTransaction) {
      await queryRunner.connect();
      await queryRunner.startTransaction();
    }
    
    try {
      // 使用悲观锁获取用户服务记录
      const userService = await queryRunner.manager.findOne(UserServiceEntity, {
        where: { userId, serviceId },
        lock: { mode: 'pessimistic_write' }
      });
      
      if (!userService) {
        throw new NotFoundException(`用户 ${userId} 的服务 ${serviceId} 不存在`);
      }
      
      // 检查是否启用
      if (!userService.enabled) {
        throw new ForbiddenException('该服务已被禁用');
      }
      
      // 检查剩余次数
      if (userService.remainingCount < increment) {
        throw new BadRequestException('调用次数不足');
      }
      
      // 更新使用次数
      userService.usedCount += increment;
      userService.remainingCount = userService.totalCount - userService.usedCount;
      userService.lastUsedAt = new Date();
      
      // 检查是否需要发送预警 (剩余次数不足10%)
      if (
        !userService.alertSent && 
        userService.remainingCount > 0 &&
        userService.remainingCount / userService.totalCount <= 0.1
      ) {
        userService.alertSent = true;
        
        // 在事务之外发送预警通知，避免事务时间过长
        setImmediate(() => {
          this.eventEmitter.emit('user.service.warning', {
            userId,
            serviceId,
            remainingCount: userService.remainingCount
          });
        });
      }
      
      // 保存更新
      await queryRunner.manager.save(userService);
      
      // 如果我们负责事务，提交它
      if (shouldManageTransaction) {
        await queryRunner.commitTransaction();
      }
    } catch (error) {
      // 如果我们负责事务且发生错误，回滚
      if (shouldManageTransaction && queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      
      this.logger.error(
        `更新用户 ${userId} 服务 ${serviceId} 使用次数失败: ${error.message}`,
        error.stack
      );
      
      throw error;
    } finally {
      // 如果我们负责事务，释放资源
      if (shouldManageTransaction) {
        await queryRunner.release();
      }
    }
  }

  /**
   * 更新用户服务的预警发送状态
   */
  async updateAlertSent(userId: number, serviceId: number, alertSent: boolean): Promise<void> {
    const userService = await this.repository.findOne({
      where: { userId, serviceId },
    });

    if (!userService) {
      throw new NotFoundException(`用户 ${userId} 的服务 ${serviceId} 不存在`);
    }

    userService.alertSent = alertSent;
    await this.repository.save(userService);
    
    this.logger.log(`更新预警状态成功: 用户ID=${userId}, 服务ID=${serviceId}, 预警状态=${alertSent}`);
  }

  /**
   * 为用户的所有服务增加免费使用次数
   * @param userId 用户ID
   * @param freeCount 增加的免费次数
   * @param reason 增加原因
   */
  async addFreeCountForAllServices(
    userId: number,
    freeCount: number,
    reason: string = '系统奖励',
  ): Promise<void> {
    try {
      // 查找用户的所有服务关联
      const userServices = await this.repository.find({
        where: { user: { id: userId } },
        relations: ['service'],
      });

      if (userServices.length === 0) {
        this.logger.warn(`用户 ${userId} 没有关联任何服务，跳过免费次数发放`);
        return;
      }

      // 批量更新所有服务的总次数
      const updatePromises = userServices.map(async (userService) => {
        userService.totalCount += freeCount;
        return this.repository.save(userService);
      });

      await Promise.all(updatePromises);

      this.logger.log(
        `为用户 ${userId} 的 ${userServices.length} 个服务增加免费次数成功: 每个服务+${freeCount}次, 原因: ${reason}`,
      );
    } catch (error) {
      this.logger.error(
        `为用户 ${userId} 增加免费次数失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 为指定用户和服务增加免费使用次数
   * @param userId 用户ID
   * @param serviceId 服务ID
   * @param freeCount 增加的免费次数
   * @param reason 增加原因
   */
  async addFreeCountForService(
    userId: number,
    serviceId: number,
    freeCount: number,
    reason: string = '系统奖励',
  ): Promise<void> {
    try {
      const userService = await this.repository.findOne({
        where: { user: { id: userId }, service: { id: serviceId } },
        relations: ['service'],
      });

      if (!userService) {
        throw new NotFoundException(
          `用户 ${userId} 与服务 ${serviceId} 的关联不存在`,
        );
      }

      userService.totalCount += freeCount;
      await this.repository.save(userService);

      this.logger.log(
        `为用户 ${userId} 的服务 ${serviceId} 增加免费次数成功: +${freeCount}次, 原因: ${reason}`,
      );
    } catch (error) {
      this.logger.error(
        `为用户 ${userId} 服务 ${serviceId} 增加免费次数失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 增加用户服务的使用次数（通过用户服务ID）
   * @param userServiceId 用户服务关联ID
   * @param increment 增加的次数
   * @param queryRunner 可选的查询运行器，用于事务支持
   */
  async incrementUsedCount(
    userServiceId: number,
    increment: number = 1,
    queryRunner?: QueryRunner
  ): Promise<void> {
    const manager = queryRunner?.manager || this.repository.manager;
    
    const userService = await manager.findOne(UserServiceEntity, {
      where: { id: userServiceId },
      lock: queryRunner ? { mode: 'pessimistic_write' } : undefined
    });

    if (!userService) {
      throw new NotFoundException('用户服务关联不存在');
    }

    userService.usedCount += increment;
    await manager.save(userService);
    
    this.logger.log(`增加使用次数成功: 用户服务ID=${userServiceId}, 增量=${increment}`);
  }

  /**
   * 添加服务调用次数
   * @param userId 用户ID
   * @param serviceId 服务ID
   * @param purchaseCount 购买的次数
   * @param source 来源说明
   * @returns 更新后的用户服务实体
   */
  async addServiceCount(
    userId: number,
    serviceId: number,
    purchaseCount: number,
    source: string = '购买'
  ): Promise<UserServiceEntity> {
    if (purchaseCount <= 0) {
      throw new BadRequestException('购买次数必须大于零');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      // 使用悲观锁获取用户服务记录
      let userService = await queryRunner.manager.findOne(UserServiceEntity, {
        where: { userId, serviceId },
        lock: { mode: 'pessimistic_write' }
      });
      
      // 如果记录不存在，创建新记录
      if (!userService) {
        // 验证用户和服务是否存在
          await this.userService.findById(userId);
        
        const service = await this.serviceService.findOne(serviceId);
        if (!service) {
          throw new NotFoundException(`服务 ${serviceId} 不存在`);
        }
        
        userService = queryRunner.manager.create(UserServiceEntity, {
          userId,
          serviceId,
          totalCount: purchaseCount,
          usedCount: 0,
          remainingCount: purchaseCount,
          purchasedCount: purchaseCount,
          freeCount: 0,
          freeUsedToday: 0,
          enabled: true,
          lastResetDate: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        });
      } else {
        // 增加总次数和购买次数
        userService.totalCount += purchaseCount;
        userService.purchasedCount += purchaseCount;
        userService.remainingCount = userService.totalCount - userService.usedCount;
        userService.updatedAt = new Date();
        
        // 如果之前发过预警，但现在次数充足，重置预警状态
        if (
          userService.alertSent && 
          userService.remainingCount / userService.totalCount > 0.1
        ) {
          userService.alertSent = false;
        }
      }
      
      // 保存更新
      const result = await queryRunner.manager.save(userService);
      
      // 创建购买记录（如果有相关实体）
      // 这里可以添加对应的记录代码
      
      // 提交事务
      await queryRunner.commitTransaction();
      
      this.logger.log(`服务次数增加成功: 用户ID=${userId}, 服务ID=${serviceId}, 购买次数=${purchaseCount}, 来源=${source}`);
      
      return result;
    } catch (error) {
      // 发生错误，回滚事务
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      
      this.logger.error(
        `为用户 ${userId} 添加服务 ${serviceId} 次数失败: ${error.message}`,
        error.stack
      );
      
      throw error;
    } finally {
      // 释放资源
      await queryRunner.release();
    }
  }

  /**
   * 增加用户服务调用总次数
   * 适用于用户购买或充值增加调用次数的场景
   */
  async updateTotalCount(
    updateDto: UpdateTotalCountDto,
    operatorId?: number, // 操作者ID，可以是管理员或用户本人
  ): Promise<UserServiceResponseDto> {
    const { userId, serviceId, additionalCount, source, remark } = updateDto;
    
    // 记录操作者信息
    const operatorType = operatorId === userId ? '用户本人' : '管理员';
    this.logger.log(`${operatorType} #${operatorId || '未知'} 开始为用户 #${userId} 的服务 #${serviceId} 增加 ${additionalCount} 次调用次数`);
    
    // 使用事务确保数据一致性
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      this.logger.log(`开始为用户 #${userId} 的服务 #${serviceId} 增加 ${additionalCount} 次调用次数`);
      
      // 查询用户服务关联
      const userService = await queryRunner.manager.findOne(UserServiceEntity, {
        where: { 
          user: { id: userId },
          service: { id: serviceId }
        },
        relations: ['user', 'service'],
        lock: { mode: 'pessimistic_write' }
      });
      
      // 如果不存在关联，创建新的关联
      if (!userService) {
        this.logger.log(`用户 #${userId} 与服务 #${serviceId} 没有关联，创建新的关联`);
        
        // 验证用户和服务是否存在
          await this.userService.findById(userId);
        
        const service = await this.serviceService.findOne(serviceId);
        if (!service) {
          throw new NotFoundException(`服务 #${serviceId} 不存在`);
        }
        
        // 创建新的关联记录
        const newUserService = new UserServiceEntity();
        newUserService.user = { id: userId } as any;
        newUserService.service = { id: serviceId } as any;
        newUserService.totalCount = additionalCount;
        newUserService.usedCount = 0;
        newUserService.freeUsedToday = 0;
        newUserService.lastResetDate = new Date();
        newUserService.alertSent = false;
        
        // 保存新的关联记录
        const savedEntity = await queryRunner.manager.save(newUserService);
        
        // 记录操作日志
        this.logger.log(`${operatorType} #${operatorId || '未知'} 为用户 #${userId} 创建服务 #${serviceId} 关联并设置初始调用次数 ${additionalCount}，来源: ${source || '未指定'}, 备注: ${remark || '无'}`);
        
        // 提交事务
        await queryRunner.commitTransaction();
        
        // 返回格式化后的响应
        return this.formatUserServiceResponse(savedEntity, {
          freeCount: 0,
          purchasedCount: additionalCount,
          remainingCount: additionalCount,
          enabled: true
        });
      }
      
      // 更新现有关联的总调用次数
      const originalCount = userService.totalCount;
      userService.totalCount += additionalCount;
      
      // 保存更新
      const updatedEntity = await queryRunner.manager.save(userService);
      
      // 记录操作日志
      this.logger.log(`${operatorType} #${operatorId || '未知'} 将用户 #${userId} 的服务 #${serviceId} 调用次数从 ${originalCount} 增加到 ${updatedEntity.totalCount}，增加: ${additionalCount}，来源: ${source || '未指定'}, 备注: ${remark || '无'}`);
      
      // 提交事务
      await queryRunner.commitTransaction();
      
      // 返回格式化后的响应
      return this.formatUserServiceResponse(updatedEntity, {
        freeCount: 0,
        totalCount: updatedEntity.totalCount,
        usedCount: updatedEntity.usedCount,
        remainingCount: updatedEntity.totalCount - updatedEntity.usedCount,
        enabled: true
      });
    } catch (error) {
      // 发生错误时回滚事务
      await queryRunner.rollbackTransaction();
      this.logger.error(`更新用户 #${userId} 服务 #${serviceId} 调用次数失败: ${error.message}`, error.stack);
      throw error;
    } finally {
      // 释放查询运行器
      await queryRunner.release();
    }
  }
}
