import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallH<PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { Request, Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { ResponseFormatterService } from '../../shared/response-formatter.service';
import { MetricsService } from '../../shared/metrics.service';
import { ApiCategorizerService } from '../../shared/api-categorizer.service';
import { AuthUserDto, ApiKeyDto } from '../dto/auth.dto';
import { RequestUtils } from '../utils/request.utils';
import { SKIP_RESPONSE_FORMAT_KEY } from '../decorators/public.decorator';

/**
 * 统一拦截器
 * 响应格式化功能
 */
@Injectable()
export class UnifiedInterceptor implements NestInterceptor {
  protected readonly logger = new Logger(UnifiedInterceptor.name);

  constructor(
    protected readonly configService: ConfigService,
    private readonly responseFormatter: ResponseFormatterService,
    private readonly metricsService: MetricsService,
    private readonly apiCategorizer: ApiCategorizerService,
    private readonly reflector: Reflector,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    const startTime = Date.now();
    const requestId = RequestUtils.generateRequestId();

    // 添加请求ID到响应头
    response.setHeader('X-Request-ID', requestId);

    // 检查是否跳过响应格式化
    const skipResponseFormat = this.reflector.get<boolean>(
      SKIP_RESPONSE_FORMAT_KEY,
      context.getHandler(),
    );

    // 记录请求开始
    this.logRequestStart(request, requestId);

    // 检查是否为SSE请求（URL包含/stream）
    if (request.url.includes('/stream')) {
      this.logger.debug(`检测到SSE请求: ${request.url}，跳过响应拦截`);
      // 对于SSE请求，不进行响应格式化，直接返回处理结果
      return next.handle().pipe(
        catchError((error) => {
          const endTime = Date.now();
          const duration = endTime - startTime;
          // 仅记录错误，不格式化响应
          this.logRequestError(request, requestId, duration, error);
          return throwError(() => error);
        })
      );
    }

    return next.handle().pipe(
      // 处理成功响应
      map((data) => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        // 记录请求成功
        this.logRequestSuccess(request, requestId, duration, data);

        // 强制设置HTTP状态码为200
        const response = context.switchToHttp().getResponse();
        if (response && typeof response.status === 'function') {
          response.status(200);
        }

        // 如果标记为跳过响应格式化，直接返回原始数据
        if (skipResponseFormat) {
          this.logger.debug(`跳过响应格式化: ${request.url}`);
          return data;
        }

        // 使用响应格式化服务格式化响应
        return this.responseFormatter.formatSuccessResponse(data, '操作成功', {
          requestId,
        });
      }),

      // 处理错误响应
      catchError((error) => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        // 记录请求错误
        this.logRequestError(request, requestId, duration, error);

        // 使用响应格式化服务格式化错误响应
        const formattedError = this.responseFormatter.formatErrorResponse(error, {
          requestId,
        });
        return throwError(() => formattedError);
      }),

      // 记录性能指标
      tap(() => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        this.recordPerformanceMetrics(request, duration);
      })
    );
  }

  /**
   * 记录请求开始
   */
  private logRequestStart(request: Request, requestId: string): void {
    const user = request['user'] as AuthUserDto;
    const authType = request['authType'] as string;
    const apiKey = request['apiKey'] as ApiKeyDto;
    const dateKey = RequestUtils.getDateKey();

    // 记录请求统计
    this.metricsService.recordRequestStat(dateKey, authType);

    // 对请求进行分类并记录
    const apiCategory = this.apiCategorizer.categorizeRequest(request);
    if (apiCategory) {
      this.metricsService.recordApiCategoryStat(
        dateKey, 
        apiCategory.category, 
        apiCategory.serviceCode
      );
    }

    // 记录日志
    this.logger.log(`请求开始: ${request.method} ${request.url}, ID=${requestId}, 用户=${user?.id || '未认证'}, 类型=${authType || '无'}`);
  }

  /**
   * 记录请求成功
   */
  private logRequestSuccess(
    request: Request,
    requestId: string,
    duration: number,
    data: any
  ): void {
    const user = request['user'] as AuthUserDto;

    // 记录成功日志（减少详细信息）
    this.logger.log(`请求成功: ${request.method} ${request.url}, ID=${requestId}, 用户=${user?.id || '未认证'}, 耗时=${duration}ms`);
  }

  /**
   * 记录请求错误
   */
  private logRequestError(
    request: Request,
    requestId: string,
    duration: number,
    error: any
  ): void {
    const user = request['user'] as AuthUserDto;

    // 记录错误日志
    this.logger.error(
      `请求错误: ${request.method} ${request.url}, ID=${requestId}, 用户=${user?.id || '未认证'}, 耗时=${duration}ms, 错误=${error.message}`,
      error.stack
    );
  }

  /**
   * 记录性能指标
   */
  private recordPerformanceMetrics(
    request: Request,
    duration: number
  ): void {
    const dateKey = RequestUtils.getDateKey();
    
    // 使用指标服务记录响应时间
    this.metricsService.recordResponseTime(duration, dateKey);
    
    // 如果是慢查询（超过1秒），添加更多信息
    if (duration > 1000) {
      const user = request['user'] as AuthUserDto;
      const apiCategory = this.apiCategorizer.categorizeRequest(request);
      
      this.metricsService.recordSlowQuery({
        method: request.method,
        url: request.url,
        duration,
        timestamp: new Date().toISOString(),
        userId: user?.id,
        category: apiCategory?.category,
        serviceCode: apiCategory?.serviceCode,
      });
    }
  }
}
