import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '@/shared/redis.service';
import * as nodemailer from 'nodemailer';

// 邮件提供商接口
interface EmailProvider {
  name: string;
  host: string;
  port: number;
  secure: boolean;
  user: string;
  pass: string;
  from: string;
  fromName: string;
  weight: number;
  status: 'active' | 'inactive';
  dailyLimit: number;
}

@Injectable()
export class EmailService implements OnModuleInit {
  private readonly logger = new Logger(EmailService.name);
  private providers: EmailProvider[] = [];
  private currentProviderIndex = 0;
  private transporters: Map<string, nodemailer.Transporter> = new Map();
  private isInitialized = false;

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {
    // 构造函数中只进行基本初始化，不执行可能阻塞的操作
  }

  async onModuleInit() {
    try {
      await this.initialize();
    } catch (error) {
      this.logger.error('邮件服务初始化失败，但不影响应用启动:', error);
      // 即使初始化失败，也不抛出错误，避免阻塞应用启动
    }
  }

  /**
   * 异步初始化邮件服务
   */
  private async initialize(): Promise<void> {
    this.initializeProviders();
    await this.initializeTransporters();
    this.startHealthCheck();
    this.isInitialized = true;
    this.logger.log('邮件服务初始化完成');
  }

  /**
   * 初始化邮件提供商配置
   */
  private initializeProviders(): void {
    const providers = this.configService.get<EmailProvider[]>('email.providers', []);
    this.providers = providers.filter(provider => provider.status === 'active');
    this.logger.log(`已加载 ${this.providers.length} 个邮件提供商`);
  }

  /**
   * 初始化邮件传输器
   */
  private async initializeTransporters(): Promise<void> {
    if (this.providers.length === 0) {
      this.logger.warn('未配置邮件提供商，邮件功能将不可用');
      return;
    }

    for (const provider of this.providers) {
      try {
        const transporter = nodemailer.createTransport({
          host: provider.host,
          port: provider.port,
          secure: provider.secure,
          auth: {
            user: provider.user,
            pass: provider.pass,
          },
          pool: true,
          maxConnections: 3,
          maxMessages: 50,
          connectionTimeout: 10000,
          greetingTimeout: 5000,
          socketTimeout: 30000,
          tls: {
            rejectUnauthorized: false,
          },
        });

        this.transporters.set(provider.name, transporter);
        this.logger.log(`邮件传输器初始化成功: ${provider.name}`);
      } catch (error) {
        this.logger.error(`邮件传输器初始化失败: ${provider.name}`, error);
        // 继续初始化其他提供商，不中断流程
      }
    }
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    if (this.providers.length === 0) {
      this.logger.warn('无邮件提供商可用，跳过健康检查');
      return;
    }

    // 延迟启动健康检查，避免启动时的阻塞
    setTimeout(() => {
      this.checkProvidersHealth();
      // 设置定期健康检查
      setInterval(async () => {
        try {
          await this.checkProvidersHealth();
        } catch (error) {
          this.logger.error('健康检查执行失败:', error);
          // 健康检查失败不应该影响服务运行
        }
      }, 1800000); // 每30分钟检查一次
    }, 30000); // 延迟30秒启动
  }

  /**
   * 检查提供商健康状态
   */
  private async checkProvidersHealth(): Promise<void> {
    if (!this.isInitialized) {
      this.logger.debug('邮件服务未初始化，跳过健康检查');
      return;
    }

    for (const provider of this.providers) {
      const transporter = this.transporters.get(provider.name);
      if (transporter) {
        try {
          // 使用超时控制，避免长时间阻塞
          await Promise.race([
            transporter.verify(),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('健康检查超时')), 15000)
            )
          ]);
          
          await this.redisService.getClient().setex(
            `email:provider:${provider.name}:health`,
            300,
            'healthy'
          );
          this.logger.debug(`邮件提供商健康检查通过: ${provider.name}`);
        } catch (error) {
          this.logger.warn(`邮件提供商健康检查失败: ${provider.name}`, error.message || error);
          try {
            await this.redisService.getClient().setex(
              `email:provider:${provider.name}:health`,
              300,
              'unhealthy'
            );
          } catch (redisError) {
            this.logger.error('Redis操作失败:', redisError);
          }
        }
      }
    }
  }

  /**
   * 获取下一个可用的邮件提供商
   */
  private async getNextProvider(): Promise<EmailProvider | null> {
    if (this.providers.length === 0) {
      return null;
    }

    // 轮询策略
    for (let i = 0; i < this.providers.length; i++) {
      const provider = this.providers[this.currentProviderIndex];
      this.currentProviderIndex = (this.currentProviderIndex + 1) % this.providers.length;

      // 检查提供商健康状态
      const health = await this.redisService.getClient().get(
        `email:provider:${provider.name}:health`
      );
      
      if (health !== 'unhealthy') {
        // 检查日发送限制
        const dailyCount = await this.getDailyCount(provider.name);
        if (dailyCount < provider.dailyLimit) {
          return provider;
        }
      }
    }

    return null;
  }

  /**
   * 获取提供商日发送量
   */
  private async getDailyCount(providerName: string): Promise<number> {
    const today = new Date().toISOString().split('T')[0];
    const key = `email:provider:${providerName}:daily:${today}`;
    const count = await this.redisService.getClient().get(key);
    return count ? parseInt(count, 10) : 0;
  }

  /**
   * 增加提供商日发送量
   */
  private async incrementDailyCount(providerName: string): Promise<void> {
    const today = new Date().toISOString().split('T')[0];
    const key = `email:provider:${providerName}:daily:${today}`;
    await this.redisService.getClient().incr(key);
    await this.redisService.getClient().expire(key, 86400); // 24小时过期
  }

  /**
   * 发送验证码邮件
   */
  async sendVerificationCode(
    email: string,
    code: string,
    type: 'register' | 'login' | 'reset' = 'register',
    ip?: string
  ): Promise<{ success: boolean; message?: string; provider?: string }> {
    try {
      // 检查服务是否已初始化
      if (!this.isInitialized) {
        this.logger.warn('邮件服务未初始化，无法发送邮件');
        return {
          success: false,
          message: '邮件服务暂时不可用，请稍后重试'
        };
      }

      // 限流检查已移至统一限流守卫，通过装饰器控制
      // const rateLimitKey = `email:rate:${email}`;
      // try {
      //   const recentSends = await this.redisService.getClient().get(rateLimitKey);
      //   if (recentSends && parseInt(recentSends, 10) >= 5) {
      //     return {
      //       success: false,
      //       message: '发送过于频繁，请稍后再试'
      //     };
      //   }
      // } catch (redisError) {
      //   this.logger.warn('Redis频率限制检查失败，继续发送:', redisError);
      // }

      // 获取可用的邮件提供商
      const provider = await this.getNextProvider();
      if (!provider) {
        return {
          success: false,
          message: '暂无可用的邮件服务'
        };
      }

      const transporter = this.transporters.get(provider.name);
      if (!transporter) {
        return {
          success: false,
          message: '邮件服务配置错误'
        };
      }

      // 开发环境下输出验证码到控制台
      if (this.configService.get('NODE_ENV') === 'development') {
        this.logger.log(`[EMAIL] 发送验证码到 ${email}: ${code}`);
      }

      // 构建邮件内容
      const subject = this.getEmailSubject(type);
      const html = this.getEmailTemplate(code, type);

      // 发送邮件
      const mailOptions = {
        from: `${provider.fromName} <${provider.from}>`,
        to: email,
        subject,
        html,
      };

      await transporter.sendMail(mailOptions);

      // 更新发送计数
      try {
        await this.incrementDailyCount(provider.name);
        // 限流计数已移至统一限流模块
        // await this.redisService.getClient().incr(rateLimitKey);
        // await this.redisService.getClient().expire(rateLimitKey, 60); // 1分钟内最多5次
      } catch (redisError) {
        this.logger.warn('Redis计数更新失败:', redisError);
      }

      this.logger.log(`验证码邮件发送成功: ${email}, 提供商: ${provider.name}`);
      
      return {
        success: true,
        message: '验证码发送成功',
        provider: provider?.name
      };
    } catch (error) {
      this.logger.error('发送验证码邮件失败:', error);
      return {
        success: false,
        message: '邮件发送失败，请稍后重试'
      };
    }
  }

  /**
   * 获取邮件主题
   */
  private getEmailSubject(type: string): string {
    const subjects = {
      register: '【开放平台】注册验证码',
      login: '【开放平台】登录验证码',
      reset: '【开放平台】密码重置验证码',
    };
    return subjects[type] || '【开放平台】验证码';
  }

  /**
   * 获取邮件模板
   */
  private getEmailTemplate(code: string, type: string): string {
    const templates = {
      register: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">欢迎注册开放平台</h2>
          <p>您的注册验证码是：</p>
          <div style="background: #f5f5f5; padding: 20px; text-align: center; font-size: 24px; font-weight: bold; color: #007bff;">
            ${code}
          </div>
          <p style="color: #666;">验证码5分钟内有效，请及时使用。</p>
        </div>
      `,
      login: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">登录验证</h2>
          <p>您的登录验证码是：</p>
          <div style="background: #f5f5f5; padding: 20px; text-align: center; font-size: 24px; font-weight: bold; color: #28a745;">
            ${code}
          </div>
          <p style="color: #666;">验证码5分钟内有效，请及时使用。</p>
        </div>
      `,
      reset: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">密码重置</h2>
          <p>您的密码重置验证码是：</p>
          <div style="background: #f5f5f5; padding: 20px; text-align: center; font-size: 24px; font-weight: bold; color: #dc3545;">
            ${code}
          </div>
          <p style="color: #666;">验证码5分钟内有效，请及时使用。</p>
        </div>
      `,
    };
    return templates[type] || templates.register;
  }

  /**
   * 检查邮件发送状态
   */
  async checkEmailStatus(email: string): Promise<{
    canSend: boolean;
    remainingAttempts: number;
    nextAvailableTime?: Date;
  }> {
    const rateLimitKey = `email:rate:${email}`;
    const recentSends = await this.redisService.getClient().get(rateLimitKey);
    const attempts = recentSends ? parseInt(recentSends, 10) : 0;
    const maxAttempts = 5;
    
    if (attempts >= maxAttempts) {
      const ttl = await this.redisService.getClient().ttl(rateLimitKey);
      const nextAvailableTime = new Date(Date.now() + ttl * 1000);
      
      return {
        canSend: false,
        remainingAttempts: 0,
        nextAvailableTime,
      };
    }
    
    return {
      canSend: true,
      remainingAttempts: maxAttempts - attempts,
    };
  }

  /**
   * 发送用户注册欢迎邮件
   */
  async sendWelcomeEmail(
    email: string,
    username: string,
    giftPackage?: any
  ): Promise<{ success: boolean; message?: string }> {
    try {
      if (!this.isInitialized) {
        return { success: false, message: '邮件服务暂时不可用' };
      }

      const provider = await this.getNextProvider();
      if (!provider) {
        return { success: false, message: '暂无可用的邮件服务' };
      }

      const transporter = this.transporters.get(provider.name);
      if (!transporter) {
        return { success: false, message: '邮件服务配置错误' };
      }

      const subject = '【开放平台】欢迎加入！';
      const html = this.getWelcomeEmailTemplate(username, giftPackage);

      const mailOptions = {
        from: `${provider.fromName} <${provider.from}>`,
        to: email,
        subject,
        html,
      };

      await transporter.sendMail(mailOptions);
      await this.incrementDailyCount(provider.name);

      this.logger.log(`欢迎邮件发送成功: ${email}`);
      return { success: true, message: '欢迎邮件发送成功' };
    } catch (error) {
      this.logger.error('发送欢迎邮件失败:', error);
      return { success: false, message: '邮件发送失败' };
    }
  }

  /**
   * 发送实名认证结果通知邮件
   */
  async sendVerificationResultEmail(
    email: string,
    username: string,
    status: 'approved' | 'rejected',
    reason?: string,
    reward?: any
  ): Promise<{ success: boolean; message?: string }> {
    try {
      if (!this.isInitialized) {
        return { success: false, message: '邮件服务暂时不可用' };
      }

      const provider = await this.getNextProvider();
      if (!provider) {
        return { success: false, message: '暂无可用的邮件服务' };
      }

      const transporter = this.transporters.get(provider.name);
      if (!transporter) {
        return { success: false, message: '邮件服务配置错误' };
      }

      const subject = status === 'approved' 
        ? '【开放平台】实名认证通过通知'
        : '【开放平台】实名认证未通过通知';
      const html = this.getVerificationResultTemplate(username, status, reason, reward);

      const mailOptions = {
        from: `${provider.fromName} <${provider.from}>`,
        to: email,
        subject,
        html,
      };

      await transporter.sendMail(mailOptions);
      await this.incrementDailyCount(provider.name);

      this.logger.log(`实名认证结果邮件发送成功: ${email}, 状态: ${status}`);
      return { success: true, message: '通知邮件发送成功' };
    } catch (error) {
      this.logger.error('发送实名认证结果邮件失败:', error);
      return { success: false, message: '邮件发送失败' };
    }
  }

  /**
   * 发送推荐奖励通知邮件
   */
  async sendReferralRewardEmail(
    email: string,
    username: string,
    referredUser: string,
    reward: any
  ): Promise<{ success: boolean; message?: string }> {
    try {
      if (!this.isInitialized) {
        return { success: false, message: '邮件服务暂时不可用' };
      }

      const provider = await this.getNextProvider();
      if (!provider) {
        return { success: false, message: '暂无可用的邮件服务' };
      }

      const transporter = this.transporters.get(provider.name);
      if (!transporter) {
        return { success: false, message: '邮件服务配置错误' };
      }

      const subject = '【开放平台】推荐奖励到账通知';
      const html = this.getReferralRewardTemplate(username, referredUser, reward);

      const mailOptions = {
        from: `${provider.fromName} <${provider.from}>`,
        to: email,
        subject,
        html,
      };

      await transporter.sendMail(mailOptions);
      await this.incrementDailyCount(provider.name);

      this.logger.log(`推荐奖励邮件发送成功: ${email}`);
      return { success: true, message: '奖励通知邮件发送成功' };
    } catch (error) {
      this.logger.error('发送推荐奖励邮件失败:', error);
      return { success: false, message: '邮件发送失败' };
    }
  }

  /**
   * 发送服务调用异常通知邮件
   */
  async sendServiceErrorNotification(
    email: string,
    username: string,
    serviceName: string,
    errorMessage: string,
    timestamp: Date
  ): Promise<{ success: boolean; message?: string }> {
    try {
      if (!this.isInitialized) {
        return { success: false, message: '邮件服务暂时不可用' };
      }

      const provider = await this.getNextProvider();
      if (!provider) {
        return { success: false, message: '暂无可用的邮件服务' };
      }

      const transporter = this.transporters.get(provider.name);
      if (!transporter) {
        return { success: false, message: '邮件服务配置错误' };
      }

      const subject = '【开放平台】服务调用异常通知';
      const html = this.getServiceErrorTemplate(username, serviceName, errorMessage, timestamp);

      const mailOptions = {
        from: `${provider.fromName} <${provider.from}>`,
        to: email,
        subject,
        html,
      };

      await transporter.sendMail(mailOptions);
      await this.incrementDailyCount(provider.name);

      this.logger.log(`服务异常通知邮件发送成功: ${email}`);
      return { success: true, message: '异常通知邮件发送成功' };
    } catch (error) {
      this.logger.error('发送服务异常通知邮件失败:', error);
      return { success: false, message: '邮件发送失败' };
    }
  }



  /**
   * 通用邮件发送方法
   */
  async sendEmail(options: {
    to: string;
    subject: string;
    html: string;
    from?: string;
  }): Promise<{ success: boolean; message?: string }> {
    try {
      if (!this.isInitialized) {
        return { success: false, message: '邮件服务暂时不可用' };
      }

      const provider = await this.getNextProvider();
      if (!provider) {
        return { success: false, message: '暂无可用的邮件服务' };
      }

      const transporter = this.transporters.get(provider.name);
      if (!transporter) {
        return { success: false, message: '邮件服务配置错误' };
      }

      const mailOptions = {
        from: options.from || `${provider.fromName} <${provider.from}>`,
        to: options.to,
        subject: options.subject,
        html: options.html,
      };

      await transporter.sendMail(mailOptions);

      // 更新发送计数
      try {
        await this.incrementDailyCount(provider.name);
      } catch (redisError) {
        this.logger.warn('Redis计数更新失败:', redisError);
      }

      this.logger.log(`邮件发送成功: ${options.to}, 提供商: ${provider.name}`);
      
      return {
        success: true,
        message: '邮件发送成功',
      };
    } catch (error) {
      this.logger.error('发送邮件失败:', error);
      return {
        success: false,
        message: '邮件发送失败，请稍后重试'
      };
    }
  }

  /**
   * 模板邮件发送方法
   */
  async sendTemplateEmail(
    to: string,
    subject: string,
    template: string,
    data: any
  ): Promise<{ success: boolean; message?: string }> {
    try {
      // 根据模板生成HTML内容
      let html = '';
      
      switch (template) {
        case 'quota_warning':
          html = this.getQuotaWarningTemplate(data);
          break;
        case 'quota_exhausted':
          html = this.getQuotaExhaustedTemplate(data);
          break;
        case 'payment_failed':
          html = this.getPaymentFailedTemplate(data);
          break;
        case 'system_error':
          html = this.getSystemErrorTemplate(data);
          break;
        default:
          html = this.getDefaultTemplate(data);
      }

      return await this.sendEmail({
        to,
        subject,
        html
      });
    } catch (error) {
      this.logger.error('发送模板邮件失败:', error);
      return {
        success: false,
        message: '模板邮件发送失败，请稍后重试'
      };
    }
  }

  /**
   * 配额警告模板
   */
  private getQuotaWarningTemplate(data: any): string {
    const { userName, serviceName, threshold, currentValue } = data;
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #ff9800; margin: 0;">⚠️ 配额使用警告</h1>
        </div>
        
        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #ffeaa7;">
          <p style="font-size: 16px; margin: 0 0 15px 0;">亲爱的 <strong>${userName}</strong>，</p>
          <p style="margin: 0 0 15px 0;">您的服务 <strong>${serviceName}</strong> 配额使用量已达到警告阈值。</p>
          
          <div style="background: white; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <p style="margin: 5px 0;"><strong>警告阈值：</strong>${threshold}%</p>
            <p style="margin: 5px 0;"><strong>当前使用：</strong>${currentValue}%</p>
          </div>
          
          <p style="margin: 15px 0 0 0;">请及时关注配额使用情况，避免服务中断。</p>
        </div>
      </div>
    `;
  }

  /**
   * 配额耗尽模板
   */
  private getQuotaExhaustedTemplate(data: any): string {
    const { userName, serviceName } = data;
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #dc3545; margin: 0;">🚫 配额已耗尽</h1>
        </div>
        
        <div style="background: #f8d7da; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #f5c6cb;">
          <p style="font-size: 16px; margin: 0 0 15px 0;">亲爱的 <strong>${userName}</strong>，</p>
          <p style="margin: 0 0 15px 0;">您的服务 <strong>${serviceName}</strong> 配额已完全耗尽，服务已暂停。</p>
          <p style="margin: 15px 0 0 0;">请及时充值或升级套餐以恢复服务。</p>
        </div>
      </div>
    `;
  }

  /**
   * 支付失败模板
   */
  private getPaymentFailedTemplate(data: any): string {
    const { userName, message } = data;
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #dc3545; margin: 0;">💳 支付失败通知</h1>
        </div>
        
        <div style="background: #f8d7da; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #f5c6cb;">
          <p style="font-size: 16px; margin: 0 0 15px 0;">亲爱的 <strong>${userName}</strong>，</p>
          <p style="margin: 0 0 15px 0;">您的支付操作失败：${message}</p>
          <p style="margin: 15px 0 0 0;">请检查支付方式或联系客服处理。</p>
        </div>
      </div>
    `;
  }

  /**
   * 系统错误模板
   */
  private getSystemErrorTemplate(data: any): string {
    const { userName, message } = data;
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #dc3545; margin: 0;">⚠️ 系统异常通知</h1>
        </div>
        
        <div style="background: #f8d7da; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #f5c6cb;">
          <p style="font-size: 16px; margin: 0 0 15px 0;">亲爱的 <strong>${userName}</strong>，</p>
          <p style="margin: 0 0 15px 0;">系统检测到异常：${message}</p>
          <p style="margin: 15px 0 0 0;">我们正在处理此问题，请稍后重试。</p>
        </div>
      </div>
    `;
  }

  /**
   * 默认模板
   */
  private getDefaultTemplate(data: any): string {
    const { userName, message } = data;
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <p style="font-size: 16px; margin: 0 0 15px 0;">亲爱的 <strong>${userName || '用户'}</strong>，</p>
          <p style="margin: 0 0 15px 0;">${message || '您有一条新的通知消息。'}</p>
        </div>
      </div>
    `;
  }

  /**
   * 获取欢迎邮件模板
   */
  private getWelcomeEmailTemplate(username: string, giftPackage?: any): string {
    const giftInfo = giftPackage ? `
      <div style="background: #e8f5e8; padding: 15px; margin: 20px 0; border-radius: 5px;">
        <h3 style="color: #28a745; margin: 0 0 10px 0;">🎁 新用户礼包已到账</h3>
        <p style="margin: 5px 0;">套餐名称：${giftPackage.name}</p>
        <p style="margin: 5px 0;">包含服务：${giftPackage.services?.join(', ') || '多项服务'}</p>
        <p style="margin: 5px 0;">有效期：${giftPackage.validDays || 30}天</p>
      </div>
    ` : '';

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #007bff; margin: 0;">欢迎加入开放平台！</h1>
        </div>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <p style="font-size: 16px; margin: 0 0 15px 0;">亲爱的 <strong>${username}</strong>，</p>
          <p style="margin: 0 0 15px 0;">恭喜您成功注册开放平台账户！我们为您提供了丰富的API服务，包括：</p>
          <ul style="margin: 10px 0; padding-left: 20px;">
            <li>OCR文字识别服务</li>
            <li>地址信息提取服务</li>
            <li>自然语言处理服务</li>
            <li>计算机视觉服务</li>
          </ul>
        </div>
        
        ${giftInfo}
        
        <div style="background: #fff3cd; padding: 15px; margin: 20px 0; border-radius: 5px; border-left: 4px solid #ffc107;">
          <h3 style="color: #856404; margin: 0 0 10px 0;">💡 快速开始</h3>
          <p style="margin: 5px 0;">1. 完成实名认证，获得更多奖励</p>
          <p style="margin: 5px 0;">2. 获取API密钥，开始调用服务</p>
          <p style="margin: 5px 0;">3. 查看API文档，了解接口详情</p>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
          <p style="color: #666; font-size: 14px;">如有任何问题，请随时联系我们的客服团队。</p>
          <p style="color: #666; font-size: 14px;">祝您使用愉快！</p>
        </div>
      </div>
    `;
  }

  /**
   * 获取实名认证结果邮件模板
   */
  private getVerificationResultTemplate(
    username: string,
    status: 'approved' | 'rejected',
    reason?: string,
    reward?: any
  ): string {
    if (status === 'approved') {
      const rewardInfo = reward ? `
        <div style="background: #e8f5e8; padding: 15px; margin: 20px 0; border-radius: 5px;">
          <h3 style="color: #28a745; margin: 0 0 10px 0;">🎁 实名认证奖励已到账</h3>
          <p style="margin: 5px 0;">套餐名称：${reward.name}</p>
          <p style="margin: 5px 0;">包含服务：${reward.services?.join(', ') || '多项服务'}</p>
          <p style="margin: 5px 0;">有效期：${reward.validDays || 30}天</p>
        </div>
      ` : '';

      return `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #28a745; margin: 0;">✅ 实名认证通过</h1>
          </div>
          
          <div style="background: #d4edda; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #c3e6cb;">
            <p style="font-size: 16px; margin: 0 0 15px 0;">亲爱的 <strong>${username}</strong>，</p>
            <p style="margin: 0 0 15px 0;">恭喜您！您的实名认证已通过审核。</p>
            <p style="margin: 0;">现在您可以享受更多的平台服务和更高的调用限额。</p>
          </div>
          
          ${rewardInfo}
          
          <div style="text-align: center; margin-top: 30px;">
            <p style="color: #666; font-size: 14px;">感谢您对我们平台的信任！</p>
          </div>
        </div>
      `;
    } else {
      return `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #dc3545; margin: 0;">❌ 实名认证未通过</h1>
          </div>
          
          <div style="background: #f8d7da; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #f5c6cb;">
            <p style="font-size: 16px; margin: 0 0 15px 0;">亲爱的 <strong>${username}</strong>，</p>
            <p style="margin: 0 0 15px 0;">很抱歉，您的实名认证未通过审核。</p>
            ${reason ? `<p style="margin: 0 0 15px 0;"><strong>原因：</strong>${reason}</p>` : ''}
            <p style="margin: 0;">请检查您提交的信息是否准确，并重新提交认证申请。</p>
          </div>
          
          <div style="text-align: center; margin-top: 30px;">
            <p style="color: #666; font-size: 14px;">如有疑问，请联系客服咨询。</p>
          </div>
        </div>
      `;
    }
  }

  /**
   * 获取推荐奖励邮件模板
   */
  private getReferralRewardTemplate(
    username: string,
    referredUser: string,
    reward: any
  ): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #007bff; margin: 0;">🎉 推荐奖励到账</h1>
        </div>
        
        <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #bbdefb;">
          <p style="font-size: 16px; margin: 0 0 15px 0;">亲爱的 <strong>${username}</strong>，</p>
          <p style="margin: 0 0 15px 0;">恭喜您！您推荐的用户 <strong>${referredUser}</strong> 已成功注册并完成实名认证。</p>
          <p style="margin: 0;">您的推荐奖励已到账，感谢您对平台的支持！</p>
        </div>
        
        <div style="background: #e8f5e8; padding: 15px; margin: 20px 0; border-radius: 5px;">
          <h3 style="color: #28a745; margin: 0 0 10px 0;">🎁 奖励详情</h3>
          <p style="margin: 5px 0;">套餐名称：${reward.name}</p>
          <p style="margin: 5px 0;">包含服务：${reward.services?.join(', ') || '多项服务'}</p>
          <p style="margin: 5px 0;">有效期：${reward.validDays || 30}天</p>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
          <p style="color: #666; font-size: 14px;">继续推荐好友，获得更多奖励！</p>
        </div>
      </div>
    `;
  }

  /**
   * 获取服务异常通知邮件模板
   */
  private getServiceErrorTemplate(
    username: string,
    serviceName: string,
    errorMessage: string,
    timestamp: Date
  ): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #dc3545; margin: 0;">⚠️ 服务调用异常通知</h1>
        </div>
        
        <div style="background: #f8d7da; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #f5c6cb;">
          <p style="font-size: 16px; margin: 0 0 15px 0;">亲爱的 <strong>${username}</strong>，</p>
          <p style="margin: 0 0 15px 0;">您的服务调用出现异常，详情如下：</p>
          
          <div style="background: #fff; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <p style="margin: 5px 0;"><strong>服务名称：</strong>${serviceName}</p>
            <p style="margin: 5px 0;"><strong>异常时间：</strong>${timestamp.toLocaleString('zh-CN')}</p>
            <p style="margin: 5px 0;"><strong>错误信息：</strong>${errorMessage}</p>
          </div>
          
          <p style="margin: 0;">请检查您的调用参数或联系技术支持。</p>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
          <p style="color: #666; font-size: 14px;">如需帮助，请联系我们的技术支持团队。</p>
        </div>
      </div>
    `;
  }
}