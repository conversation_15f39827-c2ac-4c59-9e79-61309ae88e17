import { AuthResult, AuthApiKey } from '../types/auth.types';

/**
 * 认证验证器接口
 * 定义认证相关的核心方法，用于解耦守卫和具体实现
 */
export interface IAuthValidator {
  /**
   * 验证JWT令牌
   */
  validateJwtToken(token: string): Promise<AuthResult>;

  /**
   * 验证API密钥
   */
  validateApiKey(apiKey: string): Promise<AuthResult>;

  /**
   * 更新API密钥使用记录
   */
  updateApiKeyUsage(apiKey: string, clientIp: string): Promise<void>;
}

/**
 * 核心认证服务接口
 * 定义基础认证功能，不涉及业务逻辑
 */
export interface ICoreAuthService {
  /**
   * 验证JWT令牌（纯验证，不查询数据库）
   */
  validateJwtToken(token: string): Promise<AuthResult>;

  /**
   * 验证API密钥格式
   */
  validateApiKeyFormat(apiKey: string): boolean;

  /**
   * 从缓存获取API密钥信息
   */
  getApiKeyFromCache(keyHash: string): Promise<AuthApiKey | null>;

  /**
   * 缓存API密钥信息
   */
  cacheApiKey(keyHash: string, apiKeyData: AuthApiKey, ttl?: number): Promise<void>;

  /**
   * 生成API密钥哈希
   */
  hashApiKey(apiKey: string): Promise<string>;

  /**
   * 验证API密钥哈希
   */
  verifyApiKeyHash(apiKey: string, hash: string): Promise<boolean>;

  /**
   * 检查API密钥是否过期
   */
  isApiKeyExpired(apiKey: AuthApiKey): boolean;

  /**
   * 检查API密钥状态
   */
  isApiKeyActive(apiKey: AuthApiKey): boolean;

  /**
   * 记录认证失败
   */
  recordAuthFailure(identifier: string, reason: string, clientIp?: string): Promise<void>;

  /**
   * 检查是否被限制访问
   */
  isAccessBlocked(identifier: string, maxFailures?: number): Promise<boolean>;

  /**
   * 清除认证失败记录
   */
  clearAuthFailures(identifier: string): Promise<void>;
}

/**
 * 认证事件接口
 */
export interface IAuthEvent {
  /**
   * 事件类型
   */
  type: 'user_info_request' | 'api_key_validation' | 'auth_failure' | 'auth_success';

  /**
   * 事件数据
   */
  data: any;

  /**
   * 回调函数
   */
  callback?: (result: any) => void;
}

/**
 * 用户信息请求事件
 */
export interface IUserInfoRequestEvent extends IAuthEvent {
  type: 'user_info_request';
  data: {
    userId: number;
    includeRoles?: boolean;
    includePermissions?: boolean;
  };
  callback: (user: any) => void;
}

/**
 * API密钥验证事件
 */
export interface IApiKeyValidationEvent extends IAuthEvent {
  type: 'api_key_validation';
  data: {
    apiKey: string;
    clientIp?: string;
  };
  callback: (result: AuthResult) => void;
}

/**
 * 认证失败事件
 */
export interface IAuthFailureEvent extends IAuthEvent {
  type: 'auth_failure';
  data: {
    identifier: string;
    reason: string;
    clientIp?: string;
    userAgent?: string;
  };
}

/**
 * 认证成功事件
 */
export interface IAuthSuccessEvent extends IAuthEvent {
  type: 'auth_success';
  data: {
    userId: number;
    authType: 'jwt' | 'api-key';
    clientIp?: string;
    userAgent?: string;
  };
}