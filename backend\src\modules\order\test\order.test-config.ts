import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DataSource } from 'typeorm';
import { OrderModule } from '../order.module';
import { UserModule } from '../../user/user.module';
import { ServiceModule } from '../../service/service.module';
import { UserServiceModule } from '../../user-service/user-service.module';
import { OrderEntity } from '../entities/order.entity';
import { OrderItemEntity } from '../entities/order-item.entity';
import { UserEntity } from '../../user/entities/user.entity';
import { ServiceEntity } from '../../service/entities/service.entity';
import { ServiceType, ServiceStatus, PricingModel } from '../../service/enums/service.enum';
import { PaymentMethod } from '../enums/order.enum';
import { UserServiceEntity } from '../../user-service/entities/user-service.entity';

/**
 * 测试数据库配置
 */
export const getTestDatabaseConfig = () => ({
  type: 'sqlite' as const,
  database: ':memory:',
  entities: [
    OrderEntity,
    OrderItemEntity,
    UserEntity,
    ServiceEntity,
    UserServiceEntity,
  ],
  synchronize: true,
  logging: false,
});

/**
 * 创建测试模块
 */
export async function createTestModule(): Promise<TestingModule> {
  const module: TestingModule = await Test.createTestingModule({
    imports: [
      ConfigModule.forRoot({
        isGlobal: true,
        envFilePath: '.env.test',
      }),
      TypeOrmModule.forRoot(getTestDatabaseConfig()),
      EventEmitterModule.forRoot(),
      OrderModule,
      UserModule,
      ServiceModule,
      UserServiceModule,
    ],
  }).compile();

  return module;
}

/**
 * 清理测试数据库
 */
export async function cleanupTestDatabase(dataSource: DataSource): Promise<void> {
  const entities = dataSource.entityMetadatas;
  
  for (const entity of entities) {
    const repository = dataSource.getRepository(entity.name);
    await repository.clear();
  }
}

/**
 * 创建测试用户
 */
export const createTestUser = () => ({
  username: 'testuser',
  email: '<EMAIL>',
  password: 'hashedpassword',
  balance: 1000,
  isActive: true,
  isFreeQuotaEligible: true,
});

/**
 * 创建测试服务
 */
export const createTestService = () => ({
  name: 'Test Service',
  code: 'TEST_SERVICE',
  description: 'Test service for unit tests',
  unitPrice: 10.00,
  type: ServiceType.OTHER,
  status: ServiceStatus.ACTIVE,
  pricingModel: PricingModel.PER_REQUEST,
  config: {
    maxConcurrency: 10,
    timeout: 30000,
  },
});

/**
 * 创建测试订单数据
 */
export const createTestOrderData = (userId: number) => ({
  userId,
  orderType: 'service' as const,
  totalAmount: 100.00,
  remark: 'Test order',
});

/**
 * 创建测试充值订单数据
 */
export const createTestRechargeData = () => ({
  amount: 100.00,
  paymentMethod: PaymentMethod.ALIPAY,
  remark: 'Test recharge',
});

/**
 * 模拟支付回调数据
 */
export const createMockPaymentCallback = (paymentNo: string) => ({
  paymentNo,
  thirdPartyNo: 'mock_third_party_no',
  status: 'success' as const,
  amount: 100.00,
  paidAt: new Date().toISOString(),
  callbackData: { source: 'test' },
});
