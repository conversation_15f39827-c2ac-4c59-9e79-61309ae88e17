import {
  Controller,
  Post,
  Body,
  UseInterceptors,
  ClassSerializerInterceptor,
  Request,
  Get,
  Headers,
  Req,
  BadRequestException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiBody,
  ApiConsumes,
  ApiQuery,
  ApiBearerAuth,
  ApiHeader,
} from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { CaptchaService } from '@/shared/captcha.service';
import { Recaptcha } from '@nestlab/google-recaptcha';
import { Public } from '@/common/decorators/public.decorator';
import { ApiException } from '@/common/exceptions/api.exception';
import {
  LoginDto,
  RegisterDto,
  LoginResponseDto,
  RefreshTokenDto,
  RefreshResponseDto,
} from './dto';
import { CreateUserDto } from '../user/dto';
import { UserService } from '../user/user.service';
import { ApiKeyService } from '../api-key/api-key.service';
import {
  SendSmsCodeDto,
  VerifySmsCodeDto,
  SendEmailCodeDto,
  VerifyEmailCodeDto,
  VerifyImageCaptchaDto,
  VerifySlideVerifyDto,
  VerifyRecaptchaDto,
  EnterpriseCaptchaVerifyDto,
} from './dto/captcha';

/**
 * auth控制器
 * 处理用户登录、注册、验证码等认证相关操作
 */
@ApiTags('认证管理')
@Public()
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly captchaService: CaptchaService,
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    @Inject(forwardRef(() => ApiKeyService))
    private readonly apiKeyService: ApiKeyService,
  ) {}

  @ApiOperation({
    summary: '用户注册',
  })
  @ApiBody({
    type: CreateUserDto,
    required: true,
  })
  @UseInterceptors(ClassSerializerInterceptor) // 接口返回数据时过滤掉使用 @Exclude 标记的列
  @Post('/register')
  // @RegisterRateLimit()
  async register(@Body() user: RegisterDto) {
    const result = await this.authService.register(user);
    return result;
  }

  @ApiOperation({
    summary: '用户登陆',
  })
  @ApiBody({
    type: LoginDto,
    required: true,
  })
  @UseInterceptors(ClassSerializerInterceptor) // 接口返回数据时过滤掉使用 @Exclude 标记的列
  @Post('/login')
  async login(@Body() user: LoginDto, @Request() req) {
    return await this.authService.login(user);
  }

  @ApiOperation({
    summary: '退出登陆',
  })
  @ApiBody({
    type: LoginDto,
    required: true,
  })
  @Post('/logout')
  logout(@Body() user, @Request() req) {
    const accessToken = req.get('Authorization');
    return this.authService.logout(accessToken);
  }

  /**
   * 刷新访问令牌
   */
  @Post('refresh')
  @ApiOperation({
    summary: '刷新token',
    description: '使用刷新令牌获取新的访问令牌和刷新令牌',
  })
  @ApiBody({
    type: RefreshTokenDto,
    required: true,
  })
  async refresh(@Body() refreshTokenDto: RefreshTokenDto) {
    return this.authService.refreshToken(refreshTokenDto.refresh_token);
  }

  @Get('captcha/image')
  @ApiOperation({ summary: '获取图像验证码' })
  async getImageCaptcha(): Promise<Record<string, any>> {
    return await this.captchaService.generateImageCaptcha();
  }

  @Post('slide/verify')
  @ApiOperation({ summary: '验证滑动拼图验证码' })
  @ApiBody({ type: VerifySlideVerifyDto })
  async verifySlideVerify(
    @Body() verifySlideVerifyDto: VerifySlideVerifyDto,
  ): Promise<Record<string, any>> {
    const {
      moveDistance,
      duration,
      deviceFingerprint,
      behaviorPattern,
      userAgent,
      screenResolution,
      timezone,
      language,
      timestamp,
      sessionId,
      clientIP,
      browserFingerprint,
      mouseTrajectory,
      keyboardPattern,
      touchPattern,
    } = verifySlideVerifyDto;

    const securityData = {
      deviceFingerprint,
      behaviorPattern,
      userAgent,
      screenResolution,
      timezone,
      language,
      timestamp,
      sessionId,
      clientIP,
      browserFingerprint,
      mouseTrajectory,
      keyboardPattern,
      touchPattern,
    };

    const result = await this.captchaService.verifySlideVerify(
      moveDistance,
      duration,
      securityData,
    );

    if (result?.success) {
      return {
        success: result?.success,
        riskScore: result.riskScore,
      };
    } else {
      throw new ApiException(20006);
    }
  }

  @Post('image/verify')
  @ApiOperation({ summary: '验证图像验证码' })
  @ApiBody({ type: VerifyImageCaptchaDto })
  async verifyImageCaptcha(
    @Body() verifyImageCaptchaDto: VerifyImageCaptchaDto,
  ): Promise<Record<string, any>> {
    const { captchaId, captchaCode } = verifyImageCaptchaDto;
    const valid = await this.captchaService.verifyImageCaptcha(
      captchaId,
      captchaCode,
    );
    if (valid) {
      return { valid };
    } else {
      throw new ApiException(20001);
    }
  }

  @Post('sms/send')
  @ApiOperation({ summary: '发送短信验证码' })
  @ApiBody({ type: SendSmsCodeDto })
  async sendSmsCode(@Body() sendSmsCodeDto: SendSmsCodeDto): Promise<boolean> {
    const { phone, type } = sendSmsCodeDto;
    return await this.captchaService.sendSmsCode(phone, type);
  }

  @Post('sms/verify')
  @ApiOperation({ summary: '验证短信验证码' })
  @ApiBody({ type: VerifySmsCodeDto })
  async verifySmsCode(
    @Body() verifySmsCodeDto: VerifySmsCodeDto,
  ): Promise<{ valid: boolean }> {
    const { phone, code, type } = verifySmsCodeDto;
    const valid = await this.captchaService.verifySmsCode(phone, code, type);
    if (valid) {
      return { valid };
    } else {
      throw new ApiException(20001);
    }
  }

  @Post('email/send')
  @ApiOperation({ summary: '发送邮箱验证码（增强版）' })
  @ApiBody({ type: SendEmailCodeDto })
  async sendEmailCode(
    @Body() sendEmailCodeDto: SendEmailCodeDto,
    @Req() req: any,
  ): Promise<any> {
    const { email, type, fingerprint, behavior } = sendEmailCodeDto;
    const ip = req.ip || req.connection.remoteAddress || '127.0.0.1';
    return await this.captchaService.sendEmailCode(
      email,
      type,
      ip,
      fingerprint,
      behavior,
    );
  }

  @Post('email/verify')
  @ApiOperation({ summary: '验证邮箱验证码（增强版）' })
  @ApiBody({ type: VerifyEmailCodeDto })
  async verifyEmailCode(
    @Body() verifyEmailCodeDto: VerifyEmailCodeDto,
  ): Promise<Record<string, any>> {
    const { email, code, type } = verifyEmailCodeDto;
    const result = await this.captchaService.verifyEmailCode(email, code, type);
    if (result.valid) {
      return { valid: result.valid };
    } else {
      throw new BadRequestException(result?.message || '验证失败');
    }
  }

  @Post('recaptcha/verify')
  @Recaptcha()
  @ApiOperation({ summary: '验证Google reCAPTCHA企业版' })
  @ApiBody({ type: VerifyRecaptchaDto })
  async verifyRecaptcha(
    @Body() verifyRecaptchaDto: VerifyRecaptchaDto,
  ): Promise<Record<string, any>> {
    // reCAPTCHA验证由@Recaptcha()装饰器自动处理
    // 如果到达这里，说明验证已经通过
    return {
      success: true,
      action: verifyRecaptchaDto.action,
    };
  }

  @Post('enterprise/verify')
  @ApiOperation({ summary: '企业级多重验证' })
  @ApiBody({ type: EnterpriseCaptchaVerifyDto })
  async verifyEnterprise(
    @Body() enterpriseVerifyDto: EnterpriseCaptchaVerifyDto,
  ): Promise<Record<string, any>> {
    const result = await this.captchaService.verifyEnterpriseCaptcha(
      enterpriseVerifyDto.puzzleCaptcha,
      enterpriseVerifyDto.recaptchaToken,
      enterpriseVerifyDto.action,
    );

    if (result?.success) {
      return { success: result.success };
    } else {
      throw new BadRequestException(result?.message || '验证失败');
    }
  }

  @Post('test-sms')
  @ApiOperation({ summary: '测试短信发送（仅用于开发）' })
  @ApiBody({ 
    schema: {
      type: 'object',
      properties: {
        phone: { type: 'string', description: '手机号码' }
      }
    }
  })
  async testSms(@Body() body: { phone: string }): Promise<{ success: boolean; message: string }> {
    try {
      const result = await this.captchaService.testSendSms(body.phone);
      return result;
    } catch (error) {
      return { 
        success: false, 
        message: error.message || '发送短信失败' 
      };
    }
  }

  @Post('test-silent-register')
  @Public()
  @ApiOperation({ summary: '测试静默注册并创建API密钥（仅用于开发环境）' })
  @ApiBody({ 
    schema: {
      type: 'object',
      properties: {
        phone: { type: 'string', description: '手机号码' }
      }
    }
  })
  async testSilentRegister(@Body() body: { phone: string }): Promise<any> {
    try {
      // 先检查用户是否存在
      const existingUser = await this.userService.findByPhone(body.phone);
      if (existingUser) {
        // 如果用户存在，删除用户以便重新测试
        await this.userService.deleteUser(existingUser.id);
      }
      
      // 执行静默注册
      const user = await this.userService.createUserByPhone({
        phone: body.phone,
        userType: 'individual',
        userStatus: 'active',
        level: 'basic',
        role: 'user'
      });
      
      // 查询用户的API密钥
      const apiKeys = await this.apiKeyService.findAll({ userId: user.id });
      
      return {
        success: true,
        message: '静默注册测试成功',
        user: {
          id: user.id,
          phone: user.phone,
          username: user.username
        },
        apiKeys: apiKeys.data || [],
        hasApiKeys: (apiKeys.data && apiKeys.data.length > 0) ? true : false
      };
    } catch (error) {
      return { 
        success: false, 
        message: error.message || '静默注册测试失败' 
      };
    }
  }
}
