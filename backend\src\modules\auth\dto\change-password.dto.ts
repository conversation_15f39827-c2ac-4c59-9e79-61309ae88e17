import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, Matches, MinLength } from 'class-validator';

export class ChangePasswordDto {
  @IsString()
  @IsNotEmpty({ message: '当前密码不能为空' })
  @ApiProperty({ description: '当前密码' })
  currentPassword: string;
  
  @IsString()
  @IsNotEmpty({ message: '新密码不能为空' })
  @MinLength(8, { message: '新密码至少需要8个字符' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/, {
    message: '密码必须包含至少一个大写字母，一个小写字母和一个数字',
  })
  @ApiProperty({ description: '新密码' })
  newPassword: string;
  
  @IsString()
  @IsNotEmpty({ message: '确认密码不能为空' })
  @ApiProperty({ description: '确认新密码' })
  confirmPassword: string;
} 