#!/usr/bin/env ts-node

import { DataSource } from 'typeorm';
import { createDataSourceConfig } from '../src/database/config/database.config';
import { seedManager } from '../src/database/seeds';
import { Logger } from '@nestjs/common';
import * as readline from 'readline';

/**
 * 数据库初始化脚本
 * 执行数据库迁移和种子数据
 */
class DatabaseInitializer {
  private readonly logger = new Logger('DatabaseInitializer');
  private dataSource: DataSource;
  
  constructor() {
    this.dataSource = new DataSource(createDataSourceConfig());
  }
  
  /**
   * 初始化数据库
   */
  async initialize(): Promise<void> {
    try {
      // 解析命令行参数
      const options = this.parseArguments();
      
      this.logger.log('开始数据库初始化...');
      
      // 连接数据库
      await this.connectDatabase();
      
      // 执行迁移
      if (options.migrate) {
        await this.runMigrations();
      }
      
      // 执行种子数据
      if (options.seed) {
        await this.runSeeds(options);
      }
      
      this.logger.log('数据库初始化完成');
      
    } catch (error) {
      this.logger.error(`数据库初始化失败: ${error.message}`);
      process.exit(1);
    } finally {
      if (this.dataSource.isInitialized) {
        await this.dataSource.destroy();
      }
    }
  }
  
  /**
   * 连接数据库
   */
  private async connectDatabase(): Promise<void> {
    this.logger.log('连接数据库...');
    await this.dataSource.initialize();
    this.logger.log('数据库连接成功');
  }
  
  /**
   * 执行数据库迁移
   */
  private async runMigrations(): Promise<void> {
    this.logger.log('执行数据库迁移...');
    
    const pendingMigrations = await this.dataSource.showMigrations();
    
    if (pendingMigrations) {
      this.logger.log(`发现 ${pendingMigrations} 个待执行的迁移`);
      await this.dataSource.runMigrations();
      this.logger.log('数据库迁移完成');
    } else {
      this.logger.log('没有待执行的迁移');
    }
  }
  
  /**
   * 执行种子数据
   */
  private async runSeeds(options: any): Promise<void> {
    this.logger.log('执行种子数据...');
    
    const seedOptions = {
      environment: options.environment,
      force: options.force,
      names: options.seeds,
      verbose: options.verbose,
    };
    
    const results = await seedManager.run(this.dataSource, seedOptions);
    
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    this.logger.log(`种子数据执行完成: 成功 ${successful}, 失败 ${failed}`);
    
    if (failed > 0) {
      const failedResults = results.filter(r => !r.success);
      failedResults.forEach(result => {
        this.logger.error(`种子数据执行失败: ${result.message}`);
      });
    }
  }
  
  /**
   * 解析命令行参数
   */
  private parseArguments(): any {
    const args = process.argv.slice(2);
    const options = {
      migrate: true,
      seed: true,
      environment: process.env.NODE_ENV || 'development',
      force: false,
      verbose: false,
      seeds: [] as string[],
    };
    
    for (let i = 0; i < args.length; i++) {
      const arg = args[i];
      
      switch (arg) {
        case '--no-migrate':
          options.migrate = false;
          break;
        case '--no-seed':
          options.seed = false;
          break;
        case '--env':
        case '--environment':
          options.environment = args[++i];
          break;
        case '--force':
          options.force = true;
          break;
        case '--verbose':
          options.verbose = true;
          break;
        case '--seeds':
          options.seeds = args[++i]?.split(',') || [];
          break;
        case '--help':
          this.showHelp();
          process.exit(0);
          break;
      }
    }
    
    return options;
  }
  
  /**
   * 显示帮助信息
   */
  private showHelp(): void {
    console.log(`
数据库初始化工具

用法: npm run db:init [选项]

选项:
  --no-migrate          跳过数据库迁移
  --no-seed            跳过种子数据
  --env <environment>   指定环境 (development|test|production)
  --force              强制执行种子数据
  --verbose            显示详细日志
  --seeds <names>      指定要执行的种子数据 (逗号分隔)
  --help               显示帮助信息

示例:
  npm run db:init                           # 完整初始化
  npm run db:init -- --no-migrate          # 只执行种子数据
  npm run db:init -- --env production      # 生产环境初始化
  npm run db:init -- --seeds admin-user    # 只执行管理员用户种子数据
    `);
  }
  
  /**
   * 确认操作（生产环境）
   */
  private async confirmProduction(): Promise<boolean> {
    if (process.env.NODE_ENV !== 'production') {
      return true;
    }
    
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
    
    return new Promise((resolve) => {
      rl.question('您正在生产环境中执行数据库初始化，是否继续？(y/N): ', (answer) => {
        rl.close();
        resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
      });
    });
  }
}

/**
 * 主函数
 */
async function main() {
  const initializer = new DatabaseInitializer();
  await initializer.initialize();
}

// 执行初始化
if (require.main === module) {
  main().catch(console.error);
}
