import { Injectable, Logger, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { DataSource, QueryRunner } from 'typeorm';
import { PaymentMethod } from '../../order/enums/order.enum';
import { UserService } from '../../user/user.service';
import {
  IPaymentProvider,
  PaymentRequest,
  PaymentResult,
  CallbackVerifyResult,
  PaymentQueryResult,
  RefundRequest,
  RefundResult,
} from '../interfaces/payment-provider.interface';

@Injectable()
export class BalancePayService implements IPaymentProvider {
  private readonly logger = new Logger(BalancePayService.name);

  constructor(
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * 获取支付方式
   */
  getPaymentMethod(): PaymentMethod {
    return PaymentMethod.BALANCE;
  }

  /**
   * 创建余额支付
   */
  async createPayment(request: PaymentRequest): Promise<PaymentResult> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log(`创建余额支付: ${request.paymentNo}, 用户ID: ${request.userId}, 金额: ${request.amount}`);

      // 获取用户信息
      const user = await this.userService.findById(request.userId);
      if (!user) {
        throw new BadRequestException('用户不存在');
      }

      // 检查余额是否充足
      const userBalance = user.balance || 0;
      if (userBalance < request.amount) {
        throw new BadRequestException(`余额不足，当前余额: ${userBalance}元，需要支付: ${request.amount}元`);
      }

      // 扣减用户余额
      await queryRunner.manager.query(
        'UPDATE users SET balance = balance - ? WHERE id = ? AND balance >= ?',
        [request.amount, request.userId, request.amount]
      );

      // 检查扣减是否成功
      const affectedRows = queryRunner.manager.queryRunner?.data?.affectedRows;
      if (affectedRows === 0) {
        throw new BadRequestException('余额扣减失败，可能余额不足');
      }

      await queryRunner.commitTransaction();

      this.logger.log(`余额支付成功: ${request.paymentNo}, 用户ID: ${request.userId}, 扣减金额: ${request.amount}`);

      return {
        success: true,
        thirdPartyNo: `BALANCE_${request.paymentNo}_${Date.now()}`,
        expiresAt: new Date(Date.now() + 5 * 60 * 1000), // 5分钟后过期（余额支付通常立即完成）
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`余额支付失败: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message,
      };
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 验证余额支付回调（余额支付无需回调，直接返回成功）
   */
  async verifyCallback(callbackData: Record<string, any>): Promise<CallbackVerifyResult> {
    try {
      this.logger.log(`验证余额支付回调: ${JSON.stringify(callbackData)}`);

      // 余额支付是同步的，不需要回调验证
      // 这里主要是为了接口一致性
      return {
        success: true,
        paymentNo: callbackData.paymentNo,
        thirdPartyNo: callbackData.thirdPartyNo,
        status: 'success',
        amount: parseFloat(callbackData.amount),
        paidAt: new Date(),
        rawData: callbackData,
      };
    } catch (error) {
      this.logger.error(`验证余额支付回调失败: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message,
      };
    }
  }

  /**
   * 查询余额支付状态（余额支付通常立即完成）
   */
  async queryPayment(paymentNo: string, thirdPartyNo?: string): Promise<PaymentQueryResult> {
    try {
      this.logger.log(`查询余额支付状态: ${paymentNo}`);

      // 余额支付是同步的，如果创建成功就是支付成功
      // 这里可以根据实际需要查询支付记录表
      return {
        success: true,
        status: 'success',
        thirdPartyNo,
      };
    } catch (error) {
      this.logger.error(`查询余额支付状态失败: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message,
      };
    }
  }

  /**
   * 余额支付退款
   */
  async refund(request: RefundRequest): Promise<RefundResult> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log(`申请余额退款: ${request.refundNo}, 金额: ${request.refundAmount}`);

      // 从第三方支付单号中提取用户ID
      // 格式: BALANCE_{paymentNo}_{timestamp}
      const thirdPartyParts = request.thirdPartyNo.split('_');
      if (thirdPartyParts.length < 2 || thirdPartyParts[0] !== 'BALANCE') {
        throw new BadRequestException('无效的余额支付单号');
      }

      // 这里需要从支付记录中获取用户ID，简化处理
      // 实际应该从 payments 表中查询
      const userId = 1; // 临时处理，实际需要查询

      // 增加用户余额
      await queryRunner.manager.query(
        'UPDATE users SET balance = balance + ? WHERE id = ?',
        [request.refundAmount, userId]
      );

      await queryRunner.commitTransaction();

      this.logger.log(`余额退款成功: ${request.refundNo}, 用户ID: ${userId}, 退款金额: ${request.refundAmount}`);

      return {
        success: true,
        refundNo: request.refundNo,
        thirdPartyRefundNo: `REFUND_${request.refundNo}_${Date.now()}`,
        refundAmount: request.refundAmount,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`余额退款失败: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message,
      };
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 查询余额退款状态
   */
  async queryRefund(refundNo: string, thirdPartyRefundNo?: string): Promise<RefundResult> {
    try {
      this.logger.log(`查询余额退款状态: ${refundNo}`);

      // 余额退款是同步的，如果创建成功就是退款成功
      return {
        success: true,
        refundNo,
        thirdPartyRefundNo,
      };
    } catch (error) {
      this.logger.error(`查询余额退款状态失败: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message,
      };
    }
  }

  /**
   * 检查用户余额是否充足
   */
  async checkBalance(userId: number, amount: number): Promise<boolean> {
    try {
      const user = await this.userService.findById(userId);
      if (!user) {
        return false;
      }

      const userBalance = user.balance || 0;
      return userBalance >= amount;
    } catch (error) {
      this.logger.error(`检查用户余额失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 获取用户余额
   */
  async getUserBalance(userId: number): Promise<number> {
    try {
      const user = await this.userService.findById(userId);
      return user?.balance || 0;
    } catch (error) {
      this.logger.error(`获取用户余额失败: ${error.message}`, error.stack);
      return 0;
    }
  }
}
