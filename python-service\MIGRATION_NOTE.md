# 项目整合说明

本文档记录了将paddleocr目录下的OCR功能整合到主项目的过程和相关更改。

## 完成的更改

### 1. 代码整合

将paddleocr/app.py中的功能合并到主app.py中：
- 添加了PaddleOCR相关导入
- 初始化PaddleOCR实例
- 添加了`/predict/ocr_system`路由和实现
- 统一了端口为8866

### 2. 依赖整合

更新了requirements.txt：
- 添加了PaddleOCR及其相关依赖
- 调整了Flask版本为2.2.3，以确保与PaddleOCR兼容
- 指定了Werkzeug版本为2.2.3，以与Flask版本兼容
- 添加了opencv-python库

### 3. Dockerfile更新

- 添加了PaddleOCR所需的系统依赖
- 更新了暴露端口和健康检查端口为8866

### 4. 文档更新

- 更新了README.md，添加了关于OCR功能的说明
- 添加了OCR API的使用示例

### 5. 清理工具

- 创建了cleanup.bat脚本，可以帮助用户在功能确认正常后删除paddleocr目录

### 6. OCR图像优化

添加了OCR图像处理优化功能：
- 图像尺寸自动压缩（提高处理速度）
- 质量控制（减小传输体积）
- 图像增强处理（提高OCR识别率）
- 通过API参数可灵活控制以上优化选项

## 使用说明

### API 端点

所有功能现在均通过端口8866提供：

1. 地址提取与坐标逆解析服务：
   - `/extract`
   - `/extract-address`
   - `/rev-geo`
   - `/b-rev-geo`
   - `/health`

2. OCR服务：
   - `/predict/ocr_system`：支持图像优化参数
     - `max_size`：控制图像最大尺寸
     - `quality`：控制JPEG压缩质量
     - `enhance`：是否启用图像增强

### 部署流程

项目现已统一，可以通过以下方式启动：

```bash
# 直接启动
python app.py

# 或使用Docker
docker build -t address-ocr-service .
docker run -p 8866:8866 address-ocr-service
```

## 清理

功能确认正常后，运行cleanup.bat可以删除不再需要的paddleocr目录。 


现在，我们还需要创建一个脚本来管理pip包的缓存。让我创建一个新的脚本：

```bash
#!/bin/bash

# 创建缓存目录
CACHE_DIR="./pip_cache"
mkdir -p "$CACHE_DIR"

# 下载依赖包到缓存目录
echo "正在下载Python依赖包到本地缓存..."
pip download -r requirements.txt -d "$CACHE_DIR"

echo "依赖包下载完成，保存在 $CACHE_DIR 目录下"
echo "现在可以使用 docker compose build 来构建服务了"
```
让我们给脚本添加执行权限：

chmod +x python-service/download_packages.sh

使用方法：

第一次构建前，先下载依赖包到本地缓存：

cd python-service
chmod +x download_packages.sh
./download_packages.sh

之后的构建都会使用本地缓存：

docker compose build --no-cache python-service
docker compose up -d python-service

这样做的好处是：
第一次下载的包会被缓存在本地的pip_cache目录
后续构建时直接从本地缓存安装，不需要重新下载
大大减少构建时间和网络资源消耗
如果需要更新依赖，只需要重新运行download_packages.sh