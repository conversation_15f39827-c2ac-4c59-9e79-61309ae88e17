### 业务流程图

```
graph TD
    subgraph "用户请求"
        A[用户API请求] --> B{UnifiedAuthGuard<br/>全局守卫}
        B -->|认证通过| C[UnifiedGatewayController]
        B -->|认证失败| D[返回401错误]
    end
    
    subgraph "网关层"
        C --> E[ImprovedGatewayProxyService]
        E --> F{ProcessingModeDecider<br/>处理模式决策}
        F -->|同步处理| G[handleSyncRequest]
        F -->|异步处理| H[handleAsyncRequest]
    end
    
    subgraph "业务层"
        G --> I[业务服务<br/>OcrService/AddressService]
        I --> J[执行层服务]
        H --> K[EnhancedQueueManager]
    end
    
    subgraph "执行层"
        J --> J1{执行器类型}
        J1 -->|OCR| J2[OcrExecutorService]
        J1 -->|地址| J3[AddressExecutorService]
        J2 --> L[具体业务执行]
        J3 --> L
        L --> M[返回结果给用户]
    end
    
    subgraph "队列层"
        K --> N[添加任务到队列]
        N --> O[返回任务ID给用户]
    end
    
    subgraph "处理器层"
        P[Bull队列] --> Q[处理器]
        Q --> Q1{处理器类型?}
        Q1 -->|OCR| S[OcrProcessor]
        Q1 -->|地址提取| T[AddressExtractionProcessor]
        Q1 -->|坐标逆解析| U[ReverseGeocodingProcessor]
    end
    
    subgraph "执行层(异步)"
        S --> V[OcrExecutorService]
        T --> W[AddressExecutorService]
        U --> W
        V --> Y[ApiUsageTrackerService<br/>更新API使用记录]
        W --> Y
    end
    
    N -.-> P
    O -.-> Z[用户查询任务状态]
    Z --> AA[getTaskStatus]
    AA --> BB[返回任务结果]
```

### 优化后的架构说明

1. **新增执行层**：
   - 将具体业务执行逻辑从业务层和处理器层中分离出来
   - 解决循环依赖问题
   - 提高代码复用性和可测试性
   - 主要执行器服务：
     - OcrExecutorService：负责OCR识别的具体执行
     - AddressExecutorService：负责地址提取和地理编码的具体执行

2. **队列管理优化**：
   - 使用EnhancedQueueManagerService替代旧的QueueManagerService
   - 提供统一的队列操作接口
   - 实现IQueueManager接口，确保功能一致性

3. **处理器层改进**：
   - 处理器不再直接依赖业务服务，而是依赖执行器服务
   - 使用ModuleRef动态获取服务，避免循环依赖
   - 提供模拟服务实现，增强容错性
   - 主要处理器：
     - OcrProcessor：处理OCR识别任务
     - AddressExtractionProcessor：处理地址提取任务
     - ReverseGeocodingProcessor：处理地理坐标逆解析任务

4. **数据流优化**：
   - 同步流程：用户请求 → 网关层 → 业务层 → 执行层 → 返回结果
   - 异步流程：用户请求 → 网关层 → 队列层 → 处理器层 → 执行层 → 更新状态

5. **错误处理和容错性**：
   - 每一层都有独立的错误处理逻辑
   - 处理器层提供降级服务，即使依赖不可用也能正常工作
   - 执行层统一处理业务逻辑错误，确保一致的错误响应

6. **模块间通信**：
   - 使用forwardRef解决循环依赖问题
   - 业务层和执行层之间是直接调用关系
   - 处理器层和执行层之间是动态获取关系
   - 队列层和处理器层之间是事件驱动关系