# 开放平台部署指南

本文档提供在Ubuntu服务器上部署开放平台全栈应用的详细步骤。


## 1. 清理Docker环境

在开始部署前，我们需要清理Docker环境，确保没有旧的容器、镜像和卷影响新的部署：

```bash
# 停止所有运行中的容器
docker stop $(docker ps -aq)

# 删除所有容器
docker rm $(docker ps -aq)

# 删除所有镜像
docker rmi $(docker images -q)

# 删除所有卷
docker volume rm $(docker volume ls -q)

# 删除所有网络
docker network prune -f

# 清理未使用的数据（包括构建缓存）
docker system prune -a --volumes -f
```


### 2.4 构建并启动Python服务

使用docker-compose命令来构建和启动Python服务：

```bash
# 确保在项目根目录
cd /home/<USER>/open-platform

# 仅构建并启动Python服务和它的依赖（Redis）
docker-compose up -d --build redis python-service


DOCKER_REGISTRY_MIRROR=https://la7b45hm.mirror.aliyuncs.com docker-compose up -d --build redis python-service

# 查看容器状态
docker-compose ps

# 查看Python服务日志
docker-compose logs -f python-service
```

### 2.5 测试Python服务

Python服务启动后，可以通过以下命令测试服务是否正常运行：

```bash
# 测试健康检查端点
curl http://localhost:8866/health

# 测试地址提取功能
curl -X POST http://localhost:8866/extract-address \
  -H "Content-Type: application/json" \
  -d '{"text": "北京市海淀区清华大学"}'
```


### 4.4 测试Vue前端服务

Vue前端服务启动后，可以通过浏览器访问以下地址测试服务是否正常运行：

```
http://服务器IP:3000
```

### 线上阿里云服务器系统信息：
```
root@iZ8vbj22m6wpl9t5ii12f6Z:~# lsb_release -a
No LSB modules are available.
Distributor ID: Ubuntu
Description:    Ubuntu 24.04.2 LTS
Release:        24.04
Codename:       noble
root@iZ8vbj22m6wpl9t5ii12f6Z:~# ^C
root@iZ8vbj22m6wpl9t5ii12f6Z:~# cat /etc/os-release
PRETTY_NAME="Ubuntu 24.04.2 LTS"
NAME="Ubuntu"
VERSION_ID="24.04"
VERSION="24.04.2 LTS (Noble Numbat)"
VERSION_CODENAME=noble
ID=ubuntu
ID_LIKE=debian
HOME_URL="https://www.ubuntu.com/"
SUPPORT_URL="https://help.ubuntu.com/"
BUG_REPORT_URL="https://bugs.launchpad.net/ubuntu/"
PRIVACY_POLICY_URL="https://www.ubuntu.com/legal/terms-and-policies/privacy-policy"
UBUNTU_CODENAME=noble
LOGO=ubuntu-logo
root@iZ8vbj22m6wpl9t5ii12f6Z:~# arch
x86_64
root@iZ8vbj22m6wpl9t5ii12f6Z:~# lscpu
Architecture:             x86_64
  CPU op-mode(s):         32-bit, 64-bit
  Address sizes:          46 bits physical, 57 bits virtual
  Byte Order:             Little Endian
CPU(s):                   4
  On-line CPU(s) list:    0-3
Vendor ID:                GenuineIntel
  BIOS Vendor ID:         Alibaba Cloud
  Model name:             Intel(R) Xeon(R) Platinum 8369B CPU @ 2.70GHz
    BIOS Model name:      pc-i440fx-2.1  CPU @ 0.0GHz
    BIOS CPU family:      1
    CPU family:           6
    Model:                106
    Thread(s) per core:   2
    Core(s) per socket:   2
    Socket(s):            1
    Stepping:             6
    BogoMIPS:             5399.99
    Flags:                fpu vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx 
                          fxsr sse sse2 ss ht syscall nx pdpe1gb rdtscp lm constant_tsc rep_good nopl nonstop_
                          tsc cpuid aperfmperf tsc_known_freq pni pclmulqdq monitor ssse3 fma cx16 pcid sse4_1
                           sse4_2 x2apic movbe popcnt aes xsave avx f16c rdrand hypervisor lahf_lm abm 3dnowpr
                          efetch cpuid_fault ibrs_enhanced fsgsbase tsc_adjust bmi1 avx2 smep bmi2 erms invpci
                          d avx512f avx512dq rdseed adx smap avx512ifma clflushopt clwb avx512cd sha_ni avx512
                          bw avx512vl xsaveopt xsavec xgetbv1 xsaves wbnoinvd arat avx512vbmi pku ospke avx512
                          _vbmi2 gfni vaes vpclmulqdq avx512_vnni avx512_bitalg avx512_vpopcntdq rdpid fsrm ar
                          ch_capabilities
Virtualization features:  
  Hypervisor vendor:      KVM
  Virtualization type:    full
Caches (sum of all):      
```


## Vue 3前端项目部署指南

- 1.1 docker-compose.frontend.yml 在项目根目录创建此文件

```bash

version: '3.8'

services:
  vue-frontend:
    build:
      context: ./vue3-open
    container_name: open-platform-frontend
    restart: always
    ports:
      - "3000:80"
    networks:
      - open-platform-network

networks:
  open-platform-network:
    driver: bridge
```

- 1.2 aiszyl.cn.nginx.conf 

> 为主域名创建Nginx配置文件:
```bash
server {
    listen 80;
    server_name aiszyl.cn www.aiszyl.cn;

    # 访问日志
    access_log /var/log/nginx/aiszyl.cn.access.log;
    error_log /var/log/nginx/aiszyl.cn.error.log;

    # 将所有请求转发到 HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name aiszyl.cn www.aiszyl.cn;
    
    # SSL证书配置 - 使用Let's Encrypt证书
    ssl_certificate /etc/letsencrypt/live/aiszyl.cn/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/aiszyl.cn/privkey.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;

    # HSTS (ngx_http_headers_module is required) (63072000 seconds = 2 years)
    add_header Strict-Transport-Security "max-age=63072000" always;
    
    # 访问日志
    access_log /var/log/nginx/aiszyl.cn.access.log;
    error_log /var/log/nginx/aiszyl.cn.error.log;

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        proxy_pass http://localhost:3000;
        expires 30d;
        add_header Cache-Control "public, no-transform";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 所有其他请求
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
    }

    # 安全相关头信息
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";
}
```
### 2. 部署步骤

- 2.1 将代码复制到服务器

```bash
# 登录服务器
ssh user@your_server_ip

# 创建工作目录
mkdir -p /home/<USER>/open-platform
cd /home/<USER>/open-platform

# 上传代码到服务器(在本地执行)
# scp -r /path/to/your/project/* user@your_server_ip:/home/<USER>/open-platform/
```

- 2.2 安装Docker和Docker Compose

```bash
# 更新包索引
sudo apt update

# 安装必要的包
sudo apt install -y apt-transport-https ca-certificates curl software-properties-common

# 添加Docker官方GPG密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -

# 添加Docker存储库
sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"

# 安装Docker
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io

# 安装Docker Compose
sudo apt install -y docker-compose-plugin

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 将当前用户添加到docker组(无需sudo执行docker命令)
sudo usermod -aG docker $USER
newgrp docker
```

- 2.3 配置Nginx

```bash

# 安装Nginx
sudo apt update
sudo apt install -y nginx

# 复制Nginx配置文件
sudo cp aiszyl.cn.nginx.conf /etc/nginx/sites-available/aiszyl.cn.conf

# 创建符号链接
sudo ln -s /etc/nginx/sites-available/aiszyl.cn.conf /etc/nginx/sites-enabled/

# 测试Nginx配置
sudo nginx -t

# 如果配置正确，重新加载Nginx
sudo systemctl reload nginx
```

- 2.4 获取SSL证书(HTTPS)

```bash
# 安装Certbot
sudo apt update
sudo apt install -y certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d aiszyl.cn -d www.aiszyl.cn

# 自动续期证书
sudo certbot renew --dry-run
```
- 2.5 构建并启动前端容器
```bash
# 确保在项目根目录
cd /home/<USER>/open-platform

# 构建并启动前端容器
docker-compose -f docker-compose.frontend.yml up -d --build
```

- 2.6 配置防火墙

```bash
# 安装UFW(如果尚未安装)
sudo apt install -y ufw

# 允许SSH连接
sudo ufw allow ssh

# 允许HTTP和HTTPS连接
sudo ufw allow 80
sudo ufw allow 443

# 如果需要直接访问前端容器端口(通常不需要)
sudo ufw allow 3000

# 启用防火墙
sudo ufw enable

# 检查状态
sudo ufw status
```
- 2.7 验证部署

```bash
# 检查容器状态
docker ps

# 查看容器日志
docker logs open-platform-frontend

# 测试网站访问
curl -I http://localhost:3000
curl -I http://aiszyl.cn
curl -I https://aiszyl.cn
```

- 3. 配置文件说明

- 3.1 Vue前端Dockerfile审查

```bash
现有的Dockerfile看起来没有问题，它:
使用Node.js 18.18.0作为构建环境
使用pnpm安装依赖并构建应用
使用Nginx提供静态文件服务
暴露80端口(不是3000)
包含健康检查

```

### 5. 维护与更新

```bash
# 拉取最新代码
git pull

# 重新构建并启动容器
docker-compose -f docker-compose.frontend.yml up -d --build

# 查看所有运行中的容器
docker ps

# 查看特定容器的状态
docker ps -f name=open-platform-frontend

# 检查容器日志
docker logs open-platform-frontend
```

- 5.2 查看日志

```bash
# 查看Docker容器日志
docker logs -f open-platform-frontend

# 查看Nginx访问日志
sudo tail -f /var/log/nginx/aiszyl.cn.access.log

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/aiszyl.cn.error.log

# 使用正确的docker-compose文件
# 停止并删除容器：
docker compose -f docker-compose.frontend.yml down

# 删除相关镜像：
docker rmi open-backend-frontend_vue-frontend

# 清理构建缓存：
docker builder prune -f

docker compose -f docker-compose.frontend.yml up -d --build

# 重新构建并启动（不使用缓存）：
docker compose -f docker-compose.frontend.yml up -d --build --no-cache

docker compose -f docker-compose.frontend.yml build --no-cache
docker compose -f docker-compose.frontend.yml up -d

# 查看Certbot修改后的配置
sudo cat /etc/nginx/sites-available/aiszyl.cn.temp.conf

# 如果需要，将这个配置复制回原始文件
sudo cp /etc/nginx/sites-available/aiszyl.cn.temp.conf /etc/nginx/sites-available/aiszyl.cn.conf

# 更新符号链接
sudo rm /etc/nginx/sites-enabled/aiszyl.cn.temp.conf
sudo ln -s /etc/nginx/sites-available/aiszyl.cn.conf /etc/nginx/sites-enabled/

```

##### 使用tmux创建一个持久会话，防止SSH断开导致命令中断：
```bash
# 安装tmux（如果尚未安装）
apt-get update && apt-get install -y tmux

# 使用tmux创建会话以防止SSH断开导致构建中断
tmux new -s build
tmux new -s rebuild_frontend

# 在tmux会话中，导航到项目目录并重新开始构建：
cd /home/<USER>/open-backend-frontend
docker compose -f docker-compose.frontend.yml down
docker compose -f docker-compose.frontend.yml build --no-cache
docker compose -f docker-compose.frontend.yml up -d

- 如果需要暂时离开tmux会话而不中断命令执行，可以按Ctrl+B然后按D来分离会话。
- 稍后可以通过以下命令重新连接到会话：

# 稍后可以通过以下命令重新连接到会话：
tmux attach -t build
tmux attach -t rebuild_frontend

- 构建完成后，验证容器是否正常运行：


退出tmux的方法

# 方法1：按快捷键退出
Ctrl + b, 然后按 d

# 方法2：在tmux内执行命令退出
exit

# 方法3：强制退出当前会话
tmux kill-session

docker ps | grep open-platform-frontend

```

- 5.3 备份配置文件

```bash
# 备份Nginx配置
sudo cp /etc/nginx/sites-available/aiszyl.cn.conf /backup/

# 备份SSL证书
sudo cp -r /etc/letsencrypt/live/aiszyl.cn /backup/
```