import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, QueryRunner, Between } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { OrderEntity } from './entities/order.entity';
import { OrderItemEntity } from './entities/order-item.entity';
import { UserService } from '../user/user.service';
import { ServiceService } from '../service/service.service';
import { UserServiceService } from '../user-service/user-service.service';
import {
  CreateServiceOrderDto,
  QueryOrderDto,
  UpdateOrderDto,
  OrderResponseDto,
  OrderListResponseDto,
  OrderStatsDto,
} from './dto/order.dto';
import { OrderStatus, OrderType, PaymentMethod } from './enums/order.enum';

@Injectable()
export class OrderService {
  private readonly logger = new Logger(OrderService.name);

  constructor(
    @InjectRepository(OrderEntity)
    private readonly orderRepository: Repository<OrderEntity>,
    @InjectRepository(OrderItemEntity)
    private readonly orderItemRepository: Repository<OrderItemEntity>,
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    private readonly serviceService: ServiceService,
    @Inject(forwardRef(() => UserServiceService))
    private readonly userServiceService: UserServiceService,
    private readonly dataSource: DataSource,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * 创建服务订单
   */
  async createServiceOrder(
    userId: number,
    createOrderDto: CreateServiceOrderDto,
  ): Promise<OrderResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 验证用户
      const user = await this.userService.findById(userId);
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      // 计算订单总金额
      let totalAmount = 0;
      const orderItems: Partial<OrderItemEntity>[] = [];

      for (const itemDto of createOrderDto.orderItems) {
        // 如果有服务ID，验证服务存在
        if (itemDto.serviceId) {
          const service = await this.serviceService.findOne(itemDto.serviceId);
          if (!service) {
            throw new NotFoundException(`服务 ${itemDto.serviceId} 不存在`);
          }
        }

        const itemTotalPrice = Number((itemDto.unitPrice * itemDto.quantity).toFixed(2));
        totalAmount += itemTotalPrice;

        orderItems.push({
          serviceId: itemDto.serviceId,
          itemName: itemDto.itemName,
          itemDescription: itemDto.itemDescription,
          unitPrice: itemDto.unitPrice,
          quantity: itemDto.quantity,
          totalPrice: itemTotalPrice,
          itemConfig: itemDto.itemConfig,
        });
      }

      // 应用优惠
      const discountAmount = createOrderDto.discountAmount || 0;
      totalAmount = Math.max(0, totalAmount - discountAmount);

      // 创建订单
      const order = queryRunner.manager.create(OrderEntity, {
        userId,
        orderType: OrderType.SERVICE,
        status: OrderStatus.PENDING,
        totalAmount,
        paidAmount: 0,
        discountAmount,
        remark: createOrderDto.remark,
        expiresAt: new Date(Date.now() + 30 * 60 * 1000), // 30分钟后过期
      });

      const savedOrder = await queryRunner.manager.save(order);

      // 创建订单项
      for (const itemData of orderItems) {
        const orderItem = queryRunner.manager.create(OrderItemEntity, {
          ...itemData,
          orderId: savedOrder.id,
        });
        await queryRunner.manager.save(orderItem);
      }

      await queryRunner.commitTransaction();

      this.logger.log(`服务订单创建成功: ${savedOrder.orderNo}, 用户ID: ${userId}`);

      // 发送订单创建事件
      this.eventEmitter.emit('order.created', {
        orderId: savedOrder.id,
        userId,
        orderType: OrderType.SERVICE,
        totalAmount,
      });

      return this.findById(savedOrder.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`创建服务订单失败: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 根据ID查找订单
   */
  async findById(id: number): Promise<OrderResponseDto> {
    const order = await this.orderRepository.findOne({
      where: { id },
      relations: ['orderItems'],
    });

    if (!order) {
      throw new NotFoundException('订单不存在');
    }

    return this.toOrderResponseDto(order);
  }

  /**
   * 根据订单号查找订单
   */
  async findByOrderNo(orderNo: string): Promise<OrderResponseDto> {
    const order = await this.orderRepository.findOne({
      where: { orderNo },
      relations: ['orderItems'],
    });

    if (!order) {
      throw new NotFoundException('订单不存在');
    }

    return this.toOrderResponseDto(order);
  }

  /**
   * 查询订单列表
   */
  async findAll(queryDto: QueryOrderDto): Promise<OrderListResponseDto> {
    const { page = 1, limit = 20, ...filters } = queryDto;
    const skip = (page - 1) * limit;

    const queryBuilder = this.orderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.orderItems', 'orderItems')
      .orderBy('order.createdAt', 'DESC');

    // 应用过滤条件
    if (filters.userId) {
      queryBuilder.andWhere('order.userId = :userId', { userId: filters.userId });
    }

    if (filters.orderNo) {
      queryBuilder.andWhere('order.orderNo LIKE :orderNo', { orderNo: `%${filters.orderNo}%` });
    }

    if (filters.orderType) {
      queryBuilder.andWhere('order.orderType = :orderType', { orderType: filters.orderType });
    }

    if (filters.status) {
      queryBuilder.andWhere('order.status = :status', { status: filters.status });
    }

    if (filters.paymentMethod) {
      queryBuilder.andWhere('order.paymentMethod = :paymentMethod', { paymentMethod: filters.paymentMethod });
    }

    if (filters.startTime && filters.endTime) {
      queryBuilder.andWhere('order.createdAt BETWEEN :startTime AND :endTime', {
        startTime: filters.startTime,
        endTime: filters.endTime,
      });
    }

    const [orders, totalItems] = await queryBuilder
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    const items = orders.map(order => this.toOrderResponseDto(order));

    return {
      items,
      meta: {
        page,
        limit,
        totalItems,
        totalPages: Math.ceil(totalItems / limit),
      },
    };
  }

  /**
   * 更新订单
   */
  async update(id: number, updateDto: UpdateOrderDto): Promise<OrderResponseDto> {
    const order = await this.orderRepository.findOne({ where: { id } });
    if (!order) {
      throw new NotFoundException('订单不存在');
    }

    // 更新订单信息
    Object.assign(order, updateDto);
    await this.orderRepository.save(order);

    this.logger.log(`订单更新成功: ${order.orderNo}`);

    // 发送订单更新事件
    this.eventEmitter.emit('order.updated', {
      orderId: id,
      changes: updateDto,
    });

    return this.findById(id);
  }

  /**
   * 取消订单
   */
  async cancelOrder(id: number, userId?: number): Promise<OrderResponseDto> {
    const order = await this.orderRepository.findOne({ where: { id } });
    if (!order) {
      throw new NotFoundException('订单不存在');
    }

    // 验证用户权限
    if (userId && order.userId !== userId) {
      throw new ForbiddenException('无权操作此订单');
    }

    // 检查订单是否可以取消
    if (!order.canCancel()) {
      throw new BadRequestException('订单状态不允许取消');
    }

    order.status = OrderStatus.CANCELLED;
    await this.orderRepository.save(order);

    this.logger.log(`订单取消成功: ${order.orderNo}`);

    // 发送订单取消事件
    this.eventEmitter.emit('order.cancelled', {
      orderId: id,
      userId: order.userId,
      orderType: order.orderType,
    });

    return this.toOrderResponseDto(order);
  }

  /**
   * 完成订单
   */
  async completeOrder(id: number): Promise<OrderResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const order = await queryRunner.manager.findOne(OrderEntity, {
        where: { id },
        relations: ['orderItems'],
      });

      if (!order) {
        throw new NotFoundException('订单不存在');
      }

      if (order.status !== OrderStatus.PAID) {
        throw new BadRequestException('只有已支付的订单才能完成');
      }

      // 如果是服务订单，增加用户服务次数
      if (order.orderType === OrderType.SERVICE) {
        for (const item of order.orderItems) {
          if (item.serviceId) {
            await this.userServiceService.addServiceCount(
              order.userId,
              item.serviceId,
              item.quantity,
              '订单购买'
            );
          }
        }
      }

      // 更新订单状态
      order.status = OrderStatus.COMPLETED;
      await queryRunner.manager.save(order);

      await queryRunner.commitTransaction();

      this.logger.log(`订单完成: ${order.orderNo}`);

      // 发送订单完成事件
      this.eventEmitter.emit('order.completed', {
        orderId: id,
        userId: order.userId,
        orderType: order.orderType,
        totalAmount: order.totalAmount,
      });

      return this.toOrderResponseDto(order);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`完成订单失败: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 转换为响应DTO
   */
  public toOrderResponseDto(order: OrderEntity): OrderResponseDto {
    return {
      id: order.id,
      userId: order.userId,
      orderNo: order.orderNo,
      orderType: order.orderType,
      status: order.status,
      totalAmount: order.totalAmount,
      paidAmount: order.paidAmount,
      discountAmount: order.discountAmount,
      paymentMethod: order.paymentMethod,
      expiresAt: order.expiresAt,
      remark: order.remark,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      orderItems: order.orderItems?.map(item => ({
        id: item.id,
        orderId: item.orderId,
        serviceId: item.serviceId,
        itemName: item.itemName,
        itemDescription: item.itemDescription,
        unitPrice: item.unitPrice,
        quantity: item.quantity,
        totalPrice: item.totalPrice,
        itemConfig: item.itemConfig,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      })) || [],
      isExpired: order.isExpired(),
      canPay: order.canPay(),
      canCancel: order.canCancel(),
      canRefund: order.canRefund(),
    };
  }

  /**
   * 获取订单统计
   */
  async getStats(): Promise<OrderStatsDto> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // 基础统计
    const [
      totalOrders,
      pendingOrders,
      paidOrders,
      completedOrders,
      cancelledOrders,
      totalAmountResult,
      todayOrdersResult,
      todayAmountResult,
    ] = await Promise.all([
      this.orderRepository.count(),
      this.orderRepository.count({ where: { status: OrderStatus.PENDING } }),
      this.orderRepository.count({ where: { status: OrderStatus.PAID } }),
      this.orderRepository.count({ where: { status: OrderStatus.COMPLETED } }),
      this.orderRepository.count({ where: { status: OrderStatus.CANCELLED } }),
      this.orderRepository
        .createQueryBuilder('order')
        .select('SUM(order.totalAmount)', 'total')
        .where('order.status IN (:...statuses)', { statuses: [OrderStatus.PAID, OrderStatus.COMPLETED] })
        .getRawOne(),
      this.orderRepository.count({
        where: { createdAt: Between(today, tomorrow) },
      }),
      this.orderRepository
        .createQueryBuilder('order')
        .select('SUM(order.totalAmount)', 'total')
        .where('order.createdAt BETWEEN :start AND :end', { start: today, end: tomorrow })
        .andWhere('order.status IN (:...statuses)', { statuses: [OrderStatus.PAID, OrderStatus.COMPLETED] })
        .getRawOne(),
    ]);

    // 按订单类型统计
    const orderTypeStats = await this.orderRepository
      .createQueryBuilder('order')
      .select('order.orderType', 'orderType')
      .addSelect('COUNT(*)', 'count')
      .addSelect('SUM(order.totalAmount)', 'amount')
      .where('order.status IN (:...statuses)', { statuses: [OrderStatus.PAID, OrderStatus.COMPLETED] })
      .groupBy('order.orderType')
      .getRawMany();

    // 按支付方式统计
    const paymentMethodStats = await this.orderRepository
      .createQueryBuilder('order')
      .select('order.paymentMethod', 'paymentMethod')
      .addSelect('COUNT(*)', 'count')
      .addSelect('SUM(order.totalAmount)', 'amount')
      .where('order.status IN (:...statuses)', { statuses: [OrderStatus.PAID, OrderStatus.COMPLETED] })
      .andWhere('order.paymentMethod IS NOT NULL')
      .groupBy('order.paymentMethod')
      .getRawMany();

    const byOrderType = {};
    orderTypeStats.forEach(stat => {
      byOrderType[stat.orderType] = {
        count: parseInt(stat.count),
        amount: parseFloat(stat.amount) || 0,
      };
    });

    const byPaymentMethod = {};
    paymentMethodStats.forEach(stat => {
      byPaymentMethod[stat.paymentMethod] = {
        count: parseInt(stat.count),
        amount: parseFloat(stat.amount) || 0,
      };
    });

    return {
      totalOrders,
      pendingOrders,
      paidOrders,
      completedOrders,
      cancelledOrders,
      totalAmount: parseFloat(totalAmountResult?.total) || 0,
      todayOrders: todayOrdersResult,
      todayAmount: parseFloat(todayAmountResult?.total) || 0,
      byOrderType,
      byPaymentMethod,
    };
  }
}