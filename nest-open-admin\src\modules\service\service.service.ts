import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, Between } from 'typeorm';
import { ServiceEntity } from './entities/service.entity';
import {
  CreateServiceDto,
  UpdateServiceDto,
  QueryServiceDto,
  ServiceResponseDto,
  ServiceListResponseDto,
  ServiceStatus,
  ServiceType,
  PricingModel,
} from './dto/service.dto';
import { BaseCrudService } from '../../common/base/base-crud.service';

@Injectable()
export class ServiceService extends BaseCrudService<
  ServiceEntity,
  CreateServiceDto,
  UpdateServiceDto,
  QueryServiceDto,
  ServiceResponseDto,
  ServiceListResponseDto
> {
  protected readonly repository: Repository<ServiceEntity>;
  protected readonly entityName = 'Service';

  constructor(
    @InjectRepository(ServiceEntity)
    serviceRepository: Repository<ServiceEntity>,
  ) {
    super();
    this.repository = serviceRepository;
  }

  /**
   * 创建前验证
   */
  protected async validateBeforeCreate(createDto: CreateServiceDto): Promise<void> {
    // 检查服务代码是否已存在
    const existingService = await this.repository.findOne({
      where: { code: createDto.code },
    });

    if (existingService) {
      throw new ConflictException('服务代码已存在');
    }
  }

  /**
   * 转换创建DTO
   */
  protected transformCreateDto(createDto: CreateServiceDto): Partial<ServiceEntity> {
    return {
      code: createDto.code,
      name: createDto.name,
      description: createDto.description,
      type: createDto.type,
      serviceStatus: createDto.serviceStatus || ServiceStatus.ACTIVE,
      pricingModel: createDto.pricingModel,
      currentVersion: createDto.currentVersion || 'v1.0.0',
      features: createDto.features || '',
      endpoint: createDto.endpoint,
      config: {
        requestConfig: createDto.requestConfig,
        responseConfig: createDto.responseConfig,
        tags: createDto.tags,
        iconUrl: createDto.iconUrl,
        documentationUrl: createDto.documentationUrl,
      },
      price: createDto.pricePerCall || 0,
      dailyLimit: createDto.dailyLimit || 1000,
      minuteLimit: createDto.monthlyLimit ? Math.floor(createDto.monthlyLimit / 30 / 24 / 60) : 100,
      requireAuth: createDto.requiresAuth ? 1 : 0,
      isAsync: createDto.supportsBatch ? 1 : 0,
      timeout: 30,
      callCount: 0,
      userId: 1, // 临时设置，实际应从认证信息获取
      serviceId: 0, // 临时设置
    };
  }

  /**
   * 更新前验证
   */
  protected async validateBeforeUpdate(
    id: number,
    updateDto: UpdateServiceDto,
    entity: ServiceEntity,
  ): Promise<void> {
    // 如果更新服务代码，检查是否与其他服务冲突
    if (updateDto.code && updateDto.code !== entity.code) {
      const existingService = await this.repository.findOne({
        where: { code: updateDto.code },
      });
      if (existingService) {
        throw new ConflictException('服务代码已存在');
      }
    }
  }

  /**
   * 转换更新DTO
   */
  protected transformUpdateDto(updateDto: UpdateServiceDto, entity: ServiceEntity): Partial<ServiceEntity> {
    const updateData: Partial<ServiceEntity> = {
      code: updateDto.code || entity.code,
      name: updateDto.name || entity.name,
      description: updateDto.description !== undefined ? updateDto.description : entity.description,
      type: updateDto.type || entity.type,
      serviceStatus: updateDto.serviceStatus || entity.serviceStatus,
      pricingModel: updateDto.pricingModel || entity.pricingModel,
      currentVersion: updateDto.currentVersion || entity.currentVersion,
      features: updateDto.features !== undefined ? updateDto.features : entity.features,
      endpoint: updateDto.endpoint !== undefined ? updateDto.endpoint : entity.endpoint,
      price: updateDto.price !== undefined ? updateDto.price : entity.price,
      dailyLimit: updateDto.dailyLimit !== undefined ? updateDto.dailyLimit : entity.dailyLimit,
      requireAuth: updateDto.requiresAuth !== undefined ? (updateDto.requiresAuth ? 1 : 0) : entity.requireAuth,
      isAsync: updateDto.supportsBatch !== undefined ? (updateDto.supportsBatch ? 1 : 0) : entity.isAsync,
    };

    // 更新配置信息
    if (updateDto.requestConfig || updateDto.responseConfig || updateDto.tags || updateDto.iconUrl || updateDto.documentationUrl) {
      updateData.config = {
        ...entity.config,
        requestConfig: updateDto.requestConfig || entity.config?.requestConfig,
        responseConfig: updateDto.responseConfig || entity.config?.responseConfig,
        tags: updateDto.tags || entity.config?.tags,
        iconUrl: updateDto.iconUrl || entity.config?.iconUrl,
        documentationUrl: updateDto.documentationUrl || entity.config?.documentationUrl,
      };
    }

    return updateData;
  }

  /**
   * 删除前验证
   */
  protected async validateBeforeDelete(id: number, entity: ServiceEntity): Promise<void> {
    // 加载关联数据
    const serviceWithRelations = await this.repository.findOne({
      where: { id: entity.id },
      relations: ['apiKeys', 'userServices', 'orders', 'callRecords', 'alertRecords'],
    });

    if (!serviceWithRelations) {
      return;
    }

    // 检查是否有关联数据
    const hasRelatedData = 
      serviceWithRelations.apiKeys?.length > 0 ||
      serviceWithRelations.userServices?.length > 0 ||
      serviceWithRelations.orders?.length > 0 ||
      serviceWithRelations.callRecords?.length > 0 ||
      serviceWithRelations.alertRecords?.length > 0;

    if (hasRelatedData) {
      throw new BadRequestException('该服务存在关联数据，无法删除');
    }
  }

  /**
   * 转换实体为响应DTO
   */
  protected transformToResponseDto(entity: ServiceEntity): ServiceResponseDto {
    return this.formatServiceResponse(entity);
  }

  /**
   * 应用查询条件
   */
  protected applyQueryConditions(
    queryBuilder: any,
    queryDto: QueryServiceDto,
  ): void {
    const {
      code,
      name,
      type,
      serviceStatus,
      pricingModel,
      requiresAuth,
      supportsBatch,
    } = queryDto;

    if (code) {
      queryBuilder.andWhere('entity.code LIKE :code', { code: `%${code}%` });
    }
    if (name) {
      queryBuilder.andWhere('entity.name LIKE :name', { name: `%${name}%` });
    }
    if (type) {
      queryBuilder.andWhere('entity.type = :type', { type });
    }
    if (serviceStatus) {
      queryBuilder.andWhere('entity.serviceStatus = :serviceStatus', { serviceStatus });
    }
    if (pricingModel) {
      queryBuilder.andWhere('entity.pricingModel = :pricingModel', { pricingModel });
    }
    if (requiresAuth !== undefined) {
      queryBuilder.andWhere('entity.requireAuth = :requireAuth', { requireAuth: requiresAuth ? 1 : 0 });
    }
    if (supportsBatch !== undefined) {
      queryBuilder.andWhere('entity.isAsync = :isAsync', { isAsync: supportsBatch ? 1 : 0 });
    }
  }

  /**
   * 转换实体列表为响应DTO
   */
  protected transformToListResponseDto(
    data: ServiceResponseDto[],
    total: number,
    page: number,
    limit: number,
    totalPages: number,
  ): ServiceListResponseDto {
    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }





  /**
   * 根据代码查询服务
   */
  async findByCode(code: string): Promise<ServiceResponseDto> {
    const service = await this.repository.findOne({
      where: { code },
    });

    if (!service) {
      throw new NotFoundException('服务不存在');
    }

    return this.formatServiceResponse(service);
  }

  /**
   * 根据ID查询服务实体（用于内部服务调用）
   */
  async findEntityById(id: number): Promise<ServiceEntity | null> {
    return await this.repository.findOne({
      where: { id },
    });
  }

  /**
   * 根据代码查询服务实体（用于内部服务调用）
   */
  async findEntityByCode(code: string): Promise<ServiceEntity | null> {
    return await this.repository.findOne({
      where: { code },
    });
  }





  /**
   * 批量删除服务
   */
  async batchRemove(ids: number[]): Promise<{ message: string }> {
    for (const id of ids) {
      await this.remove(id);
    }
    return { message: `已成功删除 ${ids.length} 个服务` };
  }

  /**
   * 更新服务状态
   */
  async updateStatus(id: number, status: ServiceStatus): Promise<ServiceResponseDto> {
    const service = await this.repository.findOne({
      where: { id },
    });

    if (!service) {
      throw new NotFoundException('服务不存在');
    }

    service.serviceStatus = status;
    const savedService = await this.repository.save(service);
    return this.formatServiceResponse(savedService);
  }

  /**
   * 获取服务统计信息
   */
  async getStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    maintenance: number;
    deprecated: number;
    totalCalls: number;
    avgPrice: number;
  }> {
    const [total, active, inactive, maintenance, deprecated] = await Promise.all([
      this.repository.count(),
      this.repository.count({ where: { serviceStatus: ServiceStatus.ACTIVE } }),
      this.repository.count({ where: { serviceStatus: ServiceStatus.INACTIVE } }),
      this.repository.count({ where: { serviceStatus: ServiceStatus.MAINTENANCE } }),
      this.repository.count({ where: { serviceStatus: ServiceStatus.DEPRECATED } }),
    ]);

    const callStats = await this.repository
      .createQueryBuilder('service')
      .select('SUM(service.callCount)', 'totalCalls')
      .addSelect('AVG(service.price)', 'avgPrice')
      .getRawOne();

    return {
      total,
      active,
      inactive,
      maintenance,
      deprecated,
      totalCalls: parseInt(callStats.totalCalls) || 0,
      avgPrice: parseFloat(callStats.avgPrice) || 0,
    };
  }

  /**
   * 格式化服务响应数据
   */
  private formatServiceResponse(service: ServiceEntity): ServiceResponseDto {
    return {
      id: service.id,
      code: service.code,
      name: service.name,
      description: service.description,
      type: service.type,
      serviceStatus: service.serviceStatus,
      pricingModel: service.pricingModel,
      currentVersion: service.currentVersion,
      features: service.features,
      endpoint: service.endpoint,
      requestConfig: service.config?.requestConfig,
      responseConfig: service.config?.responseConfig,
      price: service.price,
      monthlyPrice: undefined, // 需要根据业务逻辑计算
      yearlyPrice: undefined, // 需要根据业务逻辑计算
      freeCallsPerDay: undefined, // 需要根据业务逻辑设置
      dailyLimit: service.dailyLimit,
      tags: service.config?.tags,
      iconUrl: service.config?.iconUrl,
      documentationUrl: service.config?.documentationUrl,
      requiresAuth: service.requireAuth === 1,
      supportsBatch: service.isAsync === 1,
      createdAt: service.createdAt,
      updatedAt: service.updatedAt,
    };
  }
}
