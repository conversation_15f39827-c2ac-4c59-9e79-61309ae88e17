import { type AxiosRequestConfig, type AxiosResponse, type AxiosError } from 'axios'
import { apiTestService as apiTestAxios } from '@/utils/request'
// 导出EventSourcePolyfill供其他组件使用
import { NativeEventSource, EventSourcePolyfill } from 'event-source-polyfill'

export interface ApiTestRequest {
  method: string;
  url: string;
  headers?: Record<string, string>;
  params?: Record<string, string>;
  data?: any;
  isFileUpload?: boolean;
  isAsync?: boolean;
  signal?: AbortSignal;
}

export interface ApiTestResponse {
  status: number;
  statusText: string;
  headers: Record<string, any>;
  data: any;
  imageUrl?: string;
  error?: string;
}

export interface SavedRequest {
  id: string
  name: string
  description: string
  config: ApiTestRequest
  createdAt: string
  updatedAt: string
}

export interface TaskStatusResponse {
  success: boolean
  jobId: string
  status: string
  progress: number
  result?: any
  error?: any
  addresses?: any[] // 地址提取服务的结果
  duration?: number // 任务执行时长
  queuedAt?: number // 任务排队时间
}

// 导出EventSourcePolyfill
export { EventSourcePolyfill }

export const apiTestApi = {
  // 发送API测试请求
  async sendRequest(requestData: ApiTestRequest): Promise<ApiTestResponse> {
    try {
      // 确保URL是有效的
      let url = requestData.url;
      
      const axiosConfig: AxiosRequestConfig = {
        method: requestData.method,
        url: url,
        headers: requestData.headers,
        params: requestData.params,
        signal: requestData.signal, // 添加AbortSignal支持
      };
      
      if (requestData.isFileUpload && requestData.data) {
        // 处理文件上传
        const formData = new FormData();
        if (Array.isArray(requestData.data)) {
          // 如果是文件数组
          requestData.data.forEach((file: File) => {
            formData.append('file', file);
          });
        } else if (requestData.data instanceof FormData) {
          // 如果已经是FormData
          axiosConfig.data = requestData.data;
        } else {
          // 其他情况
          formData.append('file', requestData.data);
          axiosConfig.data = formData;
        }
        
        if (!axiosConfig.data) {
          axiosConfig.data = formData;
        }
      } else if (requestData.data) {
        // 处理普通数据
        axiosConfig.data = requestData.data;
      }

      // 处理异步模式
      if (requestData.isAsync) {
        // 确保params存在
        axiosConfig.params = axiosConfig.params || {};
        // 添加异步模式参数
        axiosConfig.params.mode = 'async';
      }

      // 发送请求 - 使用统一的apiTestAxios实例，包含完整的拦截器逻辑
      const response = await apiTestAxios(axiosConfig);

      // 处理图片响应
      let imageUrl: string | undefined = undefined;
      if (response.headers['content-type']?.includes('image')) {
        imageUrl = URL.createObjectURL(new Blob([response.data]));
      }

      return {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data,
        imageUrl
      };
    } catch (error: unknown) {
      console.error('API请求失败:', error);
      
      // 处理错误响应
      const axiosError = error as AxiosError;
      if (axiosError.response) {
        return {
          status: axiosError.response.status,
          statusText: axiosError.response.statusText,
          headers: axiosError.response.headers,
          data: axiosError.response.data,
          error: axiosError.message
        };
      }
      
      // 处理网络错误或其他错误
      throw new Error(axiosError.message || '请求失败');
    }
  },

  /**
   * 获取任务状态（轮询方式）
   * 
   * 优化说明：
   * 1. 保留原始响应结构，同时提取嵌套数据
   * 2. 特别处理addresses字段，确保地址数据能被正确提取
   * 3. 兼容多种数据结构，适应不同API的返回格式
   * 
   * @param jobId 任务ID
   * @param headers 请求头
   * @returns 任务状态响应
   */
  async getTaskStatus(jobId: string, headers: Record<string, string> = {}): Promise<TaskStatusResponse> {
    try {
      // 确保使用完整的URL（包括基础API路径）
      const baseUrl = import.meta.env.VITE_APP_BASE_API || ''
      const url = `${baseUrl}/op/api/tasks/${jobId}`
      console.log('获取任务状态URL:', url)
      console.log('获取任务状态请求头:', headers)
      
      const response = await apiTestAxios.get(url, {
        headers: {
          'Content-Type': 'application/json',
          ...headers // 使用传入的请求头
        },
      })
      console.log('任务状态原始响应:', response.data)
      
      // 处理响应数据，保留原始数据结构
      let taskData = response.data;
      
      // 如果响应数据嵌套在data字段中，提取出来
      if (taskData.data && typeof taskData.data === 'object') {
        // 将顶层的success等字段保留，同时合并data中的数据
        taskData = {
          ...taskData,
          ...taskData.data
        };
        
        // 特别处理addresses字段，确保它能被正确提取
        if (taskData.data.addresses && Array.isArray(taskData.data.addresses)) {
          taskData.addresses = taskData.data.addresses;
        }
      }
      
      // 确保基本字段存在
      if (!taskData.jobId) {
        taskData.jobId = jobId;
      }
      
      // 兼容state和status字段
      if (!taskData.status && taskData.state) {
        taskData.status = taskData.state;
      }
      
      // 确保success字段存在
      if (taskData.status === 'completed' && taskData.success === undefined) {
        taskData.success = true;
      }
      
      // 确保progress字段存在
      if (taskData.progress === undefined) {
        taskData.progress = taskData.status === 'completed' ? 100 : 0;
      }
      
      console.log('处理后的任务数据:', taskData);
      return taskData;
    } catch (error) {
      console.error('获取任务状态失败:', error)
      throw error
    }
  },

  // 获取保存的请求列表
  async getSavedRequests(): Promise<SavedRequest[]> {
    return await request.get('/op/api-test/saved-requests')
  },

  // 保存请求配置
  async saveRequest(requestData: {
    name: string
    description: string
    config: ApiTestRequest
  }): Promise<SavedRequest> {
    return await request.post('/op/api-test/saved-requests', requestData)
  },

  // 更新保存的请求
  async updateSavedRequest(
    id: string,
    requestData: {
      name: string
      description: string
      config: ApiTestRequest
    },
  ): Promise<SavedRequest> {
    return await request.put(`/op/api-test/saved-requests/${id}`, requestData)
  },

  // 删除保存的请求
  async deleteSavedRequest(id: string): Promise<void> {
    await request.delete(`/op/api-test/saved-requests/${id}`)
  },

  // 获取API文档
  async getApiDocs(): Promise<any> {
    const response = await request.get('/op/api-test/docs')
    return response
  },

  // 获取请求示例
  async getRequestExamples(serviceType: string): Promise<any> {
    const response = await request.get(`/op/api-test/examples/${serviceType}`)
    return response
  },

  // 验证API密钥
  async validateApiKey(apiKeyId: string): Promise<boolean> {
    try {
      const response: any = await request.get(`/op/api-test/validate-key/${apiKeyId}`)
      return response.valid
    } catch {
      return false
    }
  },

  // 获取服务端点列表
  async getServiceEndpoints(serviceType: string): Promise<any[]> {
    const response: any = await request.get(`/op/api-test/endpoints/${serviceType}`)
    return response
  },
}

export default apiTestApi
