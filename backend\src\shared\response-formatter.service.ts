import { Injectable, HttpException, Logger } from '@nestjs/common';
import { StandardResponse } from '../common/result/index';
import { ErrorHandlerService } from './error-handler.service';

/**
 * 响应格式化服务
 * 负责统一处理API响应格式
 */
@Injectable()
export class ResponseFormatterService {
  private readonly logger = new Logger(ResponseFormatterService.name);

  constructor(private readonly errorHandlerService: ErrorHandlerService) {}

  /**
   * 格式化成功响应
   * @param data 响应数据
   * @param message 成功消息
   * @param metadata 元数据
   * @returns 标准响应对象
   */
  formatSuccessResponse<T>(
    data: T,
    message = '操作成功',
    metadata: Record<string, any> = {}
  ): StandardResponse<T> {
    return {
      success: true,
      code: 0,
      message,
      data,
      timestamp: new Date().toISOString(),
      ...metadata
    };
  }

  /**
   * 格式化错误响应
   * @param error 错误对象
   * @param metadata 元数据
   * @returns 格式化后的HttpException
   */
  formatErrorResponse(
    error: any,
    metadata: Record<string, any> = {}
  ): HttpException {
    try {
      // 使用错误处理服务处理错误
      const errorResponse = this.errorHandlerService.handleUnknownError(error);
      
      // 添加元数据到错误响应
      if (errorResponse.getResponse && typeof errorResponse.getResponse === 'function') {
        const response = errorResponse.getResponse() as any;
        
        if (typeof response === 'object') {
          Object.assign(response, {
            timestamp: new Date().toISOString(),
            ...metadata
          });
        }
      }
      
      return errorResponse;
    } catch (formatError) {
      this.logger.error(`格式化错误响应时发生异常: ${formatError.message}`, formatError.stack);
      return error;
    }
  }

  /**
   * 格式化分页响应
   * @param items 分页项目
   * @param total 总数
   * @param page 当前页码
   * @param pageSize 每页大小
   * @param message 成功消息
   * @param metadata 元数据
   * @returns 标准分页响应
   */
  formatPaginatedResponse<T>(
    items: T[],
    total: number,
    page: number,
    pageSize: number,
    message = '查询成功',
    metadata: Record<string, any> = {}
  ): StandardResponse<{ items: T[]; pagination: { total: number; page: number; pageSize: number; pages: number } }> {
    const pages = Math.ceil(total / pageSize);
    
    return this.formatSuccessResponse(
      {
        items,
        pagination: {
          total,
          page,
          pageSize,
          pages
        }
      },
      message,
      metadata
    );
  }

  /**
   * 格式化流式响应（SSE等）
   * @param data 响应数据
   * @returns 格式化后的数据
   */
  formatStreamResponse<T>(data: T): T {
    // 流式响应不做特殊处理，直接返回原始数据
    return data;
  }

  /**
   * 格式化空响应
   * @param message 成功消息
   * @param metadata 元数据
   * @returns 标准响应对象
   */
  formatEmptyResponse(
    message = '操作成功',
    metadata: Record<string, any> = {}
  ): StandardResponse<null> {
    return this.formatSuccessResponse(null, message, metadata);
  }
} 