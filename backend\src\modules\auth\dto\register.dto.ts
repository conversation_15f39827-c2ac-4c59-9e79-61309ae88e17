import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsNotEmpty, IsObject, IsOptional, IsPhoneNumber, IsString, Length, Matches, MinLength, ValidateIf } from 'class-validator';
import { UserType } from '@/modules/user/entities/user.entity';

export class RegisterDto {
  @ValidateIf(o => !o.phone)
  @IsEmail({}, { message: '邮箱格式不正确' })
  @ApiPropertyOptional({ description: '邮箱' })
  email?: string;
  
  @ValidateIf(o => !o.email)
  @IsPhoneNumber('CN', { message: '手机号格式不正确' })
  @ApiPropertyOptional({ description: '手机号' })
  phone?: string;
  
  @IsString()
  @IsNotEmpty({ message: '密码不能为空' })
  @MinLength(8, { message: '密码至少需要8个字符' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/, {
    message: '密码必须包含至少一个大写字母，一个小写字母和一个数字',
  })
  @ApiProperty({ description: '密码' })
  password: string;
  
  @IsString()
  @IsNotEmpty({ message: '确认密码不能为空' })
  @ApiProperty({ description: '确认密码' })
  confirmPassword: string;
  
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: '邮箱验证码' })
  emailCode?: string;
  
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: '短信验证码' })
  smsCode?: string;
  
  @IsObject()
  @ApiProperty({ description: '安全验证数据' })
  securityVerification: {
    level: number;
    behaviorPattern: any;
    verificationTimestamp: number;
  };
  
  @IsOptional()
  @IsEnum(UserType)
  @ApiPropertyOptional({ description: '用户类型', enum: UserType })
  userType?: UserType;
  
  @IsOptional()
  @IsString()
  @Length(2, 30, { message: '昵称长度必须在2-30之间' })
  @ApiPropertyOptional({ description: '用户昵称' })
  nickname?: string;
} 