// 检查数据库表结构
const mysql = require('mysql2/promise');

async function checkDatabaseTables() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '123456',
    database: 'openapidb'
  });
  
  try {
    console.log('=== 检查数据库表 ===');
    
    // 查看所有表
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('数据库中的表:');
    tables.forEach((table, index) => {
      const tableName = Object.values(table)[0];
      console.log(`  ${index + 1}. ${tableName}`);
    });
    
    // 检查是否有任务相关的表
    console.log('\n=== 查找任务相关的表 ===');
    const taskRelatedTables = tables.filter(table => {
      const tableName = Object.values(table)[0].toLowerCase();
      return tableName.includes('task') || 
             tableName.includes('job') || 
             tableName.includes('queue') ||
             tableName.includes('call') ||
             tableName.includes('record');
    });
    
    if (taskRelatedTables.length > 0) {
      console.log('找到任务相关的表:');
      taskRelatedTables.forEach((table, index) => {
        const tableName = Object.values(table)[0];
        console.log(`  ${index + 1}. ${tableName}`);
      });
    } else {
      console.log('没有找到任务相关的表');
    }
    
    // 检查user_service表
    console.log('\n=== 检查user_service表 ===');
    try {
      const [userServices] = await connection.execute(
        'SELECT user_id, service_id, total_count, used_count, remaining_count FROM user_service WHERE user_id = 4 LIMIT 5'
      );
      console.log('用户4的服务记录:');
      userServices.forEach((record, index) => {
        console.log(`  ${index + 1}. 服务${record.service_id}: 总${record.total_count}, 已用${record.used_count}, 剩余${record.remaining_count}`);
      });
    } catch (error) {
      console.log('user_service表查询失败:', error.message);
    }
    
  } catch (error) {
    console.error('数据库操作失败:', error.message);
  } finally {
    await connection.end();
  }
}

checkDatabaseTables().catch(console.error);
