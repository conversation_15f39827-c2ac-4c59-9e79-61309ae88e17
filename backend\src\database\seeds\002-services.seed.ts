import { DataSource } from 'typeorm';
import { BaseSeed } from './base/base.seed';
import { SeedConfig, SeedResult } from './base/seed.interface';
import { ServiceEntity } from '../../modules/service/entities/service.entity';
import { ServiceType, ServiceStatus, PricingModel } from '../../modules/service/enums/service.enum';

/**
 * 服务种子数据
 * 创建系统默认的API服务
 */
export class ServicesSeed extends BaseSeed {
  config: SeedConfig = {
    name: 'services',
    description: '创建系统默认的API服务',
    environments: ['development', 'test', 'production'],
    dependencies: [],
    version: '1.0.0',
    priority: 2,
  };

  protected async execute(dataSource: DataSource): Promise<SeedResult> {
    // 默认服务配置（根据修复后的API端点列表）
    const defaultServices = [
      // OCR服务
      {
        code: 'OCR_UPLOAD',
        name: 'OCR文件上传识别',
        type: ServiceType.OCR,
        status: ServiceStatus.ACTIVE,
        description: '支持图片文件上传进行OCR文字识别，提取图片中的文字信息',
        pricingModel: PricingModel.PER_REQUEST,
        unitPrice: 0.05,
        endpoint: '/v1/op/ocr/upload',
        sortOrder: 100,
      },
      {
        code: 'OCR_RECOGNIZE',
        name: 'OCR Base64图片识别',
        type: ServiceType.OCR,
        status: ServiceStatus.ACTIVE,
        description: '支持Base64编码的图片进行OCR文字识别，无需文件上传',
        pricingModel: PricingModel.PER_REQUEST,
        unitPrice: 0.05,
        endpoint: '/v1/op/ocr/recognize',
        sortOrder: 101,
      },
      {
        code: 'OCR_STO_UPLOAD',
        name: '申通面单OCR文件上传识别',
        type: ServiceType.OCR,
        status: ServiceStatus.ACTIVE,
        description: '专门针对申通物流面单的文件上传OCR识别，优化识别准确率和字段提取',
        pricingModel: PricingModel.PER_REQUEST,
        unitPrice: 0.05,
        endpoint: '/v1/op/ocr/sto/upload',
        sortOrder: 102,
      },
      {
        code: 'OCR_STO_RECOGNIZE',
        name: '申通面单OCR Base64识别',
        type: ServiceType.OCR,
        status: ServiceStatus.ACTIVE,
        description: '专门针对申通物流面单的Base64图片OCR识别，无需文件上传',
        pricingModel: PricingModel.PER_REQUEST,
        unitPrice: 0.05,
        endpoint: '/v1/op/ocr/sto/recognize',
        sortOrder: 103,
      },
      
      // 地址处理服务
      {
        code: 'ADDRESS_EXTRACT',
        name: '从文本中提取地址信息',
        type: ServiceType.NLP,
        status: ServiceStatus.ACTIVE,
        description: '从文本中提取省市区、详细地址、手机号、姓名等地址相关信息',
        pricingModel: PricingModel.PER_REQUEST,
        unitPrice: 0.05,
        endpoint: '/v1/op/address/extract',
        sortOrder: 200,
      },
      {
        code: 'ADDRESS_NORMALIZE',
        name: '地址标准化',
        type: ServiceType.NLP,
        status: ServiceStatus.ACTIVE,
        description: '将非标准地址格式转换为标准地址格式，统一地址表示',
        pricingModel: PricingModel.PER_REQUEST,
        unitPrice: 0.05,
        endpoint: '/v1/op/address/normalize',
        sortOrder: 201,
      },
      
      // 地理编码服务
      {
        code: 'GEO_REVERSE',
        name: '逆地理编码（坐标转地址）',
        type: ServiceType.GEO,
        status: ServiceStatus.ACTIVE,
        description: '根据地理坐标（经纬度）获取对应的详细地址信息',
        pricingModel: PricingModel.PER_REQUEST,
        unitPrice: 0.05,
        endpoint: '/v1/op/geo/reverse',
        sortOrder: 300,
      },
      {
        code: 'GEO_FORWARD',
        name: '正地理编码（地址转坐标）',
        type: ServiceType.GEO,
        status: ServiceStatus.ACTIVE,
        description: '根据地址信息获取对应的地理坐标（经纬度）',
        pricingModel: PricingModel.PER_REQUEST,
        unitPrice: 0.05,
        endpoint: '/v1/op/geo/forward',
        sortOrder: 301,
      },
    ];
    
    let affectedRows = 0;
    
    for (const serviceData of defaultServices) {
      try {
        // 使用 upsert 方法确保幂等性
        await this.upsert(
          dataSource,
          ServiceEntity,
          {
            ...serviceData,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          ['code'] // 以 code 作为唯一标识
        );
        
        affectedRows++;
        this.logger.log(`服务 ${serviceData.name} 创建/更新成功`);
        
      } catch (error) {
        this.logger.error(`创建服务 ${serviceData.name} 失败: ${error.message}`);
      }
    }
    
    return {
      success: true,
      message: `成功创建/更新 ${affectedRows} 个服务`,
      affectedRows,
    };
  }
  
  /**
   * 检查是否需要执行
   */
  async shouldRun(dataSource: DataSource): Promise<boolean> {
    const serviceRepository = this.getRepository(dataSource, ServiceEntity);
    
    // 检查是否存在任何服务
    const serviceCount = await serviceRepository.count();
    
    return serviceCount === 0;
  }
}
