import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';
import { Exclude } from 'class-transformer';
import { GenerateUUID } from '../../../common/utils/index';
// 移除直接导入其他模块的Entity，使用字符串关系定义避免循环依赖
// import { ServiceEntity } from '../../service/entities/service.entity';
// import { UserEntity } from '../../user/entities/user.entity';

@Entity('user_service', {
  comment: '用户服务表',
})
export class UserServiceEntity extends BaseEntity {
  @Column({ name: 'total_count', default: 0 })
  totalCount: number; // 总调用次数

  @Column({ name: 'used_count', default: 0 })
  usedCount: number; // 已用次数

  @Column({ name: 'free_used_today', default: 0 })
  freeUsedToday: number; // 今日已用免费次数

  @Column({ name: 'last_reset_date', type: 'date', nullable: true })
  lastResetDate?: Date; // 上次免费额度重置日期

  @Column({ name: 'alert_sent', default: false })
  alertSent: boolean; // 是否已发送预警

  // 关系 - 使用字符串关系定义，避免循环引用
  @ManyToOne('UserEntity', 'userServices')
  @JoinColumn({ name: 'userId' })
  user: any;

  @ManyToOne('ServiceEntity', 'userServices')
  @JoinColumn({ name: 'serviceId' })
  service: any;
}
