# 网关模块异步接口调用使用指南

## 概述

本说明文档介绍如何通过网关模块以异步方式调用三大业务服务（物流面单OCR识别、物流地址提取、省市区识别、坐标逆解析），并通过任务ID（jobId）结合SSE方式获取最终处理结果。异步调用模式适用于长耗时任务，可以有效避免API调用超时。

## 异步调用基础

### 认证方式

所有接口调用需要使用API密钥进行认证：

1. **请求头认证**：在HTTP请求头中添加密钥秘钥`X-API-KEY、X-SECRET-KEY`字段，注意不要泄露密钥，如果泄露或者忘记请尽快进开发平台重新生成密钥。

### 异步调用流程

异步API调用的基本流程如下：

1. 发起异步API请求，在请求参数中设置`mode=async`
2. 服务器立即返回包含`jobId`的响应
3. 使用`jobId`通过任务状态API或SSE实时推送获取结果
4. 处理最终结果或错误信息

### 异步响应格式

所有异步调用的初始响应格式统一如下：

```json
{
  "success": true,
  "jobId": "job_xxxxxxxxxx",
  "status": "queued",
  "statusUrl": "/v1/op/api/tasks/job_xxxxxxxxxx",
  "message": "任务已提交到队列，请通过statusUrl查询结果",
  "requestId": "req_xxxxxxxxxx",
  "responseTime": 120
}
```


## 物流面单OCR识别

### 文件上传方式

**接口地址**：`POST /v1/op/api/ocr/upload`
**请求类型**：`multipart/form-data`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|------|-----|
| file | File | 是 | 要识别的图片文件 |
| mode | String | 是 | 处理模式，固定值`async` |

**请求示例**：

```bash
curl -X POST http://localhost:8088/v1/op/api/ocr/upload \
  -H "X-API-KEY: your-api-key" \
  -H "X-SECRET-KEY: your-secret-key" \
  -F "file=@/path/to/your/image.jpg" \
  -F "mode=async"
```


### Base64方式

**接口地址**：`POST /v1/op/api/ocr/recognize`
**请求类型**：`application/json`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|------|-----|
| image_base64 | String | 是 | Base64编码的图片数据 |
| filename | String | 否 | 文件名（含扩展名） |
| mode | String | 是 | 处理模式，固定值`async` |

**请求示例**：

```bash
curl -X POST http://localhost:8088/v1/op/api/ocr/recognize \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: your-api-key" \
  -H "X-SECRET-KEY: your-secret-key" \
  -d '{
    "image_base64": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEASABIAAD/...",
    "mode": "async"
  }'
```



## 地址提取

**接口地址**：`POST /v1/op/api/address/extract`
**请求类型**：`application/json`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|------|-----|
| text | String | 是 | 包含地址的文本 |
| mode | String | 是 | 处理模式，固定值`async` |



**请求示例**：

```bash
curl -X POST http://localhost:8088/v1/op/api/address/extract \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: your-api-key" \
  -H "X-SECRET-KEY: your-secret-key" \
  -d '{
    "text": "上海市浦东新区张江高科技园区",
    "mode": "async"
  }'
```


## 坐标逆解析

**接口地址**：`POST /v1/op/api/geo/reverse`
**请求类型**：`application/json`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|------|-----|
| latitude | Number | 是 | 纬度 |
| longitude | Number | 是 | 经度 |
| mode | String | 是 | 处理模式，固定值`async` |

**请求示例**：

```bash
curl -X POST http://localhost:8088/v1/op/api/geo/reverse \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: your-api-key" \
  -H "X-SECRET-KEY: your-secret-key" \
  -d '{
    "latitude": 31.2304,
    "longitude": 121.4737,
    "mode": "async"
  }'
```

## 统一说明

- 所有异步接口返回的jobId可用于SSE接口获取最终处理结果。
- SSE接口为长连接，任务完成后自动关闭。

## 任务状态查询(轮询)

**接口地址**：`GET /v1/op/api/tasks/{jobId}`
**请求类型**：`application/json`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|------|-----|
| jobId | String | 是 | 任务ID，路径参数 |

**请求示例**：

```bash
curl -X GET "http://localhost:8088/v1/op/api/tasks/job_xxxxxxxxxx" \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: your-api-key" \
  -H "X-SECRET-KEY: your-secret-key" \
```

**响应格式**：

```json
{
  "success": true,
  "jobId": "job_xxxxxxxxxx",
  "status": "completed",
  "progress": 100,
  "result": {
    // 任务结果，与同步API返回格式一致
  },
  "timestamp": {
    "created": "2023-07-01T12:00:00Z",
    "processed": "2023-07-01T12:00:05Z",
    "finished": "2023-07-01T12:00:10Z"
  }
}
```

## SSE实时状态推送异步任务结果（推荐）

服务器发送事件(SSE)允许客户端接收任务状态的实时更新，无需频繁轮询API。

**接口地址**：`GET /v1/op/api/tasks/{jobId}/stream`
**请求类型**：`application/json`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|------|-----|
| jobId | String | 是 | 任务ID，路径参数 |
| service | String |  否 | 服务类型，可选值： |
| timeout | Number | 否 | 超时时间(ms)，默认值：30000 |
| interval | Number | 否 | 检查间隔(ms)，默认值：1000 |

> SSE返回格式（任务完成时）

```
{
  "success": true,
  "status": "completed",
  "result": {
    // 识别结果结构，具体见业务返回
  },
  "process_time": 1234,
  "image_info": {},
  "requestId": "{jobId}",
  "responseTime": 567
}
```

> 任务未完成时会持续推送进度，任务完成后推送最终结果并关闭连接。

**前端代码示例**：

```javascript
function subscribeToTaskStatus(jobId, service) {
  const eventSource = new EventSource(`/v1/op/api/tasks/${jobId}/stream?service=${service}`);
  
  eventSource.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log(`任务状态更新: ${data.status}, 进度: ${data.progress}%`);
    
    // 更新UI显示任务进度
    document.getElementById('progressBar').value = data.progress;
    document.getElementById('statusText').innerText = `状态: ${data.status}`;
    
    if (data.status === 'completed' || data.status === 'failed') {
      console.log('任务结束，关闭SSE连接');
      eventSource.close();
      
      if (data.status === 'completed') {
        console.log('任务结果:', data.result);
        // 处理并显示结果
        displayResult(data.result);
      } else {
        console.error('任务失败:', data.error);
        // 显示错误信息
        showError(data.error);
      }
    }
  };
  
  eventSource.onerror = (error) => {
    console.error('SSE连接错误:', error);
    eventSource.close();
    
    // 显示连接错误
    showError('实时状态连接失败，请稍后重试');
  };
}

// 处理结果的辅助函数
function displayResult(result) {
  const resultContainer = document.getElementById('resultContainer');
  
  // 清空之前的结果
  resultContainer.innerHTML = '';
  
  // 创建结果显示
  const resultElement = document.createElement('div');
  resultElement.className = 'result-box';
  
  // 根据不同的服务类型，显示不同的结果格式
  if (result.address) {
    // 地址提取结果
    resultElement.innerHTML = `
      <h3>地址提取结果</h3>
      <p><strong>省份:</strong> ${result.address.province || '未提取'}</p>
      <p><strong>城市:</strong> ${result.address.city || '未提取'}</p>
      <p><strong>区县:</strong> ${result.address.district || '未提取'}</p>
      <p><strong>详细地址:</strong> ${result.address.detail || '未提取'}</p>
      ${result.name ? `<p><strong>姓名:</strong> ${result.name}</p>` : ''}
      ${result.phone ? `<p><strong>电话:</strong> ${result.phone}</p>` : ''}
    `;
  } else if (result.logistics) {
    // OCR结果
    resultElement.innerHTML = `
      <h3>物流单号识别结果</h3>
      <p><strong>快递公司:</strong> ${result.logistics.company || '未识别'}</p>
      <p><strong>运单号:</strong> ${result.logistics.waybillNo || '未识别'}</p>
      <p><strong>寄件人:</strong> ${result.logistics.sender?.name || '未识别'}</p>
      <p><strong>寄件人电话:</strong> ${result.logistics.sender?.phone || '未识别'}</p>
      <p><strong>寄件人地址:</strong> ${result.logistics.sender?.address || '未识别'}</p>
      <p><strong>收件人:</strong> ${result.logistics.receiver?.name || '未识别'}</p>
      <p><strong>收件人电话:</strong> ${result.logistics.receiver?.phone || '未识别'}</p>
      <p><strong>收件人地址:</strong> ${result.logistics.receiver?.address || '未识别'}</p>
    `;
  } else if (result.formatted_address) {
    // 逆地理编码结果
    resultElement.innerHTML = `
      <h3>坐标逆解析结果</h3>
      <p><strong>详细地址:</strong> ${result.formatted_address}</p>
      <p><strong>省份:</strong> ${result.province || '未提取'}</p>
      <p><strong>城市:</strong> ${result.city || '未提取'}</p>
      <p><strong>区县:</strong> ${result.district || '未提取'}</p>
      <p><strong>街道:</strong> ${result.street || '未提取'}</p>
      <p><strong>门牌号:</strong> ${result.street_number || '未提取'}</p>
    `;
  } else {
    // 通用结果显示
    resultElement.innerHTML = `
      <h3>处理结果</h3>
      <pre>${JSON.stringify(result, null, 2)}</pre>
    `;
  }
  
  resultContainer.appendChild(resultElement);
}

// 显示错误信息
function showError(errorMessage) {
  const resultContainer = document.getElementById('resultContainer');
  resultContainer.innerHTML = `
    <div class="error-box">
      <h3>处理失败</h3>
      <p>${errorMessage}</p>
    </div>
  `;
}
```



## 常见问题

### 1. 任务超时怎么处理？

异步任务默认有60秒的处理超时时间。如果任务在此时间内未完成，系统会自动将任务标记为失败。对于大型图片或复杂处理，建议使用更小的文件或优化处理参数。

### 2. 如何处理任务失败？

当任务失败时，可以通过以下步骤处理：

1. 检查任务状态API的错误信息
2. 根据错误信息调整请求参数
3. 重新提交任务

### 3. SSE连接断开如何处理？

SSE连接可能因为网络问题或服务器重启而断开。建议实现自动重连逻辑：

```javascript
let retryCount = 0;
const maxRetries = 5;

function connectSSE(jobId, service) {
  const eventSource = new EventSource(`/v1/op/api/tasks/${jobId}/stream?service=${service}`);
  
  eventSource.onopen = () => {
    retryCount = 0; // 连接成功，重置重试计数
  };
  
  eventSource.onerror = (error) => {
    eventSource.close();
    
    if (retryCount < maxRetries) {
      retryCount++;
      const delay = Math.min(1000 * Math.pow(2, retryCount), 30000);
      console.log(`连接断开，${delay/1000}秒后重试(${retryCount}/${maxRetries})...`);
      setTimeout(() => connectSSE(jobId, service), delay);
    } else {
      console.error('达到最大重试次数，切换到轮询模式');
      // 切换到轮询模式
      pollTaskStatus(jobId, service);
    }
  };
  
  // 处理消息...
}
```

### 4. 任务结果保存多久？

任务结果在系统中保存7天，超过保存期限后将无法查询。对于重要数据，请及时获取并保存。

---

如有更多问题，请联系技术支持。 