import { Injectable, Logger } from '@nestjs/common';
import { getServiceConfig } from '../config/service-registry.config';

/**
 * 请求选项接口
 */
export interface RequestOptions {
  method: string;
  path: string;
  body?: any;
  query?: Record<string, any>;
}

/**
 * 处理模式决策器
 * 负责智能判断请求应该使用同步还是异步处理模式
 */
@Injectable()
export class ProcessingModeDecider {
  private readonly logger = new Logger(ProcessingModeDecider.name);
  
  /**
   * 决定处理模式
   * @param serviceCode 服务代码
   * @param requestOptions 请求选项
   * @param userPreference 用户偏好的处理模式
   * @returns 处理模式: 'sync' | 'async'
   */
  decideProcessingMode(
    serviceCode: string,
    requestOptions: RequestOptions,
    userPreference?: 'sync' | 'async'
  ): 'sync' | 'async' {
    // 1. 用户强制指定的处理模式优先
    if (userPreference) {
      this.logger.debug(`使用用户指定的处理模式: ${userPreference}`);
      return userPreference;
    }
    
    // 2. 获取服务配置
    const serviceConfig = getServiceConfig(serviceCode);
    if (!serviceConfig) {
      this.logger.warn(`未找到服务配置: ${serviceCode}，默认使用同步模式`);
      return 'sync';
    }
    
    // 3. 检查服务是否支持异步处理
    if (!serviceConfig.processingModes.includes('async')) {
      this.logger.debug(`服务 ${serviceCode} 不支持异步处理，使用同步模式`);
      return 'sync';
    }
    
    // 4. 基于请求特征判断
    // 4.1 检查是否包含文件上传
    if (requestOptions.body && requestOptions.body.file) {
      const file = requestOptions.body.file;
      // 如果文件大于1MB，建议异步处理
      if (file.size && file.size > 1024 * 1024) {
        this.logger.debug(`文件大小 ${file.size} 字节 > 1MB，建议异步处理`);
        return 'async';
      }
    }
    
    // 4.2 检查是否包含base64图片数据
    if (requestOptions.body && requestOptions.body.image_base64) {
      const base64Data = requestOptions.body.image_base64;
      // 如果base64字符串长度超过1MB对应的字符数，建议异步处理
      if (typeof base64Data === 'string' && base64Data.length > 1024 * 1024 * 1.33) {
        this.logger.debug(`Base64数据长度 ${base64Data.length} > 1.33MB等效字符数，建议异步处理`);
        return 'async';
      }
    }
    
    // 4.3 检查是否为批量请求
    if (
      (requestOptions.body && Array.isArray(requestOptions.body.texts) && requestOptions.body.texts.length > 10) ||
      (requestOptions.body && Array.isArray(requestOptions.body.coordinates) && requestOptions.body.coordinates.length > 10)
    ) {
      this.logger.debug(`批量请求数量超过10，建议异步处理`);
      return 'async';
    }
    
    // 4.4 检查路径是否明确指示异步处理
    if (requestOptions.path.includes('/async') || requestOptions.query?.async === 'true') {
      this.logger.debug(`路径或查询参数指示异步处理`);
      return 'async';
    }
    
    // 5. 使用服务默认模式
    this.logger.debug(`使用服务 ${serviceCode} 的默认处理模式: ${serviceConfig.defaultMode}`);
    return serviceConfig.defaultMode;
  }
} 