import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsObject, IsOptional, IsPhoneNumber, IsString, Matches, MinLength, ValidateIf } from 'class-validator';

export class ResetPasswordDto {
  @ValidateIf(o => !o.phone)
  @IsEmail({}, { message: '邮箱格式不正确' })
  @ApiPropertyOptional({ description: '邮箱' })
  email?: string;
  
  @ValidateIf(o => !o.email)
  @IsPhoneNumber('CN', { message: '手机号格式不正确' })
  @ApiPropertyOptional({ description: '手机号' })
  phone?: string;
  
  @IsString()
  @IsNotEmpty({ message: '验证码不能为空' })
  @ApiProperty({ description: '验证码' })
  code: string;
  
  @IsString()
  @IsNotEmpty({ message: '新密码不能为空' })
  @MinLength(8, { message: '新密码至少需要8个字符' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/, {
    message: '密码必须包含至少一个大写字母，一个小写字母和一个数字',
  })
  @ApiProperty({ description: '新密码' })
  newPassword: string;
  
  @IsString()
  @IsNotEmpty({ message: '确认密码不能为空' })
  @ApiProperty({ description: '确认新密码' })
  confirmPassword: string;
  
  @IsObject()
  @ApiProperty({ description: '安全验证数据' })
  securityVerification: {
    level: number;
    behaviorPattern: any;
    verificationTimestamp: number;
  };
} 