const axios = require('axios');
const crypto = require('crypto');

/**
 * 测试API密钥认证脚本
 */
async function testApiAuth() {
  console.log('开始测试API密钥认证...');

  // 测试参数
  const apiKey = 'ak-579b27759508f152525a4e5a567efd5a';
  const secretKey = 'sk-93f682d8f646ca950'; // 这个需要从数据库获取解密后的值
  const baseUrl = 'http://localhost:3000';
  const endpoint = '/v1/op/address/extract?mode=async';

  try {
    // 生成签名认证头部
    const timestamp = Math.floor(Date.now() / 1000);
    const nonce = crypto.randomBytes(16).toString('hex');

    // 构建规范化请求字符串
    const method = 'POST';
    const canonicalRequest = `${method}\n${endpoint}\n${timestamp}\n${nonce}`;

    // 生成签名
    const signature = crypto
      .createHmac('sha256', secretKey)
      .update(canonicalRequest)
      .digest('base64');

    console.log('认证信息:');
    console.log(`API Key: ${apiKey}`);
    console.log(`Timestamp: ${timestamp}`);
    console.log(`Nonce: ${nonce}`);
    console.log(`Signature: ${signature}`);

    // 发送请求
    const response = await axios.post(`${baseUrl}${endpoint}`, {
      text: '测试地址提取'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-API-KEY': apiKey,
        'X-TIMESTAMP': timestamp.toString(),
        'X-NONCE': nonce,
        'X-SIGNATURE': signature
      }
    });

    console.log('\n✅ 请求成功！');
    console.log('响应状态:', response.status);
    console.log('响应数据:', response.data);

  } catch (error) {
    console.log('\n❌ 请求失败！');
    if (error.response) {
      console.log('错误状态:', error.response.status);
      console.log('错误信息:', error.response.data);
    } else {
      console.log('错误信息:', error.message);
    }
  }
}

// 运行测试
testApiAuth().catch(console.error);