# 免费额度资格规则

本文档描述了系统中免费额度的分配和资格规则。

## 基本规则

1. **新用户资格**：
   - 所有新注册用户（未进行过充值或购买操作）都有资格获取每日免费使用额度
   - 一旦用户充值或购买服务，将永久失去免费额度资格

2. **额度分配**：
   - 系统每天凌晨（00:00）重置符合条件用户的每日免费额度
   - 默认免费额度为3次调用/天（可通过配置更改）

3. **资格判定**：
   - 用户实体表中的`isFreeQuotaEligible`字段标记用户是否有资格
   - 系统定时任务只为有资格的用户重置免费额度

## 实现细节

### 用户付费状态跟踪

当用户执行以下操作时，会永久丧失免费额度资格：

1. **充值操作**：
   - 当用户向账户充值时，系统会更新用户的`isFreeQuotaEligible`字段为`false`
   - 此操作在`UserService.recharge()`方法中实现

2. **购买服务**：
   - 当用户购买服务或增加使用次数时，同样会失去免费额度资格
   - 此逻辑在订单支付成功回调中实现

### 免费额度重置机制

1. **定时任务**：
   - `ApiUsageTrackerService.resetDailyFreeQuota()`方法负责重置免费额度
   - 该方法只会为`isFreeQuotaEligible=true`的用户重置额度

2. **额度查询优化**：
   - API调用流程中会先检查用户是否有资格使用免费额度
   - 对于没有资格的用户，直接跳过免费额度检查，节省数据库查询

## 数据模型

1. **UserEntity**:
   ```typescript
   @Entity('open_user')
   export class UserEntity extends BaseEntity {
     // ... 其他字段 ...
     
     @Column({ default: true, comment: '是否有资格获取免费额度' })
     isFreeQuotaEligible: boolean;
     
     @Column({ type: 'int', default: 0, comment: '每日免费使用次数剩余' })
     dailyFreeUsageRemaining: number;
     
     // ... 其他字段 ...
   }
   ```

2. **UserPaymentStatusEntity**:
   ```typescript
   @Entity('user_payment_status')
   export class UserPaymentStatusEntity extends BaseEntity {
     @Column({ default: false })
     hasPaid: boolean;
     
     @Column({ nullable: true })
     lastPaymentDate: Date;
     
     // ... 其他字段 ...
   }
   ```

## 配置选项

系统管理员可以通过环境变量或配置文件调整免费额度参数：

```typescript
// 从 src/modules/user/user.config.ts
export const DEFAULT_USER_USAGE_CONFIG: UserUsageConfig = {
  // ... 其他配置 ...
  dailyFreeUsage: 3,  // 每日免费额度次数，可通过环境变量USER_DAILY_FREE_USAGE覆盖
};
```

## 最佳实践

1. **不应混用计费策略**：
   - 对于已付费用户，应该直接扣减其购买的次数
   - 免费额度仅用于吸引新用户体验服务

2. **清晰的用户提示**：
   - 当用户首次充值或购买时，应明确提示将失去免费额度资格
   - 在用户界面中清晰展示用户是否还有免费额度资格 