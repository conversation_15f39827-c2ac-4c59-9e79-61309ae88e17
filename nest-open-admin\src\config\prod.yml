# 生产环境配置
app:
  prefix: 'v1'
  port: 8088
    # 是否启用cors，启用就是后端允许跨域
  cors: false
  # 主域名，用于CORS同源策略判断，例如 example.com
  mainDomain: 'aiszyl.cn'
  enableDetailedErrors: false
  logger:
    # 项目日志存储路径，相对路径（相对本项目根目录）或绝对路径
    dir: '../logs'
  swagger:
    enable: true
    title: 开放平台API文档
    desc: 开放平台管理系统
    version: 1.0
  # 文件相关
  file:
    # 是否为本地文件服务或cos
    isLocal: true
    # location 文件上传后存储目录，相对路径（相对本项目根目录）或绝对路径
    location: 'upload'
    # 文件服务器地址，生产环境使用域名
    domain: 'https://api.aiszyl.cn'
    # 文件虚拟路径, 必须以 / 开头， 如 http://localhost:8081/static/****.jpg  , 如果不需要则 设置 ''
    serveRoot: '/upload'
    # 文件大小限制，单位M
    maxSize: 10

# TypeORM数据库配置
database:
  type: 'mysql'
  host: 'mysql'
  port: 3306
  username: 'root'
  password: '4iF6TSENcSaH8pSz'
  database: 'openapidb'
  charset: 'utf8mb4'
  logger: 'file'
  multipleStatements: true
  supportBigNumbers: true
  bigNumberStrings: true
  retryAttempts: 3
  retryDelay: 3000
  autoLoadEntities: true
  synchronize: false  # 生产环境必须为 false
  logging: false      # 生产环境可关闭详细日志
  migrationsRun: true # 生产环境自动运行迁移
  migrations: ['dist/database/migrations/*{.ts,.js}']

# Python服务配置
PYTHON_SERVICE_URL: 'http://python-service:8866'  # 基础URL
OCR_SERVICE_URL: 'http://python-service:8866/predict/ocr_system'  # OCR专用端点
ADDRESS_SERVICE_URL: 'http://python-service:8866/extract-address'  # 地址提取专用端点
GEO_SERVICE_URL: 'http://python-service:8866/rev-geo'  # 坐标逆解析专用端点

# 高德地图API Key
AMAP_KEY: 941c8903ad31b2acf1b20118d2a54bb1
TENCENT_MAP_KEY: GT7BZ-QV2RD-KH74P-HKCNM-5AF76-3VBHL
BAIDU_MAP_KEY: V4GVHuRD0T0TImlunB7UoehFiFY4Rbme

# 使用阿里云内网DNS
DNS_SERVERS: *************,*************


# AI服务配置
AI_PROVIDERS:
  openai:
    api_key: ''
    base_url: 'https://api.openai.com/v1'
    model: 'gpt-3.5-turbo'
    timeout: 30000
  claude:
    api_key: ''
    base_url: 'https://api.anthropic.com'
    model: 'claude-3-sonnet-20240229'
    timeout: 30000
  local:
    base_url: 'http://localhost:8000'
    model: 'local-llm'
    timeout: 60000

# NLP服务配置
NLP_PROVIDERS:
  baidu:
    app_id: ''
    api_key: ''
    secret_key: ''
    timeout: 30000
  tencent:
    secret_id: ''
    secret_key: ''
    region: 'ap-beijing'
    timeout: 30000
  local:
    base_url: 'http://localhost:8001'
    timeout: 30000

# 服务提供者负载均衡配置
SERVICE_PROVIDERS:
  load_balance_strategy: 'round_robin'  # round_robin, random, least_connections
  health_check_interval: 60000  # 健康检查间隔(ms)
  circuit_breaker:
    failure_threshold: 5  # 失败阈值
    timeout: 60000  # 熔断器超时时间
    reset_timeout: 300000  # 重置超时时间


# redis 配置
redis:
  host: 'redis'
  password: 'BamsxmSesxBDBDEX'
  port: 6379
  db: 0
  keyPrefix: ''

# 限流 短期： 1秒内最多10次，10秒内最多20次，60秒内最多100次
THROTTLE_SHORT_LIMIT: 10
THROTTLE_MEDIUM_LIMIT: 20
THROTTLE_LONG_LIMIT: 100

# 短信配置
sms:
  provider: 'qiniu'  # 短信服务提供商: qiniu, aliyun, tencent
  qiniu:
    accessKey: 'gl181BHCQifYj9B24TneE7Th3kkkvcJC4EeTgPHB'
    secretKey: 'xeNkUIOlFcjCe-DwjwrWtZUrnZwUsZZJR-vhoKx2'
    signatureId: '1929433938451775488'  # 短信签名ID
    templates:
      register: '1929436006310752256'  # 注册验证码模板ID
      login: '1929436006310752256'     # 登录验证码模板ID
      reset: '1929436006310752256'     # 重置密码验证码模板ID

# jwt 配置
jwt:
  secretkey: 'zANDwNQVFzxlfG9myPxVWAkq4iXJEPhy'
  refreshTokenSecret: 'bzANDwNQVFxfG9myPxVWAkh4iXJEPtx'
  expiresin: '2h'
  refreshExpiresIn: '7d'
# 权限 白名单配置
perm:
  router:
    whitelist:
      [
        { path: '/nestApi/admin/captchaImage', method: 'GET' },
        { path: '/nestApi/admin/register', method: 'POST' },
        { path: '/nestApi/admin/login', method: 'POST' },
        { path: '/nestApi/common//upload', method: 'POST' },
        { path: '/nestApi/admin/article/posts/list', method: 'GET' },
        { path: '/nestApi/admin/article/category/list', method: 'GET' },
        { path: '/nestApi/mall/pms/product', method: 'GET' },
        { path: '/nestApi/mall/pms/product/list', method: 'POST' },
        { path: '/nestApi/mall/pms/cate', method: 'GET' },
        { path: '/nestApi/mall/resource/list', method: 'GET' },
      ]
# 初始化数据
initData:
  users:
    [
      {  username: 'admin', nickname: '管理员', password: 'szyl123456', role: 'admin', userStatus: 'active', remark: '管理员' },
      {  username: 'sysTest', nickname: '普通账号', password: 'szyl123456', role: 'user', userStatus: 'active', remark: '测试员' },
    ]

# HTTP客户端配置
http:
  timeout: 30000  # 30秒
  maxRedirects: 5

# 队列配置
queue:
  defaultJobOptions:
    attempts: 3
    backoff:
      type: 'exponential'
      delay: 1000
    timeout: 180000  # 3分钟
    removeOnComplete: true
    removeOnFail: false
