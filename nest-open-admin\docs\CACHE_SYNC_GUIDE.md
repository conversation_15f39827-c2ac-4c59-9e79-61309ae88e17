# 缓存同步机制完善指南

## 概述

本文档描述了项目中缓存同步机制的完善情况，确保在密钥和用户信息更新时能够正确同步缓存中的信息。

## 已完善的缓存同步机制

### 1. API密钥缓存同步

#### 涉及的操作
- ✅ **创建API密钥**: 自动缓存新密钥信息
- ✅ **更新API密钥**: 清除旧缓存，确保下次获取最新数据
- ✅ **重新生成密钥**: 清除旧密钥缓存，缓存新密钥
- ✅ **撤销/激活密钥**: 清除缓存，确保状态同步
- ✅ **标记密钥已查看**: 清除缓存，更新查看状态
- ✅ **删除密钥**: 清除相关缓存
- ✅ **批量删除**: 批量清除缓存
- ✅ **更新使用记录**: 清除缓存，确保使用时间同步

#### 缓存策略
```typescript
// 缓存键格式
api_key:{keyHash}

// TTL: 300秒（5分钟）
// 操作策略: 写后删除（Write-Through Delete）
```

### 2. 用户信息缓存同步

#### 涉及的操作
- ✅ **更新用户资料**: 写后更新缓存
- ✅ **更新用户信息**: 清除相关缓存，如用户类型变更时清除角色权限缓存
- ✅ **变更用户状态**: 清除用户相关缓存和API密钥缓存
- ✅ **批量更新用户缓存**: 支持批量操作
- ✅ **清除用户缓存**: 全量清除用户相关缓存

#### 缓存策略
```typescript
// 缓存键格式
user:{userId}
user_roles:{userId}
user_permissions:{userId}
user_balance:{userId}
user_quota:{userId}*

// TTL: 1800秒（30分钟）
// 操作策略: 写后更新（Write-Through Update）
```

### 3. 角色权限缓存同步

#### 新增功能
- ✅ **角色权限更新事件**: 通过事件机制处理角色权限变更
- ✅ **缓存清理**: 自动清除用户角色权限相关缓存
- ✅ **批量处理**: 支持批量用户角色权限缓存同步
- ✅ **事件监听**: 完善的事件监听器处理缓存同步

#### 缓存策略
```typescript
// 缓存键格式
user_roles:{userId}
user_permissions:{userId}

// TTL: 600秒（10分钟）
// 操作策略: 事件驱动清除（Event-Driven Clear）
```

## 核心组件

### 1. CacheSyncService

新增的专用缓存同步服务，提供统一的缓存同步接口：

```typescript
@Injectable()
export class CacheSyncService {
  // API密钥缓存同步
  async syncApiKeyCache(keyHash: string, operation: 'update' | 'delete'): Promise<void>
  
  // 用户缓存同步
  async syncUserCache(userId: number, operation: 'update' | 'delete' | 'clear'): Promise<void>
  
  // 用户角色权限缓存同步
  async syncUserRolePermissionCache(userId: number, action: 'refresh' | 'clear'): Promise<void>
  
  // 批量同步
  async batchSyncUserRolePermissionCache(userIds: number[], action: 'refresh' | 'clear'): Promise<void>
  
  // 全量清除
  async clearAllUserRelatedCache(userId: number): Promise<void>
}
```

### 2. AuthEventListener

完善的事件监听器，处理角色权限更新事件：

```typescript
@OnEvent('user.role-permission.update')
async handleUserRolePermissionUpdate(event: UserRolePermissionUpdateEvent): Promise<void> {
  const { userId, action } = event;
  
  if (action === 'clear') {
    await this.clearUserRolePermissionCache(userId);
  } else if (action === 'refresh') {
    await this.refreshUserRolePermissionCache(userId);
  }
}
```

### 3. 增强的服务方法

#### ApiKeyService
- `markSecretAsViewed()`: 添加缓存清理
- `remove()`: 新增单个删除方法，包含缓存清理
- 所有更新操作都包含缓存同步

#### EnhancedUserService
- `updateUserRolePermissions()`: 新增角色权限缓存同步方法
- `batchUpdateUserRolePermissions()`: 批量角色权限缓存同步
- `changeStatus()`: 状态变更时清除角色权限缓存
- `updateUser()`: 用户类型变更时清除角色权限缓存

## 事件驱动架构

### 事件类型

```typescript
// 用户角色权限更新事件
export class UserRolePermissionUpdateEvent {
  constructor(
    public readonly userId: number,
    public readonly action: 'refresh' | 'clear'
  ) {}
}
```

### 事件流程

1. **触发**: 用户信息/角色权限发生变更
2. **发送事件**: 服务层发送相应事件
3. **监听处理**: AuthEventListener 接收并处理事件
4. **缓存同步**: 执行相应的缓存清理或刷新操作
5. **日志记录**: 记录操作结果和错误信息

## 缓存一致性策略

### 1. 写后删除 (Write-Through Delete)
适用于：API密钥、用户状态变更
- 优点：确保数据一致性
- 缺点：下次访问需要重新加载

### 2. 写后更新 (Write-Through Update)
适用于：用户基本信息更新
- 优点：保持缓存热度
- 缺点：需要额外的更新操作

### 3. 事件驱动清除 (Event-Driven Clear)
适用于：角色权限变更
- 优点：解耦操作，支持异步处理
- 缺点：需要可靠的事件机制

## 容错机制

### 1. 缓存操作失败处理
- 缓存操作失败不影响业务流程
- 记录错误日志便于排查
- 提供降级策略（直接查询数据库）

### 2. 批量操作容错
- 使用 `Promise.allSettled()` 确保部分失败不影响整体
- 详细的错误日志记录

### 3. Redis 连接异常处理
- 缓存服务具备容错能力
- 自动降级到数据库查询

## 性能优化

### 1. 缓存TTL设置
- API密钥: 5分钟（频繁验证）
- 用户信息: 30分钟（相对稳定）
- 角色权限: 10分钟（中等频率变更）

### 2. 批量操作
- 支持批量缓存更新
- 减少Redis连接开销

### 3. 缓存预热
- 提供缓存预热接口
- 支持常用数据预加载

## 监控和日志

### 1. 操作日志
- 详细记录缓存同步操作
- 包含操作类型、用户ID、时间戳

### 2. 错误日志
- 缓存操作失败详情
- 包含错误堆栈信息

### 3. 性能监控
- 缓存命中率统计
- 操作耗时监控

## 使用示例

### 1. API密钥更新后同步缓存

```typescript
// 在 ApiKeyService 中
async update(id: number, updateApiKeyDto: UpdateApiKeyDto): Promise<ApiKeyResponseDto> {
  // ... 更新数据库
  
  // 清除缓存
  await this.clearApiKeyCache(apiKey.keyHash);
  
  return this.transformToResponseDto(updatedApiKey, false);
}
```

### 2. 用户角色变更后同步缓存

```typescript
// 在 EnhancedUserService 中
async updateUser(id: number, updateUserDto: UpdateUserDto): Promise<Record<string, any>> {
  // ... 更新数据库
  
  // 如果用户类型发生变化，需要清除角色权限缓存
  if (oldData.userType !== updatedUser.userType) {
    await this.updateUserRolePermissions(updatedUser.id, 'clear');
  }
  
  return result;
}
```

### 3. 批量处理用户角色权限缓存

```typescript
// 批量更新用户角色权限缓存
await this.cacheSyncService.batchSyncUserRolePermissionCache([1, 2, 3], 'clear');
```

## 总结

通过以上完善，项目现在具备了完整的缓存同步机制：

1. ✅ **API密钥更新时自动同步缓存**
2. ✅ **用户信息更新时自动同步缓存**
3. ✅ **角色权限变更时自动清除相关缓存**
4. ✅ **事件驱动的异步缓存同步**
5. ✅ **批量操作支持**
6. ✅ **完善的容错机制**
7. ✅ **详细的日志记录**

这确保了系统在各种数据更新场景下都能保持缓存与数据库的一致性，提供了可靠的缓存管理机制。