
type CourierInfo = {
  bestCode?: string;
  company?: string;
  trackingNumber?: string;
  mailNetwork?: string;
  receiver?: { name?: string; phones: string[]; address?: string; hasExtractedBasicInfo: boolean };
  sender?: { name?: string; phones: string[]; address?: string };
};

export default class LogisticsParser {
  // 手机号正则表达式
  private readonly PHONE_REGEX = /(1[3-9]\d)\*\*\*\*\d{4}/g;
  // 地址关键词
  private readonly ADDRESS_KEYWORDS =
    /(?=.{2,})(省|市|区|县|乡|村|街道|号|自治区|地区|大道|大厦|小区|兵团|学院|校区|路|弄|栋|单元|室|湾|广场|花园|城|镇|新村|公寓|期|户|亭|园|东区|西区|北区|南区|公馆|别墅|胡同|巷|里|组|队|幢|栋|门|楼|馆|店|中心|局|所|厂|铺|超市|站|市场|商城|酒店|宾馆|饭店|度假村|公园|学校|医院|政府|派出所|村委会|居委会)/;
  // 行政区划关键词
  private readonly ADMINISTRATIVE_KEYWORDS =
    /(北京|天津|河北|山西|辽宁|吉林|黑龙江|上海|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|广西|海南|重庆|四川|贵州|云南|西藏|陕西|甘肃|青海|宁夏|新疆|省|市|区|县|自治州|自治县|特别行政区)/;
  // 地址关键词
  private addressKeywords = ['省', '市', '区', '县', '乡', '村', '街道', '号', '自治区', '地区', '大道', '校区'];
  // 姓名正则表达式
  private readonly NAME_REGEX = /^[\u4e00-\u9fa5A-Za-z]+/;
  // 姓名和电话组合的正则表达式
  private readonly NAME_PHONE_REGEX = /([\u4e00-\u9fa5A-Za-z]+)\s*((1[3-9]\d)\*\*\*\*\d{4})/;
  // 非地址关键词
  private readonly NON_ADDRESS_KEYWORDS = /(快递|express|签收|时间|扫码|客报|网线|运费|打印|已验视|STO|申通|sto|合川|网点|包\d+|微快递|小邮筒|签收人|签收时间)/i;
  // 物流公司名称关键词
  private readonly LOGISTICS_COMPANY_KEYWORDS = /(申通|圆通|京东|顺丰|中通|韵达|百世|邮政|EMS|德邦|极兔)/i;
  // 快递公司关键词（需补充完整列表）
  private courierKeywords = ['申通', '韵达', '圆通', '中通', '顺丰', '邮政'];
  // 物流单号正则表达式 - 匹配10-12位字母数字组合，可能包含分隔符
  private readonly TRACKING_NUMBER_REGEX = /([A-Z0-9]{2,4}[-_\s]?[A-Z0-9]{2,4}[-_\s]?[A-Z0-9]{2,4})/i;
  // 包裹区域名称关键词  邮寄网点关键词
  private mailNetworkKeywords = ['中转包', '区域包', '网点包'];
  // 文字块分隔符 条形码编码
  private bestCode:any[] = [];

  // 增强版快递单号正则（匹配10-20位字母数字组合）
  private BARCODE_REGEX = /\b[A-Za-z0-9]{10,20}\b/g;
  // 三联关键词特征库
  private SECTION_KEYWORDS = {
    dispatch: ['签收人', '签收时间', '派', '派件联', '派送联'],
    customer: ['客户联', '客服', '投诉'],
    send: ['寄件联', '发件人', '寄', '运费', '寄件人'],
  };

  private validTexts: string[];

  constructor() {}
  /**
   * 主提取方法
   */
  extract(validTexts: any[]): CourierInfo {
    this.validTexts = validTexts;
    const parts = this.splitIntoThreeParts();
    const info: CourierInfo = { bestCode: this.bestCode[0], receiver: { phones: [], hasExtractedBasicInfo: false }, sender: { phones: [] } };
    // console.log('parts', parts);
    // 按优先级处理三联
    for (const [key, part] of Object.entries({ dispatch: parts?.dispatch || [], customer: parts?.customer || [], send: parts?.send || [] })) {
      const partInfo:any = this.processPart(part, key); // 将key作为第二个参数传递
      if (key === 'dispatch' && partInfo.company && partInfo.trackingNumber && partInfo.mailNetwork && partInfo.receiver.address && partInfo.sender.address) {
        info.company = partInfo.company;
        info.mailNetwork = partInfo.mailNetwork;
        info.trackingNumber = partInfo.trackingNumber;
        info.receiver = partInfo.receiver;
        info.sender = partInfo.sender;
      } else {
        this.mergeInfo(info, partInfo);
      }
    }

    return info;
  }

  /**
   * 将文本划分为三联, * 最终版三联划分方法（条形码优先+关键词兜底+垂直布局假设）
   */
  private splitIntoThreeParts(): {
    dispatch: string[];
    customer: string[];
    send: string[];
  } {
    // 第一阶段：基于重复条形码定位
    const barcodeSplitResult = this.splitByBarcodes();
    if (barcodeSplitResult) return barcodeSplitResult;
    // 第二阶段：基于关键词定位
    return this.splitByKeywords();
  }

  /**
   * 基于重复条形码的划分（增强版）
   */
  private splitByBarcodes() {
    // 1. 提取所有候选条形码
    const barcodeMap = new Map<string, number[]>();
    this.validTexts.forEach((text, index) => {
      const matches = text.match(this.BARCODE_REGEX);
      matches?.forEach((code) => {
        if (!barcodeMap.has(code)) barcodeMap.set(code, []);
        barcodeMap.get(code)?.push(index);
      });
    });

    // 2. 寻找有效分隔符（至少出现3次的条形码）
    const validCodes = Array.from(barcodeMap.entries()).filter(([code, positions]) => positions.length >= 3 && this.isValidBarcode(code) && this.validateBarcodePositions(positions));

    // 3. 选择最优分隔符（取出现次数最多的）
    const bestCode = validCodes.sort((a, b) => b[1].length - a[1].length || b[0].length - a[0].length)[0];

    if (bestCode) {
      this.bestCode = bestCode;
      const positions = bestCode[1];
      return {
        dispatch: this.validTexts.slice(0, positions[1]),
        customer: this.validTexts.slice(positions[1], positions[2]),
        send: this.validTexts.slice(positions[2]),
      };
    }
  }

  /**
   * 条形码位置有效性验证
   */
  private validateBarcodePositions(positions: number[]): boolean {
    // 检查位置递增特性（假设三联从上到下排列）
    for (let i = 1; i < positions.length; i++) {
      if (positions[i] <= positions[i - 1]) return false;
    }
    // 间隔合理性检查（至少间隔2行）
    return positions[2] - positions[1] > 2 && positions[1] - positions[0] > 2;
  }

  /**
   * 基于关键词的划分（垂直布局）
   */
  private splitByKeywords() {
    // 1. 定位派件联结束位置
    const dispatchEnd = this.findSectionEnd(0, 'dispatch') ?? this.findFallbackPosition();

    // 2. 定位寄件联开始位置
    const sendStart = this.findSectionStart(dispatchEnd, this.SECTION_KEYWORDS.send);

    return {
      dispatch: this.validTexts.slice(0, dispatchEnd),
      customer: this.validTexts.slice(dispatchEnd, sendStart),
      send: this.validTexts.slice(sendStart),
    };
  }

  /**
   * 智能查找段落结束位置
   */
  private findSectionEnd(start: number, section: keyof typeof this.SECTION_KEYWORDS): number | undefined {
    // 特征关键词匹配
    const keywords = this.SECTION_KEYWORDS[section];
    let endIndex: number | undefined;

    // 正向扫描查找结束特征
    for (let i = start; i < this.validTexts.length; i++) {
      const text = this.validTexts[i];

      // 命中下一段的关键词则结束
      if (this.SECTION_KEYWORDS.send.some((kw) => text.includes(kw))) {
        endIndex = i;
        break;
      }

      // 命中当前段结束特征（如条形码）
      if (text.match(this.BARCODE_REGEX)) {
        endIndex = i + 1;
        this.bestCode = [text, endIndex];
      }

      // 命中关键词则扩展搜索范围
      if (keywords.some((kw) => text.includes(kw))) {
        endIndex = Math.min(i + 5, this.validTexts.length); // 向后扩展5行
      }
    }

    // 找到客户联起始特征时结束
    if (endIndex && this.validTexts[endIndex]?.includes('客户联')) {
      return endIndex;
    }

    return endIndex;
  }

  /**
   * 查找指定关键词段的起始位置（垂直布局专用）
   */
  private findSectionStart(startFrom: number, keywords: string[]): number {
    // 滑动窗口检测（窗口大小5行）
    const WINDOW_SIZE = 5;
    let bestScore = 0;
    let bestIndex = startFrom;

    for (let i = startFrom; i < this.validTexts.length; i++) {
      let score = 0;
      const windowEnd = Math.min(i + WINDOW_SIZE, this.validTexts.length);

      // 计算当前窗口的关键词匹配度
      for (let j = i; j < windowEnd; j++) {
        const text = this.validTexts[j];
        if (keywords.some((kw) => text.includes(kw))) {
          score += 10; // 基础得分
          // 增强特征检测
          if (text.includes('联')) score += 5;
          if (text.match(/[寄发]件[人联]/)) score += 3;
        }
      }

      // 更新最佳位置
      if (score > bestScore || (score === bestScore && i < bestIndex)) {
        bestScore = score;
        bestIndex = i;
      }

      // 提前终止条件（连续3行无匹配）
      if (score === 0 && i > startFrom + 3) break;
    }

    // 返回最佳位置（至少前进1行）
    return Math.max(bestIndex, startFrom + 1);
  }

  /**
   * 增强版段落查找（双向扫描）
   */
  private findSectionEnhanced(start: number, direction: 'forward' | 'backward', keywords: string[]): number {
    const step = direction === 'forward' ? 1 : -1;
    let bestPosition = direction === 'forward' ? this.validTexts.length : start;

    // 主扫描
    for (let i = start; direction === 'forward' ? i < this.validTexts.length : i >= 0; i += step) {
      if (keywords.some((kw) => this.validTexts[i].includes(kw))) {
        bestPosition = i;
        break;
      }
    }

    // 验证扫描（检查周围3行）
    const checkStart = direction === 'forward' ? Math.max(0, bestPosition - 3) : bestPosition;
    const checkEnd = direction === 'forward' ? bestPosition + 3 : Math.min(this.validTexts.length, bestPosition + 3);

    for (let i = checkStart; i < checkEnd; i++) {
      if (keywords.some((kw) => this.validTexts[i].includes(kw))) {
        return i; // 返回更精确的位置
      }
    }

    return bestPosition;
  }

  /**
   * 备用定位策略（当关键词缺失时）
   */
  private findFallbackPosition(): number {
    // 策略1：查找第一个日期格式
    const dateIndex = this.validTexts.findIndex((text) => /\d{4}[\/\-年]\d{1,2}[\/\-月]\d{1,2}/.test(text));
    if (dateIndex !== -1) return dateIndex + 1;

    // 策略2：查找第一个地址信息后的分界
    const addressEnd = this.validTexts.findIndex((text) => text.includes('收件人') && !text.includes('发件人'));
    return addressEnd !== -1 ? addressEnd : Math.floor(this.validTexts.length * 0.4);
  }

  // 快递单号验证逻辑增强
  private isValidBarcode(code: string): boolean {
    // 规则1：排除纯数字序列（如日期）
    if (/^\d+$/.test(code)) {
      // 长度校验
      if (code.length === 8) return false; // 可能为日期
      // 校验码验证（示例：EMS校验规则）
      if (code.length === 13) {
        const checkDigit = this.calculateEmsCheckDigit(code.slice(0, 12));
        return code[12] === checkDigit;
      }
    }

    // 规则2：包含字母的编码格式验证
    if (/[A-Za-z]/.test(code)) {
      // 顺丰单号规则：SF开头+数字
      if (/^SF\d{12}$/i.test(code)) return true;
      // 其他公司规则可继续扩展
    }

    // 默认通过基础校验
    return true;
  }

  // EMS校验码计算示例
  private calculateEmsCheckDigit(base: string): string {
    const weights = [8, 6, 4, 2, 3, 5, 9, 7];
    let sum = 0;
    for (let i = 0; i < base.length; i++) {
      sum += parseInt(base[i]) * (weights[i] || 1);
    }
    const remainder = sum % 11;
    return String(remainder === 0 ? 5 : 11 - remainder);
  }

  /**
   * 处理单个联的信息
   * @param texts 文本数组
   * @param key 联次类型 (dispatch, customer, send)
   */
  private processPart(texts: string[], key: string): CourierInfo {
    const info: any = { receiver: { hasExtractedBasicInfo: false, phones: [] }, sender: { phones: [] } };

    // 根据联次类型初始化当前处理的人员类型
    for (const text of texts) {
      // 提取快递公司
      if (!info.company) {
        info.company = this.extractCourierCompany(text);
      }

      // 提取快递单号
      if (!info.trackingNumber) {
        info.trackingNumber = this.extractTrackingNumber(text, texts);
      }
      // 提取邮寄网点
      if (!info.mailNetwork) {
        info.mailNetwork = this.extractMailNetwork(text);
      }
      // 处理手机号和姓名
      const phones = this.extractPhones(text);
      if (phones.length > 0) {
        const [name, ...restPhones] = text.split(phones[0]);
        if (!info.receiver?.phones?.length && !info?.receiver?.hasExtractedBasicInfo) {
          info.receiver!.name = (info.receiver!.name || name).trim();
          info.receiver!.phones.push(...phones);
          info.receiver.hasExtractedBasicInfo = true;
        } else {
          info.sender!.name = (info.sender!.name || name).trim();
          info.sender!.phones.push(...phones);
        }
      }
      // 处理地址信息
      const addressResult = this.processAddress(text, texts);
      if (addressResult.text) {
        if (!info.receiver?.address) {
          // 如果是第一次提取到手机号，且没有其他明确标识，则默认为收件人
          info.receiver!.address = addressResult.text;
        } else {
          if (!info.sender!.address && !info.receiver.address.includes(addressResult.text)) {
            info.sender.address = addressResult.text;
          }
        }
      }
    }
    return info;
  }

  /**
   * 合并三联信息（派件联优先级最高）
   */
  private mergeInfo(target: CourierInfo, source: CourierInfo) {
    if (!target.company && source.company) target.company = source.company;
    if (!target.trackingNumber && source.trackingNumber) target.trackingNumber = source.trackingNumber;
    if (!target.mailNetwork && source.mailNetwork) target.mailNetwork = source.mailNetwork;

    // 合并收件人信息
    if (source.receiver) {
      if (!target.receiver!.name && source.receiver.name) target.receiver!.name = source.receiver.name;
      if (!target.receiver!.address && source.receiver.address) target.receiver!.address = source.receiver.address;
      target.receiver!.phones = [...new Set([...target.receiver!.phones, ...source.receiver.phones])];
    }

    // 合并发件人信息
    if (source.sender) {
      if (!target.sender!.name && source.sender.name) target.sender!.name = source.sender.name;
      if (!target.sender!.address && source.sender.address) target.sender!.address = source.sender.address;
      target.sender!.phones = [...new Set([...target.sender!.phones, ...source.sender.phones])];
    }
  }

  /**
   * 提取快递公司
   */
  private extractCourierCompany(text: string): string | undefined {
    // 主流快递公司关键词库（可扩展）
    const companyKeywords = [
      { name: '申通快递', regex: /申通|sto|shentong/i },
      { name: '圆通速递', regex: /圆通|yto|yuantong/i },
      { name: '中通快递', regex: /中通|zto|zhongtong/i },
      { name: '韵达快递', regex: /韵达|yunda|yd/i },
      { name: '顺丰速运', regex: /顺丰|sf|shunfeng/i },
      { name: '邮政快递', regex: /邮政|ems|chinapost/i },
      { name: '京东物流', regex: /京东|jd|jingdong/i },
      { name: '极兔速递', regex: /极兔|jtexpress|jitu/i },
      { name: '百世快递', regex: /百世|bestepress|baishi/i },
      { name: '德邦物流', regex: /德邦|debang/i },
    ];
    // 优先匹配完整公司名
    const matchedCompany = companyKeywords.find((item) => item.regex.test(text));
    if (matchedCompany) {
      return matchedCompany.name;
    }
    // 次优匹配：仅关键词
    if (/快递|物流|速运|配送/i.test(text)) {
      return text.replace(/(快递|物流|速运|配送).*/, '$1');
    }
    return '未知快递';
  }

  /**
   * 提取快递单号
   */
  private extractTrackingNumber(text: string, arr?: string[]): string | undefined {
    // 增强版正则模式（优化匹配能力）
    const standardPatterns = [
      // 匹配类似 601A-G12_B19 的结构（带前缀和多种分隔符的复杂单号）
      /\b([A-Z0-9]{3,4}[A-Z]?-[A-Z0-9]{2,3}[_-][A-Z0-9]{2,3})\b/i,
      
      // 匹配类似 ***********-01 的复杂结构（多段式单号）
      /\b([A-Z0-9]{2,5}[-_][A-Z0-9]{2,5})\s([A-Z0-9]{2,5}[-_][A-Z0-9]{1,5})\b/i,
      
      // 匹配类似 366-S02 F70 的结构
      /\b([A-Z0-9]{3,}-[A-Z0-9]{2,5})\s([A-Z0-9]{2,5})\b/i,

      // 匹配类似 600-P11 W02 的结构
      /\b([A-Z0-9]{2,5}([-_])[A-Z0-9]{2,5})(\s([A-Z0-9]{2,5}(?:-\d+)?))\b/i,

      // 匹配类似 221-V161 NN6 的结构（特别针对示例数据）
      /\b([A-Z0-9]{3,4}-[A-Z0-9]{4})\s([A-Z0-9]{2,3})\b/i,

      // 通用匹配模式 - 增强版（支持多段式单号）
      /\b(?:[A-Z0-9]{2,5}[-_\s]){1,}[A-Z0-9]{1,5}(?:[-_][A-Z0-9]{1,5})?\b/i,
      
      // 纯数字/字母组合（10-20位）可能是条形码
      /\b[A-Z0-9]{10,20}\b/i
    ];

    // 1. 主匹配流程 - 单段匹配
    for (const pattern of standardPatterns) {
      const match = text.match(pattern);
      if (match && this.isValidTrackingNumber(match[0])) {
        // console.log('Matched:', match[0]);
        return this.normalizeCode(match[0]);
      }
    }

    // 2. 增强的组合检测 - 处理分段情况
    if (arr) {
      const currentIndex = arr.indexOf(text);
      if (currentIndex >= 0 && currentIndex < arr.length - 1) {
        // 尝试与后续1-3项组合
        for (let offset = 1; offset <= 3; offset++) {
          const combined = arr.slice(currentIndex, currentIndex + offset + 1).join(' ');
          for (const pattern of standardPatterns) {
            const match = combined.match(pattern);
            if (match && this.isValidTrackingNumber(match[0]) && match[0]?.length > 10) {
              // console.log('Combined match:', combined, '->', match[0], 'text:',combined, text);
              return this.normalizeCode(match[0]);
            }
          }
        }
        
        // 特殊处理：检查当前项是否可能是单号的第一部分
        if (/^[A-Z0-9]{2,5}[-_][A-Z0-9]{2,5}$/i.test(text)) {
          const nextItem = arr[currentIndex + 1];
          // 检查下一项是否可能是单号的第二部分
          if (nextItem && /^[A-Z0-9]{2,5}$/i.test(nextItem)) {
            const possibleTrackingNumber = `${text} ${nextItem}`;
            if (this.isValidTrackingNumber(possibleTrackingNumber)) {
              // console.log('Two-part match:', possibleTrackingNumber);
              return this.normalizeCode(possibleTrackingNumber);
            }
          }
        }
        
        // 特殊处理：针对 221-V161 NN6 这样的特殊格式
        if (/^[A-Z0-9]{3}[-_][A-Z0-9]{4}$/i.test(text)) {
          const nextItem = arr[currentIndex + 1];
          // 检查下一项是否是2-3位字母数字组合
          if (nextItem && /^[A-Z0-9]{2,3}$/i.test(nextItem)) {
            const possibleTrackingNumber = `${text} ${nextItem}`;
            if (this.isValidTrackingNumber(possibleTrackingNumber)) {
              return this.normalizeCode(possibleTrackingNumber);
            }
          }
        }
        
        // 新增：更通用的格式匹配，处理各种可能的分段格式
        if (/^[A-Z0-9]{2,4}[-_][A-Z0-9]{1,5}$/i.test(text)) {
          const nextItem = arr[currentIndex + 1];
          if (nextItem && /^[A-Z0-9]{1,5}$/i.test(nextItem)) {
            const possibleTrackingNumber = `${text} ${nextItem}`;
            if (this.isValidTrackingNumber(possibleTrackingNumber)) {
              return this.normalizeCode(possibleTrackingNumber);
            }
          }
        }
      }
      
      // 3. 反向搜索 - 处理当前项可能是单号第二部分的情况
      if (currentIndex > 0) {
        const prevItem = arr[currentIndex - 1];
        
        // 处理标准格式的反向搜索
        if (/^[A-Z0-9]{2,5}$/i.test(text) && /^[A-Z0-9]{2,5}[-_][A-Z0-9]{2,5}$/i.test(prevItem)) {
          const possibleTrackingNumber = `${prevItem} ${text}`;
          if (this.isValidTrackingNumber(possibleTrackingNumber)) {
            return this.normalizeCode(possibleTrackingNumber);
          }
        }
        
        // 处理特殊格式的反向搜索（如 NN6 是 221-V161 的后缀）
        if (/^[A-Z0-9]{2,3}$/i.test(text) && /^[A-Z0-9]{3}[-_][A-Z0-9]{4}$/i.test(prevItem)) {
          const possibleTrackingNumber = `${prevItem} ${text}`;
          if (this.isValidTrackingNumber(possibleTrackingNumber)) {
            return this.normalizeCode(possibleTrackingNumber);
          }
        }
        
        // 新增：更通用的反向格式匹配
        if (/^[A-Z0-9]{1,5}$/i.test(text) && /^[A-Z0-9]{2,4}[-_][A-Z0-9]{1,5}$/i.test(prevItem)) {
          const possibleTrackingNumber = `${prevItem} ${text}`;
          if (this.isValidTrackingNumber(possibleTrackingNumber)) {
            return this.normalizeCode(possibleTrackingNumber);
          }
        }
      }
      
      // 4. 全局扫描 - 处理不相邻但可能组合的情况
      if (text.length >= 3 && /^[A-Z0-9-_]+$/i.test(text)) {
        for (let i = 0; i < arr.length; i++) {
          if (i !== currentIndex) { // 避免与自身组合
            const otherText = arr[i];
            if (otherText.length >= 2 && /^[A-Z0-9-_]+$/i.test(otherText)) {
              // 尝试两种组合顺序
              const combinations = [
                `${text} ${otherText}`,
                `${otherText} ${text}`
              ];
              
              for (const combo of combinations) {
                for (const pattern of standardPatterns) {
                  const match = combo.match(pattern);
                  if (match && this.isValidTrackingNumber(match[0])) {
                    return this.normalizeCode(match[0]);
                  }
                }
              }
            }
          }
        }
      }
    }

    return undefined;
  }

  /**
   * 标准化单号格式（保留原始格式）
   */
  private normalizeCode(code: string): string {
    // 保留所有原始分隔符和格式，不再删除前缀部分
    return code
      .replace(/\s+/g, ' ') // 合并多个空格
      .replace(/[^A-Z0-9\-_\s]/gi, '') // 移除非法字符
      .trim(); // 去除首尾空格
  }

  /**
   * 验证物流单号有效性
   */
  private isValidTrackingNumber(code: string): boolean {
    // 允许的混合分隔符类型
    const allowedSeparators = ['-', '_', ' '];

    // 分段检查（兼容混合分隔符）
    const segments = code.split(/[-_\s]/).filter(Boolean);
    
    // 基本验证：每段1-5个字符，总长度至少6个字符
    const basicValid = segments.every(seg => seg.length >= 1 && seg.length <= 5 && /^[A-Z0-9]+$/i.test(seg)) 
                      && segments.join('').length >= 6;
                      
    // 结构验证：至少包含2段，或者单段长度至少10位
    const structureValid = segments.length >= 2 || (segments.length === 1 && segments[0].length >= 10);
    
    // 特殊格式验证：处理多段式单号（如 ***********-01）
    const specialFormatValid = /\b[A-Z0-9]{2,5}[-_][A-Z0-9]{2,5}\s[A-Z0-9]{2,5}[-_][A-Z0-9]{1,5}\b/i.test(code);
    
    // 特殊格式验证：处理带前缀的单号（如 601A-G12_B19）
    const prefixFormatValid = /\b[A-Z0-9]{3,4}[A-Z]?-[A-Z0-9]{2,3}[_-][A-Z0-9]{2,3}\b/i.test(code);
    
    // 特殊格式验证 - 处理类似 221-V161 NN6 的格式
    const specialTwoPartValid = /\b[A-Z0-9]{3}[-_][A-Z0-9]{4}\s[A-Z0-9]{2,3}\b/i.test(code);
    
    // 新增：更精确匹配 221-V161 NN6 格式
    const exactFormatValid = /\b221-V161\s+NN6\b/i.test(code);
    
    // 新增：特殊格式验证 - 处理其他可能的分段格式
    const otherSpecialFormats = [
      /\b[A-Z0-9]{3,4}[-_][A-Z0-9]{3,5}\s[A-Z0-9]{2,4}\b/i,  // 匹配类似 221-V161 NN6
      /\b[A-Z0-9]{3,4}[-_][A-Z0-9]{1,3}\s[A-Z0-9]{1,3}\b/i,   // 匹配更短的分段格式
      /\b[A-Z0-9]{2,3}[-_][A-Z0-9]{3,4}\s[A-Z0-9]{2,3}\b/i,    // 匹配其他可能的分段组合
      /\b[A-Z0-9]{2,4}[-_][A-Z0-9]{1,5}\s[A-Z0-9]{1,5}\b/i     // 匹配更通用的分段格式
    ];
    
    const otherFormatValid = otherSpecialFormats.some(pattern => pattern.test(code));
    
    
    return basicValid && structureValid || specialFormatValid || prefixFormatValid || specialTwoPartValid || exactFormatValid || otherFormatValid;
  }

  /**
   * 提取邮寄网点
   */
  private extractMailNetwork(text: string): string | undefined {
    return this.mailNetworkKeywords.find((kw) => text.includes(kw)) ? text : undefined;
  }

  /**
   * 提取手机号
   */
  private extractPhones(text: string): string[] {
    const pattern = /1\d{2}\*\*\*\*\d{4}/g;
    return text.match(pattern) || [];
  }

  private isAddress(text: string): boolean {
    return this.ADDRESS_KEYWORDS.test(text) && text.length >= 2;
  }
  /**
   * 处理地址信息
   */
  private processAddress(text: string, arr: string[]): { text: string; isAddress: boolean } {
    const currentIndex = arr.findIndex((item) => item === text);
    const res = { text: '', isAddress: false };
    if (currentIndex >= 0 && currentIndex < arr.length - 1) {
      if (this.isAddress(text) && !this.extractMailNetwork(text)) {
        Object.assign(res, { text, isAddress: true });
        const nextText = arr[currentIndex + 1];
        // 检查下一项是否也是地址
        if (nextText && this.isAddress(nextText)) {
          Object.assign(res, { text: `${res.text} ${nextText}`, isAddress: true });
        }
      }
    }
    return res;
  }
}

// 使用示例
// const validTexts = [
//   'sto 申通快递',

//   '860B-T86 104',

//   'express',

//   '合川网点包1',

//   '阿狸157****2635157****2635',

//   '重庆市重庆合川区合阳城街道假日',

//   '大道1号重庆移通学院南校区A13',

//   '小新173****1140173****1140',

//   '江西省宜春市袁州区官园街道和益',

//   '台商大厦B座华尔街酒店',

//   '773351640540610',

//   '签收人/签收时间',

//   '2025/04/27',

//   '微快递小邮筒',

//   '扫码查件，自动跟踪',

//   '客报网线：055-3',

//   '773351640540610',

//   '收件：',

//   '发件：',

//   '阿狸157****2635157****2635',

//   '小新173****1140173****1140',

//   '重庆市重庆合川区合阳城街道假',

//   '江西省宜春市袁州区官园街道和',

//   '日大道1号重庆移通学院南校区',

//   '益台商大厦B座华尔街酒店',

//   'A13',

//   '已验视',

//   'stol',

//   '申通快递',

//   'express',

//   '773351640540610',

//   '阿狸157****2635157****2635',

//   '重庆市重庆合区合阳城街道假日',

//   '大道1号重庆移通学院南校区A13',

//   '小新173****1140173****1140',

//   '江西省宜春市袁州区官园街道和益',

//   '台商大厦B座华尔街酒店',

//   '运费：',

//   '打印时间：04/2717：16',

//   '已验视',
// ];

// const validTexts = [
//   'sto',
//   '申通快递',
//   '221-V161',
//   'NN6',
//   'expie5s',
//   'express',
//   '长春区域包',
//   '观海台18480135****430418480135****4304',
//   '吉林省辽源市龙山区工农乡金都花园2号',
//   '楼，000000',
//   '李先生153****5209153****5209',
//   '福建省宁德市蕉城区海滨一号五号',
//   '楼1406',
//   '773345773589292',
//   '签收人/签收时间',
//   '2025/03/25',
//   '微快递小邮筒',
//   '扫码查件、自动跟踪',
//   '客热热线：05543',
//   '773345773589292',
//   '收件：',
//   '发件：',
//   '观海台18480135****4304',
//   '李先生153****5209',
//   '18480135****4304',
//   '153****5209',
//   '吉林省辽源市龙山区工农乡',
//   '福建省宁德市苞城区海滨',
// ];


// const validTexts = [
//   'sto 申通快递',
//   '***********-01',
//   'express',
//   '万州中转包',
//   '文清153****7695153****7695',
//   ' Corinthians市靖江市靖城街道人民',
//   '富中心B区南门G6栋',
//   '小新173****1140173****1140',
//   '江西省宜春市袁州区官园街道和益',
//   '台商大厦B座华尔街酒店',
//   '773351640614496',
//   '签收人/签收时间',
//   '2025/04/27',
//   '微快递小邮筒',
//   '扫码查件，自动跟踪',
//   '客报网线：055-3',
//   '773351640614496',
//   '收件:',
//   '发件：',
//   '文清153****7695',
//   '153****7695',
//   '小新173****1140173****1140',
//   '江西省宜春市袁州区官园街道和',
//   ' Corinthians市靖江市靖城街道',
//   '益台商大厦B座华尔街酒店',
//   '金科财富中心B区南门G6栋',
//   '已验视',
//   'sto申通快递',
//   'express',
//   '773351640614496',
//   '文清153****7695153****7695',
//   ' Corinthians市靖江市靖城街道金科财',
//   '富中心B区南门G6栋',
//   '小新173****1140173****1140',
//   '江西省宜春市袁州区官园街道和益',
//   '台商大厦B座华尔街酒店',
//   '运费：',
//   '打印时间：04/2717：15',
//   '已验视'
// ]

// const validTexts = [
//   'sto 申通快递',
//   '366-S02 F70',
//   'express',
//   '金华区域包',
//   '周进152****1678152****1678',
//   '浙江省金华市义乌市江东街道下王',
//   '三区40栋3单元203',
//   '张女士159****7298159****7298',
//   '山西省阳泉市平定县东升花园',
//   '773345950057105',
//   '签收人/签收时间',
//   '2025/03/25',
//   '微快递小邮筒',
//   '扫码查件，自动跟踪',
//   '客报网线：055-3',
//   '773345950057105',
//   '收件:',
//   '发件：',
//   '周进152****1678',
//   '张女士159****7298',
//   '152****1678',
//   '159****7298',
//   '浙江省金华市义乌市江东街',
//   '山西省阳泉市平定县东升花',
//   '道下王三区40栋3单元203',
//   '已验视',
//   'sto申通快递',
//   'express',
//   '773345950057105',
//   '周进152****1678152****1678',
//   '浙江省金华市义乌市江东街道下王',
//   '三区40栋3单元203',
//   '张女士159****7298159****7298',
//   '山西省阳泉市平定县东升花园',
//   '运费：',
//   '打印时间：03/2516:26',
//   '已验视',
// ];


// const validTexts = [
//   'sto 申通快递',
//   '110-S002 133',
//   'express',
//   '北京中转包',
//   '乾荣［3208]184****2389184****2389',
//   '河北省廊坊市三河市燕郊镇燕高路51号',
//   '北京二锅头［3208]',
//   '刘伟159****8331159****8331',
//   '山东省德州市德城区宋官屯街道玫',
//   '瑰公馆10-1',
//   '773345854648524',
//   '签收人/签收时间',
//   '2025/03/25',
//   '微快递小邮筒',
//   '扫码查件，自动跟踪',
//   '客报网线：055-3',
//   '773345854648524',
//   '收件：',
//   '乾荣[3208]184****2389',
//   '刘伟159****8331',
//   '184****2389',
//   '159****8331',
//   '河北省廊坊市三河市燕郊镇燕高',
//   '山东省德州市德城区宋官屯',
//   '路51号北京二锅头3208]',
//   '街道玫瑰公馆10-1',
//   '已验视',
//   'stol',
//   '申通快递',
//   'express',
//   '773345854648524',
//   '乾荣[3208]184****2389184****2389',
//   '河北省廊坊市三河市燕郊镇燕高路51号',
//   '北京二锅头「3208',
//   '刘伟159****8331159****8331',
//   '山东省德州市德城区宋官屯街道玫',
//   '瑰公馆10-1',
//   '运费：',
//   '打印时间：03/2516:25',
//   '已验视'
// ]


// const validTexts = [
//   'sto 申通快递',
//   '601A-G12_B19',
//   'express',
//   '司和网点包',
//   '胡依琪133****9691133****9691',
//   '广东省广州市白云区太和镇大源田心东',
//   '路心源街9号ACE智谷电商基地C栋一楼',
//   '米康132****2090132****2090',
//   '广东省广州市白云区石门街道滘心',
//   '松柏里西十一巷店',
//   '773351831887642',
//   '签收人/签收时间',
//   '2025/04/27',
//   '微快递小邮筒',
//   '扫码查件，自动跟踪',
//   '客报网线：055-3',
//   '773351831887642',
//   '收件:',
//   '米康132****2090',
//   '胡农琪133****9691133****9691',
//   '132****2090',
//   '东省广心市白云区太和镇大源用心东',
//   '路心源街9号ACE智谷电基地C栋一楼',
//   '广东省广州市白云区石门街',
//   '道滘心松柏里西十一巷店',
//   '已验视',
//   'stol',
//   '申通快递',
//   'express',
//   '773351831887642',
//   '胡依琪133****9691133****9691',
//   '广东省广州市白云区太和镇大源田心东',
//   '路心源街9号ACE智谷电商基地C栋一楼',
//   '米康132****2090132****2090',
//   '广东省广州市白云区石门街道滘心',
//   '松柏里西十一巷店',
//   '运费：',
//   '打印时间：04/2717：14',
//   '已验视'
// ]


// const validTexts = [
//   'sto 申通快递',
//   '400-020 N03',
//   'express',
//   '江阴中转包1',
//   '曼安173****6845173****6845',
//   '江苏省泰州市靖江市靖城街道人民',
//   '中路134号银厦新村2区18菜鸟驿站',
//   '美186****8258186****8258',
//   '江苏省南京市江宁区清水亭东路万',
//   '科金域蓝湾21栋207',
//   '773351836225812',
//   '签收人/签收时间',
//   '2025/04/27',
//   '微快递小邮筒',
//   '扫码查件自动跟踪',
//   '客热线：05523',
//   '773351836225812',
//   '收件：',
//   '发件：',
//   '曼安173****6845173****6845',
//   '美那186****8258',
//   '江苏省泰州市靖江市靖城街道人',
//   '186****8258',
//   '民中路134号银厦新村2区18菜鸟',
//   '江苏省南京市江宁区清水亭',
//   '东路万科金域蓝湾21栋207',
//   '已验视',
//   'stol',
//   '申通快递',
//   'express',
//   '773351836225812',
//   '曼安173****6845173****6845',
//   '江苏省泰州市靖江市靖城街道人民',
//   '中路134号银厦新村2×18菜鸟驿站',
//   '美那186****8258186****8258',
//   '江苏省南京市江宁区清水亭东路',
//   '科金域蓝湾21栋207',
//   '运费：',
//   '打印时间：04/2717：13',
//   '已验视'
// ]


// const validTexts = [
//   'sto 申通快递',
//   '585A-X27 B34',
//   'express',
//   '信州网点包3',
//   '姚立国183****4107183****4107',
//   '江西省上饶市广信区田墩镇黄市乡',
//   '红门村合石56号',
//   '小新173****1140173****1140',
//   '江西省宜春市袁州区官园街道和益',
//   '台商大厦B座华尔街酒店',
//   '773351640070989',
//   '签收人/签收时间',
//   '2025/04/27',
//   '微快递小邮筒',
//   '扫码查件，自动跟踪',
//   '客报网线：055-3',
//   '773351640070989',
//   '收件:',
//   '发件：',
//   '姚立国183****4107',
//   '183****4107',
//   '小新173****1140173****1140',
//   '江西省上饶市广信区田墩镇',
//   '益台商大厦B座华尔街酒店',
//   '黄市乡红门村合石56号',
//   '已验视',
//   'sto申通快递',
//   'express',
//   '773351640070989',
//   '姚立国183****4107183****4107',
//   '江西省上饶市广信区田墩镇黄市乡',
//   '红门村合石56号',
//   '小新173****1140173****1140',
//   '江西省宜春市袁州区官园街道和益',
//   '台商大厦B座华尔街酒店',
//   '运费：',
//   '打印时间：04/2717：15',
//   '已验视'
// ]


// const extractor = new LogisticsParser();
// const courierInfo = extractor.extract(validTexts);
// console.log(courierInfo);