import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { DataSource } from 'typeorm';
import request from 'supertest';
import { OrderModule } from '../order.module';
import { PaymentModule } from '../../payment/payment.module';
import { UserModule } from '../../user/user.module';
import { ServiceModule } from '../../service/service.module';
import { UserServiceModule } from '../../user-service/user-service.module';
import { AuthModule } from '../../auth/auth.module';
import { OrderEntity } from '../entities/order.entity';
import { OrderItemEntity } from '../entities/order-item.entity';
import { PaymentEntity } from '../../payment/entities/payment.entity';
import { UserEntity } from '../../user/entities/user.entity';
import { ServiceEntity } from '../../service/entities/service.entity';
import { UserServiceEntity } from '../../user-service/entities/user-service.entity';
import { OrderType, OrderStatus, PaymentMethod, PaymentStatus } from '../enums/order.enum';
import {
  getTestDatabaseConfig,
  cleanupTestDatabase,
  createTestUser,
  createTestService,
} from './order.test-config';

describe('Order Integration Tests', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let authToken: string;
  let testUser: UserEntity;
  let testService: ServiceEntity;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(getTestDatabaseConfig()),
        EventEmitterModule.forRoot(),
        OrderModule,
        PaymentModule,
        UserModule,
        ServiceModule,
        UserServiceModule,
        AuthModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    dataSource = moduleFixture.get<DataSource>(DataSource);
  });

  beforeEach(async () => {
    await cleanupTestDatabase(dataSource);
    
    // 创建测试用户
    const userRepository = dataSource.getRepository(UserEntity);
    testUser = await userRepository.save(createTestUser());
    
    // 创建测试服务
    const serviceRepository = dataSource.getRepository(ServiceEntity);
    testService = await serviceRepository.save(createTestService());
    
    // 模拟用户登录获取token
    authToken = 'mock-jwt-token';
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /orders/service', () => {
    it('应该成功创建服务订单', async () => {
      const createOrderDto = {
        serviceId: testService.id,
        quantity: 2,
        remark: '测试订单',
      };

      const response = await request(app.getHttpServer())
        .post('/orders/service')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createOrderDto)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data).toHaveProperty('orderNo');
      expect(response.body.data.orderType).toBe(OrderType.SERVICE);
      expect(response.body.data.status).toBe(OrderStatus.PENDING);
      expect(response.body.data.totalAmount).toBe(testService.unitPrice * 2);
    });

    it('当服务不存在时应该返回404', async () => {
      const createOrderDto = {
        serviceId: 999,
        quantity: 1,
        remark: '测试订单',
      };

      await request(app.getHttpServer())
        .post('/orders/service')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createOrderDto)
        .expect(404);
    });

    it('当数量无效时应该返回400', async () => {
      const createOrderDto = {
        serviceId: testService.id,
        quantity: 0,
        remark: '测试订单',
      };

      await request(app.getHttpServer())
        .post('/orders/service')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createOrderDto)
        .expect(400);
    });
  });

  describe('POST /orders/recharge', () => {
    it('应该成功创建充值订单', async () => {
      const createRechargeDto = {
        amount: 100.00,
        paymentMethod: PaymentMethod.ALIPAY,
        remark: '账户充值',
      };

      const response = await request(app.getHttpServer())
        .post('/orders/recharge')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createRechargeDto)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data).toHaveProperty('orderNo');
      expect(response.body.data.orderType).toBe(OrderType.RECHARGE);
      expect(response.body.data.totalAmount).toBe(100.00);
    });

    it('当充值金额无效时应该返回400', async () => {
      const createRechargeDto = {
        amount: 0.5,
        paymentMethod: PaymentMethod.ALIPAY,
        remark: '账户充值',
      };

      await request(app.getHttpServer())
        .post('/orders/recharge')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createRechargeDto)
        .expect(400);
    });
  });

  describe('GET /orders', () => {
    it('应该返回用户的订单列表', async () => {
      // 先创建一个订单
      const orderRepository = dataSource.getRepository(OrderEntity);
      await orderRepository.save({
        userId: testUser.id,
        orderType: OrderType.SERVICE,
        status: OrderStatus.PENDING,
        totalAmount: 100.00,
        remark: '测试订单',
      });

      const response = await request(app.getHttpServer())
        .get('/orders')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('items');
      expect(response.body.data).toHaveProperty('meta');
      expect(Array.isArray(response.body.data.items)).toBe(true);
    });

    it('应该支持分页查询', async () => {
      const response = await request(app.getHttpServer())
        .get('/orders?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.meta.page).toBe(1);
      expect(response.body.data.meta.limit).toBe(10);
    });
  });

  describe('GET /orders/:id', () => {
    it('应该返回订单详情', async () => {
      // 创建测试订单
      const orderRepository = dataSource.getRepository(OrderEntity);
      const order = await orderRepository.save({
        userId: testUser.id,
        orderType: OrderType.SERVICE,
        status: OrderStatus.PENDING,
        totalAmount: 100.00,
        remark: '测试订单',
      });

      const response = await request(app.getHttpServer())
        .get(`/orders/${order.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(order.id);
      expect(response.body.data.userId).toBe(testUser.id);
    });

    it('当订单不存在时应该返回404', async () => {
      await request(app.getHttpServer())
        .get('/orders/999')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('POST /orders/:id/cancel', () => {
    it('应该成功取消待支付订单', async () => {
      // 创建待支付订单
      const orderRepository = dataSource.getRepository(OrderEntity);
      const order = await orderRepository.save({
        userId: testUser.id,
        orderType: OrderType.SERVICE,
        status: OrderStatus.PENDING,
        totalAmount: 100.00,
        remark: '测试订单',
      });

      const response = await request(app.getHttpServer())
        .post(`/orders/${order.id}/cancel`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe(OrderStatus.CANCELLED);
    });

    it('当订单已支付时应该返回400', async () => {
      // 创建已支付订单
      const orderRepository = dataSource.getRepository(OrderEntity);
      const order = await orderRepository.save({
        userId: testUser.id,
        orderType: OrderType.SERVICE,
        status: OrderStatus.PAID,
        totalAmount: 100.00,
        remark: '测试订单',
      });

      await request(app.getHttpServer())
        .post(`/orders/${order.id}/cancel`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);
    });
  });

  describe('GET /orders/recharge/config', () => {
    it('应该返回充值配置', async () => {
      const response = await request(app.getHttpServer())
        .get('/orders/recharge/config')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('presetAmounts');
      expect(response.body.data).toHaveProperty('minAmount');
      expect(response.body.data).toHaveProperty('maxAmount');
      expect(response.body.data).toHaveProperty('promotions');
    });
  });

  describe('完整的订单支付流程', () => {
    it('应该完成完整的服务购买流程', async () => {
      // 1. 创建服务订单
      const createOrderDto = {
        serviceId: testService.id,
        quantity: 1,
        remark: '集成测试订单',
      };

      const orderResponse = await request(app.getHttpServer())
        .post('/orders/service')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createOrderDto)
        .expect(201);

      const order = orderResponse.body.data;

      // 2. 创建支付
      const createPaymentDto = {
        orderId: order.id,
        paymentMethod: PaymentMethod.BALANCE,
        amount: order.totalAmount,
      };

      const paymentResponse = await request(app.getHttpServer())
        .post('/payments')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createPaymentDto)
        .expect(201);

      const payment = paymentResponse.body.data;

      // 3. 模拟支付成功回调
      const callbackData = {
        paymentNo: payment.paymentNo,
        thirdPartyNo: payment.thirdPartyNo || 'mock_third_party_no',
        status: 'success',
        amount: payment.amount,
      };

      await request(app.getHttpServer())
        .post(`/payments/callback/${PaymentMethod.BALANCE}`)
        .send(callbackData)
        .expect(200);

      // 4. 验证订单状态已更新
      const updatedOrderResponse = await request(app.getHttpServer())
        .get(`/orders/${order.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(updatedOrderResponse.body.data.status).toBe(OrderStatus.PAID);
    });

    it('应该完成完整的充值流程', async () => {
      // 1. 创建充值订单
      const createRechargeDto = {
        amount: 100.00,
        paymentMethod: PaymentMethod.BALANCE,
        remark: '集成测试充值',
      };

      const orderResponse = await request(app.getHttpServer())
        .post('/orders/recharge')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createRechargeDto)
        .expect(201);

      const order = orderResponse.body.data;

      // 2. 创建支付
      const createPaymentDto = {
        orderId: order.id,
        paymentMethod: PaymentMethod.ALIPAY,
        amount: order.totalAmount,
      };

      const paymentResponse = await request(app.getHttpServer())
        .post('/payments')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createPaymentDto)
        .expect(201);

      expect(paymentResponse.body.data).toHaveProperty('paymentUrl');
      expect(paymentResponse.body.data).toHaveProperty('qrCode');
    });
  });
});
