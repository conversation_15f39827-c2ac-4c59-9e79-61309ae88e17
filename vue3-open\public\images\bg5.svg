<svg width="300" height="150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="abstractGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="abstractGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:0.8" />
    </linearGradient>
    <radialGradient id="circleGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:0.6" />
    </radialGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="300" height="150" fill="url(#abstractGradient1)"/>
  
  <!-- 大型几何形状 -->
  <polygon points="0,0 80,0 60,60 0,40" fill="url(#abstractGradient2)"/>
  <polygon points="300,150 220,150 240,90 300,110" fill="url(#abstractGradient2)"/>
  
  <!-- 圆形元素 -->
  <circle cx="50" cy="80" r="25" fill="url(#circleGradient)"/>
  <circle cx="200" cy="40" r="20" fill="url(#circleGradient)" opacity="0.7"/>
  <circle cx="250" cy="100" r="30" fill="url(#circleGradient)" opacity="0.5"/>
  
  <!-- 三角形 -->
  <polygon points="100,30 130,30 115,60" fill="#ff6b6b" opacity="0.8"/>
  <polygon points="160,80 190,80 175,110" fill="#4ecdc4" opacity="0.8"/>
  <polygon points="80,120 110,120 95,150" fill="#45b7d1" opacity="0.8"/>
  
  <!-- 矩形 -->
  <rect x="120" y="50" width="40" height="20" fill="#96ceb4" opacity="0.7" transform="rotate(15 140 60)"/>
  <rect x="180" y="100" width="35" height="25" fill="#ffeaa7" opacity="0.7" transform="rotate(-20 197.5 112.5)"/>
  <rect x="30" y="100" width="30" height="15" fill="#fd79a8" opacity="0.7" transform="rotate(30 45 107.5)"/>
  
  <!-- 线条装饰 -->
  <line x1="0" y1="70" x2="100" y2="50" stroke="#ffffff" stroke-width="2" opacity="0.6"/>
  <line x1="150" y1="20" x2="250" y2="70" stroke="#ffffff" stroke-width="2" opacity="0.6"/>
  <line x1="200" y1="130" x2="300" y2="80" stroke="#ffffff" stroke-width="2" opacity="0.6"/>
  
  <!-- 小装饰圆点 -->
  <circle cx="70" cy="40" r="3" fill="#ffffff" opacity="0.8"/>
  <circle cx="140" cy="90" r="4" fill="#ffffff" opacity="0.7"/>
  <circle cx="220" cy="60" r="3.5" fill="#ffffff" opacity="0.8"/>
  <circle cx="90" cy="130" r="2.5" fill="#ffffff" opacity="0.9"/>
  <circle cx="260" cy="30" r="3" fill="#ffffff" opacity="0.8"/>
  
  <!-- 波浪线 -->
  <path d="M 0 100 Q 25 90 50 100 T 100 100" stroke="#a29bfe" stroke-width="3" fill="none" opacity="0.7"/>
  <path d="M 150 130 Q 175 120 200 130 T 250 130" stroke="#fd79a8" stroke-width="3" fill="none" opacity="0.7"/>
  
  <!-- 星形 -->
  <polygon points="180,25 183,32 190,32 185,37 187,44 180,40 173,44 175,37 170,32 177,32" fill="#fdcb6e" opacity="0.8"/>
  <polygon points="120,100 123,107 130,107 125,112 127,119 120,115 113,119 115,112 110,107 117,107" fill="#e17055" opacity="0.8"/>
  
  <!-- 六边形 -->
  <polygon points="240,80 250,75 260,80 260,90 250,95 240,90" fill="#00b894" opacity="0.7"/>
  <polygon points="60,110 70,105 80,110 80,120 70,125 60,120" fill="#e84393" opacity="0.7"/>
  
  <!-- 椭圆 -->
  <ellipse cx="150" cy="70" rx="15" ry="8" fill="#6c5ce7" opacity="0.6" transform="rotate(45 150 70)"/>
  <ellipse cx="280" cy="120" rx="12" ry="6" fill="#a29bfe" opacity="0.6" transform="rotate(-30 280 120)"/>
</svg>