<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-header">
        <h1 class="login-title">登录您的账号</h1>
        <p class="login-subtitle">欢迎回来，请输入您的登录信息</p>
      </div>
      
      <!-- 登录方式切换 -->
      <div class="login-type-switch">
        <el-radio-group v-model="loginMode" class="login-type-group">
          <el-radio-button label="password">账号登录</el-radio-button>
          <el-radio-button label="sms">手机快捷登录</el-radio-button>
        </el-radio-group>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        size="large"
        @submit.prevent="handleLogin"
      >
        <!-- 密码登录表单 -->
        <template v-if="loginMode === 'password'">
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="手机或邮箱"
              :prefix-icon="User"
              clearable
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              :prefix-icon="Lock"
              show-password
              clearable
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          
          <el-form-item>
            <div class="login-options">
              <el-checkbox v-model="loginForm.rememberMe">
                记住我
              </el-checkbox>
              <router-link to="/forgot-password" class="forgot-password-link">
                忘记密码？
              </router-link>
            </div>
          </el-form-item>
        </template>
        
        <!-- 手机快捷登录表单 -->
        <template v-else>
          <el-form-item prop="phone">
            <el-input
              v-model="loginForm.phone"
              placeholder="请输入手机号"
              :prefix-icon="Phone"
              clearable
              maxlength="11"
            />
          </el-form-item>
          
          <el-form-item prop="smsCode">
            <div class="sms-code-container">
              <el-input
                v-model="loginForm.smsCode"
                placeholder="请输入短信验证码"
                :prefix-icon="Message"
                clearable
                maxlength="6"
                @keyup.enter="handleLogin"
              />
              <el-button
                type="primary"
                class="send-sms-btn"
                :disabled="!canSendSms || smsCountdown > 0"
                :loading="smsSending"
                @click="sendSmsCodeHandler"
              >
                {{ smsCountdown > 0 ? `${smsCountdown}s` : '发送验证码' }}
              </el-button>
            </div>
          </el-form-item>
        </template>
        
        <el-form-item>
          <el-button
            type="primary"
            class="login-button"
            :loading="loading"
            :disabled="loading"
            @click="handleLogin"
          >
            {{ loading ? (loginMode === 'password' ? '登录中...' : '验证登录中...') : (loginMode === 'password' ? '登录' : '验证并登录') }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-footer">
        <p class="register-link">
          还没有账号？
          <router-link to="/register" class="link">
            立即注册
          </router-link>
        </p>
        
        <!-- <div class="terms-privacy">
          登录即表示您同意我们的
          <a href="#" @click.prevent="showTerms" class="link">服务条款</a>
          和
          <a href="#" @click.prevent="showPrivacy" class="link">隐私政策</a>
        </div> -->
        
        <div class="divider">
          <span>或</span>
        </div>
        
        <div class="social-login">
          <el-button class="social-button wechat-button" @click="handleSocialLogin('wechat')">
            <el-icon><svg viewBox="0 0 1024 1024"><path d="M692.8 499.2c0-132.267-132.267-239.467-294.4-239.467S104 366.933 104 499.2c0 73.6 40.533 140.8 104 187.733L179.2 768l98.133-49.067c32.533 6.4 59.733 12.8 91.733 12.8 19.2 0 32.533 0 51.2-6.4-6.4-25.6-12.8-51.2-12.8-78.933 0-119.467 104-216.533 238.933-216.533 12.8 0 25.6 0 38.4 6.4 6.4-12.8 6.4-25.6 6.4-38.4zM499.2 371.2c19.2 0 32.533 12.8 32.533 32.533s-12.8 32.533-32.533 32.533-32.533-12.8-32.533-32.533 12.8-32.533 32.533-32.533z m-198.4 65.067c-19.2 0-32.533-12.8-32.533-32.533s12.8-32.533 32.533-32.533 32.533 12.8 32.533 32.533-12.8 32.533-32.533 32.533z m518.4 198.4c0-104-91.733-187.733-198.4-187.733s-198.4 83.733-198.4 187.733 91.733 187.733 198.4 187.733c25.6 0 51.2-6.4 78.933-12.8l78.933 38.4-25.6-65.067c51.2-38.4 85.333-91.733 85.333-147.2z m-262.4-32.533c-12.8 0-25.6-12.8-25.6-25.6s12.8-25.6 25.6-25.6 25.6 12.8 25.6 25.6-12.8 25.6-25.6 25.6z m132.267 0c-12.8 0-25.6-12.8-25.6-25.6s12.8-25.6 25.6-25.6 25.6 12.8 25.6 25.6-12.8 25.6-25.6 25.6z"/></svg></el-icon>
            微信登录
          </el-button>

          <el-button class="social-button github-button" @click="handleSocialLogin('github')">
            <el-icon><svg viewBox="0 0 1024 1024"><path d="M512 12.64c-282.752 0-512 229.216-512 512 0 226.208 146.688 418.144 350.08 485.824 25.6 4.736 35.008-11.104 35.008-24.64 0-12.192-0.48-52.544-0.704-95.328-142.464 30.976-172.512-60.416-172.512-60.416-23.296-59.168-56.832-74.912-56.832-74.912-46.464-31.776 3.52-31.136 3.52-31.136 51.392 3.616 78.464 52.768 78.464 52.768 45.664 78.272 119.776 55.648 148.992 42.56 4.576-33.088 17.856-55.68 32.512-68.48-113.728-12.928-233.28-56.864-233.28-253.024 0-55.904 19.936-101.568 52.672-137.408-5.312-12.896-22.848-64.96 4.96-135.488 0 0 42.88-13.76 140.8 52.48 40.832-11.36 84.64-17.024 128.16-17.248 43.488 0.192 87.328 5.888 128.256 17.248 97.728-66.24 140.64-52.48 140.64-52.48 27.872 70.528 10.336 122.592 5.024 135.488 32.832 35.84 52.608 81.504 52.608 137.408 0 196.64-119.776 239.936-233.792 252.64 18.368 15.904 34.72 47.04 34.72 94.816 0 68.512-0.608 123.648-0.608 140.512 0 13.632 9.216 29.6 35.168 24.576C877.472 942.08 1024 750.208 1024 524.64c0-282.784-229.248-512-512-512z"/></svg></el-icon>
            GitHub 登录
          </el-button>

          <el-button class="social-button google-button" @click="handleSocialLogin('google')">
            <el-icon><svg viewBox="0 0 1024 1024"><path d="M881 442.4H519.7v148.5h206.4c-8.9 48-35.9 88.6-76.6 115.8-34.4 23-78.3 36.6-129.9 36.6-99.9 0-184.4-67.5-214.6-158.2-7.6-23-12-47.6-12-72.9s4.4-49.9 12-72.9c30.3-90.6 114.8-158.1 214.6-158.1 56.3 0 106.8 19.4 146.6 57.4l110-110.1c-66.5-62-153.2-100-256.6-100-149.9 0-279.6 86.8-342.7 213.1C59.2 295.6 51.7 357.9 51.7 512s7.5 216.4 40.1 295.9c63.1 126.3 192.8 213.1 342.7 213.1 118.3 0 218.8-38.9 291.1-115C779.1 842.3 827.2 773.3 827.2 512c0-30.9-2.6-60.7-7.9-89.6z"/></svg></el-icon>
            Google 登录
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>
    
    <!-- 弹窗验证码 -->
    <CaptchaModal
      v-model="showCaptchaModal"
      :loading="loading"
      @success="onCaptchaSuccess"
      @cancel="onCaptchaCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Lock, Phone, Message } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { useOAuthStore } from '@/stores/oauth'
import type { LoginForm } from '@/stores/user'
import CaptchaModal from '@/components/CaptchaModal.vue'
import type { DeviceFingerprint } from '@/utils/deviceFingerprint'
import type { BehaviorPattern } from '@/utils/behaviorTracker'
import { request } from '@/utils/request'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const oauthStore = useOAuthStore()

const loading = ref(false)
const loginFormRef = ref<FormInstance>()
const showCaptchaModal = ref(false)
const isCaptchaVerified = ref(false)
const securityLevel = ref('low')

// 登录模式：password（密码登录）或 sms（短信登录）
const loginMode = ref<'password' | 'sms'>('password')

// 短信验证码相关
const smsSending = ref(false)
const smsCountdown = ref(0)
let smsTimer: number | null = null

// 安全验证数据
const securityData = ref<{
  deviceFingerprint?: DeviceFingerprint
  behaviorPattern?: BehaviorPattern
  verificationTimestamp?: number
  humanLikelihood?: number
}>({})

// 登录表单
const loginForm = reactive<LoginForm & { phone?: string; smsCode?: string }>({
  username: '',
  password: '',
  phone: '',
  smsCode: '',
  rememberMe: false
})

// 表单验证规则
const loginRules = computed((): FormRules => {
  if (loginMode.value === 'password') {
    return {
      username: [
        { required: true, message: '请输入手机或邮箱', trigger: 'blur' },
        { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
      ]
    }
  } else {
    return {
      phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号', trigger: 'blur' }
      ],
      smsCode: [
        { required: true, message: '请输入短信验证码', trigger: 'blur' },
        { pattern: /^\d{6}$/, message: '验证码必须是6位数字', trigger: 'blur' }
      ]
    }
  }
})

// 是否可以发送短信
const canSendSms = computed(() => {
  return loginForm.phone && /^1[3-9]\d{9}$/.test(loginForm.phone)
})

// 发送短信验证码
const sendSmsCodeHandler = async () => {
  if (!canSendSms.value) {
    ElMessage.warning('请输入有效的手机号')
    return
  }
  
  try {
    smsSending.value = true
    const securityVerification = {
      level: 2,
      behaviorPattern: { verified: true },
      verificationTimestamp: Date.now(),
      type: 'behavior'
    }
    await userStore.sendSmsCode(loginForm.phone!, 'login', securityVerification)
    ElMessage.success('验证码已发送')
    
    // 开始倒计时
    smsCountdown.value = 60
    smsTimer = setInterval(() => {
      smsCountdown.value--
      if (smsCountdown.value <= 0) {
        if (smsTimer) {
          clearInterval(smsTimer)
        }
        smsTimer = null
      }
    }, 1000)
  } catch (error: any) {
    ElMessage.error(error.message || '发送验证码失败')
  } finally {
    smsSending.value = false
  }
}

// 验证码相关方法
const onCaptchaSuccess = (result: {
  deviceFingerprint?: DeviceFingerprint
  behaviorPattern?: BehaviorPattern
  verificationTimestamp: number
  humanLikelihood: number
  securityLevel: string
}) => {
  isCaptchaVerified.value = true
  securityLevel.value = result.securityLevel
  
  // 保存安全验证数据
  securityData.value = {
    deviceFingerprint: result.deviceFingerprint,
    behaviorPattern: result.behaviorPattern,
    verificationTimestamp: result.verificationTimestamp,
    humanLikelihood: result.humanLikelihood
  }
  
  console.log('弹窗验证成功，开始登录:', {
    securityLevel: securityLevel.value,
    humanLikelihood: result.humanLikelihood,
    result
  })

  // 验证成功后根据登录模式执行不同的登录逻辑
  if (loginMode.value === 'sms') {
    performSmsLogin()
  } else {
    performLogin()
  }
}

// 弹窗取消回调
const onCaptchaCancel = () => {
  isCaptchaVerified.value = false
  securityLevel.value = 'low'
  securityData.value = {}
  showCaptchaModal.value = false
  loading.value = false
  ElMessage.info('已取消验证')
}

// 处理登录 - 根据登录模式执行不同逻辑
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()

    // 无论是密码登录还是短信登录，都需要先进行滑动验证
    showCaptchaModal.value = true
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

// 短信登录/注册逻辑（支持静默注册）
const performSmsLogin = async () => {
  if (!isCaptchaVerified.value) {
    ElMessage.warning('请先完成安全验证')
    return
  }

  try {
    loading.value = true

    // 构建短信登录数据，使用真实的滑动验证结果
    const loginData:any = {
      phone: loginForm.phone!,
      smsCode: loginForm.smsCode!,
      isSilentRegister: true, // 启用静默注册
      securityVerification: {
        level: securityLevel.value === 'high' ? 3 : securityLevel.value === 'medium' ? 2 : 1,
        behaviorPattern: securityData.value.behaviorPattern,
        verificationTimestamp: securityData.value.verificationTimestamp,
        type: 'slide' // 使用滑动验证类型
      },
      rememberMe: loginForm.rememberMe
    }

    console.log('发送登录请求数据:', JSON.stringify(loginData, null, 2))

    // 使用统一的登录接口，后端会自动处理静默注册
    await userStore.login(loginData)
    
    ElMessage.success('登录成功')
    
    // 重定向到原来要访问的页面或控制台
    const redirect = route.query.redirect as string
    router.push(redirect || '/console')
  } catch (error: any) {
    console.error('短信登录失败:', error)
    ElMessage.error(error.message || '登录失败，请检查验证码')

    // 登录失败时重置验证状态
    showCaptchaModal.value = false
    securityData.value = {}
    isCaptchaVerified.value = false
    securityLevel.value = 'low'
  } finally {
    loading.value = false
  }
}

// 检查输入是否为邮箱格式
const isEmail = (input: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(input)
}

// 执行实际的登录逻辑（密码登录）
const performLogin = async () => {
  if (!isCaptchaVerified.value) {
    ElMessage.warning('请先完成安全验证')
    return
  }
  
  try {
    loading.value = true
    
    // 判断用户输入的是邮箱还是用户名
    const inputValue = loginForm.username || ''
    const isEmailInput = isEmail(inputValue)
    
    // 构建登录数据，根据输入类型设置相应字段
    const loginData: any = {
      password: loginForm.password,
      rememberMe: loginForm.rememberMe,
      securityVerification: {
        type: 'slide', // 添加验证类型
        level: securityLevel.value === 'high' ? 3 : securityLevel.value === 'medium' ? 2 : 1,
        behaviorPattern: securityData.value.behaviorPattern,
        verificationTimestamp: securityData.value.verificationTimestamp
      }
    }
    
    // 根据输入类型添加相应字段
    if (isEmailInput) {
      loginData.email = inputValue
    } else {
      loginData.username = inputValue
    }
    
    await userStore.login(loginData)
    
    ElMessage.success('登录成功')
    
    // 重定向到原来要访问的页面或控制台
    const redirect = route.query.redirect as string
    router.push(redirect || '/console')
  } catch (error: any) {
    console.error('登录失败:', error)
    ElMessage.error(error.message || '登录失败，请检查用户名和密码')
    // 登录失败时重置验证状态
    showCaptchaModal.value = false
    // 清空安全验证数据
    securityData.value = {}
    isCaptchaVerified.value = false
    securityLevel.value = 'low'
  } finally {
    loading.value = false
  }
}



// 第三方登录
const handleSocialLogin = async (provider: string) => {
  try {
    loading.value = true

    // 获取第三方登录授权URL并跳转
    const authUrl = oauthStore.getOAuthUrl(provider)

    // 保存当前页面信息，用于登录成功后跳转
    const redirectUrl = route.query.redirect as string || '/console'
    localStorage.setItem('oauth_redirect', redirectUrl)
    localStorage.setItem('oauth_provider', provider)

    // 跳转到第三方授权页面
    window.location.href = authUrl

  } catch (error: any) {
    ElMessage.error(`${provider} 登录失败: ${error.message}`)
    loading.value = false
  }
}

// 微信登录处理
const handleWechatLogin = async () => {
  try {
    // 检查是否在微信环境中
    if (isWechatBrowser()) {
      // 在微信浏览器中，直接跳转到微信授权
      await redirectToWechatAuth('login')
    } else {
      // 在非微信环境中，显示二维码
      await showWechatQRCode('login')
    }
  } catch (error: any) {
    ElMessage.error(`微信登录失败: ${error.message}`)
  }
}

// 检查是否在微信浏览器中
const isWechatBrowser = (): boolean => {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('micromessenger')
}

// 跳转到微信授权页面
const redirectToWechatAuth = async (type: 'login' | 'register') => {
  const appId = import.meta.env.VITE_WECHAT_APP_ID
  const redirectUri = encodeURIComponent(`${window.location.origin}/auth/wechat/callback?type=${type}`)
  const state = Math.random().toString(36).substring(2, 15)
  
  // 保存state到localStorage用于验证
  localStorage.setItem('wechat_auth_state', state)
  
  const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_userinfo&state=${state}#wechat_redirect`
  
  window.location.href = authUrl
}

// 显示微信二维码
const showWechatQRCode = async (type: 'login' | 'register') => {
  try {
    // 调用后端API生成微信登录二维码
    const response = await fetch('/open/v1/auth/wechat/qrcode', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ type })
    })
    
    const data = await response.json()
    
    if (data.success) {
      // 显示二维码弹窗
      await ElMessageBox({
        title: type === 'login' ? '微信扫码登录' : '微信扫码注册',
        message: `
          <div style="text-align: center;">
            <img src="${data.qrCodeUrl}" alt="微信二维码" style="width: 200px; height: 200px;" />
            <p style="margin-top: 10px; color: #666;">请使用微信扫描二维码完成${type === 'login' ? '登录' : '注册'}</p>
          </div>
        `,
        dangerouslyUseHTMLString: true,
        showCancelButton: true,
        confirmButtonText: type === 'login' ? '已完成登录' : '已完成注册',
        cancelButtonText: '取消',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // 检查登录状态
            checkWechatAuthStatus(data.ticket, type)
          }
          done()
        }
      })
      
      // 开始轮询检查登录状态
      startPollingAuthStatus(data.ticket, type)
    } else {
      throw new Error(data.message || '生成二维码失败')
    }
  } catch (error: any) {
    throw new Error(error.message || '显示微信二维码失败')
  }
}

// 轮询检查认证状态
const startPollingAuthStatus = (ticket: string, type: 'login' | 'register') => {
  const pollInterval = setInterval(async () => {
    try {
      const success = await checkWechatAuthStatus(ticket, type)
      if (success) {
        clearInterval(pollInterval)
      }
    } catch (error) {
      clearInterval(pollInterval)
    }
  }, 2000) // 每2秒检查一次
  
  // 5分钟后停止轮询
  setTimeout(() => {
    clearInterval(pollInterval)
  }, 300000)
}

// 检查微信认证状态
const checkWechatAuthStatus = async (ticket: string, type: 'login' | 'register'): Promise<boolean> => {
  try {
    const response = await fetch(`/open/v1/auth/wechat/status?ticket=${ticket}&type=${type}`)
    const data = await response.json()
    
    if (data.success && data.status === 'success') {
      // 认证成功，处理登录或注册
      if (type === 'login') {
        userStore.token = data.token
        userStore.userInfo = data.user
        ElMessage.success('微信登录成功')
        
        // 重定向到原来要访问的页面或控制台
        const redirect = route.query.redirect as string
        router.push(redirect || '/console')
      } else {
        ElMessage.success('微信注册成功，请登录')
        router.push('/login')
      }
      return true
    } else if (data.status === 'waiting') {
      // 还在等待扫码
      return false
    } else {
      // 认证失败或取消
      throw new Error(data.message || '微信认证失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '检查认证状态失败')
    throw error
  }
}

// 显示服务条款
const showTerms = async () => {
  try {
    const response = await fetch('/docs/terms-of-service.md')
    const content = await response.text()
    
    // 将Markdown转换为HTML显示
    const htmlContent = content
      .replace(/^# (.+)$/gm, '<h1>$1</h1>')
      .replace(/^## (.+)$/gm, '<h2>$1</h2>')
      .replace(/^### (.+)$/gm, '<h3>$1</h3>')
      .replace(/^#### (.+)$/gm, '<h4>$1</h4>')
      .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.+?)\*/g, '<em>$1</em>')
      .replace(/^- (.+)$/gm, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')
      .replace(/\n/g, '<br>')
    
    ElMessageBox({
      title: '服务条款',
      message: `<div style="max-height: 400px; overflow-y: auto; text-align: left; line-height: 1.6;">${htmlContent}</div>`,
      dangerouslyUseHTMLString: true,
      confirmButtonText: '我已阅读',
      showCancelButton: false,
      customClass: 'terms-dialog'
    })
  } catch (error) {
    ElMessageBox.alert(
      '服务条款加载失败，请稍后重试',
      '提示',
      {
        confirmButtonText: '确定',
        type: 'warning'
      }
    )
  }
}

// 显示隐私政策
const showPrivacy = async () => {
  try {
    const response = await fetch('/docs/privacy-policy.md')
    const content = await response.text()
    
    // 将Markdown转换为HTML显示
    const htmlContent = content
      .replace(/^# (.+)$/gm, '<h1>$1</h1>')
      .replace(/^## (.+)$/gm, '<h2>$1</h2>')
      .replace(/^### (.+)$/gm, '<h3>$1</h3>')
      .replace(/^#### (.+)$/gm, '<h4>$1</h4>')
      .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.+?)\*/g, '<em>$1</em>')
      .replace(/^- (.+)$/gm, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')
      .replace(/\n/g, '<br>')
    
    ElMessageBox({
      title: '隐私政策',
      message: `<div style="max-height: 400px; overflow-y: auto; text-align: left; line-height: 1.6;">${htmlContent}</div>`,
      dangerouslyUseHTMLString: true,
      confirmButtonText: '我已阅读',
      showCancelButton: false,
      customClass: 'privacy-dialog'
    })
  } catch (error) {
    ElMessageBox.alert(
      '隐私政策加载失败，请稍后重试',
      '提示',
      {
        confirmButtonText: '确定',
        type: 'warning'
      }
    )
  }
}

// 组件销毁时清理定时器
onUnmounted(() => {
  if (smsTimer) {
    clearInterval(smsTimer)
    smsTimer = null
  }
})
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.login-container {
  width: 100%;
  max-width: 420px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  z-index: 1;
  margin: 20px auto;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-mode-switch {
  margin-bottom: 24px;
}

.mode-group {
  width: 100%;
  display: flex;
}

.mode-group :deep(.el-radio-button) {
  flex: 1;
}

.mode-group :deep(.el-radio-button__inner) {
  width: 100%;
  border-radius: 8px;
}

.sms-input-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.sms-input-group :deep(.el-input) {
  flex: 1;
}

.sms-button {
  flex-shrink: 0;
  min-width: 100px;
  height: 40px;
}

.logo {
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  color: #409eff;
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 20px;
}

.logo-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 10px 0;
}

.login-subtitle {
  color: #606266;
  margin: 0 0 30px 0;
  font-size: 14px;
}

.login-form {
  margin-bottom: 24px;
}

.login-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.login-form :deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 0 0 1px #dcdfe6;
  transition: all 0.3s ease;
}

.login-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc;
}

.login-form :deep(.el-input.is-focus .el-input__wrapper) {
  box-shadow: 0 0 0 1px #409eff;
}

.login-form :deep(.el-input__inner) {
  height: 40px;
  font-size: 14px;
  color: #606266;
}

/* 登录方式切换样式 */
.login-type-switch {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
}

.login-type-group {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.login-type-group :deep(.el-radio-button__inner) {
  border: none;
  background: transparent;
  color: #606266;
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.login-type-group :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background: #409eff;
  color: white;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.login-type-group :deep(.el-radio-button__inner:hover) {
  color: #409eff;
}

/* 短信验证码容器样式 */
.sms-code-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.sms-code-container .el-input {
  flex: 1;
}

.send-sms-btn {
  flex-shrink: 0;
  min-width: 120px;
  height: 40px;
  font-size: 14px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.send-sms-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.send-sms-btn:disabled {
  background-color: #c0c4cc !important;
  border-color: #c0c4cc !important;
  color: #ffffff !important;
  cursor: not-allowed;
  opacity: 0.8;
}

.send-sms-btn:disabled:hover {
  background-color: #c0c4cc !important;
  border-color: #c0c4cc !important;
  color: #ffffff !important;
}

/* 登录选项 */
.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  width:100%;
}

.login-options :deep(.el-checkbox__label) {
  font-size: 14px;
  color: #606266;
}

.login-options :deep(.el-link) {
  font-size: 14px;
}

.forgot-password-link {
  color: #409eff;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password-link:hover {
  color: #66b1ff;
  text-decoration: underline;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s;
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.login-footer {
  text-align: center;
}

.register-link {
  color: #606266;
  font-size: 14px;
  margin: 0 0 20px 0;
}

.link {
  color: #409eff;
  text-decoration: none;
  font-weight: 500;
}

.link:hover {
  text-decoration: underline;
}

.divider {
  position: relative;
  margin: 20px 0;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e4e7ed;
  z-index: 0;
}

.divider span {
  background: rgba(255, 255, 255, 0.95);
  padding: 0 15px;
  position: relative;
  z-index: 1;
}

.social-login {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.social-button {
  width: 100%;
  height: 40px;
  border: 1px solid #e4e7ed;
  background: white;
  color: #606266;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.social-button:not(:first-child) {
    margin-left: 0;
  }

.social-button:hover {
  border-color: #409eff;
  color: #409eff;
}

.social-button .el-icon {
  font-size: 18px;
}

.social-button .el-icon svg {
  width: 18px;
  height: 18px;
  fill: currentColor;
}

.wechat-button {
  background: #07c160;
  border-color: #07c160;
  color: white;
}

.wechat-button:hover {
  background: #06ad56;
  border-color: #06ad56;
  color: white;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    margin: 20px;
    padding: 30px 20px;
  }
  
  .login-title {
    font-size: 24px;
  }
  
  .social-login {
    gap: 10px;
  }
  
  .social-button {
    height: 36px;
    font-size: 14px;
  }
  .social-button:not(:first-child) {
    margin-left: 0;
  }
}


</style>