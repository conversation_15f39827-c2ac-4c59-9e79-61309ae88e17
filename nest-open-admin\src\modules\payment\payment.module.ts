import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PaymentService } from './payment.service';
import { PaymentController } from './payment.controller';
import { OrderEntity } from '../order/entities/order.entity';
import { UserModule } from '../user/user.module';
import { ServiceModule } from '../service/service.module';
import { UserServiceModule } from '../user-service/user-service.module';
import { QueueModule } from '../queue/queue.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([OrderEntity]),
    forwardRef(() => UserModule),
    forwardRef(() => ServiceModule),
    forwardRef(() => UserServiceModule),
    QueueModule,
  ],
  controllers: [PaymentController],
  providers: [PaymentService],
  exports: [PaymentService],
})
export class PaymentModule {}
