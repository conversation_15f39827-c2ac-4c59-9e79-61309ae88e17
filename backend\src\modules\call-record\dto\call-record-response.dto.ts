import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CallStatus } from '../enums/call-status.enum';
import { CallRecordEntity } from '../entities/call-record.entity';

export class CallRecordResponseDto {
  @ApiProperty({ description: '记录ID', example: '550e8400-e29b-41d4-a716-446655440000' })
  id: string;

  @ApiProperty({ description: '用户ID', example: 1 })
  userId: number;

  @ApiProperty({ description: '服务ID', example: 1 })
  serviceId: number;

  @ApiProperty({ description: 'HTTP方法', example: 'POST' })
  method: string;

  @ApiPropertyOptional({ description: '请求唯一标识', example: 'req_abcdef123456' })
  requestId?: string;

  @ApiProperty({
    description: '调用状态',
    enum: CallStatus,
    example: CallStatus.SUCCESS
  })
  status: CallStatus;

  @ApiPropertyOptional({ description: '失败原因', example: '服务内部错误' })
  failReason?: string;

  @ApiProperty({ description: '调用耗时(ms)', example: 156 })
  duration: number;

  @ApiProperty({ description: '创建时间', example: '2023-08-15T10:30:00Z' })
  createdAt: Date;

  @ApiPropertyOptional({ description: 'IP地址', example: '***********' })
  ipAddress?: string;

  @ApiPropertyOptional({ description: 'API密钥ID', example: 'key_12345' })
  apiKeyId?: string;

  constructor(entity: CallRecordEntity) {
    this.id = entity.id;
    this.userId = entity.userId;
    this.serviceId = entity.serviceId;
    this.method = entity.method;
    this.requestId = entity.requestId;
    this.status = entity.status;
    this.failReason = entity.failReason;
    this.duration = entity.duration;
    this.createdAt = entity.createdAt;
    this.ipAddress = entity.ipAddress;
    this.apiKeyId = entity.apiKeyId;
  }
}

export class CallRecordListResponseDto {
  @ApiProperty({ description: '调用记录列表', type: [CallRecordResponseDto] })
  records: CallRecordResponseDto[];

  @ApiProperty({ description: '总数', example: 42 })
  total: number;

  @ApiProperty({ description: '当前页', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  limit: number;

  @ApiProperty({ description: '总页数', example: 5 })
  totalPages: number;

  constructor(
    records: CallRecordEntity[],
    total: number,
    page: number,
    limit: number
  ) {
    this.records = records.map(record => new CallRecordResponseDto(record));
    this.total = total;
    this.page = page;
    this.limit = limit;
    this.totalPages = Math.ceil(total / limit);
  }
} 