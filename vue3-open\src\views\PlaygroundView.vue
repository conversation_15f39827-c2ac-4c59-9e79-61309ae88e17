<template>
  <div class="playground-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>API 测试工具</h1>
      <p>在线测试和调试 API 接口</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="playground-content">
      <!-- 左侧配置面板 -->
      <div class="config-panel">
        <!-- 服务选择 -->
        <div class="service-selection">
          <h3>选择服务</h3>
          <div class="service-tags">
            <el-tag v-for="service in availableServices" :key="service.id"
              :type="playgroundStore.selectedService?.id === service.id ? 'primary' : ''"
              :effect="playgroundStore.selectedService?.id === service.id ? 'dark' : 'plain'" class="service-tag"
              @click="playgroundStore.switchService(service.id)">
              {{ service.name }}
            </el-tag>
          </div>
        </div>

        <!-- 请求配置组件 -->
        <RequestConfig v-if="playgroundStore.selectedService" :method="playgroundStore.requestConfig.method"
          :url="playgroundStore.requestConfig.url" :headers="playgroundStore.requestConfig.headers"
          :body="playgroundStore.requestConfig.body" :api-key="playgroundStore.selectedApiKey"
          :available-keys="playgroundStore.apiKeys"
          @update:method="playgroundStore.updateRequestConfig({ method: $event })"
          @update:url="playgroundStore.updateRequestConfig({ url: $event })"
          @update:headers="playgroundStore.updateRequestConfig({ headers: $event })"
          @update:body="playgroundStore.updateRequestConfig({ body: $event })"
          @update:api-key="playgroundStore.selectApiKey($event)" @send-request="sendRequest"
          @load-example="playgroundStore.loadExample" @reset="playgroundStore.resetConfig()" />
      </div>

      <!-- 右侧结果面板 -->
      <div class="result-panel">
        <!-- 响应查看器 -->
        <div class="response-section">
          <ResponseViewer :loading="playgroundStore.isLoading" :status="playgroundStore.currentResponse?.status"
            :status-text="playgroundStore.currentResponse?.statusText"
            :duration="playgroundStore.currentResponse?.duration" :data="playgroundStore.currentResponse?.data"
            :headers="playgroundStore.currentResponse?.headers" :error="playgroundStore.currentError"
            :request-method="playgroundStore.requestConfig.method" :request-url="playgroundStore.requestConfig.url"
            :request-headers="JSON.stringify(playgroundStore.requestConfig.headers, null, 2)"
            :request-body="playgroundStore.requestConfig.body" @clear="clearResponse" />
        </div>

        <!-- 底部标签页 -->
        <div class="bottom-tabs">
          <el-tabs v-model="activeBottomTab" class="result-tabs">
            <!-- 历史记录 -->
            <el-tab-pane label="历史记录" name="history">
              <HistoryPanel :history="playgroundStore.requestHistory" :selected-record="selectedHistoryRecord"
                @select-record="selectHistoryRecord" @load-request="loadRequestFromHistory"
                @delete-record="deleteHistoryRecord" @clear-history="clearHistory" />
            </el-tab-pane>

            <!-- 代码生成 -->
            <el-tab-pane label="代码生成" name="code">
              <CodeGenerator :method="playgroundStore.requestConfig.method" :url="playgroundStore.requestConfig.url"
                :headers="playgroundStore.requestConfig.headers" :body="playgroundStore.requestConfig.body"
                :api-key="playgroundStore.selectedApiKey" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- API配置 -->
    <div class="api-config">
      <div class="config-card">
        <div class="card-header">
          <h3>API 配置</h3>
          <div class="header-actions">
            <el-button size="small" @click="showApiKeyDialog = true">
              <el-icon>
                <Key />
              </el-icon>
              API密钥
            </el-button>
            <el-button size="small" @click="loadExample">
              <el-icon>
                <Document />
              </el-icon>
              加载示例
            </el-button>
          </div>
        </div>

        <div class="config-form">
          <el-form :model="playgroundStore.apiConfig" label-width="100px">
            <el-form-item label="请求URL">
              <el-input v-model="playgroundStore.apiConfig.url" placeholder="API请求地址" readonly>
                <template #prepend>
                  <el-select v-model="playgroundStore.apiConfig.method" style="width: 100px;">
                    <el-option label="POST" value="POST" />
                    <el-option label="GET" value="GET" />
                    <el-option label="PUT" value="PUT" />
                    <el-option label="DELETE" value="DELETE" />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item label="API密钥">
              <el-input v-model="playgroundStore.apiConfig.apiKey" placeholder="请输入API密钥" type="password" show-password>
                <template #append>
                  <el-button @click="showApiKeyDialog = true">
                    选择
                  </el-button>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item label="请求头">
              <div class="headers-editor">
                <div v-for="(header, index) in playgroundStore.apiConfig.headers" :key="index" class="header-row">
                  <el-input v-model="header.key" placeholder="Header名称" style="width: 200px;" />
                  <el-input v-model="header.value" placeholder="Header值" style="flex: 1; margin: 0 8px;" />
                  <el-button type="danger" size="small" @click="playgroundStore.removeHeader(index)">
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </el-button>
                </div>
                <el-button type="primary" size="small" @click="playgroundStore.addHeader">
                  <el-icon>
                    <Plus />
                  </el-icon>
                  添加Header
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 请求参数 -->
    <div class="request-params">
      <div class="params-card">
        <div class="card-header">
          <h3>请求参数</h3>
          <div class="param-tabs">
            <el-radio-group v-model="paramType" size="small">
              <el-radio-button label="form">表单</el-radio-button>
              <el-radio-button label="json">JSON</el-radio-button>
              <el-radio-button label="file">文件上传</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <!-- 表单参数 -->
        <div v-if="paramType === 'form'" class="form-params">
          <div v-for="(param, index) in playgroundStore.apiConfig.params" :key="index" class="param-row">
            <el-input v-model="param.key" placeholder="参数名" style="width: 200px;" />
            <el-input v-model="param.value" placeholder="参数值" style="flex: 1; margin: 0 8px;" />
            <el-select v-model="param.type" style="width: 100px; margin-right: 8px;">
              <el-option label="文本" value="text" />
              <el-option label="文件" value="file" />
            </el-select>
            <el-button type="danger" size="small" @click="removeFormParam(index)">
              <el-icon>
                <Delete />
              </el-icon>
            </el-button>
          </div>
          <el-button type="primary" size="small" @click="addFormParam">
            <el-icon>
              <Plus />
            </el-icon>
            添加参数
          </el-button>
        </div>

        <!-- JSON参数 -->
        <div v-if="paramType === 'json'" class="json-params">
          <div class="json-editor">
            <el-input v-model="jsonParams" type="textarea" :rows="12" placeholder="请输入JSON格式的请求参数"
              class="json-textarea" />
          </div>
          <div class="json-actions">
            <el-button size="small" @click="formatJson">
              <el-icon>
                <Document />
              </el-icon>
              格式化
            </el-button>
            <el-button size="small" @click="validateJson">
              <el-icon>
                <Check />
              </el-icon>
              验证
            </el-button>
            <el-button size="small" @click="clearJson">
              <el-icon>
                <Delete />
              </el-icon>
              清空
            </el-button>
          </div>
        </div>

        <!-- 文件上传 -->
        <div v-if="paramType === 'file'" class="file-params">
          <el-upload ref="uploadRef" class="upload-demo" drag :auto-upload="false" :on-change="handleFileChange"
            :file-list="fileList" multiple>
            <el-icon class="el-icon--upload">
              <UploadFilled />
            </el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持多种格式文件，单个文件大小不超过10MB
              </div>
            </template>
          </el-upload>
        </div>
      </div>
    </div>

    <!-- 发送请求 -->
    <div class="request-actions">
      <div class="actions-card">
        <div class="actions-left">
          <el-button type="primary" size="large" :loading="playgroundStore.isLoading" @click="sendRequest">
            <el-icon>
              <Position />
            </el-icon>
            发送请求
          </el-button>
          <el-button size="large" @click="clearAll">
            <el-icon>
              <Refresh />
            </el-icon>
            重置
          </el-button>
        </div>

        <div class="actions-right">
          <el-button size="small" @click="sendRequest">
            <el-icon>
              <Collection />
            </el-icon>
            保存请求
          </el-button>
          <el-button size="small" @click="showHistoryDialog = true">
            <el-icon>
              <Clock />
            </el-icon>
            历史记录
          </el-button>
        </div>
      </div>
    </div>

    <!-- 响应结果 -->
    <div v-if="playgroundStore.currentResponse" class="response-section">
      <div class="response-card">
        <div class="card-header">
          <h3>响应结果</h3>
          <div class="response-info">
            <el-tag
              :type="playgroundStore.currentResponse.status >= 200 && playgroundStore.currentResponse.status < 300 ? 'success' : 'danger'"
              size="small">
              {{ playgroundStore.currentResponse.status }} {{ playgroundStore.currentResponse.statusText }}
            </el-tag>
            <span class="response-time">{{ playgroundStore.currentResponse.duration }}ms</span>
          </div>
        </div>

        <div class="response-tabs">
          <el-tabs v-model="responseTab">
            <el-tab-pane label="响应体" name="body">
              <div class="response-body">
                <pre v-if="playgroundStore.currentResponse.data" class="response-content">{{
                  formatResponseData(playgroundStore.currentResponse.data) }}</pre>
                <div v-else class="empty-response">无响应数据</div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="响应头" name="headers">
              <div class="response-headers">
                <div v-for="(value, key) in playgroundStore.currentResponse.headers" :key="key" class="header-item">
                  <span class="header-key">{{ key }}:</span>
                  <span class="header-value">{{ value }}</span>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="请求详情" name="request">
              <div class="request-details">
                <div class="detail-item">
                  <span class="detail-label">请求URL:</span>
                  <span class="detail-value">{{ playgroundStore.requestConfig.url }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">请求方法:</span>
                  <span class="detail-value">{{ playgroundStore.requestConfig.method?.toUpperCase() }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">响应时间:</span>
                  <span class="detail-value">{{ playgroundStore.currentResponse.duration }}ms</span>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- API密钥选择对话框 -->
    <el-dialog v-model="showApiKeyDialog" title="选择API密钥" width="600px">
      <div class="api-key-list">
        <div v-for="key in playgroundStore.apiKeys" :key="key.id" class="api-key-item"
          :class="{ selected: playgroundStore.selectedApiKey === key.id }"
          @click="playgroundStore.selectApiKey(key.id)">
          <div class="key-info">
            <div class="key-name">{{ key.name }}</div>
            <div class="key-value">{{ maskApiKey(key.apiKey) }}</div>
          </div>
          <div class="key-status">
            <el-tag :type="key.status === 'active' ? 'success' : 'danger'" size="small">
              {{ key.status === 'active' ? '活跃' : '禁用' }}
            </el-tag>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showApiKeyDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmApiKey">确认</el-button>
      </template>
    </el-dialog>

    <!-- 历史记录对话框 -->
    <el-dialog v-model="showHistoryDialog" title="请求历史" width="800px">
      <div class="history-list">
        <div v-for="(item, index) in playgroundStore.requestHistory" :key="index" class="history-item"
          @click="loadRequestFromHistory(item)">
          <div class="history-info">
            <div class="history-method">{{ item.config.method }}</div>
            <div class="history-url">{{ item.config.url }}</div>
            <div class="history-time">{{ formatTime(new Date(item.timestamp).getTime()) }}</div>
          </div>
          <div class="history-status">
            <el-tag
              :type="item.response?.status && item.response.status >= 200 && item.response.status < 300 ? 'success' : 'danger'"
              size="small">
              {{ item.response?.status || 'N/A' }}
            </el-tag>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="clearHistory">清空历史</el-button>
        <el-button @click="showHistoryDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { usePlaygroundStore } from '@/stores/playground'
import { useServiceStore } from '@/stores/service'
import RequestConfig from '@/components/playground/RequestConfig.vue'
import ResponseViewer from '@/components/playground/ResponseViewer.vue'
import HistoryPanel from '@/components/playground/HistoryPanel.vue'
import CodeGenerator from '@/components/playground/CodeGenerator.vue'
import type { RequestHistory } from '@/stores/playground'
import { useRoute } from 'vue-router'

// Store
const playgroundStore = usePlaygroundStore()
const serviceStore = useServiceStore()

// 响应式数据
const activeBottomTab = ref('history')
const selectedHistoryRecord = ref<RequestHistory>()
const showHistoryDialog = ref(false)
const showApiKeyDialog = ref(false)
const responseTab = ref('body')
const fileList = ref<any[]>([])
const paramType = ref('form')
const jsonParams = ref('')

// 计算属性
const availableServices = computed(() => {
  return serviceStore.services.filter(service => service.serviceStatus === 'active')
})

// 方法
const sendRequest = async () => {
  try {
    await playgroundStore.sendRequest()
    ElMessage.success('请求发送成功')
  } catch (error: any) {
    ElMessage.error('请求失败: ' + (error.message || '未知错误'))
  }
}

const clearResponse = () => {
  playgroundStore.clearCurrentResponse()
}

const selectHistoryRecord = (record: RequestHistory) => {
  selectedHistoryRecord.value = record
}

const loadRequestFromHistory = (record: RequestHistory) => {
  playgroundStore.loadFromHistory(record)
  ElMessage.success('历史记录已加载到编辑器')
}

const deleteHistoryRecord = (index: number) => {
  playgroundStore.deleteHistoryRecord(index)
  ElMessage.success('记录已删除')
}

const clearHistory = () => {
  playgroundStore.clearHistory()
  selectedHistoryRecord.value = undefined
  ElMessage.success('历史记录已清空')
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString()
}

const confirmApiKey = () => {
  // TODO: 实现 API 密钥确认逻辑
  ElMessage.success('API 密钥已确认')
  showApiKeyDialog.value = false
}

const maskApiKey = (apiKey: string) => {
  if (!apiKey || apiKey.length <= 8) return apiKey
  return apiKey.substring(0, 4) + '****' + apiKey.substring(apiKey.length - 4)
}

const formatResponseData = (data: any) => {
  if (typeof data === 'string') {
    try {
      return JSON.stringify(JSON.parse(data), null, 2)
    } catch {
      return data
    }
  }
  return JSON.stringify(data, null, 2)
}

const clearAll = () => {
  playgroundStore.resetConfig()
  playgroundStore.clearCurrentResponse()
  ElMessage.success('已重置所有配置')
}

const handleFileChange = (file: any, fileListParam: any[]) => {
  fileList.value = fileListParam
}

const clearJson = () => {
  playgroundStore.requestConfig.body = ''
  ElMessage.success('JSON 参数已清空')
}

const validateJson = () => {
  try {
    JSON.parse(playgroundStore.requestConfig.body || '{}')
    ElMessage.success('JSON 格式正确')
  } catch (error) {
    ElMessage.error('JSON 格式错误')
  }
}

const formatJson = () => {
  try {
    const parsed = JSON.parse(playgroundStore.requestConfig.body || '{}')
    playgroundStore.requestConfig.body = JSON.stringify(parsed, null, 2)
    ElMessage.success('JSON 已格式化')
  } catch (error) {
    ElMessage.error('JSON 格式错误，无法格式化')
  }
}

const addFormParam = () => {
  if (!playgroundStore.apiConfig.params) {
    playgroundStore.apiConfig.params = []
  }
  playgroundStore.apiConfig.params.push({ key: '', value: '', type: 'string' })
}

const removeFormParam = (index: number) => {
  playgroundStore.apiConfig.params.splice(index, 1)
}

const loadExample = () => {
  // 加载示例配置
  playgroundStore.apiConfig.url = 'https://api.example.com/v1/chat/completions'
  playgroundStore.apiConfig.method = 'POST'
  playgroundStore.apiConfig.body = JSON.stringify({
    model: 'gpt-3.5-turbo',
    messages: [{ role: 'user', content: 'Hello, world!' }]
  }, null, 2)
  ElMessage.success('示例配置已加载')
}

// 生命周期
const route = useRoute()
const serviceStore = useServiceStore()
const playgroundStore = usePlaygroundStore()

onMounted(async () => {
  await serviceStore.fetchServices()
  const code = route.query.code as string
  if (code) {
    playgroundStore.switchService(code)
  }
})
</script>

<style scoped>
.playground-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 12px 0;
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.playground-content {
  max-width: 1600px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  align-items: start;
}

.config-panel {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.service-selection {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.service-selection h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.service-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.service-tag {
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
}

.service-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.result-panel {
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: calc(100vh - 200px);
}

.response-section {
  flex: 1;
  min-height: 0;
}

.bottom-tabs {
  height: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.result-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.result-tabs :deep(.el-tabs__header) {
  margin: 0;
  padding: 0 24px;
  background: #fafbfc;
  border-bottom: 1px solid #e4e7ed;
}

.result-tabs :deep(.el-tabs__content) {
  flex: 1;
  padding: 0;
  overflow: hidden;
}

.result-tabs :deep(.el-tab-pane) {
  height: 100%;
  overflow: hidden;
}

.service-selector,
.api-config,
.request-params,
.request-actions,
.response-section {
  margin-bottom: 24px;
}

.selector-card,
.config-card,
.params-card,
.actions-card,
.response-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.card-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.service-tabs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.service-tag {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.service-tag:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.service-tag.active {
  border-color: #409eff;
  background: #e6f7ff;
}

.tab-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409eff;
}

.tab-content {
  flex: 1;
}

.tab-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.tab-desc {
  color: #606266;
  font-size: 14px;
}

.config-form {
  max-width: 800px;
}

.headers-editor {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.param-tabs {
  display: flex;
  align-items: center;
}

.form-params {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.param-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.json-params {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.json-editor {
  position: relative;
}

.json-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.json-actions {
  display: flex;
  gap: 8px;
}

.file-params {
  min-height: 200px;
}

.actions-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.actions-left,
.actions-right {
  display: flex;
  gap: 12px;
}

.response-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.response-time {
  color: #606266;
  font-size: 14px;
}

.response-body {
  max-height: 400px;
  overflow-y: auto;
}

.response-content {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.empty-response {
  text-align: center;
  color: #909399;
  padding: 40px;
}

.response-headers {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-item {
  display: flex;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.header-key {
  font-weight: 600;
  color: #303133;
  margin-right: 8px;
  min-width: 150px;
}

.header-value {
  color: #606266;
  word-break: break-all;
}

.request-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.detail-label {
  font-weight: 600;
  color: #303133;
  margin-right: 12px;
  min-width: 100px;
}

.detail-value {
  color: #606266;
  word-break: break-all;
}

.api-key-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.api-key-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.api-key-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.api-key-item.selected {
  border-color: #409eff;
  background: #e6f7ff;
}

.key-info {
  flex: 1;
}

.key-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.key-value {
  color: #606266;
  font-family: monospace;
  font-size: 14px;
}

.key-status {
  flex-shrink: 0;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 400px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.history-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.history-info {
  flex: 1;
}

.history-method {
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.history-url {
  color: #303133;
  margin-bottom: 4px;
  word-break: break-all;
}

.history-time {
  color: #909399;
  font-size: 12px;
}

.history-status {
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .playground-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .result-panel {
    height: auto;
  }

  .bottom-tabs {
    height: 500px;
  }
}

@media (max-width: 768px) {
  .playground-container {
    padding: 16px;
  }

  .page-header {
    padding: 30px 20px;
    margin-bottom: 24px;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .playground-content {
    gap: 16px;
  }

  .config-panel {
    gap: 16px;
  }

  .result-panel {
    gap: 16px;
  }

  .service-selection {
    padding: 20px;
  }

  .service-tags {
    gap: 8px;
  }

  .service-tag {
    font-size: 14px;
    padding: 6px 12px;
  }

  .bottom-tabs {
    height: 400px;
  }

  .service-tabs {
    grid-template-columns: 1fr;
  }

  .actions-card {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .actions-left,
  .actions-right {
    justify-content: center;
  }

  .header-row,
  .param-row {
    flex-direction: column;
    align-items: stretch;
  }

  .response-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* 动画效果 */
.playground-content {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
:deep(.el-scrollbar__wrap) {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar-track) {
  background: #f1f1f1;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 3px;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}
</style>