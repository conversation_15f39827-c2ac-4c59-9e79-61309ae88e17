#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地理坐标逆解析功能测试脚本

使用方法：
1. 确保服务已启动：python app.py
2. 运行测试：python test_geocoding.py
"""

import requests
import json
import time
from typing import List, Dict

class GeocodingTester:
    def __init__(self, base_url: str = 'http://localhost:8868'):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'GeocodingTester/1.0'
        })
    
    def test_single_geocoding(self, lat: float, lng: float, description: str = '') -> Dict:
        """测试单点地理坐标逆解析"""
        url = f'{self.base_url}/rev-geo'
        payload = {
            'latitude': lat,
            'longitude': lng
        }
        
        try:
            start_time = time.time()
            response = self.session.post(url, json=payload, timeout=30)
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                result['test_description'] = description
                result['actual_response_time'] = f'{end_time - start_time:.3f}s'
                return result
            else:
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: {response.text}',
                    'test_description': description
                }
        except Exception as e:
            return {
                'success': False,
                'error': f'请求异常: {str(e)}',
                'test_description': description
            }
    
    def test_batch_geocoding(self, coordinates: List[Dict]) -> Dict:
        """测试批量地理坐标逆解析"""
        url = f'{self.base_url}/b-rev-geo'
        payload = {
            'coordinates': coordinates
        }
        
        try:
            start_time = time.time()
            response = self.session.post(url, json=payload, timeout=60)
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                result['actual_response_time'] = f'{end_time - start_time:.3f}s'
                return result
            else:
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: {response.text}'
                }
        except Exception as e:
            return {
                'success': False,
                'error': f'请求异常: {str(e)}'
            }
    
    def test_service_health(self) -> bool:
        """测试服务健康状态"""
        try:
            response = self.session.get(f'{self.base_url}/health', timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("=" * 60)
        print("地理坐标逆解析功能测试")
        print("=" * 60)
        
        # 检查服务状态
        print("\n1. 检查服务状态...")
        if not self.test_service_health():
            print("❌ 服务未启动或不可访问")
            print("请确保服务已启动：python app.py")
            return
        print("✅ 服务正常运行")
        
        # 测试用例
        test_cases = [
            {'lat': 39.9042, 'lng': 116.4074, 'desc': '北京天安门'},
            {'lat': 31.2304, 'lng': 121.4737, 'desc': '上海外滩'},
            {'lat': 22.5431, 'lng': 114.0579, 'desc': '深圳市中心'},
            {'lat': 30.2741, 'lng': 120.1551, 'desc': '杭州西湖'},
            {'lat': 32.0603, 'lng': 118.7969, 'desc': '南京夫子庙'},
            {'lat': 23.1291, 'lng': 113.2644, 'desc': '广州天河'},
            {'lat': 29.5647, 'lng': 106.5507, 'desc': '重庆解放碑'},
            {'lat': 30.6586, 'lng': 104.0647, 'desc': '成都天府广场'}
        ]
        
        # 单点测试
        print("\n2. 单点坐标逆解析测试...")
        single_results = []
        
        for i, case in enumerate(test_cases[:5]):  # 测试前5个
            print(f"\n测试 {i+1}: {case['desc']} ({case['lat']}, {case['lng']})")
            result = self.test_single_geocoding(case['lat'], case['lng'], case['desc'])
            single_results.append(result)
            
            if result.get('success'):
                address = result.get('address', {})
                print(f"✅ 解析成功")
                print(f"   省份: {address.get('province', 'N/A')}")
                print(f"   城市: {address.get('city', 'N/A')}")
                print(f"   区县: {address.get('district', 'N/A')}")
                print(f"   完整地址: {address.get('full_address', 'N/A')}")
                print(f"   置信度: {result.get('confidence', 0):.2f}")
                print(f"   响应时间: {result.get('actual_response_time', 'N/A')}")
                
                if address.get('nearest_poi'):
                    print(f"   最近POI: {address.get('nearest_poi')} ({address.get('poi_distance', 0):.0f}米)")
            else:
                print(f"❌ 解析失败: {result.get('error', 'Unknown error')}")
        
        # 批量测试
        print("\n3. 批量坐标逆解析测试...")
        batch_coordinates = [
            {'latitude': case['lat'], 'longitude': case['lng']} 
            for case in test_cases[:3]
        ]
        
        batch_result = self.test_batch_geocoding(batch_coordinates)
        
        if batch_result.get('success'):
            print("✅ 批量解析成功")
            print(f"   处理数量: {batch_result.get('total_count', 0)}")
            print(f"   响应时间: {batch_result.get('actual_response_time', 'N/A')}")
            
            for item in batch_result.get('results', []):
                coords = item.get('input_coordinates', {})
                result = item.get('result', {})
                if result.get('success'):
                    address = result.get('address', {})
                    print(f"   坐标 ({coords.get('latitude')}, {coords.get('longitude')}) -> {address.get('full_address', 'N/A')}")
                else:
                    print(f"   坐标 ({coords.get('latitude')}, {coords.get('longitude')}) -> 解析失败")
        else:
            print(f"❌ 批量解析失败: {batch_result.get('error', 'Unknown error')}")
        
        # 边界测试
        print("\n4. 边界条件测试...")
        
        # 测试无效坐标
        print("\n测试无效坐标...")
        invalid_cases = [
            {'lat': 91, 'lng': 0, 'desc': '纬度超出范围'},
            {'lat': 0, 'lng': 181, 'desc': '经度超出范围'},
            {'lat': 'invalid', 'lng': 116, 'desc': '非数字纬度'}
        ]
        
        for case in invalid_cases:
            print(f"测试: {case['desc']}")
            try:
                result = self.test_single_geocoding(case['lat'], case['lng'], case['desc'])
                if not result.get('success'):
                    print(f"✅ 正确拒绝无效输入: {result.get('error', '')}")
                else:
                    print(f"❌ 应该拒绝但接受了无效输入")
            except:
                print(f"✅ 正确拒绝无效输入")
        
        # 性能统计
        print("\n5. 性能统计...")
        successful_results = [r for r in single_results if r.get('success')]
        
        if successful_results:
            response_times = []
            confidences = []
            
            for result in successful_results:
                try:
                    time_str = result.get('actual_response_time', '0s')
                    time_val = float(time_str.replace('s', ''))
                    response_times.append(time_val)
                    
                    confidence = result.get('confidence', 0)
                    confidences.append(confidence)
                except:
                    pass
            
            if response_times:
                avg_time = sum(response_times) / len(response_times)
                max_time = max(response_times)
                min_time = min(response_times)
                
                print(f"   平均响应时间: {avg_time:.3f}s")
                print(f"   最快响应时间: {min_time:.3f}s")
                print(f"   最慢响应时间: {max_time:.3f}s")
            
            if confidences:
                avg_confidence = sum(confidences) / len(confidences)
                max_confidence = max(confidences)
                min_confidence = min(confidences)
                
                print(f"   平均置信度: {avg_confidence:.3f}")
                print(f"   最高置信度: {max_confidence:.3f}")
                print(f"   最低置信度: {min_confidence:.3f}")
            
            success_rate = len(successful_results) / len(single_results) * 100
            print(f"   成功率: {success_rate:.1f}%")
        
        print("\n=" * 60)
        print("测试完成")
        print("=" * 60)

def main():
    """主函数"""
    tester = GeocodingTester()
    tester.run_comprehensive_test()

if __name__ == '__main__':
    main()