<template>
  <div class="monitoring-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">系统监控</h1>
        <p class="page-subtitle">实时监控系统状态和性能指标</p>
      </div>
      <div class="header-right">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="primary" @click="showAlertDialog = true">
          <el-icon><Bell /></el-icon>
          告警设置
        </el-button>
      </div>
    </div>

    <!-- 系统状态概览 -->
    <div class="status-overview">
      <div class="status-card" :class="{ 'status-error': systemStatus.status === 'error' }">
        <div class="status-icon">
          <el-icon :color="systemStatus.status === 'healthy' ? '#67C23A' : '#F56C6C'">
            <component :is="systemStatus.status === 'healthy' ? 'CircleCheck' : 'CircleClose'" />
          </el-icon>
        </div>
        <div class="status-info">
          <div class="status-title">系统状态</div>
          <div class="status-value">{{ getStatusText(systemStatus.status) }}</div>
          <div class="status-time">最后更新: {{ formatTime(systemStatus.lastUpdated) }}</div>
        </div>
      </div>

      <div class="metric-cards">
        <div class="metric-card">
          <div class="metric-icon cpu">
            <el-icon><Cpu /></el-icon>
          </div>
          <div class="metric-info">
            <div class="metric-value">{{ systemMetrics.cpu }}%</div>
            <div class="metric-label">CPU使用率</div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon memory">
            <el-icon><Monitor /></el-icon>
          </div>
          <div class="metric-info">
            <div class="metric-value">{{ systemMetrics.memory }}%</div>
            <div class="metric-label">内存使用率</div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon disk">
            <el-icon><FolderOpened /></el-icon>
          </div>
          <div class="metric-info">
            <div class="metric-value">{{ systemMetrics.disk }}%</div>
            <div class="metric-label">磁盘使用率</div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon network">
            <el-icon><Connection /></el-icon>
          </div>
          <div class="metric-info">
            <div class="metric-value">{{ systemMetrics.network }}ms</div>
            <div class="metric-label">网络延迟</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 服务健康检查 -->
    <div class="health-check-section">
      <el-card class="health-card">
        <template #header>
          <div class="card-header">
            <h3>服务健康检查</h3>
            <el-button size="small" @click="runHealthCheck" :loading="healthCheckLoading">
              <el-icon><Refresh /></el-icon>
              检查
            </el-button>
          </div>
        </template>
        
        <div class="health-list">
          <div 
            v-for="service in healthChecks" 
            :key="service.name"
            class="health-item"
            :class="`health-${service.status}`"
          >
            <div class="health-icon">
              <el-icon :color="getHealthColor(service?.status as string)">
                <component :is="getHealthIcon(service?.status as string)" />
              </el-icon>
            </div>
            <div class="health-info">
              <div class="health-name">{{ service.name }}</div>
              <div class="health-description">{{ service.description }}</div>
            </div>
            <div class="health-status">
              <el-tag :type="getHealthTagType(service.status as string)">{{ getHealthStatusText(service.status as string) }}</el-tag>
              <div class="health-time">{{ formatTime(service.lastCheck as Date) }}</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 性能图表 -->
    <div class="charts-section">
      <el-row :gutter="24">
        <el-col :lg="12" :md="24">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <h3>CPU & 内存使用率</h3>
                <el-radio-group v-model="chartTimeRange" size="small">
                  <el-radio-button label="1h">1小时</el-radio-button>
                  <el-radio-button label="6h">6小时</el-radio-button>
                  <el-radio-button label="24h">24小时</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div ref="systemChart" class="chart-container"></div>
          </el-card>
        </el-col>
        
        <el-col :lg="12" :md="24">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <h3>API响应时间</h3>
                <el-select v-model="selectedService" size="small" style="width: 120px;">
                  <el-option label="全部服务" value="all" />
                  <el-option label="OCR服务" value="ocr" />
                  <el-option label="NLP服务" value="nlp" />
                  <el-option label="AI生成" value="ai" />
                </el-select>
              </div>
            </template>
            <div ref="responseChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 告警历史 -->
    <div class="alerts-section">
      <el-card class="alerts-card">
        <template #header>
          <div class="card-header">
            <h3>告警历史</h3>
            <div class="header-actions">
              <el-select v-model="alertFilter" size="small" style="width: 120px;">
                <el-option label="全部" value="all" />
                <el-option label="严重" value="critical" />
                <el-option label="警告" value="warning" />
                <el-option label="信息" value="info" />
              </el-select>
              <el-button size="small" @click="clearAlerts">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </div>
          </div>
        </template>
        
        <div class="alerts-list">
          <div 
            v-for="alert in filteredAlerts" 
            :key="alert.id"
            class="alert-item"
            :class="`alert-${alert.level}`"
          >
            <div class="alert-icon">
              <el-icon :color="getAlertColor(alert.level as string)">
                <component :is="getAlertIcon(alert.level as string)" />
              </el-icon>
            </div>
            <div class="alert-content">
              <div class="alert-title">{{ alert.title }}</div>
              <div class="alert-message">{{ alert.message }}</div>
              <div class="alert-time">{{ formatTime(alert.timestamp as Date) }}</div>
            </div>
            <div class="alert-actions">
              <el-button size="small" @click="resolveAlert(alert.id as string)">
                标记已解决
              </el-button>
            </div>
          </div>
        </div>
        
        <div v-if="filteredAlerts.length === 0" class="empty-alerts">
          <el-empty description="暂无告警信息" />
        </div>
      </el-card>
    </div>

    <!-- 告警设置对话框 -->
    <el-dialog
      v-model="showAlertDialog"
      title="告警设置"
      width="600px"
    >
      <el-form :model="alertSettings" label-width="120px">
        <el-form-item label="CPU告警阈值">
          <el-slider v-model="alertSettings.cpuThreshold" :max="100" show-input />
        </el-form-item>
        <el-form-item label="内存告警阈值">
          <el-slider v-model="alertSettings.memoryThreshold" :max="100" show-input />
        </el-form-item>
        <el-form-item label="磁盘告警阈值">
          <el-slider v-model="alertSettings.diskThreshold" :max="100" show-input />
        </el-form-item>
        <el-form-item label="响应时间阈值">
          <el-input-number v-model="alertSettings.responseTimeThreshold" :min="100" :max="10000" />
          <span style="margin-left: 8px;">毫秒</span>
        </el-form-item>
        <el-form-item label="邮件通知">
          <el-switch v-model="alertSettings.emailNotification" />
        </el-form-item>
        <el-form-item label="短信通知">
          <el-switch v-model="alertSettings.smsNotification" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAlertDialog = false">取消</el-button>
        <el-button type="primary" @click="saveAlertSettings">保存设置</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { useMonitoringStore } from '@/stores/monitoring'
import type { Alert, HealthCheck, AlertHistory } from '@/types/monitoring'

const monitoringStore: any = useMonitoringStore()

// 响应式数据
const loading = ref(false)
const healthCheckLoading = ref(false)
const showAlertDialog = ref(false)
const chartTimeRange = ref('1h')
const selectedService = ref('all')
const alertFilter = ref('all')

// 图表实例
const systemChart = ref<HTMLElement>()
const responseChart = ref<HTMLElement>()
let systemChartInstance: echarts.ECharts | null = null
let responseChartInstance: echarts.ECharts | null = null

// 系统状态
const systemStatus = reactive({
  status: 'healthy' as 'healthy' | 'warning' | 'error',
  lastUpdated: new Date()
})

// 系统指标
const systemMetrics = reactive({
  cpu: 45,
  memory: 62,
  disk: 78,
  network: 23
})

// 健康检查
const healthChecks = ref<HealthCheck[]>([
  {
    name: 'API服务',
    description: '主要API服务状态',
    status: 'healthy',
    lastCheck: new Date()
  },
  {
    name: '数据库',
    description: 'MySQL数据库连接',
    status: 'healthy',
    lastCheck: new Date()
  },
  {
    name: 'Redis缓存',
    description: 'Redis缓存服务',
    status: 'warning',
    lastCheck: new Date()
  },
  {
    name: '文件存储',
    description: '文件上传存储服务',
    status: 'healthy',
    lastCheck: new Date()
  }
])

// 告警历史
const alertHistory = ref<AlertHistory[]>([
  {
    id: '1',
    title: 'CPU使用率过高',
    message: 'CPU使用率达到85%，超过告警阈值',
    level: 'warning',
    timestamp: new Date(Date.now() - 1000 * 60 * 30),
    resolved: false
  },
  {
    id: '2',
    title: 'API响应时间异常',
    message: 'OCR服务平均响应时间超过2秒',
    level: 'critical',
    timestamp: new Date(Date.now() - 1000 * 60 * 60),
    resolved: false
  }
])

// 告警设置
const alertSettings = reactive({
  cpuThreshold: 80,
  memoryThreshold: 85,
  diskThreshold: 90,
  responseTimeThreshold: 2000,
  emailNotification: true,
  smsNotification: false
})

// 计算属性
const filteredAlerts = computed(() => {
  if (alertFilter.value === 'all') {
    return alertHistory.value.filter(alert => !alert.resolved)
  }
  return alertHistory.value.filter(alert => 
    alert.level === alertFilter.value && !alert.resolved
  )
})

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      monitoringStore.fetchSystemStatus(),
      monitoringStore.fetchSystemMetrics(),
      monitoringStore.fetchHealthChecks(),
      monitoringStore.fetchAlertHistory()
    ])
    
    // 更新本地数据
    Object.assign(systemMetrics, monitoringStore.systemMetrics)
    systemStatus.status = monitoringStore.systemStatus.status
    systemStatus.lastUpdated = new Date()
    
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const runHealthCheck = async () => {
  healthCheckLoading.value = true
  try {
    await monitoringStore.runHealthCheck()
    healthChecks.value = monitoringStore.healthChecks
    ElMessage.success('健康检查完成')
  } catch (error) {
    ElMessage.error('健康检查失败')
  } finally {
    healthCheckLoading.value = false
  }
}

const clearAlerts = () => {
  alertHistory.value = alertHistory.value.filter(alert => alert.resolved)
  ElMessage.success('告警历史已清空')
}

const resolveAlert = (alertId: string) => {
  const alert = alertHistory.value.find(a => a.id === alertId)
  if (alert) {
    alert.resolved = true
    ElMessage.success('告警已标记为已解决')
  }
}

const saveAlertSettings = async () => {
  try {
    await monitoringStore.updateAlertSettings(alertSettings)
    showAlertDialog.value = false
    ElMessage.success('告警设置已保存')
  } catch (error) {
    ElMessage.error('保存设置失败')
  }
}

// 工具方法
const getStatusText = (status: string) => {
  const statusMap = {
    healthy: '正常',
    warning: '警告',
    error: '异常'
  }
  return statusMap[status as keyof typeof statusMap] || '未知'
}

const getHealthColor = (status: string) => {
  const colorMap = {
    healthy: '#67C23A',
    warning: '#E6A23C',
    error: '#F56C6C'
  }
  return colorMap[status as keyof typeof colorMap] || '#909399'
}

const getHealthIcon = (status: string) => {
  const iconMap = {
    healthy: 'CircleCheck',
    warning: 'Warning',
    error: 'CircleClose'
  }
  return iconMap[status as keyof typeof iconMap] || 'QuestionFilled'
}

const getHealthTagType = (status: string) => {
  const typeMap = {
    healthy: 'success',
    warning: 'warning',
    error: 'danger'
  }
  return typeMap[status as keyof typeof typeMap] || 'info'
}

const getHealthStatusText = (status: string) => {
  const textMap = {
    healthy: '正常',
    warning: '警告',
    error: '异常'
  }
  return textMap[status as keyof typeof textMap] || '未知'
}

const getAlertColor = (level: string) => {
  const colorMap = {
    info: '#409EFF',
    warning: '#E6A23C',
    critical: '#F56C6C'
  }
  return colorMap[level as keyof typeof colorMap] || '#909399'
}

const getAlertIcon = (level: string) => {
  const iconMap = {
    info: 'InfoFilled',
    warning: 'Warning',
    critical: 'CircleClose'
  }
  return iconMap[level as keyof typeof iconMap] || 'QuestionFilled'
}

const formatTime = (time: Date) => {
  return time.toLocaleString('zh-CN')
}

// 图表初始化
const initCharts = async () => {
  await nextTick()
  
  if (systemChart.value) {
    systemChartInstance = echarts.init(systemChart.value)
    updateSystemChart()
  }
  
  if (responseChart.value) {
    responseChartInstance = echarts.init(responseChart.value)
    updateResponseChart()
  }
}

const updateSystemChart = () => {
  if (!systemChartInstance) return
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['CPU使用率', '内存使用率']
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: 'CPU使用率',
        type: 'line',
        data: [30, 45, 60, 55, 48, 42],
        smooth: true,
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '内存使用率',
        type: 'line',
        data: [50, 62, 70, 65, 58, 55],
        smooth: true,
        itemStyle: { color: '#67C23A' }
      }
    ]
  }
  
  systemChartInstance.setOption(option)
}

const updateResponseChart = () => {
  if (!responseChartInstance) return
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}ms'
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}ms'
      }
    },
    series: [
      {
        name: '响应时间',
        type: 'line',
        data: [120, 150, 180, 160, 140, 130],
        smooth: true,
        itemStyle: { color: '#E6A23C' },
        areaStyle: {
          opacity: 0.3
        }
      }
    ]
  }
  
  responseChartInstance.setOption(option)
}

// 响应式处理
const handleResize = () => {
  systemChartInstance?.resize()
  responseChartInstance?.resize()
}

// 生命周期
onMounted(async () => {
  await refreshData()
  await initCharts()
  
  window.addEventListener('resize', handleResize)
  
  // 定时刷新数据
  const interval = setInterval(refreshData, 30000) // 30秒刷新一次
  
  onUnmounted(() => {
    clearInterval(interval)
    window.removeEventListener('resize', handleResize)
    systemChartInstance?.dispose()
    responseChartInstance?.dispose()
  })
})
</script>

<style scoped>
.monitoring-page {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 状态概览 */
.status-overview {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.status-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 280px;
}

.status-card.status-error {
  border-left: 4px solid #F56C6C;
}

.status-icon {
  font-size: 32px;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.status-value {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.status-time {
  font-size: 12px;
  color: #C0C4CC;
}

.metric-cards {
  display: flex;
  gap: 16px;
  flex: 1;
}

.metric-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.metric-icon.cpu {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-icon.memory {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.metric-icon.disk {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.metric-icon.network {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 12px;
  color: #909399;
}

/* 健康检查 */
.health-check-section {
  margin-bottom: 24px;
}

.health-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.health-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.health-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #E4E7ED;
}

.health-item.health-healthy {
  border-left-color: #67C23A;
}

.health-item.health-warning {
  border-left-color: #E6A23C;
}

.health-item.health-error {
  border-left-color: #F56C6C;
}

.health-icon {
  font-size: 20px;
}

.health-info {
  flex: 1;
}

.health-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.health-description {
  font-size: 12px;
  color: #909399;
}

.health-status {
  text-align: right;
}

.health-time {
  font-size: 12px;
  color: #C0C4CC;
  margin-top: 4px;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart-container {
  height: 300px;
  width: 100%;
}

/* 告警区域 */
.alerts-section {
  margin-bottom: 24px;
}

.alerts-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #E4E7ED;
}

.alert-item.alert-info {
  border-left-color: #409EFF;
}

.alert-item.alert-warning {
  border-left-color: #E6A23C;
}

.alert-item.alert-critical {
  border-left-color: #F56C6C;
}

.alert-icon {
  font-size: 20px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.alert-message {
  font-size: 13px;
  color: #606266;
  margin-bottom: 4px;
}

.alert-time {
  font-size: 12px;
  color: #C0C4CC;
}

.empty-alerts {
  padding: 40px 0;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .monitoring-page {
    padding: 16px;
  }
  
  .status-overview {
    flex-direction: column;
  }
  
  .metric-cards {
    flex-direction: column;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-right {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>