import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThan, LessThan } from 'typeorm';
import { CallRecordEntity } from './entities/call-record.entity';
import { UserService } from '../user/user.service';
import { ServiceService } from '../service/service.service';
import { BaseCrudService } from '../../common/base/base-crud.service';
import {
  CreateCallRecordDto,
  UpdateCallRecordDto,
  QueryCallRecordDto,
  CallRecordResponseDto,
  CallRecordListResponseDto,
  CallRecordStatsDto,
  ExportCallRecordDto,
} from './dto/call-record.dto';

@Injectable()
export class CallRecordService extends BaseCrudService<
  CallRecordEntity,
  CreateCallRecordDto,
  UpdateCallRecordDto,
  QueryCallRecordDto,
  CallRecordResponseDto,
  CallRecordListResponseDto
> {
  protected readonly repository: Repository<CallRecordEntity>;
  protected readonly entityName = 'CallRecord';

  constructor(
    @InjectRepository(CallRecordEntity)
    callRecordRepository: Repository<CallRecordEntity>,
    private readonly userService: UserService,
    private readonly serviceService: ServiceService,
  ) {
    super();
    this.repository = callRecordRepository;
  }

  /**
   * 验证创建前的业务逻辑
   */
  protected async validateBeforeCreate(
    createDto: CreateCallRecordDto,
  ): Promise<void> {
    const { userId, serviceId, requestId } = createDto;

    // 验证用户是否存在
    const user = await this.userService.findOne(userId);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 验证服务是否存在
    const service = await this.serviceService.findOne(serviceId);
    if (!service) {
      throw new NotFoundException('服务不存在');
    }

    // 检查请求ID是否已存在
    const existingRecord = await this.repository.findOne({
      where: { requestId },
    });

    if (existingRecord) {
      throw new ConflictException('请求ID已存在');
    }
  }

  /**
   * 验证更新前的业务逻辑
   */
  protected async validateBeforeUpdate(
    id: number,
    updateDto: UpdateCallRecordDto,
    entity: CallRecordEntity,
  ): Promise<void> {
    // 可以添加更新前的验证逻辑
    // 例如：检查是否有权限更新、验证业务规则等
  }

  /**
   * 验证删除前的业务逻辑
   */
  protected async validateBeforeDelete(id: number, entity: CallRecordEntity): Promise<void> {
    // 可以添加删除前的验证逻辑
    // 例如：检查是否有关联数据、是否有权限删除等
  }

  /**
   * 转换创建DTO为实体
   */
  protected transformCreateDto(
    createDto: CreateCallRecordDto,
  ): Partial<CallRecordEntity> {
    return {
      user: { id: createDto.userId } as any,
      service: { id: createDto.serviceId } as any,
      requestId: createDto.requestId,
      input: createDto.input,
      output: createDto.output,
      cost: createDto.cost,
      amount: createDto.amount,
      success: createDto.success,
    };
  }

  /**
   * 转换实体为响应DTO
   */
  protected transformToResponseDto(
    entity: CallRecordEntity,
  ): CallRecordResponseDto {
    return this.formatCallRecordResponse(entity, {});
  }

  /**
   * 应用查询条件
   */
  protected applyQueryConditions(
    queryBuilder: any,
    queryDto: QueryCallRecordDto,
  ): void {
    const {
      userId,
      serviceId,
      requestId,
      success,
      minCost,
      maxCost,
      minAmount,
      maxAmount,
      minResponseTime,
      maxResponseTime,
      httpStatus,
      clientIp,
      startDate,
      endDate,
    } = queryDto;

    if (userId) {
      queryBuilder.andWhere('entity.user.id = :userId', { userId });
    }

    if (serviceId) {
      queryBuilder.andWhere('entity.service.id = :serviceId', { serviceId });
    }

    if (requestId) {
      queryBuilder.andWhere('entity.requestId LIKE :requestId', {
        requestId: `%${requestId}%`,
      });
    }

    if (success !== undefined) {
      queryBuilder.andWhere('entity.success = :success', { success });
    }

    if (minCost !== undefined) {
      queryBuilder.andWhere('entity.cost >= :minCost', { minCost });
    }

    if (maxCost !== undefined) {
      queryBuilder.andWhere('entity.cost <= :maxCost', { maxCost });
    }

    if (minAmount !== undefined) {
      queryBuilder.andWhere('entity.amount >= :minAmount', { minAmount });
    }

    if (maxAmount !== undefined) {
      queryBuilder.andWhere('entity.amount <= :maxAmount', { maxAmount });
    }

    if (minResponseTime !== undefined) {
      queryBuilder.andWhere('entity.responseTime >= :minResponseTime', {
        minResponseTime,
      });
    }

    if (maxResponseTime !== undefined) {
      queryBuilder.andWhere('entity.responseTime <= :maxResponseTime', {
        maxResponseTime,
      });
    }

    if (httpStatus) {
      queryBuilder.andWhere('entity.httpStatus = :httpStatus', { httpStatus });
    }

    if (clientIp) {
      queryBuilder.andWhere('entity.clientIp = :clientIp', { clientIp });
    }

    if (startDate) {
      queryBuilder.andWhere('entity.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('entity.createdAt <= :endDate', { endDate });
    }
  }

  /**
   * 转换实体列表为响应DTO
   */
  protected transformToListResponseDto(
    data: CallRecordResponseDto[],
    total: number,
    page: number,
    limit: number,
    totalPages: number,
  ): CallRecordListResponseDto {
    return {
      data: data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * 查询调用记录列表
   */
  async findAll(
    queryDto: QueryCallRecordDto = {},
  ): Promise<CallRecordListResponseDto> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      userId,
      serviceId,
      requestId,
      success,
      minCost,
      maxCost,
      minAmount,
      maxAmount,
      httpStatus,
      clientIp,
      startDate,
      endDate,
    } = queryDto;

    const queryBuilder = this.repository
      .createQueryBuilder('callRecord')
      .leftJoinAndSelect('callRecord.user', 'user')
      .leftJoinAndSelect('callRecord.service', 'service');

    // 添加查询条件
    if (userId) {
      queryBuilder.andWhere('user.id = :userId', { userId });
    }
    if (serviceId) {
      queryBuilder.andWhere('service.id = :serviceId', { serviceId });
    }
    if (requestId) {
      queryBuilder.andWhere('callRecord.requestId LIKE :requestId', {
        requestId: `%${requestId}%`,
      });
    }
    if (success !== undefined) {
      queryBuilder.andWhere('callRecord.success = :success', { success });
    }
    if (minCost !== undefined) {
      queryBuilder.andWhere('callRecord.cost >= :minCost', { minCost });
    }
    if (maxCost !== undefined) {
      queryBuilder.andWhere('callRecord.cost <= :maxCost', { maxCost });
    }
    if (minAmount !== undefined) {
      queryBuilder.andWhere('callRecord.amount >= :minAmount', { minAmount });
    }
    if (maxAmount !== undefined) {
      queryBuilder.andWhere('callRecord.amount <= :maxAmount', { maxAmount });
    }
    if (httpStatus) {
      queryBuilder.andWhere('callRecord.httpStatus = :httpStatus', {
        httpStatus,
      });
    }
    if (clientIp) {
      queryBuilder.andWhere('callRecord.clientIp = :clientIp', { clientIp });
    }
    if (startDate) {
      queryBuilder.andWhere('callRecord.createdAt >= :startDate', {
        startDate,
      });
    }
    if (endDate) {
      queryBuilder.andWhere('callRecord.createdAt <= :endDate', { endDate });
    }

    // 排序
    queryBuilder.orderBy(`callRecord.${sortBy}`, sortOrder);

    // 分页
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    const [callRecords, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    const data = callRecords.map((callRecord) =>
      this.formatCallRecordResponse(callRecord),
    );

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * 根据ID查询调用记录
   */
  async findOne(id: number): Promise<CallRecordResponseDto> {
    const callRecord = await this.repository.findOne({
      where: { id },
      relations: ['user', 'service'],
    });

    if (!callRecord) {
      throw new NotFoundException('调用记录不存在');
    }

    return this.formatCallRecordResponse(callRecord);
  }

  /**
   * 根据请求ID查询调用记录
   */
  async findByRequestId(requestId: string): Promise<CallRecordResponseDto> {
    const callRecord = await this.repository.findOne({
      where: { requestId },
      relations: ['user', 'service'],
    });

    if (!callRecord) {
      throw new NotFoundException('调用记录不存在');
    }

    return this.formatCallRecordResponse(callRecord);
  }

  /**
   * 更新调用记录
   */
  async update(
    id: number,
    updateCallRecordDto: UpdateCallRecordDto,
  ): Promise<CallRecordResponseDto> {
    const callRecord = await this.repository.findOne({
      where: { id },
      relations: ['user', 'service'],
    });

    if (!callRecord) {
      throw new NotFoundException('调用记录不存在');
    }

    // 更新字段
    Object.assign(callRecord, updateCallRecordDto);

    const updatedEntity = await this.repository.save(callRecord);

    this.logger.log(`更新调用记录成功: ID=${id}`);

    return this.formatCallRecordResponse(updatedEntity, updateCallRecordDto);
  }

  /**
   * 删除调用记录
   */
  async remove(id: number): Promise<{ message: string }> {
    const callRecord = await this.repository.findOne({
      where: { id },
    });

    if (!callRecord) {
      throw new NotFoundException('调用记录不存在');
    }

    await this.repository.softDelete(id);

    this.logger.log(`删除调用记录成功: ID=${id}`);

    return { message: '调用记录已成功删除' };
  }

  /**
   * 批量删除调用记录
   */
  async batchRemove(ids: number[]): Promise<void> {
    if (!ids || ids.length === 0) {
      throw new BadRequestException('请提供要删除的记录ID');
    }

    const existingRecords = await this.repository.findByIds(ids);
    if (existingRecords.length !== ids.length) {
      throw new NotFoundException('部分调用记录不存在');
    }

    await this.repository.softDelete(ids);

    this.logger.log(`批量删除调用记录成功: 数量=${ids.length}`);
  }

  /**
   * 获取调用记录统计
   */
  async getStats(
    queryDto: QueryCallRecordDto = {},
  ): Promise<CallRecordStatsDto> {
    const { userId, serviceId, startDate, endDate } = queryDto;

    const queryBuilder = this.repository
      .createQueryBuilder('callRecord')
      .leftJoin('callRecord.user', 'user')
      .leftJoin('callRecord.service', 'service');

    // 添加查询条件
    if (userId) {
      queryBuilder.andWhere('user.id = :userId', { userId });
    }
    if (serviceId) {
      queryBuilder.andWhere('service.id = :serviceId', { serviceId });
    }
    if (startDate) {
      queryBuilder.andWhere('callRecord.createdAt >= :startDate', {
        startDate,
      });
    }
    if (endDate) {
      queryBuilder.andWhere('callRecord.createdAt <= :endDate', { endDate });
    }

    // 基础统计
    const totalCalls = await queryBuilder.getCount();
    const successCalls = await queryBuilder
      .clone()
      .andWhere('callRecord.success = :success', { success: true })
      .getCount();
    const failedCalls = totalCalls - successCalls;
    const successRate = totalCalls > 0 ? (successCalls / totalCalls) * 100 : 0;

    // 聚合统计
    const aggregateResult = await queryBuilder
      .select([
        'SUM(callRecord.cost) as totalCost',
        'SUM(callRecord.amount) as totalAmount',
        'AVG(callRecord.responseTime) as avgResponseTime',
      ])
      .getRawOne();

    const totalCost = parseInt(aggregateResult.totalCost) || 0;
    const totalAmount = parseFloat(aggregateResult.totalAmount) || 0;
    const avgResponseTime = parseFloat(aggregateResult.avgResponseTime) || 0;

    // 按服务分组统计
    const byServiceResult = await this.repository
      .createQueryBuilder('callRecord')
      .leftJoin('callRecord.service', 'service')
      .select([
        'service.code as serviceCode',
        'COUNT(*) as calls',
        'SUM(CASE WHEN callRecord.success = true THEN 1 ELSE 0 END) as successCalls',
        'SUM(CASE WHEN callRecord.success = false THEN 1 ELSE 0 END) as failedCalls',
        'SUM(callRecord.cost) as totalCost',
        'SUM(callRecord.amount) as totalAmount',
        'AVG(callRecord.responseTime) as avgResponseTime',
      ])
      .groupBy('service.id')
      .getRawMany();

    const byService = byServiceResult.reduce((acc, item) => {
      acc[item.serviceCode] = {
        calls: parseInt(item.calls),
        successCalls: parseInt(item.successCalls),
        failedCalls: parseInt(item.failedCalls),
        totalCost: parseInt(item.totalCost),
        totalAmount: parseFloat(item.totalAmount),
        avgResponseTime: parseFloat(item.avgResponseTime),
      };
      return acc;
    }, {});

    // 按用户分组统计
    const byUserResult = await this.repository
      .createQueryBuilder('callRecord')
      .leftJoin('callRecord.user', 'user')
      .select([
        'user.username as username',
        'COUNT(*) as calls',
        'SUM(CASE WHEN callRecord.success = true THEN 1 ELSE 0 END) as successCalls',
        'SUM(CASE WHEN callRecord.success = false THEN 1 ELSE 0 END) as failedCalls',
        'SUM(callRecord.cost) as totalCost',
        'SUM(callRecord.amount) as totalAmount',
      ])
      .groupBy('user.id')
      .getRawMany();

    const byUser = byUserResult.reduce((acc, item) => {
      acc[item.username] = {
        calls: parseInt(item.calls),
        successCalls: parseInt(item.successCalls),
        failedCalls: parseInt(item.failedCalls),
        totalCost: parseInt(item.totalCost),
        totalAmount: parseFloat(item.totalAmount),
      };
      return acc;
    }, {});

    // 按小时分组统计
    const byHourResult = await this.repository
      .createQueryBuilder('callRecord')
      .select([
        'HOUR(callRecord.createdAt) as hour',
        'COUNT(*) as calls',
        'SUM(CASE WHEN callRecord.success = true THEN 1 ELSE 0 END) as successCalls',
        'SUM(CASE WHEN callRecord.success = false THEN 1 ELSE 0 END) as failedCalls',
      ])
      .groupBy('HOUR(callRecord.createdAt)')
      .getRawMany();

    const byHour = byHourResult.reduce((acc, item) => {
      acc[item.hour] = {
        calls: parseInt(item.calls),
        successCalls: parseInt(item.successCalls),
        failedCalls: parseInt(item.failedCalls),
      };
      return acc;
    }, {});

    // 按HTTP状态码分组统计
    const byHttpStatusResult = await this.repository
      .createQueryBuilder('callRecord')
      .select(['callRecord.httpStatus as httpStatus', 'COUNT(*) as count'])
      .where('callRecord.httpStatus IS NOT NULL')
      .groupBy('callRecord.httpStatus')
      .getRawMany();

    const byHttpStatus = byHttpStatusResult.reduce((acc, item) => {
      acc[item.httpStatus] = parseInt(item.count);
      return acc;
    }, {});

    return {
      totalCalls,
      successCalls,
      failedCalls,
      successRate: Math.round(successRate * 100) / 100,
      totalCost,
      totalAmount,
      avgResponseTime: Math.round(avgResponseTime),
      byService,
      byUser,
      byHour,
      byHttpStatus,
    };
  }

  /**
   * 格式化调用记录响应数据
   */
  private formatCallRecordResponse(
    callRecord: CallRecordEntity,
    additionalData?: Partial<CallRecordResponseDto>,
  ): CallRecordResponseDto {
    const response: CallRecordResponseDto = {
      id: callRecord.id,
      userId: callRecord.user?.id,
      serviceId: callRecord.service?.id,
      requestId: callRecord.requestId,
      input: callRecord.input,
      output: callRecord.output,
      cost: callRecord.cost,
      amount: callRecord.amount,
      success: callRecord.success,
      createdAt: callRecord.createdAt,
      updatedAt: callRecord.updatedAt,
    };

    // 添加用户信息
    if (callRecord.user) {
      response.user = {
        id: callRecord.user.id,
        username: callRecord.user.username,
        email: callRecord.user.email,
      };
    }

    // 添加服务信息
    if (callRecord.service) {
      response.service = {
        id: callRecord.service.id,
        code: callRecord.service.code,
        name: callRecord.service.name,
        type: callRecord.service.type,
      };
    }

    // 合并额外数据
    if (additionalData) {
      Object.assign(response, additionalData);
    }

    return response;
  }

  /**
   * 转换更新DTO
   */
  protected transformUpdateDto(
    updateDto: UpdateCallRecordDto,
    entity: CallRecordEntity,
  ): Partial<CallRecordEntity> {
    const transformed: Partial<CallRecordEntity> = {};

    if (updateDto.output !== undefined) {
      transformed.output = updateDto.output;
    }
    if (updateDto.success !== undefined) {
      transformed.success = updateDto.success;
    }

    return transformed;
  }
}
