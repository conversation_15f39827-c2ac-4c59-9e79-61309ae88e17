import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  AlertRecordEntity,
  AlertType,
  AlertLevel,
  AlertStatus,
} from '../entities/alert-record.entity';
import { EnhancedQueueManagerService } from '../../queue/services/enhanced-queue-manager.service';
import { UserService } from '../../user/user.service';
import { ServiceService } from '../../service/service.service';
import { RedisService } from '../../../shared/redis.service';
import { PRIORITY_LEVELS } from '../../queue/queue.constants'

/**
 * 预警处理服务
 * 使用QueueManagerService统一处理预警队列
 */
@Injectable()
export class AlertService {
  private readonly logger = new Logger(AlertService.name);
  private readonly ALERT_COOLDOWN_PREFIX = 'alert_cooldown:';
  private readonly COOLDOWN_PERIOD = 3600; // 1小时冷却期

  constructor(
    @InjectRepository(AlertRecordEntity)
    private readonly alertRecordRepository: Repository<AlertRecordEntity>,
    private readonly queueManagerService: EnhancedQueueManagerService,
    private readonly redisService: RedisService,
    private readonly userService: UserService,
    private readonly serviceService: ServiceService,
  ) {}

  /**
   * 发送预警到统一队列
   */
  async sendAlert(alertData: {
    userId: number;
    serviceId?: number;
    alertType: string;
    threshold?: number;
    currentValue?: number;
    message: string;
    metadata?: any;
    priority?: 'HIGH' | 'NORMAL' | 'LOW';
  }): Promise<boolean> {
    try {
      // 检查冷却期
      const cooldownKey = `${this.ALERT_COOLDOWN_PREFIX}${alertData.userId}:${alertData.serviceId || 'system'}:${alertData.alertType}`;
      const inCooldown = await this.redisService.get(cooldownKey);

      if (inCooldown) {
        this.logger.log(
          `预警在冷却期内，跳过: ${alertData.alertType} for user ${alertData.userId}`,
        );
        return false;
      }

      // 使用QueueManagerService处理预警
      const alertDto = {
        userId: alertData.userId,
        serviceId: alertData.serviceId,
        alertType: alertData.alertType,
        threshold: alertData.threshold,
        currentValue: alertData.currentValue,
        message: alertData.message,
        metadata: alertData.metadata,
        level:
          alertData.priority === 'HIGH'
            ? 'critical'
            : alertData.priority === 'LOW'
              ? 'info'
              : 'warning',
        channels: ['system'],
      };

      const job = await this.queueManagerService.addJob(
        'alert',
        'alert_processing',
        alertDto,
        {
          priority: alertData.priority === 'HIGH' 
            ? PRIORITY_LEVELS.HIGH 
            : alertData.priority === 'LOW' 
              ? PRIORITY_LEVELS.LOW 
              : PRIORITY_LEVELS.NORMAL
        }
      );

      if (job) {
        // 设置冷却期
        await this.redisService.setex(cooldownKey, this.COOLDOWN_PERIOD, '1');
        this.logger.log(
          `预警已发送到队列: ${alertData.alertType} for user ${alertData.userId}, taskId: ${job.id}`,
        );
      }

      return !!job;
    } catch (error) {
      this.logger.error(`发送预警失败: ${error.message}`, error);
      return false;
    }
  }

  /**
   * 处理预警（由QueueManagerService调用）
   */
  async handleAlert(alertData: {
    userId: number;
    serviceId?: number;
    alertType: string;
    threshold?: number;
    currentValue?: number;
    message: string;
    metadata?: any;
    priority?: 'HIGH' | 'NORMAL' | 'LOW';
  }): Promise<void> {
    try {
      // 获取用户信息
      const user = await this.userService.findById(alertData.userId);
      if (!user) {
        this.logger.error(`用户不存在: ${alertData.userId}`);
        return;
      }

      // 获取服务信息（如果有serviceId）
      let service: any = null;
      if (alertData.serviceId) {
        service = await this.serviceService.findOne(alertData.serviceId);
      }

      // 创建预警记录
      const alertRecord = this.alertRecordRepository.create({
        userId: alertData.userId,
        serviceId: alertData.serviceId,
        type: this.mapAlertType(alertData.alertType),
        level: this.mapAlertLevel(alertData.priority || 'NORMAL'),
        title: this.generateAlertTitle(alertData.alertType, service?.name),
        content: alertData.message,
        threshold: alertData.threshold?.toString(),
        currentValue: alertData.currentValue?.toString(),
        metadata: alertData.metadata || undefined,
        status: AlertStatus.PENDING,
      });

      await this.alertRecordRepository.save(alertRecord);

      // 发送通知（通过UnifiedQueueService）
      await this.sendAlertNotifications(user, alertRecord, service);

      // 更新预警记录状态
      alertRecord.status = AlertStatus.ACKNOWLEDGED;
      alertRecord.notified = true;
      await this.alertRecordRepository.save(alertRecord);

      this.logger.log(
        `预警处理完成: ${alertData.alertType} for user ${alertData.userId}`,
      );
    } catch (error) {
      this.logger.error(`处理预警失败: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * 发送预警通知
   */
  private async sendAlertNotifications(
    user: any,
    alertRecord: AlertRecordEntity,
    service: any,
  ): Promise<void> {
    const notificationData = {
      userId: user.id,
      alertType: alertRecord.type,
      title: alertRecord.title,
      message: alertRecord.content,
      serviceName: service?.name,
      threshold: alertRecord.threshold,
      currentValue: alertRecord.currentValue,
      timestamp: alertRecord.createdAt.toISOString(),
      metadata: alertRecord.metadata,
    };

    // 发送邮件通知
    if (user.email) {
      const emailDto = {
        data: {
          to: user.email,
          subject: alertRecord.title,
          template: this.getEmailTemplate(alertRecord.type),
          context: notificationData,
          priority: this.convertPriorityToNumber(
            this.getNotificationPriority(alertRecord.type),
          ),
        },
      };
      // TODO:调用邮件服务发送邮件
    }

    // 发送短信通知（仅高优先级）
    if (user.phone && this.shouldSendSMS(alertRecord.type)) {
      const smsDto = {
        data: {
          phone: user.phone,
          template: this.getSMSTemplate(alertRecord.type),
          params: notificationData,
          priority: this.convertPriorityToNumber(
            this.getNotificationPriority(alertRecord.type),
          ),
        },
      };
      // TODO: 发送短信
    }

    // TODO: 发送Webhook通知 - 需要实现新的Webhook队列服务
    if (user.webhookUrl) {
      this.logger.log(`Webhook通知暂未实现: ${user.webhookUrl}`);
      // 可以通过HTTP直接调用或者实现专门的Webhook队列
    }
  }

  /**
   * 映射预警级别
   */
  private mapAlertLevel(priority: string): AlertLevel {
    switch (priority) {
      case 'HIGH':
        return AlertLevel.HIGH;
      case 'NORMAL':
        return AlertLevel.MEDIUM;
      case 'LOW':
        return AlertLevel.LOW;
      default:
        return AlertLevel.MEDIUM;
    }
  }

  /**
   * 映射预警类型
   */
  private mapAlertType(alertType: string): AlertType {
    switch (alertType) {
      case 'QUOTA_WARNING':
        return AlertType.OTHER;
      case 'QUOTA_EXHAUSTED':
        return AlertType.QUOTA_EXCEEDED;
      case 'PAYMENT_FAILED':
        return AlertType.PAYMENT;
      case 'SYSTEM_ERROR':
        return AlertType.SYSTEM_ERROR;
      case 'LOW_BALANCE':
        return AlertType.LOW_BALANCE;
      case 'RATE_LIMIT':
        return AlertType.RATE_LIMIT;
      case 'ERROR_RATE':
        return AlertType.ERROR_RATE;
      case 'SERVICE_ERROR':
        return AlertType.SERVICE_ERROR;
      case 'HIGH_FREQUENCY':
        return AlertType.HIGH_FREQUENCY;
      case 'RESPONSE_TIME':
        return AlertType.RESPONSE_TIME;
      case 'SECURITY':
        return AlertType.SECURITY;
      default:
        return AlertType.OTHER;
    }
  }

  /**
   * 生成预警标题
   */
  private generateAlertTitle(alertType: string, serviceName?: string): string {
    const service = serviceName || '服务';

    switch (alertType) {
      case 'QUOTA_WARNING':
        return `${service}额度预警`;
      case 'QUOTA_EXHAUSTED':
        return `${service}额度已用完`;
      case 'PAYMENT_FAILED':
        return '支付失败通知';
      case 'SYSTEM_ERROR':
        return '系统错误通知';
      case 'LOW_BALANCE':
        return '余额不足预警';
      case 'RATE_LIMIT':
        return '频率限制预警';
      case 'ERROR_RATE':
        return '错误率预警';
      case 'SERVICE_ERROR':
        return `${service}服务异常`;
      case 'HIGH_FREQUENCY':
        return '高频访问预警';
      case 'RESPONSE_TIME':
        return '响应时间预警';
      case 'SECURITY':
        return '安全预警';
      default:
        return '系统通知';
    }
  }

  /**
   * 获取邮件模板
   */
  private getEmailTemplate(alertType: AlertType): string {
    switch (alertType) {
      case AlertType.OTHER:
        return 'quota_warning';
      case AlertType.QUOTA_EXCEEDED:
        return 'quota_exhausted';
      case AlertType.PAYMENT:
        return 'payment_failed';
      case AlertType.SYSTEM_ERROR:
        return 'system_error';
      case AlertType.LOW_BALANCE:
        return 'low_balance';
      case AlertType.RATE_LIMIT:
        return 'rate_limit';
      case AlertType.ERROR_RATE:
        return 'error_rate';
      case AlertType.SERVICE_ERROR:
        return 'service_error';
      case AlertType.HIGH_FREQUENCY:
        return 'high_frequency';
      case AlertType.RESPONSE_TIME:
        return 'response_time';
      case AlertType.SECURITY:
        return 'security_alert';
      default:
        return 'general_alert';
    }
  }

  /**
   * 获取短信模板
   */
  private getSMSTemplate(alertType: AlertType): string {
    switch (alertType) {
      case AlertType.OTHER:
        return 'sms_quota_warning';
      case AlertType.QUOTA_EXCEEDED:
        return 'sms_quota_exhausted';
      case AlertType.PAYMENT:
        return 'sms_payment_failed';
      case AlertType.SYSTEM_ERROR:
        return 'sms_system_error';
      case AlertType.LOW_BALANCE:
        return 'sms_low_balance';
      case AlertType.SECURITY:
        return 'sms_security_alert';
      default:
        return 'sms_general_alert';
    }
  }

  /**
   * 生成短信内容
   */
  private generateSMSContent(alertRecord: AlertRecordEntity): string {
    return `【系统预警】${alertRecord.title}：${alertRecord.content}`;
  }

  /**
   * 转换优先级为数字
   */
  private convertPriorityToNumber(priority: 'HIGH' | 'NORMAL' | 'LOW'): number {
    switch (priority) {
      case 'HIGH':
        return PRIORITY_LEVELS.HIGH;
      case 'NORMAL':
        return PRIORITY_LEVELS.NORMAL;
      case 'LOW':
        return PRIORITY_LEVELS.LOW;
      default:
        return PRIORITY_LEVELS.NORMAL;
    }
  }

  /**
   * 获取通知优先级
   */
  private getNotificationPriority(
    alertType: AlertType,
  ): 'HIGH' | 'NORMAL' | 'LOW' {
    switch (alertType) {
      case AlertType.QUOTA_EXCEEDED:
      case AlertType.PAYMENT:
      case AlertType.SYSTEM_ERROR:
      case AlertType.SECURITY:
        return 'HIGH';
      case AlertType.LOW_BALANCE:
      case AlertType.ERROR_RATE:
      case AlertType.SERVICE_ERROR:
        return 'NORMAL';
      default:
        return 'LOW';
    }
  }

  /**
   * 判断是否应该发送短信
   */
  private shouldSendSMS(alertType: AlertType): boolean {
    // 只有高优先级的预警才发送短信
    return [
      AlertType.QUOTA_EXCEEDED,
      AlertType.PAYMENT,
      AlertType.SYSTEM_ERROR,
      AlertType.SECURITY,
    ].includes(alertType);
  }

  /**
   * 获取预警统计
   */
  async getAlertStats(userId: string, days: number = 7): Promise<any> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const stats = await this.alertRecordRepository
      .createQueryBuilder('alert')
      .select('alert.type', 'alertType')
      .addSelect('COUNT(*)', 'count')
      .where('alert.userId = :userId', { userId })
      .andWhere('alert.createdAt >= :startDate', { startDate })
      .groupBy('alert.type')
      .getRawMany();

    return stats.reduce((acc, stat) => {
      acc[stat.alertType] = parseInt(stat.count, 10);
      return acc;
    }, {});
  }

  /**
   * 清理过期的预警记录
   */
  async cleanupExpiredAlerts(days: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const result = await this.alertRecordRepository
      .createQueryBuilder()
      .delete()
      .where('createdAt < :cutoffDate', { cutoffDate })
      .execute();

    this.logger.log(`清理了 ${result.affected} 条过期预警记录`);
    return result.affected || 0;
  }
}
