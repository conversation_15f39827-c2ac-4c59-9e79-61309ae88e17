# 业务接口调用管理需求文档

## 1. 文档概述

本文档描述了开放平台业务接口调用管理模块的需求规范，该模块基于单一职责原则设计，专注于API调用次数的管理、记录和计费功能。业务接口调用管理模块负责跟踪和管理用户对各服务接口的调用情况，确保准确计费和调用限制，并提供调用统计分析能力。

## 2. 功能需求

### 2.1 核心功能

- **调用次数管理**
  - 记录服务总调用次数
  - 记录已使用调用次数
  - 计算剩余调用次数
  - 调用次数不足时拒绝请求
  - 支持次数预警通知

- **调用记录**
  - 记录所有调用的关键信息（包括成功和失败调用）
  - 记录最小必要字段集（服务接口ID、用户ID、请求方式、时间戳等）
  - 对于失败调用，额外记录失败原因
  - 支持按用户、服务和状态查询调用记录
  - 定期清理历史记录（每2个月）

- **次数扣减**
  - 成功调用后原子性扣减次数
  - 使用分布式锁防止并发扣减冲突
  - 支持事务保证数据一致性
  - 扣减失败时的异常处理

- **次数增加**
  - 支持购买增加次数
  - 支持充值后购买增加次数
  - 支持赠送免费次数
  - 次数变更历史记录

### 2.2 非功能需求

- **高性能**：调用次数扣减响应时间<20ms
- **高可用性**：确保计费系统高可用，避免影响业务调用
- **数据一致性**：保证调用次数和记录的准确性
- **并发安全**：支持高并发调用场景下的次数管理
- **可扩展性**：支持未来新增服务类型和计费模式
- **存储优化**：通过精简记录和定期清理减少存储开销

## 3. 技术规范

### 3.1 数据模型

```typescript
// 用户服务关联实体
@Entity('user_services')
export class UserServiceEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  userId: number;

  @Column()
  serviceId: number;

  @Column({ type: 'int', default: 0 })
  totalCount: number;  // 总调用次数

  @Column({ type: 'int', default: 0 })
  usedCount: number;   // 已使用次数

  @Column({ nullable: true })
  warningThreshold: number; // 预警阈值

  @Column({ default: false })
  isWarned: boolean;   // 是否已发送预警

  @Column()
  createdAt: Date;

  @Column()
  updatedAt: Date;

  // 计算剩余次数
  @AfterLoad()
  calculateRemainingCount() {
    this.remainingCount = this.totalCount - this.usedCount;
    return this.remainingCount;
  }
}

// 调用记录实体 - 精简版
@Entity('call_records')
export class CallRecordEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: number;

  @Column()
  serviceId: number;

  @Column({ length: 10 })
  method: string;  // HTTP方法: GET, POST等

  @Column({ nullable: true, length: 64 })
  requestId: string;  // 请求唯一标识

  @Column({ type: 'enum', enum: CallStatus })
  status: CallStatus;  // 调用状态: success, failed

  @Column({ nullable: true, length: 255 })
  failReason: string;  // 失败原因，仅当status=failed时有值

  @Column({ type: 'int' })
  duration: number;  // 调用耗时(ms)

  @Column()
  createdAt: Date;

  @Column({ type: 'inet', nullable: true })
  ipAddress: string;

  @Index()
  @Column({ nullable: true, length: 36 })
  apiKeyId: string;  // 使用的API密钥ID
}

export enum CallStatus {
  SUCCESS = 'success',
  FAILED = 'failed',
  QUOTA_EXCEEDED = 'quota_exceeded'
}

// 调用统计实体 - 用于聚合存储
@Entity('call_statistics')
export class CallStatisticsEntity {
  @PrimaryGeneratedColumn()
  id: number;
  
  @Column()
  userId: number;
  
  @Column()
  serviceId: number;
  
  @Column({ type: 'date' })
  date: Date;  // 统计日期
  
  @Column({ type: 'int' })
  totalCalls: number;  // 总调用次数
  
  @Column({ type: 'int' })
  successCalls: number;  // 成功调用次数
  
  @Column({ type: 'int' })
  failedCalls: number;  // 失败调用次数
  
  @Column({ type: 'int' })
  totalDuration: number;  // 总耗时
  
  @Column({ type: 'float' })
  avgDuration: number;  // 平均耗时
}
```

### 3.2 核心功能实现

```typescript
// 简化示意 - 次数扣减实现
@Injectable()
export class UserServiceService {
  constructor(
    @InjectRepository(UserServiceEntity)
    private userServiceRepository: Repository<UserServiceEntity>,
    @InjectRepository(CallRecordEntity)
    private callRecordRepository: Repository<CallRecordEntity>,
    private connection: Connection,
    private logger: LoggerService,
    private eventEmitter: EventEmitter2
  ) {}

  /**
   * 更新使用次数 - 使用悲观锁确保原子性
   * @param userId 用户ID
   * @param serviceId 服务ID
   * @param increment 增量，默认为1
   * @param queryRunner 可选的查询运行器，用于事务
   */
  async updateUsedCount(
    userId: number,
    serviceId: number,
    increment = 1,
    queryRunner?: QueryRunner
  ): Promise<UserServiceEntity> {
    // 确定是否需要创建和管理事务
    const shouldManageTransaction = !queryRunner;
    queryRunner = queryRunner || this.connection.createQueryRunner();
    
    if (shouldManageTransaction) {
      await queryRunner.connect();
      await queryRunner.startTransaction();
    }
    
    try {
      // 使用悲观锁获取用户服务记录
      const userService = await queryRunner.manager.findOne(UserServiceEntity, {
        where: { userId, serviceId },
        lock: { mode: 'pessimistic_write' }
      });
      
      if (!userService) {
        throw new NotFoundException(`用户 ${userId} 的服务 ${serviceId} 不存在`);
      }
      
      // 检查剩余次数
      if (userService.totalCount - userService.usedCount < increment) {
        throw new BadRequestException('调用次数不足');
      }
      
      // 更新使用次数
      userService.usedCount += increment;
      userService.updatedAt = new Date();
      
      // 检查是否需要发送预警
      if (
        !userService.isWarned && 
        userService.warningThreshold && 
        (userService.totalCount - userService.usedCount) <= userService.warningThreshold
      ) {
        userService.isWarned = true;
        // 触发预警事件
        this.eventEmitter.emit('user.service.warning', {
          userId,
          serviceId,
          remainingCount: userService.totalCount - userService.usedCount
        });
      }
      
      // 保存更新
      const result = await queryRunner.manager.save(userService);
      
      // 如果我们负责事务，提交它
      if (shouldManageTransaction) {
        await queryRunner.commitTransaction();
      }
      
      return result;
    } catch (error) {
      // 如果我们负责事务且发生错误，回滚
      if (shouldManageTransaction && queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      
      this.logger.error(
        `更新用户 ${userId} 服务 ${serviceId} 使用次数失败: ${error.message}`,
        error.stack,
        'UserServiceService'
      );
      
      throw error;
    } finally {
      // 如果我们负责事务，释放资源
      if (shouldManageTransaction) {
        await queryRunner.release();
      }
    }
  }

  /**
   * 添加服务调用次数
   * @param userId 用户ID
   * @param serviceId 服务ID
   * @param purchaseCount 购买的次数
   */
  async addServiceCount(
    userId: number,
    serviceId: number,
    purchaseCount: number
  ): Promise<UserServiceEntity> {
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      // 使用悲观锁获取用户服务记录
      let userService = await queryRunner.manager.findOne(UserServiceEntity, {
        where: { userId, serviceId },
        lock: { mode: 'pessimistic_write' }
      });
      
      // 如果记录不存在，创建新记录
      if (!userService) {
        userService = queryRunner.manager.create(UserServiceEntity, {
          userId,
          serviceId,
          totalCount: 0,
          usedCount: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
      
      // 增加总次数
      userService.totalCount += purchaseCount;
      userService.updatedAt = new Date();
      
      // 如果之前发过预警，但现在次数充足，重置预警状态
      if (
        userService.isWarned && 
        userService.warningThreshold && 
        (userService.totalCount - userService.usedCount) > userService.warningThreshold
      ) {
        userService.isWarned = false;
      }
      
      // 保存更新
      const result = await queryRunner.manager.save(userService);
      
      // 创建购买记录
      await queryRunner.manager.insert(ServiceCountChangeRecord, {
        userId,
        serviceId,
        changeType: 'purchase',
        amount: purchaseCount,
        createdAt: new Date()
      });
      
      // 提交事务
      await queryRunner.commitTransaction();
      
      return result;
    } catch (error) {
      // 发生错误，回滚事务
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      
      this.logger.error(
        `为用户 ${userId} 添加服务 ${serviceId} 次数失败: ${error.message}`,
        error.stack,
        'UserServiceService'
      );
      
      throw error;
    } finally {
      // 释放资源
      await queryRunner.release();
    }
  }
}
```

### 3.3 调用记录管理

```typescript
// 简化示意 - 调用记录管理
@Injectable()
export class CallRecordService {
  constructor(
    @InjectRepository(CallRecordEntity)
    private callRecordRepository: Repository<CallRecordEntity>,
    @InjectRepository(CallStatisticsEntity)
    private callStatisticsRepository: Repository<CallStatisticsEntity>,
    private dataSource: DataSource,
    private logger: LoggerService,
    private configService: ConfigService
  ) {}

  /**
   * 创建调用记录
   */
  async createCallRecord(data: CreateCallRecordDto): Promise<CallRecordEntity> {
    try {
      const callRecord = this.callRecordRepository.create({
        ...data,
        createdAt: new Date()
      });
      
      return await this.callRecordRepository.save(callRecord);
    } catch (error) {
      this.logger.error(
        `创建调用记录失败: ${error.message}`,
        error.stack,
        'CallRecordService'
      );
      
      // 确保调用记录失败不会影响主业务流程
      return null;
    }
  }

  /**
   * 查询用户服务调用记录
   */
  async getUserServiceCallRecords(
    userId: number,
    serviceId: number,
    queryParams: CallRecordQueryDto
  ): Promise<{ records: CallRecordEntity[]; total: number }> {
    const { status, startDate, endDate, page, limit } = queryParams;
    
    const queryBuilder = this.callRecordRepository.createQueryBuilder('record')
      .where('record.userId = :userId', { userId })
      .andWhere('record.serviceId = :serviceId', { serviceId });
    
    // 添加状态过滤
    if (status) {
      queryBuilder.andWhere('record.status = :status', { status });
    }
    
    // 添加日期过滤
    if (startDate) {
      queryBuilder.andWhere('record.createdAt >= :startDate', { 
        startDate: new Date(startDate) 
      });
    }
    
    if (endDate) {
      queryBuilder.andWhere('record.createdAt <= :endDate', { 
        endDate: new Date(endDate) 
      });
    }
    
    // 添加分页
    const pageNum = page || 1;
    const pageSize = limit || 10;
    queryBuilder.skip((pageNum - 1) * pageSize).take(pageSize);
    
    // 添加排序
    queryBuilder.orderBy('record.createdAt', 'DESC');
    
    // 执行查询
    const [records, total] = await queryBuilder.getManyAndCount();
    
    return { records, total };
  }

  /**
   * 生成每日调用统计
   * 该方法由schedule模块的定时任务调用，每天凌晨2:00执行
   */
  async generateDailyStatistics(): Promise<void> {
    // 获取昨天的日期
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);
    
    const endDate = new Date(yesterday);
    endDate.setDate(endDate.getDate() + 1);
    
    try {
      this.logger.log(
        `开始生成 ${yesterday.toISOString().split('T')[0]} 的调用统计`,
        'CallRecordService'
      );
      
      // 按用户和服务统计
      const stats = await this.callRecordRepository
        .createQueryBuilder('record')
        .select('record.userId', 'userId')
        .addSelect('record.serviceId', 'serviceId')
        .addSelect('COUNT(*)', 'totalCalls')
        .addSelect('SUM(CASE WHEN record.status = :successStatus THEN 1 ELSE 0 END)', 'successCalls')
        .addSelect('SUM(CASE WHEN record.status = :failedStatus THEN 1 ELSE 0 END)', 'failedCalls')
        .addSelect('SUM(record.duration)', 'totalDuration')
        .addSelect('AVG(record.duration)', 'avgDuration')
        .where('record.createdAt >= :startDate', { startDate: yesterday })
        .andWhere('record.createdAt < :endDate', { endDate })
        .setParameter('successStatus', CallStatus.SUCCESS)
        .setParameter('failedStatus', CallStatus.FAILED)
        .groupBy('record.userId')
        .addGroupBy('record.serviceId')
        .getRawMany();
      
      // 保存统计结果
      const entities = stats.map(stat => this.callStatisticsRepository.create({
        userId: stat.userId,
        serviceId: stat.serviceId,
        date: yesterday,
        totalCalls: parseInt(stat.totalCalls),
        successCalls: parseInt(stat.successCalls),
        failedCalls: parseInt(stat.failedCalls),
        totalDuration: parseInt(stat.totalDuration) || 0,
        avgDuration: parseFloat(stat.avgDuration) || 0
      }));
      
      if (entities.length > 0) {
        await this.callStatisticsRepository.save(entities);
      }
      
      this.logger.log(
        `调用统计生成完成 - 共生成 ${entities.length} 条统计记录`,
        'CallRecordService'
      );
    } catch (error) {
      this.logger.error(
        `生成调用统计失败: ${error.message}`,
        error.stack,
        'CallRecordService'
      );
    }
  }

  /**
   * 清理过期调用记录
   * 该方法由schedule模块的定时任务调用，每两个月执行一次
   */
  async cleanupCallRecords(): Promise<void> {
    try {
      // 计算截止日期（2个月前）
      const cutoffDate = new Date();
      cutoffDate.setMonth(cutoffDate.getMonth() - 2);
      cutoffDate.setDate(1);
      cutoffDate.setHours(0, 0, 0, 0);
      
      this.logger.log(
        `开始清理调用记录 - 清理 ${cutoffDate.toISOString().split('T')[0]} 之前的记录`,
        'CallRecordService'
      );
      
      // 批量删除数据，避免锁表
      const batchSize = this.configService.get<number>('callRecords.cleanupBatchSize', 10000);
      let totalDeleted = 0;
      let batchDeleted = 0;
      
      do {
        const result = await this.dataSource.createQueryBuilder()
          .delete()
          .from(CallRecordEntity)
          .where('createdAt < :cutoffDate', { cutoffDate })
          .limit(batchSize)
          .execute();
        
        batchDeleted = result.affected || 0;
        totalDeleted += batchDeleted;
        
        // 每批次间隔，减少数据库压力
        if (batchDeleted > 0) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } while (batchDeleted > 0);
      
      this.logger.log(
        `调用记录清理完成 - 共删除 ${totalDeleted} 条记录`,
        'CallRecordService'
      );
    } catch (error) {
      this.logger.error(
        `调用记录清理失败: ${error.message}`,
        error.stack,
        'CallRecordService'
      );
    }
  }
}
```

### 3.4 定时任务配置

```typescript
// 简化示意 - schedule模块中的定时任务配置
@Injectable()
export class CallRecordScheduleService {
  constructor(
    private readonly callRecordService: CallRecordService,
    private readonly logger: LoggerService
  ) {}

  /**
   * 每天凌晨2:00执行生成每日调用统计
   */
  @Cron('0 0 2 * * *')
  async handleDailyStatistics() {
    this.logger.log('开始执行每日调用统计任务', 'CallRecordScheduleService');
    await this.callRecordService.generateDailyStatistics();
  }

  /**
   * 每两个月执行一次清理过期调用记录
   * 在每单月的1号凌晨3:30执行
   */
  @Cron('0 30 3 1 1,3,5,7,9,11 *')
  async handleCallRecordCleanup() {
    this.logger.log('开始执行调用记录清理任务', 'CallRecordScheduleService');
    await this.callRecordService.cleanupCallRecords();
  }
}
```

### 3.5 定时任务失败处理机制

```typescript
// 简化示意 - 定时任务失败处理机制实现
@Injectable()
export class CallRecordScheduleService {
  private readonly retryDelay = [0, 5 * 60 * 1000, 15 * 60 * 1000]; // 重试延迟：立即、5分钟、15分钟
  private readonly maxRetries = 3; // 最多重试3次
  private taskLock = new Map<string, boolean>();

  /**
   * 定时任务执行器 - 包含失败重试逻辑
   * @param taskName 任务名称
   * @param task 任务执行函数
   */
  private async executeWithRetry(taskName: string, task: () => Promise<void>): Promise<void> {
    // 使用任务名称作为锁的键，确保同一任务不会并发执行
    if (this.taskLock.get(taskName)) {
      this.logger.warn(`任务${taskName}已在执行中，跳过本次执行`);
      return;
    }

    this.taskLock.set(taskName, true);
    let retryCount = 0;

    try {
      // 首次执行
      await task();
      this.logger.log(`任务${taskName}执行成功`);
    } catch (error) {
      // 记录失败
      this.logger.error(`任务${taskName}执行失败: ${error.message}`, error.stack);
      
      // 重试逻辑
      while (retryCount < this.maxRetries) {
        try {
          // 按延迟时间等待
          if (this.retryDelay[retryCount] > 0) {
            this.logger.log(`等待${this.retryDelay[retryCount] / 1000}秒后重试任务${taskName}`);
            await new Promise(resolve => setTimeout(resolve, this.retryDelay[retryCount]));
          }
          
          this.logger.log(`开始重试任务${taskName}，第${retryCount + 1}次尝试`);
          await task();
          this.logger.log(`任务${taskName}重试成功`);
          break; // 成功则退出重试循环
        } catch (retryError) {
          retryCount++;
          this.logger.error(
            `任务${taskName}第${retryCount}次重试失败: ${retryError.message}`, 
            retryError.stack
          );
          
          // 达到最大重试次数，发送告警
          if (retryCount >= this.maxRetries) {
            this.sendTaskFailureAlert(taskName, retryError);
          }
        }
      }
    } finally {
      this.taskLock.set(taskName, false);
    }
  }
  
  /**
   * 发送任务失败告警
   * @param taskName 任务名称
   * @param error 错误对象
   */
  private sendTaskFailureAlert(taskName: string, error: Error): void {
    // 记录到日志系统
    this.logger.error(
      `任务${taskName}失败，已达到最大重试次数(${this.maxRetries})，需要手动干预`,
      error.stack
    );
    
    // TODO: 发送告警邮件/短信/站内通知
    // this.notificationService.sendAlert({
    //   level: 'critical',
    //   title: `定时任务${taskName}失败`,
    //   message: `任务${taskName}已重试${this.maxRetries}次仍然失败，错误信息: ${error.message}`,
    //   source: 'CallRecordScheduleService'
    // });
  }

  /**
   * 每天凌晨2:00执行生成每日调用统计 - 使用重试机制
   */
  @Cron('0 0 2 * * *')
  async handleDailyStatistics() {
    await this.executeWithRetry('generateDailyStatistics', async () => {
      this.logger.log('开始执行每日调用统计任务');
      await this.callRecordService.generateDailyStatistics();
      this.logger.log('每日调用统计任务完成');
    });
  }

  /**
   * 每两个月执行一次清理过期调用记录 - 使用重试机制
   */
  @Cron('0 30 3 1 1,3,5,7,9,11 *')
  async handleCallRecordCleanup() {
    await this.executeWithRetry('cleanupCallRecords', async () => {
      this.logger.log('开始执行调用记录清理任务');
      await this.callRecordService.cleanupCallRecords();
      this.logger.log('调用记录清理任务完成');
    });
  }
}
```

## 4. 系统架构

### 4.1 模块结构

```
user-service/
├── dto/
│   ├── create-call-record.dto.ts    # 创建调用记录DTO
│   ├── call-record-query.dto.ts     # 调用记录查询DTO
│   └── call-statistics.dto.ts       # 调用统计DTO
├── entities/
│   ├── user-service.entity.ts       # 用户服务关联实体
│   ├── call-record.entity.ts        # 调用记录实体（精简版）
│   ├── call-statistics.entity.ts    # 调用统计实体
│   └── service-count-change.entity.ts # 次数变更记录实体
├── services/
│   ├── user-service.service.ts      # 用户服务核心服务
│   └── call-record.service.ts       # 调用记录服务
├── controllers/
│   ├── user-service.controller.ts   # 用户服务控制器
│   └── call-record.controller.ts    # 调用记录控制器
├── user-service.module.ts           # 模块定义
└── call-status.enum.ts              # 调用状态枚举

schedule/
├── services/
│   └── call-record-schedule.service.ts  # 调用记录定时任务服务
└── schedule.module.ts               # 定时任务模块定义
```

### 4.2 调用流程图

```
graph TD
    A[API请求] --> B[网关模块]
    B --> C[密钥验证]
    C --> D{验证通过?}
    D -->|否| E[返回401错误]
    D -->|是| F{检查调用次数}
    F -->|次数不足| G[返回402错误]
    F -->|次数充足| H[处理业务请求]
    H --> I{请求成功?}
    I -->|否| J[记录失败调用]
    I -->|是| K[扣减调用次数]
    K --> L{扣减成功?}
    L -->|是| M[记录成功调用]
    L -->|否| N[记录失败调用]
    J --> O[返回结果]
    M --> O
    N --> O
    
    P[Schedule模块] -->|每天凌晨2:00| Q[生成每日调用统计]
    P -->|每两个月1号凌晨3:30| R[清理过期调用记录]
```

## 5. 接口定义

### 5.1 创建调用记录

```
POST /call-records
Content-Type: application/json
Authorization: Bearer <token>

Request:
{
  "userId": 456,
  "serviceId": 789,
  "method": "POST",
  "requestId": "req_abcdef123456",
  "status": "success",
  "duration": 156,
  "ipAddress": "***********",
  "apiKeyId": "key_12345"
}

Response:
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "userId": 456,
  "serviceId": 789,
  "method": "POST",
  "requestId": "req_abcdef123456",
  "status": "success",
  "duration": 156,
  "createdAt": "2023-08-15T10:30:00Z",
  "ipAddress": "***********",
  "apiKeyId": "key_12345"
}
```

### 5.2 查询调用记录

```
GET /call-records?userId=456&serviceId=789&status=failed&startDate=2023-08-01&endDate=2023-08-15&page=1&limit=10
Authorization: Bearer <token>

Response:
{
  "records": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440001",
      "userId": 456,
      "serviceId": 789,
      "method": "POST",
      "requestId": "req_abcdef123457",
      "status": "failed",
      "failReason": "服务内部错误",
      "duration": 203,
      "createdAt": "2023-08-15T10:35:00Z",
      "ipAddress": "***********",
      "apiKeyId": "key_12345"
    },
    // 更多记录...
  ],
  "total": 15,
  "page": 1,
  "limit": 10
}
```

### 5.3 获取调用统计

```
GET /call-statistics?userId=456&serviceId=789&startDate=2023-08-01&endDate=2023-08-15
Authorization: Bearer <token>

Response:
{
  "statistics": [
    {
      "date": "2023-08-01",
      "totalCalls": 52,
      "successCalls": 48,
      "failedCalls": 4,
      "totalDuration": 7650,
      "avgDuration": 159.38
    },
    {
      "date": "2023-08-02",
      "totalCalls": 58,
      "successCalls": 52,
      "failedCalls": 6,
      "totalDuration": 8320,
      "avgDuration": 160.00
    },
    // 更多日期...
  ],
  "summary": {
    "totalCalls": 110,
    "successCalls": 100,
    "failedCalls": 10,
    "successRate": 0.91,
    "avgDuration": 159.70,
    "dateRange": {
      "start": "2023-08-01",
      "end": "2023-08-02"
    }
  }
}
```

## 6. 实现要点

### 6.1 原子性操作

- **悲观锁**：使用数据库悲观锁确保次数扣减的原子性
- **事务控制**：所有次数变更操作在事务中执行
- **失败回滚**：任何步骤失败时自动回滚事务

### 6.2 数据优化策略

- **精简记录字段**：仅保存必要字段，减少存储开销
- **记录所有调用**：记录成功和失败的调用，但保持字段精简
- **定期清理**：通过schedule模块定时清理过期记录（每2个月）
- **统计聚合**：生成聚合统计数据，便于长期分析

### 6.3 性能优化

- **索引优化**：为常用查询字段建立合适索引（如status、createdAt）
- **批量处理**：清理操作使用批量处理，避免锁表
- **表分区**：根据时间对调用记录表进行分区

### 6.4 安全考虑

- **权限验证**：确保只有授权用户能查询自己的调用记录
- **数据脱敏**：敏感信息不记录或进行脱敏处理
- **审计日志**：关键操作记录审计日志

## 7. 监控和可观测性

- **关键指标**：调用量、成功率、平均响应时间
- **性能监控**：监控扣减操作的性能和延迟
- **存储监控**：监控调用记录表的大小和增长趋势
- **清理任务监控**：监控schedule模块定时清理任务的执行情况
- **失败分析**：监控并分析失败调用的模式和趋势

## 8. 后续优化方向

- **自适应清理策略**：根据存储压力动态调整清理周期
- **冷热数据分离**：将热数据和冷数据分开存储
- **数据压缩**：对长期存储的统计数据进行压缩
- **多级存储**：实现数据分层存储，降低存储成本
- **失败分析工具**：提供更丰富的失败调用分析工具 