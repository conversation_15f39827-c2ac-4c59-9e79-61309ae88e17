import { request } from '@/utils/request'
import type {
  DashboardOverview,
  StatCard,
  UsageTrendData,
  ServiceDistributionItem,
  RecentActivity,
  TimeRange
} from '@/types/dashboard'
import type { SystemNotification, DashboardStats } from '@/stores/dashboard'

// 仪表盘API接口
export const dashboardApi = {
  // 获取仪表盘概览数据
  getOverview: (timeRange?: TimeRange) => {
    return request.get<DashboardOverview>('/op/dashboard/overview', {
      params: { timeRange }
    })
  },

  // 获取统计数据
  getStats: (timeRange?: TimeRange) => {
    return request.get<DashboardStats>('/op/dashboard/stats', {
      params: { timeRange }
    })
  },

  // 获取使用趋势
  getUsageTrend: (timeRange?: TimeRange) => {
    return request.get<UsageTrendData>('/op/dashboard/usage-trend', {
      params: { timeRange }
    })
  },

  // 获取服务分布
  getServiceDistribution: (timeRange?: TimeRange) => {
    return request.get<ServiceDistributionItem[]>('/op/dashboard/service-distribution', {
      params: { timeRange }
    })
  },

  // 获取最近活动
  getRecentActivities: (limit?: number) => {
    return request.get<RecentActivity[]>('/op/dashboard/activities', {
      params: { limit }
    })
  },

  // 获取系统通知
  getNotifications: (limit?: number) => {
    return request.get<SystemNotification[]>('/op/dashboard/notifications', {
      params: { limit }
    })
  },

  // 标记通知为已读
  markNotificationAsRead: (notificationId: string) => {
    return request.post(`/op/dashboard/notifications/${notificationId}/read`)
  },

  // 批量标记通知为已读
  markAllNotificationsAsRead: () => {
    return request.post('/op/dashboard/notifications/read-all')
  },

  // 删除通知
  deleteNotification: (notificationId: string) => {
    return request.delete(`/op/dashboard/notifications/${notificationId}`)
  }
}

export default dashboardApi