<template>
  <el-dialog
    v-model="visible"
    title="安全验证"
    :width="400"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="true"
    class="captcha-modal"
    append-to-body
  >
    <div class="captcha-modal-content">
      <!-- 验证成功闪烁效果 -->
      <div v-if="showSuccessFlash" class="success-flash-overlay">
        <div class="success-flash-content">
          <div class="success-icon">✓</div>
          <div class="success-text">验证成功</div>
        </div>
        <!-- 白色高亮线条效果 -->
        <div class="success-highlight-line"></div>
      </div>
      
      <div class="captcha-header">
        <div class="security-icon">
          <el-icon :size="32" color="#1890ff">
            <Lock />
          </el-icon>
        </div>
        <h3 class="captcha-title">安全验证</h3>
        <p class="captcha-description">
          为了保护您的账户安全，请完成以下验证
        </p>
      </div>
      
      <div class="captcha-container">
        <!-- 安全等级指示器 -->
        <div class="security-level-indicator">
          <span class="level-label">安全等级:</span>
          <el-tag 
            :type="securityLevel === 'high' ? 'success' : securityLevel === 'medium' ? 'warning' : 'info'"
            size="small"
          >
            {{ securityLevel === 'high' ? '高' : securityLevel === 'medium' ? '中' : '低' }}
          </el-tag>
        </div>
        
        <!-- 滑块验证组件 -->
        <SlideVerify
          ref="slideVerifyRef"
          :width="350"
          :height="180"
          :enable-behavior-tracking="true"
          :enable-device-fingerprint="true"
          @success="onCaptchaSuccess"
          @fail="onCaptchaFailure"
          @refresh="onCaptchaRefresh"
          @again="onCaptchaAgain"
        />
      </div>
      
      <!-- 验证提示 -->
      <div class="captcha-tips">
        <div class="tip-item">
          <el-icon size="14" color="#52c41a"><Check /></el-icon>
          <span>拖动滑块完成拼图验证</span>
        </div>
        <div class="tip-item">
          <el-icon size="14" color="#52c41a"><Check /></el-icon>
          <span>系统会自动检测您的行为模式</span>
        </div>
        <div class="tip-item">
          <el-icon size="14" color="#52c41a"><Check /></el-icon>
          <span>验证成功后将自动继续</span>
        </div>
      </div>
    </div>
    
    <!-- 只保留取消按钮 -->
    <template #footer>
      <div class="captcha-footer">
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElIcon } from 'element-plus'
import { Lock, Check } from '@element-plus/icons-vue'
import SlideVerify from './SlideVerify.vue'
import type { DeviceFingerprint } from '@/utils/deviceFingerprint'
import type { BehaviorPattern } from '@/utils/behaviorTracker'

// 定义 props
interface Props {
  modelValue: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 定义 emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: [data: {
    deviceFingerprint?: DeviceFingerprint
    behaviorPattern?: BehaviorPattern
    verificationTimestamp: number
    humanLikelihood: number
    securityLevel: string
  }]
  cancel: []
}>()

// 组件引用
const slideVerifyRef = ref()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isCaptchaVerified = ref(false)
const securityLevel = ref<'low' | 'medium' | 'high'>('low')
const securityData = ref<any>({})
const showSuccessFlash = ref(false)

// 重置验证码
const resetCaptcha = () => {
  isCaptchaVerified.value = false
  securityLevel.value = 'low'
  securityData.value = {}
  showSuccessFlash.value = false
  
  if (slideVerifyRef.value) {
    slideVerifyRef.value.reset()
  }
}

// 监听弹窗显示状态
watch(visible, (newVal) => {
  if (newVal) {
    // 弹窗打开时重置状态
    resetCaptcha()
  }
})

// 验证码成功回调
const onCaptchaSuccess = async (successData: {
  x: number
  y: number
  timestamp: number
  deviceFingerprint?: DeviceFingerprint
  behaviorPattern?: BehaviorPattern
}) => {
  console.log('验证成功数据:', successData)
  
  isCaptchaVerified.value = true
  
  // 计算安全等级
  let calculatedSecurityLevel: 'low' | 'medium' | 'high' = 'medium'
  let humanLikelihood = 0.7 // 默认人类可能性
  
  if (successData.behaviorPattern) {
    // 基于行为模式计算人类可能性
    const { mouseEvents, keyboardEvents, touchEvents } = successData.behaviorPattern
    
    // 简单的启发式算法
    let score = 0.5
    
    // 鼠标轨迹自然度
    if (mouseEvents && mouseEvents.length > 10) {
      score += 0.2
    }
    
    // 键盘事件存在
    if (keyboardEvents && keyboardEvents.length > 0) {
      score += 0.1
    }
    
    // 触摸事件（移动设备）
    if (touchEvents && touchEvents.length > 0) {
      score += 0.1
    }
    
    // 设备指纹完整性
    if (successData.deviceFingerprint) {
      score += 0.1
    }
    
    humanLikelihood = Math.min(score, 1.0)
    
    if (humanLikelihood >= 0.8) {
      calculatedSecurityLevel = 'high'
    } else if (humanLikelihood >= 0.6) {
      calculatedSecurityLevel = 'medium'
    } else {
      calculatedSecurityLevel = 'low'
    }
  }
  
  securityLevel.value = calculatedSecurityLevel
  securityData.value = {
    deviceFingerprint: successData.deviceFingerprint,
    behaviorPattern: successData.behaviorPattern,
    verificationTimestamp: successData.timestamp,
    humanLikelihood,
    securityLevel: calculatedSecurityLevel
  }
  
  // 显示成功闪烁效果
  showSuccessFlash.value = true
  
  // 延迟关闭弹窗并触发成功事件
  setTimeout(() => {
    emit('success', {
      deviceFingerprint: securityData.value.deviceFingerprint,
      behaviorPattern: securityData.value.behaviorPattern,
      verificationTimestamp: securityData.value.verificationTimestamp || Date.now(),
      humanLikelihood: securityData.value.humanLikelihood || 0,
      securityLevel: securityLevel.value
    })
    
    visible.value = false
    
    // 重置闪烁效果
    setTimeout(() => {
      showSuccessFlash.value = false
    }, 300)
  }, 1500) // 1.5秒后自动关闭
}

// 验证码失败回调
const onCaptchaFailure = (failureData?: {
  deviceFingerprint?: DeviceFingerprint
  behaviorPattern?: BehaviorPattern
}) => {
  isCaptchaVerified.value = false
  securityLevel.value = 'low'
  securityData.value = {}
  showSuccessFlash.value = false
  
  if (failureData) {
    console.warn('弹窗验证失败数据:', failureData)
  }
  
  ElMessage.error('验证失败，请重试')
}

// 验证码刷新回调
const onCaptchaRefresh = () => {
  isCaptchaVerified.value = false
  securityLevel.value = 'low'
  securityData.value = {}
  showSuccessFlash.value = false
}

// 验证码重试回调
const onCaptchaAgain = () => {
  isCaptchaVerified.value = false
  securityLevel.value = 'low'
  securityData.value = {}
  showSuccessFlash.value = false
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  visible.value = false
  resetCaptcha()
}

// 暴露方法
defineExpose({
  resetCaptcha
})
</script>

<style scoped>
.captcha-modal {
  --el-dialog-border-radius: 12px;
}

:deep(.el-dialog) {
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 0;
  border-bottom: none;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

:deep(.el-dialog__body) {
  padding: 10px 20px 20px;
}

:deep(.el-dialog__footer) {
  padding: 0 20px 20px;
  border-top: 1px solid #f0f0f0;
  margin-top: 20px;
  padding-top: 20px;
}

.captcha-modal-content {
  text-align: center;
  position: relative;
}

.captcha-header {
  margin-bottom: 20px;
}

.security-icon {
  margin-bottom: 12px;
}

.captcha-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.captcha-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.captcha-container {
  margin: 20px 0;
}

.security-level-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  gap: 8px;
}

.level-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.captcha-tips {
  margin-top: 20px;
  text-align: left;
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
  color: #4b5563;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.captcha-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.captcha-footer .el-button {
  min-width: 80px;
}

/* 验证成功闪烁效果 */
.success-flash-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: successFlash 1.5s ease-in-out;
  border-radius: 8px;
  overflow: hidden;
}

.success-flash-content {
  text-align: center;
  color: #52c41a;
  z-index: 1001;
  position: relative;
}

.success-icon {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 8px;
  animation: successIconPulse 0.6s ease-in-out;
  text-shadow: 0 0 10px rgba(82, 196, 26, 0.5);
}

.success-text {
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 0 5px rgba(82, 196, 26, 0.3);
}

/* 白色高亮线条效果 */
.success-highlight-line {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 20%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0.3) 80%,
    transparent 100%
  );
  animation: highlightSweep 1.2s ease-out;
  z-index: 1000;
}

@keyframes successFlash {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  20% {
    opacity: 1;
    transform: scale(1.02);
  }
  80% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0.9;
    transform: scale(1);
  }
}

@keyframes successIconPulse {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes highlightSweep {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }
  
  .captcha-container :deep(.slide-verify-container) {
    transform: scale(0.9);
    transform-origin: center;
  }
}

/* 动画效果 */
:deep(.el-dialog) {
  animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* 验证成功状态样式 */
.security-level-indicator .el-tag {
  font-weight: 500;
}

/* 滑块验证组件样式优化 */
:deep(.slide-verify-container) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>