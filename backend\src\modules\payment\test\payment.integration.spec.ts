import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { DataSource } from 'typeorm';
import request from 'supertest';
import { PaymentModule } from '../payment.module';
import { OrderModule } from '../../order/order.module';
import { UserModule } from '../../user/user.module';
import { ServiceModule } from '../../service/service.module';
import { UserServiceModule } from '../../user-service/user-service.module';
import { AuthModule } from '../../auth/auth.module';
import { PaymentEntity } from '../entities/payment.entity';
import { OrderEntity } from '../../order/entities/order.entity';
import { UserEntity } from '../../user/entities/user.entity';
import { PaymentMethod, PaymentStatus, OrderStatus, OrderType } from '../../order/enums/order.enum';
import {
  getTestDatabaseConfig,
  cleanupTestDatabase,
  createTestUser,
} from '../../order/test/order.test-config';

describe('Payment Integration Tests', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let authToken: string;
  let testUser: UserEntity;
  let testOrder: OrderEntity;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(getTestDatabaseConfig()),
        EventEmitterModule.forRoot(),
        PaymentModule,
        OrderModule,
        UserModule,
        ServiceModule,
        UserServiceModule,
        AuthModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    dataSource = moduleFixture.get<DataSource>(DataSource);
  });

  beforeEach(async () => {
    await cleanupTestDatabase(dataSource);
    
    // 创建测试用户
    const userRepository = dataSource.getRepository(UserEntity);
    testUser = await userRepository.save({
      ...createTestUser(),
      balance: 1000, // 设置足够的余额
    });
    
    // 创建测试订单
    const orderRepository = dataSource.getRepository(OrderEntity);
    testOrder = await orderRepository.save({
      userId: testUser.id,
      orderType: OrderType.SERVICE,
      status: OrderStatus.PENDING,
      totalAmount: 100.00,
      remark: '测试订单',
    });
    
    // 模拟用户登录获取token
    authToken = 'mock-jwt-token';
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /payments', () => {
    it('应该成功创建支付宝支付', async () => {
      const createPaymentDto = {
        orderId: testOrder.id,
        paymentMethod: PaymentMethod.ALIPAY,
        amount: testOrder.totalAmount,
        callbackUrl: 'https://example.com/callback',
        returnUrl: 'https://example.com/return',
      };

      const response = await request(app.getHttpServer())
        .post('/payments')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createPaymentDto)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('paymentId');
      expect(response.body.data).toHaveProperty('paymentNo');
      expect(response.body.data).toHaveProperty('paymentUrl');
      expect(response.body.data).toHaveProperty('qrCode');
      expect(response.body.data.paymentMethod).toBe(PaymentMethod.ALIPAY);
      expect(response.body.data.amount).toBe(testOrder.totalAmount);
    });

    it('应该成功创建微信支付', async () => {
      const createPaymentDto = {
        orderId: testOrder.id,
        paymentMethod: PaymentMethod.WECHAT,
        amount: testOrder.totalAmount,
      };

      const response = await request(app.getHttpServer())
        .post('/payments')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createPaymentDto)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.paymentMethod).toBe(PaymentMethod.WECHAT);
      expect(response.body.data).toHaveProperty('qrCode');
    });

    it('应该成功创建余额支付', async () => {
      const createPaymentDto = {
        orderId: testOrder.id,
        paymentMethod: PaymentMethod.BALANCE,
        amount: testOrder.totalAmount,
      };

      const response = await request(app.getHttpServer())
        .post('/payments')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createPaymentDto)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.paymentMethod).toBe(PaymentMethod.BALANCE);
      
      // 余额支付应该立即完成
      const paymentResponse = await request(app.getHttpServer())
        .get(`/payments/${response.body.data.paymentId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // 注意：余额支付在创建时就会立即处理，所以状态可能已经是成功
    });

    it('当订单不存在时应该返回404', async () => {
      const createPaymentDto = {
        orderId: 999,
        paymentMethod: PaymentMethod.ALIPAY,
        amount: 100.00,
      };

      await request(app.getHttpServer())
        .post('/payments')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createPaymentDto)
        .expect(404);
    });

    it('当支付金额与订单金额不符时应该返回400', async () => {
      const createPaymentDto = {
        orderId: testOrder.id,
        paymentMethod: PaymentMethod.ALIPAY,
        amount: 200.00, // 与订单金额不符
      };

      await request(app.getHttpServer())
        .post('/payments')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createPaymentDto)
        .expect(400);
    });

    it('当余额不足时应该返回400', async () => {
      // 创建一个高金额的订单
      const orderRepository = dataSource.getRepository(OrderEntity);
      const highAmountOrder = await orderRepository.save({
        userId: testUser.id,
        orderType: OrderType.SERVICE,
        status: OrderStatus.PENDING,
        totalAmount: 2000.00, // 超过用户余额
        remark: '高金额测试订单',
      });

      const createPaymentDto = {
        orderId: highAmountOrder.id,
        paymentMethod: PaymentMethod.BALANCE,
        amount: 2000.00,
      };

      await request(app.getHttpServer())
        .post('/payments')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createPaymentDto)
        .expect(400);
    });
  });

  describe('POST /payments/callback/:paymentMethod', () => {
    let testPayment: PaymentEntity;

    beforeEach(async () => {
      // 创建测试支付记录
      const paymentRepository = dataSource.getRepository(PaymentEntity);
      testPayment = await paymentRepository.save({
        orderId: testOrder.id,
        userId: testUser.id,
        paymentMethod: PaymentMethod.ALIPAY,
        status: PaymentStatus.PENDING,
        amount: testOrder.totalAmount,
      });
    });

    it('应该成功处理支付宝回调', async () => {
      const callbackData = {
        paymentNo: testPayment.paymentNo,
        thirdPartyNo: 'alipay_third_party_123',
        status: 'success',
        amount: testPayment.amount,
        sign: 'mock_signature',
      };

      const response = await request(app.getHttpServer())
        .post(`/payments/callback/${PaymentMethod.ALIPAY}`)
        .send(callbackData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('支付成功');
    });

    it('应该成功处理微信支付回调', async () => {
      // 更新支付记录为微信支付
      const paymentRepository = dataSource.getRepository(PaymentEntity);
      await paymentRepository.update(testPayment.id, {
        paymentMethod: PaymentMethod.WECHAT,
      });

      const callbackData = {
        paymentNo: testPayment.paymentNo,
        thirdPartyNo: 'wechat_third_party_123',
        status: 'success',
        amount: testPayment.amount,
        sign: 'mock_signature',
      };

      const response = await request(app.getHttpServer())
        .post(`/payments/callback/${PaymentMethod.WECHAT}`)
        .send(callbackData)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('当支付记录不存在时应该返回失败', async () => {
      const callbackData = {
        paymentNo: 'NONEXISTENT_PAYMENT_NO',
        thirdPartyNo: 'third_party_123',
        status: 'success',
        amount: 100.00,
      };

      const response = await request(app.getHttpServer())
        .post(`/payments/callback/${PaymentMethod.ALIPAY}`)
        .send(callbackData)
        .expect(200);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('支付记录不存在');
    });
  });

  describe('GET /payments', () => {
    it('应该返回用户的支付列表', async () => {
      // 创建测试支付记录
      const paymentRepository = dataSource.getRepository(PaymentEntity);
      await paymentRepository.save({
        orderId: testOrder.id,
        userId: testUser.id,
        paymentMethod: PaymentMethod.ALIPAY,
        status: PaymentStatus.SUCCESS,
        amount: testOrder.totalAmount,
      });

      const response = await request(app.getHttpServer())
        .get('/payments')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('items');
      expect(response.body.data).toHaveProperty('meta');
      expect(Array.isArray(response.body.data.items)).toBe(true);
    });

    it('应该支持按支付方式过滤', async () => {
      const response = await request(app.getHttpServer())
        .get(`/payments?paymentMethod=${PaymentMethod.ALIPAY}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('应该支持按状态过滤', async () => {
      const response = await request(app.getHttpServer())
        .get(`/payments?status=${PaymentStatus.SUCCESS}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('GET /payments/:id', () => {
    it('应该返回支付详情', async () => {
      // 创建测试支付记录
      const paymentRepository = dataSource.getRepository(PaymentEntity);
      const payment = await paymentRepository.save({
        orderId: testOrder.id,
        userId: testUser.id,
        paymentMethod: PaymentMethod.ALIPAY,
        status: PaymentStatus.SUCCESS,
        amount: testOrder.totalAmount,
      });

      const response = await request(app.getHttpServer())
        .get(`/payments/${payment.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(payment.id);
      expect(response.body.data.userId).toBe(testUser.id);
    });

    it('当支付记录不存在时应该返回404', async () => {
      await request(app.getHttpServer())
        .get('/payments/999')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    it('当用户无权访问时应该返回403', async () => {
      // 创建其他用户的支付记录
      const userRepository = dataSource.getRepository(UserEntity);
      const otherUser = await userRepository.save({
        ...createTestUser(),
        username: 'otheruser',
        email: '<EMAIL>',
      });

      const paymentRepository = dataSource.getRepository(PaymentEntity);
      const payment = await paymentRepository.save({
        orderId: testOrder.id,
        userId: otherUser.id,
        paymentMethod: PaymentMethod.ALIPAY,
        status: PaymentStatus.SUCCESS,
        amount: testOrder.totalAmount,
      });

      await request(app.getHttpServer())
        .get(`/payments/${payment.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);
    });
  });

  describe('GET /payments/payment-no/:paymentNo', () => {
    it('应该根据支付单号返回支付详情', async () => {
      // 创建测试支付记录
      const paymentRepository = dataSource.getRepository(PaymentEntity);
      const payment = await paymentRepository.save({
        orderId: testOrder.id,
        userId: testUser.id,
        paymentMethod: PaymentMethod.ALIPAY,
        status: PaymentStatus.SUCCESS,
        amount: testOrder.totalAmount,
      });

      const response = await request(app.getHttpServer())
        .get(`/payments/payment-no/${payment.paymentNo}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.paymentNo).toBe(payment.paymentNo);
    });

    it('当支付单号不存在时应该返回404', async () => {
      await request(app.getHttpServer())
        .get('/payments/payment-no/NONEXISTENT')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });
});
