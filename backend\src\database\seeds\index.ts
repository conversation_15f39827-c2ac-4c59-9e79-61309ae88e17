import { SeedManager } from './seed-manager';
import { UsersSeed } from './001-users.seed';
import { ServicesSeed } from './002-services.seed';
import { ApiKeysSeed } from './003-api-keys.seed';
import { UserServicesSeed } from './004-user-services.seed';

/**
 * 种子数据注册器
 * 统一管理所有种子数据的注册和配置
 */
export class SeedRegistry {
  private static instance: SeedManager;
  
  /**
   * 获取种子数据管理器实例
   */
  static getInstance(): SeedManager {
    if (!this.instance) {
      this.instance = new SeedManager();
      this.registerAllSeeds();
    }
    return this.instance;
  }
  
  /**
   * 注册所有种子数据
   */
  private static registerAllSeeds(): void {
    const seeds = [
      new UsersSeed(),
      new ServicesSeed(),
      new ApiKeysSeed(),
      new UserServicesSeed(),
    ];

    this.instance.registerAll(seeds);
  }
  
  /**
   * 获取所有种子数据
   */
  static getAllSeeds() {
    return this.getInstance().getAll();
  }
  
  /**
   * 按环境获取种子数据
   */
  static getSeedsByEnvironment(environment: string) {
    return this.getInstance()
      .getAll()
      .filter(seed => 
        seed.config.environments.includes(environment) ||
        seed.config.environments.includes('all')
      );
  }
  
  /**
   * 获取基础种子数据（所有环境都需要的）
   */
  static getBasicSeeds() {
    return this.getInstance()
      .getAll()
      .filter(seed =>
        seed.config.environments.includes('production') &&
        (seed.config.priority || 100) <= 2
      );
  }
  
  /**
   * 获取开发种子数据
   */
  static getDevelopmentSeeds() {
    return this.getSeedsByEnvironment('development');
  }
  
  /**
   * 获取测试种子数据
   */
  static getTestSeeds() {
    return this.getSeedsByEnvironment('test');
  }
  
  /**
   * 获取生产种子数据
   */
  static getProductionSeeds() {
    return this.getSeedsByEnvironment('production');
  }
}

// 导出种子数据管理器实例
export const seedManager = SeedRegistry.getInstance();

// 导出所有种子数据类
export {
  UsersSeed,
  ServicesSeed,
  ApiKeysSeed,
  UserServicesSeed,
};

// 导出接口和基础类
export * from './base/seed.interface';
export * from './base/base.seed';
export * from './seed-manager';
