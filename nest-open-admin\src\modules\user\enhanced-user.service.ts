import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
  HttpException,
  HttpStatus,
  Inject,
  forwardRef,
  Optional,
} from '@nestjs/common';

import { UserRolePermissionUpdateEvent } from '../../common/events/auth.events';
import { ApiException } from '../../common/exceptions/api.exception';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import {
  UserEntity,
  UserType,
  UserVerificationStatus,
  UserStatus,
} from './entities/user.entity';
import {
  CreateUserDto,
  UpdateUserDto,
  QueryUserDto,
  ChangeStatusDto,
  ResetPwdDto,
  UpdateProfileDto,
  UpdatePwdDto,
  RechargeDto,
  EnterpriseVerifyDto,
} from './dto';
import * as bcrypt from 'bcrypt';
import { CaptchaService } from '@/shared/captcha.service';
import { EnhancedCacheManagerService } from '../../shared/enhanced-cache-manager.service';
import { UserCacheService } from '../../shared/user-cache.service';
import { UserUpdatedEvent } from '../../common/events/cache.events';

/**
 * 增强版用户服务
 * 在原有功能基础上添加缓存一致性管理
 */
@Injectable()
export class EnhancedUserService {
  private readonly logger = new Logger(EnhancedUserService.name);

  constructor(
    @InjectRepository(UserEntity)
    private userRepository: Repository<UserEntity>,
    private dataSource: DataSource,
    private readonly cacheManager: EnhancedCacheManagerService,
    private readonly userCacheService: UserCacheService,

  ) {}

  /**
   * 更新用户个人资料（增强版）
   */
  async updateProfile(
    user: UserEntity,
    updateProfileDto: UpdateProfileDto,
  ): Promise<Record<string, any>> {
    const { nickName, realName, avatar, bio, settings } = updateProfileDto;

    // 保存更新前的数据用于事件
    const oldData = {
      nickname: user.nickname,
      realName: user.realName,
      avatar: user.avatar,
      bio: user.bio,
      settings: user.settings,
    };

    if (nickName !== undefined) user.nickname = nickName;
    if (realName !== undefined) user.realName = realName;
    if (avatar !== undefined) user.avatar = avatar;
    if (bio !== undefined) user.bio = bio;
    if (settings !== undefined) user.settings = JSON.stringify(settings);

    const updatedUser = await this.userRepository.save(user);

    // 缓存同步策略：写后更新
    await this.syncUserCache(updatedUser);

    // 清理相关缓存
    await this.cacheManager.clearUserRelatedCache(updatedUser.id);

    // 发送用户更新事件
    const userActionEvent: any = {
      id: `user_updated_${updatedUser.id}_${Date.now()}`,
      type: 'user_action' as const,
      source: 'enhanced-user-service',
      timestamp: Date.now(),
      data: {
        id: `user_updated_${updatedUser.id}_${Date.now()}`,
        userId: updatedUser.id,
        action: 'profile_updated',
        resource: 'user_profile',
        timestamp: Date.now(),
        success: true,
        details: {
          newData: {
            nickname: updatedUser.nickname,
            realName: updatedUser.realName,
            avatar: updatedUser.avatar,
            bio: updatedUser.bio,
            settings: updatedUser.settings,
          },
          oldData,
        },
      },
      payload: {
        id: `user_updated_${updatedUser.id}_${Date.now()}`,
        userId: updatedUser.id,
        action: 'profile_updated',
        resource: 'user_profile',
        timestamp: Date.now(),
        success: true,
        details: {
          newData: {
            nickname: updatedUser.nickname,
            realName: updatedUser.realName,
            avatar: updatedUser.avatar,
            bio: updatedUser.bio,
            settings: updatedUser.settings,
          },
          oldData,
        },
      },
      metadata: {
        serviceName: 'enhanced-user-service',
        operation: 'profile_update',
        timestamp: Date.now(),
      },
    };

    // 用户操作事件已记录到日志
    this.logger.log(`用户操作事件: ${JSON.stringify(userActionEvent)}`);

    this.logger.log(`用户 ${updatedUser.id} 个人资料已更新`);

    return {
      id: updatedUser.id,
      nickname: updatedUser.nickname,
      realName: updatedUser.realName,
      avatar: updatedUser.avatar,
      bio: updatedUser.bio,
      settings: updatedUser.settings,
      message: '个人资料更新成功',
    };
  }

  /**
   * 更新用户信息（增强版）
   */
  async updateUser(
    id: number,
    updateUserDto: UpdateUserDto,
  ): Promise<Record<string, any>> {
    const user = await this.findById(id);

    // 保存更新前的数据
    const oldData = {
      email: user.email,
      userType: user.userType,
      balance: user.balance,
    };

    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingUser = await this.userRepository.findOne({
        where: { email: updateUserDto.email },
      });
      if (existingUser) {
        throw new ApiException(30006);
      }
    }

    Object.assign(user, updateUserDto);
    const updatedUser = await this.userRepository.save(user);

    // 缓存同步策略：写后更新
    await this.syncUserCache(updatedUser);

    // 清理相关缓存
    await this.cacheManager.clearUserRelatedCache(updatedUser.id);

    // 如果用户类型发生变化，需要清除角色权限缓存
    if (oldData.userType !== updatedUser.userType) {
      await this.updateUserRolePermissions(updatedUser.id, 'clear');
    }

    // 发送用户更新事件
    const userUpdateEvent: any = {
      id: `user_info_updated_${updatedUser.id}_${Date.now()}`,
      type: 'user_action' as const,
      source: 'enhanced-user-service',
      timestamp: Date.now(),
      data: {
        id: `user_info_updated_${updatedUser.id}_${Date.now()}`,
        userId: updatedUser.id,
        action: 'info_updated',
        resource: 'user_info',
        timestamp: Date.now(),
        success: true,
        details: {
          newData: {
            email: updatedUser.email,
            userType: updatedUser.userType,
            balance: updatedUser.balance,
          },
          oldData,
        },
      },
      payload: {
        id: `user_info_updated_${updatedUser.id}_${Date.now()}`,
        userId: updatedUser.id,
        action: 'info_updated',
        resource: 'user_info',
        timestamp: Date.now(),
        success: true,
        details: {
          newData: {
            email: updatedUser.email,
            userType: updatedUser.userType,
            balance: updatedUser.balance,
          },
          oldData,
        },
      },
      metadata: {
        serviceName: 'enhanced-user-service',
        operation: 'user_update',
        timestamp: Date.now(),
      },
    };

    // 用户操作事件已记录到日志
    this.logger.log(`用户操作事件: ${JSON.stringify(userUpdateEvent)}`);

    this.logger.log(`用户 #${id} 信息已更新`);

    return {
      id: updatedUser.id,
      email: updatedUser.email,
      userType: updatedUser.userType,
      balance: updatedUser.balance,
      updatedAt: updatedUser.updatedAt,
    };
  }

  /**
   * 修改用户状态（增强版）
   */
  async changeStatus(
    id: number,
    changeStatusDto: ChangeStatusDto,
  ): Promise<Record<string, any>> {
    const { userStatus, reason } = changeStatusDto;
    const user = await this.findById(id);

    const oldStatus = user.userStatus;
    user.userStatus = userStatus;

    if (userStatus === UserStatus.LOCKED) {
      user.lockedUntil = new Date(Date.now() + 24 * 60 * 60 * 1000);
    } else if (userStatus === UserStatus.ACTIVE) {
      user.lockedUntil = undefined;
    }

    const updatedUser = await this.userRepository.save(user);

    // 缓存同步策略：写后删除（状态变更影响范围大）
    await this.cacheManager.clearUserRelatedCache(updatedUser.id);

    // 如果用户被锁定，清理其API Key缓存
    if (userStatus === UserStatus.LOCKED) {
      await this.cacheManager.clearApiKeyRelatedCache(
        0,
        undefined,
        updatedUser.id,
      );
    }

    // 状态变更时需要清除角色权限缓存
    await this.updateUserRolePermissions(updatedUser.id, 'clear');

    // 发送用户状态变更事件
    const statusChangeEvent: any = {
      id: `user_status_changed_${updatedUser.id}_${Date.now()}`,
      type: 'user_action' as const,
      source: 'enhanced-user-service',
      timestamp: Date.now(),
      data: {
        id: `user_status_changed_${updatedUser.id}_${Date.now()}`,
        userId: updatedUser.id,
        action: 'status_changed',
        resource: 'user_status',
        timestamp: Date.now(),
        success: true,
        details: {
          oldStatus,
          newStatus: userStatus,
          reason,
        },
      },
      payload: {
        id: `user_status_changed_${updatedUser.id}_${Date.now()}`,
        userId: updatedUser.id,
        action: 'status_changed',
        resource: 'user_status',
        timestamp: Date.now(),
        success: true,
        details: {
          oldStatus,
          newStatus: userStatus,
          reason,
        },
      },
      metadata: {
        serviceName: 'enhanced-user-service',
        operation: 'status_change',
        timestamp: Date.now(),
      },
    };

    // 用户操作事件已记录到日志
    this.logger.log(`用户操作事件: ${JSON.stringify(statusChangeEvent)}`);

    this.logger.log(
      `用户 #${id} 状态从 ${oldStatus} 变更为 ${userStatus}${reason ? `，原因: ${reason}` : ''}`,
    );

    return {
      id: updatedUser.id,
      userStatus: updatedUser.userStatus,
      lockedUntil: updatedUser.lockedUntil,
      updatedAt: updatedUser.updatedAt,
    };
  }

  /**
   * 更新用户密码（增强版）
   */
  async updatePassword(
    user: UserEntity,
    updatePwdDto: UpdatePwdDto,
  ): Promise<Record<string, any>> {
    const { oldPassword, newPassword } = updatePwdDto;

    // 验证旧密码
    const isOldPasswordValid = await bcrypt.compare(oldPassword, user.password);
    if (!isOldPasswordValid) {
      throw new ApiException(30007); // 旧密码错误
    }

    // 加密新密码
    const saltRounds = 10;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    user.password = hashedNewPassword;
    user.updatedAt = new Date();

    const updatedUser = await this.userRepository.save(user);

    // 密码更新后清理所有相关缓存（包括认证缓存）
    await this.cacheManager.clearUserRelatedCache(updatedUser.id);
    await this.cacheManager.clearApiKeyRelatedCache(
      0,
      undefined,
      updatedUser.id,
    );

    // 发送密码更新事件
    const passwordUpdateEvent: any = {
      id: `user_password_updated_${updatedUser.id}_${Date.now()}`,
      type: 'user_action' as const,
      source: 'enhanced-user-service',
      timestamp: Date.now(),
      data: {
        id: `user_password_updated_${updatedUser.id}_${Date.now()}`,
        userId: updatedUser.id,
        action: 'password_updated',
        resource: 'user_password',
        timestamp: Date.now(),
        success: true,
        details: {
          type: 'password',
        },
      },
      payload: {
        id: `user_password_updated_${updatedUser.id}_${Date.now()}`,
        userId: updatedUser.id,
        action: 'password_updated',
        resource: 'user_password',
        timestamp: Date.now(),
        success: true,
        details: {
          type: 'password',
        },
      },
      metadata: {
        serviceName: 'enhanced-user-service',
        operation: 'password_update',
        timestamp: Date.now(),
      },
    };

    // 用户操作事件已记录到日志
    this.logger.log(`用户操作事件: ${JSON.stringify(passwordUpdateEvent)}`);

    this.logger.log(`用户 ${updatedUser.id} 密码已更新`);

    return {
      id: updatedUser.id,
      message: '密码更新成功',
    };
  }

  /**
   * 用户充值（增强版）
   */
  async rechargeById(
    id: number,
    rechargeDto: RechargeDto,
  ): Promise<Record<string, any>> {
    const { amount, remark } = rechargeDto;

    return this.dataSource.transaction(async (queryRunner) => {
      const user = await queryRunner.findOne(UserEntity, {
        where: { id },
        lock: { mode: 'pessimistic_write' },
      });

      if (!user) {
        throw new NotFoundException(`用户 #${id} 不存在`);
      }

      const oldBalance = user.balance;
      user.balance = Number((user.balance + amount).toFixed(2));
      user.updatedAt = new Date();

      const updatedUser = await queryRunner.save(user);

      // 事务提交后异步更新缓存
      setImmediate(async () => {
        try {
          // 清理余额相关缓存
          await this.cacheManager.clearBillingRelatedCache(updatedUser.id);
          await this.cacheManager.clearUserRelatedCache(updatedUser.id);

          // 发送余额更新事件
          const balanceUpdateEvent: any = {
            id: `user_balance_updated_${updatedUser.id}_${Date.now()}`,
            type: 'user_action' as const,
            source: 'enhanced-user-service',
            timestamp: Date.now(),
            data: {
              id: `user_balance_updated_${updatedUser.id}_${Date.now()}`,
              userId: updatedUser.id,
              action: 'balance_updated',
              resource: 'user_balance',
              timestamp: Date.now(),
              success: true,
              details: {
                newData: { balance: updatedUser.balance },
                oldData: { balance: oldBalance },
                amount,
                remark,
              },
            },
            payload: {
              id: `user_balance_updated_${updatedUser.id}_${Date.now()}`,
              userId: updatedUser.id,
              action: 'balance_updated',
              resource: 'user_balance',
              timestamp: Date.now(),
              success: true,
              details: {
                newData: { balance: updatedUser.balance },
                oldData: { balance: oldBalance },
                amount,
                remark,
              },
            },
            metadata: {
              serviceName: 'enhanced-user-service',
              operation: 'recharge',
              timestamp: Date.now(),
            },
          };

          // 用户操作事件已记录到日志
          this.logger.log(`用户操作事件: ${JSON.stringify(balanceUpdateEvent)}`);
        } catch (error) {
          this.logger.error(
            `充值后缓存更新失败: userId=${updatedUser.id}, error=${error.message}`,
          );
        }
      });

      this.logger.log(
        `用户 ${id} 充值成功: ${oldBalance} -> ${updatedUser.balance} (+${amount})`,
      );

      return {
        id: updatedUser.id,
        oldBalance,
        newBalance: updatedUser.balance,
        amount,
        remark,
        message: '充值成功',
      };
    });
  }

  /**
   * 扣减用户余额（增强版）
   */
  async deductBalance(
    userId: number,
    amount: number,
    remark?: string,
  ): Promise<Record<string, any>> {
    return this.dataSource.transaction(async (queryRunner) => {
      const user = await queryRunner.findOne(UserEntity, {
        where: { id: userId },
        lock: { mode: 'pessimistic_write' },
      });

      if (!user) {
        throw new NotFoundException(`用户 #${userId} 不存在`);
      }

      if (user.balance < amount) {
        throw new BadRequestException('余额不足');
      }

      const oldBalance = user.balance;
      user.balance = Number((user.balance - amount).toFixed(2));
      user.updatedAt = new Date();

      const updatedUser = await queryRunner.save(user);

      // 事务提交后异步更新缓存
      setImmediate(async () => {
        try {
          // 清理余额相关缓存
          await this.cacheManager.clearBillingRelatedCache(updatedUser.id);
          await this.cacheManager.clearUserRelatedCache(updatedUser.id);

          // 发送余额更新事件
          const balanceDeductEvent: any = {
            id: `user_balance_deducted_${updatedUser.id}_${Date.now()}`,
            type: 'user_action' as const,
            source: 'enhanced-user-service',
            timestamp: Date.now(),
            data: {
              id: `user_balance_deducted_${updatedUser.id}_${Date.now()}`,
              userId: updatedUser.id,
              action: 'balance_deducted',
              resource: 'user_balance',
              timestamp: Date.now(),
              success: true,
              details: {
                newData: { balance: updatedUser.balance },
                oldData: { balance: oldBalance },
                amount,
                remark,
              },
            },
            payload: {
              id: `user_balance_deducted_${updatedUser.id}_${Date.now()}`,
              userId: updatedUser.id,
              action: 'balance_deducted',
              resource: 'user_balance',
              timestamp: Date.now(),
              success: true,
              details: {
                newData: { balance: updatedUser.balance },
                oldData: { balance: oldBalance },
                amount,
                remark,
              },
            },
            metadata: {
              serviceName: 'enhanced-user-service',
              operation: 'deduct_balance',
              timestamp: Date.now(),
            },
          };

          // 用户操作事件已记录到日志
          this.logger.log(`用户操作事件: ${JSON.stringify(balanceDeductEvent)}`);
        } catch (error) {
          this.logger.error(
            `扣费后缓存更新失败: userId=${updatedUser.id}, error=${error.message}`,
          );
        }
      });

      this.logger.log(
        `用户 ${userId} 扣费成功: ${oldBalance} -> ${updatedUser.balance} (-${amount})`,
      );

      return {
        id: updatedUser.id,
        oldBalance,
        newBalance: updatedUser.balance,
        amount,
        remark,
        message: '扣费成功',
      };
    });
  }

  /**
   * 同步用户缓存
   */
  private async syncUserCache(user: UserEntity): Promise<void> {
    try {
      const cacheData = {
        id: user.id,
        email: user.email,
        phone: user.phone,
        username: user.username,
        nickname: user.nickname,
        realName: user.realName,
        avatar: user.avatar,
        userType: user.userType,
        userStatus: user.userStatus,
        isActive: user.userStatus === UserStatus.ACTIVE,
        balance: user.balance,
        lastLoginAt: user.lastLoginAt,
        updatedAt: user.updatedAt,
      };

      await this.userCacheService.refreshUserCache(user.id, cacheData);
      this.logger.debug(`用户 ${user.id} 缓存同步完成`);
    } catch (error) {
      this.logger.error(
        `用户 ${user.id} 缓存同步失败: ${error.message}`,
        error.stack,
      );
      // 缓存同步失败不影响业务流程
    }
  }

  /**
   * 根据ID查找用户
   */
  async findById(id: number): Promise<UserEntity> {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException(`用户 #${id} 不存在`);
    }
    return user;
  }

  /**
   * 批量更新用户缓存
   */
  async batchUpdateUserCache(userIds: number[]): Promise<void> {
    try {
      const users = await this.userRepository.findByIds(userIds);

      const promises = users.map((user) => this.syncUserCache(user));
      await Promise.allSettled(promises);

      this.logger.log(`批量更新 ${users.length} 个用户缓存完成`);
    } catch (error) {
      this.logger.error(`批量更新用户缓存失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 更新用户角色权限后的缓存同步
   */
  async updateUserRolePermissions(
    userId: number,
    action: 'refresh' | 'clear' = 'clear',
  ): Promise<void> {
    try {
      // 发送用户角色权限更新事件
      const rolePermissionEvent: any = {
        id: `user_role_permission_${userId}_${Date.now()}`,
        type: 'user_action' as const,
        source: 'enhanced-user-service',
        timestamp: Date.now(),
        data: {
          id: `user_role_permission_${userId}_${Date.now()}`,
          userId: userId,
          action: 'role_permission_update',
          resource: 'user_role_permission',
          timestamp: Date.now(),
          success: true,
          details: {
            operation: action,
          },
        },
        payload: {
          id: `user_role_permission_${userId}_${Date.now()}`,
          userId: userId,
          action: 'role_permission_update',
          resource: 'user_role_permission',
          timestamp: Date.now(),
          success: true,
          details: {
            operation: action,
          },
        },
        metadata: {
          serviceName: 'enhanced-user-service',
          operation: 'role_permission_update',
          timestamp: Date.now(),
        },
      };

      // 用户操作事件已记录到日志
      this.logger.log(`用户操作事件: ${JSON.stringify(rolePermissionEvent)}`);

      this.logger.log(
        `用户 ${userId} 角色权限缓存同步事件已发送，操作: ${action}`,
      );
    } catch (error) {
      this.logger.error(
        `用户 ${userId} 角色权限缓存同步失败: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * 批量更新用户角色权限缓存
   */
  async batchUpdateUserRolePermissions(
    userIds: number[],
    action: 'refresh' | 'clear' = 'clear',
  ): Promise<void> {
    try {
      const promises = userIds.map((userId) =>
        this.updateUserRolePermissions(userId, action),
      );
      await Promise.allSettled(promises);

      this.logger.log(`批量更新 ${userIds.length} 个用户角色权限缓存完成`);
    } catch (error) {
      this.logger.error(
        `批量更新用户角色权限缓存失败: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * 清理用户相关的所有缓存
   */
  async clearUserCache(userId: number): Promise<void> {
    await this.cacheManager.clearUserRelatedCache(userId);
    await this.cacheManager.clearApiKeyRelatedCache(0, undefined, userId);
    await this.cacheManager.clearBillingRelatedCache(userId);

    this.logger.log(`用户 ${userId} 所有缓存已清理`);
  }
}
