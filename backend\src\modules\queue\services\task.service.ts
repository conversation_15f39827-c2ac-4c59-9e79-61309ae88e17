import { Injectable, NotFoundException } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { Subscription } from 'rxjs';
import { RedisService } from '../../../shared/redis.service';
import { RedisPubSubService } from './pub-sub.service';
import { TaskStatus } from '../interfaces/task-status.enum';
import { TaskDetails, TaskStatusUpdate } from '../interfaces/task.interface';
import { StructuredLogger } from '../../../common/logging/structured-logger';

/**
 * 任务服务
 * 负责任务的创建、状态更新、查询等操作
 */
@Injectable()
export class TaskService {
  constructor(
    private readonly redisService: RedisService,
    private readonly pubSub: RedisPubSubService,
    private readonly logger: StructuredLogger,
  ) {}
  
  /**
   * 创建新任务并返回任务ID
   */
  async createTask(data: any, type: string): Promise<string> {
    const taskId = uuidv4();
    
    // 存储任务信息
    await this.redisService.hmset(`task:${taskId}`, {
      status: TaskStatus.QUEUED,
      type,
      createdAt: Date.now(),
      data: JSON.stringify(data),
    });
    
    // 设置过期时间（24小时）
    await this.redisService.expire(`task:${taskId}`, 86400);
    
    this.logger.log(
      `创建了类型为${type}的任务${taskId}`,
      { module: 'TaskService' }
    );
    
    return taskId;
  }
  
  /**
   * 更新任务状态和相关数据
   */
  async updateTaskStatus(
    taskId: string,
    status: TaskStatus,
    data?: Record<string, any>,
  ): Promise<void> {
    const update: Record<string, any> = {
      status,
      updatedAt: Date.now(),
    };
    
    if (data) {
      if (data.result) {
        update.result = JSON.stringify(data.result);
      }
      
      if (data.error) {
        update.error = data.error;
      }
      
      if (typeof data.progress === 'number') {
        update.progress = data.progress.toString();
      }
      
      if (data.message) {
        update.message = data.message;
      }
    }
    
    // 更新Redis中的任务状态
    await this.redisService.hmset(`task:${taskId}`, update);
    
    // 发布状态更新事件
    await this.pubSub.publish(`task:${taskId}:updates`, {
      status,
      ...data,
    });
    
    this.logger.debug(
      `更新任务${taskId}状态为${status}`,
      { module: 'TaskService', metadata: { ...data } }
    );
  }
  
  /**
   * 获取任务状态
   */
  async getTaskStatus(taskId: string): Promise<TaskDetails> {
    const task = await this.redisService.hgetall(`task:${taskId}`);
    
    if (!task || Object.keys(task).length === 0) {
      throw new NotFoundException(`任务${taskId}不存在`);
    }
    
    const result: Record<string, any> = {
      taskId,
      status: task.status as TaskStatus,
      type: task.type,
      createdAt: parseInt(task.createdAt),
    };
    
    if (task.progress) {
      result.progress = parseFloat(task.progress);
    }
    
    if (task.message) {
      result.message = task.message;
    }
    
    if (task.error) {
      result.error = task.error;
    }
    
    if (task.result) {
      try {
        result.result = JSON.parse(task.result);
      } catch (e) {
        result.result = task.result;
      }
    }
    
    if (task.updatedAt) {
      result.updatedAt = parseInt(task.updatedAt);
    }
    
    return result as TaskDetails;
  }
  
  /**
   * 检查任务是否存在
   */
  async checkTaskExists(taskId: string): Promise<boolean> {
    return await this.redisService.exists(`task:${taskId}`);
  }
  
  /**
   * 订阅任务状态更新
   */
  subscribeToTaskUpdates(
    taskId: string,
    callback: (data: TaskStatusUpdate) => void,
  ): Subscription {
    return this.pubSub.subscribe(`task:${taskId}:updates`, callback);
  }

  /**
   * 删除任务
   */
  async deleteTask(taskId: string): Promise<boolean> {
    const exists = await this.checkTaskExists(taskId);
    if (!exists) {
      return false;
    }
    
    await this.redisService.del(`task:${taskId}`);
    this.logger.debug(`删除任务${taskId}`, { module: 'TaskService' });
    return true;
  }

  /**
   * 获取任务原始数据
   */
  async getTaskData(taskId: string): Promise<any> {
    const dataStr = await this.redisService.hget(`task:${taskId}`, 'data');
    if (!dataStr) {
      throw new NotFoundException(`任务${taskId}不存在或数据为空`);
    }

    try {
      return JSON.parse(dataStr);
    } catch (e) {
      return dataStr;
    }
  }
} 