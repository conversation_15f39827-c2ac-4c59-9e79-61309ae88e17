import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { Repository, SelectQueryBuilder, FindOptionsWhere, DeepPartial } from 'typeorm';
import { BaseEntity } from '../entities/base.entity';

/**
 * 基础CRUD服务抽象类
 * 提供通用的CRUD操作，减少代码重复
 */
export abstract class BaseCrudService<
  T extends BaseEntity,
  CreateDto,
  UpdateDto,
  QueryDto,
  ResponseDto,
  ListResponseDto
> {
  protected readonly logger: Logger;
  protected abstract readonly repository: Repository<T>;
  protected abstract readonly entityName: string;

  constructor() {
    this.logger = new Logger(this.constructor.name);
  }

  /**
   * 创建实体
   */
  async create(createDto: CreateDto): Promise<ResponseDto> {
    await this.validateBeforeCreate(createDto);
    
    const entity = this.repository.create(this.transformCreateDto(createDto));
    const savedEntity = await this.repository.save(entity);
    
    this.logger.log(`${this.entityName}创建成功: ID=${savedEntity.id}`);
    
    return this.transformToResponseDto(savedEntity);
  }

  /**
   * 查询列表
   */
  async findAll(queryDto: QueryDto): Promise<ListResponseDto> {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'DESC' } = queryDto as any;
    
    const queryBuilder = this.createQueryBuilder('entity');
    
    // 应用查询条件
    this.applyQueryConditions(queryBuilder, queryDto);
    
    // 应用排序
    queryBuilder.orderBy(`entity.${sortBy}`, sortOrder);
    
    // 应用分页
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);
    
    const [entities, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);
    
    const data = entities.map(entity => this.transformToResponseDto(entity));
    
    return this.transformToListResponseDto(data, total, page, limit, totalPages);
  }

  /**
   * 根据ID查询单个实体
   */
  async findOne(id: number): Promise<ResponseDto> {
    const entity = await this.repository.findOne({
      where: { id } as FindOptionsWhere<T>,
      relations: this.getRelations(),
    });

    if (!entity) {
      throw new NotFoundException(`${this.entityName}不存在`);
    }

    return this.transformToResponseDto(entity);
  }

  /**
   * 更新实体
   */
  async update(id: number, updateDto: UpdateDto): Promise<ResponseDto> {
    const entity = await this.repository.findOne({
      where: { id } as FindOptionsWhere<T>,
      relations: this.getRelations(),
    });

    if (!entity) {
      throw new NotFoundException(`${this.entityName}不存在`);
    }

    await this.validateBeforeUpdate(id, updateDto, entity);
    
    Object.assign(entity, this.transformUpdateDto(updateDto, entity));
    const savedEntity = await this.repository.save(entity);
    
    this.logger.log(`${this.entityName}更新成功: ID=${id}`);
    
    return this.transformToResponseDto(savedEntity);
  }

  /**
   * 删除实体
   */
  async remove(id: number): Promise<{ message: string }> {
    const entity = await this.repository.findOne({
      where: { id } as FindOptionsWhere<T>,
      relations: this.getRelationsForDelete(),
    });

    if (!entity) {
      throw new NotFoundException(`${this.entityName}不存在`);
    }

    await this.validateBeforeDelete(id, entity);
    
    await this.repository.softDelete(id);
    
    this.logger.log(`${this.entityName}删除成功: ID=${id}`);
    
    return { message: `${this.entityName}已成功删除` };
  }

  /**
   * 创建查询构建器
   */
  protected createQueryBuilder(alias: string): SelectQueryBuilder<T> {
    return this.repository.createQueryBuilder(alias);
  }

  /**
   * 获取关联关系
   */
  protected getRelations(): string[] {
    return [];
  }

  /**
   * 获取删除时需要检查的关联关系
   */
  protected getRelationsForDelete(): string[] {
    return this.getRelations();
  }

  // 抽象方法，子类必须实现
  protected abstract validateBeforeCreate(createDto: CreateDto): Promise<void>;
  protected abstract validateBeforeUpdate(id: number, updateDto: UpdateDto, entity: T): Promise<void>;
  protected abstract validateBeforeDelete(id: number, entity: T): Promise<void>;
  protected abstract transformCreateDto(createDto: CreateDto): DeepPartial<T>;
  protected abstract transformUpdateDto(updateDto: UpdateDto, entity: T): DeepPartial<T>;
  protected abstract transformToResponseDto(entity: T): ResponseDto;
  protected abstract transformToListResponseDto(
    data: ResponseDto[],
    total: number,
    page: number,
    limit: number,
    totalPages: number
  ): ListResponseDto;
  protected abstract applyQueryConditions(queryBuilder: SelectQueryBuilder<T>, queryDto: QueryDto): void;
}