import { DataSource } from 'typeorm';
import { BaseSeed } from './base/base.seed';
import { SeedConfig, SeedResult } from './base/seed.interface';
import { UserEntity, RoleEnum, UserStatus, TierEnum } from '../../modules/user/entities/user.entity';
import * as bcrypt from 'bcrypt';

/**
 * 用户种子数据
 * 创建管理员和测试用户
 */
export class UsersSeed extends BaseSeed {
  config: SeedConfig = {
    name: 'users',
    description: '创建管理员和测试用户',
    environments: ['development', 'test', 'production'],
    dependencies: [],
    version: '1.0.0',
    priority: 1,
  };

  protected async execute(dataSource: DataSource): Promise<SeedResult> {
    const userRepository = this.getRepository(dataSource, UserEntity);
    
    // 默认用户数据
    const defaultUsers = [
      {
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123456',
        nickname: '系统管理员',
        role: RoleEnum.ADMIN,
        status: UserStatus.ACTIVE,
        tier: TierEnum.ENTERPRISE,
        balance: 10000.00,
        emailVerified: true,
        phoneVerified: false,
        isActive: true,
        isFreeQuotaEligible: false,
        remark: '系统管理员账户',
      },
      {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'test123456',
        nickname: '测试用户',
        role: RoleEnum.USER,
        status: UserStatus.ACTIVE,
        tier: TierEnum.BASIC,
        balance: 1000.00,
        emailVerified: true,
        phoneVerified: false,
        isActive: true,
        isFreeQuotaEligible: true,
        remark: '测试用户账户',
      },
      {
        username: 'demouser',
        email: '<EMAIL>',
        password: 'demo123456',
        nickname: '演示用户',
        role: RoleEnum.USER,
        status: UserStatus.ACTIVE,
        tier: TierEnum.PREMIUM,
        balance: 500.00,
        emailVerified: true,
        phoneVerified: true,
        phone: '13800138000',
        isActive: true,
        isFreeQuotaEligible: true,
        remark: '演示用户账户',
      },
    ];
    
    let affectedRows = 0;
    
    for (const userData of defaultUsers) {
      try {
        // 检查用户是否已存在
        const existingUser = await userRepository.findOne({
          where: [
            { username: userData.username },
            { email: userData.email }
          ]
        });
        
        if (existingUser && !this.config.force) {
          this.logger.log(`用户 ${userData.username} 已存在，跳过创建`);
          continue;
        }
        
        // 加密密码
        const hashedPassword = await bcrypt.hash(userData.password, 10);
        
        const userEntity = {
          ...userData,
          password: hashedPassword,
          emailVerifiedAt: userData.emailVerified ? new Date() : null,
          phoneVerifiedAt: userData.phoneVerified ? new Date() : null,
          lastDailyResetDate: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        
        if (existingUser) {
          // 更新现有用户（除了密码）
          const { password, ...updateData } = userEntity;
          await userRepository.update({ id: existingUser.id }, updateData);
          this.logger.log(`更新用户: ${userData.username}`);
        } else {
          // 创建新用户
          const newUser = userRepository.create(userEntity);
          await userRepository.save(newUser);
          this.logger.log(`创建用户: ${userData.username}`);
        }
        
        affectedRows++;
        
      } catch (error) {
        this.logger.error(`创建用户 ${userData.username} 失败: ${error.message}`);
      }
    }
    
    return {
      success: true,
      message: `成功创建/更新 ${affectedRows} 个用户`,
      affectedRows,
    };
  }
  
  /**
   * 检查是否需要执行
   */
  async shouldRun(dataSource: DataSource): Promise<boolean> {
    const userRepository = this.getRepository(dataSource, UserEntity);
    
    // 检查是否存在管理员用户
    const adminCount = await userRepository.count({
      where: { role: RoleEnum.ADMIN }
    });
    
    return adminCount === 0;
  }
}
