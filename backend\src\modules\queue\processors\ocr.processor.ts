import { Processor, Process, OnQueueActive, OnQueueCompleted, OnQueueFailed } from '@nestjs/bull';
import { Job } from 'bull';
import { StructuredLogger } from '../../../common/logging/structured-logger';
import { TaskService } from '../services/task.service';
import { TaskStatus } from '../interfaces/task-status.enum';
import { OcrJobData, OcrResult } from '../interfaces/queue-job.interface';

/**
 * OCR任务处理器
 * 处理OCR识别任务的执行和状态更新
 */
@Processor('ocr')
export class OcrProcessor {
  constructor(
    private readonly taskService: TaskService,
    private readonly logger: StructuredLogger,
  ) {}

  /**
   * 处理OCR任务
   */
  @Process()
  async processOcrTask(job: Job<OcrJobData>): Promise<OcrResult> {
    const { taskId, imageData } = job.data;
    
    try {
      // 更新任务状态为处理中
      await this.taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING, {
        progress: 0,
        message: '开始识别图片',
      });
      
      // 模拟处理过程，实际项目中应调用真实的OCR服务
      await this.simulateOcrProcess(taskId);
      
      // 模拟OCR结果
      const result: OcrResult = {
        sender: {
          name: '张三',
          phone: '13800138000',
          address: '北京市海淀区中关村大街1号',
        },
        receiver: {
          name: '李四',
          phone: '13900139000',
          address: '上海市浦东新区张江高科技园区',
        },
        expressInfo: {
          company: '顺丰速运',
          trackingNumber: 'SF1234567890',
        },
        confidence: 0.95,
      };
      
      return result;
    } catch (error) {
      this.logger.error(
        `处理OCR任务${taskId}时出错`,
        error,
        { module: 'OcrProcessor' }
      );
      throw error;
    }
  }
  
  /**
   * 模拟OCR处理过程
   * 实际项目中应替换为真实的OCR识别逻辑
   * simulateOcrProcess方法后续可直接替换为实际的OCR服务调用。
只需保证返回结构与OcrResult类型一致即可
   */
  private async simulateOcrProcess(taskId: string): Promise<void> {
    const steps = [0.2, 0.4, 0.6, 0.8, 1.0];
    const messages = [
      '预处理图像...',
      '检测文字区域...',
      '识别文字内容...',
      '提取物流信息...',
      '识别完成',
    ];
    
    for (let i = 0; i < steps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 500)); // 模拟处理耗时
      
      // 更新任务进度
      await this.taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING, {
        progress: steps[i],
        message: messages[i],
      });
    }
  }
  
  /**
   * 任务开始处理时触发
   */
  @OnQueueActive()
  async onActive(job: Job): Promise<void> {
    const { taskId } = job.data;
    await this.taskService.updateTaskStatus(taskId, TaskStatus.ACTIVE, {
      message: '任务开始处理',
    });
  }
  
  /**
   * 任务完成时触发
   */
  @OnQueueCompleted()
  async onCompleted(job: Job, result: OcrResult): Promise<void> {
    const { taskId } = job.data;
    await this.taskService.updateTaskStatus(taskId, TaskStatus.COMPLETED, {
      result,
      message: '识别完成',
      progress: 1,
    });
    
    this.logger.log(
      `OCR任务${taskId}完成`,
      { 
        module: 'OcrProcessor',
        metadata: { confidence: result.confidence }
      }
    );
  }
  
  /**
   * 任务失败时触发
   */
  @OnQueueFailed()
  async onFailed(job: Job, error: Error): Promise<void> {
    const { taskId } = job.data;
    await this.taskService.updateTaskStatus(taskId, TaskStatus.FAILED, {
      error: error.message,
      message: '识别失败',
    });
    
    this.logger.error(
      `OCR任务${taskId}失败`,
      error,
      { module: 'OcrProcessor' }
    );
  }
} 