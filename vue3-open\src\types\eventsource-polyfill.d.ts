declare module 'eventsource-polyfill' {
  export type EventSourcePolyfillType = typeof EventSourcePolyfill;
  export default EventSourcePolyfill;
  interface EventSourcePolyfillInit extends EventSourceInit {
    headers?: Record<string, string>;
    heartbeatTimeout?: number;
    connectionTimeout?: number;
    reconnectInterval?: number;
  }

  export class EventSourcePolyfill implements EventSource {
    constructor(url: string, options?: EventSourcePolyfillInit);
    readonly CLOSED: number;
    readonly CONNECTING: number;
    readonly OPEN: number;
    readonly readyState: number;
    readonly url: string;
    readonly withCredentials: boolean;
    onopen: ((this: EventSource, ev: Event) => any) | null;
    onmessage: ((this: EventSource, ev: MessageEvent) => any) | null;
    onerror: ((this: EventSource, ev: Event) => any) | null;
    addEventListener<K extends keyof EventSourceEventMap>(type: K, listener: (this: EventSource, ev: EventSourceEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
    addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
    removeEventListener<K extends keyof EventSourceEventMap>(type: K, listener: (this: EventSource, ev: EventSourceEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
    removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    dispatchEvent(event: Event): boolean;
    close(): void;
  }
} 