const Redis = require('ioredis');

/**
 * 清除Nonce缓存脚本
 */
async function clearNonceCache() {
  console.log('开始清除Nonce缓存...');
  
  const redis = new Redis({
    host: 'localhost',
    port: 6379,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
  });

  try {
    // 清除Nonce缓存
    const nonceKeys = await redis.keys('auth:nonce:*');
    
    if (nonceKeys.length > 0) {
      console.log(`删除 ${nonceKeys.length} 个Nonce缓存项`);
      await redis.del(...nonceKeys);
    } else {
      console.log('没有找到Nonce缓存项');
    }
    
    console.log('✅ Nonce缓存清除完成');
    
  } catch (error) {
    console.error('❌ 清除Nonce缓存失败:', error.message);
  } finally {
    redis.disconnect();
    console.log('Redis连接已关闭');
  }
}

// 运行脚本
clearNonceCache().catch(console.error);
