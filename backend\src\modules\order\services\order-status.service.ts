import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan, In } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { OrderEntity } from '../entities/order.entity';
import { OrderStatus } from '../enums/order.enum';

@Injectable()
export class OrderStatusService {
  private readonly logger = new Logger(OrderStatusService.name);

  constructor(
    @InjectRepository(OrderEntity)
    private readonly orderRepository: Repository<OrderEntity>,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * 定时检查并处理过期订单
   * 每5分钟执行一次
   */
  @Cron('0 */5 * * * *')
  async handleExpiredOrders(): Promise<void> {
    try {
      this.logger.log('开始检查过期订单');

      const now = new Date();
      
      // 查找所有过期的待支付订单
      const expiredOrders = await this.orderRepository.find({
        where: {
          status: OrderStatus.PENDING,
          expiresAt: LessThan(now),
        },
      });

      if (expiredOrders.length === 0) {
        this.logger.log('没有发现过期订单');
        return;
      }

      this.logger.log(`发现 ${expiredOrders.length} 个过期订单，开始处理`);

      // 批量更新订单状态为过期
      const orderIds = expiredOrders.map(order => order.id);
      await this.orderRepository.update(
        { id: In(orderIds) },
        { status: OrderStatus.EXPIRED }
      );

      // 发送订单过期事件
      for (const order of expiredOrders) {
        this.eventEmitter.emit('order.expired', {
          orderId: order.id,
          userId: order.userId,
          orderNo: order.orderNo,
          orderType: order.orderType,
          totalAmount: order.totalAmount,
        });
      }

      this.logger.log(`成功处理 ${expiredOrders.length} 个过期订单`);
    } catch (error) {
      this.logger.error(`处理过期订单失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 手动检查订单状态
   */
  async checkOrderStatus(orderId: number): Promise<{
    orderId: number;
    currentStatus: OrderStatus;
    isExpired: boolean;
    expiresAt?: Date;
  }> {
    const order = await this.orderRepository.findOne({ where: { id: orderId } });
    
    if (!order) {
      throw new Error('订单不存在');
    }

    const now = new Date();
    const isExpired = order.expiresAt && order.expiresAt < now;

    // 如果订单已过期但状态还是待支付，更新状态
    if (isExpired && order.status === OrderStatus.PENDING) {
      await this.orderRepository.update(orderId, { status: OrderStatus.EXPIRED });
      
      this.eventEmitter.emit('order.expired', {
        orderId: order.id,
        userId: order.userId,
        orderNo: order.orderNo,
        orderType: order.orderType,
        totalAmount: order.totalAmount,
      });

      return {
        orderId,
        currentStatus: OrderStatus.EXPIRED,
        isExpired: true,
        expiresAt: order.expiresAt,
      };
    }

    return {
      orderId,
      currentStatus: order.status,
      isExpired: isExpired || false,
      expiresAt: order.expiresAt,
    };
  }

  /**
   * 批量更新订单状态
   */
  async batchUpdateOrderStatus(
    orderIds: number[],
    newStatus: OrderStatus,
    reason?: string,
  ): Promise<{ successCount: number; failedCount: number }> {
    try {
      this.logger.log(`批量更新订单状态: ${orderIds.length} 个订单 -> ${newStatus}`);

      const result = await this.orderRepository.update(
        { id: In(orderIds) },
        {
          status: newStatus,
          remark: reason ? `${reason} (批量操作)` : undefined,
        }
      );

      const successCount = result.affected || 0;
      const failedCount = orderIds.length - successCount;

      this.logger.log(`批量更新完成: 成功 ${successCount} 个，失败 ${failedCount} 个`);

      return { successCount, failedCount };
    } catch (error) {
      this.logger.error(`批量更新订单状态失败: ${error.message}`, error.stack);
      return { successCount: 0, failedCount: orderIds.length };
    }
  }

  /**
   * 获取订单状态统计
   */
  async getOrderStatusStats(): Promise<{
    total: number;
    byStatus: Record<OrderStatus, number>;
    expiredCount: number;
    expiringSoonCount: number; // 1小时内过期
  }> {
    const now = new Date();
    const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);

    const [
      total,
      statusStats,
      expiredCount,
      expiringSoonCount,
    ] = await Promise.all([
      // 总订单数
      this.orderRepository.count(),
      
      // 按状态统计
      this.orderRepository
        .createQueryBuilder('order')
        .select('order.status', 'status')
        .addSelect('COUNT(*)', 'count')
        .groupBy('order.status')
        .getRawMany(),
      
      // 已过期订单数
      this.orderRepository.count({
        where: {
          status: OrderStatus.PENDING,
          expiresAt: LessThan(now),
        },
      }),
      
      // 即将过期订单数（1小时内）
      this.orderRepository.count({
        where: {
          status: OrderStatus.PENDING,
          expiresAt: LessThan(oneHourLater),
        },
      }),
    ]);

    // 构建按状态统计的对象
    const byStatus = {} as Record<OrderStatus, number>;
    Object.values(OrderStatus).forEach(status => {
      byStatus[status] = 0;
    });
    
    statusStats.forEach(stat => {
      byStatus[stat.status as OrderStatus] = parseInt(stat.count);
    });

    return {
      total,
      byStatus,
      expiredCount,
      expiringSoonCount,
    };
  }

  /**
   * 获取即将过期的订单列表
   */
  async getExpiringSoonOrders(hours: number = 1): Promise<OrderEntity[]> {
    const expireTime = new Date(Date.now() + hours * 60 * 60 * 1000);
    
    return this.orderRepository.find({
      where: {
        status: OrderStatus.PENDING,
        expiresAt: LessThan(expireTime),
      },
      order: {
        expiresAt: 'ASC',
      },
    });
  }

  /**
   * 延长订单过期时间
   */
  async extendOrderExpiration(
    orderId: number,
    additionalMinutes: number,
  ): Promise<{ success: boolean; newExpiresAt?: Date; message: string }> {
    try {
      const order = await this.orderRepository.findOne({ where: { id: orderId } });
      
      if (!order) {
        return { success: false, message: '订单不存在' };
      }

      if (order.status !== OrderStatus.PENDING) {
        return { success: false, message: '只能延长待支付订单的过期时间' };
      }

      const currentExpiresAt = order.expiresAt || new Date();
      const newExpiresAt = new Date(currentExpiresAt.getTime() + additionalMinutes * 60 * 1000);

      await this.orderRepository.update(orderId, { expiresAt: newExpiresAt });

      this.logger.log(`订单 ${order.orderNo} 过期时间已延长 ${additionalMinutes} 分钟`);

      return {
        success: true,
        newExpiresAt,
        message: `过期时间已延长 ${additionalMinutes} 分钟`,
      };
    } catch (error) {
      this.logger.error(`延长订单过期时间失败: ${error.message}`, error.stack);
      return { success: false, message: error.message };
    }
  }
}
