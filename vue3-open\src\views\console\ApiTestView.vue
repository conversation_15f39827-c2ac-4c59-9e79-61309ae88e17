<template>
  <div class="api-test-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">API测试工具</h1>
        <p class="page-subtitle">在线测试和调试API接口</p>
      </div>
      <div class="header-right">
        <el-button @click="loadExample">
          <el-icon>
            <Document />
          </el-icon>
          加载示例
        </el-button>
        <el-button type="primary" @click="saveRequest">
          <el-icon>
            <FolderAdd />
          </el-icon>
          保存请求
        </el-button>
      </div>
    </div>

    <el-row :gutter="24">
      <!-- 左侧请求配置 -->
      <el-col :lg="12" :md="24">
        <el-card class="request-card">
          <template #header>
            <div class="card-header">
              <h3>请求配置</h3>
              <el-button type="primary" @click="sendRequest" :loading="loading" :disabled="!canSendRequest">
                <el-icon>
                  <Position />
                </el-icon>
                发送请求
              </el-button>
            </div>
          </template>

          <!-- 基本信息 -->
          <div class="request-section">
            <h4>基本信息</h4>
            <el-form :model="requestConfig" label-width="80px">
              <el-form-item label="服务类型">
                <el-select v-model="requestConfig.serviceType" @change="onServiceTypeChange" :loading="servicesLoading" placeholder="请选择服务类型">
                  <el-option v-for="option in serviceTypeOptions" :key="option.value" :label="option.label" :value="option.value" />
                </el-select>
              </el-form-item>

              <el-form-item label="API接口">
                <el-select v-model="requestConfig.endpoint" @change="onEndpointChange" :loading="servicesLoading" placeholder="请选择API接口">
                  <el-option v-for="endpoint in availableEndpoints" :key="endpoint.value" :label="endpoint.label"
                    :value="endpoint.value" />
                </el-select>
                <div class="form-item-hint" v-if="currentServiceInfo">
                  <div class="service-info">
                    <strong>{{ currentServiceInfo.name }}</strong>
                    <span class="service-type">({{ currentServiceInfo.type }})</span>
                  </div>
                  <div class="service-description">{{ currentServiceInfo.description }}</div>
                </div>
                <div class="endpoint-info" v-if="requestConfig.endpoint">
                  <el-alert type="info" :closable="false" show-icon>
                    <template #title>
                      接口路径：<code>{{ requestConfig.endpoint }}</code>
                      <el-button link type="primary" size="small" @click="copyEndpoint">复制</el-button>
                    </template>
                  </el-alert>
                </div>
              </el-form-item>

              <el-form-item label="请求方法">
                <el-radio-group v-model="requestConfig.method">
                  <el-radio label="POST">POST</el-radio>
                  <el-radio label="GET">GET</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="处理模式">
                <div class="mode-setting">
                  <el-switch v-model="requestConfig.isAsync" active-text="异步模式" inactive-text="同步模式" class="mb-2" />
                  <el-tooltip content="异步模式适用于长耗时任务，可避免API调用超时。系统会返回jobId，通过SSE方式实时获取结果" placement="top" effect="light">
                    <el-icon class="info-icon">
                      <InfoFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
              </el-form-item>

              <el-form-item label="SSE设置" v-if="requestConfig.isAsync">
                <div class="mode-setting">
                  <el-switch v-model="autoConnectSSE" active-text="自动连接" inactive-text="手动连接" class="mb-2" />
                  <el-tooltip content="开启后将在请求成功后自动连接SSE获取任务结果" placement="top" effect="light">
                    <el-icon class="info-icon">
                      <InfoFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
              </el-form-item>
            </el-form>
          </div>

          <!-- 请求头 -->
          <div class="request-section">
            <div class="section-header">
              <h4>请求头</h4>
              <el-button size="small" @click="addHeader">
                <el-icon>
                  <Plus />
                </el-icon>
                添加
              </el-button>
            </div>
            <div class="headers-list">
              <div v-for="(header, index) in requestConfig.headers" :key="index" class="header-item">
                <el-input v-model="header.key" placeholder="Header名称" style="width: 40%;" />
                <el-input v-model="header.value" placeholder="Header值" style="width: 50%;" />
                <el-button size="small" @click="removeHeader(index)">
                  <el-icon>
                    <Delete />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <!-- 请求参数 -->
          <div class="request-section">
            <h4>请求参数</h4>
            <el-tabs v-model="paramType">
              <!-- 文件上传 -->
              <el-tab-pane label="文件上传" name="file" v-if="requestConfig.method === 'POST'">
                <div class="file-upload-section">
                  <FileUploader v-model="requestConfig.files" :accept="getAcceptTypes()" :multiple="false"
                    :max-size="10" :auto-upload="false" @change="onFileChange" />
                </div>
              </el-tab-pane>

              <!-- JSON参数 -->
              <el-tab-pane label="JSON参数" name="json" v-if="requestConfig.method === 'POST'">
                <div class="json-editor">
                  <el-input v-model="requestConfig.jsonBody" type="textarea" :rows="10" placeholder="请输入JSON格式的请求参数" />
                  <div class="json-actions">
                    <el-button size="small" @click="formatJson">格式化</el-button>
                    <el-button size="small" @click="validateJson">验证</el-button>
                  </div>
                </div>
              </el-tab-pane>

              <!-- URL参数 -->
              <el-tab-pane label="URL参数" name="query">
                <div class="query-params">
                  <div class="section-header">
                    <span>查询参数</span>
                    <el-button size="small" @click="addQueryParam">
                      <el-icon>
                        <Plus />
                      </el-icon>
                      添加
                    </el-button>
                  </div>
                  <div class="params-list">
                    <div v-for="(param, index) in requestConfig.queryParams" :key="index" class="param-item">
                      <el-input v-model="param.key" placeholder="参数名" style="width: 40%;" />
                      <el-input v-model="param.value" placeholder="参数值" style="width: 50%;" />
                      <el-button size="small" @click="removeQueryParam(index)">
                        <el-icon>
                          <Delete />
                        </el-icon>
                      </el-button>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 认证信息 -->
          <div class="request-section">
            <h4>认证信息</h4>
            <el-form :model="requestConfig" label-width="100px">
              <el-form-item label="API密钥">
                <el-input v-model="requestConfig.apiKey" placeholder="输入API密钥" />
                <div class="form-item-hint">请输入您的API密钥（作为 X-API-KEY 发送）</div>
              </el-form-item>
              <el-form-item label="Secret密钥">
                <el-input v-model="requestConfig.secretKey" placeholder="输入Secret密钥" show-password />
                <div class="form-item-hint">仅用于本地生成签名，不会随请求发送</div>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧响应结果 -->
      <el-col :lg="12" :md="24">
        <el-card class="response-card">
          <template #header>
            <div class="card-header">
              <h3>响应结果</h3>
              <div class="response-actions" v-if="response">
                <el-button size="small" @click="copyResponse">
                  <el-icon>
                    <CopyDocument />
                  </el-icon>
                  复制
                </el-button>
                <el-button size="small" @click="downloadResponse">
                  <el-icon>
                    <Download />
                  </el-icon>
                  下载
                </el-button>
              </div>
            </div>
          </template>

          <!-- 响应状态 -->
          <div v-if="response" class="response-status">
            <div class="status-info">
              <el-tag :type="getStatusType(response.status)" size="large">
                {{ response.status }} {{ response.statusText }}
              </el-tag>
              <span class="response-time">{{ response.responseTime }}ms</span>

              <!-- 异步任务状态 -->
              <el-tag v-if="response.isAsync && response.jobId" :type="getTaskStatusType()" size="large" style="margin-left: 8px;">
                {{ getTaskStatusText() }}
              </el-tag>
            </div>
            <div class="response-size">
              响应大小: {{ formatBytes(response.size) }}
            </div>
          </div>

          <!-- 异步任务进度 -->
          <div v-if="response && response.isAsync && response.jobId" class="task-progress-section">
            <div class="progress-header">
              <span>任务进度</span>
              <span>{{ taskProgress }}%</span>
            </div>
            <el-progress
              :percentage="taskProgress"
              :status="taskStatus === 'failed' ? 'exception' : (taskStatus === 'completed' ? 'success' : undefined)"
              :stroke-width="8"
            />
            <div v-if="taskStatus !== 'queued'" class="progress-info">
              <span>任务ID: {{ response.jobId }}</span>
              <span>状态: {{ getTaskStatusText() }}</span>
            </div>
          </div>

          <!-- 响应头 -->
          <div v-if="response" class="response-section">
            <h4>响应头</h4>
            <div class="response-headers">
              <div v-for="(value, key) in response.headers" :key="key" class="header-row">
                <span class="header-key">{{ key }}:</span>
                <span class="header-value">{{ value }}</span>
              </div>
            </div>
          </div>

          <!-- 响应体 -->
          <div v-if="response" class="response-section">
            <div class="section-header">
              <h4>响应体</h4>
              <el-radio-group v-model="responseViewType" size="small">
                <el-radio-button label="pretty">格式化</el-radio-button>
                <el-radio-button label="raw">原始</el-radio-button>
                <el-radio-button label="preview" v-if="isImageResponse">预览</el-radio-button>
              </el-radio-group>
            </div>

            <!-- 格式化视图 -->
            <div v-if="responseViewType === 'pretty'" class="response-body pretty">
              <pre><code>{{ formatResponseBody(response.data) }}</code></pre>
            </div>

            <!-- 原始视图 -->
            <div v-else-if="responseViewType === 'raw'" class="response-body raw">
              <el-input :model-value="response.rawData" type="textarea" :rows="15" readonly />
            </div>

            <!-- 图片预览 -->
            <div v-else-if="responseViewType === 'preview' && isImageResponse" class="response-body preview">
              <el-image :src="response.imageUrl" fit="contain" style="max-width: 100%; max-height: 400px;" />
            </div>
          </div>

          <!-- 任务状态 -->
          <div v-if="response && response.isAsync" class="task-status-section">
            <h4>任务状态</h4>
            <div class="task-info">
              <div class="job-id">任务ID: {{ response.jobId }}</div>
              <div class="status-wrapper">
                <div class="status-label">状态: {{ taskStatus }}</div>
                <el-progress :percentage="taskProgress" :status="taskProgressStatus"></el-progress>
              </div>
            </div>

            <div class="task-actions">
              <el-button @click="connectSSE" type="primary" :disabled="sseConnected" size="small">
                实时获取结果
              </el-button>
              <el-button @click="checkTaskStatus" :disabled="!response.jobId || sseConnected" size="small">
                查询任务状态
              </el-button>
            </div>
          </div>

          <!-- 任务结果 -->
          <div v-if="taskResult" class="response-section">
            <div class="section-header">
              <h4>任务结果</h4>
            </div>
            <!-- 地址提取结果展示 -->
            <div v-if="isAddressExtractionResult" class="address-extraction-result">
              <el-card v-for="(address, index) in taskResult" :key="index" class="address-card">
                <template #header>
                  <div class="address-card-header">
                    <span>地址 #{{ index + 1 }}</span>
                    <el-tag size="small" :type="(address as any).confidence > 0.8 ? 'success' : (address as any).confidence > 0.6 ? 'warning' : 'danger'">
                      可信度: {{ ((address as any).confidence * 100).toFixed(0) }}%
                    </el-tag>
                  </div>
                </template>
                <div class="address-details">
                  <div class="address-item" v-if="(address as any).name">
                    <span class="label">姓名:</span>
                    <span class="value">{{ (address as any).name }}</span>
                  </div>
                  <div class="address-item" v-if="(address as any).phone">
                    <span class="label">电话:</span>
                    <span class="value">{{ (address as any).phone }}</span>
                  </div>
                  <div class="address-item" v-if="(address as any).province || (address as any).city">
                    <span class="label">省市:</span>
                    <span class="value">{{ (address as any).province || '' }} {{ (address as any).city || '' }}</span>
                  </div>
                  <div class="address-item" v-if="(address as any).district">
                    <span class="label">区县:</span>
                    <span class="value">{{ (address as any).district }}</span>
                  </div>
                  <div class="address-item" v-if="(address as any).detailAddress || (address as any).detail_address">
                    <span class="label">详细地址:</span>
                    <span class="value">{{ (address as any).detailAddress || (address as any).detail_address || '' }}</span>
                  </div>
                  <div class="address-item" v-if="(address as any).fullAddress">
                    <span class="label">完整地址:</span>
                    <span class="value">{{ (address as any).fullAddress }}</span>
                  </div>
                  <div class="address-item" v-if="(address as any).extractMethod">
                    <span class="label">提取方法:</span>
                    <span class="value">{{ (address as any).extractMethod }}</span>
                  </div>
                </div>
              </el-card>
            </div>
            <!-- 默认的JSON显示 -->
            <div v-else class="response-body pretty">
              <pre><code>{{ formatResponseBody(taskResult) }}</code></pre>
            </div>
          </div>
          <!-- 调试信息 -->
          <div v-if="response" class="debug-info">
            <h4>响应数据</h4>
            <p>任务ID: {{ response.jobId || '无' }}</p>
            <p>是否异步: {{ response.isAsync ? '是' : '否' }}</p>
            <pre>{{ JSON.stringify(response.data, null, 2) }}</pre>
          </div>

          <!-- SSE实时消息面板 -->
          <div v-if="response" class="sse-message-panel">
            <div class="section-header">
              <h4>实时任务消息</h4>
              <div class="sse-status">
                <el-tag :type="sseConnected ? 'success' : 'info'" size="small">
                  {{ sseConnected ? '已连接' : '未连接' }}
                </el-tag>
                <el-button v-if="response?.jobId && !sseConnected" type="primary" size="small" @click="connectSSE"
                  style="margin-left: 10px;">
                  连接SSE
                </el-button>
                <el-button v-if="response?.jobId && !sseConnected" size="small" @click="checkTaskStatus"
                  style="margin-left: 10px;">
                  查询状态
                </el-button>
              </div>
            </div>
            <div class="sse-messages">
              <div v-if="sseMessages.length === 0" class="empty-messages">
                <p>暂无实时消息，请点击"实时获取结果"按钮连接SSE</p>
              </div>
              <div v-else class="message-list">
                <div v-for="(message, index) in sseMessages" :key="index" class="message-item"
                  :class="{ 'message-success': message.type === 'success', 'message-error': message.type === 'error', 'message-info': message.type === 'info' }">
                  <div class="message-time">{{ message.time }}</div>
                  <div class="message-content">{{ message.content }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-response">
            <el-empty description="暂无响应数据">
              <template #image>
                <el-icon size="60" color="#C0C4CC">
                  <Position />
                </el-icon>
              </template>
              <template #description>
                <p>配置请求参数后点击"发送请求"查看响应结果</p>
              </template>
            </el-empty>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { InfoFilled, Document, FolderAdd, Position, Plus, Delete, CopyDocument, Download } from '@element-plus/icons-vue'
import FileUploader from '@/components/upload/FileUploader.vue'
import { apiTestApi } from '@/api/apiTest.ts'
import { EventSourcePolyfill } from 'event-source-polyfill'
import CryptoJS from 'crypto-js'
import { buildCanonicalRequest, generateSignature, generateNonce } from '@/utils/signature-test'
import { serviceApi } from '@/api/service'
import type { Service } from '@/types/service'

const route = useRoute()

// 响应式数据
const loading = ref(false)
const paramType = ref('file')
const responseViewType = ref('pretty')
const showSaveDialog = ref(false)
const taskStatus = ref('queued')
const taskProgress = ref(0)
const taskResult = ref<any>(null)
const sseConnected = ref(false)
const sseMessages = ref<{ time: string; content: string; type: 'success' | 'error' | 'info' | 'warning' }[]>([])
let eventSource: InstanceType<typeof EventSourcePolyfill> | null = null
const autoConnectSSE = ref(true)

// SSE重连控制
const sseRetryCount = ref(0)
const maxSSERetries = 20 // 最大重试次数
const sseRetryDelay = 3000 // 重连延迟（毫秒）
const sseMaxWaitTime = 60000 // 最大等待时间（60秒）
let sseRetryTimer: number | null = null
let sseTimeoutTimer: number | null = null
const finalTaskStatuses = ['completed', 'failed', 'cancelled'] // 终态状态
const sseTaskCompleted = ref(false) // 标记任务是否已完成（收到final事件）
const sseDisabled = ref(false) // 全局禁用SSE标志

// 服务相关数据
const services = ref<Service[]>([])
const servicesLoading = ref(false)
const currentService = ref<Service | null>(null)

// 请求配置
const requestConfig: any = reactive({
  serviceType: '',
  endpoint: '',
  method: 'POST',
  headers: [
    { key: 'Content-Type', value: 'application/json' }
  ],
  queryParams: [],
  jsonBody: '',
  files: [],
  apiKey: '',
  secretKey: '',
  isAsync: true
})

// 当请求方法切换时，确保Tabs激活面板存在
watch(
  () => requestConfig.method,
  (val) => {
    if (val === 'GET') {
      // GET 仅保留URL参数面板
      paramType.value = 'query'
    } else {
      // POST 默认切回JSON参数（若当前是query）
      if (paramType.value === 'query') {
        paramType.value = 'json'
      }
    }
  },
)

// 响应数据
const response = ref<any>(null)

// 请求历史
const requestHistory = ref<any[]>([])

// 保存表单
const saveForm = reactive({
  name: '',
  description: ''
})

// 动态生成服务类型选项
const serviceTypeOptions = computed(() => {
  const typeMap = new Map<string, { label: string; value: string }>()
  services.value.forEach(service => {
    if (service.type && !typeMap.has(service.type)) {
      const label = getServiceTypeLabel(service.type)
      typeMap.set(service.type, { label, value: service.type })
    }
  })
  return Array.from(typeMap.values())
})

const getServiceTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    'ai_service': 'AI服务',
    'ocr': 'OCR识别',
    'nlp': 'NLP处理',
    'cv': '计算机视觉',
    'geo': '地理服务',
    'data': '数据服务',
    'other': '其他服务'
  }
  return labelMap[type] || type
}

const availableEndpoints = computed(() => {
  if (!requestConfig.serviceType) return []
  const filteredServices = services.value.filter(service => service.type === requestConfig.serviceType)
  return filteredServices.map(service => ({
    label: service.name,
    value: service.endpoint || getServiceEndpoint(service),
    service: service
  }))
})

// 根据服务类型获取正确的端点路径
function getServiceEndpoint(service: Service): string {
  // 根据服务代码直接映射到具体的API端点
  const endpointMap: Record<string, string> = {
    // OCR服务
    'OCR_UPLOAD': '/v1/op/ocr/upload',                    // OCR文件上传识别
    'OCR_RECOGNIZE': '/v1/op/ocr/recognize',              // OCR Base64图片识别
    'OCR_STO_UPLOAD': '/v1/op/ocr/sto/upload',            // 申通面单OCR文件上传识别
    'OCR_STO_RECOGNIZE': '/v1/op/ocr/sto/recognize',      // 申通面单OCR Base64识别
    
    // 地址服务
    'ADDRESS_EXTRACT': '/v1/op/address/extract',          // 从文本中提取地址信息
    'ADDRESS_NORMALIZE': '/v1/op/address/normalize',      // 地址标准化
    
    // 地理服务
    'GEO_REVERSE': '/v1/op/geo/reverse',                  // 逆地理编码（坐标转地址）
    'GEO_FORWARD': '/v1/op/geo/forward',                  // 正地理编码（地址转坐标）
    
    // 其他服务类型（兜底）
    'geo': '/v1/op/geo',
    'ocr': '/v1/op/ocr', 
    'address': '/v1/op/address',
    'ai_service': '/v1/op/ai',
    'nlp': '/v1/op/nlp',
    'cv': '/v1/op/cv',
    'data': '/v1/op/data'
  }
  
  // 优先使用服务代码映射，如果没有则使用类型映射
  const endpoint = endpointMap[service.code] || endpointMap[service.type] || `/v1/op/${service.type}`
  
  return endpoint
}

const canSendRequest = computed(() => {
  return requestConfig.endpoint && requestConfig.apiKey && requestConfig.secretKey && !loading.value
})

const isImageResponse = computed(() => {
  return response.value && response.value.headers?.['content-type']?.includes('image')
})

const taskProgressStatus = computed(() => {
  if (taskStatus.value === 'failed') return 'exception'
  if (taskStatus.value === 'completed_no_data') return 'warning'
  if (taskStatus.value === 'completed' && taskResult.value) return 'success'
  return ''
})

const currentServiceInfo = computed(() => {
  if (currentService.value) {
    return {
      name: currentService.value.name,
      description: currentService.value.description,
      type: getServiceTypeLabel(currentService.value.type),
      endpoint: currentService.value.endpoint
    }
  }
  return null
})

// 是否为地址提取结果（用于结构化展示）
const isAddressExtractionResult = computed(() => {
  if (!taskResult.value) return false
  // 简单判断：数组且元素包含典型字段
  if (Array.isArray(taskResult.value)) {
    const first = taskResult.value[0] || {}
    return 'province' in first || 'city' in first || 'detailAddress' in first || 'detail_address' in first
  }
  return false
})

// 工具方法
function nowIsoTime(): string {
  return new Date().toISOString().slice(11, 19)
}

function addSSEMessage(content: string, type: 'success' | 'error' | 'info' | 'warning' = 'info') {
  sseMessages.value.push({ time: nowIsoTime(), content, type })
}

function getStatusType(status: number) {
  if (status >= 200 && status < 300) return 'success'
  if (status >= 400 && status < 500) return 'warning'
  if (status >= 500) return 'danger'
  return ''
}

function getTaskStatusType() {
  switch (taskStatus.value) {
    case 'completed': return 'success'
    case 'failed': return 'danger'
    case 'active':
    case 'processing': return 'warning'
    default: return 'info'
  }
}

function getTaskStatusText() {
  switch (taskStatus.value) {
    case 'queued': return '排队中'
    case 'active': return '处理中'
    case 'processing': return '处理中'
    case 'completed': return '已完成'
    case 'failed': return '处理失败'
    default: return '未知状态'
  }
}

function formatBytes(bytes: number) {
  if (!bytes && bytes !== 0) return '-'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`
}

function formatResponseBody(data: any) {
  try { return JSON.stringify(data, null, 2) } catch { return String(data) }
}

function copyEndpoint() {
  if (!requestConfig.endpoint) return
  navigator.clipboard?.writeText(requestConfig.endpoint)
  ElMessage.success('已复制接口路径')
}

function copyResponse() {
  if (!response.value) return
  navigator.clipboard?.writeText(typeof response.value.data === 'string' ? response.value.data : JSON.stringify(response.value.data))
  ElMessage.success('已复制响应')
}

function downloadResponse() {
  if (!response.value) return
  const blob = new Blob([typeof response.value.data === 'string' ? response.value.data : JSON.stringify(response.value.data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'response.json'
  a.click()
  URL.revokeObjectURL(url)
}

function addHeader() {
  requestConfig.headers.push({ key: '', value: '' })
}

function removeHeader(index: number) {
  requestConfig.headers.splice(index, 1)
}

function addQueryParam() {
  requestConfig.queryParams.push({ key: '', value: '' })
}

function removeQueryParam(index: number) {
  requestConfig.queryParams.splice(index, 1)
}

function getAcceptTypes() {
  // 基于服务类型简单映射
  if (requestConfig.serviceType === 'ocr') return '.png,.jpg,.jpeg,.webp'
  return '*/*'
}

function onFileChange(files: File[]) {
  requestConfig.files = files
}

function formatJson() {
  if (!requestConfig.jsonBody) return
  try {
    requestConfig.jsonBody = JSON.stringify(JSON.parse(requestConfig.jsonBody), null, 2)
  } catch {
    ElMessage.error('JSON格式不正确')
  }
}

function validateJson() {
  if (!requestConfig.jsonBody) return ElMessage.info('空JSON')
  try {
    JSON.parse(requestConfig.jsonBody)
    ElMessage.success('JSON有效')
  } catch {
    ElMessage.error('JSON无效')
  }
}

// 服务加载与变更
async function loadServices() {
  try {
    servicesLoading.value = true
    const res: any = await serviceApi.getServices({ page: 1, pageSize: 100 })
    
    // 处理响应数据格式
    if (res && res.data) {
      // 如果response.data本身就是服务数组（直接返回的情况）
      if (Array.isArray(res.data)) {
        services.value = res.data
      }
      // 如果response.data包含data和meta字段（标准分页格式）
      else if (res.data.data && Array.isArray(res.data.data)) {
        services.value = res.data.data
      }
      // 其他格式
      else {
        services.value = []
        console.warn('API返回数据格式不正确:', res)
      }
    } else {
      services.value = []
      console.warn('API返回数据为空:', res)
    }
    
    console.log('API测试页面加载服务成功:', {
      servicesCount: services.value.length,
      services: services.value.map(s => ({ id: s.id, name: s.name, type: s.type, code: s.code, endpoint: s.endpoint }))
    })
  } catch (error) {
    console.error('加载服务列表失败:', error)
    services.value = []
  } finally {
    servicesLoading.value = false
  }
}

async function loadServiceByCode(code: string) {
  try {
    const detail: any = await serviceApi.getServiceByCode(code)
    if (detail) {
      console.log('根据服务代码加载服务详情:', detail)
      requestConfig.serviceType = detail.type
      currentService.value = detail as Service
      requestConfig.endpoint = detail.endpoint || getServiceEndpoint(detail as Service)
      onEndpointChange()
    }
  } catch (error) {
    console.error('根据服务代码加载服务失败:', error)
    // 如果通过API获取失败，尝试从已加载的服务列表中查找
    const foundService = services.value.find(s => s.code === code)
    if (foundService) {
      console.log('从已加载的服务列表中找到服务:', foundService)
      requestConfig.serviceType = foundService.type
      currentService.value = foundService
      requestConfig.endpoint = foundService.endpoint || getServiceEndpoint(foundService)
      onEndpointChange()
    }
  }
}

function onServiceTypeChange() {
  const list = availableEndpoints.value
  if (!list.length) return
  // 默认切第一个
  requestConfig.endpoint = list[0].value
  currentService.value = list[0].service
  onEndpointChange()
}

function onEndpointChange() {
  // 同步 currentService
  const hit = availableEndpoints.value.find(e => e.value === requestConfig.endpoint)
  currentService.value = hit?.service || null
  // 根据端点加载示例
  loadExample()
}

// 示例加载（精简：按服务类型/端点设置参数范例）
function loadExample() {
  // 基础默认
  requestConfig.method = 'POST'
  paramType.value = 'json'
  requestConfig.headers = [{ key: 'Content-Type', value: 'application/json' }]

  const endpoint = requestConfig.endpoint || ''
  if (endpoint.includes('/ocr')) {
    requestConfig.method = 'POST'
    paramType.value = 'file'
  } else if (endpoint.includes('/address')) {
    requestConfig.method = 'POST'
        paramType.value = 'json'
    requestConfig.jsonBody = JSON.stringify({ text: '张三，13800138000，广东省深圳市南山区科技园' }, null, 2)
  } else if (endpoint.includes('/geo/reverse')) {
    // 逆地理编码：坐标转地址
    requestConfig.method = 'GET'
    paramType.value = 'query'
    requestConfig.queryParams = [
      { key: 'lat', value: '22.543096' },
      { key: 'lng', value: '114.057865' }
    ]
  } else if (endpoint.includes('/geo/forward')) {
    // 正地理编码：地址转坐标
    requestConfig.method = 'GET'
    paramType.value = 'query'
    requestConfig.queryParams = [
      { key: 'address', value: '广东省深圳市南山区科技园' }
    ]
  } else if (endpoint.includes('/geo')) {
    // 其他地理服务
    requestConfig.method = 'GET'
    paramType.value = 'query'
    requestConfig.queryParams = [
      { key: 'lat', value: '22.543096' },
      { key: 'lng', value: '114.057865' }
    ]
    } else {
    // 其他服务默认
    requestConfig.method = 'POST'
          paramType.value = 'json'
    requestConfig.jsonBody = JSON.stringify({ text: 'hello' }, null, 2)
  }
}

// 请求去重：存储最近的请求签名，防止重复发送
const recentRequests = new Set<string>()

// 当前请求的AbortController，用于取消重复请求
let currentRequestController: AbortController | null = null

// 构建签名头
function buildSignedHeaders(method: string, path: string, queryParams: Record<string, any>, body: any) {
  const timestamp = Math.floor(Date.now() / 1000).toString()
  const nonce = generateNonce()
  const canonical = buildCanonicalRequest(method, path, queryParams, body, timestamp)
  const signature = generateSignature(canonical, requestConfig.secretKey)

  // 生成请求唯一标识，用于去重
  const requestId = `${method}:${path}:${timestamp}:${nonce}`

  // 检查是否是重复请求
  if (recentRequests.has(requestId)) {
    console.warn('检测到重复请求，重新生成签名')
    // 递归调用，生成新的签名
    return buildSignedHeaders(method, path, queryParams, body)
  }

  // 记录请求，5秒后自动清除
  recentRequests.add(requestId)
  setTimeout(() => {
    recentRequests.delete(requestId)
  }, 5000)

  // 确保API Key包含正确的前缀
  let apiKey = requestConfig.apiKey.trim()
  if (!apiKey.startsWith('ak-') && !apiKey.startsWith('user_') && !apiKey.startsWith('admin_')) {
    apiKey = `ak-${apiKey}`
  }

  const headers: Record<string, string> = {
    'X-API-KEY': apiKey,
    'X-Timestamp': timestamp,
    'X-Nonce': nonce,
    'X-Signature': signature,
  }
  return headers
}

// 发送请求（带签名，不发送Secret）
async function sendRequest() {
  if (!requestConfig.apiKey || !requestConfig.secretKey) {
    ElMessage.warning('请先填写API密钥与Secret密钥')
    return
  }
  if (!requestConfig.endpoint) {
    ElMessage.warning('请选择API接口')
    return
  }

  // 防重复提交
  if (loading.value) {
    ElMessage.warning('请求正在处理中，请稍候...')
    return
  }

  // 重置SSE状态（新请求开始）
  resetSSEState()

  // 取消之前的请求
  if (currentRequestController) {
    currentRequestController.abort()
    currentRequestController = null
  }

  // 创建新的AbortController
  currentRequestController = new AbortController()

  loading.value = true
  const start = performance.now()
  try {
    // 解析URL与基础路径
    // 由于Vite代理已经处理了/v1路径，这里不需要额外的baseUrl
    const fullUrl = requestConfig.endpoint.startsWith('http') ? requestConfig.endpoint : requestConfig.endpoint

    // 准备query/body
    const query: Record<string, any> = {}
    requestConfig.queryParams?.forEach((p: any) => { if (p.key) query[p.key] = p.value })

    // 添加服务ID到查询参数（对于GET请求）
    if (currentService.value?.id && requestConfig.method === 'GET') {
      query.serviceId = currentService.value.id
    }

    // 注入处理模式
    if (requestConfig.isAsync) {
      if (requestConfig.method === 'GET') query.mode = 'async'
    }

    let data: any = undefined
    let isFileUpload = false

    if (requestConfig.method === 'POST') {
      if (paramType.value === 'file') {
        isFileUpload = true
        if (!requestConfig.files || requestConfig.files.length === 0) {
          ElMessage.error('请先选择要上传的文件')
          loading.value = false
          return
        }
        // FormData 签名时仅用占位字符串（与后端规则一致）
        data = requestConfig.files[0]
      } else {
        data = requestConfig.jsonBody ? JSON.parse(requestConfig.jsonBody) : {}
        if (requestConfig.isAsync) data.mode = 'async'
      }
    }

    // 添加服务ID到请求体（如果有当前服务信息）
    if (currentService.value?.id && data && typeof data === 'object') {
      data.serviceId = currentService.value.id
    }

    // 构建签名头
    const headers = buildSignedHeaders(requestConfig.method, requestConfig.endpoint, query, data)

    // 合并自定义头
    requestConfig.headers?.forEach((h: any) => { if (h.key) headers[h.key] = h.value })

    // 发送
    const res = await apiTestApi.sendRequest({
      method: requestConfig.method,
      url: fullUrl,
      headers,
      params: query,
      data,
      isFileUpload,
      isAsync: requestConfig.isAsync,
      signal: currentRequestController?.signal,
    })

    const end = performance.now()
    response.value = {
      ...res,
      responseTime: Math.round(end - start),
      size: JSON.stringify(res.data)?.length || 0,
      isAsync: requestConfig.isAsync,
      jobId: (res.data && (res.data.taskId || res.data.data?.taskId)) || undefined,
    }

    // 如果是异步任务且有taskId，自动连接SSE
    if (requestConfig.isAsync && response.value.jobId && autoConnectSSE.value) {
      nextTick(() => {
        connectSSE()
      })
    }
  } catch (e: any) {
    console.error(e)
    // 如果是请求被取消，不显示错误消息
    if (e.name !== 'AbortError' && e.name !== 'CanceledError') {
      ElMessage.error(e?.message || '请求失败')
    }
  } finally {
    loading.value = false
    currentRequestController = null
  }
}

// 轮询任务状态
async function checkTaskStatus() {
  if (!response.value?.jobId) return

  const taskUrlPath = `/v1/op/tasks/${response.value.jobId}`
  const signedHeaders = buildSignedHeaders('GET', taskUrlPath, {}, {})

  const status = await apiTestApi.getTaskStatus(response.value.jobId, signedHeaders)
  taskStatus.value = status.status
  taskProgress.value = status.progress ?? (status.status === 'completed' ? 100 : 0)
  taskResult.value = status.result || status.addresses || status.data || null
}

// SSE 连接/关闭
function connectSSE() {
  console.log('尝试连接SSE，当前状态:', {
    sseConnected: sseConnected.value,
    sseTaskCompleted: sseTaskCompleted.value,
    hasJobId: !!response.value?.jobId,
    taskStatus: taskStatus.value
  })

  if (sseConnected.value || !response.value?.jobId || eventSource) {
    console.log('SSE连接被阻止：已连接或无jobId或已有EventSource实例')
    return
  }

  // 检查SSE是否被禁用
  if (sseDisabled.value) {
    console.log('SSE已被全局禁用')
    return
  }

  // 检查任务是否已经完成
  if (sseTaskCompleted.value || finalTaskStatuses.includes(taskStatus.value)) {
    addSSEMessage(`任务已${taskStatus.value === 'completed' ? '完成' : '结束'}，无需连接SSE`, 'info')
    return
  }

  // 检查重试次数
  if (sseRetryCount.value >= maxSSERetries) {
    addSSEMessage(`SSE重试次数已达上限(${maxSSERetries}次)，停止连接`, 'error')
    return
  }

  const path = `/v1/op/tasks/${response.value.jobId}/events`
  const timestamp = Math.floor(Date.now() / 1000).toString()
  const nonce = generateNonce()
  const canonical = buildCanonicalRequest('GET', path, {}, {}, timestamp)
  const signature = generateSignature(canonical, requestConfig.secretKey)

  // 确保API Key包含正确的前缀
  let apiKey = requestConfig.apiKey.trim()
  if (!apiKey.startsWith('ak-') && !apiKey.startsWith('user_') && !apiKey.startsWith('admin_')) {
    apiKey = `ak-${apiKey}`
  }

  const url = path
  const headers = {
    'X-API-KEY': apiKey,
    'X-Timestamp': timestamp,
    'X-Nonce': nonce,
    'X-Signature': signature,
  } as any

  if (sseRetryCount.value > 0) {
    addSSEMessage(`正在重连SSE... (第${sseRetryCount.value + 1}次尝试)`, 'info')
  } else {
    addSSEMessage('正在连接SSE...', 'info')
  }

  eventSource = new EventSourcePolyfill(url, { headers })

  // 设置超时定时器
  sseTimeoutTimer = setTimeout(() => {
    if (!sseTaskCompleted.value) {
      console.log('SSE连接超时，强制关闭')
      addSSEMessage(`任务处理超时（${sseMaxWaitTime / 1000}秒），强制关闭连接`, 'error')
      sseTaskCompleted.value = true // 标记为已完成，避免重连
      taskStatus.value = 'timeout'
      closeSSEConnection(false)
    }
  }, sseMaxWaitTime) as any

  eventSource.onopen = () => {
    sseConnected.value = true
    sseRetryCount.value = 0 // 连接成功，重置重试计数
    addSSEMessage('SSE连接已建立', 'success')
  }

  eventSource.onmessage = (ev) => {
    try {
      console.log('收到原始SSE数据:', ev.data)
      const data = JSON.parse(ev.data)
      console.log('解析后的SSE消息:', data)
      console.log('当前sseTaskCompleted状态:', sseTaskCompleted.value)

      // 处理不同类型的SSE事件
      if (data.type === 'connected') {
        addSSEMessage('SSE连接已建立', 'success')
        return
      }

      if (data.type === 'status') {
        // 更新任务状态和进度
        if (data.status) {
          taskStatus.value = data.status
          addSSEMessage(`任务状态: ${data.status}`, 'info')
        }

        if (data.progress !== undefined) {
          taskProgress.value = Math.round(data.progress * 100) // 转换为百分比
        }

        if (data.message) {
          addSSEMessage(data.message, 'info')
        }

        // 检查是否为终态状态
        if (finalTaskStatuses.includes(data.status)) {
          console.log('检测到任务完成状态，等待final事件...')
        }
      }

      if (data.type === 'final') {
        console.log('收到final事件，任务已完成')

        // 标记任务已完成并禁用SSE
        sseTaskCompleted.value = true
        sseDisabled.value = true

        // 更新最终状态
        if (data.status) {
          taskStatus.value = data.status
        }

        // 处理任务结果
        if (data.result) {
          taskResult.value = data.result
          addSSEMessage('任务结果已更新', 'success')
        }

        // 显示完成消息
        if (data.status === 'completed') {
          addSSEMessage('任务处理完成！', 'success')
        } else if (data.status === 'failed') {
          addSSEMessage(`任务处理失败: ${data.error || '未知错误'}`, 'error')
        } else {
          addSSEMessage(`任务已${data.status}`, 'info')
        }

        // 立即关闭EventSource，防止自动重连
        console.log('任务已完成，立即关闭SSE连接')
        if (eventSource) {
          console.log('强制关闭EventSource，当前状态:', eventSource.readyState)
          eventSource.close()
          console.log('EventSource关闭后状态:', eventSource.readyState)
          eventSource = null
        }
        sseConnected.value = false

        // 清除所有定时器
        if (sseRetryTimer) {
          clearTimeout(sseRetryTimer)
          sseRetryTimer = null
        }
        if (sseTimeoutTimer) {
          clearTimeout(sseTimeoutTimer)
          sseTimeoutTimer = null
        }

        console.log('SSE连接已完全关闭，不会重连')
        return // 立即返回，避免后续处理
      }

      if (data.type === 'error') {
        addSSEMessage(`任务错误: ${data.error || '未知错误'}`, 'error')
        closeSSEConnection(false) // 错误时不重连
      }

    } catch (error) {
      console.error('解析SSE消息失败:', error)
      addSSEMessage('收到无效的SSE消息', 'warning')
    }
  }

  eventSource.onerror = (error) => {
    console.error('SSE连接错误:', error)
    console.log('SSE错误时状态检查:', {
      sseTaskCompleted: sseTaskCompleted.value,
      taskStatus: taskStatus.value,
      sseConnected: sseConnected.value
    })

    // 检查是否是正常关闭（已收到final事件或任务已完成）
    if (sseTaskCompleted.value || finalTaskStatuses.includes(taskStatus.value)) {
      console.log('任务已完成，SSE连接正常关闭')
      addSSEMessage('SSE连接已关闭', 'info')
      closeSSEConnection(false) // 不重连
    } else {
      console.log('SSE连接异常断开，准备重连')
      addSSEMessage('SSE连接发生错误', 'error')
      closeSSEConnection(true) // 允许重连
    }
  }
}

function closeSSEConnection(shouldRetry: boolean = false) {
  console.log(`关闭SSE连接，shouldRetry: ${shouldRetry}, 当前任务状态: ${taskStatus.value}`)

  // 清除所有定时器
  if (sseRetryTimer) {
    clearTimeout(sseRetryTimer)
    sseRetryTimer = null
  }
  if (sseTimeoutTimer) {
    clearTimeout(sseTimeoutTimer)
    sseTimeoutTimer = null
  }

  // 关闭当前连接
  if (eventSource) {
    eventSource.close()
    eventSource = null
  }
  sseConnected.value = false

  // 决定是否重连
  if (shouldRetry && !sseTaskCompleted.value && !finalTaskStatuses.includes(taskStatus.value)) {
    if (sseRetryCount.value < maxSSERetries) {
      sseRetryCount.value++
      console.log(`准备重连SSE，第${sseRetryCount.value}次尝试`)
      addSSEMessage(`${sseRetryDelay / 1000}秒后尝试重连... (${sseRetryCount.value}/${maxSSERetries})`, 'warning')

      sseRetryTimer = setTimeout(() => {
        connectSSE()
      }, sseRetryDelay) as any
    } else {
      console.log('已达到最大重试次数，停止重连')
      sseTaskCompleted.value = true // 标记为已完成，避免再次重连
      taskStatus.value = 'failed'
      addSSEMessage(`已达到最大重试次数(${maxSSERetries})，停止重连`, 'error')
      addSSEMessage('建议手动查询任务状态或刷新页面重试', 'info')
    }
  } else {
    if (sseTaskCompleted.value || finalTaskStatuses.includes(taskStatus.value)) {
      console.log('任务已完成，不再重连')
      addSSEMessage('任务已完成，SSE连接已关闭', 'info')
    } else {
      console.log('不满足重连条件，停止重连')
    }
  }
}

// 重置SSE状态（用于新任务）
function resetSSEState() {
  closeSSEConnection(false)
  sseRetryCount.value = 0
  sseTaskCompleted.value = false // 重置完成标志
  sseDisabled.value = false // 重置禁用标志
  sseMessages.value = []
  taskStatus.value = 'queued'
  taskProgress.value = 0
  taskResult.value = null
  console.log('SSE状态已重置')
}

function saveRequest() {
  // 先最小实现以避免报错；完整保存逻辑后续补充
  ElMessage.success('已保存到本地（演示）')
}

onMounted(async () => {
  await loadServices()

  // 从URL参数获取服务信息
  const serviceCode = route.query.code as string
  const serviceType = route.query.serviceType as string
  const endpoint = route.query.endpoint as string

  console.log('API测试页面URL参数:', { serviceCode, serviceType, endpoint })

  // 优先处理服务代码
  if (serviceCode) {
    console.log('根据服务代码加载服务:', serviceCode)
    await loadServiceByCode(serviceCode)
  }
  // 如果没有服务代码但有服务类型和端点，直接设置
  else if (serviceType && endpoint) {
    console.log('根据服务类型和端点设置:', { serviceType, endpoint })
    requestConfig.serviceType = serviceType
    requestConfig.endpoint = endpoint
    
    // 查找对应的服务信息
    const foundService = services.value.find(s => s.endpoint === endpoint || s.type === serviceType)
    if (foundService) {
      console.log('找到匹配的服务:', foundService)
      currentService.value = foundService
    }
    
    // 触发端点变更，加载示例
    onEndpointChange()
  }
  // 只有服务类型的情况
  else if (serviceType) {
    console.log('根据服务类型设置:', serviceType)
    const foundService = serviceTypeOptions.value.find(opt => opt.value === serviceType)
    if (foundService) {
      requestConfig.serviceType = foundService.value
      onServiceTypeChange()
    }
  }
  
  console.log('API测试页面初始化完成，当前配置:', {
    serviceType: requestConfig.serviceType,
    endpoint: requestConfig.endpoint,
    currentService: currentService.value
  })
})

onBeforeUnmount(() => {
  closeSSEConnection(false) // 组件卸载时不重连
  if (sseRetryTimer) {
    clearTimeout(sseRetryTimer)
    sseRetryTimer = null
  }
  if (sseTimeoutTimer) {
    clearTimeout(sseTimeoutTimer)
    sseTimeoutTimer = null
  }
})
</script>

<style scoped>
.api-test-page {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 卡片样式 */
.request-card,
.response-card,
.history-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 请求配置区域 */
.request-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #EBEEF5;
}

.request-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.request-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
}

/* 头部和参数列表 */
.headers-list,
.params-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-item,
.param-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 文件上传区域 */
.file-upload-section {
  padding: 16px;
  border: 2px dashed #DCDFE6;
  border-radius: 8px;
  background-color: #FAFAFA;
}

/* JSON编辑器 */
.json-editor {
  position: relative;
}

.json-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

/* 响应区域 */
.response-status {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #F8F9FA;
  border-radius: 8px;
}

.status-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.response-time {
  font-size: 12px;
  color: #909399;
}

.response-size {
  font-size: 12px;
  color: #909399;
}

.task-progress-section {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #F8F9FA;
  border-radius: 8px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.response-section {
  margin-bottom: 16px;
}

.response-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.response-headers {
  background-color: #F8F9FA;
  border-radius: 8px;
  padding: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.header-row {
  display: flex;
  margin-bottom: 4px;
  font-size: 12px;
}

.header-key {
  font-weight: 600;
  color: #606266;
  min-width: 120px;
}

.header-value {
  color: #303133;
  word-break: break-all;
}

.response-body {
  border-radius: 8px;
  overflow: hidden;
}

.response-body.pretty pre {
  background-color: #F8F9FA;
  padding: 16px;
  margin: 0;
  font-size: 12px;
  line-height: 1.5;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.response-body.preview {
  text-align: center;
  padding: 16px;
  background-color: #F8F9FA;
}

.empty-response {
  padding: 40px 0;
  text-align: center;
}

/* 历史记录 */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #F8F9FA;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.history-item:hover {
  background-color: #E6F7FF;
}

.history-info {
  flex: 1;
}

.history-method {
  font-size: 12px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 4px;
}

.history-url {
  font-size: 13px;
  color: #303133;
  margin-bottom: 4px;
}

.history-time {
  font-size: 11px;
  color: #909399;
}

.empty-history {
  padding: 40px 0;
  text-align: center;
}

.response-actions {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .api-test-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .header-item,
  .param-item {
    flex-direction: column;
    align-items: stretch;
  }

  .status-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* 任务状态部分 */
.task-status-section {
  margin-bottom: 16px;
}

.task-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.job-id {
  font-size: 12px;
  font-weight: 600;
  color: #303133;
}

.status-wrapper {
  flex: 1;
  margin: 0 16px;
}

.status-label {
  font-size: 12px;
  color: #303133;
}

.task-actions {
  display: flex;
  gap: 8px;
}

/* SSE消息面板样式 */
.sse-message-panel {
  margin-top: 16px;
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  padding: 16px;
  background-color: #F8F9FA;
}

.sse-status {
  display: flex;
  align-items: center;
}

.sse-messages {
  margin-top: 12px;
  max-height: 200px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 8px;
}

.empty-messages {
  text-align: center;
  color: #909399;
  padding: 20px 0;
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.message-item {
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: flex-start;
}

.message-time {
  color: #909399;
  margin-right: 8px;
  font-weight: 600;
  min-width: 60px;
}

.message-content {
  flex: 1;
  word-break: break-word;
}

.message-info {
  background-color: #F4F4F5;
  color: #606266;
}

.message-success {
  background-color: #F0F9EB;
  color: #67C23A;
}

.message-error {
  background-color: #FEF0F0;
  color: #F56C6C;
}

.sse-hint {
  margin-top: 8px;
}

/* 调试信息样式 */
.debug-info {
  margin: 16px 0;
  padding: 16px;
  background-color: #f8f9fa;
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

.debug-info h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
}

.debug-info p {
  margin: 8px 0;
  font-size: 14px;
}

.debug-info pre {
  margin: 12px 0 0;
  padding: 12px;
  background-color: #f1f1f1;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

/* 模式设置样式 */
.mode-setting {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-icon {
  color: #909399;
  cursor: pointer;
  font-size: 16px;
}

.info-icon:hover {
  color: #409EFF;
}

/* 表单提示文字 */
.form-item-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.service-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.service-type {
  color: #409EFF;
  font-size: 11px;
  background: rgba(64, 158, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.service-description {
  color: #606266;
  font-size: 11px;
  line-height: 1.3;
}

.endpoint-info {
  margin-top: 8px;
}

/* 地址提取结果样式 */
.address-extraction-result {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.address-card {
  margin-bottom: 16px;
}

.address-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.address-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.address-item {
  display: flex;
  margin-bottom: 4px;
}

.address-item .label {
  font-weight: 600;
  width: 80px;
  color: #606266;
}

.address-item .value {
  flex: 1;
  word-break: break-all;
}
</style>