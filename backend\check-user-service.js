const mysql = require('mysql2/promise');

async function checkUserService() {
  const connection = await mysql.createConnection({
    host: 'localhost', 
    user: 'root', 
    password: '123456', 
    database: 'openapidb'
  });
  
  try {
    console.log('=== 检查用户4的服务次数 ===');
    
    const [userServiceData] = await connection.execute(
      'SELECT * FROM user_service WHERE user_id = ? ORDER BY service_id', 
      [4]
    );
    
    console.log('用户4的服务记录:');
    userServiceData.forEach(record => {
      console.log(`服务ID: ${record.service_id}, 总次数: ${record.total_count}, 已用: ${record.used_count}, 剩余: ${record.remaining_count}, 启用: ${record.enabled}`);
    });
    
    // 检查地址解析服务（service_id = 2）
    const addressService = userServiceData.find(s => s.service_id === 2);
    if (addressService) {
      console.log('\n=== 地址解析服务详情 ===');
      console.log('服务ID:', addressService.service_id);
      console.log('总次数:', addressService.total_count);
      console.log('已用次数:', addressService.used_count);
      console.log('剩余次数:', addressService.remaining_count);
      console.log('是否启用:', addressService.enabled);
      console.log('免费次数:', addressService.free_count);
      console.log('购买次数:', addressService.purchased_count);
      
      if (addressService.remaining_count <= 0) {
        console.log('\n❌ 地址解析服务次数不足！');
        console.log('需要重置次数或充值');
      } else {
        console.log('\n✅ 地址解析服务次数充足');
      }
    } else {
      console.log('\n❌ 用户4没有地址解析服务记录');
    }
    
  } catch (error) {
    console.error('查询失败:', error.message);
  } finally {
    await connection.end();
  }
}

checkUserService();
