<template>
  <div class="package-detail-page" v-loading="packageStore.loading">
    <div v-if="packageDetail" class="container">
      <!-- 面包屑导航 -->
      <el-breadcrumb class="breadcrumb" separator=">">
        <el-breadcrumb-item :to="{ path: '/packages' }">套餐包</el-breadcrumb-item>
        <el-breadcrumb-item>{{ packageDetail.name }}</el-breadcrumb-item>
      </el-breadcrumb>

      <!-- 套餐包头部信息 -->
      <div class="package-header">
        <div class="package-main-info">
          <div class="package-icon">
            <el-icon :size="64">
              <component :is="getPackageIcon(packageDetail.type)" />
            </el-icon>
          </div>
          
          <div class="package-meta">
            <div class="package-title-row">
              <h1 class="package-title">{{ packageDetail.name }}</h1>
              <el-tag :type="getStatusType(packageDetail.status)" size="large">
                {{ getStatusLabel(packageDetail.status) }}
              </el-tag>
            </div>
            
            <p class="package-subtitle">{{ packageDetail.description }}</p>
            
            <div class="package-stats">
              <div class="stat-item">
                <el-icon><Star /></el-icon>
                <span>4.8 分</span>
              </div>
              <div class="stat-item">
                <el-icon><ShoppingBag /></el-icon>
                <span>{{ packageDetail.salesCount }} 人购买</span>
              </div>
              <div class="stat-item">
                <el-icon><Clock /></el-icon>
                <span>有效期 {{ packageDetail.duration }} 天</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 价格和购买区域 -->
        <div class="package-purchase">
          <div class="price-section">
            <div class="price-main">
              <span class="current-price">¥{{ (packageDetail.price / 100).toFixed(2) }}</span>
              <span v-if="packageDetail.originalPrice" class="original-price">
                ¥{{ (packageDetail.originalPrice / 100).toFixed(2) }}
              </span>
            </div>
            <div class="price-billing">
              <span class="billing-type">{{ getBillingTypeLabel(packageDetail.billingType) }}</span>
              <span v-if="packageDetail.originalPrice && packageDetail.originalPrice > packageDetail.price" class="discount-badge">
                {{ Math.round((packageDetail.price / packageDetail.originalPrice) * 10) }}折优惠
              </span>
            </div>
          </div>
          
          <div class="purchase-actions">
            <el-button
              type="primary"
              size="large"
              :disabled="packageDetail.status !== 'active'"
              @click="handlePurchase"
              class="purchase-btn"
            >
              <el-icon><ShoppingCart /></el-icon>
              立即购买
            </el-button>
            
            <el-button size="large" @click="handleAddToCart" class="cart-btn">
              <el-icon><Plus /></el-icon>
              加入购物车
            </el-button>
          </div>
          
          <div class="purchase-tips">
            <div class="tip-item">
              <el-icon><Shield /></el-icon>
              <span>7天无理由退款</span>
            </div>
            <div class="tip-item">
              <el-icon><Headset /></el-icon>
              <span>24小时客服支持</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 详情标签页 -->
      <div class="package-details">
        <el-tabs v-model="activeTab" class="detail-tabs">
          <!-- 功能特性 -->
          <el-tab-pane label="功能特性" name="features">
            <div class="features-section">
              <div class="features-grid">
                <div v-for="feature in packageDetail.features" :key="feature.name" class="feature-card">
                  <div class="feature-header">
                    <el-icon class="feature-icon" :class="{ included: feature.included }">
                      <Check v-if="feature.included" />
                      <Close v-else />
                    </el-icon>
                    <h3 class="feature-name">{{ feature.name }}</h3>
                  </div>
                  
                  <p class="feature-description">{{ feature.description }}</p>
                  
                  <div v-if="feature.limit" class="feature-limit">
                    <span v-if="feature.limit === -1" class="unlimited">不限量使用</span>
                    <span v-else class="limited">限制：{{ feature.limit }}{{ feature.unit }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 服务配额 -->
          <el-tab-pane label="服务配额" name="quotas">
            <div class="quotas-section">
              <div class="quotas-table">
                <el-table :data="packageDetail.quotas" style="width: 100%">
                  <el-table-column prop="serviceName" label="服务名称" width="200">
                    <template #default="{ row }">
                      <div class="service-info">
                        <el-icon><Box /></el-icon>
                        <span>{{ row.serviceName }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="description" label="服务描述" />
                  
                  <el-table-column prop="limit" label="配额限制" width="150">
                    <template #default="{ row }">
                      <el-tag v-if="row.unlimited" type="success">不限量</el-tag>
                      <span v-else>{{ row.limit }} 次/月</span>
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="price" label="超额价格" width="120">
                    <template #default="{ row }">
                      <span v-if="row.overagePrice">
                        ¥{{ (row.overagePrice / 100).toFixed(2) }}/次
                      </span>
                      <span v-else>-</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 使用说明 -->
          <el-tab-pane label="使用说明" name="usage">
            <div class="usage-section">
              <div class="usage-steps">
                <div class="step-item" v-for="(step, index) in usageSteps" :key="index">
                  <div class="step-number">{{ index + 1 }}</div>
                  <div class="step-content">
                    <h3 class="step-title">{{ step.title }}</h3>
                    <p class="step-description">{{ step.description }}</p>
                  </div>
                </div>
              </div>
              
              <div class="usage-notes">
                <h3>注意事项</h3>
                <ul>
                  <li>套餐包购买后立即生效，有效期从购买日开始计算</li>
                  <li>套餐包内的服务配额在有效期内可以累计使用</li>
                  <li>超出配额的使用将按照标准价格计费</li>
                  <li>套餐包到期后未使用的配额将自动失效</li>
                  <li>支持在有效期内升级到更高级别的套餐包</li>
                </ul>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 用户评价 -->
          <el-tab-pane label="用户评价" name="reviews">
            <div class="reviews-section">
              <div class="reviews-summary">
                <div class="rating-overview">
                    <div class="rating-score">
                      <span class="score">4.8</span>
                      <el-rate :model-value="4.8" disabled show-score text-color="#ff9900" />
                  </div>
                  <div class="rating-stats">
                    <div class="rating-bar" v-for="i in 5" :key="i">
                      <span class="star-count">{{ 6 - i }}星</span>
                      <div class="bar">
                        <div class="bar-fill" :style="{ width: '60%' }"></div>
                      </div>
                      <span class="count">120</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="reviews-list">
                <div class="review-item" v-for="review in mockReviews" :key="review.id">
                  <div class="review-header">
                    <div class="user-info">
                      <el-avatar :size="40" :src="review.avatar" />
                      <div class="user-meta">
                        <span class="username">{{ review.username }}</span>
                        <span class="review-date">{{ review.date }}</span>
                      </div>
                    </div>
                    <el-rate v-model="review.rating" disabled size="small" />
                  </div>
                  
                  <div class="review-content">
                    <p>{{ review.content }}</p>
                  </div>
                  
                  <div class="review-actions">
                    <el-button text size="small">
                      <el-icon><ChatDotRound /></el-icon>
                      回复
                    </el-button>
                    <el-button text size="small">
                      <el-icon><Star /></el-icon>
                      有用 ({{ review.helpful }})
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 相关推荐 -->
      <div class="related-packages">
        <h2 class="section-title">相关推荐</h2>
        <div class="packages-grid">
          <div 
            v-for="pkg in relatedPackages" 
            :key="pkg.id"
            class="package-card"
            @click="viewPackage(pkg.id)"
          >
            <PackageCard :package="pkg" :show-actions="true" @purchase="handleRelatedPurchase" />
          </div>
        </div>
      </div>
    </div>

    <!-- 购买对话框 -->
    <el-dialog
      v-model="showPurchaseDialog"
      title="购买套餐包"
      width="500px"
      :close-on-click-modal="false"
    >
      <PurchaseForm
        v-if="packageDetail"
        :package="packageDetail"
        @success="handlePurchaseSuccess"
        @cancel="showPurchaseDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Star,
  ShoppingBag,
  Clock,
  ShoppingCart,
  Plus,
  Headset,
  Check,
  Close,
  Box,
  ChatDotRound,
  Trophy
} from '@element-plus/icons-vue'
import { usePackageStore } from '@/stores/package'
import PackageCard from '@/components/package/PackageCard.vue'
import PurchaseForm from '@/components/package/PurchaseForm.vue'
import type { Package } from '@/types/package'

const route = useRoute()
const router = useRouter()
const packageStore = usePackageStore()

// 响应式数据
const activeTab = ref('features')
const showPurchaseDialog = ref(false)

// 计算属性
const packageDetail = computed(() => packageStore.currentPackage)
const relatedPackages = computed(() => packageStore.packages.slice(0, 3))

// 模拟数据
const usageSteps = [
  {
    title: '购买套餐包',
    description: '选择适合的套餐包并完成支付，套餐包将立即生效'
  },
  {
    title: '获取API密钥',
    description: '在控制台中创建或使用现有的API密钥来调用服务'
  },
  {
    title: '调用服务',
    description: '使用API密钥调用套餐包中包含的各种服务'
  },
  {
    title: '监控使用量',
    description: '在控制台中查看服务使用情况和剩余配额'
  }
]

const mockReviews = [
  {
    id: '1',
    username: '用户***123',
    avatar: '',
    rating: 5,
    date: '2024-01-15',
    content: '套餐包性价比很高，服务稳定，客服响应及时，推荐购买！',
    helpful: 12
  },
  {
    id: '2',
    username: '开发***456',
    avatar: '',
    rating: 4,
    date: '2024-01-10',
    content: 'API调用速度快，文档清晰，就是价格稍微有点贵。',
    helpful: 8
  }
]

// 方法
const getPackageIcon = (type: string) => {
  const icons: Record<string, any> = {
    basic: Box,
    standard: Star,
    premium: Star,
    enterprise: Trophy
  }
  return icons[type] || Box
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    active: 'success',
    inactive: 'info',
    deprecated: 'warning',
    discontinued: 'danger'
  }
  return types[status] || 'info'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    active: '可用',
    inactive: '暂停',
    deprecated: '即将下线',
    discontinued: '已下线'
  }
  return labels[status] || status
}

const getBillingTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    one_time: '一次性',
    monthly: '/ 月',
    yearly: '/ 年',
    usage_based: '按量付费'
  }
  return labels[type] || type
}

const handlePurchase = () => {
  showPurchaseDialog.value = true
}

const handleAddToCart = () => {
  ElMessage.success('已加入购物车')
}

const handlePurchaseSuccess = () => {
  showPurchaseDialog.value = false
  ElMessage.success('购买成功！')
}

const handleRelatedPurchase = (pkg: Package) => {
  router.push(`/packages/${pkg.id}`)
}

const viewPackage = (packageId: string) => {
  router.push(`/packages/${packageId}`)
}

// 生命周期
onMounted(async () => {
  const packageId = route.params.id as string
  if (packageId) {
    await packageStore.fetchPackageDetail(packageId)
    await packageStore.fetchPackages({ pageSize: 3 })
  }
})
</script>

<style scoped>
.package-detail-page {
  min-height: 100vh;
  background: #f8fafc;
  padding: 24px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.breadcrumb {
  margin-bottom: 24px;
}

.package-header {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 40px;
  align-items: start;
}

.package-main-info {
  display: flex;
  gap: 24px;
}

.package-icon {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.package-meta {
  flex: 1;
}

.package-title-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.package-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0;
  color: #1f2937;
}

.package-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0 0 20px 0;
  line-height: 1.6;
}

.package-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #6b7280;
}

.package-purchase {
  background: #f8fafc;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e5e7eb;
}

.price-section {
  margin-bottom: 24px;
}

.price-main {
  display: flex;
  align-items: baseline;
  gap: 12px;
  margin-bottom: 8px;
}

.current-price {
  font-size: 36px;
  font-weight: 700;
  color: #3b82f6;
}

.original-price {
  font-size: 20px;
  color: #9ca3af;
  text-decoration: line-through;
}

.price-billing {
  display: flex;
  align-items: center;
  gap: 12px;
}

.billing-type {
  font-size: 14px;
  color: #6b7280;
}

.discount-badge {
  background: #fef3c7;
  color: #d97706;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
}

.purchase-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.purchase-btn,
.cart-btn {
  width: 100%;
  height: 48px;
  font-weight: 600;
}

.purchase-tips {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
  color: #6b7280;
}

.package-details {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.detail-tabs {
  --el-tabs-header-height: 48px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.feature-card {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  background: #fafbfc;
}

.feature-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.feature-icon {
  width: 20px;
  height: 20px;
  color: #ef4444;
}

.feature-icon.included {
  color: #10b981;
}

.feature-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #1f2937;
}

.feature-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.feature-limit {
  font-size: 13px;
}

.unlimited {
  color: #10b981;
  font-weight: 600;
}

.limited {
  color: #f59e0b;
}

.quotas-table {
  margin-top: 16px;
}

.service-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.usage-steps {
  margin-bottom: 32px;
}

.step-item {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1f2937;
}

.step-description {
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.usage-notes h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: #1f2937;
}

.usage-notes ul {
  margin: 0;
  padding-left: 20px;
}

.usage-notes li {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 8px;
}

.reviews-summary {
  margin-bottom: 32px;
}

.rating-overview {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 32px;
  align-items: center;
}

.rating-score {
  text-align: center;
}

.score {
  font-size: 48px;
  font-weight: 700;
  color: #f59e0b;
  display: block;
  margin-bottom: 8px;
}

.rating-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rating-bar {
  display: grid;
  grid-template-columns: 40px 1fr 40px;
  gap: 12px;
  align-items: center;
  font-size: 13px;
}

.bar {
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: #f59e0b;
  transition: width 0.3s ease;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.review-item {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.username {
  font-weight: 600;
  color: #1f2937;
}

.review-date {
  font-size: 12px;
  color: #9ca3af;
}

.review-content p {
  margin: 0 0 16px 0;
  color: #374151;
  line-height: 1.6;
}

.review-actions {
  display: flex;
  gap: 16px;
}

.related-packages {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 24px 0;
  color: #1f2937;
}

.packages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.package-card {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.package-card:hover {
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .package-header {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .package-main-info {
    flex-direction: column;
    text-align: center;
  }
  
  .package-stats {
    justify-content: center;
  }
  
  .rating-overview {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}
</style>