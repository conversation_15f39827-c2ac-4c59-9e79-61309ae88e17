import { MiddlewareConsumer, Module, OnModuleInit, RequestMethod, Logger } from '@nestjs/common';
import { RouterModule } from '@nestjs/core';
import { OPEN_PREFIX } from '../common/constant';

// 新的模块结构
import { ServiceModule } from './service/service.module';
import { UserModule } from './user/user.module';
import { AuthModule } from './auth/auth.module';
import { UserServiceModule } from './user-service/user-service.module';
import { ScheduleModule } from './schedule/schedule.module';
import { ApiKeyModule } from './api-key/api-key.module';
import { QueueModule } from './queue/queue.module';
import { CallRecordModule } from './call-record/call-record.module';
import { GatewayModule } from './gateway/gateway.module';
import { OrderModule } from './order/order.module';
import { PaymentModule } from './payment/payment.module';

/**
 * open模块
 */
@Module({
  imports: [
    RouterModule.register([
      // 对外开放的API服务（使用 op 前缀）
      {
        path: OPEN_PREFIX,
        module: GatewayModule,
      },
      // 内部管理API（不使用额外前缀，直接在v1下）
      // AuthModule, UserModule等将直接注册到v1路径下
    ]),
    AuthModule,
    UserModule,
    ServiceModule,
    UserServiceModule,
    ScheduleModule,
    ApiKeyModule,
    QueueModule,
    CallRecordModule,
    GatewayModule,
    OrderModule,
    PaymentModule,
  ],
  providers: [
    // AuthEventListener
  ],
  exports: [
    UserModule,
    AuthModule,
    ApiKeyModule,
    QueueModule,
    GatewayModule,
    OrderModule,
    PaymentModule,
  ],
})

export class OpenModule {}
