# 模块协作流程需求文档

## 1. 文档概述

本文档描述了开放平台核心模块间的协作关系和流程，旨在明确各模块职责边界和交互方式。开放平台由密钥模块、网关模块、队列模块、业务接口调用管理模块、支付充值模块和订单模块等组成，这些模块相互协作，共同支撑平台的核心业务流程。本文档遵循单一职责原则和最佳实践，为开发团队提供清晰的模块协作指南。

## 2. 核心模块职责

### 2.1 密钥模块

- 负责API密钥的创建、更新、查询和删除
- 提供密钥验证功能
- 维护密钥缓存和状态

### 2.2 网关模块

- 接收和路由API请求
- 处理同步和异步请求
- 支持两种异步模式：客户端自管理和网关代理

### 2.3 队列模块

- 管理异步任务队列
- 处理任务调度和执行
- 提供任务状态查询和SSE推送

### 2.4 业务接口调用管理模块

- 管理API调用次数
- 记录调用历史和状态
- 处理次数扣减和增加

### 2.5 支付充值模块

- 处理用户充值请求
- 集成支付渠道
- 处理支付回调和结果

### 2.6 订单模块

- 创建和管理订单
- 维护订单状态和历史
- 提供订单查询和统计

## 3. 关键业务流程

### 3.1 API调用流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as 网关模块
    participant KeyModule as 密钥模块
    participant CallMgmt as 调用管理模块
    participant Queue as 队列模块
    participant Service as 业务服务

    Client->>Gateway: 发送API请求(带密钥)
    Gateway->>KeyModule: 验证密钥
    KeyModule-->>Gateway: 返回验证结果
    
    alt 密钥无效
        Gateway-->>Client: 返回401错误
    else 密钥有效
        Gateway->>CallMgmt: 检查调用次数
        
        alt 次数不足
            CallMgmt-->>Gateway: 返回次数不足
            Gateway-->>Client: 返回402错误
        else 次数充足
            Gateway->>Gateway: 确定处理模式
            
            alt 同步处理
                Gateway->>Service: 转发请求
                Service-->>Gateway: 返回结果
                Gateway->>CallMgmt: 记录调用并扣减次数
                Gateway-->>Client: 返回结果
                
            else 异步处理(客户端自管理)
                Gateway->>Queue: 添加任务到队列
                Queue-->>Gateway: 返回任务ID
                Gateway->>CallMgmt: 记录调用(不扣减次数)
                Gateway-->>Client: 返回任务ID和SSE连接URL
                Client->>Queue: 建立SSE连接
                Queue->>Service: 处理任务
                Service-->>Queue: 返回结果
                Queue->>CallMgmt: 记录结果并扣减次数
                Queue-->>Client: 推送结果
                
            else 异步处理(网关代理)
                Gateway->>Queue: 添加任务到队列
                Queue-->>Gateway: 返回任务ID
                Gateway->>Queue: 订阅任务结果
                Queue->>Service: 处理任务
                Service-->>Queue: 返回结果
                Queue-->>Gateway: 推送结果
                Gateway->>CallMgmt: 记录调用并扣减次数
                Gateway-->>Client: 返回结果和任务ID
            end
        end
    end
```

### 3.2 服务次数购买流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Order as 订单模块
    participant Payment as 支付充值模块
    participant CallMgmt as 调用管理模块
    participant PayProvider as 支付服务商

    Client->>Order: 创建购买次数订单
    Order->>Order: 生成订单号
    Order-->>Client: 返回订单信息
    
    Client->>Payment: 发起支付请求
    Payment->>Order: 获取订单信息
    Order-->>Payment: 返回订单详情
    
    alt 余额支付
        Payment->>CallMgmt: 检查账户余额
        
        alt 余额不足
            CallMgmt-->>Payment: 返回余额不足
            Payment-->>Client: 返回支付失败
        else 余额充足
            Payment->>CallMgmt: 扣减账户余额
            Payment->>Order: 更新订单状态为已支付
            Payment->>CallMgmt: 增加服务调用次数
            Payment-->>Client: 返回支付成功
        end
        
    else 第三方支付
        Payment->>PayProvider: 创建支付订单
        PayProvider-->>Payment: 返回支付链接/表单
        Payment-->>Client: 返回支付链接/表单
        
        Client->>PayProvider: 完成支付
        PayProvider->>Payment: 发送支付结果回调
        
        alt 支付成功
            Payment->>Order: 更新订单状态为已支付
            Payment->>CallMgmt: 增加服务调用次数
            Payment-->>PayProvider: 确认接收回调
            Payment-->>Client: 通知支付成功
        else 支付失败
            Payment->>Order: 更新订单状态为支付失败
            Payment-->>PayProvider: 确认接收回调
            Payment-->>Client: 通知支付失败
        end
    end
```

### 3.3 账户充值流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Order as 订单模块
    participant Payment as 支付充值模块
    participant PayProvider as 支付服务商

    Client->>Order: 创建充值订单
    Order->>Order: 生成订单号
    Order-->>Client: 返回订单信息
    
    Client->>Payment: 发起支付请求
    Payment->>Order: 获取订单信息
    Order-->>Payment: 返回订单详情
    
    Payment->>PayProvider: 创建支付订单
    PayProvider-->>Payment: 返回支付链接/表单
    Payment-->>Client: 返回支付链接/表单
    
    Client->>PayProvider: 完成支付
    PayProvider->>Payment: 发送支付结果回调
    
    alt 支付成功
        Payment->>Order: 更新订单状态为已支付
        Payment->>Payment: 增加账户余额
        Payment-->>PayProvider: 确认接收回调
        Payment-->>Client: 通知充值成功
    else 支付失败
        Payment->>Order: 更新订单状态为支付失败
        Payment-->>PayProvider: 确认接收回调
        Payment-->>Client: 通知充值失败
    end
```

## 4. 模块间接口定义

### 4.1 密钥模块对外接口

- **验证密钥**
  ```typescript
  interface IKeyValidationResult {
    valid: boolean;
    userId?: number;
    scopes?: string[];
    error?: string;
  }
  
  async validateApiKey(apiKey: string, secretKey: string): Promise<IKeyValidationResult>
  ```

### 4.2 网关模块对外接口

- **处理API请求**
  ```typescript
  interface IApiResponse {
    success: boolean;
    data?: any;
    error?: string;
    jobId?: string;
    eventsUrl?: string;
  }
  
  async handleApiRequest(
    path: string, 
    method: string, 
    headers: Record<string, string>, 
    body: any
  ): Promise<IApiResponse>
  ```

### 4.3 队列模块对外接口

- **添加任务**
  ```typescript
  interface ITaskResult {
    taskId: string;
    status: 'queued' | 'processing' | 'completed' | 'failed';
    eventsUrl: string;
  }
  
  async addTask(
    taskType: string,
    data: any,
    options?: { priority?: number; delay?: number }
  ): Promise<ITaskResult>
  ```

- **获取任务状态**
  ```typescript
  interface ITaskStatus {
    taskId: string;
    status: 'queued' | 'processing' | 'completed' | 'failed';
    progress?: number;
    result?: any;
    error?: string;
  }
  
  async getTaskStatus(taskId: string): Promise<ITaskStatus>
  ```

### 4.4 调用管理模块对外接口

- **检查调用次数**
  ```typescript
  interface IQuotaCheckResult {
    sufficient: boolean;
    remainingCount: number;
    totalCount: number;
    usedCount: number;
  }
  
  async checkServiceQuota(
    userId: number, 
    serviceId: number
  ): Promise<IQuotaCheckResult>
  ```

- **扣减调用次数**
  ```typescript
  async deductServiceQuota(
    userId: number,
    serviceId: number,
    count: number = 1
  ): Promise<boolean>
  ```

- **记录调用**
  ```typescript
  async recordServiceCall(
    userId: number,
    serviceId: number,
    status: 'success' | 'failed',
    details: {
      requestId: string;
      duration: number;
      failReason?: string;
      requestData?: any;
      responseData?: any;
    }
  ): Promise<void>
  ```

### 4.5 支付充值模块对外接口

- **创建支付**
  ```typescript
  interface IPaymentResult {
    paymentId: string;
    orderId: string;
    paymentUrl?: string;
    formData?: any;
    qrCodeUrl?: string;
    status?: string;
    message?: string;
  }
  
  async createPayment(
    userId: number,
    type: 'recharge' | 'purchase',
    amount: number,
    method: 'alipay' | 'wechat' | 'balance',
    metadata?: {
      serviceId?: number;
      count?: number;
    }
  ): Promise<IPaymentResult>
  ```

### 4.6 订单模块对外接口

- **创建订单**
  ```typescript
  interface IOrderResult {
    id: string;
    orderNo: string;
    status: string;
    amount: number;
    description: string;
    createdAt: Date;
  }
  
  async createOrder(
    userId: number,
    type: 'recharge' | 'purchase',
    amount: number,
    description: string,
    metadata?: any
  ): Promise<IOrderResult>
  ```

- **更新订单状态**
  ```typescript
  async updateOrderStatus(
    orderId: string,
    status: 'pending_payment' | 'paid' | 'completed' | 'cancelled' | 'payment_failed',
    additionalData?: {
      paymentId?: string;
      reason?: string;
    }
  ): Promise<void>
  ```

## 5. 事件通信

### 5.1 核心事件定义

```typescript
// 订单事件
interface OrderCreatedEvent {
  orderId: string;
  userId: number;
  type: 'recharge' | 'purchase';
  amount: number;
}

interface OrderStatusChangedEvent {
  orderId: string;
  userId: number;
  status: string;
  previousStatus: string;
  reason?: string;
}

// 支付事件
interface PaymentCompletedEvent {
  paymentId: string;
  userId: number;
  orderId: string;
  status: 'completed' | 'failed';
  amount: number;
  method: string;
}

// 调用次数事件
interface ServiceQuotaUpdatedEvent {
  userId: number;
  serviceId: number;
  totalCount: number;
  usedCount: number;
  remainingCount: number;
  operation: 'increment' | 'deduct';
  amount: number;
}

interface ServiceQuotaWarningEvent {
  userId: number;
  serviceId: number;
  remainingCount: number;
  warningThreshold: number;
}

// 任务事件
interface TaskStatusChangedEvent {
  taskId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress?: number;
  result?: any;
  error?: string;
}
```

### 5.2 事件订阅关系

| 事件                    | 发布者          | 订阅者                                |
|------------------------|----------------|--------------------------------------|
| OrderCreatedEvent      | 订单模块        | 支付充值模块                           |
| OrderStatusChangedEvent| 订单模块        | 支付充值模块, 用户通知服务               |
| PaymentCompletedEvent  | 支付充值模块     | 订单模块, 调用管理模块, 用户通知服务      |
| ServiceQuotaUpdatedEvent| 调用管理模块    | 用户通知服务, 监控服务                  |
| ServiceQuotaWarningEvent| 调用管理模块    | 用户通知服务, 监控服务                  |
| TaskStatusChangedEvent | 队列模块        | 网关模块, 用户通知服务                  |

## 6. 错误处理和恢复策略

### 6.1 跨模块事务

对于涉及多个模块的操作，采用最终一致性模式，使用事件驱动架构确保数据一致性：

1. **支付成功后增加调用次数**
   - 支付模块发布支付成功事件
   - 调用管理模块订阅事件并增加次数
   - 使用事件存储确保消息不丢失
   - 实现幂等处理避免重复增加

2. **调用次数扣减失败处理**
   - 使用补偿事务模式
   - 记录失败操作到专门的错误队列
   - 定时任务重试失败的操作
   - 达到最大重试次数后通知管理员

### 6.2 模块间超时处理

1. **网关调用其他模块超时**
   - 设置合理的超时时间（通常<500ms）
   - 超时时返回特定错误码
   - 记录超时事件到监控系统
   - 触发熔断机制防止雪崩效应

2. **异步任务处理超时**
   - 设置任务最大执行时间
   - 超时自动取消或标记失败
   - 发送超时通知给用户
   - 记录超时原因便于分析

### 6.3 定时任务失败处理机制

1. **自动重试策略**
   - 任务失败立即记录失败状态和原因
   - 自动重试机制：首次失败后立即重试1次，然后按指数退避策略（5分钟、15分钟、1小时）再重试2次，总计最多重试3次
   - 重试前检查前提条件，确保系统状态适合重试
   - 使用分布式锁避免多实例同时重试造成冲突

2. **失败通知与手动干预**
   - 3次重试全部失败后，发送告警至运维团队
   - 提供管理界面手动触发重试
   - 记录完整失败上下文，便于故障分析
   - 重要任务失败可触发短信/邮件告警

3. **部分成功处理**
   - 支持任务拆分和部分提交
   - 记录已成功处理的部分，避免重复处理
   - 仅重试失败部分

### 6.4 用户状态变更机制

1. **用户状态落库优先**
   - 用户状态变更先保证数据库记录更新
   - 数据一致性优先于通知分发
   - 使用数据库事务保证状态更新的原子性

2. **实名认证与服务次数奖励**
   - 实名认证状态更新到用户表后，通过直接调用user-service模块接口发放次数奖励
   - 具体接口：`userServiceService.addServiceCount(userId, serviceId, rewardCount)`
   - 使用同步调用确保实时到账，避免异步事件可能的延迟
   - 操作记录审计日志，包括认证通过时间、奖励时间、奖励次数

3. **事务一致性保证**
   - 实名认证状态更新和奖励发放使用事务包装
   - 奖励发放失败不回滚认证状态，而是记录到重试队列

## 7. 部署和扩展考虑

### 7.1 模块部署独立性

- 各模块可独立部署和扩展
- 使用API网关统一入口
- 服务发现确保模块间通信
- 负载均衡提高可用性

### 7.2 扩展策略

- **水平扩展**：增加模块实例数量
- **功能扩展**：在现有模块架构上增加新功能
- **新模块集成**：遵循既定接口规范开发新模块

## 8. 后续优化方向

- **服务网格**：引入服务网格提升模块间通信能力
- **分布式追踪**：实现请求全链路追踪
- **自适应扩缩容**：基于负载自动调整模块实例数
- **API版本控制**：模块间接口的版本管理策略
- **统一监控**：跨模块的统一监控和告警系统 

## 9. 业务流程与模块协作可视化补充

### 9.1 用户全链路旅程

```mermaid
graph TD
    Start([开始]) --> Register[用户注册]
    Register --> EmailVerify[邮箱验证]
    EmailVerify --> CreateApiKey[创建API密钥]
    CreateApiKey --> ViewServices[浏览服务目录]
    ViewServices --> SelectService[选择服务]
    SelectService --> CheckBalance{检查余额}
    CheckBalance -->|余额不足| RechargeAccount[账户充值]
    RechargeAccount --> PayRecharge[支付充值]
    PayRecharge --> CheckBalance
    CheckBalance -->|余额充足| BuyService[购买服务次数]
    BuyService --> UseService[调用服务API]
    UseService --> CheckQuota{检查剩余次数}
    CheckQuota -->|次数不足| BuyService
    CheckQuota -->|次数充足| ProcessRequest[处理请求]
    ProcessRequest --> GetResult[获取结果]
    GetResult --> ViewRecords[查看调用记录]
    ViewRecords --> UseService
    style Start fill:#f9f,stroke:#333,stroke-width:2px
    style Register fill:#bbf,stroke:#333,stroke-width:1px
    style BuyService fill:#bfb,stroke:#333,stroke-width:1px
    style UseService fill:#fbf,stroke:#333,stroke-width:1px
    style RechargeAccount fill:#fbb,stroke:#333,stroke-width:1px
```

### 9.2 充值-购买-消费闭环

```mermaid
graph TD
    Start([用户]) --> Choose{选择操作}
    Choose -->|充值账户| CreateRechargeOrder[创建充值订单]
    Choose -->|直接购买服务次数| CreatePurchaseOrder[创建购买订单]
    CreateRechargeOrder --> SelectPayMethod1[选择支付方式]
    CreatePurchaseOrder --> SelectPayMethod2[选择支付方式]
    SelectPayMethod1 -->|第三方支付| ThirdPartyPay1[支付宝/微信支付]
    SelectPayMethod2 -->|第三方支付| ThirdPartyPay2[支付宝/微信支付]
    SelectPayMethod2 -->|余额支付| BalancePay[余额支付]
    ThirdPartyPay1 --> PaymentCallback1[支付回调]
    ThirdPartyPay2 --> PaymentCallback2[支付回调]
    PaymentCallback1 -->|支付成功| IncreaseBalance[增加账户余额]
    PaymentCallback2 -->|支付成功| UpdateOrder2[更新订单状态]
    BalancePay -->|余额充足| DeductBalance[扣减账户余额]
    BalancePay -->|余额不足| PaymentFailed[支付失败]
    DeductBalance --> UpdateOrder2
    UpdateOrder2 --> IncreaseServiceCount[增加服务调用次数]
    IncreaseBalance --> UserBalance[(用户余额)]
    UserBalance --> DeductBalance
    IncreaseServiceCount --> ServiceQuota[(服务次数)]
    ServiceQuota --> CallService[调用服务]
    CallService -->|调用成功| DeductServiceCount[扣减服务次数]
    CallService -->|调用失败| RecordFailure[记录失败]
    DeductServiceCount --> ServiceQuota
    DeductServiceCount --> RecordSuccess[记录成功调用]
    RecordSuccess --> CallRecords[(调用记录)]
    RecordFailure --> CallRecords
    style Start fill:#f9f,stroke:#333,stroke-width:2px
    style UserBalance fill:#bbf,stroke:#333,stroke-width:1px
    style ServiceQuota fill:#bfb,stroke:#333,stroke-width:1px
    style CallRecords fill:#fbf,stroke:#333,stroke-width:1px
```

### 9.3 API调用全链路与异步处理模式

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as 网关模块
    participant Auth as 认证模块
    participant CallMgmt as 调用管理模块
    participant Queue as 队列模块
    participant Service as 业务服务
    Client->>Gateway: 发送请求(mode=async/proxy-async)
    Gateway->>Auth: 验证API密钥
    Auth-->>Gateway: 返回验证结果
    Gateway->>CallMgmt: 检查调用次数
    CallMgmt-->>Gateway: 返回次数检查结果
    Gateway->>Queue: 创建任务并入队
    Queue-->>Gateway: 返回任务ID
    alt 客户端自管理
        Gateway->>CallMgmt: 记录请求(不扣减次数)
        Gateway-->>Client: 返回任务ID和SSE URL
        Client->>Queue: 建立SSE连接(taskId)
        Queue->>Service: 开始处理任务
        Service-->>Queue: 返回进度/结果
        Queue->>CallMgmt: 记录结果并扣减次数
        Queue-->>Client: 推送最终结果
    else 网关代理
        Gateway->>Gateway: 建立内部SSE连接(taskId)
        Queue->>Service: 开始处理任务
        Service-->>Queue: 返回进度/结果
        Queue-->>Gateway: 推送最终结果
        Gateway->>CallMgmt: 记录调用并扣减次数
        Gateway-->>Client: 返回结果和任务ID
    end
```

### 9.4 错误处理与定时清理

```mermaid
graph TD
    ErrorStart([错误发生]) --> Classify{错误类型}
    Classify -->|认证/权限| AuthError[认证错误处理]
    Classify -->|业务| BizError[业务错误处理]
    Classify -->|系统| SysError[系统错误处理]
    Classify -->|网络| NetError[网络错误处理]
    AuthError --> Return401[返回401/403/402]
    BizError --> Return400[返回400/404/409]
    SysError --> Return5xx[返回500/502/503/504]
    NetError --> RetryOrFail[重试或失败]
    Return401 --> LogError[记录错误日志]
    Return400 --> LogError
    Return5xx --> AlertAdmin[告警管理员]
    AlertAdmin --> LogError
    RetryOrFail -->|重试成功| Continue[继续处理]
    RetryOrFail -->|重试失败| Return5xx
    LogError --> ReturnToClient[返回错误响应]
    Continue --> NextStep[继续执行流程]
```

### 9.5 定时任务执行与重试流程

```mermaid
graph TD
    Start([定时任务触发]) --> Execute[执行任务]
    Execute --> CheckResult{检查结果}
    CheckResult -->|成功| RecordSuccess[记录成功结果]
    CheckResult -->|失败| Retry1[立即重试1次]
    Retry1 --> CheckRetry1{检查结果}
    CheckRetry1 -->|成功| RecordSuccess
    CheckRetry1 -->|失败| Wait5m[等待5分钟]
    Wait5m --> Retry2[重试2]
    Retry2 --> CheckRetry2{检查结果}
    CheckRetry2 -->|成功| RecordSuccess
    CheckRetry2 -->|失败| Wait15m[等待15分钟]
    Wait15m --> Retry3[重试3]
    Retry3 --> CheckRetry3{检查结果}
    CheckRetry3 -->|成功| RecordSuccess
    CheckRetry3 -->|失败| MarkFailed[标记为最终失败]
    MarkFailed --> SendAlert[发送告警通知]
    SendAlert --> ManualIntervention[等待手动干预]
    RecordSuccess --> Complete([任务完成])

    style Start fill:#f9f,stroke:#333,stroke-width:2px
    style Execute fill:#bbf,stroke:#333,stroke-width:1px
    style Retry1 fill:#ffb,stroke:#333,stroke-width:1px
    style Retry2 fill:#ffb,stroke:#333,stroke-width:1px
    style Retry3 fill:#ffb,stroke:#333,stroke-width:1px
    style MarkFailed fill:#fbb,stroke:#333,stroke-width:1px
    style RecordSuccess fill:#bfb,stroke:#333,stroke-width:1px
```

### 9.6 跨模块事件流与定时清理

```mermaid
graph TD
    PaymentCompleted[支付完成事件] --> CallMgmt[调用管理模块]
    CallMgmt -->|增加服务次数| UserQuota[用户服务配额]
    CallMgmt -->|触发预警| Notify[通知服务]
    CallMgmt -->|记录调用| CallRecord[调用记录表]
    CallRecord -->|定时清理| Schedule[定时任务模块]
    Schedule -->|每两月清理| CallRecord
    CallMgmt -->|统计分析| Monitor[监控服务]
    
    UserVerified[用户实名认证通过] --> UserStatusUpdate[用户状态更新]
    UserStatusUpdate -->|直接同步调用| UserServiceModule[用户服务关联模块]
    UserServiceModule -->|增加奖励次数| UserQuota
```

> 以上流程图可直接复制到Mermaid支持的编辑器中渲染。所有流程均已结合最佳实践、分布式一致性、异步与同步处理、定时清理、事件驱动等核心要素，确保文档完整、专业、可落地。

## 8. 申通面单OCR服务集成说明

### 8.1 服务特性

申通面单OCR服务作为专门针对申通物流的OCR识别服务，具有以下特性：

- **专业化识别**：针对申通面单格式优化，识别准确率更高
- **特有字段支持**：支持申通特有的分拣码、路由码等字段识别
- **独立队列处理**：使用专门的队列确保处理质量和优先级
- **差异化定价**：相比通用OCR服务，定价略高以体现专业化价值

### 8.2 技术架构集成

```mermaid
graph TD
    Client[客户端] -->|POST /v1/op/ocr/sto/upload| Gateway[网关模块]
    Gateway -->|验证API密钥| Auth[认证模块]
    Gateway -->|扣减调用次数| CallMgmt[调用管理模块]
    Gateway -->|路由请求| StoOcrQueue[申通OCR队列]
    StoOcrQueue -->|处理任务| StoOcrProcessor[申通OCR处理器]
    StoOcrProcessor -->|调用专用算法| StoOcrService[申通OCR服务]
    StoOcrService -->|返回结果| StoOcrProcessor
    StoOcrProcessor -->|推送结果| SSE[SSE事件流]
    SSE -->|实时通知| Client
```

### 8.3 业务流程差异

与通用OCR服务相比，申通面单OCR在以下方面有所不同：

1. **队列配置**：使用独立的`sto-ocr-tasks`队列，并发数较低但优先级较高
2. **处理时间**：允许更长的处理时间以确保识别质量
3. **结果格式**：包含申通特有的字段如分拣码、路由码等
4. **错误处理**：针对申通面单特点的专门错误处理逻辑

### 8.4 服务管理

申通面单OCR服务在服务模块中作为独立服务管理：

- **服务代码**：`sto-ocr`
- **服务类型**：`OCR`
- **定价模式**：按请求计费
- **单价**：0.12元/次（比通用OCR略高）
- **状态管理**：支持独立的上线/下线控制