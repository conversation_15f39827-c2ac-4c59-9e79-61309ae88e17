# 网关模块需求文档

## 1. 文档概述

本文档描述了开放平台网关模块的需求规范，该模块负责接收和处理特定的密钥认证业务接口请求，并根据请求类型进行同步或异步处理。网关模块遵循单一职责原则，专注于请求路由和代理功能，不直接处理业务逻辑或认证授权。

## 2. 功能需求

### 2.1 基本需求

- 业务接口代理：代理特定的密钥认证业务接口，包括：
  1. **物流面单OCR识别**：识别物流快递面单上的文字信息，提取收件人、寄件人等数据
  2. **申通面单OCR识别**：专门针对申通物流面单的OCR识别服务，优化识别准确率和字段提取
  3. **地址文字块提取**：从文本中提取省市区、详细地址、手机号、姓名等信息
  4. **地理坐标处理**：包括地理坐标逆解析（坐标转地址）和地址正解析（地址转坐标）
- 请求路由：根据请求路径和配置，将请求路由到对应服务
- 处理模式：支持同步和异步两种处理模式
- 错误处理：统一捕获和处理各类异常，返回标准错误格式

> **注意**：认证授权由全局守卫负责，网关模块不负责认证功能

### 2.2 同步请求处理

- 直接代理：将请求直接转发到业务模块对应服务
- 响应传递：将业务模块的响应直接返回给客户端
- 超时控制：设定合理的超时时间，避免长时间阻塞

### 2.3 异步请求处理

- 请求入队：将请求参数和元数据存入Redis队列
- 任务ID生成：生成唯一的任务ID
- 结果获取与返回：提供两种异步处理模式

#### 2.3.1 客户端自管理模式（Direct Mode）

- 任务ID直接返回：立即将任务ID和SSE连接URL返回给客户端
- 客户端主动获取：客户端通过SSE连接直接获取任务进度和结果
- 适用场景：客户端需要实时显示进度，或有自己的结果处理逻辑

#### 2.3.2 网关代理模式（Proxy Mode）

- 任务代理处理：网关将请求放入队列后，不立即返回结果给客户端
- 内部等待机制：网关内部通过SSE订阅或轮询方式等待任务完成
- 结果统一返回：任务完成后，网关将最终结果和任务ID一并返回给客户端
- 适用场景：客户端期望一次请求获得最终结果，无需处理异步逻辑

> **重要**：网关代理模式虽然对客户端更友好，但会占用服务器资源等待任务完成，请根据任务预期执行时间和系统负载选择合适的模式

### 2.4 非功能需求

- 高性能：能够处理高并发请求，响应时间<100ms
- 高可用：支持水平扩展，无单点故障
- 可监控：提供详细的请求日志和性能指标

## 3. 技术规范

### 3.1 路由配置管理

路由配置采用常量配置方式，不依赖数据库，仅包含特定的业务接口：

```typescript
// gateway.constants.ts
export const API_ROUTES = {
  // OCR服务路由配置
  LOGISTICS_OCR: {
    prefix: '/v1/op/ocr',  // 修正为正确路径格式
    target: 'OCR_SERVICE',
    methods: ['POST'],
    allowAsync: true,
    allowProxyAsync: true,
    defaultMode: 'proxy-async',
    timeout: 30000, // ms
    maxProxyWaitTime: 60000, // 最大网关代理等待时间 (60秒)
    avgProcessingTime: 20000, // 平均处理时间估计 (20秒)
    queue: 'ocr',  // 更新队列名称
  },
  
  // 地址提取服务路由配置
  ADDRESS_EXTRACTION: {
    prefix: '/v1/op/address/extract',  // 修正为正确路径格式
    target: 'ADDRESS_SERVICE',
    methods: ['POST'],
    allowAsync: true,
    allowProxyAsync: true,
    defaultMode: 'proxy-async',
    timeout: 10000, // ms
    maxProxyWaitTime: 30000, // 30秒
    avgProcessingTime: 8000, // 8秒
    queue: 'extract-address',  // 更新队列名称
  },
  
  // 地理坐标服务配置
  GEO_COORDINATE: {
    prefix: '/v1/op/geo',  // 修正为正确路径格式
    paths: {
      REVERSE: {
        path: '/reverse',  // 逆地理编码
        target: 'GEO_SERVICE',
        methods: ['GET', 'POST'],
        allowAsync: true,
        allowProxyAsync: true,
        defaultMode: 'sync',
        timeout: 5000, // ms
        queue: 'rev-geo',  // 更新队列名称
      },
      FORWARD: {
        path: '/forward',  // 正地理编码
        target: 'GEO_SERVICE',
        methods: ['GET', 'POST'],
        allowAsync: true,
        allowProxyAsync: true,
        defaultMode: 'sync',
        timeout: 5000, // ms
        queue: 'rev-geo',  // 更新队列名称
      }
    }
  }
};

// 服务地址配置
export const SERVICE_ENDPOINTS = {
  OCR_SERVICE: process.env.OCR_SERVICE_URL || 'http://localhost:8866',  // 更新为python服务地址
  ADDRESS_SERVICE: process.env.ADDRESS_SERVICE_URL || 'http://localhost:8866',  // 更新为python服务地址
  GEO_SERVICE: process.env.GEO_SERVICE_URL || 'http://localhost:8866',  // 更新为python服务地址
};
```

### 3.2 处理模式决策

请求处理模式通过以下方式确定：

1. 请求中的`mode`参数，可选值为`sync`、`async`、`proxy-async`
   - `sync`: 同步处理模式，直接代理到业务服务并等待结果
   - `async`: 异步处理模式（客户端自管理），将请求加入队列后立即返回任务ID
   - `proxy-async`: 异步代理模式（网关代理），将请求加入队列后网关等待结果再返回
2. 若未指定，则根据服务配置的默认模式和资源预估决定

```typescript
// 处理模式决策逻辑
function determineProcessingMode(request, serviceConfig) {
  // 1. 检查请求中的mode参数
  const requestMode = request.body?.mode || request.query?.mode;
  if (requestMode === 'sync' || requestMode === 'async' || requestMode === 'proxy-async') {
    // 检查服务是否允许该模式
    if (requestMode === 'sync' || 
        (requestMode === 'async' && serviceConfig.allowAsync) ||
        (requestMode === 'proxy-async' && serviceConfig.allowProxyAsync)) {
      return requestMode;
    }
  }
  
  // 2. 检查请求体大小是否超过阈值，决定同步/异步
  const isLargeRequest = getRequestSize(request) > ASYNC_SIZE_THRESHOLD;
  
  // 3. 检查预估处理时间是否超过阈值，决定使用哪种异步模式
  const estimatedProcessingTime = estimateProcessingTime(request, serviceConfig);
  
  // 如果请求较大或预计处理时间长，但处理时间在可接受范围内，使用代理异步模式
  if (isLargeRequest && 
      estimatedProcessingTime <= MAX_PROXY_WAIT_TIME && 
      serviceConfig.allowProxyAsync) {
    return 'proxy-async';
  }
  
  // 如果请求较大或预计处理时间很长，使用客户端自管理异步模式
  if (isLargeRequest && serviceConfig.allowAsync) {
    return 'async';
  }
  
  // 4. 使用服务默认配置
  return serviceConfig.defaultMode || 'sync';
}

// 预估处理时间（基于请求特征和历史数据）
function estimateProcessingTime(request, serviceConfig) {
  // 根据请求类型、数据大小、历史平均处理时间等因素预估处理时间
  // 这里可以实现基于机器学习的预测模型或简单的启发式算法
  
  // 示例实现
  let baseTime = serviceConfig.avgProcessingTime || 1000; // 基准时间(ms)
  
  // 根据请求数据大小调整时间估计
  const requestSize = getRequestSize(request);
  const sizeFactor = Math.log(1 + requestSize / 1024) / Math.log(10); // 对数缩放
  
  // 根据请求类型调整时间估计
  let typeFactor = 1.0;
  if (request.body?.imageBase64 || request.file) {
    typeFactor = 2.5; // 图像处理通常需要更多时间
  } else if (request.body?.text && request.body.text.length > 1000) {
    typeFactor = 1.8; // 长文本处理需要更多时间
  }
  
  return baseTime * sizeFactor * typeFactor;
}
```

### 3.3 队列处理器的SSE实现

队列处理器完全负责SSE连接和结果推送，网关不参与此过程。实现方案如下：

1. 客户端发送异步请求到网关
2. 网关将请求路由到队列，生成任务ID
3. 网关返回任务ID和SSE连接URL给客户端
4. 客户端使用SSE连接URL直接连接到队列处理器
5. 队列处理器负责维护SSE连接和推送结果

```typescript
// 队列处理器中的SSE实现
@Controller('tasks')
export class TaskEventsController {
  constructor(private taskService: TaskService) {}

  @Get(':taskId/events')
  @Header('Content-Type', 'text/event-stream')
  @Header('Cache-Control', 'no-cache')
  @Header('Connection', 'keep-alive')
  async streamTaskEvents(@Param('taskId') taskId, @Res() response) {
    // 设置SSE头部
    response.flushHeaders();
    
    // 初始化连接
    const sendEvent = (data) => {
      response.write(`data: ${JSON.stringify(data)}\n\n`);
    };
    
    // 发送初始状态
    sendEvent({
      taskId,
      status: await this.taskService.getTaskStatus(taskId),
      timestamp: Date.now()
    });
    
    // 订阅任务更新
    const subscription = this.taskService.subscribeToTaskUpdates(taskId, (update) => {
      sendEvent(update);
      
      // 如果任务已完成或失败，关闭连接
      if (['completed', 'failed'].includes(update.status)) {
        response.end();
      }
    });
    
    // 处理客户端断开连接
    response.on('close', () => {
      subscription.unsubscribe();
    });
  }
}
```

### 3.4 Redis队列集成

异步处理任务使用Redis队列管理：

```typescript
// 队列配置
const queueOptions = {
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD
  },
  // 任务处理超时设置
  defaultJobOptions: {
    attempts: 3,
    timeout: 180000, // 3分钟
    removeOnComplete: true,
    removeOnFail: false
  }
};

// 创建队列实例
@Module({
  imports: [
    BullModule.registerQueue({
      name: 'gateway-tasks',
      redis: queueOptions.redis,
      defaultJobOptions: queueOptions.defaultJobOptions
    })
  ]
})
```

### 3.5 文件处理

网关模块支持文件上传和Base64字符串两种方式接收图像数据：

```typescript
// 文件上传处理
@UseInterceptors(
  FileInterceptor('file', {
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB大小限制
    },
    fileFilter: (req, file, callback) => {
      const allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
      if (allowedMimes.includes(file.mimetype)) {
        callback(null, true);
      } else {
        callback(new BadRequestException('不支持的文件类型'), false);
      }
    },
  }),
)
handleFileUpload(@UploadedFile() file: Express.Multer.File) {
  // 处理上传文件
}

// Base64图像处理
validateBase64Image(base64String: string): boolean {
  // 验证Base64字符串合法性
  if (!base64String || typeof base64String !== 'string') {
    return false;
  }
  
  // 检查是否是有效的Base64格式
  const regex = /^data:image\/(jpeg|png|gif);base64,([A-Za-z0-9+/=])+$/;
  return regex.test(base64String);
}
```

## 4. 系统架构

### 4.1 组件架构图

```
                                        +----------------+
                                        |                |
                                        |  全局守卫      |
                                        | (认证授权)     |
                                        |                |
                                        +-------+--------+
                                                |
 +------------------+                           |                          +------------------+
 |                  |      特定业务API请求      |                          |                  |
 |    客户端        +--------------------------->      网关模块            |                  |
 |                  |                           |    (请求路由与代理)     |                  |
 +--------+---------+                           +----------+-------------+                  |
          ^                                     |          |                                |
          |                                     |          |                                |
          |                                     |          |                                |
          |                                     |          |  同步请求                      |
          |                                     |          +-------------------------------->  业务模块       |
          |                                     |                                          |                  |
          |                                     | 异步请求                                 +------------------+
          |                                     v
          |                             +-------+--------+      +------------------+
          |                             |                |      |                  |
          |                             |  Redis队列     +----->+  队列处理服务    |
          |                             |                |      |                  |
          |                             +----------------+      +--------+---------+
          |                                                              |
          |              SSE连接                                         |
          +---------------------------------------------------------<----+
```

### 4.2 请求处理流程图

```
graph TD
    A[客户端请求特定业务API] --> B{全局守卫认证}
    B -->|失败| C[返回401错误]
    B -->|成功| D{请求路由匹配}
    D -->|失败| E[返回404错误]
    D -->|成功| F{处理模式?}
    
    F -->|同步| G[代理到业务服务]
    G --> H[等待业务服务响应]
    H --> I[返回业务响应给客户端]
    
    F -->|客户端自管理异步| J[生成任务ID]
    J --> K[将任务加入Redis队列]
    K --> L[返回任务ID和SSE连接URL]
    L --> M[客户端直接连接到队列处理器的SSE端点]
    M --> N[队列处理器推送任务状态更新]
    N --> O{任务完成?}
    O -->|否| N
    O -->|是| P[队列处理器推送最终结果]
    P --> Q[队列处理器关闭SSE连接]
    
    F -->|网关代理异步| R[生成任务ID]
    R --> S[将任务加入Redis队列]
    S --> T[网关内部订阅SSE更新]
    T --> U[等待任务完成或超时]
    U --> V{任务完成?}
    V -->|是| W[返回最终结果和任务ID]
    V -->|超时| X[返回超时状态和任务ID]
```

### 4.3 模块结构

```
gateway/
├── config/
│   ├── gateway.config.ts        # 网关配置
│   └── routes.constants.ts      # 路由常量定义
├── controllers/
│   ├── gateway.controller.ts    # 主网关控制器 
│   └── task-proxy.controller.ts # 任务代理控制器
├── dto/
│   ├── task-result.dto.ts       # 任务结果DTO
│   └── proxy-options.dto.ts     # 代理选项DTO
├── interfaces/
│   ├── gateway-request.interface.ts # 网关请求接口
│   └── proxy-mode.enum.ts       # 代理模式枚举
├── services/
│   ├── gateway.service.ts       # 网关服务
│   ├── gateway-proxy.service.ts # 代理服务
│   └── task-result.service.ts   # 任务结果处理服务
├── gateway.module.ts            # 网关模块定义
└── index.ts                     # 模块入口
```

## 5. 接口定义

### 5.1 物流OCR识别接口

#### 请求方式1：文件上传模式

```
POST /v1/op/ocr/upload
Headers:
  X-API-KEY: <API密钥> (由全局守卫处理)
  X-SECRET-KEY: <API密钥秘钥> (由全局守卫处理)
  Content-Type: multipart/form-data

Form Data:
  file: [物流面单图片文件]
  mode: "proxy-async"  // 指定处理模式（可选）
```

#### 请求方式2：Base64字符串模式

```
POST /v1/op/ocr/recognize
Headers:
  X-API-KEY: <API密钥> (由全局守卫处理)
  X-SECRET-KEY: <API密钥秘钥> (由全局守卫处理)
  Content-Type: application/json

Body:
{
  "imageBase64": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...", // Base64编码的图片数据
  "mode": "proxy-async"  // 指定处理模式（可选）
}
```

#### 同步响应：

```json
{
  "success": true,
  "data": {
    "sender": {
      "name": "张三",
      "phone": "13800138000",
      "address": "北京市海淀区中关村大街1号"
    },
    "receiver": {
      "name": "李四",
      "phone": "13900139000",
      "address": "上海市浦东新区张江高科技园区"
    },
    "expressInfo": {
      "company": "顺丰速运",
      "trackingNumber": "SF1234567890"
    }
  },
  "requestId": "req_abcdef123456",
  "responseTime": 1203
}
```

#### 客户端自管理异步响应：

```json
{
  "success": true,
  "jobId": "job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a",
  "status": "queued",
  "statusUrl": "/v1/op/tasks/job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a",
  "eventsUrl": "/v1/op/tasks/job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a/events",
  "message": "任务已提交到队列，请通过statusUrl查询结果",
  "requestId": "req_abcdef123456",
  "responseTime": 120
}
```

#### 网关代理异步响应（成功）：

```json
{
  "success": true,
  "jobId": "job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a",
  "status": "completed",
  "data": {
    "sender": {
      "name": "张三",
      "phone": "13800138000",
      "address": "北京市海淀区中关村大街1号"
    },
    "receiver": {
      "name": "李四",
      "phone": "13900139000",
      "address": "上海市浦东新区张江高科技园区"
    },
    "expressInfo": {
      "company": "顺丰速运",
      "trackingNumber": "SF1234567890"
    }
  },
  "processingTime": 18543,  // 处理耗时(毫秒)
  "requestId": "req_abcdef123456",
  "responseTime": 18603
}
```

#### 网关代理异步响应（超时）：

```json
{
  "success": true,
  "jobId": "job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a",
  "status": "processing",
  "progress": 0.45,  // 当前进度
  "estimatedCompletion": 25000,  // 预估剩余时间(毫秒)
  "message": "任务处理中，请通过事件流获取最终结果",
  "statusUrl": "/v1/op/tasks/job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a",
  "eventsUrl": "/v1/op/tasks/job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a/events",
  "requestId": "req_abcdef123456",
  "responseTime": 60120
}
```

### 5.2 申通面单OCR识别接口

#### 请求方式1：文件上传模式

```
POST /v1/op/ocr/sto/upload
Headers:
  X-API-KEY: <API密钥> (由全局守卫处理)
  X-SECRET-KEY: <API密钥秘钥> (由全局守卫处理)
  Content-Type: multipart/form-data

Form Data:
  file: [申通面单图片文件]
  mode: "proxy-async"  // 指定处理模式（可选）
```

#### 请求方式2：Base64字符串模式

```
POST /v1/op/ocr/sto/recognize
Headers:
  X-API-KEY: <API密钥> (由全局守卫处理)
  X-SECRET-KEY: <API密钥秘钥> (由全局守卫处理)
  Content-Type: application/json

Body:
{
  "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...",
  "mode": "proxy-async"  // 指定处理模式
}
```

#### 同步响应：

```json
{
  "success": true,
  "data": {
    "sender": {
      "name": "张三",
      "phone": "13800138000",
      "address": "北京市海淀区中关村大街1号"
    },
    "receiver": {
      "name": "李四",
      "phone": "13900139000",
      "address": "上海市浦东新区张江高科技园区"
    },
    "expressInfo": {
      "company": "申通快递",
      "trackingNumber": "STO1234567890",
      "serviceType": "标准快递",
      "weight": "1.2kg"
    },
    "stoSpecific": {
      "sortingCode": "021-PD-001",
      "routeCode": "SH-BJ-001",
      "packageType": "标准包装"
    }
  },
  "processingTime": 15432,
  "requestId": "req_sto_123456",
  "responseTime": 15492
}
```

#### 客户端自管理异步响应：

```json
{
  "success": true,
  "jobId": "job_sto_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a",
  "statusUrl": "/v1/op/tasks/job_sto_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a",
  "eventsUrl": "/v1/op/tasks/job_sto_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a/events",
  "requestId": "req_sto_123456",
  "responseTime": 156
}
```

#### 网关代理异步响应（成功）：

```json
{
  "success": true,
  "data": {
    "sender": {
      "name": "张三",
      "phone": "13800138000",
      "address": "北京市海淀区中关村大街1号"
    },
    "receiver": {
      "name": "李四",
      "phone": "13900139000",
      "address": "上海市浦东新区张江高科技园区"
    },
    "expressInfo": {
      "company": "申通快递",
      "trackingNumber": "STO1234567890",
      "serviceType": "标准快递",
      "weight": "1.2kg"
    },
    "stoSpecific": {
      "sortingCode": "021-PD-001",
      "routeCode": "SH-BJ-001",
      "packageType": "标准包装"
    }
  },
  "processingTime": 18543,
  "requestId": "req_sto_123456",
  "responseTime": 18603
}
```

### 5.3 地址提取接口

#### 请求：

```
POST /v1/op/address/extract
Headers:
  X-API-KEY: <API密钥> (由全局守卫处理)
  X-SECRET-KEY: <API密钥秘钥> (由全局守卫处理)
  Content-Type: application/json

Body:
{
  "text": "收件人:李四，电话:13900139000，地址:上海市浦东新区张江高科技园区1号楼3层",
  "mode": "proxy-async"  // 指定处理模式
}
```

#### 同步响应：

```json
{
  "success": true,
  "data": {
    "name": "李四",
    "phone": "13900139000",
    "address": {
      "province": "上海市",
      "city": "上海市",
      "district": "浦东新区",
      "street": "",
      "detail": "张江高科技园区1号楼3层",
      "full": "上海市浦东新区张江高科技园区1号楼3层"
    }
  },
  "requestId": "req_abcdef123456",
  "responseTime": 503
}
```

### 5.3 地理坐标接口

#### 逆地理编码请求（坐标转地址）：

```
GET /v1/op/geo/reverse?lat=31.230416&lng=121.473701
Headers:
  X-API-KEY: <API密钥> (由全局守卫处理)
  X-SECRET-KEY: <API密钥秘钥> (由全局守卫处理)
```

#### 逆地理编码响应：

```json
{
  "success": true,
  "data": {
    "address": "上海市黄浦区南京东路1号",
    "province": "上海市",
    "city": "上海市",
    "district": "黄浦区",
    "street": "南京东路",
    "streetNumber": "1号",
    "poi": "人民广场"
  },
  "requestId": "req_abcdef123456",
  "responseTime": 156
}
```

#### 正地理编码请求（地址转坐标）：

```
POST /v1/op/geo/forward
Headers:
  X-API-KEY: <API密钥> (由全局守卫处理)
  X-SECRET-KEY: <API密钥秘钥> (由全局守卫处理)
  Content-Type: application/json

Body:
{
  "address": "上海市黄浦区南京东路1号"
}
```

#### 正地理编码响应：

```json
{
  "success": true,
  "data": {
    "lat": 31.230416,
    "lng": 121.473701,
    "confidence": 0.95,
    "level": "门牌号"
  },
  "requestId": "req_abcdef123456",
  "responseTime": 178
}
```

### 5.4 任务状态SSE接口（队列处理器提供）

```
GET /v1/op/tasks/:jobId/events
Headers:
  X-API-KEY: <API密钥> (由全局守卫处理)
  X-SECRET-KEY: <API密钥秘钥> (由全局守卫处理)
  Accept: text/event-stream
```

#### SSE事件流示例：

```
data: {"jobId":"job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a","status":"queued","timestamp":1633456789000}

data: {"jobId":"job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a","status":"processing","progress":0.3,"timestamp":1633456790000}

data: {"jobId":"job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a","status":"completed","result":{"sender":{"name":"张三","phone":"13800138000","address":"北京市海淀区中关村大街1号"},"receiver":{"name":"李四","phone":"13900139000","address":"上海市浦东新区张江高科技园区"},"expressInfo":{"company":"顺丰速运","trackingNumber":"SF1234567890"}},"timestamp":1633456792000}
```

### 5.5 任务状态查询接口

```
GET /v1/op/tasks/:jobId
Headers:
  X-API-KEY: <API密钥> (由全局守卫处理)
  X-SECRET-KEY: <API密钥秘钥> (由全局守卫处理)
```

#### 响应示例（已完成）：

```json
{
  "success": true,
  "jobId": "job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a",
  "status": "completed",
  "createdAt": 1633456789000,
  "updatedAt": 1633456792000,
  "result": {
    "sender": {
      "name": "张三",
      "phone": "13800138000",
      "address": "北京市海淀区中关村大街1号"
    },
    "receiver": {
      "name": "李四",
      "phone": "13900139000",
      "address": "上海市浦东新区张江高科技园区"
    },
    "expressInfo": {
      "company": "顺丰速运",
      "trackingNumber": "SF1234567890"
    }
  },
  "requestId": "req_abcdef123456",
  "responseTime": 45
}
```

#### 响应示例（处理中）：

```json
{
  "success": true,
  "jobId": "job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a",
  "status": "processing",
  "progress": 0.45,
  "message": "任务处理中",
  "createdAt": 1633456789000,
  "updatedAt": 1633456790000,
  "estimatedCompletion": 25000,
  "eventsUrl": "/v1/op/tasks/job_6f8d7a3c-e9b0-4f5a-b6c1-2d8e9b5f4c3a/events",
  "requestId": "req_abcdef123456",
  "responseTime": 37
}
```

## 6. 实现要点

### 6.1 单一职责原则

- 网关模块仅负责特定业务接口的请求路由和代理，不处理认证授权和业务逻辑
- 认证授权由全局守卫负责
- 同步请求直接代理到业务模块
- 异步请求代理到Redis队列，网关不参与后续处理
- SSE连接和结果推送完全由队列处理器负责

### 6.2 错误处理

- 统一捕获并格式化错误响应
- 针对不同类型错误定义明确的错误码和消息
- 记录详细错误日志，但隐藏敏感信息

```typescript
// 错误处理示例
@Catch()
export class GatewayExceptionFilter implements ExceptionFilter {
  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();
    
    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let errorCode = 'INTERNAL_ERROR';
    let message = '服务器内部错误';
    
    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const errorResponse = exception.getResponse() as any;
      message = errorResponse.message || exception.message;
      errorCode = errorResponse.errorCode || this.mapHttpStatus(status);
    } else if (exception.code === 'ETIMEDOUT') {
      status = HttpStatus.GATEWAY_TIMEOUT;
      errorCode = 'GATEWAY_TIMEOUT';
      message = '请求超时';
    }
    
    // 记录错误日志
    this.logger.error(
      `Gateway Error: [${errorCode}] ${message}`,
      exception.stack,
      { 
        path: request.url,
        method: request.method,
        requestId: request.id
      }
    );
    
    response.status(status).json({
      code: status,
      errorCode: errorCode,
      message: message,
      timestamp: new Date().toISOString(),
      requestId: request.id
    });
  }
}
```

### 6.3 任务状态管理

使用Redis存储任务状态和结果，由队列处理器负责更新和推送：

```typescript
// 任务状态管理
@Injectable()
export class TaskService {
  constructor(
    @InjectRedis() private readonly redis: Redis,
    private readonly pubSub: RedisPubSubService,
  ) {}
  
  async createTask(data: any): Promise<string> {
    const taskId = uuidv4();
    
    // 存储任务信息
    await this.redis.hmset(`task:${taskId}`, {
      status: 'queued',
      createdAt: Date.now(),
      data: JSON.stringify(data)
    });
    
    // 设置过期时间（24小时）
    await this.redis.expire(`task:${taskId}`, 86400);
    
    return taskId;
  }
  
  async updateTaskStatus(taskId: string, status: string, data?: any): Promise<void> {
    const update: any = { status, updatedAt: Date.now() };
    
    if (data) {
      update.result = JSON.stringify(data);
    }
    
    await this.redis.hmset(`task:${taskId}`, update);
    
    // 发布状态更新事件
    await this.pubSub.publish(`task:${taskId}:updates`, {
      taskId,
      status,
      ...(data ? { result: data } : {}),
      timestamp: Date.now()
    });
  }
  
  async getTaskStatus(taskId: string): Promise<any> {
    const task = await this.redis.hgetall(`task:${taskId}`);
    
    if (!task || Object.keys(task).length === 0) {
      throw new NotFoundException(`Task ${taskId} not found`);
    }
    
    return {
      taskId,
      status: task.status,
      ...(task.result ? { result: JSON.parse(task.result) } : {}),
      createdAt: parseInt(task.createdAt),
      updatedAt: task.updatedAt ? parseInt(task.updatedAt) : undefined
    };
  }
  
  subscribeToTaskUpdates(taskId: string, callback: (data: any) => void): Subscription {
    return this.pubSub.subscribe(`task:${taskId}:updates`, callback);
  }
}
```

### 6.4 文件处理策略

网关模块对物流OCR识别接口提供文件上传和Base64两种方式的支持：

```typescript
@Post('logistics')
@UseInterceptors(FileInterceptor('file'))
async handleLogisticsOcr(
  @UploadedFile() file: Express.Multer.File,
  @Body() body: any,
  @Req() request: Request
) {
  // 处理模式决策
  const processingMode = this.determineProcessingMode(request, this.routeConfig.LOGISTICS_OCR);
  
  let imageData: { type: 'file' | 'base64', data: any };
  
  // 判断请求类型
  if (file) {
    // 文件上传模式
    imageData = {
      type: 'file',
      data: {
        buffer: file.buffer,
        originalname: file.originalname,
        mimetype: file.mimetype
      }
    };
  } else if (body.imageBase64) {
    // Base64字符串模式
    if (!this.validateBase64Image(body.imageBase64)) {
      throw new BadRequestException('无效的Base64图片格式');
    }
    
    imageData = {
      type: 'base64',
      data: body.imageBase64
    };
  } else {
    throw new BadRequestException('请提供文件或Base64编码的图片');
  }
  
  // 根据处理模式进行不同处理
  if (processingMode === 'async') {
    // 异步处理逻辑...
    const taskId = await this.queueService.addTask('logistics-ocr', { imageData });
    return {
      code: 202,
      message: '任务已接受',
      data: {
        taskId,
        status: 'queued',
        eventsUrl: `/tasks/${taskId}/events`
      }
    };
  } else {
    // 同步处理逻辑...
    const result = await this.proxyService.forwardToService(
      'OCR_SERVICE',
      '/logistics',
      { imageData }
    );
    
    return result;
  }
}
```

### 6.5 网关代理异步模式实现

网关代理异步模式通过Promise封装SSE订阅等待过程，实现任务结果的内部等待：

```typescript
@Injectable()
export class TaskResultService {
  constructor(
    private readonly taskService: TaskService,
    private readonly pubSubService: RedisPubSubService,
    private readonly cacheManager: CacheService,
    private readonly logger: StructuredLoggerService,
    private readonly metricsService: MetricsService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 等待任务完成并返回结果，设置最大等待时间
   * 使用Promise封装SSE订阅过程，确保资源正确释放
   */
  async waitForTaskResult(
    taskId: string,
    options: {
      maxWaitTime?: number;            // 最大等待时间
      checkInterval?: number;          // 轮询检查间隔（毫秒）
      usePollingFallback?: boolean;    // 是否在订阅失败时使用轮询
      progressCallback?: Function;     // 进度回调函数
    } = {}
  ): Promise<{ result: any; status: string; progress?: number; error?: string }> {
    // 从任务类型获取合理的默认超时时间
    const taskInfo = await this.taskService.getTaskInfo(taskId);
    const taskType = taskInfo?.type || 'default';
    
    // 使用自适应超时策略获取超时时间
    const defaultMaxWaitTime = await this.getOptimalTimeoutForTaskType(taskType);
    
    // 合并选项与默认值
    const maxWaitTime = options.maxWaitTime || defaultMaxWaitTime;
    const checkInterval = options.checkInterval || 2000;
    const usePollingFallback = options.usePollingFallback !== false;
    
    // 记录指标
    this.metricsService.recordTaskWaitStart(taskId, taskType);
    
    // 请求开始时间
    const startTime = Date.now();
    
    return new Promise((resolve, reject) => {
      // 创建取消标记
      const abortController = new AbortController();
      let timeoutId: NodeJS.Timeout;
      let intervalId: NodeJS.Timeout;
      let subscription: Subscription;
      let isResolved = false;
      
      // 确保资源清理函数
      const cleanup = () => {
        if (timeoutId) clearTimeout(timeoutId);
        if (intervalId) clearInterval(intervalId);
        if (subscription) subscription.unsubscribe();
        this.logger.debug(
          `清理任务${taskId}的等待资源，总耗时: ${Date.now() - startTime}ms`,
          'TaskResultService'
        );
      };
      
      // 结果处理函数
      const handleResult = (status: string, result?: any, error?: string, progress?: number) => {
        if (isResolved) return;
        isResolved = true;
        
        // 记录结果指标
        this.metricsService.recordTaskWaitComplete(
          taskId,
          status,
          Date.now() - startTime
        );
        
        cleanup();
        resolve({ status, result, error, progress });
      };
      
      // 错误处理函数
      const handleError = (error: Error) => {
        if (isResolved) return;
        
        this.logger.error(
          `等待任务${taskId}结果时出错: ${error.message}`,
          error.stack,
          'TaskResultService'
        );
        
        // 记录错误指标
        this.metricsService.recordTaskWaitError(taskId, error.message);
        
        cleanup();
        reject(new GatewayTimeoutException(`等待任务${taskId}结果失败: ${error.message}`));
      };
      
      // 设置超时保护
      timeoutId = setTimeout(async () => {
        try {
          // 获取当前任务状态
          const status = await this.taskService.getTaskStatus(taskId);
          
          // 使用进度预估剩余时间
          const progress = status.progress || 0;
          const estimatedCompletion = this.estimateRemainingTime(taskType, progress);
          
          // 记录超时指标
          this.metricsService.recordTaskWaitTimeout(
            taskId,
            progress,
            estimatedCompletion
          );
          
          // 添加任务信息到缓存，便于客户端后续查询
          await this.cacheTaskStatusForLaterRetrieval(taskId, status);
          
          this.logger.warn(
            `等待任务${taskId}结果超时，当前进度: ${Math.round(progress*100)}%，预估剩余时间: ${estimatedCompletion}ms`,
            'TaskResultService'
          );
          
          handleResult(
            'processing',
            null,
            null,
            progress
          );
        } catch (error) {
          handleError(error);
        }
      }, maxWaitTime);
      
      // 首先获取当前状态
      this.taskService.getTaskStatus(taskId)
        .then(initialStatus => {
          // 检查任务是否已经完成
          if (initialStatus.status === TaskStatus.COMPLETED) {
            return handleResult('completed', initialStatus.result);
          }
          
          // 检查任务是否已经失败
          if (initialStatus.status === TaskStatus.FAILED) {
            return handleResult('failed', null, initialStatus.error || '任务执行失败');
          }
          
          // 进度回调
          if (options.progressCallback && typeof options.progressCallback === 'function') {
            options.progressCallback(initialStatus.progress || 0);
          }
          
          try {
            // 使用发布/订阅模式订阅任务更新
            subscription = this.pubSubService.subscribe(
              `task:${taskId}:updates`,
              (update) => {
                // 更新进度回调
                if (options.progressCallback && typeof options.progressCallback === 'function' && update.progress) {
                  options.progressCallback(update.progress);
                }
                
                // 检查任务是否已完成或失败
                if (update.status === TaskStatus.COMPLETED) {
                  handleResult('completed', update.result);
                } else if (update.status === TaskStatus.FAILED) {
                  handleResult('failed', null, update.error || '任务执行失败');
                }
              }
            );
            
            // 注册中断处理
            abortController.signal.addEventListener('abort', () => {
              this.logger.debug(
                `任务${taskId}等待被中断`,
                'TaskResultService'
              );
              cleanup();
            });
          } catch (error) {
            // 如果订阅失败但允许使用轮询
            if (usePollingFallback) {
              this.logger.warn(
                `无法使用SSE订阅任务${taskId}更新，改用轮询: ${error.message}`,
                'TaskResultService'
              );
              
              // 使用轮询作为后备方案
              intervalId = setInterval(async () => {
                try {
                  const status = await this.taskService.getTaskStatus(taskId);
                  
                  // 更新进度回调
                  if (options.progressCallback && typeof options.progressCallback === 'function') {
                    options.progressCallback(status.progress || 0);
                  }
                  
                  // 检查任务是否已完成或失败
                  if (status.status === TaskStatus.COMPLETED) {
                    handleResult('completed', status.result);
                  } else if (status.status === TaskStatus.FAILED) {
                    handleResult('failed', null, status.error || '任务执行失败');
                  }
                } catch (pollingError) {
                  handleError(pollingError);
                }
              }, checkInterval);
            } else {
              // 如果不允许轮询，则直接报错
              handleError(error);
            }
          }
        })
        .catch(error => handleError(error));
        
      // 返回取消函数，允许外部中断等待
      return () => {
        if (!isResolved) {
          abortController.abort();
        }
      };
    });
  }
  
  /**
   * 获取特定任务类型的最佳超时时间
   * 基于历史数据自适应调整
   */
  private async getOptimalTimeoutForTaskType(taskType: string): Promise<number> {
    try {
      // 从缓存或配置获取基准超时时间
      const baseTimeout = this.configService.get<number>(
        `taskTimeouts.${taskType}`, 
        30000 // 默认30秒
      );
      
      // 从指标服务获取该任务类型的历史执行数据
      const metrics = await this.metricsService.getTaskTypeMetrics(taskType);
      
      if (!metrics || !metrics.avgDuration) {
        return baseTimeout;
      }
      
      // 计算动态超时时间：平均持续时间 + 2个标准差（覆盖约95%的执行情况）
      let dynamicTimeout = metrics.avgDuration + (2 * metrics.stdDeviation);
      
      // 添加缓冲区（额外20%的时间）
      dynamicTimeout *= 1.2;
      
      // 确保超时时间在合理范围内
      const minTimeout = Math.max(5000, baseTimeout * 0.5); // 最小为5秒或基准的50%
      const maxTimeout = baseTimeout * 3; // 最大为基准的3倍
      
      return Math.max(minTimeout, Math.min(dynamicTimeout, maxTimeout));
    } catch (error) {
      this.logger.warn(
        `计算任务${taskType}的最佳超时时间失败，使用默认值: ${error.message}`,
        'TaskResultService'
      );
      
      // 发生错误时返回默认超时时间
      return this.configService.get<number>('taskTimeouts.default', 30000);
    }
  }
  
  /**
   * 估计任务剩余完成时间
   */
  private estimateRemainingTime(taskType: string, progress: number): number {
    if (progress >= 1) return 0;
    if (progress <= 0) return 60000; // 默认1分钟
    
    try {
      // 获取任务类型的平均处理时间
      const avgProcessingTime = this.configService.get<number>(
        `taskAvgProcessingTimes.${taskType}`, 
        20000 // 默认20秒
      );
      
      // 如果进度数据可靠，使用线性估计
      if (progress > 0.1) { // 进度超过10%时，线性估计较为准确
        const elapsedTime = Date.now() - progress * avgProcessingTime;
        const estimatedTotal = elapsedTime / progress;
        return Math.max(1000, estimatedTotal - elapsedTime);
      }
      
      // 对于初始阶段，使用历史数据估计
      const remainingPercentage = 1 - progress;
      return remainingPercentage * avgProcessingTime;
    } catch (error) {
      // 发生错误时使用保守估计
      return (1 - progress) * 60000; // 默认满进度1分钟
    }
  }
  
  /**
   * 将任务状态缓存以便客户端后续查询
   * 特别是处理超时的情况，客户端仍然可以通过状态API获取结果
   */
  private async cacheTaskStatusForLaterRetrieval(taskId: string, status: any): Promise<void> {
    try {
      // 缓存最近状态
      await this.cacheManager.set(
        `task_last_status:${taskId}`,
        status,
        { ttl: 86400 } // 24小时过期
      );
      
      // 记录该任务已超时但仍在处理
      await this.cacheManager.set(
        `task_timeout_flag:${taskId}`,
        {
          timeoutAt: Date.now(),
          lastProgress: status.progress || 0
        },
        { ttl: 86400 } // 24小时过期
      );
    } catch (error) {
      this.logger.error(
        `缓存任务${taskId}状态信息失败: ${error.message}`,
        error.stack,
        'TaskResultService'
      );
    }
  }
}
```

### 6.6 网关控制器实现示例

以下是网关控制器如何处理不同处理模式的示例：

```typescript
@Controller('op/ocr')
export class OcrGatewayController {
  constructor(
    private readonly gatewayService: GatewayService,
    private readonly gatewayProxyService: GatewayProxyService,
    private readonly queueService: QueueManagerService,
    private readonly taskResultService: TaskResultService,
    private readonly logger: StructuredLoggerService,
    private readonly metricsService: MetricsService,
    private readonly configService: ConfigService,
  ) {}

  @Post('logistics')
  @UseInterceptors(FileInterceptor('file'))
  async handleLogisticsOcr(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: any,
    @Req() request: Request,
    @Res() response: Response,
  ) {
    const requestStartTime = Date.now();
    const requestId = this.generateRequestId();
    
    try {
      this.logger.log(
        `处理物流OCR请求 [${requestId}]`,
        'OcrGatewayController',
        { hasFile: !!file, hasImageBase64: !!body?.imageBase64 }
      );
      
      // 处理模式决策
      const processingMode = this.gatewayService.determineProcessingMode(
        request, 
        API_ROUTES.LOGISTICS_OCR
      );
      
      this.logger.debug(
        `请求 [${requestId}] 使用处理模式: ${processingMode}`,
        'OcrGatewayController'
      );
      
      // 处理图像数据
      const imageData = await this.prepareImageData(file, body);
      
      // 根据处理模式进行不同处理
      switch (processingMode) {
        case 'sync':
          // 同步处理 - 直接转发到业务服务
          try {
            // 记录同步请求指标
            this.metricsService.recordSyncRequest('ocr-logistics');
            
            // 设置超时时间和重试策略
            const timeout = this.configService.get<number>('services.ocr.timeout', 30000);
            const retries = this.configService.get<number>('services.ocr.retries', 1);
            
            // 发送请求到业务服务
            const syncResult = await this.gatewayProxyService.forwardToService(
              'OCR_SERVICE',
              '/logistics',
              { imageData },
              { 
                timeout,
                retries,
                headers: {
                  'X-Request-ID': requestId
                }
              }
            );
            
            // 记录处理时间指标
            const processingTime = Date.now() - requestStartTime;
            this.metricsService.recordSyncResponse('ocr-logistics', processingTime);
            
            // 返回成功响应
            return response.status(200).json({
              code: 200,
              message: '操作成功',
              data: syncResult
            });
          } catch (error) {
            // 记录同步请求失败
            this.metricsService.recordSyncError('ocr-logistics', error.message);
            
            // 抛出错误让全局异常过滤器处理
            throw error;
          }
          
        case 'async':
          // 客户端自管理异步处理
          const taskId = await this.queueService.addTask('logistics-ocr', { 
            imageData,
            requestId,
            clientInfo: this.getClientInfo(request)
          });
          
          // 记录异步请求指标
          this.metricsService.recordAsyncRequest('ocr-logistics', taskId);
          
          // 返回任务ID和SSE连接URL
          return response.status(202).json({
            code: 202,
            message: '任务已接受',
            data: {
              taskId,
              status: 'queued',
              eventsUrl: `/tasks/${taskId}/events`,
              statusUrl: `/tasks/${taskId}/status`
            }
          });
          
        case 'proxy-async':
          // 网关代理异步处理
          try {
            // 1. 创建任务
            const proxyTaskId = await this.queueService.addTask('logistics-ocr', { 
              imageData,
              requestId,
              clientInfo: this.getClientInfo(request)
            });
            
            // 记录代理异步请求指标
            this.metricsService.recordProxyAsyncRequest('ocr-logistics', proxyTaskId);
            
            // 2. 读取配置的等待时间或使用默认值
            const routeConfig = API_ROUTES.LOGISTICS_OCR;
            const maxWaitTime = routeConfig.maxProxyWaitTime || 60000; // 默认60秒
            
            // 设置进度更新回调
            let lastProgress = 0;
            const progressCallback = (progress: number) => {
              // 仅记录显著变化的进度（每增加5%记录一次）
              if (progress - lastProgress >= 0.05) {
                this.logger.debug(
                  `任务 ${proxyTaskId} 进度更新: ${Math.round(progress * 100)}%`,
                  'OcrGatewayController'
                );
                lastProgress = progress;
              }
            };
            
            // 3. 等待任务完成（有超时保护）
            const result = await this.taskResultService.waitForTaskResult(proxyTaskId, {
              maxWaitTime,
              progressCallback,
              usePollingFallback: true
            });
            
            // 记录总处理时间
            const totalProcessingTime = Date.now() - requestStartTime;
            
            // 4. 根据结果状态返回不同响应
            if (result.status === 'completed') {
              // 任务已完成，返回结果
              this.metricsService.recordProxyAsyncComplete(
                'ocr-logistics', 
                proxyTaskId,
                totalProcessingTime
              );
              
              return response.status(200).json({
                code: 200,
                message: '操作成功',
                data: {
                  taskId: proxyTaskId,
                  result: result.result,
                  processingTime: totalProcessingTime
                }
              });
            } else if (result.status === 'failed') {
              // 任务失败，返回错误信息
              this.metricsService.recordProxyAsyncFailed(
                'ocr-logistics', 
                proxyTaskId,
                result.error
              );
              
              return response.status(500).json({
                code: 500,
                message: '任务处理失败',
                error: result.error || '未知错误',
                data: {
                  taskId: proxyTaskId,
                  eventsUrl: `/tasks/${proxyTaskId}/events`,
                  statusUrl: `/tasks/${proxyTaskId}/status`
                }
              });
            } else {
              // 任务超时，返回任务状态和进度信息
              const remainingTime = this.taskResultService.estimateRemainingTime(
                'ocr-logistics',
                result.progress || 0
              );
              
              this.metricsService.recordProxyAsyncTimeout(
                'ocr-logistics', 
                proxyTaskId,
                result.progress || 0,
                remainingTime
              );
              
              return response.status(202).json({
                code: 202,
                message: '任务处理中，请通过事件流获取最终结果',
                data: {
                  taskId: proxyTaskId,
                  status: 'processing',
                  progress: result.progress || 0,
                  estimatedCompletion: remainingTime,
                  elapsedTime: totalProcessingTime,
                  eventsUrl: `/tasks/${proxyTaskId}/events`,
                  statusUrl: `/tasks/${proxyTaskId}/status`
                }
              });
            }
          } catch (error) {
            this.logger.error(
              `处理代理异步请求时出错: ${error.message}`,
              error.stack,
              'OcrGatewayController'
            );
            
            // 记录代理异步错误
            this.metricsService.recordProxyAsyncError('ocr-logistics', error.message);
            
            throw error;
          }
          
        default:
          throw new BadRequestException('不支持的处理模式');
      }
    } catch (error) {
      this.logger.error(
        `处理物流OCR请求 [${requestId}] 时出错: ${error.message}`,
        error.stack,
        'OcrGatewayController'
      );
      
      // 抛出错误让全局异常过滤器处理
      throw error;
    }
  }
  
  /**
   * 生成唯一请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  }
  
  /**
   * 收集客户端信息用于日志和调试
   */
  private getClientInfo(request: Request): Record<string, any> {
    return {
      ip: request.ip,
      userAgent: request.headers['user-agent'],
      referer: request.headers['referer'],
      // 其他可能需要的客户端信息
    };
  }
  
  /**
   * 准备图像数据（文件或Base64）
   */
  private async prepareImageData(file: Express.Multer.File, body: any): Promise<any> {
    if (file) {
      // 文件上传模式
      return {
        type: 'file',
        data: {
          buffer: file.buffer,
          originalname: file.originalname,
          mimetype: file.mimetype,
          size: file.size
        }
      };
    } else if (body.imageBase64) {
      // Base64字符串模式
      if (!this.gatewayService.validateBase64Image(body.imageBase64)) {
        throw new BadRequestException('无效的Base64图片格式');
      }
      
      // 计算Base64数据大小（近似值）
      const base64Size = Math.ceil((body.imageBase64.length * 3) / 4);
      
      return {
        type: 'base64',
        data: body.imageBase64,
        size: base64Size
      };
    }
    
    throw new BadRequestException('请提供文件或Base64编码的图片');
  }
}
```

## 7. 测试计划

### 7.1 单元测试

- 路由解析逻辑测试
- 处理模式决策测试
- 错误处理测试
- 特定业务接口代理测试
- 文件上传和Base64处理测试

### 7.2 集成测试

- 同步代理请求测试
- 异步队列请求测试
- Redis队列集成测试
- 各业务接口端到端测试
- 文件上传大文件处理测试

### 7.3 性能测试

- 高并发请求处理能力测试
- 网关服务资源消耗监控
- 响应时间和吞吐量基准测试
- 不同业务接口的性能对比测试
- 文件上传vs Base64性能对比

## 8. 部署注意事项

- 支持水平扩展，多实例部署
- 确保Redis集群配置支持发布/订阅功能
- 配置适当的负载均衡策略
- 设置合理的健康检查和故障转移机制
- 实现请求跟踪和分布式日志收集
- 配置文件上传大小限制和临时存储策略

## 9. 后续优化方向

- 实现智能路由，根据负载动态分配请求
- 增加请求重试和熔断机制
- 实现请求限流和防滥用机制
- 开发管理界面，提供实时监控和调试功能
- 支持更多业务接口的代理
- 优化文件处理流程，支持更多图片格式
- **自适应处理模式选择**：根据历史执行数据和当前系统负载，自动优化处理模式选择
- **分布式网关代理等待**：针对网关代理模式，实现分布式等待机制，避免单节点资源占用
- **增强任务进度估计**：使用机器学习模型，基于任务类型和输入数据特征，更精准预测处理时间
- **网关预缓存**：对频繁请求的相同或相似任务进行缓存，避免重复处理
- **客户端SDK**：提供多语言SDK，简化客户端使用网关服务的复杂度，特别是异步模式的处理 

### 6.7 网关代理模式的超时策略与资源管理

由于网关代理模式会占用服务器资源等待任务完成，因此需要特别注意超时策略和资源管理：

#### 6.7.1 自适应超时策略

为不同任务类型和数据规模设置合理的超时时间：

1. **基于历史数据的动态超时**：根据任务类型和历史执行时间自动调整超时时间
2. **超时分级策略**：根据数据规模和复杂度分级设置超时时间
3. **系统负载感知**：在系统高负载时适当减少超时时间

```typescript
// 配置示例
const timeoutConfig = {
  'ocr-logistics': {
    base: 30000,        // 基准超时时间（毫秒）
    small: 15000,       // 小型图像
    medium: 30000,      // 中型图像
    large: 60000,       // 大型图像
    maxAllowed: 120000, // 最大允许超时
    loadFactors: {
      low: 1.2,         // 低负载时间倍数
      medium: 1.0,      // 中等负载时间倍数
      high: 0.8,        // 高负载时间倍数
    }
  },
  // 其他任务类型...
};

// 计算适当的超时时间
function calculateTimeout(taskType, dataSize, systemLoad) {
  const config = timeoutConfig[taskType] || timeoutConfig.default;
  
  // 基于数据大小选择基准时间
  let baseTimeout;
  if (dataSize < 200 * 1024) {
    baseTimeout = config.small;
  } else if (dataSize < 1024 * 1024) {
    baseTimeout = config.medium;
  } else {
    baseTimeout = config.large;
  }
  
  // 应用系统负载因子
  let loadFactor;
  if (systemLoad < 50) {
    loadFactor = config.loadFactors.low;
  } else if (systemLoad < 80) {
    loadFactor = config.loadFactors.medium;
  } else {
    loadFactor = config.loadFactors.high;
  }
  
  // 计算最终超时时间
  const finalTimeout = baseTimeout * loadFactor;
  
  // 确保不超过最大允许值
  return Math.min(finalTimeout, config.maxAllowed);
}
```

#### 6.7.2 资源管理和并发控制

为防止过多的等待请求耗尽系统资源，需实现有效的资源管理：

1. **请求限流**：针对网关代理模式设置单独的并发限制
2. **请求分级**：基于用户级别或API密钥分配不同的资源配额
3. **自动降级**：当系统负载过高时，自动将代理异步请求转换为客户端自管理异步请求

```typescript
@Injectable()
export class ProxyAsyncLimiterService {
  private activeProxyRequests: number = 0;
  private readonly maxConcurrentRequests: number;
  private readonly maxRequestsPerClient: number;
  private readonly clientRequests: Map<string, number> = new Map();
  
  constructor(
    private readonly configService: ConfigService,
    private readonly metricsService: MetricsService,
    private readonly logger: StructuredLoggerService,
  ) {
    this.maxConcurrentRequests = this.configService.get<number>('gateway.maxConcurrentProxyRequests', 100);
    this.maxRequestsPerClient = this.configService.get<number>('gateway.maxProxyRequestsPerClient', 5);
  }
  
  /**
   * 检查是否可以接受新的代理异步请求
   */
  canAcceptRequest(clientId: string): boolean {
    // 检查整体系统限制
    if (this.activeProxyRequests >= this.maxConcurrentRequests) {
      return false;
    }
    
    // 检查每客户端限制
    const clientRequests = this.clientRequests.get(clientId) || 0;
    if (clientRequests >= this.maxRequestsPerClient) {
      return false;
    }
    
    return true;
  }
  
  /**
   * 注册新的代理异步请求
   */
  registerRequest(clientId: string): void {
    this.activeProxyRequests++;
    this.clientRequests.set(clientId, (this.clientRequests.get(clientId) || 0) + 1);
    
    this.metricsService.gaugeActiveConcurrentRequests(this.activeProxyRequests);
    this.metricsService.gaugeClientConcurrentRequests(clientId, this.clientRequests.get(clientId) || 0);
  }
  
  /**
   * 释放代理异步请求
   */
  releaseRequest(clientId: string): void {
    this.activeProxyRequests = Math.max(0, this.activeProxyRequests - 1);
    
    const clientRequests = this.clientRequests.get(clientId) || 0;
    if (clientRequests > 0) {
      this.clientRequests.set(clientId, clientRequests - 1);
    }
    
    this.metricsService.gaugeActiveConcurrentRequests(this.activeProxyRequests);
    this.metricsService.gaugeClientConcurrentRequests(clientId, this.clientRequests.get(clientId) || 0);
  }
}
```

#### 6.7.3 中断和资源释放机制

实现有效的中断和资源释放机制，确保系统健壮性：

1. **请求中断**：当客户端断开连接时，立即中断网关代理等待
2. **资源超时释放**：设置双重超时保护，确保资源不被长时间占用
3. **优雅关闭**：服务关闭时，确保正确处理所有活跃请求

```typescript
// 使用AbortController管理请求生命周期
function handleProxyAsyncRequest(req, res) {
  const abortController = new AbortController();
  
  // 监听客户端断开连接
  req.on('close', () => {
    if (!res.writableEnded) {
      // 客户端已断开连接，中断等待操作
      abortController.abort();
      logger.warn('客户端断开连接，中断代理等待');
    }
  });
  
  // 添加请求超时
  const requestTimeout = setTimeout(() => {
    abortController.abort();
    logger.warn('请求超时，中断代理等待');
  }, MAX_REQUEST_TIMEOUT);
  
  // 处理请求，传入AbortController
  processRequest(req, res, abortController.signal)
    .finally(() => {
      clearTimeout(requestTimeout);
    });
}
```

#### 6.7.4 监控与警报

实现全面的监控系统，及时发现问题：

1. **关键指标监控**：平均等待时间、超时率、完成率等
2. **资源使用监控**：内存使用、连接数、线程数等
3. **异常模式检测**：发现异常的等待时间或失败模式
4. **自动告警**：当关键指标超过阈值时发送告警

```typescript
@Injectable()
export class ProxyAsyncMonitoringService {
  constructor(
    private readonly metricsService: MetricsService,
    private readonly alertService: AlertService,
    private readonly logger: StructuredLoggerService,
  ) {
    this.setupMonitoring();
  }
  
  private setupMonitoring(): void {
    // 每分钟检查一次指标
    setInterval(() => {
      this.checkMetrics();
    }, 60000);
  }
  
  private async checkMetrics(): Promise<void> {
    try {
      // 获取最近5分钟的指标
      const metrics = await this.metricsService.getProxyAsyncMetrics(5);
      
      // 检查超时率
      if (metrics.timeoutRate > 0.1) { // 超过10%的超时率
        this.alertService.sendAlert({
          level: 'warning',
          title: '代理异步超时率过高',
          message: `当前超时率: ${(metrics.timeoutRate * 100).toFixed(1)}%`,
          metrics: metrics
        });
      }
      
      // 检查失败率
      if (metrics.failureRate > 0.05) { // 超过5%的失败率
        this.alertService.sendAlert({
          level: 'error',
          title: '代理异步失败率过高',
          message: `当前失败率: ${(metrics.failureRate * 100).toFixed(1)}%`,
          metrics: metrics
        });
      }
      
      // 检查资源使用情况
      if (metrics.avgConcurrentRequests > metrics.maxConcurrentRequests * 0.9) {
        this.alertService.sendAlert({
          level: 'warning',
          title: '代理异步并发请求接近上限',
          message: `当前并发请求: ${metrics.avgConcurrentRequests}/${metrics.maxConcurrentRequests}`,
          metrics: metrics
        });
      }
    } catch (error) {
      this.logger.error(
        `监控指标检查失败: ${error.message}`,
        error.stack,
        'ProxyAsyncMonitoringService'
      );
    }
  }
} 
```

## 10. 规范和常量定义

为了确保项目中网关模块、队列模块和其他相关模块的路径、队列名称等保持一致，应该创建统一的常量配置文件，用于集中管理API路径、队列名称等关键信息。

### 10.1 共享常量定义

推荐创建一个集中式的常量配置文件，供项目中所有模块引用：

```typescript
// shared/constants/api-paths.constants.ts

/**
 * API路径和队列名称常量
 * 集中管理API路径、队列名称等，确保多模块之间的一致性
 */
export const API_PATHS = {
  // OCR服务相关
  OCR: {
    PREFIX: '/v1/op/ocr',
    UPLOAD: '/v1/op/ocr/upload',
    RECOGNIZE: '/v1/op/ocr/recognize',
    QUEUE_NAME: 'ocr',
  },
  
  // 地址服务相关
  ADDRESS: {
    PREFIX: '/v1/op/address',
    EXTRACT: '/v1/op/address/extract',
    EXTRACT_BATCH: '/v1/op/address/extract-batch',
    QUEUE_NAME: 'extract-address',
  },
  
  // 地理坐标服务相关
  GEO: {
    PREFIX: '/v1/op/geo',
    REVERSE: '/v1/op/geo/reverse',
    FORWARD: '/v1/op/geo/forward',
    BATCH_REVERSE: '/v1/op/geo/batch-reverse',
    QUEUE_NAME: 'rev-geo',
  },
  
  // 任务管理相关
  TASKS: {
    PREFIX: '/v1/op/tasks',
    STATUS: '/v1/op/tasks/:jobId',
    EVENTS: '/v1/op/tasks/:jobId/events',
  },
};

/**
 * 服务地址配置
 */
export const SERVICE_ENDPOINTS = {
  OCR_SERVICE: process.env.OCR_SERVICE_URL || 'http://localhost:8866',
  ADDRESS_SERVICE: process.env.ADDRESS_SERVICE_URL || 'http://localhost:8866',
  GEO_SERVICE: process.env.GEO_SERVICE_URL || 'http://localhost:8866',
};

/**
 * 队列配置常量
 */
export const QUEUE_CONFIGS = {
  OCR_QUEUE: {
    name: 'ocr',
    concurrency: 5,
    attempts: 3,
    timeout: 180000, // 3分钟
  },
  
  ADDRESS_QUEUE: {
    name: 'extract-address',
    concurrency: 8,
    attempts: 2,
    timeout: 120000, // 2分钟
  },
  
  GEO_QUEUE: {
    name: 'rev-geo',
    concurrency: 10,
    attempts: 2,
    timeout: 60000, // 1分钟
  },
};

/**
 * 处理模式枚举
 */
export enum ProcessingMode {
  SYNC = 'sync',
  ASYNC = 'async',
  PROXY_ASYNC = 'proxy-async',
}
```

### 10.2 使用共享常量的最佳实践

1. **模块引用**：所有模块应引用同一个常量文件，而不是自行定义重复的路径和名称
2. **路径生成**：使用辅助函数生成路径，处理路径参数替换
3. **版本管理**：在常量中明确版本信息，便于版本迁移
4. **文档自动生成**：基于常量文件自动生成API文档，确保文档与代码一致

### 10.3 配置项与环境变量

网关模块需要考虑以下配置项：

1. **超时设置**
   - 不同服务和请求类型的超时时间
   - 异步代理模式的最大等待时间
   - 队列任务的处理超时时间

2. **资源限制**
   - 最大并发请求数
   - 客户端并发请求限制
   - 文件上传大小限制

3. **路由和端点配置**
   - 服务地址和端口
   - API前缀和版本
   - 健康检查路径

4. **环境变量示例**

```
# 服务地址
OCR_SERVICE_URL=http://python-service:8866
ADDRESS_SERVICE_URL=http://python-service:8866
GEO_SERVICE_URL=http://python-service:8866

# 资源限制
MAX_CONCURRENT_PROXY_REQUESTS=100
MAX_PROXY_REQUESTS_PER_CLIENT=5
MAX_FILE_SIZE=5242880 # 5MB

# 超时设置（毫秒）
OCR_TIMEOUT=30000
ADDRESS_TIMEOUT=10000
GEO_TIMEOUT=5000

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=password
```

## 11. 安全性考虑

### 11.1 网关层安全措施

网关模块应实施以下安全措施：

1. **输入验证**
   - 严格验证所有请求参数
   - 检查文件上传的MIME类型和大小
   - 验证Base64数据的合法性

2. **防止滥用**
   - 实施请求频率限制
   - 监控异常请求模式
   - 对大型或耗时请求设置特殊限制

3. **数据传输安全**
   - 所有数据传输必须加密
   - 敏感信息不应记录在日志中
   - 文件处理后及时清理临时文件

### 11.2 异步处理安全措施

针对异步处理场景的特定安全措施：

1. **任务认证**
   - 确保任务ID只能由有效客户端访问
   - 验证客户端获取结果的权限

2. **资源保护**
   - 对长时间运行的任务实施资源限制
   - 防止恶意任务导致拒绝服务

3. **数据隔离**
   - 确保不同客户端之间的任务数据严格隔离
   - 任务结果按照适当权限访问控制

## 12. 监控和可观测性

### 12.1 关键指标

网关模块应收集以下关键指标：

1. **性能指标**
   - 请求响应时间
   - 处理时间分布
   - 请求吞吐量

2. **资源使用**
   - 内存和CPU使用率
   - 连接数和并发请求数
   - 文件处理资源使用情况

3. **业务指标**
   - 各服务使用频率
   - 同步与异步请求比例
   - 处理成功率和错误率

### 12.2 日志和追踪

实施全面的日志和分布式追踪：

1. **结构化日志**
   - 使用统一的日志格式
   - 包含请求ID和关联信息
   - 设置适当的日志级别

2. **分布式追踪**
   - 在服务间传递追踪上下文
   - 记录完整的请求处理链路
   - 分析服务间调用关系

3. **告警机制**
   - 设置关键指标的告警阈值
   - 实施多级告警策略
   - 集成通知渠道

### 12.3 健康检查

提供全面的健康检查端点：

1. **基础检查**
   - 服务可用性验证
   - 数据库和Redis连接检查
   - 依赖服务健康状态

2. **深度检查**
   - 队列处理能力验证
   - 文件处理功能测试
   - 端到端业务流程检查

## 13. 文档和开发指南

### 13.1 API文档

网关模块应提供全面的API文档：

1. **OpenAPI规范**
   - 使用Swagger或类似工具
   - 详细描述请求参数和响应格式
   - 包含身份验证和授权信息

2. **使用示例**
   - 提供各API的完整使用示例
   - 包括不同处理模式的调用方法
   - 展示错误处理最佳实践

### 13.2 开发者指南

为开发者提供集成和扩展指南：

1. **快速入门**
   - 环境设置和配置说明
   - 基本请求示例
   - 认证和授权流程

2. **高级功能**
   - 异步处理模式详解
   - SSE连接处理最佳实践
   - 文件上传和处理指南

3. **常见问题解答**
   - 故障排除指南
   - 性能优化建议
   - 安全最佳实践

## 14. 扩展性设计

### 14.1 支持新服务类型

网关模块应设计为易于扩展以支持新的服务类型：

1. **模块化路由配置**
   - 独立的路由配置模块
   - 基于配置的路由生成
   - 支持动态路由注册

2. **插件架构**
   - 允许通过插件添加新功能
   - 标准化的插件接口
   - 热插拔式功能扩展

### 14.2 处理模式扩展

支持未来可能的新处理模式：

1. **模式注册机制**
   - 可扩展的处理模式注册系统
   - 标准化的模式处理接口
   - 配置驱动的模式选择逻辑

2. **自定义处理逻辑**
   - 允许开发者定义自定义处理逻辑
   - 与核心功能的无缝集成
   - 保持向后兼容性