// 测试服务映射的脚本
const testPaths = [
  '/v1/op/address/extract',
  '/v1/op/ocr/recognize',
  '/v1/op/geo/forward',
  '/v1/op/geo/reverse',
  '/address/extract',
  '/ocr/',
  '/geo/'
];

// 模拟ServiceMappingService的映射逻辑
function getServiceIdFromPath(path) {
  const mappings = [
    { patterns: ['/v1/op/ocr/file', '/ocr/file'], serviceId: 1, name: 'OCR文件上传识别' },
    { patterns: ['/v1/op/ocr/base64', '/ocr/base64', '/ocr'], serviceId: 2, name: 'OCR Base64图片识别' },
    { patterns: ['/v1/op/ocr/shentong/file', '/ocr/shentong/file'], serviceId: 3, name: '申通面单OCR文件上传识别' },
    { patterns: ['/v1/op/ocr/shentong/base64', '/ocr/shentong'], serviceId: 4, name: '申通面单OCR Base64识别' },
    { patterns: ['/v1/op/address/extract', '/address/extract', '/address'], serviceId: 5, name: '从文本中提取地址信息' },
    { patterns: ['/v1/op/address/normalize', '/address/normalize'], serviceId: 6, name: '地址标准化' },
    { patterns: ['/v1/op/geo/reverse', '/geo/reverse', '/geo/decode'], serviceId: 7, name: '逆地理编码（坐标转地址）' },
    { patterns: ['/v1/op/geo/forward', '/geo/forward', '/geo/encode', '/geo'], serviceId: 8, name: '正地理编码（地址转坐标）' }
  ];

  // 精确匹配
  for (const mapping of mappings) {
    if (mapping.patterns.includes(path)) {
      return { serviceId: mapping.serviceId, name: mapping.name, matchType: '精确匹配' };
    }
  }

  // 模糊匹配
  for (const mapping of mappings) {
    for (const pattern of mapping.patterns) {
      if (path.includes(pattern)) {
        return { serviceId: mapping.serviceId, name: mapping.name, matchType: '模糊匹配', pattern };
      }
    }
  }

  return null;
}

console.log('=== 服务路径映射测试 ===');
testPaths.forEach(path => {
  const result = getServiceIdFromPath(path);
  if (result) {
    console.log(`路径: ${path}`);
    console.log(`  → 服务ID: ${result.serviceId}`);
    console.log(`  → 服务名称: ${result.name}`);
    console.log(`  → 匹配类型: ${result.matchType}`);
    if (result.pattern) {
      console.log(`  → 匹配模式: ${result.pattern}`);
    }
    console.log('');
  } else {
    console.log(`路径: ${path} → 无匹配`);
    console.log('');
  }
});

console.log('=== 关键测试 ===');
const addressExtractPath = '/v1/op/address/extract';
const result = getServiceIdFromPath(addressExtractPath);
console.log(`地址解析接口路径: ${addressExtractPath}`);
console.log(`应该映射到服务ID 5，实际映射到: ${result ? result.serviceId : '无匹配'}`);
console.log(`映射是否正确: ${result && result.serviceId === 5 ? '✅ 正确' : '❌ 错误'}`);
