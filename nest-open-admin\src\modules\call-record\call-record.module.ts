import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CallRecordController } from './call-record.controller';
import { CallRecordService } from './call-record.service';
import { CallRecordEntity } from './entities/call-record.entity';
import { ApiUsageTrackerService } from './services/api-usage-tracker.service';
import { UserServiceEntity } from '../user-service/entities/user-service.entity';
import { ScheduleModule } from '@nestjs/schedule';
import { UsageTasksService } from './services/usage-tasks.service';
import { ApiKeyModule } from '../api-key/api-key.module';
import { UserModule } from '../user/user.module';
import { ServiceModule } from '../service/service.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([CallRecordEntity, UserServiceEntity]),
    forwardRef(() => ApiKeyModule),
    forwardRef(() => UserModule),
    ServiceModule,
    ScheduleModule.forRoot(),
  ],
  controllers: [CallRecordController],
  providers: [
    CallRecordService,
    ApiUsageTrackerService,
    UsageTasksService,
  ],
  exports: [CallRecordService, ApiUsageTrackerService],
})
export class CallRecordModule {}
