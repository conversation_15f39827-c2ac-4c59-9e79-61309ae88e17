import { ApiProperty } from '@nestjs/swagger';
import { ApiKeyStatus } from '../enums/api-key-status.enum';

/**
 * API密钥DTO
 */
export class ApiKeyDto {
  @ApiProperty({ description: 'API密钥ID' })
  id: number;

  @ApiProperty({ description: 'API密钥' })
  key: string;

  @ApiProperty({ 
    description: '密钥秘钥（仅创建时返回）',
    required: false
  })
  secretKey?: string;

  @ApiProperty({ description: '密钥名称' })
  name: string;

  @ApiProperty({ 
    description: '密钥状态', 
    enum: ApiKeyStatus
  })
  status: ApiKeyStatus;

  @ApiProperty({ 
    description: '密钥权限范围',
    type: [String]
  })
  scopes: string[];

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ 
    description: '过期时间',
    required: false
  })
  expiresAt?: Date;

  @ApiProperty({ 
    description: '最后使用时间',
    required: false
  })
  lastUsedAt?: Date;

  @ApiProperty({ description: '是否已查看' })
  isViewed: boolean;

  @ApiProperty({ 
    description: '允许的IP地址',
    required: false,
    type: [String]
  })
  allowedIps?: string[];

  @ApiProperty({
    description: '关联的服务ID',
    required: false
  })
  serviceId?: number;

  @ApiProperty({
    description: '密钥类型',
    example: 'user'
  })
  keyType: string;

  @ApiProperty({
    description: '更新时间'
  })
  updatedAt: Date;

  @ApiProperty({
    description: '用户ID'
  })
  userId: number;

  @ApiProperty({
    description: '今日调用次数',
    required: false,
    default: 0
  })
  todayCalls?: number;

  @ApiProperty({
    description: '本月调用次数',
    required: false,
    default: 0
  })
  monthlyCalls?: number;

  @ApiProperty({
    description: '总调用次数',
    required: false,
    default: 0
  })
  totalCalls?: number;

  @ApiProperty({
    description: '剩余可用次数',
    required: false
  })
  remainingCalls?: number;

  @ApiProperty({
    description: '配额限制',
    required: false
  })
  quotaLimit?: number;
}

/**
 * 分页API密钥结果DTO
 */
export class PaginatedApiKeyDto {
  @ApiProperty({
    description: 'API密钥列表',
    type: [ApiKeyDto]
  })
  items: ApiKeyDto[];

  @ApiProperty({
    description: '分页元数据',
    type: 'object',
    properties: {
      totalItems: {
        type: 'number',
        description: '总条目数'
      },
      itemsPerPage: {
        type: 'number',
        description: '每页条目数'
      },
      totalPages: {
        type: 'number',
        description: '总页数'
      },
      currentPage: {
        type: 'number',
        description: '当前页码'
      }
    }
  })
  meta: {
    totalItems: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
} 