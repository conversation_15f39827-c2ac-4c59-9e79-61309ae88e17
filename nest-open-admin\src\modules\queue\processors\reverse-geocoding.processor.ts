/**
 * 地理坐标逆解析处理器
 * 处理地理坐标逆解析任务的具体逻辑
 */

import { Process, Processor } from '@nestjs/bull';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Job } from 'bull';
import { ModuleRef } from '@nestjs/core';
import { QUEUE_NAMES } from '../queue.constants';
import { ReverseGeocodingTaskData, TaskResult } from '../interfaces/queue.interface';
import { AddressExecutorService } from '../../address/services/address-executor.service';
import { ApiUsageTrackerService } from '../../call-record/services/api-usage-tracker.service';

/**
 * 地理坐标逆解析处理器
 * 处理地理坐标逆解析任务的具体逻辑
 */
@Injectable()
@Processor(QUEUE_NAMES.REV_GEO)
export class ReverseGeocodingProcessor implements OnModuleInit {
  private readonly logger = new Logger(ReverseGeocodingProcessor.name);
  private addressExecutorService: AddressExecutorService | null = null;
  private apiUsageTrackerService: ApiUsageTrackerService | null = null;

  constructor(private readonly moduleRef: ModuleRef) {}

  /**
   * 模块初始化时，动态获取所需服务
   */
  async onModuleInit() {
    try {
      // 尝试获取地址执行器服务
      try {
        this.addressExecutorService = await this.moduleRef.resolve(AddressExecutorService, undefined, { strict: false });
        this.logger.log('成功获取地址执行器服务');
      } catch (error) {
        this.logger.error(`获取地址执行器服务失败: ${error.message}`);
        this.addressExecutorService = null;
      }
      
      // 尝试获取API使用跟踪服务
      try {
        this.apiUsageTrackerService = await this.moduleRef.resolve(ApiUsageTrackerService, undefined, { strict: false });
        this.logger.log('成功获取API使用跟踪服务');
      } catch (error) {
        this.logger.warn(`获取API使用跟踪服务失败: ${error.message}`);
        this.apiUsageTrackerService = null;
      }
    } catch (error) {
      this.logger.error(`初始化服务失败: ${error.message}`);
    }
  }

  /**
   * 处理单个坐标逆解析任务
   */
  @Process('reverse_geocoding')
  async processSingle(job: Job<ReverseGeocodingTaskData>): Promise<TaskResult> {
    try {
      this.logger.log(`开始处理坐标逆解析任务: ${job.id}`);
      await this.updateProgress(job, 0, '任务开始处理');
      
      // 验证任务数据
      this.validateJobData(job.data);
      
      // 更新进度
      await this.updateProgress(job, 20, '正在准备坐标逆解析');
      
      // 获取地址执行器服务（如果尚未获取）
      if (!this.addressExecutorService) {
        this.addressExecutorService = await this.getServiceSafely<AddressExecutorService>(
          AddressExecutorService,
          () => this.createMockAddressExecutorService()
        );
      }
      
      // 更新进度
      await this.updateProgress(job, 40, '正在进行坐标逆解析');
      
      // 确定要使用的坐标
      let latitude: number;
      let longitude: number;
      
      // 优先使用直接提供的坐标，其次使用coordinates数组中的第一个坐标
      if (job.data.latitude !== undefined && job.data.longitude !== undefined) {
        latitude = job.data.latitude;
        longitude = job.data.longitude;
        this.logger.debug(`使用直接提供的坐标: ${latitude}, ${longitude}`);
      } else if (job.data.coordinates && job.data.coordinates.length > 0) {
        latitude = job.data.coordinates[0].lat;
        longitude = job.data.coordinates[0].lng;
        this.logger.debug(`使用coordinates数组中的坐标: ${latitude}, ${longitude}`);
      } else {
        throw new Error('无法确定坐标数据');
      }
      
      // 处理坐标逆解析
      const result = await this.addressExecutorService.reverseGeocode(
        {
          latitude,
          longitude
        },
        {
          userId: job.data.userId,
          apiKey: job.data.apiKeyId,
          serviceCode: job.data.serviceId?.toString(),
        }
      );
      
      // 更新进度
      await this.updateProgress(job, 80, '正在更新API使用记录');
      
      // 记录API使用情况
      if (job.data.userId && job.data.apiKeyId && job.data.serviceId) {
        try {
          // 获取API使用跟踪服务（如果尚未获取）
          if (!this.apiUsageTrackerService) {
            this.apiUsageTrackerService = await this.getServiceSafely<ApiUsageTrackerService>(
              ApiUsageTrackerService,
              () => this.createMockApiUsageTrackerService()
            );
          }
          
          if (this.apiUsageTrackerService) {
            await this.apiUsageTrackerService.trackApiCall({
              userId: job.data.userId,
              apiKeyId: job.data.apiKeyId,
              serviceId: job.data.serviceId,
              endpoint: '坐标逆解析',
              responseTime: 0,
              status: result.success ? 'completed' : 'failed',
              requestId: job.data.requestId || `req_${Date.now()}`,
              jobId: job.id.toString(),
              error: result.error,
            });
          }
        } catch (error) {
          this.logger.error(`记录API使用失败: ${error.message}`, error.stack);
          // 不中断主流程，继续返回结果
        }
      }
      
      await this.updateProgress(job, 100, '任务处理完成');
      this.logger.log(`坐标逆解析任务处理成功: ${job.id}`);
      
      return {
        success: result.success,
        data: result.address,
        error: result.error,
        duration: 0,
      };
    } catch (error) {
      this.logger.error(`坐标逆解析任务处理失败: ${job.id}, 错误: ${error.message}`, error.stack);
      
      try {
        await this.updateProgress(job, 100, `处理失败: ${error.message}`);
      } catch (err) {
        this.logger.error(`更新任务失败状态时出错: ${err.message}`);
      }
      
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 处理批量坐标逆解析任务
   */
  @Process('batch_reverse_geocoding')
  async processBatch(job: Job<ReverseGeocodingTaskData>): Promise<TaskResult> {
    try {
      this.logger.log(`开始处理批量坐标逆解析任务: ${job.id}`);
      await this.updateProgress(job, 0, '任务开始处理');
      
      // 验证任务数据
      this.validateJobData(job.data);
      
      // 更新进度
      await this.updateProgress(job, 20, '正在准备批量坐标逆解析');
      
      // 获取地址执行器服务（如果尚未获取）
      if (!this.addressExecutorService) {
        this.addressExecutorService = await this.getServiceSafely<AddressExecutorService>(
          AddressExecutorService,
          () => this.createMockAddressExecutorService()
        );
      }
      
      // 更新进度
      await this.updateProgress(job, 40, '正在进行批量坐标逆解析');
      
      // 处理批量坐标逆解析
      const result = await this.addressExecutorService.batchReverseGeocode(
        {
          coordinates: job.data.coordinates.map(coord => ({
            latitude: coord.lat,
            longitude: coord.lng
          }))
        },
        {
          userId: job.data.userId,
          apiKey: job.data.apiKeyId,
          serviceCode: job.data.serviceId?.toString(),
        }
      );
      
      // 更新进度
      await this.updateProgress(job, 80, '正在更新API使用记录');
      
      // 记录API使用情况
      if (job.data.userId && job.data.apiKeyId && job.data.serviceId) {
        try {
          // 获取API使用跟踪服务（如果尚未获取）
          if (!this.apiUsageTrackerService) {
            this.apiUsageTrackerService = await this.getServiceSafely<ApiUsageTrackerService>(
              ApiUsageTrackerService,
              () => this.createMockApiUsageTrackerService()
            );
          }
          
          if (this.apiUsageTrackerService) {
            await this.apiUsageTrackerService.trackApiCall({
              userId: job.data.userId,
              apiKeyId: job.data.apiKeyId,
              serviceId: job.data.serviceId,
              endpoint: '批量坐标逆解析',
              responseTime: 0,
              status: result.success ? 'completed' : 'failed',
              requestId: job.data.requestId || `req_${Date.now()}`,
              jobId: job.id.toString(),
              error: result.error,
            });
          }
        } catch (error) {
          this.logger.error(`记录API使用失败: ${error.message}`, error.stack);
          // 不中断主流程，继续返回结果
        }
      }
      
      await this.updateProgress(job, 100, '任务处理完成');
      this.logger.log(`批量坐标逆解析任务处理成功: ${job.id}`);
      
      return {
        success: result.success,
        data: result.results,
        error: result.error,
        duration: 0,
      };
    } catch (error) {
      this.logger.error(`批量坐标逆解析任务处理失败: ${job.id}, 错误: ${error.message}`, error.stack);
      
      try {
        await this.updateProgress(job, 100, `处理失败: ${error.message}`);
      } catch (err) {
        this.logger.error(`更新任务失败状态时出错: ${err.message}`);
      }
      
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 验证任务数据
   */
  private validateJobData(data: ReverseGeocodingTaskData): void {
    if (!data) {
      throw new Error('任务数据不能为空');
    }
    
    // 检查直接提供的坐标
    if (data.latitude !== undefined && data.longitude !== undefined) {
      // 直接使用latitude和longitude
      if (typeof data.latitude !== 'number' || typeof data.longitude !== 'number') {
        throw new Error('坐标格式无效，latitude和longitude必须为数字');
      }
      
      if (data.latitude < -90 || data.latitude > 90 || data.longitude < -180 || data.longitude > 180) {
        throw new Error(`坐标值超出范围: latitude=${data.latitude}, longitude=${data.longitude}`);
      }
      
      // 如果没有coordinates数组，创建一个
      if (!data.coordinates || !Array.isArray(data.coordinates) || data.coordinates.length === 0) {
        data.coordinates = [{ lat: data.latitude, lng: data.longitude }];
      }
      return;
    }
    
    // 检查coordinates数组
    if (!data.coordinates || !Array.isArray(data.coordinates) || data.coordinates.length === 0) {
      throw new Error('坐标数据不能为空');
    }

    if (data.coordinates.length > 50) {
      throw new Error('批量处理最多支持50个坐标点');
    }

    // 验证每个坐标的格式
    for (const coord of data.coordinates) {
      if (typeof coord.lat !== 'number' || typeof coord.lng !== 'number') {
        throw new Error('坐标格式无效，lat和lng必须为数字');
      }
      
      if (coord.lat < -90 || coord.lat > 90 || coord.lng < -180 || coord.lng > 180) {
        throw new Error(`坐标值超出范围: lat=${coord.lat}, lng=${coord.lng}`);
      }
    }
  }

  /**
   * 安全地获取服务实例
   */
  private async getServiceSafely<T>(
    serviceToken: any,
    fallbackFactory?: () => T
  ): Promise<T> {
    try {
      return await this.moduleRef.resolve(serviceToken, undefined, { strict: false });
    } catch (error) {
      this.logger.warn(
        `无法解析服务 ${serviceToken.name || serviceToken}，使用回退实现: ${error.message}`
      );
      
      if (fallbackFactory) {
        return fallbackFactory();
      }
      
      throw new Error(`无法获取服务 ${serviceToken.name || serviceToken}，且未提供回退实现`);
    }
  }

  /**
   * 处理任务进度更新
   */
  private async updateProgress(
    job: Job,
    progress: number,
    message?: string
  ): Promise<void> {
    try {
      await job.progress({
        progress: Math.min(100, Math.max(0, progress)),
        message: message || `处理进度: ${progress}%`,
        timestamp: Date.now(),
      });
    } catch (error) {
      this.logger.error(`更新任务进度失败: ${error.message}`);
    }
  }

  /**
   * 创建模拟地址执行器服务
   */
  private createMockAddressExecutorService(): AddressExecutorService {
    const mockService = {
      extractFromText: async () => {
        this.logger.warn('使用模拟地址执行器服务');
        return {
          success: false,
          addresses: [],
          error: '无法获取地址执行器服务，使用模拟服务',
          processingTime: 0,
        };
      },
      reverseGeocode: async () => {
        this.logger.warn('使用模拟地址执行器服务');
        return { 
          success: false, 
          error: '无法获取地址执行器服务，使用模拟服务' 
        };
      },
      batchReverseGeocode: async () => {
        this.logger.warn('使用模拟地址执行器服务');
        return { 
          success: false, 
          error: '无法获取地址执行器服务，使用模拟服务' 
        };
      },
    };
    
    return mockService as unknown as AddressExecutorService;
  }

  /**
   * 创建模拟API使用跟踪服务
   */
  private createMockApiUsageTrackerService(): ApiUsageTrackerService {
    const mockService = {
      trackApiCall: async () => {
        this.logger.warn('使用模拟API使用跟踪服务');
        return 'mock-call-record-id';
      },
      trackApiUsage: async () => {
        this.logger.warn('使用模拟API使用跟踪服务');
        return true;
      },
    };
    
    return mockService as unknown as ApiUsageTrackerService;
  }
} 