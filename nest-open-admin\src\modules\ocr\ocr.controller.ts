import { Controller, Post, Get, Param, Query, UploadedFile, UseInterceptors, ParseIntPipe, Body, BadRequestException } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { OcrService } from './ocr.service';
import { ApiTags, ApiOperation, ApiConsumes, ApiBody, ApiParam, ApiQuery } from '@nestjs/swagger';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { Express } from 'express';
import { CurrentApiKey } from '../../common/decorators/current-api-key.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { AuthUser, AuthApiKey } from '../../common/types/auth.types';
import { Logger } from '@nestjs/common';
import { UseAuthStrategy, AuthStrategy } from '../../common/decorators/auth-strategy.decorator';
import { Public } from '@/common/decorators/public.decorator';

@ApiTags('面单OCR识别')
@Controller('ocr')
@UseAuthStrategy(AuthStrategy.API_KEY)
export class OcrController {
  private readonly logger = new Logger(OcrController.name);

  constructor(private readonly ocrService: OcrService) {}

  @Get('test')
  @Public()
  @ApiOperation({ summary: '测试' })
  async getTest() {
    return 'hello world';
  }

  /**
   * 上传图片进行OCR识别 - 文件上传方式
   */
  @Post('upload')
  @ApiOperation({ summary: '上传图片进行OCR识别（文件上传方式）' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: '要识别的图片文件',
        },
      },
    },
  })
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './up-ocr',
        filename: (req, file, cb) => {
          // 生成唯一文件名
          const randomName = Array(32)
            .fill(null)
            .map(() => Math.round(Math.random() * 16).toString(16))
            .join('');
          return cb(null, `${randomName}${extname(file.originalname)}`);
        },
      }),
      limits: {
        fileSize: 5 * 1024 * 1024, // 限制5MB
      },
      fileFilter: (req, file, cb) => {
        // 只允许图片格式
        if (!file.originalname.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/)) {
          return cb(new Error('只允许上传图片文件!'), false);
        }
        cb(null, true);
      },
    }),
  )
  async uploadFile(
    @UploadedFile() file: any,
    @CurrentUser() user: AuthUser,
    @CurrentApiKey() apiKey: AuthApiKey,
    @Body() body: any,
  ) {
    // 记录调用信息，让网关服务能够正确记录API调用
    const callInfo = {
      user,
      apiKey,
      serviceCode: 'OCR_EXPRESS'
    };
    
    // 检查是否传入了rawData（从网关传来的OCR结果）
    if (body && body.rawData && body.rawData.results) {
      // 直接解析网关传来的OCR结果
      return {
        success: true,
        data: await this.ocrService.parseWaybillData(body.rawData.results),
        processingTime: body.process_time || 0,
      };
    }
    
    // 常规文件上传处理
    return this.ocrService.processOcrRequest(file, callInfo);
  }

  /**
   * 上传图片进行OCR识别 - Base64方式
   */
  @Post('recognize')
  @ApiOperation({ summary: '上传图片进行OCR识别（Base64方式）' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        image_base64: {
          type: 'string',
          description: 'Base64编码的图片数据',
        },
        filename: {
          type: 'string',
          description: '可选的文件名',
        },
      },
      required: ['image_base64'],
    },
  })
  async recognizeFromBase64(
    @Body('image_base64') imageBase64: string,
    @Body('filename') filename: string,
    @CurrentUser() user: AuthUser,
    @CurrentApiKey() apiKey: AuthApiKey
  ) {
    // 记录调用信息，让网关服务能够正确记录API调用
    const callInfo = {
      user,
      apiKey,
      serviceCode: 'OCR_EXPRESS'
    };
    
    return this.ocrService.processOcrRequestFromBase64(imageBase64, filename, callInfo);
  }

  /**
   * 获取OCR记录列表
   */
  @Get()
  @ApiOperation({ summary: '获取OCR记录列表' })
  @ApiQuery({ name: 'page', description: '页码', required: false, type: Number })
  @ApiQuery({ name: 'limit', description: '每页数量', required: false, type: Number })
  async findAll(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    return this.ocrService.findAll(page, limit);
  }

  /**
   * 获取单条OCR记录
   */
  @Get(':id')
  @ApiOperation({ summary: '获取单条OCR记录' })
  @ApiParam({ name: 'id', description: '记录ID', type: Number })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.ocrService.findOne(id);
  }

  /**
   * 解析OCR结果
   */
  @Post('parse')
  @ApiOperation({ summary: '解析OCR识别结果' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        rawData: {
          type: 'object',
          properties: {
            results: {
              type: 'array',
              items: {
                type: 'array',
                items: {
                  type: 'any',
                  description: 'OCR识别结果',
                },
              },
            },
          },
          required: ['results'],
        },
      },
      required: ['rawData'],
    },
  })
  async parseOcrResult(
    @Body() body: any,
    @CurrentUser() user: AuthUser,
    @CurrentApiKey() apiKey: AuthApiKey,
  ) {
    if (!body.rawData || !body.rawData.results) {
      throw new BadRequestException('缺少OCR结果数据');
    }

    this.logger.debug(`解析OCR结果，收到${body.rawData.results.length}条记录`);
    
    // 调用OCR服务解析结果
    const parsedData = await this.ocrService.parseWaybillData(body.rawData.results);
    
    return {
      success: true,
      data: parsedData,
      processingTime: body.process_time || 0,
    };
  }
}