# 密钥模块需求文档

## 1. 文档概述

本文档描述了开放平台密钥模块的需求规范，该模块基于单一职责原则设计，专注于API密钥的管理和操作。密钥模块负责处理API密钥的创建、更新、查询和删除等操作，确保开放平台用户可以安全地使用密钥访问API服务。

## 2. 功能需求

### 2.1 核心功能

- **密钥生命周期管理**
  - 创建密钥：生成API密钥和密钥秘钥(SecretKey)
  - 重新生成密钥：更新密钥并同步缓存
  - 撤销密钥：标记密钥为已撤销状态
  - 查看标记：跟踪密钥是否已被用户查看

- **密钥查询与验证**
  - 按条件查询密钥列表
  - 按用户查询关联密钥
  - 高性能密钥验证

- **缓存同步**
  - 密钥信息同步至缓存系统
  - 缓存一致性保证
  - 缓存失效策略

### 2.2 密钥属性

- **密钥ID**：唯一标识符
- **密钥值**：API认证密钥字符串
- **密钥秘钥**：二次验证密钥字符串
- **用户关联**：关联的用户ID
- **作用域**：密钥可访问的API范围
- **状态控制**：活跃/非活跃/已撤销
- **时效性**：创建时间和过期时间
- **使用跟踪**：最后使用时间
- **访问限制**：IP地址白名单

### 2.3 非功能需求

- **高性能**：密钥验证响应时间<50ms
- **高可用性**：支持集群部署，无单点故障
- **数据一致性**：确保密钥在缓存和数据库间的一致性
- **安全性**：采用强加密算法存储敏感信息

## 3. 技术规范

### 3.1 密钥模型

```typescript
// 简化示意
@Entity('api_keys')
export class ApiKey {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  key: string;

  @Column({ select: false })
  encryptedSecretKey: string;

  @Column()
  userId: number;

  @Column({ nullable: true })
  name: string;

  @Column({ enum: ApiKeyStatus, default: ApiKeyStatus.ACTIVE })
  status: ApiKeyStatus;

  @Column('simple-array', { nullable: true })
  scopes: string[];

  @Column()
  createdAt: Date;

  @Column({ nullable: true })
  expiresAt: Date;

  @Column({ nullable: true })
  lastUsedAt: Date;

  @Column({ default: false })
  isViewed: boolean;

  @Column('simple-array', { nullable: true })
  allowedIps: string[];
}

export enum ApiKeyStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  REVOKED = 'revoked',
  EXPIRED = 'expired'
}
```

### 3.2 密钥生成策略

```typescript
// 简化示意
async generateApiKey(userId: number, options = {}): Promise<ApiKeyDto> {
    // 生成随机密钥
    const keyPrefix = 'ak-';
    const secretKeyPrefix = 'sk-';
    const apiKey = keyPrefix + crypto.randomBytes(16).toString('hex');
    const secretKey = secretKeyPrefix + crypto.randomBytes(32).toString('hex');
    
  // 加密密钥秘钥
    const encryptedSecretKey = await this.cryptoService.encrypt(secretKey);
    
  // 创建密钥记录并保存
  const newApiKey = await this.repository.save({
      key: apiKey,
      userId,
    name: options.name || `API Key ${new Date().toISOString().split('T')[0]}`,
      status: ApiKeyStatus.ACTIVE,
      scopes: options.scopes || ['*'],
      createdAt: new Date(),
      expiresAt: options.expiresAt,
      allowedIps: options.allowedIps,
      encryptedSecretKey,
    });
    
    // 同步到缓存
    await this.syncApiKeyToCache(newApiKey.id);
    
  // 返回密钥信息（仅创建时返回明文秘钥）
    return {
      id: newApiKey.id,
      key: apiKey,
    secretKey,  // 明文秘钥，仅创建时返回
    // 其他字段...
  };
}
```

### 3.3 缓存策略

```typescript
// 简化示意
  async syncApiKeyToCache(apiKeyId: string): Promise<void> {
  const apiKey = await this.repository.findOne({
      where: { id: apiKeyId },
      select: ['id', 'key', 'userId', 'status', 'scopes', 'expiresAt', 'allowedIps', 'encryptedSecretKey']
    });
    
    if (!apiKey) {
      await this.cacheManager.del(`api-key:${apiKeyId}`);
      await this.cacheManager.del(`api-key-value:${apiKey?.key}`);
      return;
    }
    
    const cacheData = {
      id: apiKey.id,
      key: apiKey.key,
      userId: apiKey.userId,
      status: apiKey.status,
      scopes: apiKey.scopes,
      expiresAt: apiKey.expiresAt,
      allowedIps: apiKey.allowedIps,
      encryptedSecretKey: apiKey.encryptedSecretKey
    };
    
    // 设置缓存，有效期24小时
    await this.cacheManager.set(`api-key:${apiKeyId}`, cacheData, { ttl: 86400 });
    await this.cacheManager.set(`api-key-value:${apiKey.key}`, cacheData, { ttl: 86400 });
}
```

## 4. 系统架构

### 4.1 模块结构

```
api-key/
├── dto/
│   ├── create-api-key.dto.ts      # 创建密钥DTO
│   ├── update-api-key.dto.ts      # 更新密钥DTO
│   ├── api-key.dto.ts             # 密钥数据传输对象
│   └── api-key-query.dto.ts       # 查询条件DTO
├── entities/
│   └── api-key.entity.ts          # 密钥实体定义
├── services/
│   ├── api-key.service.ts         # 密钥核心服务
│   └── api-key-cache.service.ts   # 密钥缓存服务
├── api-key.controller.ts          # 密钥控制器
├── api-key.module.ts              # 密钥模块定义
└── api-key-status.enum.ts         # 密钥状态枚举
```

### 4.2 密钥验证流程

```
graph TD
    A[接收API请求] --> B{验证密钥}
    B -->|检查缓存| C{缓存存在?}
    C -->|是| D[验证密钥合法性]
    C -->|否| E[从数据库加载]
    E --> F[同步到缓存]
    F --> D
    D --> G{密钥有效?}
    G -->|是| H[允许访问API]
    G -->|否| I[返回401错误]
```

## 5. 接口定义

### 5.1 创建密钥

```
POST /api-keys
Content-Type: application/json
Authorization: Bearer <token>

Request:
{
  "name": "开发环境密钥",
  "scopes": ["read:user", "write:order"],
  "expiresAt": "2023-12-31T23:59:59Z",
  "allowedIps": ["***********", "10.0.0.*"]
}

Response:
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "key": "ak-a1b2c3d4e5f6g7h8i9j0",
  "secretKey": "sk-z9y8x7w6v5u4t3s2r1q0p9o8n7m6l5k4",
  "name": "开发环境密钥",
  "status": "active",
  "scopes": ["read:user", "write:order"],
  "createdAt": "2023-01-15T08:30:00Z",
  "expiresAt": "2023-12-31T23:59:59Z",
  "isViewed": false,
  "allowedIps": ["***********", "10.0.0.*"]
}
```

### 5.2 重新生成密钥

```
PATCH /api-keys/{id}/regenerate
Authorization: Bearer <token>

Response:
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "key": "ak-k1l2m3n4o5p6q7r8s9t0",
  "secretKey": "sk-a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
  "status": "active",
  "isViewed": false,
  // 其他字段...
}
```

## 6. 安全措施

### 6.1 密钥存储安全

- **加密存储**：密钥秘钥使用强加密算法加密存储
- **秘钥一次性**：密钥秘钥仅在创建和重新生成时返回
- **字段隐藏**：敏感字段通过select: false从默认查询中排除

### 6.2 访问控制

- **权限分离**：用户只能操作自己的密钥
- **特权操作**：管理员操作需要额外权限
- **操作审计**：所有密钥操作记录详细日志

### 6.3 防篡改措施

- **数字签名**：验证密钥完整性
- **随机化**：使用密码学安全的随机源生成密钥
- **密钥隔离**：密钥值与用户ID无关联性

## 7. 后续优化方向

- **自动轮换**：支持密钥的自动定期轮换
- **使用分析**：密钥使用模式分析和异常检测
- **细粒度控制**：更精细的API权限控制
- **可视化管理**：开发密钥管理界面 