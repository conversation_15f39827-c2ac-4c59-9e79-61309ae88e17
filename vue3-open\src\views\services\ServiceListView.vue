<template>
  <div class="service-list-page">
    <div class="page-header">
      <div class="container">
        <h1 class="page-title">服务列表</h1>
        <p class="page-subtitle">探索我们提供的AI服务，选择适合您需求的解决方案</p>
      </div>
    </div>

    <div class="page-content">
      <div class="container">
        <!-- 服务分类筛选 -->
        <div class="filter-section">
          <el-tabs v-model="activeCategory" @tab-change="handleCategoryChange">
            <el-tab-pane v-for="category in categories" :key="category.value" :label="category.label"
              :name="category.value" />
          </el-tabs>
        </div>

        <!-- 服务列表 -->
        <div class="services-grid" v-if="filteredServices.length" v-loading="loading">
          <div class="service-card" v-for="service in filteredServices" :key="service.id"
            @click="viewServiceDetail(service.code)">
            <div class="service-header">
              <div class="service-icon">
                <el-icon size="40" :color="getServiceColor(service.type)">
                  <component :is="getServiceIcon(service.type)" />
                </el-icon>
              </div>
              <div class="service-status">
                <el-tag :type="getServiceStatusTypeValue(service.status)" size="small">
                  {{ getServiceStatusText(service.status) }}
                </el-tag>
              </div>
            </div>

            <div class="service-info">
              <h3 class="service-name">{{ service.name }}</h3>
              <p class="service-description">{{ service.description }}</p>

              <div class="service-features">
                <el-tag v-for="feature in getFeaturesList(service.features)" :key="feature" size="small"
                  class="feature-tag">
                  {{ feature }}
                </el-tag>
              </div>

              <div class="service-pricing">
                <span class="price">¥{{ getServicePriceValue(service) }}</span>
                <span class="unit">/ 次</span>
              </div>
            </div>

            <div class="service-actions">
              <el-button type="primary" size="small" @click.stop="tryService(service)">
                立即体验
              </el-button>
              <el-button size="small" @click.stop="viewDocs(service.code)">
                查看文档
              </el-button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && filteredServices.length === 0" class="empty-state">
          <el-empty description="暂无相关服务" />
        </div>

        <!-- 分页 -->
        <div class="pagination-container" v-if="totalPages > 1">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Picture,
  ChatDotRound,
  MagicStick,
  DataAnalysis,
  Location,
  Document
} from '@element-plus/icons-vue'
import { serviceApi } from '@/api/service'
import {
  SERVICE_TYPE_LABELS
} from '@/utils/serviceUtils'

interface Service {
  id: number
  code: string
  name: string
  description: string
  type: string
  status: string
  pricingModel: string
  unitPrice: number
  endpoint: string
  sortOrder: number
  features?: string
  createdAt: string
  updatedAt: string
  price?: number // 新增价格字段
  pricing?: { price: number } // 新增价格对象字段
}

interface ServiceData {
  data: Service[]
  total: number
  page: number
  limit: number
  totalPages: number
}

interface ApiResponse {
  success: boolean
  code: number
  message: string
  timestamp: string
  data: ServiceData
  requestId: string
}

const router = useRouter()

const loading = ref(false)
const activeCategory = ref('all')
const services = ref<Service[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const totalPages = ref(0)

// 服务分类 - 与后端ServiceType枚举对应
const categories = ref([
  { value: 'all', label: '全部' },
  { value: 'ocr', label: 'OCR识别' },
  { value: 'nlp', label: '自然语言处理' },
  { value: 'geo', label: '地理编码' },
  { value: 'ai_service', label: 'AI服务' },
  { value: 'cv', label: '计算机视觉' },
  { value: 'data', label: '数据处理' },
  { value: 'other', label: '其他服务' }
])

// 根据服务类型获取图标
const getServiceIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    'ai_service': MagicStick,
    'ocr': Picture,
    'nlp': ChatDotRound,
    'cv': Picture,
    'geo': Location,
    'data': DataAnalysis,
    'other': Document
  }
  return iconMap[type] || Document
}

// 根据服务类型获取颜色
const getServiceColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'ai_service': '#8b5cf6',
    'ocr': '#3b82f6',
    'nlp': '#10b981',
    'cv': '#f59e0b',
    'geo': '#06b6d4',
    'data': '#6b7280',
    'other': '#9ca3af'
  }
  return colorMap[type] || '#6b7280'
}

// 将特性字符串转换为数组
const getFeaturesList = (features: string | string[]) => {
  if (typeof features === 'string') {
    return features ? features.split(',').map(f => f.trim()) : []
  }
  return features || []
}

// 获取服务状态文本
const getServiceStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'active': '可用',
    'inactive': '不可用',
    'maintenance': '维护中',
    'deprecated': '已废弃'
  }
  return statusMap[status] || '未知'
}

// 获取服务状态标签类型 - 直接使用导入的工具函数
const getServiceStatusTypeValue = (status: string) => {
  const statusMap: Record<string, string> = {
    'active': 'success',
    'inactive': 'info',
    'maintenance': 'warning',
    'deprecated': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取服务价格
const getServicePriceValue = (service: Service) => {
  // 优先使用unitPrice字段
  if (service.unitPrice !== undefined) {
    return service.unitPrice
  }
  // 其次使用price字段
  if (service.price !== undefined) {
    return service.price
  }
  // 最后使用pricing字段
  if (service.pricing?.price !== undefined) {
    return service.pricing.price
  }
  return 0
}

// 筛选后的服务
const filteredServices = computed(() => {
  if (!services.value || services.value.length === 0) {
    return []
  }

  if (activeCategory.value === 'all') {
    return services.value
  }
  return services.value.filter(service => service.type === activeCategory.value)
})

// 处理分类切换
const handleCategoryChange = (category: string) => {
  activeCategory.value = category
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadServices()
}

// 处理每页条数变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadServices()
}

// 查看服务详情
const viewServiceDetail = (serviceCode: string) => {
  router.push(`/services/${serviceCode}`)
}

// 体验服务
const tryService = (service: Service) => {
  router.push({
    path: '/playground',
    query: { 
      code: service.code,
      serviceType: service.type,
      endpoint: service.endpoint
    }
  })
}

// 查看文档
const viewDocs = (serviceCode: string) => {
  // 根据服务代码映射到文档区域ID
  const docSectionMap: Record<string, string> = {
    // OCR服务
    'OCR_UPLOAD': 'ocr-upload',                    // OCR文件上传识别
    'OCR_RECOGNIZE': 'ocr-recognize',              // OCR Base64图片识别
    'OCR_STO_UPLOAD': 'ocr-sto-upload',            // 申通面单OCR文件上传识别
    'OCR_STO_RECOGNIZE': 'ocr-sto-recognize',      // 申通面单OCR Base64识别
    
    // 地址服务
    'ADDRESS_EXTRACT': 'address-extract',          // 从文本中提取地址信息
    'ADDRESS_NORMALIZE': 'address-normalize',      // 地址标准化
    
    // 地理服务
    'GEO_REVERSE': 'geo-reverse',                  // 逆地理编码（坐标转地址）
    'GEO_FORWARD': 'geo-forward',                  // 正地理编码（地址转坐标）
    
    // 其他服务可以继续添加映射
  }

  // 获取对应的文档区域ID，如果没有映射则使用服务代码作为ID
  const docSection = docSectionMap[serviceCode] || serviceCode
  // 跳转到文档页面并定位到对应区域
  router.push(`/docs#${docSection}`)
}

// 加载服务列表
const loadServices = async () => {
  try {
    loading.value = true
    console.log('开始加载服务列表...', {
      currentPage: currentPage.value,
      pageSize: pageSize.value
    })
    
    const response = await serviceApi.getServices({
      page: currentPage.value,
      pageSize: pageSize.value
    })
    console.log('服务列表API响应:', response)

    // 处理响应数据 - 后端返回格式为 { success: true, data: { data: Service[], meta: {...} } }
    if (response && response.data) {
      // 如果response.data本身就是服务数组（直接返回的情况）
      if (Array.isArray(response.data)) {
        services.value = response.data
        total.value = response.data.length
        totalPages.value = 1
        console.log('响应是直接数组格式，服务数量:', response.data.length)
      }
      // 如果response.data包含data和meta字段（标准分页格式）
      else if (response.data.data && Array.isArray(response.data.data)) {
        services.value = response.data.data
        total.value = response.data.meta?.total || 0
        totalPages.value = response.data.meta?.totalPages || 0
        console.log('服务列表加载成功:', {
          servicesCount: services.value.length,
          total: total.value,
          totalPages: totalPages.value,
          currentPage: response.data.meta?.page || 1,
          services: services.value.map(s => ({ 
            id: s.id, 
            name: s.name, 
            type: s.type, 
            status: s.status,
            code: s.code,
            endpoint: s.endpoint
          }))
        })
      }
      // 其他格式
      else {
        services.value = []
        total.value = 0
        totalPages.value = 0
        console.warn('API返回数据格式不正确:', response)
      }
    } else {
      services.value = []
      total.value = 0
      totalPages.value = 0
      console.warn('API返回数据为空或格式不正确:', response)
    }
  } catch (error) {
    console.error('加载服务列表失败:', error)
    console.error('错误详情:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    })
    ElMessage.error('加载服务列表失败')
    services.value = []
    total.value = 0
    totalPages.value = 0
  } finally {
    loading.value = false
    console.log('服务列表加载完成，loading状态:', loading.value)
  }
}

// 监听分类变化
watch(activeCategory, () => {
  // 如果切换分类，重置页码
  if (currentPage.value !== 1) {
    currentPage.value = 1
  }
})

onMounted(async () => {
  // 初始加载时获取所有服务数据，确保有完整的数据用于跳转
  await loadServices()
  
  console.log('服务列表页面初始化完成:', {
    totalServices: services.value.length,
    categories: categories.value,
    services: services.value.map(s => ({ 
      id: s.id, 
      name: s.name, 
      type: s.type, 
      code: s.code,
      endpoint: s.endpoint 
    }))
  })
})
</script>

<style scoped>
.service-list-page {
  min-height: calc(100vh - 70px);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.page-header {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-bottom: none;
  padding: 60px 0;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.page-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 16px 0;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  text-align: center;
  font-weight: 400;
}

.page-content {
  padding: 40px 0;
}

.filter-section {
  margin-bottom: 30px;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  min-height: 400px;
}

.service-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 28px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.2);
}

.service-card:hover::before {
  transform: scaleX(1);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.service-icon {
  padding: 16px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.service-info {
  flex: 1;
  margin-bottom: 20px;
}

.service-name {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 12px 0;
}

.service-description {
  color: #64748b;
  line-height: 1.7;
  margin: 0 0 20px 0;
  font-size: 0.95rem;
}

.service-features {
  margin-bottom: 16px;
}

.feature-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.service-pricing {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.price {
  font-size: 1.6rem;
  font-weight: 700;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.unit {
  color: #64748b;
  font-size: 0.9rem;
  font-weight: 500;
}

.service-actions {
  display: flex;
  gap: 12px;
}

.service-actions .el-button {
  flex: 1;
}

.service-actions .el-button {
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.service-actions .el-button--primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.service-actions .el-button--primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.service-actions .el-button:not(.el-button--primary) {
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #3b82f6;
}

.service-actions .el-button:not(.el-button--primary):hover {
  background: rgba(59, 130, 246, 0.05);
  border-color: #3b82f6;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .services-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 40px 0;
  }

  .page-title {
    font-size: 2.2rem;
  }

  .page-subtitle {
    font-size: 1rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .service-card {
    padding: 20px;
    border-radius: 12px;
  }

  .service-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .service-status {
    align-self: flex-end;
  }

  .service-actions {
    flex-direction: column;
    gap: 8px;
  }

  .service-actions .el-button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }

  .page-header {
    padding: 30px 0;
  }

  .page-title {
    font-size: 1.8rem;
  }

  .page-content {
    padding: 20px 0;
  }

  .filter-section {
    margin-bottom: 20px;
  }

  .filter-section .el-tabs__nav-scroll {
    padding: 0 16px;
  }

  .service-card {
    padding: 16px;
    margin: 0 4px;
  }

  .service-name {
    font-size: 1.1rem;
  }

  .service-description {
    font-size: 0.9rem;
    line-height: 1.6;
  }

  .service-features {
    margin-bottom: 12px;
  }

  .feature-tag {
    font-size: 11px;
    padding: 2px 6px;
    margin-right: 4px;
    margin-bottom: 4px;
  }

  .price {
    font-size: 1.3rem;
  }
}

.empty-state {
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* 分页容器 */
.pagination-container {
  margin-top: 32px;
  display: flex;
  justify-content: center;
}

/* 筛选标签样式优化 */
.filter-section .el-tabs__item {
  font-weight: 600;
  color: #64748b;
  transition: all 0.3s ease;
}

.filter-section .el-tabs__item.is-active {
  color: #3b82f6;
}

.filter-section .el-tabs__active-bar {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  height: 3px;
}

.feature-tag {
  background: rgba(59, 130, 246, 0.1) !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  color: #3b82f6 !important;
  font-weight: 500;
}
</style>