<svg width="300" height="150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="oceanGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#74b9ff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0984e3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2d3436;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="waveGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#00cec9;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#00b894;stop-opacity:0.6" />
    </linearGradient>
    <radialGradient id="sunGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#fdcb6e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e17055;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- 海洋背景 -->
  <rect width="300" height="150" fill="url(#oceanGradient)"/>
  
  <!-- 太阳 -->
  <circle cx="60" cy="40" r="20" fill="url(#sunGradient)"/>
  
  <!-- 海鸥 -->
  <path d="M 100 35 Q 105 30 110 35" stroke="white" stroke-width="2" fill="none"/>
  <path d="M 120 40 Q 125 35 130 40" stroke="white" stroke-width="2" fill="none"/>
  <path d="M 200 30 Q 205 25 210 30" stroke="white" stroke-width="2" fill="none"/>
  
  <!-- 云朵 -->
  <ellipse cx="180" cy="25" rx="12" ry="6" fill="white" opacity="0.7"/>
  <ellipse cx="190" cy="20" rx="15" ry="8" fill="white" opacity="0.7"/>
  <ellipse cx="200" cy="25" rx="10" ry="5" fill="white" opacity="0.7"/>
  
  <!-- 远处的岛屿 -->
  <ellipse cx="250" cy="80" rx="30" ry="15" fill="#2d3436" opacity="0.6"/>
  <polygon points="235,75 250,65 265,75 250,80" fill="#636e72" opacity="0.7"/>
  
  <!-- 波浪层次 -->
  <path d="M 0 90 Q 50 85 100 90 T 200 90 T 300 90 L 300 150 L 0 150 Z" fill="url(#waveGradient)"/>
  <path d="M 0 100 Q 75 95 150 100 T 300 100 L 300 150 L 0 150 Z" fill="url(#waveGradient)" opacity="0.8"/>
  <path d="M 0 110 Q 60 105 120 110 T 240 110 T 300 110 L 300 150 L 0 150 Z" fill="url(#waveGradient)" opacity="0.6"/>
  
  <!-- 海浪泡沫 -->
  <ellipse cx="30" cy="95" rx="8" ry="3" fill="white" opacity="0.6"/>
  <ellipse cx="80" cy="105" rx="6" ry="2" fill="white" opacity="0.5"/>
  <ellipse cx="150" cy="100" rx="10" ry="4" fill="white" opacity="0.6"/>
  <ellipse cx="220" cy="115" rx="7" ry="3" fill="white" opacity="0.5"/>
  <ellipse cx="280" cy="108" rx="9" ry="3" fill="white" opacity="0.6"/>
  
  <!-- 海底装饰 -->
  <circle cx="50" cy="130" r="5" fill="#fd79a8" opacity="0.7"/>
  <rect x="100" y="125" width="8" height="8" fill="#a29bfe" opacity="0.6" transform="rotate(45 104 129)"/>
  <polygon points="180,135 185,125 190,135" fill="#00b894" opacity="0.8"/>
  <circle cx="240" cy="128" r="4" fill="#e84393" opacity="0.7"/>
  
  <!-- 水中气泡 -->
  <circle cx="70" cy="120" r="2" fill="white" opacity="0.4"/>
  <circle cx="75" cy="110" r="1.5" fill="white" opacity="0.5"/>
  <circle cx="78" cy="100" r="1" fill="white" opacity="0.6"/>
  
  <circle cx="160" cy="125" r="2.5" fill="white" opacity="0.4"/>
  <circle cx="165" cy="115" r="2" fill="white" opacity="0.5"/>
  <circle cx="168" cy="105" r="1.5" fill="white" opacity="0.6"/>
  
  <circle cx="260" cy="118" r="2" fill="white" opacity="0.4"/>
  <circle cx="263" cy="108" r="1.5" fill="white" opacity="0.5"/>
  <circle cx="265" cy="98" r="1" fill="white" opacity="0.6"/>
</svg>