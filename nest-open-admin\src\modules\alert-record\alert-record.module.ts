import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AlertRecordService } from './alert-record.service';
import { <PERSON><PERSON>RecordController } from './alert-record.controller';
import { AlertService } from './services/alert.service';
import { AlertRecordEntity } from './entities/alert-record.entity';
import { UserModule } from '../user/user.module';
import { ServiceModule } from '../service/service.module';
import { QueueModule } from '../queue/queue.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([AlertRecordEntity]),
    forwardRef(() => UserModule),
    ServiceModule,
    forwardRef(() => QueueModule),
  ],
  controllers: [AlertRecordController],
  providers: [AlertRecordService, AlertService],
  exports: [AlertRecordService, AlertService],
})
export class AlertRecordModule {}
