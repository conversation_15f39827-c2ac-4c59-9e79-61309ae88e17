import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { 
  IGatewayRouterService, 
  IRouteMatch, 
  IRouteConfig, 
  IPathConfig 
} from '../interfaces/gateway.interfaces';
import { 
  API_ROUTES, 
  SERVICE_ENDPOINTS 
} from '../config/gateway.constants';

/**
 * 网关路由服务
 * 负责API路由的匹配、配置管理和目标URL构建
 * 遵循单一职责原则，专注于路由逻辑
 */
@Injectable()
export class GatewayRouterService implements IGatewayRouterService {
  private readonly logger = new Logger(GatewayRouterService.name);
  private readonly routes: IRouteConfig[] = [];

  constructor(private readonly configService: ConfigService) {
    this.initializeRoutes();
  }

  /**
   * 匹配路由
   */
  matchRoute(path: string, method: string): IRouteMatch {
    this.logger.debug(`匹配路由: ${method} ${path}`);

    for (const route of this.routes) {
      const match = this.matchSingleRoute(route, path, method);
      if (match.matched) {
        this.logger.debug(`路由匹配成功: ${route.prefix}`);
        return match;
      }
    }

    this.logger.debug(`未找到匹配的路由: ${method} ${path}`);
    return { matched: false };
  }

  /**
   * 获取所有路由配置
   */
  getAllRoutes(): IRouteConfig[] {
    return [...this.routes];
  }

  /**
   * 构建目标URL
   */
  buildTargetUrl(
    config: IRouteConfig | IPathConfig, 
    path: string, 
    query?: Record<string, any>
  ): string {
    const serviceUrl = this.getServiceUrl(config.target);
    
    // 构建基础URL
    let targetUrl = `${serviceUrl}${path}`;

    // 添加查询参数
    if (query && Object.keys(query).length > 0) {
      const queryString = new URLSearchParams(query).toString();
      targetUrl += `?${queryString}`;
    }

    this.logger.debug(`构建目标URL: ${targetUrl}`);
    return targetUrl;
  }

  /**
   * 初始化路由配置
   */
  private initializeRoutes(): void {
    this.logger.log('初始化路由配置');

    // 添加OCR路由
    this.routes.push(API_ROUTES.LOGISTICS_OCR);

    // 添加地址提取路由
    this.routes.push(API_ROUTES.ADDRESS_EXTRACTION);

    // 添加地址标准化路由
    this.routes.push(API_ROUTES.ADDRESS_NORMALIZE);

    // 添加地理坐标路由（需要特殊处理，因为有子路径）
    const geoRoute = API_ROUTES.GEO_COORDINATE;
    if (geoRoute.paths) {
      Object.values(geoRoute.paths).forEach(pathConfig => {
        this.routes.push({
          prefix: `${geoRoute.prefix}${pathConfig.path}`,
          target: pathConfig.target,
          methods: pathConfig.methods,
          allowAsync: pathConfig.allowAsync,
          allowProxyAsync: pathConfig.allowProxyAsync,
          defaultMode: pathConfig.defaultMode,
          timeout: pathConfig.timeout,
          queue: pathConfig.queue,
        });
      });
    }

    this.logger.log(`已加载 ${this.routes.length} 个路由配置`);
  }

  /**
   * 匹配单个路由
   */
  private matchSingleRoute(route: IRouteConfig, path: string, method: string): IRouteMatch {
    // 检查HTTP方法
    if (!route.methods.includes(method.toUpperCase())) {
      return { matched: false };
    }

    // 检查路径前缀匹配
    if (path.startsWith(route.prefix)) {
      // 精确匹配或前缀匹配
      if (path === route.prefix || path.startsWith(route.prefix + '/')) {
        const targetUrl = this.buildTargetUrl(route, path);
        
        return {
          matched: true,
          config: route,
          targetUrl,
          params: this.extractPathParams(route.prefix, path),
        };
      }
    }

    return { matched: false };
  }

  /**
   * 提取路径参数
   */
  private extractPathParams(routePrefix: string, actualPath: string): Record<string, string> {
    const params: Record<string, string> = {};
    
    // 简单的参数提取逻辑
    // 这里可以根据需要扩展为更复杂的路径参数解析
    if (actualPath.length > routePrefix.length) {
      const remainingPath = actualPath.substring(routePrefix.length);
      if (remainingPath.startsWith('/')) {
        const segments = remainingPath.substring(1).split('/');
        segments.forEach((segment, index) => {
          params[`param${index}`] = segment;
        });
      }
    }

    return params;
  }

  /**
   * 获取服务URL
   */
  private getServiceUrl(target: string): string {
    const serviceUrl = SERVICE_ENDPOINTS[target as keyof typeof SERVICE_ENDPOINTS];
    
    if (!serviceUrl) {
      this.logger.error(`未找到服务端点配置: ${target}`);
      throw new Error(`未配置的服务端点: ${target}`);
    }

    return serviceUrl;
  }

  /**
   * 添加动态路由
   */
  addRoute(route: IRouteConfig): void {
    this.logger.log(`添加动态路由: ${route.prefix}`);
    this.routes.push(route);
  }

  /**
   * 移除路由
   */
  removeRoute(prefix: string): boolean {
    const index = this.routes.findIndex(route => route.prefix === prefix);
    if (index !== -1) {
      this.routes.splice(index, 1);
      this.logger.log(`移除路由: ${prefix}`);
      return true;
    }
    return false;
  }

  /**
   * 更新路由配置
   */
  updateRoute(prefix: string, updates: Partial<IRouteConfig>): boolean {
    const route = this.routes.find(r => r.prefix === prefix);
    if (route) {
      Object.assign(route, updates);
      this.logger.log(`更新路由配置: ${prefix}`);
      return true;
    }
    return false;
  }

  /**
   * 获取路由统计信息
   */
  getRouteStats(): {
    totalRoutes: number;
    routesByMethod: Record<string, number>;
    routesByTarget: Record<string, number>;
  } {
    const stats = {
      totalRoutes: this.routes.length,
      routesByMethod: {} as Record<string, number>,
      routesByTarget: {} as Record<string, number>,
    };

    this.routes.forEach(route => {
      // 统计方法
      route.methods.forEach(method => {
        stats.routesByMethod[method] = (stats.routesByMethod[method] || 0) + 1;
      });

      // 统计目标服务
      stats.routesByTarget[route.target] = (stats.routesByTarget[route.target] || 0) + 1;
    });

    return stats;
  }

  /**
   * 验证路由配置
   */
  validateRouteConfig(route: IRouteConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!route.prefix || !route.prefix.startsWith('/')) {
      errors.push('路由前缀必须以/开头');
    }

    if (!route.target || !SERVICE_ENDPOINTS[route.target as keyof typeof SERVICE_ENDPOINTS]) {
      errors.push('无效的目标服务');
    }

    if (!route.methods || route.methods.length === 0) {
      errors.push('必须指定至少一个HTTP方法');
    }

    if (route.timeout && route.timeout <= 0) {
      errors.push('超时时间必须大于0');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 检查路由冲突
   */
  checkRouteConflicts(newRoute: IRouteConfig): string[] {
    const conflicts: string[] = [];

    this.routes.forEach(existingRoute => {
      // 检查前缀冲突
      if (existingRoute.prefix === newRoute.prefix) {
        // 检查方法冲突
        const methodConflicts = existingRoute.methods.filter(method => 
          newRoute.methods.includes(method)
        );
        
        if (methodConflicts.length > 0) {
          conflicts.push(
            `路由冲突: ${newRoute.prefix} [${methodConflicts.join(', ')}] 与现有路由冲突`
          );
        }
      }
    });

    return conflicts;
  }

  /**
   * 获取路由健康状态
   */
  async getRoutesHealthStatus(): Promise<Record<string, boolean>> {
    const healthStatus: Record<string, boolean> = {};

    const uniqueTargets = [...new Set(this.routes.map(route => route.target))];
    
    for (const target of uniqueTargets) {
      try {
        const serviceUrl = this.getServiceUrl(target);
        // 这里可以添加实际的健康检查逻辑
        // 暂时返回true
        healthStatus[target] = true;
      } catch (error) {
        this.logger.error(`检查服务健康状态失败: ${target}`, error);
        healthStatus[target] = false;
      }
    }

    return healthStatus;
  }
}
