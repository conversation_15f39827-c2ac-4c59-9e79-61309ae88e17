# 架构优化方案

## 问题分析

### 1. 循环依赖风险

当前架构存在以下问题：

```
AppModule (全局守卫/拦截器)
    ↓ 依赖
AuthService (业务模块)
    ↓ 依赖  
UserService, ApiKeyService (其他业务模块)
```

**问题**：
- 全局守卫 `UnifiedAuthGuard` 直接依赖 `AuthService`
- 全局拦截器 `UnifiedInterceptor` 依赖 `ErrorHandlerService`
- 当业务模块需要使用全局守卫时，可能形成循环依赖
- 违反了依赖倒置原则

### 2. 职责混乱

- 全局组件（守卫/拦截器）应该是基础设施层，不应依赖业务层
- `AuthService` 既处理业务逻辑又提供基础认证功能
- 缺乏清晰的分层架构

## 优化方案

### 方案一：创建独立的认证基础服务（推荐）

#### 1. 创建 `CoreAuthService`

```typescript
// src/common/services/core-auth.service.ts
@Injectable()
export class CoreAuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {}

  // 纯粹的JWT验证，不涉及业务逻辑
  async validateJwtToken(token: string): Promise<AuthResult> {
    // JWT验证逻辑
  }

  // 纯粹的API密钥格式验证和缓存查询
  async validateApiKeyFormat(apiKey: string): Promise<boolean> {
    // 格式验证逻辑
  }

  // 从缓存获取API密钥信息
  async getApiKeyFromCache(keyHash: string): Promise<AuthApiKey | null> {
    // 缓存查询逻辑
  }
}
```

#### 2. 重构 `UnifiedAuthGuard`

```typescript
// src/common/guards/unified-auth.guard.ts
@Injectable()
export class UnifiedAuthGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly coreAuthService: CoreAuthService, // 使用核心认证服务
    private readonly redisService: RedisService,
    private readonly configService: ConfigService,
    private readonly errorHandlerService: ErrorHandlerService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 使用 coreAuthService 进行认证
    // 如果需要完整的用户信息，通过事件或回调机制获取
  }
}
```

#### 3. 事件驱动的用户信息获取

```typescript
// src/common/events/auth.events.ts
export class UserInfoRequestEvent {
  constructor(
    public readonly userId: number,
    public readonly callback: (user: AuthUser) => void
  ) {}
}

// 在业务模块中监听事件
@EventsHandler(UserInfoRequestEvent)
export class UserInfoHandler {
  constructor(private readonly userService: UserService) {}

  async handle(event: UserInfoRequestEvent) {
    const user = await this.userService.findById(event.userId);
    event.callback(user);
  }
}
```

### 方案二：接口抽象（备选）

#### 1. 定义认证接口

```typescript
// src/common/interfaces/auth.interface.ts
export interface IAuthValidator {
  validateJwtToken(token: string): Promise<AuthResult>;
  validateApiKey(apiKey: string): Promise<AuthResult>;
  updateApiKeyUsage(apiKey: string, clientIp: string): Promise<void>;
}
```

#### 2. 在业务模块实现接口

```typescript
// src/modules/auth/auth.service.ts
@Injectable()
export class AuthService implements IAuthValidator {
  // 实现接口方法
}
```

#### 3. 守卫依赖接口

```typescript
// src/common/guards/unified-auth.guard.ts
@Injectable()
export class UnifiedAuthGuard implements CanActivate {
  constructor(
    @Inject('AUTH_VALIDATOR') private readonly authValidator: IAuthValidator,
    // 其他依赖
  ) {}
}
```

## 推荐实施步骤

### 第一阶段：创建核心服务

1. 创建 `CoreAuthService`
2. 将纯认证逻辑从 `AuthService` 迁移到 `CoreAuthService`
3. 更新 `SharedModule` 导出 `CoreAuthService`

### 第二阶段：重构守卫和拦截器

1. 更新 `UnifiedAuthGuard` 使用 `CoreAuthService`
2. 移除对 `AuthService` 的直接依赖
3. 实现事件驱动的用户信息获取机制

### 第三阶段：优化业务服务

1. `AuthService` 专注于业务逻辑（登录、注册等）
2. 使用 `CoreAuthService` 进行基础认证
3. 清理冗余代码

## 优化后的架构

```
基础设施层 (Infrastructure)
├── CoreAuthService (核心认证)
├── UnifiedAuthGuard (全局守卫)
├── UnifiedInterceptor (全局拦截器)
└── ErrorHandlerService (错误处理)

应用层 (Application)
├── AuthService (认证业务逻辑)
├── UserService (用户业务逻辑)
└── ApiKeyService (API密钥业务逻辑)

领域层 (Domain)
├── AuthUser (认证用户模型)
├── AuthApiKey (认证API密钥模型)
└── AuthResult (认证结果模型)
```

## 预期收益

1. **解除循环依赖**：基础设施层不再依赖业务层
2. **提高可测试性**：各层职责清晰，便于单元测试
3. **增强可维护性**：代码结构更清晰，修改影响范围更小
4. **提升性能**：减少不必要的依赖注入和模块加载
5. **符合SOLID原则**：特别是依赖倒置原则和单一职责原则