import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { 
  BadRequestException, 
  UnauthorizedException, 
  ForbiddenException, 
  ConflictException,
  InternalServerErrorException,
  NotFoundException 
} from '@nestjs/common';
import { ApiException } from '@/common/exceptions/api.exception';
import { getClientIp } from '@/common/utils/ip.utils';

/**
 * 认证模块专用错误处理服务
 * 提供统一的错误处理、日志记录和安全监控
 */
@Injectable()
export class AuthErrorHandlerService {
  private readonly logger = new Logger(AuthErrorHandlerService.name);
  private readonly isDevelopment: boolean;

  constructor(private readonly configService: ConfigService) {
    this.isDevelopment = this.configService.get('NODE_ENV') !== 'production';
  }

  /**
   * 处理注册错误
   */
  handleRegisterError(error: any, context: { email?: string; phone?: string; ip?: string }): never {
    const { email, phone, ip } = context;
    const identifier = email || phone || 'unknown';

    if (error.code === 'ER_DUP_ENTRY' || error.errno === 1062) {
      this.logSecurityEvent('duplicate_registration_attempt', {
        identifier,
        ip,
        error: error.message,
      });
      throw new ConflictException('该邮箱或手机号已被注册');
    }

    this.logError('registration_failed', error, { identifier, ip });
    throw new InternalServerErrorException('注册失败，请稍后再试');
  }

  /**
   * 处理登录错误
   */
  handleLoginError(error: any, context: { 
    identifier?: string; 
    ip?: string; 
    loginType?: 'password' | 'email_code' | 'sms_code' 
  }): never {
    const { identifier, ip, loginType } = context;

    // 用户不存在
    if (error instanceof NotFoundException) {
      this.logSecurityEvent('login_attempt_nonexistent_user', {
        identifier,
        ip,
        loginType,
      });
      throw new UnauthorizedException('用户名或密码错误');
    }

    // 密码错误
    if (error.message?.includes('password') || error.message?.includes('密码')) {
      this.logSecurityEvent('login_failed_invalid_password', {
        identifier,
        ip,
        loginType,
      });
      throw new UnauthorizedException('用户名或密码错误');
    }

    // 验证码错误
    if (error.message?.includes('验证码') || error.message?.includes('code')) {
      this.logSecurityEvent('login_failed_invalid_code', {
        identifier,
        ip,
        loginType,
      });
      throw new UnauthorizedException('验证码错误或已过期');
    }

    // 账户被锁定
    if (error instanceof ForbiddenException) {
      this.logSecurityEvent('login_attempt_locked_account', {
        identifier,
        ip,
        reason: error.message,
      });
      throw error;
    }

    this.logError('login_failed', error, { identifier, ip, loginType });
    throw new InternalServerErrorException('登录失败，请稍后再试');
  }

  /**
   * 处理令牌相关错误
   */
  handleTokenError(error: any, context: { 
    tokenType?: 'access' | 'refresh'; 
    userId?: number; 
    ip?: string 
  }): never {
    const { tokenType, userId, ip } = context;

    if (error.name === 'TokenExpiredError') {
      this.logSecurityEvent('token_expired', {
        tokenType,
        userId,
        ip,
      });
      throw new UnauthorizedException('令牌已过期，请重新登录');
    }

    if (error.name === 'JsonWebTokenError') {
      this.logSecurityEvent('token_invalid', {
        tokenType,
        userId,
        ip,
        error: error.message,
      });
      throw new UnauthorizedException('无效的令牌');
    }

    this.logError('token_processing_failed', error, { tokenType, userId, ip });
    throw new InternalServerErrorException('令牌处理失败');
  }

  /**
   * 处理第三方登录错误
   */
  handleOAuthError(error: any, context: { 
    provider: string; 
    code?: string; 
    ip?: string 
  }): never {
    const { provider, code, ip } = context;

    if (error.response?.status === 400) {
      this.logSecurityEvent('oauth_invalid_code', {
        provider,
        ip,
        error: error.message,
      });
      throw new BadRequestException(`${provider}授权码无效或已过期`);
    }

    if (error.response?.status === 401) {
      this.logSecurityEvent('oauth_unauthorized', {
        provider,
        ip,
        error: error.message,
      });
      throw new UnauthorizedException(`${provider}授权失败，请重新授权`);
    }

    this.logError('oauth_failed', error, { provider, ip });
    throw new InternalServerErrorException(`${provider}登录失败，请稍后再试`);
  }

  /**
   * 处理验证码错误
   */
  handleCaptchaError(error: any, context: { 
    type: string; 
    identifier?: string; 
    ip?: string 
  }): never {
    const { type, identifier, ip } = context;

    if (error.message?.includes('expired') || error.message?.includes('过期')) {
      this.logSecurityEvent('captcha_expired', {
        type,
        identifier,
        ip,
      });
      throw new BadRequestException('验证码已过期，请重新获取');
    }

    if (error.message?.includes('invalid') || error.message?.includes('无效')) {
      this.logSecurityEvent('captcha_invalid', {
        type,
        identifier,
        ip,
      });
      throw new BadRequestException('验证码错误');
    }

    if (error.message?.includes('limit') || error.message?.includes('限制')) {
      this.logSecurityEvent('captcha_rate_limit', {
        type,
        identifier,
        ip,
      });
      throw new BadRequestException('验证码发送过于频繁，请稍后再试');
    }

    this.logError('captcha_failed', error, { type, identifier, ip });
    throw new InternalServerErrorException('验证码处理失败');
  }

  /**
   * 记录安全事件
   */
  private logSecurityEvent(eventType: string, data: any): void {
    const logData = {
      eventType,
      timestamp: new Date().toISOString(),
      ...data,
    };

    // 根据事件类型决定日志级别
    const securityEvents = [
      'duplicate_registration_attempt',
      'login_attempt_nonexistent_user',
      'login_failed_invalid_password',
      'login_attempt_locked_account',
      'token_invalid',
      'oauth_unauthorized',
      'captcha_rate_limit',
    ];

    if (securityEvents.includes(eventType)) {
      this.logger.warn(`安全事件: ${eventType}`, logData);
    } else {
      this.logger.log(`认证事件: ${eventType}`, logData);
    }
  }

  /**
   * 记录错误日志
   */
  private logError(operation: string, error: any, context: any): void {
    const logData = {
      operation,
      error: {
        message: error.message,
        stack: this.isDevelopment ? error.stack : undefined,
        code: error.code,
        errno: error.errno,
      },
      context,
      timestamp: new Date().toISOString(),
    };

    this.logger.error(`认证操作失败: ${operation}`, logData);
  }

  /**
   * 创建结构化的成功日志
   */
  logSuccess(operation: string, context: any): void {
    const logData = {
      operation,
      context,
      timestamp: new Date().toISOString(),
    };

    this.logger.log(`认证操作成功: ${operation}`, logData);
  }

  /**
   * 从请求中提取上下文信息
   */
  extractContext(request?: any): { ip: string; userAgent?: string } {
    if (!request) {
      return { ip: '127.0.0.1' };
    }

    return {
      ip: getClientIp(request),
      userAgent: request.headers?.['user-agent'],
    };
  }
}
