import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddEncryptedSecretKeyField1751015784123 implements MigrationInterface {
  name = 'AddEncryptedSecretKeyField1751015784123';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1. 添加加密密钥字段
    await queryRunner.query(
      `ALTER TABLE \`open_api_key\` ADD \`encryptedSecretKey\` varchar(512) NULL COMMENT '加密存储的密钥，用于长期保存和恢复，通过系统密钥加密'`,
    );

    // 2. 记录迁移状态
    console.log('已添加encryptedSecretKey字段到open_api_key表');
    
    // 3. 提示用户运行加密任务
    console.log('请注意：需要手动执行API密钥加密同步任务，确保现有密钥能够使用新的加密机制');
    console.log('可通过访问管理员API端点 /api-key/admin/encrypt-all-secret-keys 来执行此任务');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 移除加密密钥字段
    await queryRunner.query(`ALTER TABLE \`open_api_key\` DROP COLUMN \`encryptedSecretKey\``);
    console.log('已移除encryptedSecretKey字段');
  }
} 