/**
 * 队列模块导出文件
 * 统一导出所有公共接口
 */

// 模块
export { QueueModule } from './queue.module';

// 服务
export { EnhancedQueueManagerService } from './services/enhanced-queue-manager.service';
export { BullQueueService } from './services/bull-queue.service';

// 处理器
export { ApiProcessor } from './processors/api.processor';
export { AlertProcessor } from './processors/alert.processor';
export { OcrProcessor } from './processors/ocr.processor';
export { AddressExtractionProcessor } from './processors/address-extraction.processor';
export { ReverseGeocodingProcessor } from './processors/reverse-geocoding.processor';
export { BaseProcessor } from './processors/base-processor';

// 控制器
export { QueueController } from './queue.controller';

// DTO
export {
  BaseTaskDto,
  ApiCallTaskDto,
  AlertTaskDto,
  TaskQueryDto,
  TaskActionDto,
} from './dto/task.dto';

// 接口
export {
  BaseTask,
  ApiCallTaskData,
  AlertTaskData,
  TaskResult,
  QueueStatus,
  QueueStats,
  SystemHealth,
  TaskEvent,
  QueueConfig,
  TaskProcessor,
} from './interfaces/queue.interface';

// 常量
export {
  QUEUE_NAMES,
  TASK_TYPES,
  TASK_PRIORITY,
  TASK_STATUS,
  ALERT_LEVELS,
  ALERT_TYPES,
  QueueName,
  TaskType,
  TaskPriority,
  TaskStatus,
  AlertLevel,
  AlertType,
  PRIORITY_LEVELS,
} from './queue.constants';

// 配置
export { createBullConfig } from './config/bull.config';