import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue, Job, JobOptions, JobId } from 'bull';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '../../../shared/redis.service';
import { IQueueManager } from './queue-manager.interface';
import { QUEUE_NAMES } from '../queue.constants';

// 定义JobStatus接口，与IQueueManager接口兼容
export interface JobStatus {
  id: string;
  state: string;
  progress: number;
  result?: any;
  failedReason?: string;
  data?: any;
  timestamp: {
    created: number;
    processed?: number;
    finished?: number;
  };
}

/**
 * 增强的队列管理器服务
 * 实现队列管理器接口，提供统一的队列操作
 */
@Injectable()
export class EnhancedQueueManagerService implements IQueueManager {
  private readonly logger = new Logger(EnhancedQueueManagerService.name);
  private readonly queueMap: Map<string, Queue> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    @InjectQueue(QUEUE_NAMES.OCR) private readonly ocrQueue: Queue,
    @InjectQueue(QUEUE_NAMES.EXTRACT_ADDRESS) private readonly addressQueue: Queue,
    @InjectQueue(QUEUE_NAMES.REV_GEO) private readonly revGeoQueue: Queue,
    @InjectQueue(QUEUE_NAMES.ALERT) private readonly alertQueue: Queue,
    @InjectQueue(QUEUE_NAMES.API_CALL) private readonly apiCallQueue: Queue,
  ) {
    // 初始化队列映射
    this.queueMap.set(QUEUE_NAMES.OCR, ocrQueue);
    this.queueMap.set(QUEUE_NAMES.EXTRACT_ADDRESS, addressQueue);
    this.queueMap.set(QUEUE_NAMES.REV_GEO, revGeoQueue);
    this.queueMap.set(QUEUE_NAMES.ALERT, alertQueue);
    this.queueMap.set(QUEUE_NAMES.API_CALL, apiCallQueue);
    
    this.logger.log('增强的队列管理器服务已初始化');
  }

  /**
   * 添加任务到队列
   */
  async addJob<T = any>(
    queueName: string,
    taskType: string,
    data: T,
    options: JobOptions = {}
  ): Promise<{ id: string }> {
    try {
      this.logger.debug(`添加任务到队列: ${queueName}, 任务类型: ${taskType}, 选项: ${JSON.stringify(options)}`);
      
      // 获取队列
      const queue = this.getQueue(queueName);
      if (!queue) {
        throw new Error(`队列不存在: ${queueName}`);
      }
      
      // 设置默认选项
      const defaultOptions: JobOptions = {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 1000,
        },
        removeOnComplete: false, // 默认不删除已完成的任务
        removeOnFail: false,     // 默认不删除失败的任务
      };
      
      // 合并选项
      const mergedOptions = { ...defaultOptions, ...options };
      
      // 添加任务到队列
      this.logger.debug(`正在添加任务: ${taskType}, 数据长度: ${JSON.stringify(data).length}, 选项: ${JSON.stringify(mergedOptions)}`);
      const job = await queue.add(taskType, data, mergedOptions);
      this.logger.debug(`任务已添加到队列: ${queueName}, 任务ID: ${job.id}, 任务类型: ${taskType}`);
      
      // 在Redis中存储任务信息，便于后续查找
      await this.redisService.set(
        `job:${job.id}`,
        {
          queueName,
          taskType,
          createdAt: new Date().toISOString(),
          data: JSON.stringify(data),
        },
        60 * 60 * 24, // 保存24小时
      );
      
      return { id: job.id.toString() };
    } catch (error) {
      this.logger.error(`添加任务到队列失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取任务状态
   */
  async getJobStatus(queueName: string, jobId: string): Promise<JobStatus> {
    try {
      this.logger.debug(`获取任务状态: queueName=${queueName}, jobId=${jobId}`);
      
      // 获取队列
      const queue = this.getQueue(queueName);
      if (!queue) {
        throw new Error(`队列不存在: ${queueName}`);
      }
      
      // 尝试从队列获取任务
      this.logger.debug(`尝试从队列获取任务: ${jobId}`);
      const job = await queue.getJob(jobId);
      
      if (!job) {
        this.logger.warn(`任务不存在: ${jobId}`);
        return {
          id: jobId,
          state: 'not_found',
          progress: 0,
          result: null,
          failedReason: '任务不存在或已被删除',
          data: null,
          timestamp: {
            created: 0,
            processed: undefined,
            finished: undefined,
          },
        };
      }
      
      this.logger.debug(`找到任务 ${jobId}: ${JSON.stringify({
        id: job.id,
        name: job.name,
        data: `数据长度: ${JSON.stringify(job.data).length}`
      })}`);
      
      // 获取任务状态
      const state = await job.getState();
      const progress = job.progress ? await job.progress() : 0;
      
      this.logger.debug(`任务 ${jobId} 状态: ${state}, 进度: ${progress}`);
      
      // 获取任务结果
      let result = null;
      if (state === 'completed') {
        result = await job.returnvalue;
        this.logger.debug(`任务 ${jobId} 完成，结果: ${JSON.stringify(result).substring(0, 200)}...`);
      } else if (state === 'failed') {
        this.logger.debug(`任务 ${jobId} 失败，原因: ${job.failedReason}`);
      }
      
      // 构建任务状态对象
      const jobStatus: JobStatus = {
        id: job.id.toString(),
        state,
        progress: typeof progress === 'object' ? progress.progress || 0 : progress,
        result,
        failedReason: job.failedReason,
        // data: job.data,
        timestamp: {
          created: job.timestamp || 0,
          processed: job.processedOn || undefined,
          finished: job.finishedOn || undefined,
        },
      };
      
      return jobStatus;
    } catch (error) {
      this.logger.error(`获取任务状态失败: ${error.message}`, error.stack);
      return {
        id: jobId,
        state: 'error',
        progress: 0,
        result: null,
        failedReason: `获取任务状态失败: ${error.message}`,
        data: null,
        timestamp: {
          created: 0,
          processed: undefined,
          finished: undefined,
        },
      };
    }
  }

  /**
   * 检查任务是否存在
   */
  async jobExists(queueName: string, jobId: string): Promise<boolean> {
    const queue = this.getQueue(queueName);
    const job = await queue.getJob(jobId);
    return !!job;
  }

  /**
   * 移除任务
   */
  async removeJob(queueName: string, jobId: string): Promise<boolean> {
    const queue = this.getQueue(queueName);
    const job = await queue.getJob(jobId);
    
    if (!job) {
      return false;
    }
    
    try {
      await job.remove();
      // 从Redis中删除任务信息
      await this.redisService.del(`job:${jobId}`);
      return true;
    } catch (error) {
      this.logger.error(`移除任务 ${jobId} 失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 获取所有队列名称
   */
  getQueueNames(): string[] {
    return Object.values(QUEUE_NAMES);
  }

  /**
   * 获取队列状态
   */
  async getQueueStatus(queueName: string): Promise<{
    name: string;
    counts: {
      waiting: number;
      active: number;
      completed: number;
      failed: number;
      delayed?: number;
      paused?: number;
    };
    isPaused: boolean;
    workerCount: number;
  }> {
    const queue = this.getQueue(queueName);
    
    const jobCounts = await queue.getJobCounts();
    const isPaused = await queue.isPaused();
    const workers = await queue.getWorkers();
    
    return {
      name: queueName,
      counts: jobCounts,
      isPaused,
      workerCount: workers.length,
    };
  }

  /**
   * 暂停队列
   */
  async pauseQueue(queueName: string): Promise<boolean> {
    const queue = this.getQueue(queueName);
    await queue.pause();
    return true;
  }

  /**
   * 恢复队列
   */
  async resumeQueue(queueName: string): Promise<boolean> {
    const queue = this.getQueue(queueName);
    await queue.resume();
    return true;
  }

  /**
   * 获取队列实例
   */
  private getQueue(queueName: string): Queue {
    const queue = this.queueMap.get(queueName);
    
    if (!queue) {
      throw new Error(`队列 ${queueName} 不存在`);
    }
    
    return queue;
  }
} 