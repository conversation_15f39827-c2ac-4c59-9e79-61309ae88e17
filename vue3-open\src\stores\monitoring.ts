import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { monitoringApi } from '@/api/monitoring'
import type {
  SystemStatus,
  SystemMetrics,
  MetricsHistory,
  Alert,
  AlertHistory,
  MonitoringDashboard,
  HealthCheck
} from '@/types/monitoring'

export const useMonitoringStore = defineStore('monitoring', () => {
  // 状态
  const loading = ref(false)
  const systemStatus = ref<SystemStatus | null>(null)
  const systemMetrics = ref<SystemMetrics | null>(null)
  const metricsHistory = ref<MetricsHistory | null>(null)
  const activeAlerts = ref<Alert[]>([])
  const alertHistory = ref<AlertHistory[]>([])
  const dashboardData = ref<MonitoringDashboard | null>(null)
  const healthStatus = ref<HealthCheck | null>(null)
  
  // 分页信息
  const alertHistoryPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })
  
  // 筛选条件
  const alertFilters = ref({
    level: '',
    startTime: '',
    endTime: ''
  })
  
  // 时间范围
  const timeRange = ref('24h')
  
  // 计算属性
  const criticalAlertsCount = computed(() => {
    return activeAlerts.value.filter(alert => alert.level === 'critical').length
  })
  
  const warningAlertsCount = computed(() => {
    return activeAlerts.value.filter(alert => alert.level === 'warning').length
  })
  
  const systemHealthScore = computed(() => {
    if (!systemMetrics.value) return 0
    const { cpu, memory, disk }: any = systemMetrics.value
    const cpuScore = Math.max(0, 100 - cpu.usage)
    const memoryScore = Math.max(0, 100 - memory.usage)
    const diskScore = Math.max(0, 100 - disk.usage)
    return Math.round((cpuScore + memoryScore + diskScore) / 3)
  })
  
  // Actions
  
  /**
   * 获取系统状态
   */
  async function fetchSystemStatus() {
    try {
      loading.value = true
      systemStatus.value = await monitoringApi.getSystemStatus()
    } catch (error: any) {
      ElMessage.error(error.message || '获取系统状态失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 获取系统指标
   */
  async function fetchSystemMetrics() {
    try {
      systemMetrics.value = await monitoringApi.getSystemMetrics()
    } catch (error: any) {
      ElMessage.error(error.message || '获取系统指标失败')
      throw error
    }
  }
  
  /**
   * 获取历史指标数据
   */
  async function fetchMetricsHistory(range?: string) {
    try {
      const targetRange = range || timeRange.value
      metricsHistory.value = await monitoringApi.getMetricsHistory(targetRange)
    } catch (error: any) {
      ElMessage.error(error.message || '获取历史数据失败')
      throw error
    }
  }
  
  /**
   * 获取活跃告警
   */
  async function fetchActiveAlerts() {
    try {
      activeAlerts.value = await monitoringApi.getActiveAlerts()
    } catch (error: any) {
      ElMessage.error(error.message || '获取告警信息失败')
      throw error
    }
  }
  
  /**
   * 获取告警历史
   */
  async function fetchAlertHistory(params?: {
    page?: number
    pageSize?: number
    level?: string
    startTime?: string
    endTime?: string
  }) {
    try {
      loading.value = true
      const queryParams = {
        page: alertHistoryPagination.value.page,
        pageSize: alertHistoryPagination.value.pageSize,
        ...alertFilters.value,
        ...params
      }
      
      const response = await monitoringApi.getAlertHistory(queryParams)
      alertHistory.value = response.data
      alertHistoryPagination.value = {
        page: response.page,
        pageSize: response.pageSize,
        total: response.total
      }
    } catch (error: any) {
      ElMessage.error(error.message || '获取告警历史失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 获取监控面板数据
   */
  async function fetchDashboardData() {
    try {
      dashboardData.value = await monitoringApi.getMonitoringDashboard()
    } catch (error: any) {
      ElMessage.error(error.message || '获取监控面板数据失败')
      throw error
    }
  }
  
  /**
   * 健康检查
   */
  async function performHealthCheck() {
    try {
      healthStatus.value = await monitoringApi.healthCheck()
    } catch (error: any) {
      ElMessage.error(error.message || '健康检查失败')
      throw error
    }
  }
  
  /**
   * 解除告警
   */
  async function resolveAlert(alertId: string) {
    try {
      await monitoringApi.resolveAlert(alertId)
      // 从活跃告警中移除
      const index = activeAlerts.value.findIndex(alert => alert.id === alertId)
      if (index > -1) {
        activeAlerts.value.splice(index, 1)
      }
      ElMessage.success('告警已解除')
    } catch (error: any) {
      ElMessage.error(error.message || '解除告警失败')
      throw error
    }
  }
  
  /**
   * 批量解除告警
   */
  async function batchResolveAlerts(alertIds: string[]) {
    try {
      await monitoringApi.batchResolveAlerts(alertIds)
      // 从活跃告警中移除
      activeAlerts.value = activeAlerts.value.filter(
        alert => !alertIds.includes(alert.id)
      )
      ElMessage.success(`已解除 ${alertIds.length} 个告警`)
    } catch (error: any) {
      ElMessage.error(error.message || '批量解除告警失败')
      throw error
    }
  }
  
  /**
   * 设置时间范围
   */
  function setTimeRange(range: string) {
    timeRange.value = range
  }
  
  /**
   * 设置告警筛选条件
   */
  function setAlertFilters(filters: Partial<typeof alertFilters.value>) {
    Object.assign(alertFilters.value, filters)
  }
  
  /**
   * 重置告警筛选条件
   */
  function resetAlertFilters() {
    alertFilters.value = {
      level: '',
      startTime: '',
      endTime: ''
    }
  }
  
  /**
   * 刷新所有数据
   */
  async function refreshAllData() {
    try {
      await Promise.all([
        fetchSystemStatus(),
        fetchSystemMetrics(),
        fetchActiveAlerts(),
        fetchDashboardData(),
        performHealthCheck()
      ])
    } catch (error) {
      console.error('刷新监控数据失败:', error)
    }
  }
  
  /**
   * 启动自动刷新
   */
  function startAutoRefresh(interval = 30000) {
    return setInterval(() => {
      refreshAllData()
    }, interval)
  }
  
  return {
    // 状态
    loading,
    systemStatus,
    systemMetrics,
    metricsHistory,
    activeAlerts,
    alertHistory,
    dashboardData,
    healthStatus,
    alertHistoryPagination,
    alertFilters,
    timeRange,
    
    // 计算属性
    criticalAlertsCount,
    warningAlertsCount,
    systemHealthScore,
    
    // Actions
    fetchSystemStatus,
    fetchSystemMetrics,
    fetchMetricsHistory,
    fetchActiveAlerts,
    fetchAlertHistory,
    fetchDashboardData,
    performHealthCheck,
    resolveAlert,
    batchResolveAlerts,
    setTimeRange,
    setAlertFilters,
    resetAlertFilters,
    refreshAllData,
    startAutoRefresh
  }
})