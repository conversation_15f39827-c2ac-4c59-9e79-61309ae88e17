import { Request } from 'express';

/**
 * 从HTTP请求中获取真实的客户端IP地址
 * 考虑代理、负载均衡器等情况
 * 
 * @param request Express请求对象
 * @returns 客户端IP地址
 */
export function getClientIp(request: Request): string {
  // 优先级顺序：
  // 1. X-Forwarded-For (最常用的代理头)
  // 2. X-Real-IP (Nginx等反向代理使用)
  // 3. X-Client-IP (Apache等使用)
  // 4. CF-Connecting-IP (Cloudflare使用)
  // 5. request.connection.remoteAddress (直连)
  // 6. request.socket.remoteAddress (备用)
  // 7. request.ip (Express内置)

  const headers = request.headers;
  
  // X-Forwarded-For 可能包含多个IP，取第一个
  if (headers['x-forwarded-for']) {
    const forwardedFor = Array.isArray(headers['x-forwarded-for']) 
      ? headers['x-forwarded-for'][0] 
      : headers['x-forwarded-for'];
    
    // X-Forwarded-For 格式: client, proxy1, proxy2
    const firstIp = forwardedFor.split(',')[0].trim();
    if (isValidIp(firstIp)) {
      return firstIp;
    }
  }

  // X-Real-IP (通常由Nginx设置)
  if (headers['x-real-ip']) {
    const realIp = Array.isArray(headers['x-real-ip']) 
      ? headers['x-real-ip'][0] 
      : headers['x-real-ip'];
    
    if (isValidIp(realIp)) {
      return realIp;
    }
  }

  // X-Client-IP
  if (headers['x-client-ip']) {
    const clientIp = Array.isArray(headers['x-client-ip']) 
      ? headers['x-client-ip'][0] 
      : headers['x-client-ip'];
    
    if (isValidIp(clientIp)) {
      return clientIp;
    }
  }

  // CF-Connecting-IP (Cloudflare)
  if (headers['cf-connecting-ip']) {
    const cfIp = Array.isArray(headers['cf-connecting-ip']) 
      ? headers['cf-connecting-ip'][0] 
      : headers['cf-connecting-ip'];
    
    if (isValidIp(cfIp)) {
      return cfIp;
    }
  }

  // 备用方案：从连接信息获取
  const connectionIp = (request as any).connection?.remoteAddress 
    || (request as any).socket?.remoteAddress 
    || request.ip;

  if (connectionIp && isValidIp(connectionIp)) {
    // 移除IPv6映射的IPv4前缀
    return connectionIp.replace(/^::ffff:/, '');
  }

  // 默认返回本地IP（开发环境）
  return '127.0.0.1';
}

/**
 * 验证IP地址格式是否有效
 * 支持IPv4和IPv6
 * 
 * @param ip IP地址字符串
 * @returns 是否为有效IP
 */
export function isValidIp(ip: string): boolean {
  if (!ip || typeof ip !== 'string') {
    return false;
  }

  // 移除可能的端口号
  const cleanIp = ip.split(':')[0];
  
  // IPv4 正则表达式
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  
  // IPv6 正则表达式（简化版）
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
  
  return ipv4Regex.test(cleanIp) || ipv6Regex.test(cleanIp);
}

/**
 * 判断IP是否为内网地址
 * 
 * @param ip IP地址
 * @returns 是否为内网IP
 */
export function isPrivateIp(ip: string): boolean {
  if (!isValidIp(ip)) {
    return false;
  }

  // 移除IPv6映射前缀
  const cleanIp = ip.replace(/^::ffff:/, '');
  
  // 内网IP范围
  const privateRanges = [
    /^10\./,                    // 10.0.0.0/8
    /^172\.(1[6-9]|2[0-9]|3[0-1])\./, // **********/12
    /^192\.168\./,              // ***********/16
    /^127\./,                   // *********/8 (localhost)
    /^169\.254\./,              // ***********/16 (link-local)
    /^::1$/,                    // IPv6 localhost
    /^fe80:/,                   // IPv6 link-local
    /^fc00:/,                   // IPv6 unique local
  ];

  return privateRanges.some(range => range.test(cleanIp));
}

/**
 * 获取IP地址的地理位置信息（占位符函数）
 * 实际项目中可以集成第三方IP地理位置服务
 * 
 * @param ip IP地址
 * @returns 地理位置信息
 */
export function getIpLocation(ip: string): { country?: string; region?: string; city?: string } {
  // 这里可以集成第三方服务如：
  // - MaxMind GeoIP2
  // - IP2Location
  // - ipapi.co
  // - ip-api.com
  
  if (isPrivateIp(ip)) {
    return { country: 'Local', region: 'Private', city: 'Network' };
  }
  
  // 占位符返回
  return { country: 'Unknown', region: 'Unknown', city: 'Unknown' };
}
