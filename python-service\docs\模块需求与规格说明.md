# 三大功能模块需求与规格说明

本文档提供在阿里云服务器（Ubuntu 24.04.2 LTS）上部署开放平台全栈应用的详细步骤。

### 系统环境
- **操作系统**: Ubuntu 24.04.2 LTS (Noble Numbat)
- **架构**: x86_64
- **CPU**: Intel Xeon Platinum 8369B (4核)
- **虚拟化**: KVM

本文定义 `python-service` 目录下需要实现/巩固的三大模块：
1. 地理坐标逆解析（经纬度→地址）与正解析（地址→经纬度）
2. 物流地址文本解析（省市区/街道/详细地址/姓名/手机号）
3. 申通快递面单 OCR（含图片压缩与预处理）

文档聚焦接口契约、准确率与性能指标、配置项、错误码、测试用例要点与验收标准，不涉及具体代码实现。

---

## 0. 统一 API 约定（无 v1/op 前缀）
- **端点一览（以此为准）**：
  - OCR：`/ocr/upload`、`/ocr/recognize`、`/ocr/sto/upload`、`/ocr/sto/recognize`
  - 地址：`/address/extract`、`/address/normalize`
  - 地理：`/geo/reverse`、`/geo/forward`
- **HTTP**：
  - Content-Type：`application/json; charset=utf-8` 或 `multipart/form-data`（文件上传）
  - Accept-Language：`zh-CN`（默认）
  - 时区：`Asia/Shanghai`
  - Headers（建议）：`X-Request-Id`、`Idempotency-Key`（OCR 建议，30 分钟窗口）
- **统一响应包装**：
  ```json
  {
    "success": true,
    "code": 0,
    "message": "OK",
    "data": { /* 具体业务数据 */ }
  }
  ```
- **错误码（建议）**：
  - 0：OK
  - 40001：InvalidParameter（参数非法）
  - 40002：MissingRequiredField（缺少必填项）
  - 41301：PayloadTooLarge（文件/请求体过大）
  - 41501：UnsupportedMediaType（不支持的媒体类型）
  - 42901：RateLimited（限流）
  - 50001：InternalError（内部错误）
  - 50201：UpstreamUnavailable（下游不可用，如 Nominatim）
  - 50401：UpstreamTimeout（下游超时）
  - 40801：OcrTimeout（OCR 处理超时）
- **限额（建议，网关侧实现）**：
  - OCR：单文件 ≤ 8MB，像素 ≤ 4096×4096；Base64 ≤ 12MB；每 IP 60 rpm
  - 地址/地理：每 IP 120 rpm；批量最多 100 条/次

---

## 1. 地理编码模块（逆/正解析）
### 1.1 范围
- 逆解析：`latitude, longitude ⇒ address`（省、市、区/县、街道、门牌、邮编、完整地址）。
- 正解析：`address ⇒ latitude, longitude`（WGS84）。
- 语言：默认中文 `zh-CN`。

### 1.2 接口（统一端点，去掉 v1/op 前缀）
- `GET/POST /geo/reverse`
  - 请求参数：
    - `latitude`（必填，float）
    - `longitude`（必填，float）
    - `coordType`（可选，enum：`wgs84`|`gcj02`|`bd09`，默认 `wgs84`）
  - 响应：
    ```json
    {
      "success": true,
      "code": 0,
      "message": "OK",
      "data": {
        "coordinates": {"latitude": 39.9, "longitude": 116.4, "coord_type": "wgs84"},
        "address": {
          "full_address": "中国北京市朝阳区建国门外大街8号",
          "province": "北京市",
          "city": "北京市",
          "district": "朝阳区",
          "street": "建国门外大街",
          "house_number": "8号",
          "postcode": "100022",
          "confidence": 0.85
        },
        "processing_time": 0.123
      }
    }
    ```
- `GET/POST /geo/forward`
  - 请求参数：
    - `address`（必填，string）
    - `coordType`（可选，enum：`wgs84`|`gcj02`|`bd09`，默认 `wgs84`，决定输出坐标系）
  - 响应：
    ```json
    {
      "success": true,
      "code": 0,
      "message": "OK",
      "data": {
        "address": "北京市朝阳区建国门外大街8号",
        "coordinates": {"latitude": 39.909, "longitude": 116.461, "coord_type": "wgs84"},
        "formatted_address": "中国北京市朝阳区建国门外大街8号",
        "confidence": 0.85,
        "processing_time": 0.156
      }
    }
    ```

### 1.3 指标
- **准确率**：
  - 一线城市常见 POI 与行政区识别正确率 ≥ 97%；
  - 省市区路级别匹配率 ≥ 95%。
- **性能**：
  - 同 VPC（ECS ↔ Nominatim）P50 ≤ 200ms，P95 ≤ 500ms；
  - QPS ≥ 100（可水平扩展）。
- **可用性**：
  - 7×24 运行，月可用性 ≥ 99.9%。

### 1.4 依赖与配置
- 依赖：自建 Nominatim（PostgreSQL + PostGIS）。
- 环境变量：
  - `NOMINATIM_LOCAL_URL=http://nominatim:8080`
  - `TIMEOUT=300`
  - `DISABLE_PADDLE_DOWNLOAD=true`（与 OCR 无关时建议开启）
- 兼容：Python 3.8、geopy 2.2.0（已在 `requirements.txt` 锁定）。

### 1.5 错误码（建议）
- `400`：参数缺失/非法；
- `404`：未找到匹配地址/坐标；
- `429`：限流；
- `500`：服务内部错误；
- `503`：下游（Nominatim）不可用。

### 1.6 测试与验收要点
- 单元：经纬度边界值、非法字符串、空值；
- 集成：北京/上海/深圳/成都等样例点验证；常见地标正向/逆向互检；
- 性能：并发 100 压测，P95 < 500ms；
- 稳定：连续 14 天无重大故障；
- 回归：更新 OSM 数据后结果一致性抽检。

### 1.7 坐标系与转换规范
- 输入 `coordType`：`wgs84`（默认）|`gcj02`|`bd09`
- 输出坐标系：跟随请求 `coordType`（默认 `wgs84`）
- 转换规则：
  - `gcj02↔wgs84`、`bd09↔gcj02` 使用开源算法（如火星坐标转换），偏移/精度误差在 5–20m 范围；
  - 若未显式指定 `coordType`，按 `wgs84` 处理；
  - 对边界值（纬度±90，经度±180）做校验与截断。

---

## 2. 物流地址文本解析模块

### 2.1 范围
- 支持非结构化、不规则文本：
  - 任意空格、换行、标点、分隔符（/,，:；、\n 等）；
  - 多段文本（寄/收件信息可能交叉出现或重复）；
  - 特殊字符、英文夹杂、简繁体混用；
- 抽取字段：`name`、`phone`、`province`、`city`、`district`/`county`、`street`、`community`（社区/乡镇/乡）`township`、`detail_address`、`full_address`、`role`（sender/receiver/unknown）、`confidence`。

### 2.2 方法
- 预处理：
  - 统一全角/半角、去除多余空格、统一标点；
  - 行分块：按关键字（如“寄件人/收件人/收/寄/电话/地址/姓名/手机”）切分；
- 主流程：
  - 关键字行定位：基于词典+正则在各段中定位 `sender/receiver` 语义；
  - 手机号提取：`1[3-9]\d{9}`，对掩码（如 138****0000）做降权并保留原始段；
  - 地址解析：`cpca` 提取省市区，`jieba`+自定义词典切出街道/社区/园区/楼栋/房号；
  - 冗余合并：多段结果按置信度合并，冲突字段按优先级（关键字段、近邻关系）裁决；
- 归一化：
  - 省市区标准名映射（直辖市特殊处理：市=省）；
  - 街道/乡镇/社区标准化：同义词词典与黑名单清洗；
  - 最终 `full_address` 拼接规则：省+市+区县+街道/乡镇+社区+详细地址（去重复）。

### 2.3 接口（统一端点，去掉 v1/op 前缀）
- `POST /address/extract`
  - 请求：
    - 单条：`{"text": "任意不规则文本"}`
    - 批量：`{"texts": ["...", "..."]}`（最多 100 条）
  - 响应（单条示例）：
    ```json
    {
      "success": true,
      "code": 0,
      "message": "OK",
      "data": {
        "name": "张三",
        "phone": "13800000000",
        "province": "北京市",
        "city": "北京市",
        "district": "朝阳区",
        "street": "建国门外大街",
        "community": "建外街道",
        "detail_address": "8号 国贸写字楼A座1501",
        "role": "receiver",
        "full_address": "北京市北京市朝阳区建国门外大街建外街道8号 国贸写字楼A座1501",
        "confidence": 0.92,
        "extract_method": "cpca_jieba_rules"
      }
    }
    ```
- `POST /address/normalize`
  - 请求：`{"address": "北京市朝阳区建外街道建国门外大街8号 国贸写字楼A座1501"}`
  - 响应：与前述规范化输出一致，返回 `normalized` 与 `confidence`。

### 2.4 置信度策略
- 组成：`province/city/district` 覆盖度（0.3）+ 手机号有效性（0.15）+ 姓名模式（0.1）+ 街道/社区词典命中（0.25）+ 详细地址长度/模式匹配（0.1）+ 关键字上下文一致性（0.1）。
- 降权：
  - 掩码手机号、长度异常、超长字段、无任何行政区命中、地名/POI 仅口语化表达；
- 阈值：<0.5 低置信（可返回 but 标注 low_confidence），≥0.8 高置信。

### 2.5 词典与可配置项
- 自定义词典：道路/园区/小区/写字楼常见词库；
- 行政区名映射：别名与历史称谓映射；
- 关键字词库：寄/收、姓名/电话/地址等触发词；
- 黑名单：无意义词与噪声词；

### 2.6 测试与验收要点
- 不规则文本（多行、多空格、表情符号）稳健性；
- 直辖市、省辖市、县级市、自治县等多层级行政区覆盖；
- 街道/乡镇/社区/园区 POI 的识别与归一；
- 多段寄/收件信息正确归位（`role` 判定准确率 ≥ 97%）。

### 2.7 智能地址解析实现方案（cpca + jieba + 地址库 + 规则）
- 核心原则：优先使用免费开源库（cpca/jieba），利用定制词典与正则规则增强；必要时增加轻量地址库映射与纠错，保证可维护与可扩展。
- 处理流水线：
  1) 标准化预处理：全角/半角转换、空白折叠、标点统一、表情与控制符清理；
  2) 语义分段：按关键字与换行将文本分段；标注段角色候选（sender/receiver/unknown）；
  3) 手机/姓名优先抽取：手机号正则（含掩码降权）、姓名规则（2-4 汉字，或“先生/女士”后缀）；
  4) 行政区解析：cpca 提取省/市/区（县），对直辖市做“市=省”归一化；
  5) 细粒度切分：jieba + 自定义词典切出“街道/乡镇/社区/园区/楼栋/房号”等；
  6) 词典增强与纠错：
     - 地址别名词典（历史称谓/同义词）；
     - 小区/园区/写字楼高频词典；
     - 误写纠错（常见错别字映射，如“陕西→陕西”）；
  7) 详细地址提炼：在已识别组件之外的剩余短语做拼接与去噪（去冗余地名、重复行政区名）；
  8) 标准化输出：province/city/district/street/community/detail_address/full_address；
  9) 置信度计算：基于字段完整度、词典命中、手机号/姓名有效性、上下文一致性；
  10) 角色判定：优先依据段内关键词（寄/收/至/发件/收件），其次依据块位置（左上多为寄件，右下多为收件，仅适用于 OCR 版面）；
- 可选增强（保持开源/轻量）：
  - 轻量地址库映射文件（CSV/JSON）用于县级/街道别名归一（放置于 `python-service/resources/addresses/*.json`）；
  - 拼音模糊匹配（可选 `pypinyin`），处理同音误写（谨慎开启，默认关闭）。
- 性能目标：单条 P50 ≤ 50ms；批量线性扩展。

---

## 3. 申通面单 OCR 模块

### 3.1 范围
- 适配多种申通面单样式（新版/旧版，不同网点模板）；
- 抽取字段：
  - `waybill_no`（通常12位数字）
  - `carrier`（物流公司名，固定“申通快递”或模板识别）
  - `sender`（name/phone/address）
  - `receiver`（name/phone/address）
  - `route_code`（三段/四段码，如“123-45-67”）
  - `branch`（所属区域/网点）
  - `created_at`（可选，面单打印时间）
  - `confidence`

### 3.2 接口（统一端点，去掉 v1/op 前缀）
- `POST /ocr/sto/upload`
- `POST /ocr/sto/recognize`
- 查询参数：`max_size`（默认 640）、`quality`（默认 80）、`enhance`（默认 true）

### 3.3 处理流程
- 预处理：
  - 长边缩放到 `max_size`，JPEG 压缩到 `quality`；
  - 增强：亮度/对比度自适应、噪声抑制、倾斜校正；
- 识别：PaddleOCR 中文模型；
- 后处理：
  - 文本行分组（按几何位置聚类）；
  - 关键词与版式规则（运单号/寄件人/收件人/地址/电话）；
  - 条码/二维码解码（推荐开源：`pyzbar` 或 `zxing-cpp` 绑定），与 OCR 文本互校；
  - 三段/四段码模式：`\d{2,3}-\d{1,3}-\d{1,3}`（或四段），允许空格/换行变体；
  - 网点匹配：根据 route_code 与网点词典映射出 `branch`；
- 输出：结构化 JSON（已在上一版文档给出示例）。

### 3.4 指标
- 准确率目标：面单号 ≥ 98%，姓名/电话 ≥ 97%，地址 ≥ 93%，route_code ≥ 96%；
- 时延：CPU P50 ≤ 800ms，P95 ≤ 1500ms（单图）。

### 3.5 开源组件与版本
- OCR：`paddleocr==2.6.0.3`、`paddlepaddle==2.5.0`、`opencv-python-headless==4.7.0.72`
- 条码：`pyzbar`（需系统 `libzbar0`）或 `zxing-cpp`（Python 绑定 `pyzxing`/`zxings`），优先 `pyzbar` 简单直观。
- 版式：可选 `layoutparser`（如需更强版面理解，但体积增大），默认不引入。

### 3.6 测试与验收要点
- 多模板覆盖、遮挡/反光、模糊、旋转角度、低对比度；
- 条码/二维码解码稳定性与 OCR 文本互校；
- 错误与超时处理，返回清晰错误码与定位信息。

### 3.7 与地址解析联动（确保准确识别寄/收件人信息）
- 版面区域分组：
  - 使用几何位置聚类将文本行分为若干块；尝试在标题/标签中定位“寄件人/收件人/电话/地址”等关键词；
  - 每个块建立“候选角色”（sender/receiver）与“候选字段”（name/phone/address）的分数；
- 字段抽取策略：
  1) waybill_no：
     - 条码/二维码优先（`pyzbar`）；若失败再走 OCR 文本正则（12 位数字）；
     - 长度/全数字校验，必要时用上下文校验（如附近有“运单号/面单号”标签）；
  2) phone/name：
     - 正则与姓名规则先抽；
     - 若同一块同时出现多个手机号，结合“寄/收”关键词判定归属；
  3) address：
     - 将块内文本合并为段落，输入地址解析模块（2.7 流水线）；
     - 返回 province/city/district/street/community/detail_address；
  4) route_code（三段/四段码）：
     - 正则 `\d{2,3}-\d{1,3}-\d{1,3}`（或四段）；
     - 去除噪声字符，支持空格/换行变体；
  5) branch（所属网点）：
     - 通过 `route_code` 与网点词典映射（JSON）；
- 角色判定融合：
  - 关键词优先级：收件>寄件；
  - 若关键词缺失：按版面位置启发式（上/左多寄件，下/右多收件），并结合手机号是否与地址同段；
  - 若仍不确定，标注 `role=unknown` 并适度降低 `confidence`；
- 质量互校：
  - OCR 文本地址解析结果与条码信息不一致时，优先可信度高者；
  - 面单号/route_code/branch 三者不一致时返回 `warnings` 字段提示。

### 3.8 资源与词典（建议）
- `resources/addresses/alias.json`：行政区/街道别名与历史称谓映射；
- `resources/addresses/poi_frequent.txt`：高频小区/园区/写字楼；
- `resources/sto/branch_map.json`：route_code → 网点映射；
- `resources/keywords/address_keywords.txt`：寄/收/电话/地址等关键字；

---

## 4. 通用型 OCR 模块

### 4.1 端点
- `POST /ocr/upload`（文件）
- `POST /ocr/recognize`（base64）

### 4.2 方案选型（免费开源）
- 方案 A：PaddleOCR（当前已采用）
  - 优点：中文识别优秀，部署简单，CPU 也有较好性能；
  - 缺点：模型体积较大，初始化耗时，需要预热与模型缓存；
- 方案 B：Tesseract OCR + 中文训练集
  - 优点：完全开源、轻量；
  - 缺点：对复杂版面与场景文字效果一般，易受噪声影响；
- 方案 C：PaddleOCR + 条码库（组合）
  - 优点：场景通用 + 条码增强对票据/面单等场景友好；
  - 结论：推荐。

### 4.3 处理与参数
- 预处理：`max_size`、`quality`、`enhance` 同申通；
- 输出：与申通一致但不包含 `shipment` 字段；
- 错误码：`40001/41301/41501/40801/50001`。

---

## 5. 横切关注点
### 5.1 安全
- 全部服务默认内网访问；外网需加鉴权与限流；
- 敏感信息（手机号、姓名）日志脱敏；
- 统一时区与结构化日志，便于审计。

### 5.2 监控
- 健康检查：`/health`；
- 指标：QPS、P50/P95、错误率、OCR 初始化状态、Nominatim 可用性；
- 告警：阈值超限、依赖不可用、磁盘/内存告警。

### 5.3 版本与兼容（端点层面）
- 对外推荐端点：`/geo/*`、`/address/*`、`/ocr/*`、`/ocr/sto/*`（均无 `v1/op` 前缀）；
- 兼容端点：无（重构后不再参考现有 app.py 旧路径）；
- 网关负责前缀与鉴权、限流等横切能力，对应 backend 的 `v1/op/*` 路径。

### 5.4 资源与词典管理
- 词典以小文件形式纳入仓库（或配置中心），版本化管理；
- 变更需回归测试关键样例集（覆盖各省/直辖市、常见园区与面单模板）。

---

## 6. 里程碑与验收
- M1：Nominatim 自建与联调通过（逆/正解析，≥ 97% 行政区准确率）；
- M2：地址文本解析置信度策略稳定（≥ 95% 关键字段准确率）；
- M3：申通面单 OCR 字段抽取 ≥ 95% 精度；
- GA：压测、SLA/告警、灰度与回滚策略完善，连续稳定运行 14 天。 