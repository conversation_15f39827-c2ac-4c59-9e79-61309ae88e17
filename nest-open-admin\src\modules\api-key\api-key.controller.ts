import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  HttpCode,
  HttpStatus,
  Request,
  Put,
  UseGuards,
  BadRequestException,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ApiKeyService } from './api-key.service';
import {
  CreateApiKeyDto,
  UpdateApiKeyDto,
  QueryApiKeyDto,
  RegenerateApiKeyDto,
  ApiKeyResponseDto,
  ApiKeyListResponseDto,
  ApiKeyStatsDto,
} from './dto/api-key.dto';
import { ApiResult } from '../../common/decorators/api-result.decorator';
import { UnifiedAuthGuard } from '../../common/guards/unified-auth.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { AuthUser } from '../../common/types/auth.types';
import { Public } from '../../common/decorators/public.decorator';
import * as bcrypt from 'bcrypt';
import { GenerateUUID } from '@/common/utils';
import {
  UseAuthStrategy,
  AuthStrategy,
} from '../../common/decorators/auth-strategy.decorator';
import { Not, IsNull } from 'typeorm';

@ApiTags('API密钥管理')
@Controller('api-key')
@UseGuards(UnifiedAuthGuard)
@UseAuthStrategy(AuthStrategy.JWT)
export class ApiKeyController {
  private readonly logger = new Logger(ApiKeyController.name);

  constructor(private readonly apiKeyService: ApiKeyService) {}

  @Post()
  @ApiOperation({ summary: '创建API密钥' })
  @ApiResponse({ status: 201, description: '创建成功' })
  @ApiResult()
  @Roles('admin', 'user')
  async create(
    @Body() createApiKeyDto: CreateApiKeyDto,
  ): Promise<ApiKeyResponseDto> {
    return this.apiKeyService.createApiKey(createApiKeyDto);
  }

  @Get()
  // @Public()
  @ApiOperation({ summary: '获取API密钥列表' })
  @ApiResult()
  @Roles('admin', 'user')
  async findAll(
    @Query() queryParams: QueryApiKeyDto,
    @CurrentUser() user: AuthUser,
  ): Promise<ApiKeyListResponseDto> {
    // 非管理员只能查看自己的API密钥
    if (user.role !== 'admin' && !queryParams.userId) {
      queryParams.userId = user.id;
    }

    return this.apiKeyService.findAll(queryParams);
  }

  @Get('stats')
  @ApiOperation({ summary: '获取API密钥统计信息' })
  @ApiQuery({ name: 'userId', required: false, description: '用户ID（可选）' })
  async getStats(@Query('userId') userId?: number): Promise<ApiKeyStatsDto> {
    return await this.apiKeyService.getStats(userId);
  }

  @Post('sync-cache')
  @ApiOperation({ summary: '同步所有API密钥到缓存' })
  @ApiResult()
  @Roles('admin')
  async syncApiKeysToCache(): Promise<{ message: string; count: number }> {
    try {
      this.logger.log('手动触发同步所有API密钥到缓存...');
      const count = await this.apiKeyService.loadApiKeysToCache();
      this.logger.log(`成功同步 ${count} 个API密钥到缓存`);
      return {
        message: '同步API密钥到缓存成功',
        count,
      };
    } catch (error) {
      this.logger.error(`同步API密钥到缓存失败: ${error.message}`, error.stack);
      throw new BadRequestException(`同步API密钥到缓存失败: ${error.message}`);
    }
  }

  @Get('user/:userId')
  @ApiOperation({ summary: '获取指定用户的API密钥列表' })
  async getUserApiKeys(
    @Param('userId', ParseIntPipe) userId: number,
    @CurrentUser() user: AuthUser,
  ): Promise<ApiKeyResponseDto[]> {
    // 非管理员只能查看自己的API密钥列表
    if (user.role !== 'admin' && userId !== user.id) {
      throw new BadRequestException('无权查看此用户的API密钥列表');
    }

    return await this.apiKeyService.getUserApiKeysWithSecret(userId);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取API密钥详情' })
  @ApiResult()
  async findOne(
    @Param('id') id: string,
    @CurrentUser() user: AuthUser,
  ): Promise<ApiKeyResponseDto> {
    const apiKey = await this.apiKeyService.findOne(+id);

    // 非管理员只能查看自己的API密钥
    if (user.role !== 'admin' && apiKey.userId !== user.id) {
      throw new BadRequestException('无权查看此API密钥');
    }

    return apiKey;
  }

  @Put(':id')
  @ApiOperation({ summary: '更新API密钥' })
  @ApiResult()
  @Roles('admin', 'user')
  async update(
    @Param('id') id: string,
    @Body() updateApiKeyDto: UpdateApiKeyDto,
    @CurrentUser() user: AuthUser,
  ): Promise<ApiKeyResponseDto> {
    const apiKey = await this.apiKeyService.findOne(+id);

    // 非管理员只能更新自己的API密钥
    if (user.role !== 'admin' && apiKey.userId !== user.id) {
      throw new BadRequestException('无权更新此API密钥');
    }

    return this.apiKeyService.update(+id, updateApiKeyDto);
  }

  @Post('regenerate')
  @ApiOperation({ summary: '重新生成API密钥' })
  @ApiResult()
  @Roles('admin', 'user')
  async regenerate(
    @Body() dto: RegenerateApiKeyDto,
    @CurrentUser() user: AuthUser,
  ): Promise<ApiKeyResponseDto> {
    const apiKey = await this.apiKeyService.findOne(dto.id);

    // 非管理员只能重新生成自己的API密钥
    if (user.role !== 'admin' && apiKey.userId !== user.id) {
      throw new BadRequestException('无权重新生成此API密钥');
    }

    return this.apiKeyService.regenerateApiKey(dto);
  }

  @Post('revoke/:id')
  @ApiOperation({ summary: '撤销API密钥' })
  @ApiResult()
  @Roles('admin', 'user')
  async revokeApiKey(
    @Param('id') id: string,
    @CurrentUser() user: AuthUser,
  ): Promise<{ message: string }> {
    const apiKey = await this.apiKeyService.findOne(+id);

    // 非管理员只能撤销自己的API密钥
    if (user.role !== 'admin' && apiKey.userId !== user.id) {
      throw new BadRequestException('无权撤销此API密钥');
    }

    await this.apiKeyService.revokeApiKey(+id);
    return { message: `API密钥 #${id} 已成功撤销` };
  }

  @Post('activate/:id')
  @ApiOperation({ summary: '激活API密钥' })
  @ApiResult()
  @Roles('admin', 'user')
  async activateApiKey(
    @Param('id') id: string,
    @CurrentUser() user: AuthUser,
  ): Promise<{ message: string }> {
    const apiKey = await this.apiKeyService.findOne(+id);

    // 非管理员只能激活自己的API密钥
    if (user.role !== 'admin' && apiKey.userId !== user.id) {
      throw new BadRequestException('无权激活此API密钥');
    }

    await this.apiKeyService.activateApiKey(+id);
    return { message: `API密钥 #${id} 已成功激活` };
  }

  @Put(':id/mark-viewed')
  @ApiOperation({ summary: '标记密钥已被查看' })
  @HttpCode(HttpStatus.OK)
  async markAsViewed(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: AuthUser,
  ): Promise<{ message: string }> {
    const apiKey = await this.apiKeyService.findOne(id);

    // 非管理员只能标记自己的API密钥为已查看
    if (user.role !== 'admin' && apiKey.userId !== user.id) {
      throw new BadRequestException('无权标记此API密钥为已查看');
    }

    await this.apiKeyService.markSecretAsViewed(id);
    return {message: '密钥已标记为已查看' };
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除API密钥' })
  @ApiResult()
  @Roles('admin', 'user')
  async remove(
    @Param('id') id: string,
    @CurrentUser() user: AuthUser,
  ): Promise<{ message: string }> {
    const apiKey = await this.apiKeyService.findOne(+id);

    // 非管理员只能删除自己的API密钥
    if (user.role !== 'admin' && apiKey.userId !== user.id) {
      throw new BadRequestException('无权删除此API密钥');
    }

    return this.apiKeyService.remove(+id);
  }

  @Delete('batch')
  @ApiOperation({ summary: '批量删除API密钥' })
  @HttpCode(HttpStatus.OK)
  async batchRemove(@Body('ids') ids: number[]): Promise<{ message: string }> {
    await this.apiKeyService.batchRemove(ids);
    return { message: `已成功删除 ${ids.length} 个API密钥` };
  }

  @Get('stats/usage')
  @ApiOperation({ summary: '获取API密钥使用统计' })
  @ApiResult()
  @Roles('admin', 'user')
  async getApiKeyStats(
    @Query('userId') userId: number,
    @CurrentUser() user: AuthUser,
  ): Promise<ApiKeyStatsDto> {
    // 非管理员只能查看自己的API密钥统计
    if (user.role !== 'admin' && userId !== user.id) {
      throw new BadRequestException('无权查看此用户的API密钥统计');
    }

    return this.apiKeyService.getApiKeyStats(userId || user.id);
  }

  @Post('update-key')
  @ApiOperation({ summary: '更新API密钥并更新缓存' })
  @ApiResult()
  async updateApiKey(
    @Body() body: { apiKey: string; id?: number; newApiKey?: string },
  ): Promise<{ message: string; oldApiKey?: string; newApiKey: string }> {
    try {
      this.logger.log(`开始更新API密钥: ${body.apiKey.substring(0, 10)}...`);

      // 生成新的API密钥（如果未提供）
      const newApiKey = body.newApiKey || `ak_${GenerateUUID()}`;

      if (body.id) {
        // 如果提供了ID，则更新数据库中的API密钥
        const apiKey = await this.apiKeyService.findOne(body.id);
        if (!apiKey) {
          throw new NotFoundException(`API密钥 #${body.id} 不存在`);
        }

        // 获取旧API密钥 - 需要从数据库中直接获取
        const apiKeyEntity: any = await this.apiKeyService.findEntityById(
          body.id,
        );
        const oldApiKey = apiKeyEntity?.apiKey;

        // 更新数据库中的API密钥
        await this.apiKeyService.updateApiKey(body.id, newApiKey);

        // 更新缓存
        await this.apiKeyService.loadApiKeyToCache(body.id);

        return {
          message: `API密钥 #${body.id} 已更新并同步到缓存`,
          oldApiKey,
          newApiKey,
        };
      }

      return {
        message: '已生成新的API密钥，但未更新到数据库',
        newApiKey,
      };
    } catch (error) {
      this.logger.error('更新API密钥失败:', error);
      throw new BadRequestException(`更新API密钥失败: ${error.message}`);
    }
  }

  /**
   * 管理员端点：重新同步所有API密钥的Secret Key到Redis
   */
  @Post('admin/resync-secret-keys')
  @ApiOperation({ summary: '重新同步所有API密钥的Secret Key到Redis' })
  @ApiResponse({ status: 200, description: '同步成功' })
  async resyncAllSecretKeysToRedis(): Promise<{ message: string; syncedCount: number }> {
    const syncedCount = await this.apiKeyService.resyncAllSecretKeysToRedis();
    return { 
      message: `成功同步 ${syncedCount} 个API密钥的Secret Key到Redis`,
      syncedCount
    };
  }

  /**
   * 管理员端点：获取指定API密钥的Secret Key
   */
  @Get('admin/secret-key/:apiKey')
  @ApiOperation({ summary: '获取指定API密钥的Secret Key' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getSecretKeyByApiKey(@Param('apiKey') apiKey: string): Promise<{ apiKey: string; secretKey: string | null }> {
    // 首先查找API密钥实体
    const apiKeyEntity = await this.apiKeyService.findByApiKey(apiKey);
    
    if (!apiKeyEntity) {
      throw new NotFoundException(`API密钥 ${apiKey} 不存在`);
    }
    
    // 获取Secret Key
    const secretKey = await this.apiKeyService.getSecretKeyForApiKey(apiKeyEntity.id);
    
    return { 
      apiKey,
      secretKey
    };
  }

  /**
   * 管理员端点：检查Redis中是否存在指定API密钥的Secret Key
   */
  @Get('admin/check-redis-secret/:apiKey')
  @ApiOperation({ summary: '检查Redis中是否存在指定API密钥的Secret Key' })
  @ApiResponse({ status: 200, description: '检查成功' })
  async checkRedisSecretKey(@Param('apiKey') apiKey: string): Promise<{ apiKey: string; exists: boolean; apiKeyExists: boolean; secretKeyExists: boolean; apiKeyId?: number }> {
    // 首先查找API密钥实体
    const apiKeyEntity = await this.apiKeyService.findByApiKey(apiKey);
    
    if (!apiKeyEntity) {
      return {
        apiKey,
        exists: false,
        apiKeyExists: false,
        secretKeyExists: false
      };
    }
    
    // 检查Redis中是否存在API密钥
    const redisService = this.apiKeyService['redisService'];
    const cacheKey = `api_key:${apiKey}`;
    const cachedData = await redisService.get(cacheKey);
    const apiKeyExists = !!cachedData;
    
    // 检查Redis中是否存在Secret Key
    const secretKey = `api_key_secret:${apiKeyEntity.id}`;
    const cachedSecretKey = await redisService.get(secretKey);
    const secretKeyExists = !!cachedSecretKey;
    
    return { 
      apiKey,
      exists: apiKeyExists && secretKeyExists,
      apiKeyExists,
      secretKeyExists,
      apiKeyId: apiKeyEntity.id
    };
  }

  /**
   * 管理员端点：手动将指定API密钥的Secret Key同步到Redis
   */
  @Post('admin/sync-secret-to-redis/:apiKey')
  @ApiOperation({ summary: '手动将指定API密钥的Secret Key同步到Redis' })
  @ApiResponse({ status: 200, description: '同步成功' })
  async syncSecretKeyToRedis(@Param('apiKey') apiKey: string): Promise<{ apiKey: string; success: boolean; message: string }> {
    // 首先查找API密钥实体
    const apiKeyEntity = await this.apiKeyService.findByApiKey(apiKey);
    
    if (!apiKeyEntity) {
      throw new NotFoundException(`API密钥 ${apiKey} 不存在`);
    }
    
    try {
      // 获取完整的API密钥实体（包括tempSecretKey）
      const fullEntity = await this.apiKeyService.findEntityById(apiKeyEntity.id);
      
      if (!fullEntity || !fullEntity.tempSecretKey) {
        return {
          apiKey,
          success: false,
          message: `API密钥 ${apiKey} 没有可用的Secret Key`
        };
      }
      
      // 同步到Redis
      const redisService = this.apiKeyService['redisService'];
      const secretKey = `api_key_secret:${apiKeyEntity.id}`;
      await redisService.setex(secretKey, 86400, fullEntity.tempSecretKey);
      
      return {
        apiKey,
        success: true,
        message: `成功将API密钥 ${apiKey} 的Secret Key同步到Redis`
      };
    } catch (error) {
      return {
        apiKey,
        success: false,
        message: `同步失败: ${error.message}`
      };
    }
  }

  /**
   * 管理员端点：加密所有已存在的API密钥Secret Key
   * 将现有的tempSecretKey加密并存储到encryptedSecretKey字段
   */
  @Post('admin/encrypt-all-secret-keys')
  @ApiOperation({ summary: '加密所有API密钥的Secret Key' })
  @ApiResponse({ status: 200, description: '加密成功' })
  @Roles('admin')
  async encryptAllSecretKeys(): Promise<{ message: string; encrypted: number; skipped: number; failed: number }> {
    try {
      this.logger.log('开始加密所有API密钥的Secret Key...');
      
      // 查询所有包含tempSecretKey的API密钥
      const apiKeys = await this.apiKeyService['repository'].find({
        where: [
          { tempSecretKey: Not('') }, // 非空字符串
          { tempSecretKey: Not(IsNull()) } // 非null
        ]
      });
      
      this.logger.log(`找到 ${apiKeys.length} 个包含明文Secret Key的API密钥`);
      
      let encryptedCount = 0;
      let skippedCount = 0;
      let failedCount = 0;
      
      // 遍历并加密
      for (const apiKey of apiKeys) {
        try {
          // 检查是否已经有加密密钥
          if (apiKey.encryptedSecretKey) {
            this.logger.debug(`API密钥 #${apiKey.id} 已有加密Secret Key，跳过`);
            skippedCount++;
            continue;
          }
          
          // 检查是否有明文密钥
          if (!apiKey.tempSecretKey) {
            this.logger.warn(`API密钥 #${apiKey.id} 没有明文Secret Key，跳过`);
            skippedCount++;
            continue;
          }
          
          // 加密明文密钥
          const encryptedSecret = this.apiKeyService['encryptSecretKey'](apiKey.tempSecretKey);
          if (!encryptedSecret) {
            this.logger.error(`API密钥 #${apiKey.id} 的Secret Key加密失败`);
            failedCount++;
            continue;
          }
          
          // 更新数据库
          apiKey.encryptedSecretKey = encryptedSecret;
          await this.apiKeyService['repository'].save(apiKey);
          
          this.logger.debug(`API密钥 #${apiKey.id} 的Secret Key已加密`);
          encryptedCount++;
        } catch (error) {
          this.logger.error(`加密API密钥 #${apiKey.id} 的Secret Key失败: ${error.message}`);
          failedCount++;
        }
      }
      
      const message = `API密钥Secret Key加密完成，共处理 ${apiKeys.length} 个密钥，` +
        `加密 ${encryptedCount} 个，跳过 ${skippedCount} 个，失败 ${failedCount} 个`;
      
      this.logger.log(message);
      return { 
        message,
        encrypted: encryptedCount,
        skipped: skippedCount,
        failed: failedCount
      };
    } catch (error) {
      this.logger.error(`加密API密钥Secret Key失败: ${error.message}`, error.stack);
      throw new BadRequestException(`加密API密钥Secret Key失败: ${error.message}`);
    }
  }
}
