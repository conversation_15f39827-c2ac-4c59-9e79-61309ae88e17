# 认证守卫修复总结

## 问题描述

前端在API测试页面输入密钥点击"发送请求"按钮后，接口返回401错误。从后端日志可以看出：

```
[UnifiedAuthGuard] 使用JWT认证策略: POST /v1/op/address/extract?mode=async
[UnifiedAuthGuard] 认证守卫错误: JWT认证失败: 缺少令牌
```

## 问题分析

### 根本原因
网关控制器没有正确设置认证策略装饰器，导致 `UnifiedAuthGuard` 默认使用JWT认证策略，而不是API密钥签名认证策略。

### 具体问题
1. **地址网关控制器**：缺少 `@UseAuthStrategy(AuthStrategy.API_KEY)` 装饰器
2. **地理网关控制器**：缺少 `@UseAuthStrategy(AuthStrategy.API_KEY)` 装饰器  
3. **主网关控制器**：缺少 `@UseAuthStrategy(AuthStrategy.API_KEY)` 装饰器
4. **OCR网关控制器**：已正确设置（✅）
5. **申通OCR网关控制器**：已正确设置（✅）

## 修复方案

### 1. 修复地址网关控制器

**文件**: `backend/src/modules/gateway/controllers/address-gateway.controller.ts`

#### 修复前
```typescript
@ApiTags('地址网关')
@Controller('address')
@UseGuards(UnifiedAuthGuard)
@UseInterceptors(UnifiedInterceptor)
@ApiBearerAuth()
export class AddressGatewayController {
```

#### 修复后
```typescript
import { UseAuthStrategy, AuthStrategy } from '@/common/decorators/auth-strategy.decorator';

@ApiTags('地址网关')
@Controller('address')
@UseGuards(UnifiedAuthGuard)
@UseInterceptors(UnifiedInterceptor)
@UseAuthStrategy(AuthStrategy.API_KEY)
@ApiBearerAuth()
export class AddressGatewayController {
```

### 2. 修复地理网关控制器

**文件**: `backend/src/modules/gateway/controllers/geo-gateway.controller.ts`

#### 修复前
```typescript
@ApiTags('地理坐标网关')
@Controller('geo')
@UseGuards(UnifiedAuthGuard)
@UseInterceptors(UnifiedInterceptor)
@ApiBearerAuth()
export class GeoGatewayController {
```

#### 修复后
```typescript
import { UseAuthStrategy, AuthStrategy } from '@/common/decorators/auth-strategy.decorator';

@ApiTags('地理坐标网关')
@Controller('geo')
@UseGuards(UnifiedAuthGuard)
@UseInterceptors(UnifiedInterceptor)
@UseAuthStrategy(AuthStrategy.API_KEY)
@ApiBearerAuth()
export class GeoGatewayController {
```

### 3. 修复主网关控制器

**文件**: `backend/src/modules/gateway/controllers/gateway.controller.ts`

#### 修复前
```typescript
@ApiTags('网关管理')
@Controller()
@UseGuards(UnifiedAuthGuard)
@UseInterceptors(UnifiedInterceptor)
export class GatewayController {
```

#### 修复后
```typescript
import { UseAuthStrategy, AuthStrategy } from '@/common/decorators/auth-strategy.decorator';

@ApiTags('网关管理')
@Controller()
@UseGuards(UnifiedAuthGuard)
@UseInterceptors(UnifiedInterceptor)
@UseAuthStrategy(AuthStrategy.API_KEY)
export class GatewayController {
```

## 认证策略说明

### 认证策略装饰器
```typescript
export enum AuthStrategy {
  JWT = 'jwt',        // JWT令牌认证
  API_KEY = 'api_key' // API密钥签名认证
}

export const UseAuthStrategy = (strategy: AuthStrategy) => {
  return SetMetadata(AUTH_STRATEGY_KEY, strategy);
};
```

### 统一认证守卫逻辑
```typescript
// 获取认证策略
const authStrategy = this.reflector.getAllAndOverride<AuthStrategy>(
  AUTH_STRATEGY_KEY,
  [handler, classRef],
);

if (authStrategy === AuthStrategy.API_KEY) {
  // 使用签名认证
  authResult = await this.validateSignatureAuth(authInfo, request);
} else {
  // 默认使用JWT认证策略
  authResult = await this.validateJwtWithCircuitBreaker(jwtToken);
}
```

## 修复后的认证流程

### API密钥签名认证流程
1. **提取认证信息**：从请求头中提取 `X-API-KEY`、`X-Timestamp`、`X-Nonce`、`X-Signature`
2. **验证时间戳**：检查请求是否在15分钟内
3. **验证Nonce**：防止重放攻击
4. **获取密钥信息**：从Redis缓存中获取API密钥和Secret Key
5. **验证签名**：重建规范化请求字符串，计算HMAC-SHA256签名并比较
6. **更新使用记录**：记录最后使用时间和IP地址

### 请求头格式
```
X-API-KEY: ak-579b27759508f152525a4e5a567efd5a
X-Timestamp: 1754719274
X-Nonce: a47aa38365bb700e5c7b605ef307ba90
X-Signature: cHwt11McCH1ZTjT6eKdj8DiMe4kPEDNTZPZ+gkOaUMk=
```

## 诊断工具

### API密钥缓存检查脚本
创建了 `backend/scripts/check-api-keys.ts` 脚本来诊断API密钥缓存问题：

```bash
cd backend
npx ts-node scripts/check-api-keys.ts
```

该脚本会：
1. 检查数据库中所有API密钥
2. 验证Redis缓存中的密钥数据
3. 检查Secret Key是否正确缓存
4. 自动修复缺失的缓存数据

## 预期效果

修复后，前端API测试应该能够：

1. ✅ **正确识别认证策略**：使用API密钥签名认证而不是JWT
2. ✅ **成功验证签名**：正确验证HMAC-SHA256签名
3. ✅ **返回200状态码**：认证成功后正常处理请求
4. ✅ **支持所有网关接口**：地址、地理、OCR等所有服务

## 测试验证

### 1. 重启后端服务
```bash
cd backend
npm run start:dev
```

### 2. 测试API调用
在前端API测试页面：
1. 选择"地址提取"服务
2. 输入API密钥和Secret Key
3. 点击"发送请求"
4. 验证返回200状态码

### 3. 检查日志
确认日志显示：
```
[UnifiedAuthGuard] 使用签名认证策略: POST /v1/op/address/extract?mode=async
[UnifiedAuthGuard] 签名验证成功: ak-579b27759508f152525a4e5a567efd5a, 用户ID: 4
```

## 注意事项

1. **缓存同步**：确保API密钥数据已正确同步到Redis
2. **Secret Key格式**：确保Secret Key使用正确的AES加密格式
3. **时间同步**：确保服务器时间与客户端时间同步
4. **网络延迟**：考虑网络延迟对时间戳验证的影响

## 后续优化建议

1. **监控告警**：添加认证失败率监控
2. **缓存预热**：服务启动时预热API密钥缓存
3. **性能优化**：优化Redis查询性能
4. **安全增强**：添加IP白名单验证 