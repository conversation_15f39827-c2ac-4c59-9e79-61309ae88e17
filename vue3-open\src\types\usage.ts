// 时间范围类型
export type TimeRange = '7d' | '30d' | '90d' | '1y' | 'custom'

// 使用统计概览
export interface UsageStats {
  totalCalls: number
  successCalls: number
  errorCalls: number
  totalCost: number
  averageResponseTime: number
  errorRate: number
  trends: {
    callsGrowth: number
    costGrowth: number
    errorRateChange: number
  }
  topServices: {
    serviceId: string
    serviceName: string
    calls: number
    cost: number
  }[]
}

// 使用记录
export interface UsageRecord {
  id: string
  timestamp: string
  serviceId: string
  serviceName: string
  apiKeyId: string
  apiKeyName: string
  method: string
  endpoint: string
  statusCode: number
  responseTime: number
  cost: number
  requestSize: number
  responseSize: number
  userAgent?: string
  ip?: string
  errorMessage?: string
}

// 使用趋势数据点
export interface UsageTrend {
  timestamp: string
  calls: number
  successCalls: number
  errorCalls: number
  cost: number
  averageResponseTime: number
}

// 服务使用分布
export interface ServiceUsageDistribution {
  serviceId: string
  serviceName: string
  calls: number
  cost: number
  percentage: number
  errorRate: number
  averageResponseTime: number
}

// 使用统计查询参数
export interface UsageQueryParams {
  timeRange?: TimeRange
  startDate?: string
  endDate?: string
  serviceId?: string
  apiKeyId?: string
  status?: 'success' | 'error' | 'all'
  page?: number
  pageSize?: number
  sortBy?: 'timestamp' | 'cost' | 'responseTime'
  sortOrder?: 'asc' | 'desc'
}

// 使用统计图表数据
export interface UsageChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    borderColor?: string
    backgroundColor?: string
    fill?: boolean
  }[]
}

// API密钥使用统计
export interface ApiKeyUsageStats {
  apiKeyId: string
  apiKeyName: string
  totalCalls: number
  successCalls: number
  errorCalls: number
  totalCost: number
  lastUsed: string
  dailyLimit: number
  monthlyLimit: number
  dailyUsage: number
  monthlyUsage: number
  status: 'active' | 'suspended' | 'expired'
}

// 使用统计导出选项
export interface UsageExportOptions {
  format: 'csv' | 'excel' | 'json'
  timeRange?: TimeRange
  startDate?: string
  endDate?: string
  serviceId?: string
  apiKeyId?: string
  includeDetails: boolean
  includeMetrics: boolean
}