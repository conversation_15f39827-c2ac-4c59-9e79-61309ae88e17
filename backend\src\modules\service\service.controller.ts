import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ServiceService } from './service.service';
import { CreateServiceDto } from './dto/create-service.dto';
import { UpdateServiceDto } from './dto/update-service.dto';
import { ServiceResponseDto, ServiceListResponseDto } from './dto/service-response.dto';
import { ServiceEntity } from './entities/service.entity';
import { QueryServiceDto } from './dto/query-service.dto';
import { ApiResult } from '../../common/decorators/api-result.decorator';
import { Public } from '../../common/decorators/public.decorator';

@ApiTags('服务管理')
@Controller('services')
@ApiBearerAuth()
export class ServiceController {
  constructor(private readonly serviceService: ServiceService) {}

  @Post()
  @ApiOperation({ summary: '创建服务' })
  @ApiResult({
    status: HttpStatus.CREATED,
    description: '服务创建成功',
    dataSchema: { $ref: '#/components/schemas/ServiceResponseDto' }
  })
  async create(@Body() createServiceDto: CreateServiceDto): Promise<ServiceResponseDto> {
    const service = await this.serviceService.create(createServiceDto);
    return new ServiceResponseDto(service);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: '获取服务列表' })
  @ApiResult({
    description: '服务列表查询成功',
    dataSchema: { $ref: '#/components/schemas/ServiceListResponseDto' }
  })
  async findAll(@Query() queryDto: QueryServiceDto): Promise<ServiceListResponseDto> {
    return this.serviceService.findAllPaginated(queryDto);
  }

  @Get('code/:code')
  @Public()
  @ApiOperation({ summary: '根据代码获取服务' })
  @ApiParam({ name: 'code', description: '服务代码' })
  @ApiResult({
    description: '服务查询成功',
    dataSchema: { $ref: '#/components/schemas/ServiceResponseDto' }
  })
  async findByCode(@Param('code') code: string): Promise<ServiceResponseDto> {
    const service = await this.serviceService.findByCode(code);
    return new ServiceResponseDto(service);
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取服务' })
  @ApiParam({ name: 'id', description: '服务ID' })
  @ApiResult({
    description: '服务查询成功',
    dataSchema: { $ref: '#/components/schemas/ServiceResponseDto' }
  })
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<ServiceResponseDto> {
    const service = await this.serviceService.findOne(id);
    return new ServiceResponseDto(service);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新服务' })
  @ApiParam({ name: 'id', description: '服务ID' })
  @ApiResult({
    description: '服务更新成功',
    dataSchema: { $ref: '#/components/schemas/ServiceResponseDto' }
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateServiceDto: UpdateServiceDto
  ): Promise<ServiceResponseDto> {
    const service = await this.serviceService.update(id, updateServiceDto);
    return new ServiceResponseDto(service);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.OK) // 必须保留，覆盖DELETE默认返回204的行为
  @ApiOperation({ summary: '删除服务' })
  @ApiParam({ name: 'id', description: '服务ID' })
  @ApiResult({
    description: '服务删除成功'
  })
  async remove(@Param('id', ParseIntPipe) id: number): Promise<{ id: number; message: string }> {
    return this.serviceService.remove(id);
  }
}
