# python-service

面向开放平台后端（backend）的配套 Python 服务，提供：
- 地址文本解析（非结构化→结构化）：`/address/extract`、`/address/normalize`
- 申通快递面单 OCR：`/ocr/sto/upload`、`/ocr/sto/recognize`

实现遵循文档：`docs/模块需求与规格说明.md`、`docs/Python编码规范.md`、`docs/地理编码最优方案.md`

## 1. 功能与端点
- 地址解析（无批量接口）
  - `POST /address/extract`
    - 入参：`{"text": "任意不规则文本"}`
    - 出参：`name/phone/province/city/district/street/community/detail_address/full_address/role/confidence`
  - `POST /address/normalize`
    - 入参：`{"address": "原始地址"}`
    - 出参：`normalized`（标准化组件）与 `confidence`
- 申通面单 OCR
  - `POST /ocr/sto/upload`（文件上传）
  - `POST /ocr/sto/recognize`（Base64）
  - 查询参数：`max_size`（默认 640）、`quality`（默认 80）、`enhance`（默认 true）
  - 出参：`results`（文本框/文本/置信度）、`shipment`（面单结构化字段）、`process_time`、`image_info`、`barcodes`
- 健康检查
  - `GET /health`

## 2. 依赖与运行时
- 基线：Python 3.10（镜像 `python:3.10-slim`）
- 固定依赖见 `requirements.txt`：
  - Web: flask 2.2.5, flask-cors 3.0.10, Werkzeug 2.2.3, gunicorn 20.1.0
  - NLP/地址解析: jieba 0.42.1, cpca 0.5.5
  - 数值/数据: numpy 1.24.4, pandas 1.5.3
  - OCR/图像: opencv-python-headless ********, paddlepaddle 2.5.0, paddleocr *******, pyzbar 0.1.9
  - 工具: requests 2.31.0
- 系统库（Dockerfile 已安装）：`libgl1`、`libglib2.0-0`、`libsm6`、`libxrender1`、`libxext6`、`libzbar0`

## 3. 环境变量
- `DISABLE_PADDLE_DOWNLOAD`（默认 true）
  - 首次运行需下载 OCR 模型时，设为 `false` 完成预热；后续可恢复 `true` 并挂载模型缓存
- `TIMEOUT`（默认 300）：HTTP 超时
- `OCR_TIMEOUT`（默认 180）：OCR 处理超时
- `IMAGE_MAX_SIZE`（默认 640）：长边缩放阈值
- `IMAGE_QUALITY`（默认 80）：JPEG 压缩质量
- `IMAGE_ENHANCE`（默认 true）：是否启用简单增强
- `DICT_POI_PATH`：地址词典文件路径（默认 `resources/addresses/poi_frequent.txt`）
- `DICT_EXTRA_WORDS`：额外地址词条，逗号分隔，例如 `科技园,软件园,创意园`

## 4. Docker 构建与运行
- 构建镜像
  ```bash
  docker build -t python-service:latest ./python-service
  ```
- 运行容器（首次下载模型）
  ```bash
  docker run -p 8866:8866 \
    -e DISABLE_PADDLE_DOWNLOAD=false \
    -e TZ=Asia/Shanghai \
    --name python-service \
    python-service:latest
  ```
- 后续运行（挂载模型与日志缓存，避免重复下载）
  PaddleOCR 通常将模型缓存到 `/root/.paddleocr`（及 `/root/.paddle`）；建议挂载：
  ```bash
  docker run -p 8866:8866 \
    -e DISABLE_PADDLE_DOWNLOAD=true \
    -v /opt/paddle-cache:/root/.paddleocr \
    -v /opt/python-service-logs:/app/logs \
    --name python-service \
    python-service:latest
  ```

## 5. Docker Compose 集成
在工作区根目录 `docker-compose.yml` 添加（或新增）服务：
```yaml
services:
  python-service:
    build:
      context: ./python-service
      dockerfile: Dockerfile
    container_name: python-service
    ports:
      - "8866:8866"
    environment:
      TZ: Asia/Shanghai
      DISABLE_PADDLE_DOWNLOAD: "false"  # 首次预热为 false，预热完成后改为 true
      IMAGE_MAX_SIZE: "640"
      IMAGE_QUALITY: "80"
      IMAGE_ENHANCE: "true"
      # DICT_POI_PATH: "/data/poi_frequent.txt"  # 可选
      # DICT_EXTRA_WORDS: "科技园,软件园,创意园"    # 可选
    volumes:
      - ./data/paddle-cache:/root/.paddleocr
      - ./python-service/resources:/app/resources:ro
      - ./logs/python-service:/app/logs
    healthcheck:
      test: ["CMD-SHELL", "curl -sf http://localhost:8866/health > /dev/null"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
```
- 启动：`docker compose up -d --build`
- 预热完成后，将 `DISABLE_PADDLE_DOWNLOAD` 调整为 `true` 并重新 `up -d`，保留 `paddle-cache` 卷即可

## 6. 快速测试
- 健康检查
  ```bash
  curl -s http://localhost:8866/health
  ```
- 地址解析
  ```bash
  curl -s -X POST http://localhost:8866/address/extract \
    -H 'Content-Type: application/json' \
    -d '{"text":"广州市荔湾区荣兴路与花地大道北交叉口万象商业街一楼 张半仙 13190093579"}'

  curl -s -X POST http://localhost:8866/address/extract \
    -H 'Content-Type: application/json' \
    -d '{"text":"任大东 13190093579 北京市朝阳区朝外大街吉庆里佳汇中心B座京芝堂大药房 寄件地址"}'
  ```
- 申通面单（文件上传）
  ```bash
  curl -s -X POST 'http://localhost:8866/ocr/sto/upload?max_size=640&quality=80&enhance=true' \
    -F 'images=@/path/to/sto.jpg'
  ```
- 申通面单（Base64）
  ```bash
  curl -s -X POST 'http://localhost:8866/ocr/sto/recognize?max_size=640&quality=80&enhance=true' \
    -H 'Content-Type: application/json' \
    -d '{"image_base64":"<base64>"}'
  ```

## 7. 自定义地址词典
- 默认会尝试加载 `resources/addresses/poi_frequent.txt`（若存在），每行一个词条
- 可通过 `DICT_POI_PATH` 指定词典路径，或 `DICT_EXTRA_WORDS` 注入词条（逗号分隔）
- 推荐将词典文件随部署一并挂载为只读（见 compose 的 volumes）

## 8. 常见问题
- OCR 引擎未初始化
  - 日志提示 `OCR engine not initialized`：确认 `DISABLE_PADDLE_DOWNLOAD=false` 进行一次模型预热，或挂载已有 `/root/.paddleocr` 缓存
- 条码识别为空
  - 确认镜像包含 `libzbar0`（Dockerfile 已安装）；图像过小或模糊可调大 `max_size`，保持图像清晰
- 地址识别覆盖不全
  - 通过 `resources/addresses/poi_frequent.txt` 或 `DICT_EXTRA_WORDS` 增强词典；对于罕见地名可追加别名

## 9. 变更记录
- 2025-xx-xx：新增基于 cpca + jieba 的地址解析与申通面单识别；完善 Dockerfile 与部署说明；支持自定义词典加载与模型预热 


```
    docker compose -f docker-compose-python.yml up -d --build

        docker compose -f docker-compose-python.yml ps
    docker compose -f docker-compose-python.yml logs -f --tail=100 python-service
    
```