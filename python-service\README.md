# 地址提取与OCR服务

这个项目提供了两个主要功能：

1. **地址提取与坐标逆解析服务**：从自由文本中提取结构化地址，并且支持经纬度到地址的转换。
2. **PaddleOCR物流面单OCR服务**：提供物流面单的光学字符识别功能。

## 功能特点

### 地址提取与坐标逆解析
- 识别文本中的地址组成部分
- 提取姓名、电话号码
- 坐标反向解析为地址
- 批量处理地址和坐标

### PaddleOCR服务
- 支持图片文件上传和base64编码图片输入
- 面向物流面单的专业OCR识别
- 返回识别文本、位置和置信度
- 支持图像预处理和压缩优化

## API接口

### 地址提取接口
- `/extract`: 提取地址（兼容旧接口）
- `/extract-address`: 提取地址（新接口）
- `/rev-geo`: 坐标反向解析（支持GET和POST）
- `/b-rev-geo`: 批量坐标反向解析

### OCR接口
- `/predict/ocr_system`: 提供OCR识别功能，支持文件上传和base64编码
  - 支持以下查询参数:
    - `max_size`: 图像最大尺寸（长边）, 默认1600
    - `quality`: JPEG压缩质量(1-100), 默认85
    - `enhance`: 是否启用图像增强(true/false), 默认true

## 依赖包管理

由于依赖包体积较大（超过100MB），超出了代码托管平台的文件大小限制，因此这些依赖包不包含在Git仓库中。相关目录已添加到`.gitignore`文件中。

