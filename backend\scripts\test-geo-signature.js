const crypto = require('crypto');

/**
 * 测试地理服务签名计算
 */

// 后端签名算法（模拟）
function backendSignature(method, path, queryParams, body, timestamp, secretKey) {
  console.log('=== 后端签名计算 ===');
  console.log('方法:', method);
  console.log('路径:', path);
  console.log('查询参数:', queryParams);
  console.log('请求体:', body);
  console.log('时间戳:', timestamp);
  
  // 排序查询参数
  const sortedQuery = Object.keys(queryParams)
    .sort()
    .map(key => `${key}=${encodeURIComponent(queryParams[key])}`)
    .join('&');
  
  console.log('排序后的查询参数:', sortedQuery);
  
  // 处理请求体
  let bodyString = '';
  if (body && typeof body === 'object') {
    bodyString = JSON.stringify(body);
  } else if (body) {
    bodyString = String(body);
  }
  
  console.log('请求体字符串:', bodyString);
  
  const canonicalRequest = [
    method.toUpperCase(),
    path,
    sortedQuery,
    bodyString,
    timestamp
  ].join('\n');
  
  console.log('规范化请求字符串:');
  console.log(canonicalRequest);
  
  const signature = crypto
    .createHmac('sha256', secretKey)
    .update(canonicalRequest)
    .digest('base64');
  
  console.log('生成的签名:', signature);
  return signature;
}

// 前端签名算法（模拟）
function frontendSignature(method, path, queryParams, bodyData, timestamp, secretKey) {
  console.log('\n=== 前端签名计算 ===');
  console.log('方法:', method);
  console.log('路径:', path);
  console.log('查询参数:', queryParams);
  console.log('请求体数据:', bodyData);
  console.log('时间戳:', timestamp);
  
  // 前端路径处理
  let processedPath = path;
  if (!processedPath.startsWith('/v1')) {
    processedPath = '/v1' + processedPath;
  }
  console.log('处理后的路径:', processedPath);
  
  // 处理查询参数
  const params = { ...queryParams };
  
  // 如果请求体中有mode参数，确保它也在查询参数中
  if (bodyData && typeof bodyData === 'object' && bodyData.mode) {
    params.mode = bodyData.mode;
  }
  
  console.log('合并后的查询参数:', params);
  
  // 排序查询参数
  const sortedQuery = Object.keys(params)
    .sort()
    .map(key => `${key}=${encodeURIComponent(String(params[key]))}`)
    .join('&');
  
  console.log('排序后的查询参数:', sortedQuery);
  
  // 处理请求体
  let bodyString = '';
  if (bodyData) {
    if (typeof bodyData === 'string') {
      bodyString = bodyData;
    } else {
      try {
        bodyString = JSON.stringify(bodyData);
      } catch (error) {
        bodyString = String(bodyData);
      }
    }
  }
  
  console.log('请求体字符串:', bodyString);
  
  const canonicalRequest = [
    method.toUpperCase(),
    processedPath,
    sortedQuery,
    bodyString,
    timestamp
  ].join('\n');
  
  console.log('规范化请求字符串:');
  console.log(canonicalRequest);
  
  const signature = crypto
    .createHmac('sha256', secretKey)
    .update(canonicalRequest)
    .digest('base64');
  
  console.log('生成的签名:', signature);
  return signature;
}

// 测试用例：逆地理编码GET请求
console.log('测试逆地理编码GET请求签名');

const testCase = {
  method: 'GET',
  path: '/v1/op/geo/reverse',
  queryParams: {
    lat: '22.543096',
    lng: '114.057865',
    mode: 'async'
  },
  body: {},
  timestamp: '1754760120',
  secretKey: 'sk-93f682d5dc7bdbd36fcbd891340bb1e097fa9738d6aefb2c6cda908f646ca950'
};

const backendSig = backendSignature(
  testCase.method,
  testCase.path,
  testCase.queryParams,
  testCase.body,
  testCase.timestamp,
  testCase.secretKey
);

const frontendSig = frontendSignature(
  testCase.method,
  testCase.path,
  testCase.queryParams,
  testCase.body,
  testCase.timestamp,
  testCase.secretKey
);

console.log('\n=== 签名对比 ===');
console.log('后端签名:', backendSig);
console.log('前端签名:', frontendSig);
console.log('签名一致:', backendSig === frontendSig);
console.log('实际前端签名:', 'fnibKUI0iARKtuA6QqKcacMatGyA72yjyEYATTMQV6Q=');
console.log('实际后端签名:', '9MMHlc7FN3o+/FnPe6Dokvepj2uVpc3EJlOIx+crJyM=');

// 测试用例2：正地理编码GET请求
console.log('\n\n测试正地理编码GET请求签名');

const testCase2 = {
  method: 'GET',
  path: '/v1/op/geo/forward',
  queryParams: {
    lat: '22.543096',
    lng: '114.057865',
    mode: 'async'
  },
  body: {},
  timestamp: '1754760130',
  secretKey: 'sk-93f682d5dc7bdbd36fcbd891340bb1e097fa9738d6aefb2c6cda908f646ca950'
};

const backendSig2 = backendSignature(
  testCase2.method,
  testCase2.path,
  testCase2.queryParams,
  testCase2.body,
  testCase2.timestamp,
  testCase2.secretKey
);

const frontendSig2 = frontendSignature(
  testCase2.method,
  testCase2.path,
  testCase2.queryParams,
  testCase2.body,
  testCase2.timestamp,
  testCase2.secretKey
);

console.log('\n=== 签名对比 ===');
console.log('后端签名:', backendSig2);
console.log('前端签名:', frontendSig2);
console.log('签名一致:', backendSig2 === frontendSig2);
console.log('实际前端签名:', 'hW7+RWYEpBNXmqZyHI3vKs7qMNLriX0ZTUtCf1mb7ec=');
console.log('实际后端签名:', 'cND3TYcIG5+GrlC25URcakaxhOckQY+9bgaXZ1q6RzE=');
