import { Module, forwardRef } from '@nestjs/common';
import { ScheduleService } from './schedule.service';
import { ScheduleController } from './schedule.controller';
import { UserModule } from '../user/user.module';
import { UserServiceModule } from '../user-service/user-service.module';
import { ServiceModule } from '../service/service.module';
import { AlertRecordModule } from '../alert-record/alert-record.module';
import { CallRecordModule } from '../call-record/call-record.module';
import { OrderModule } from '../order/order.module';
import { ApiKeyModule } from '../api-key/api-key.module';

@Module({
  imports: [
    UserModule,
    UserServiceModule,
    ServiceModule,
    AlertRecordModule,
    CallRecordModule,
    OrderModule,
    forwardRef(() => ApiKeyModule),
  ],
  controllers: [ScheduleController],
  providers: [ScheduleService],
  exports: [ScheduleService],
})
export class ScheduleModule {}
