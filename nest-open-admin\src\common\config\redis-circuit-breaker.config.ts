import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Redis熔断器配置
 */
export interface RedisCircuitBreakerConfig {
  /** 失败阈值 - 连续失败多少次后打开熔断器 */
  failureThreshold: number;
  
  /** 恢复超时时间（毫秒） - 熔断器打开后多久尝试恢复 */
  recoveryTimeout: number;
  
  /** Redis操作超时时间（毫秒） */
  operationTimeout: number;
  
  /** 是否启用熔断器 */
  enabled: boolean;
  
  /** 监控窗口时间（毫秒） - 统计失败率的时间窗口 */
  monitoringWindow: number;
  
  /** 半开状态下的测试请求数量 */
  halfOpenMaxCalls: number;
}

/**
 * Redis熔断器配置服务
 */
@Injectable()
export class RedisCircuitBreakerConfigService {
  private readonly config: RedisCircuitBreakerConfig;

  constructor(private readonly configService: ConfigService) {
    this.config = {
      enabled: this.configService.get<boolean>('redis.circuitBreaker.enabled', true),
      failureThreshold: this.configService.get<number>('redis.circuitBreaker.failureThreshold', 5),
      recoveryTimeout: this.configService.get<number>('redis.circuitBreaker.recoveryTimeout', 60000),
      operationTimeout: this.configService.get<number>('redis.circuitBreaker.operationTimeout', 3000),
      monitoringWindow: this.configService.get<number>('redis.circuitBreaker.monitoringWindow', 60000),
      halfOpenMaxCalls: this.configService.get<number>('redis.circuitBreaker.halfOpenMaxCalls', 3),
    };
  }

  /**
   * 获取熔断器配置
   */
  getConfig(): RedisCircuitBreakerConfig {
    return { ...this.config };
  }

  /**
   * 检查熔断器是否启用
   */
  isEnabled(): boolean {
    return this.config.enabled;
  }

  /**
   * 获取失败阈值
   */
  getFailureThreshold(): number {
    return this.config.failureThreshold;
  }

  /**
   * 获取恢复超时时间
   */
  getRecoveryTimeout(): number {
    return this.config.recoveryTimeout;
  }

  /**
   * 获取操作超时时间
   */
  getOperationTimeout(): number {
    return this.config.operationTimeout;
  }

  /**
   * 获取监控窗口时间
   */
  getMonitoringWindow(): number {
    return this.config.monitoringWindow;
  }

  /**
   * 获取半开状态最大调用数
   */
  getHalfOpenMaxCalls(): number {
    return this.config.halfOpenMaxCalls;
  }
}