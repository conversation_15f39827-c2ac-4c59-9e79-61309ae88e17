// 检查任务状态
const mysql = require('mysql2/promise');

async function checkTaskStatus() {
  const taskId = '05d6de1d-c760-4d1a-8345-b47b327e6e89';
  
  console.log(`检查任务状态: ${taskId}`);
  
  // 检查Redis中的任务数据（模拟）
  console.log('\n=== 模拟Redis检查 ===');
  console.log(`Redis Key: task:${taskId}`);
  console.log('由于Redis连接问题，无法直接查询Redis数据');
  
  // 检查数据库中是否有相关记录
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '123456',
    database: 'openapidb'
  });
  
  try {
    console.log('\n=== 检查数据库中的任务记录 ===');
    
    // 检查call_record表
    const [callRecords] = await connection.execute(
      'SELECT * FROM call_record WHERE request_id LIKE ? ORDER BY created_at DESC LIMIT 5',
      [`%${taskId}%`]
    );
    
    console.log(`Call Record 匹配记录: ${callRecords.length}`);
    callRecords.forEach((record, index) => {
      console.log(`  ${index + 1}. ID: ${record.id}, RequestID: ${record.request_id}, Status: ${record.status}, Service: ${record.service_id}`);
    });
    
    // 检查最近的地址解析调用记录
    console.log('\n=== 最近的地址解析调用记录 ===');
    const [recentCalls] = await connection.execute(
      'SELECT * FROM call_record WHERE service_id = 5 ORDER BY created_at DESC LIMIT 3'
    );
    
    console.log(`最近的地址解析调用: ${recentCalls.length}`);
    recentCalls.forEach((record, index) => {
      console.log(`  ${index + 1}. RequestID: ${record.request_id}, Status: ${record.status}, Created: ${record.created_at}`);
    });
    
  } catch (error) {
    console.error('数据库查询失败:', error.message);
  } finally {
    await connection.end();
  }
}

checkTaskStatus().catch(console.error);
