import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsBoolean,
  Min<PERSON>ength,
  MaxLength,
  Min,
  IsInt,
  Max,
  IsIn,
  IsDateString,
  IsObject,
  IsArray,
} from 'class-validator';

/**
 * 创建用户服务关联DTO
 */
export class CreateUserServiceDto {
  @ApiProperty({ description: '用户ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  userId: number;

  @ApiProperty({ description: '服务ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  serviceId: number;

  @ApiPropertyOptional({ description: '已调用次数', example: 0, minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  callCount?: number = 0;

  @ApiPropertyOptional({ description: '免费次数', example: 100, minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  freeCount?: number = 0;

  @ApiPropertyOptional({ description: '购买次数', example: 1000, minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  purchasedCount?: number = 0;

  @ApiPropertyOptional({ description: '总次数', example: 1100, minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  totalCount?: number = 0;

  @ApiPropertyOptional({ description: '剩余次数', example: 1100, minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  remainingCount?: number = 0;

  @ApiPropertyOptional({ description: '是否启用', example: true })
  @IsOptional()
  @IsBoolean()
  enabled?: boolean = true;

  @ApiPropertyOptional({ description: '服务到期时间' })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;

  @ApiPropertyOptional({ description: '配置信息' })
  @IsOptional()
  @IsObject()
  config?: Record<string, any>;

  @ApiPropertyOptional({ description: '备注信息' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  notes?: string;
}

/**
 * 更新用户服务关联DTO
 */
export class UpdateUserServiceDto {
  @ApiPropertyOptional({ description: '免费次数', minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  freeCount?: number;

  @ApiPropertyOptional({ description: '购买次数', minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  purchasedCount?: number;

  @ApiPropertyOptional({ description: '是否启用' })
  @IsOptional()
  @IsBoolean()
  enabled?: boolean;

  @ApiPropertyOptional({ description: '服务到期时间' })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;

  @ApiPropertyOptional({ description: '配置信息' })
  @IsOptional()
  @IsObject()
  config?: Record<string, any>;

  @ApiPropertyOptional({ description: '备注信息' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  notes?: string;
}

/**
 * 用户服务关联查询DTO
 */
export class QueryUserServiceDto {
  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', example: 10, minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: '用户ID', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  userId?: number;

  @ApiPropertyOptional({ description: '服务ID', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  serviceId?: number;

  @ApiPropertyOptional({ description: '是否启用' })
  @IsOptional()
  @IsBoolean()
  enabled?: boolean;

  @ApiPropertyOptional({ description: '是否已过期' })
  @IsOptional()
  @IsBoolean()
  expired?: boolean;

  @ApiPropertyOptional({ description: '最小剩余次数', example: 0 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  minRemainingCount?: number;

  @ApiPropertyOptional({ description: '最大剩余次数', example: 1000 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  maxRemainingCount?: number;

  @ApiPropertyOptional({ description: '最小调用次数', example: 0 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  minCallCount?: number;

  @ApiPropertyOptional({ description: '最大调用次数', example: 1000 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  maxCallCount?: number;

  @ApiPropertyOptional({ description: '服务类型', example: 'ai_service' })
  @IsOptional()
  @IsString()
  serviceType?: string;

  @ApiPropertyOptional({ description: '服务代码', example: 'gpt-4' })
  @IsOptional()
  @IsString()
  serviceCode?: string;

  @ApiPropertyOptional({ description: '用户名搜索', example: 'john' })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiPropertyOptional({ description: '用户邮箱搜索', example: '<EMAIL>' })
  @IsOptional()
  @IsString()
  userEmail?: string;

  @ApiPropertyOptional({ description: '开始时间' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: '结束时间' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: '排序字段', example: 'createdAt' })
  @IsOptional()
  @IsString()
  @IsIn(['id', 'callCount', 'remainingCount', 'totalCount', 'enabled', 'expiresAt', 'createdAt', 'updatedAt'])
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', example: 'DESC' })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}

/**
 * 批量分配服务DTO
 */
export class BatchAssignServiceDto {
  @ApiProperty({ description: '用户ID列表', example: [1, 2, 3] })
  @IsArray()
  @IsInt({ each: true })
  @IsNotEmpty()
  userIds: number[];

  @ApiProperty({ description: '服务ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  serviceId: number;

  @ApiPropertyOptional({ description: '免费次数', example: 100, minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  freeCount?: number = 0;

  @ApiPropertyOptional({ description: '购买次数', example: 1000, minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  purchasedCount?: number = 0;

  @ApiPropertyOptional({ description: '服务到期时间' })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;

  @ApiPropertyOptional({ description: '配置信息' })
  @IsOptional()
  @IsObject()
  config?: Record<string, any>;

  @ApiPropertyOptional({ description: '备注信息' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  notes?: string;
}

/**
 * 批量更新次数DTO
 */
export class BatchUpdateCountDto {
  @ApiProperty({ description: '用户服务关联ID列表', example: [1, 2, 3] })
  @IsArray()
  @IsInt({ each: true })
  @IsNotEmpty()
  ids: number[];

  @ApiProperty({ description: '操作类型', example: 'add' })
  @IsString()
  @IsIn(['add', 'subtract', 'set'])
  operation: 'add' | 'subtract' | 'set';

  @ApiProperty({ description: '次数类型', example: 'free' })
  @IsString()
  @IsIn(['free', 'purchased'])
  countType: 'free' | 'purchased';

  @ApiProperty({ description: '数量', example: 100, minimum: 0 })
  @IsInt()
  @Min(0)
  amount: number;

  @ApiPropertyOptional({ description: '操作备注' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  reason?: string;
}

/**
 * 重置次数DTO
 */
export class ResetCountDto {
  @ApiProperty({ description: '用户ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  userId: number;

  @ApiProperty({ description: '服务ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  serviceId: number;

  @ApiPropertyOptional({ description: '重置类型', example: 'call_count' })
  @IsOptional()
  @IsString()
  @IsIn(['call_count', 'all'])
  resetType?: 'call_count' | 'all' = 'call_count';

  @ApiPropertyOptional({ description: '重置原因' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  reason?: string;
}

/**
 * 用户服务关联响应DTO
 */
export class UserServiceResponseDto {
  @ApiProperty({ description: '关联ID', example: 1 })
  id: number;

  @ApiProperty({ description: '用户ID', example: 1 })
  userId: number;

  @ApiProperty({ description: '服务ID', example: 1 })
  serviceId: number;


  @ApiProperty({ description: '免费次数', example: 100 })
  freeCount: number;

  @ApiProperty({ description: '购买次数', example: 1000 })
  purchasedCount: number;

  @ApiProperty({ description: '总次数', example: 1100 })
  totalCount: number;
  
  @ApiProperty({ description: '是否已发送预警', example: true })
  alertSent: boolean;

  @ApiProperty({ description: '已用次数', example: 1100 })
  usedCount: number;

  @ApiProperty({ description: '剩余次数', example: 1050 })
  remainingCount: number;

  @ApiProperty({ description: '是否启用', example: true })
  enabled: boolean;

  @ApiPropertyOptional({ description: '服务到期时间' })
  expiresAt?: Date;

  @ApiPropertyOptional({ description: '配置信息' })
  config?: Record<string, any>;

  @ApiPropertyOptional({ description: '备注信息' })
  notes?: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 关联信息
  @ApiPropertyOptional({ description: '用户信息' })
  user?: {
    id: number;
    username: string;
    email: string;
    realName?: string;
    phone?: string;
    type: string;
    status: string;
    tier: string;
  };

  @ApiPropertyOptional({ description: '服务信息' })
  service?: {
    id: number;
    code: string;
    name: string;
    type: string;
    status: string;
    description?: string;
    pricingModel: string;
    unitPrice: number;
    endpoint?: string;
  };

  // 计算属性
  @ApiProperty({ description: '使用率', example: 4.5 })
  usageRate: number;

  @ApiProperty({ description: '是否已过期', example: false })
  isExpired: boolean;

  @ApiProperty({ description: '剩余天数', example: 30 })
  remainingDays?: number;
}

/**
 * 用户服务关联列表响应DTO
 */
export class UserServiceListResponseDto {
  @ApiProperty({ description: '用户服务关联列表', type: [UserServiceResponseDto] })
  data: UserServiceResponseDto[];

  @ApiProperty({ description: '总数', example: 150 })
  total: number;

  @ApiProperty({ description: '当前页', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  limit: number;

  @ApiProperty({ description: '总页数', example: 15 })
  totalPages: number;
}

/**
 * 用户服务统计DTO
 */
export class UserServiceStatsDto {
  @ApiProperty({ description: '总关联数', example: 500 })
  totalAssociations: number;

  @ApiProperty({ description: '活跃关联数', example: 450 })
  activeAssociations: number;

  @ApiProperty({ description: '已过期关联数', example: 30 })
  expiredAssociations: number;

  @ApiProperty({ description: '禁用关联数', example: 20 })
  disabledAssociations: number;

  @ApiProperty({ description: '总调用次数', example: 50000 })
  totalCalls: number;

  @ApiProperty({ description: '总免费次数', example: 10000 })
  totalFreeCount: number;

  @ApiProperty({ description: '总购买次数', example: 100000 })
  totalPurchasedCount: number;

  @ApiProperty({ description: '总剩余次数', example: 60000 })
  totalRemainingCount: number;

  @ApiProperty({ description: '平均使用率', example: 45.5 })
  avgUsageRate: number;

  @ApiProperty({ description: '按服务分组统计' })
  byService: Record<string, {
    associations: number;
    totalCalls: number;
    totalRemaining: number;
    avgUsageRate: number;
  }>;

  @ApiProperty({ description: '按用户类型分组统计' })
  byUserType: Record<string, {
    associations: number;
    totalCalls: number;
    avgUsageRate: number;
  }>;

  @ApiProperty({ description: '按用户等级分组统计' })
  byUserTier: Record<string, {
    associations: number;
    totalCalls: number;
    avgUsageRate: number;
  }>;

  @ApiProperty({ description: '即将过期的关联数（7天内）', example: 15 })
  expiringSoon: number;

  @ApiProperty({ description: '次数不足的关联数（剩余<10%）', example: 25 })
  lowCount: number;
}

/**
 * 用户服务使用情况DTO
 */
export class UserServiceUsageDto {
  @ApiProperty({ description: '用户ID', example: 1 })
  userId: number;

  @ApiProperty({ description: '服务ID', example: 1 })
  serviceId: number;

  @ApiProperty({ description: '今日调用次数', example: 10 })
  todayCalls: number;

  @ApiProperty({ description: '本周调用次数', example: 50 })
  weekCalls: number;

  @ApiProperty({ description: '本月调用次数', example: 200 })
  monthCalls: number;

  @ApiProperty({ description: '总调用次数', example: 1000 })
  totalCalls: number;

  @ApiProperty({ description: '最后调用时间' })
  lastCallAt?: Date;

  @ApiProperty({ description: '平均每日调用次数', example: 6.7 })
  avgDailyCalls: number;

  @ApiProperty({ description: '调用趋势（7天）', example: [5, 8, 12, 6, 9, 15, 10] })
  callTrend: number[];

  @ApiProperty({ description: '成功率', example: 98.5 })
  successRate: number;

  @ApiProperty({ description: '平均响应时间（毫秒）', example: 1200 })
  avgResponseTime: number;
}

/**
 * 更新调用总次数DTO
 */
export class UpdateTotalCountDto {
  @ApiProperty({ description: '用户ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  userId: number;

  @ApiProperty({ description: '服务ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  serviceId: number;

  @ApiProperty({ description: '新增调用次数', example: 100, minimum: 1 })
  @IsInt()
  @Min(1)
  additionalCount: number;

  @ApiPropertyOptional({ description: '来源说明', example: '购买次数包' })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  source?: string;

  @ApiPropertyOptional({ description: '操作备注', example: '用户充值购买' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  remark?: string;
}