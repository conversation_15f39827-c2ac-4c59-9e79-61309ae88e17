import { Injectable, LoggerService, Scope } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import { Request } from 'express';

/**
 * 日志上下文接口
 */
export interface LogContext {
  /** 请求ID */
  requestId?: string;
  /** 用户ID */
  userId?: string;
  /** 会话ID */
  sessionId?: string;
  /** 服务名称 */
  service?: string;
  /** 模块名称 */
  module?: string;
  /** 方法名称 */
  method?: string;
  /** 追踪ID */
  traceId?: string;
  /** 跨度ID */
  spanId?: string;
  /** 客户端IP */
  clientIp?: string;
  /** 用户代理 */
  userAgent?: string;
  /** API版本 */
  apiVersion?: string;
  /** 业务类型 */
  businessType?: string;
  /** 额外元数据 */
  metadata?: Record<string, any>;
}

/**
 * 日志级别枚举
 */
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  VERBOSE = 'verbose',
}

/**
 * 日志条目接口
 */
export interface LogEntry {
  /** 日志级别 */
  level: LogLevel;
  /** 日志消息 */
  message: string;
  /** 时间戳 */
  timestamp: string;
  /** 日志上下文 */
  context: LogContext;
  /** 错误堆栈 */
  stack?: string;
  /** 持续时间 */
  duration?: number;
  /** 状态码 */
  statusCode?: number;
  /** 额外数据 */
  extra?: Record<string, any>;
}

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime?: number;
  /** 持续时间 */
  duration?: number;
  /** 内存使用 */
  memoryUsage?: NodeJS.MemoryUsage;
  /** CPU使用 */
  cpuUsage?: NodeJS.CpuUsage;
}

/**
 * 敏感信息过滤器接口
 */
export interface SensitiveDataFilter {
  /** 需要过滤的字段 */
  fields: string[];
  /** 替换值 */
  replacement: string;
  /** 是否启用 */
  enabled: boolean;
}

/**
 * 结构化日志记录器
 * 提供统一的日志格式、上下文管理和敏感信息过滤
 */
@Injectable({ scope: Scope.TRANSIENT })
export class StructuredLogger implements LoggerService {
  private readonly winston: winston.Logger;
  private readonly context: LogContext = {};
  private readonly sensitiveFilter: SensitiveDataFilter;
  private readonly enablePerformanceTracking: boolean;
  private readonly performanceMetrics = new Map<string, PerformanceMetrics>();

  constructor(
    private readonly configService: ConfigService,
    context?: string,
  ) {
    if (context) {
      this.context.module = context;
    }

    this.sensitiveFilter = {
      fields: this.configService.get('logging.sensitiveFields', [
        'password',
        'token',
        'secret',
        'key',
        'authorization',
        'cookie',
        'x-api-key',
      ]),
      replacement: this.configService.get('logging.sensitiveReplacement', '[REDACTED]'),
      enabled: this.configService.get('logging.sensitiveFilterEnabled', true),
    };

    this.enablePerformanceTracking = this.configService.get(
      'logging.performanceTracking',
      true,
    );

    this.winston = this.createWinstonLogger();
  }

  /**
   * 设置日志上下文
   */
  setContext(context: Partial<LogContext>): void {
    Object.assign(this.context, context);
  }

  /**
   * 从请求对象设置上下文
   */
  setContextFromRequest(request: any): void {
    this.setContext({
      requestId: request.requestId || request.headers['x-request-id'],
      userId: request.user?.id,
      sessionId: request.sessionID,
      clientIp: this.extractClientIp(request),
      userAgent: request.headers['user-agent'],
      apiVersion: request.headers['api-version'],
      traceId: request.headers['x-trace-id'],
      spanId: request.headers['x-span-id'],
    });
  }

  /**
   * 清除上下文
   */
  clearContext(): void {
    Object.keys(this.context).forEach(key => {
      delete this.context[key as keyof LogContext];
    });
  }

  /**
   * 记录错误日志
   */
  error(message: string, error?: Error | string, context?: Partial<LogContext>): void {
    const logEntry = this.createLogEntry(
      LogLevel.ERROR,
      message,
      context,
      typeof error === 'string' ? undefined : error?.stack,
    );

    if (typeof error === 'string') {
      logEntry.extra = { error };
    } else if (error) {
      logEntry.extra = {
        errorName: error.name,
        errorMessage: error.message,
      };
    }

    this.winston.error(this.formatLogEntry(logEntry));
  }

  /**
   * 记录警告日志
   */
  warn(message: string, context?: Partial<LogContext>): void {
    const logEntry = this.createLogEntry(LogLevel.WARN, message, context);
    this.winston.warn(this.formatLogEntry(logEntry));
  }

  /**
   * 记录信息日志
   */
  log(message: string, context?: Partial<LogContext>): void {
    this.info(message, context);
  }

  /**
   * 记录信息日志
   */
  info(message: string, context?: Partial<LogContext>): void {
    const logEntry = this.createLogEntry(LogLevel.INFO, message, context);
    this.winston.info(this.formatLogEntry(logEntry));
  }

  /**
   * 记录调试日志
   */
  debug(message: string, context?: Partial<LogContext>): void {
    const logEntry = this.createLogEntry(LogLevel.DEBUG, message, context);
    this.winston.debug(this.formatLogEntry(logEntry));
  }

  /**
   * 记录详细日志
   */
  verbose(message: string, context?: Partial<LogContext>): void {
    const logEntry = this.createLogEntry(LogLevel.VERBOSE, message, context);
    this.winston.verbose(this.formatLogEntry(logEntry));
  }

  /**
   * 记录HTTP请求日志
   */
  logRequest(
    method: string,
    url: string,
    statusCode: number,
    duration: number,
    context?: Partial<LogContext>,
  ): void {
    const message = `${method} ${url} ${statusCode} - ${duration}ms`;
    const logEntry = this.createLogEntry(LogLevel.INFO, message, context);
    logEntry.statusCode = statusCode;
    logEntry.duration = duration;
    logEntry.extra = { method, url };

    this.winston.info(this.formatLogEntry(logEntry));
  }

  /**
   * 记录业务操作日志
   */
  logBusiness(
    operation: string,
    result: 'success' | 'failure',
    details?: Record<string, any>,
    context?: Partial<LogContext>,
  ): void {
    const message = `Business operation: ${operation} - ${result}`;
    const logEntry = this.createLogEntry(LogLevel.INFO, message, {
      ...context,
      businessType: operation,
    });
    
    if (details) {
      logEntry.extra = this.filterSensitiveData(details);
    }

    this.winston.info(this.formatLogEntry(logEntry));
  }

  /**
   * 记录安全事件日志
   */
  logSecurity(
    event: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    details?: Record<string, any>,
    context?: Partial<LogContext>,
  ): void {
    const level = severity === 'critical' || severity === 'high' ? LogLevel.ERROR : LogLevel.WARN;
    const message = `Security event: ${event} [${severity.toUpperCase()}]`;
    
    const logEntry = this.createLogEntry(level, message, context);
    logEntry.extra = {
      securityEvent: event,
      severity,
      ...this.filterSensitiveData(details || {}),
    };

    this.winston.log(level, this.formatLogEntry(logEntry));
  }

  /**
   * 开始性能追踪
   */
  startPerformanceTracking(operationId: string): void {
    if (!this.enablePerformanceTracking) return;

    this.performanceMetrics.set(operationId, {
      startTime: Date.now(),
      cpuUsage: process.cpuUsage(),
      memoryUsage: process.memoryUsage(),
    });
  }

  /**
   * 结束性能追踪
   */
  endPerformanceTracking(
    operationId: string,
    operation: string,
    context?: Partial<LogContext>,
  ): void {
    if (!this.enablePerformanceTracking) return;

    const metrics = this.performanceMetrics.get(operationId);
    if (!metrics) return;

    const endTime = Date.now();
    const duration = endTime - metrics.startTime;
    const endCpuUsage = process.cpuUsage(metrics.cpuUsage);
    const endMemoryUsage = process.memoryUsage();

    const message = `Performance: ${operation} completed in ${duration}ms`;
    const logEntry = this.createLogEntry(LogLevel.INFO, message, context);
    logEntry.duration = duration;
    logEntry.extra = {
      operation,
      performance: {
        duration,
        cpuUsage: {
          user: endCpuUsage.user / 1000, // 转换为毫秒
          system: endCpuUsage.system / 1000,
        },
        memoryUsage: {
          heapUsed: endMemoryUsage.heapUsed - metrics.memoryUsage!.heapUsed,
          heapTotal: endMemoryUsage.heapTotal - metrics.memoryUsage!.heapTotal,
          external: endMemoryUsage.external - metrics.memoryUsage!.external,
        },
      },
    };

    this.winston.info(this.formatLogEntry(logEntry));
    this.performanceMetrics.delete(operationId);
  }

  /**
   * 创建日志条目
   */
  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: Partial<LogContext>,
    stack?: string,
  ): LogEntry {
    // 创建中国标准时间的时间戳
    const date = new Date();
    const chinaTime = new Date(date.getTime() + 8 * 60 * 60 * 1000);
    const timestamp = chinaTime.toISOString().replace('Z', '+08:00');
    
    return {
      level,
      message,
      timestamp,
      context: { ...this.context, ...context },
      ...(stack && { stack }),
      statusCode: undefined,
      duration: undefined,
      extra: undefined,
    };
  }

  /**
   * 格式化日志条目
   */
  private formatLogEntry(entry: LogEntry): any {
    const formatted: any = {
      timestamp: entry.timestamp,
      level: entry.level,
      message: entry.message,
      ...entry.context,
    };

    if (entry.stack) {
      formatted.stack = entry.stack;
    }

    if (entry.duration !== undefined) {
      formatted.duration = entry.duration;
    }

    if (entry.statusCode !== undefined) {
      formatted.statusCode = entry.statusCode;
    }

    if (entry.extra) {
      Object.assign(formatted, entry.extra);
    }

    return this.filterSensitiveData(formatted);
  }

  /**
   * 过滤敏感数据
   */
  private filterSensitiveData(data: any): any {
    if (!this.sensitiveFilter.enabled) {
      return data;
    }

    const filtered = JSON.parse(JSON.stringify(data));
    
    const filterRecursive = (obj: any): any => {
      if (typeof obj !== 'object' || obj === null) {
        return obj;
      }

      if (Array.isArray(obj)) {
        return obj.map(filterRecursive);
      }

      const result: any = {};
      for (const [key, value] of Object.entries(obj)) {
        const lowerKey = key.toLowerCase();
        const isSensitive = this.sensitiveFilter.fields.some(field => 
          lowerKey.includes(field.toLowerCase())
        );

        if (isSensitive) {
          result[key] = this.sensitiveFilter.replacement;
        } else {
          result[key] = filterRecursive(value);
        }
      }
      return result;
    };

    return filterRecursive(filtered);
  }

  /**
   * 提取客户端IP
   */
  private extractClientIp(request: any): string {
    return (
      request.headers['x-forwarded-for']?.split(',')[0] ||
      request.headers['x-real-ip'] ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      '127.0.0.1'
    );
  }

  /**
   * 创建Winston日志记录器
   */
  private createWinstonLogger(): winston.Logger {
    const logLevel = this.configService.get('logging.level', 'info');
    const logFormat = this.configService.get('logging.format', 'json');
    const enableConsole = this.configService.get('logging.console.enabled', true);
    const enableFile = this.configService.get('logging.file.enabled', true);

    // 创建自定义时间戳格式化器，使用中国标准时间
    const timestampFormat = winston.format((info) => {
      const date = new Date();
      // 添加8小时以转换为中国标准时间
      const chinaTime = new Date(date.getTime() + 8 * 60 * 60 * 1000);
      info.timestamp = chinaTime.toISOString().replace('Z', '+08:00');
      return info;
    });

    const formats: winston.Logform.Format[] = [timestampFormat()];

    if (logFormat === 'json') {
      formats.push(winston.format.json());
    } else {
      formats.push(
        winston.format.printf(({ timestamp, level, message, ...meta }) => {
          return `${timestamp} [${level.toUpperCase()}] ${message} ${Object.keys(meta).length ? JSON.stringify(meta) : ''}`;
        }),
      );
    }

    const transports: winston.transport[] = [];

    // 控制台输出
    if (enableConsole) {
      transports.push(
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            ...formats,
          ),
        }),
      );
    }

    // 文件输出
    if (enableFile) {
      // 错误日志文件
      transports.push(
        new DailyRotateFile({
          filename: 'logs/error-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          level: 'error',
          maxSize: '20m',
          maxFiles: '14d',
          format: winston.format.combine(...formats),
        }),
      );

      // 综合日志文件
      transports.push(
        new DailyRotateFile({
          filename: 'logs/combined-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          maxSize: '20m',
          maxFiles: '14d',
          format: winston.format.combine(...formats),
        }),
      );
    }

    return winston.createLogger({
      level: logLevel,
      format: winston.format.combine(...formats),
      transports,
      exitOnError: false,
    });
  }
}