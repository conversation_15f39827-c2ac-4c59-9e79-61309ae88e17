# 用户模块需求文档

## 1. 文档概述

本文档描述了开放平台用户模块的需求规范，该模块基于单一职责原则设计，专注于用户的基础信息管理和实名认证流程。用户模块是平台的基础模块，负责用户实体的持久化和业务操作，与认证模块(auth)和共享服务模块(shared)协作，支持平台的用户体系构建。

## 2. 功能需求

### 2.1 用户基础功能

- **用户实体管理**：创建、查询、更新、删除用户实体
- **用户信息维护**：管理用户基础信息和配置项
- **用户状态管理**：处理用户生命周期状态转换
- **实名认证流程**：个人和企业实名认证的申请与审核
- **免费服务次数管理**：管理用户免费服务额度资格

### 2.2 用户实名认证

#### 2.2.1 个人实名认证
- **流程**：
  1. 用户提交个人实名认证资料（真实姓名、身份证号码、身份证正反面照片）
  2. 系统创建认证记录
  3. 管理员审核认证资料
  4. 系统更新认证状态
  5. 审核通过后，更新用户状态为个人实名认证
  6. 通过user-service模块奖励用户服务调用次数（每日增加10次）

#### 2.2.2 企业实名认证
- **流程**：
  1. 用户提交企业认证资料（企业名称、营业执照号、营业执照照片、法人信息等）
  2. 系统创建认证记录
  3. 管理员审核认证资料
  4. 系统更新认证状态
  5. 审核通过后，更新用户状态为企业实名认证
  6. 通过user-service模块奖励用户服务调用次数（每日增加20次）

### 2.3 免费服务次数管理

- **新用户初始化**：
  - 新注册用户默认每日免费调用次数：5次
  - 有效期：半年（从注册日期开始计算）
  
- **实名认证奖励**：
  - 个人实名认证后每日免费调用次数：15次（基础5次+奖励10次）
  - 企业实名认证后每日免费调用次数：25次（基础5次+奖励20次）

- **免费额度终止条件**：
  - 半年内购买服务后，不再享受新用户奖励次数
  - 半年有效期到期后，不再享有每日免费调用次数

- **次数管理**：
  - 通过schedule定时任务模块每日自动重置免费调用次数
  - 用户每天使用完免费次数后，需要购买服务才能继续使用

## 3. 技术规范

### 3.1 用户实体模型

```typescript
@Entity('open_user')
export class UserEntity extends BaseEntity {
  @Column({ type: 'varchar', length: 100, unique: true, comment: '用户名' })
  username: string;

  @Column({ type: 'varchar', length: 200, unique: true, nullable: true, comment: '邮箱' })
  email: string;

  @Column({ type: 'varchar', length: 255, comment: '密码哈希' })
  password: string;

  @Column({ type: 'varchar', length: 30, nullable: false, comment: '用户昵称' })
  nickname: string;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: '真实姓名' })
  realName?: string;

  @Column({ type: 'varchar', length: 20, nullable: true, comment: '手机号' })
  phone?: string;

  @Column({ type: 'varchar', length: 500, nullable: true, comment: '头像URL' })
  avatar?: string;

  @Column({
    type: 'enum',
    enum: UserType,
    default: UserType.INDIVIDUAL,
    comment: '用户类型'
  })
  userType: UserType;

  @Column({
    type: 'enum',
    enum: UserVerificationStatus,
    default: UserVerificationStatus.UNVERIFIED,
    comment: '认证状态'
  })
  verificationStatus: UserVerificationStatus;

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE,
    comment: '账户状态'
  })
  userStatus: UserStatus;

  @Column({
    type: 'enum',
    enum: RoleEnum,
    default: RoleEnum.USER,
    comment: '角色'
  })
  role: RoleEnum;

  @Column({ type: 'boolean', default: false, comment: '邮箱是否验证' })
  emailVerified: boolean;

  @Column({ type: 'boolean', default: false, comment: '手机是否验证' })
  phoneVerified: boolean;

  @Column({ type: 'timestamp', nullable: true, comment: '最后登录时间' })
  lastLoginAt?: Date;

  @Column({ type: 'varchar', length: 45, nullable: true, comment: '最后登录IP' })
  lastLoginIp?: string;

  @Column({ type: 'int', default: 0, comment: '登录失败次数' })
  loginFailCount: number;

  @Column({ type: 'timestamp', nullable: true, comment: '账户锁定截止时间' })
  lockedUntil?: Date;

  @Column({ type: 'timestamp', nullable: true, comment: '每日免费使用次数重置日期' })
  lastDailyResetDate?: Date;

  @Column({
    type: 'enum',
    enum: TierEnum,
    default: TierEnum.BASIC,
    comment: '用户等级'
  })
  tier: TierEnum;

  @Column({ default: true, comment: '是否有资格获取免费额度' })
  isFreeQuotaEligible: boolean;
}
```

### 3.2 实名认证实体模型

#### 3.2.1 个人实名认证

```typescript
@Entity('user_verification')
export class UserVerificationEntity extends BaseEntity {
  @Column({ name: 'user_id', type: 'int' })
  userId: number;

  @Column({ name: 'real_name', type: 'varchar', length: 30, comment: '真实姓名' })
  realName: string;

  @Column({ name: 'id_card', type: 'varchar', length: 18, comment: '身份证号' })
  idCard: string;

  @Column({ name: 'id_card_front', type: 'text', comment: '身份证正面照片URL' })
  idCardFront: string;

  @Column({ name: 'id_card_back', type: 'text', comment: '身份证背面照片URL' })
  idCardBack: string;

  @Column({
    name: 'review_status',
    type: 'enum',
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending',
    comment: '审核状态'
  })
  reviewStatus: 'pending' | 'approved' | 'rejected';

  @Column({ name: 'review_remark', type: 'text', nullable: true, comment: '审核备注' })
  reviewRemark: string;

  @Column({ name: 'reviewed_at', type: 'datetime', nullable: true, comment: '审核时间' })
  reviewedAt: Date;

  @Column({ name: 'reviewer_id', type: 'int', nullable: true, comment: '审核人ID' })
  reviewerId: number;
}
```

#### 3.2.2 企业实名认证

```typescript
@Entity('user_company')
export class UserCompanyEntity extends BaseEntity {
  @Column({ type: 'int', name: 'user_id', comment: '用户ID' })
  userId: number;

  @Column({
    type: 'enum',
    enum: ['pending', 'approved', 'rejected'],
    name: 'reviewed_status',
    default: 'pending',
    comment: '审核状态'
  })
  reviewedStatus: 'pending' | 'approved' | 'rejected';

  @Column({ type: 'varchar', comment: '企业名称', nullable: true })
  enterprise: string;

  @Column({ type: 'varchar', name: 'license_no', length: 50, default: '', comment: '营业执照号' })
  licenseNo: string;

  @Column({ type: 'varchar', name: 'license_image', default: '', comment: '营业执照照片' })
  licenseImage: string;

  @Column({ type: 'varchar', name: 'legal_person', length: 30, default: '', comment: '法人姓名' })
  legalPerson: string;

  @Column({ type: 'varchar', name: 'address', length: 200, default: '', comment: '企业地址' })
  address: string;

  @Column({ type: 'varchar', name: 'contact_name', length: 30, default: '', comment: '联系人姓名' })
  contactName: string;

  @Column({ type: 'varchar', name: 'contact_number', length: 30, default: '', comment: '联系人电话' })
  contactNumber: string;

  @Column({ type: 'varchar', name: 'contact_phone', length: 20, default: '', comment: '联系人手机' })
  contactPhone: string;
}
```

### 3.3 用户模块服务接口定义

#### 3.3.1 用户创建服务

```typescript
/**
 * 创建用户
 * @param createUserDto - 用户创建DTO
 * @returns 创建的用户实体
 */
async create(createUserDto: CreateUserDto): Promise<UserEntity>
```

#### 3.3.2 用户查询服务

```typescript
/**
 * 根据ID查询用户
 * @param id - 用户ID
 * @returns 用户实体
 */
async findById(id: number): Promise<UserEntity>

/**
 * 根据邮箱查询用户
 * @param email - 用户邮箱
 * @returns 用户实体
 */
async findByEmail(email: string): Promise<UserEntity>

/**
 * 根据手机号查询用户
 * @param phone - 用户手机号
 * @returns 用户实体
 */
async findByPhone(phone: string): Promise<UserEntity>
```

#### 3.3.3 用户更新服务

```typescript
/**
 * 更新用户信息
 * @param id - 用户ID
 * @param updateUserDto - 用户更新DTO
 * @returns 更新后的用户实体
 */
async update(id: number, updateUserDto: UpdateUserDto): Promise<UserEntity>

/**
 * 更新用户状态
 * @param id - 用户ID
 * @param status - 新状态
 * @returns 更新后的用户实体
 */
async updateStatus(id: number, status: UserStatus): Promise<UserEntity>
```

#### 3.3.4 实名认证服务

```typescript
/**
 * 提交个人实名认证
 * @param userId - 用户ID
 * @param verificationDto - 认证信息
 * @returns 认证记录
 */
async submitPersonalVerification(userId: number, verificationDto: PersonalVerificationDto): Promise<UserVerificationEntity>

/**
 * 提交企业实名认证
 * @param userId - 用户ID
 * @param verificationDto - 认证信息
 * @returns 认证记录
 */
async submitEnterpriseVerification(userId: number, verificationDto: EnterpriseVerificationDto): Promise<UserCompanyEntity>

/**
 * 审核实名认证
 * @param verificationId - 认证记录ID
 * @param reviewDto - 审核信息
 * @returns 审核结果
 */
async reviewVerification(verificationId: number, reviewDto: ReviewVerificationDto): Promise<{ success: boolean; message: string }>
```

## 4. 系统架构

### 4.1 模块结构

```
user/
├── dto/
│   ├── create-user.dto.ts       # 创建用户DTO
│   ├── update-user.dto.ts       # 更新用户DTO
│   ├── user-profile.dto.ts      # 用户资料DTO
│   ├── user-verification.dto.ts # 实名认证DTO
│   └── user-response.dto.ts     # 用户响应DTO
├── entities/
│   ├── user.entity.ts           # 用户实体
│   ├── user-verification.entity.ts # 个人实名认证实体
│   └── user-company.entity.ts   # 企业实名认证实体
├── services/
│   └── user.service.ts          # 用户核心服务
├── controllers/
│   └── user.controller.ts       # 用户接口控制器
├── interfaces/
│   └── user.interface.ts        # 用户相关接口定义
└── user.module.ts               # 用户模块定义
```

### 4.2 模块间协作关系

```mermaid
graph TD
    Auth[认证模块] -- "注册/登录" --> User[用户模块]
    Auth --> Shared[共享服务模块]
    User --> UserService[用户服务关联模块]
    User --> Schedule[定时任务模块]
    
    subgraph "全局认证守卫"
        UnifiedAuth[UnifiedAuthGuard]
    end
    
    subgraph "认证模块职责"
        Auth -- "调用" --> Shared
        Shared -- "验证码处理" --> CaptchaService[验证码服务]
        Shared -- "邮件发送" --> EmailService[邮件服务]
        Shared -- "短信发送" --> SmsService[短信服务]
    end
    
    subgraph "用户模块职责"
        User -- "创建用户" --> CreateUser[用户创建]
        User -- "实名认证" --> VerificationProcess[认证处理]
        VerificationProcess --> UpdateStatus[更新状态]
        UpdateStatus --> GrantReward[发放奖励]
    end
    
    Schedule --> ResetDaily[每日重置免费额度]
    Schedule --> CheckExpire[检查奖励过期]
```

## 5. 实现要点

### 5.1 用户模块与认证模块的分工

- **用户模块**：
  - 负责用户实体的CRUD操作
  - 处理用户基础信息和业务数据
  - 管理用户状态和生命周期
  - 处理实名认证业务逻辑

- **认证模块**：
  - 处理用户注册流程
  - 处理用户登录/登出逻辑
  - 生成和验证JWT令牌
  - 调用共享模块的验证码服务

- **共享模块**：
  - 提供验证码生成和验证服务
  - 提供邮件发送服务
  - 提供短信发送服务
  - 提供Redis缓存服务

### 5.2 注册流程职责划分

1. **Auth模块**：接收注册请求，验证请求参数
2. **Shared模块**：验证邮箱/手机验证码的有效性
3. **User模块**：创建用户实体并存储
4. **Auth模块**：生成JWT令牌
5. **ApiKey模块**：创建并关联API密钥

### 5.3 用户模块核心接口

- **创建用户**：为Auth模块提供创建用户的服务
- **查询用户**：根据ID、邮箱或手机号查询用户
- **更新用户**：更新用户基本信息
- **实名认证**：处理用户实名认证申请和审核

### 5.4 免费调用次数管理

- **定时任务**：
  - 每天凌晨00:00重置符合条件用户的免费调用次数
  - 每月对用户奖励资格进行检查

- **额度更新逻辑**：
  1. 查询所有有资格获取免费额度的用户
  2. 根据用户认证状态设置相应的免费次数
  3. 更新用户的lastDailyResetDate字段
  4. 通过user-service模块更新用户的freeUsedToday字段为0

- **奖励终止逻辑**：
  1. 定期检查用户注册时间是否超过半年
  2. 检查用户是否已购买服务
  3. 符合终止条件的用户，更新isFreeQuotaEligible为false

### 5.5 实名认证奖励机制

- **实名认证奖励流程**：
  1. 用户提交实名认证资料
  2. 管理员审核认证资料
  3. 审核通过后，用户状态更新为已认证状态（数据库事务开始）
  4. 直接调用user-service模块的addServiceCount方法发放奖励次数
     - 个人认证：`userServiceService.addServiceCount(userId, serviceId, 10)`
     - 企业认证：`userServiceService.addServiceCount(userId, serviceId, 20)`
  5. 记录审计日志，包含认证时间、奖励发放时间、奖励次数（数据库事务结束）
  6. 发送认证成功和奖励到账的邮件通知

- **奖励发放异常处理**：
  - 使用事务包装认证状态更新和奖励发放
  - 如果奖励发放失败，不回滚认证状态，而是记录到重试队列
  - 将失败记录添加到重试队列，由定时任务重试发放
  - 达到最大重试次数后发送告警给管理员

- **实名认证接口示例**：

```typescript
/**
 * 处理实名认证审核通过
 * @param userId 用户ID
 * @param verificationType 认证类型：'personal'|'enterprise'
 * @param reviewerId 审核人ID
 * @returns 处理结果
 */
async approveVerification(
  userId: number,
  verificationType: 'personal' | 'enterprise',
  reviewerId: number
): Promise<{ success: boolean; message: string }> {
  // 使用事务包装整个过程
  const queryRunner = this.dataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();
  
  try {
    // 1. 更新用户状态为已认证
    const user = await queryRunner.manager.findOne(UserEntity, { where: { id: userId } });
    if (!user) {
      throw new NotFoundException(`用户ID ${userId} 不存在`);
    }
    
    // 2. 设置认证状态
    if (verificationType === 'personal') {
      user.verificationStatus = UserVerificationStatus.PERSONAL_VERIFIED;
    } else {
      user.verificationStatus = UserVerificationStatus.ENTERPRISE_VERIFIED;
    }
    
    // 3. 更新认证记录状态
    if (verificationType === 'personal') {
      await queryRunner.manager.update(UserVerificationEntity, 
        { userId }, 
        { 
          reviewStatus: 'approved',
          reviewedAt: new Date(),
          reviewerId
        }
      );
    } else {
      await queryRunner.manager.update(UserCompanyEntity,
        { userId },
        {
          reviewedStatus: 'approved',
          reviewedAt: new Date(),
          reviewerId
        }
      );
    }
    
    // 4. 保存用户状态更新
    await queryRunner.manager.save(user);
    
    // 5. 提交事务
    await queryRunner.commitTransaction();
    
    // 6. 异步发放奖励（不在事务内，避免奖励失败影响认证状态）
    try {
      const rewardCount = verificationType === 'personal' ? 10 : 20;
      // 遍历平台所有服务，为每个服务增加奖励次数
      const services = await this.serviceRepository.find();
      for (const service of services) {
        await this.userServiceService.addServiceCount(
          userId,
          service.id,
          rewardCount,
          'verification_reward'
        );
      }
      
      // 7. 发送认证成功和奖励到账通知
      await this.emailService.sendVerificationResultEmail(
        user.email,
        user.nickname,
        'approved',
        '',
        { rewardCount, rewardType: '调用次数' }
      );
      
      this.logger.log(`用户 ${userId} ${verificationType} 认证成功，已发放奖励`);
    } catch (rewardError) {
      // 记录奖励失败，但不影响认证结果
      this.logger.error(
        `用户 ${userId} ${verificationType} 认证奖励发放失败: ${rewardError.message}`,
        rewardError.stack
      );
      
      // 将失败记录添加到重试队列
      await this.addToRetryQueue({
        userId,
        verificationType,
        serviceIds: services.map(s => s.id),
        rewardCount: verificationType === 'personal' ? 10 : 20,
        attempts: 0
      });
    }
    
    return {
      success: true,
      message: `${verificationType === 'personal' ? '个人' : '企业'}实名认证审核通过`
    };
  } catch (error) {
    // 事务回滚
    if (queryRunner.isTransactionActive) {
      await queryRunner.rollbackTransaction();
    }
    
    this.logger.error(
      `用户 ${userId} ${verificationType} 认证状态更新失败: ${error.message}`,
      error.stack
    );
    
    return {
      success: false,
      message: `认证状态更新失败: ${error.message}`
    };
  } finally {
    // 释放资源
    await queryRunner.release();
  }
}
```

## 6. 安全与隐私

### 6.1 数据安全

- 密码使用bcrypt算法加盐哈希存储，不可逆
- 身份证号码等敏感信息采用AES-256加密存储
- 认证图片存储采用私有访问策略，通过临时URL访问

### 6.2 隐私保护

- API响应中对邮箱、手机号进行部分遮蔽处理
- 遵循最小授权原则，仅收集必要的个人信息
- 个人敏感数据在展示时进行脱敏处理

## 7. 后续优化方向

- **用户群组管理**：支持企业用户创建用户组并分配权限
- **实名认证流程优化**：接入第三方实名认证接口，实现实时验证
- **用户标签系统**：基于用户行为和属性的标签管理
- **用户画像构建**：基于用户行为的画像分析
- **多级权限系统**：细粒度的功能和数据权限控制

## 8. 附录：相关配置

### 8.1 用户模块配置

```yaml
# 部分示例配置
user:
  defaultSettings:
    initialFreeQuota: 5
    personalVerifiedQuota: 15
    enterpriseVerifiedQuota: 25
    freeQuotaValidityDays: 180
  security:
    passwordSaltRounds: 10
    sensitiveDataEncryptionKey: ${USER_ENCRYPTION_KEY}
  verification:
    personalReviewEnabled: true
    enterpriseReviewEnabled: true
    automaticReviewTimeout: 86400 # 24小时自动审核超时
``` 