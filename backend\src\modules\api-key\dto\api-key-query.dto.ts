import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsInt, IsEnum, IsString, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiKeyStatus } from '../enums/api-key-status.enum';

/**
 * API密钥查询DTO
 */
export class ApiKeyQueryDto {
  @ApiProperty({ 
    description: '页码', 
    default: 1,
    required: false
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({ 
    description: '每页数量', 
    default: 10,
    required: false
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  limit?: number = 10;

  @ApiProperty({ 
    description: '状态过滤', 
    enum: ApiKeyStatus,
    required: false
  })
  @IsOptional()
  @IsEnum(ApiKeyStatus)
  status?: ApiKeyStatus;

  @ApiProperty({ 
    description: '排序字段', 
    default: 'createdAt',
    required: false
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiProperty({ 
    description: '排序方向', 
    enum: ['ASC', 'DESC'], 
    default: 'DESC',
    required: false
  })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortDirection?: 'ASC' | 'DESC' = 'DESC';

  @ApiProperty({
    description: '关键字搜索（名称）',
    required: false
  })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({
    description: '用户ID',
    required: false
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  userId?: number;

  @ApiProperty({
    description: '服务ID',
    required: false
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  serviceId?: number;

  @ApiProperty({
    description: '密钥类型',
    required: false
  })
  @IsOptional()
  @IsString()
  keyType?: string;
} 