import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Req,
  HttpCode,
  HttpStatus,
  UseGuards
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiNoContentResponse,
  ApiBearerAuth
} from '@nestjs/swagger';
import { Request } from 'express';
import { ApiKeyService } from './services/api-key.service';
import { CreateApiKeyDto } from './dto/create-api-key.dto';
import { ApiKeyQueryDto } from './dto/api-key-query.dto';
import { ApiKeyDto, PaginatedApiKeyDto } from './dto/api-key.dto';
import { AuthPermission } from '../../common/decorators/auth-permission.decorator';

/**
 * API密钥控制器
 * 处理API密钥的CRUD操作
 */
@ApiTags('API密钥管理')
@ApiBearerAuth()
@Controller('api-keys')
export class ApiKeyController {
  constructor(private readonly apiKeyService: ApiKeyService) {}

  /**
   * 创建API密钥
   */
  @Post()
  @ApiOperation({ summary: '创建API密钥' })
  @ApiCreatedResponse({ description: '创建成功', type: ApiKeyDto })
  @AuthPermission('api-key:create')
  async create(@Body() createApiKeyDto: CreateApiKeyDto, @Req() req: Request): Promise<ApiKeyDto> {
    const userId: number = req.user?.id  as number;
    return await this.apiKeyService.createApiKey(userId, createApiKeyDto);
  }

  /**
   * 获取API密钥列表
   */
  @Get()
  @ApiOperation({ summary: '获取API密钥列表' })
  @ApiOkResponse({ description: '查询成功', type: PaginatedApiKeyDto })
  @AuthPermission('api-key:read')
  async findAll(@Query() queryDto: ApiKeyQueryDto, @Req() req: Request): Promise<PaginatedApiKeyDto> {
    const userId = req.user?.id  as number;
    return await this.apiKeyService.findAll(userId, queryDto);
  }

  /**
   * 获取单个API密钥
   */
  @Get(':id')
  @ApiOperation({ summary: '获取单个API密钥' })
  @ApiOkResponse({ description: '查询成功', type: ApiKeyDto })
  @AuthPermission('api-key:read')
  async findOne(@Param('id') id: string, @Req() req: Request): Promise<ApiKeyDto> {
    const userId = req.user?.id  as number;
    return await this.apiKeyService.findOne(+id, userId);
  }

  /**
   * 重新生成API密钥
   */
  @Patch(':id/regenerate')
  @ApiOperation({ summary: '重新生成API密钥' })
  @ApiOkResponse({ description: '重新生成成功', type: ApiKeyDto })
  @AuthPermission('api-key:update')
  async regenerate(@Param('id') id: string, @Req() req: Request): Promise<ApiKeyDto> {
    const userId = req.user?.id  as number;
    return await this.apiKeyService.regenerateApiKey(+id, userId);
  }

  /**
   * 标记API密钥已查看
   */
  @Patch(':id/mark-viewed')
  @ApiOperation({ summary: '标记API密钥已查看' })
  @ApiNoContentResponse({ description: '标记成功' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @AuthPermission('api-key:update')
  async markViewed(@Param('id') id: string, @Req() req: Request): Promise<void> {
    const userId = req.user?.id  as number;
    await this.apiKeyService.markAsViewed(+id, userId);
  }

  /**
   * 删除API密钥
   */
  @Delete(':id')
  @ApiOperation({ summary: '删除API密钥' })
  @ApiNoContentResponse({ description: '删除成功' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @AuthPermission('api-key:delete')
  async remove(@Param('id') id: string, @Req() req: Request): Promise<void> {
    const userId = req.user?.id  as number;
    await this.apiKeyService.removeApiKey(+id, userId);
  }

  /**
   * 同步所有API密钥到缓存
   */
  @Post('sync-cache')
  @ApiOperation({ summary: '同步所有API密钥到缓存' })
  @ApiNoContentResponse({ description: '同步成功' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @AuthPermission('api-key:admin')
  async syncCache(): Promise<void> {
    await this.apiKeyService.syncAllApiKeysToCache();
  }

  /**
   * 获取用户的API密钥列表
   */
  @Get('user/:userId')
  @ApiOperation({ summary: '获取用户的API密钥列表' })
  @ApiOkResponse({ description: '查询成功', type: [ApiKeyDto] })
  @AuthPermission('api-key:admin')
  async findByUserId(@Param('userId') userId: string): Promise<ApiKeyDto[]> {
    return await this.apiKeyService.findByUserId(+userId);
  }
} 