ARG NODE_VERSION=18.18.0
FROM node:${NODE_VERSION}-slim AS builder

WORKDIR /app

# 复制package.json和pnpm-lock.yaml
COPY package.json pnpm-lock.yaml ./

# 安装pnpm和依赖
RUN npm install -g pnpm@8.8.0 && \
    pnpm install

# 复制源代码
COPY . .

# 构建应用 - 跳过类型检查
RUN pnpm run build

# 生产阶段 - 使用nginx提供静态文件服务
FROM nginx:stable-alpine

# 复制nginx配置
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD wget -q --spider http://localhost/ || exit 1
# HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
#   CMD wget -q --spider http://localhost:3000/ || exit 1

CMD ["nginx", "-g", "daemon off;"] 