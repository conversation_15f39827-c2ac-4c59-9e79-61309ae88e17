/**
 * 设备指纹生成工具
 * 基于 @fingerprintjs/fingerprintjs 插件实现
 * 用于生成唯一的设备标识，增强安全验证
 */

import FingerprintJS from '@fingerprintjs/fingerprintjs'

export interface DeviceFingerprint {
  fingerprint: string
  confidence: number
  components: Record<string, any>
  timestamp: number
}

class DeviceFingerprintGenerator {
  private fpPromise: Promise<any> | null = null

  constructor() {
    // 初始化 FingerprintJS
    this.fpPromise = FingerprintJS.load()
  }

  /**
   * 生成设备指纹
   */
  async generateFingerprint(): Promise<DeviceFingerprint> {
    try {
      if (!this.fpPromise) {
        this.fpPromise = FingerprintJS.load()
      }
      
      const fp = await this.fpPromise
      const result = await fp.get()
      
      return {
        fingerprint: result.visitorId,
        confidence: result.confidence?.score || 0.99,
        components: result.components,
        timestamp: Date.now()
      }
    } catch (error) {
      console.warn('设备指纹生成失败，使用备用方案:', error)
      return this.generateFallbackFingerprint()
    }
  }

  /**
   * 备用指纹生成方案（当 FingerprintJS 失败时使用）
   */
  private generateFallbackFingerprint(): DeviceFingerprint {
    const basicInfo = {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      screenResolution: `${screen.width}x${screen.height}`,
      timezone: new Date().getTimezoneOffset(),
      colorDepth: screen.colorDepth,
      pixelRatio: window.devicePixelRatio,
      cookieEnabled: navigator.cookieEnabled,
      hardwareConcurrency: navigator.hardwareConcurrency || 0
    }
    
    const fingerprint = this.simpleHash(JSON.stringify(basicInfo))
    
    return {
      fingerprint,
      confidence: 0.5, // 备用方案置信度较低
      components: basicInfo,
      timestamp: Date.now()
    }
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): string {
    let hash = 0
    if (str.length === 0) return hash.toString()
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString(16)
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.fpPromise = null
  }
}

// 导出单例实例
export const deviceFingerprint = new DeviceFingerprintGenerator()