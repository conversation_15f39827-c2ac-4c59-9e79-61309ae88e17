<template>
  <div class="image-cropper">
    <div class="cropper-header">
      <h3>图片裁剪</h3>
      <div class="header-actions">
        <el-button size="small" @click="resetCrop">
          <el-icon><RefreshLeft /></el-icon>
          重置
        </el-button>
        <el-button size="small" @click="$emit('cancel')">
          取消
        </el-button>
        <el-button type="primary" size="small" @click="confirmCrop">
          <el-icon><Check /></el-icon>
          确认
        </el-button>
      </div>
    </div>

    <div class="cropper-content">
      <!-- 左侧：原图和裁剪区域 -->
      <div class="cropper-main">
        <div class="cropper-container" ref="cropperContainer">
          <img
            ref="imageRef"
            :src="imageSrc"
            alt="待裁剪图片"
            class="cropper-image"
            @load="initCropper"
          />
        </div>
        
        <!-- 工具栏 -->
        <div class="cropper-toolbar">
          <div class="toolbar-group">
            <el-tooltip content="放大" placement="top">
              <el-button size="small" @click="zoomIn">
                <el-icon><ZoomIn /></el-icon>
              </el-button>
            </el-tooltip>
            
            <el-tooltip content="缩小" placement="top">
              <el-button size="small" @click="zoomOut">
                <el-icon><ZoomOut /></el-icon>
              </el-button>
            </el-tooltip>
            
            <el-tooltip content="向左旋转" placement="top">
              <el-button size="small" @click="rotateLeft">
                <el-icon><RefreshLeft /></el-icon>
              </el-button>
            </el-tooltip>
            
            <el-tooltip content="向右旋转" placement="top">
              <el-button size="small" @click="rotateRight">
                <el-icon><RefreshRight /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
          
          <div class="toolbar-group">
            <el-tooltip content="水平翻转" placement="top">
              <el-button size="small" @click="flipHorizontal">
                <el-icon><Sort /></el-icon>
              </el-button>
            </el-tooltip>
            
            <el-tooltip content="垂直翻转" placement="top">
              <el-button size="small" @click="flipVertical">
                <el-icon><Sort /></el-icon>
              </el-button>
            </el-tooltip>
            
            <el-tooltip content="移动模式" placement="top">
              <el-button size="small" :type="dragMode === 'move' ? 'primary' : 'default'" @click="setDragMode('move')">
                <el-icon><Rank /></el-icon>
              </el-button>
            </el-tooltip>
            
            <el-tooltip content="裁剪模式" placement="top">
              <el-button size="small" :type="dragMode === 'crop' ? 'primary' : 'default'" @click="setDragMode('crop')">
                <el-icon><Crop /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </div>
      
      <!-- 右侧：预览和设置 -->
      <div class="cropper-sidebar">
        <!-- 预览区域 -->
        <div class="preview-section">
          <h4>预览</h4>
          <div class="preview-container">
            <div class="preview-item">
              <div class="preview-label">原始尺寸</div>
              <div class="preview-box large" ref="previewLarge"></div>
            </div>
            
            <div class="preview-item">
              <div class="preview-label">缩略图</div>
              <div class="preview-box small" ref="previewSmall"></div>
            </div>
          </div>
        </div>
        
        <!-- 裁剪设置 -->
        <div class="settings-section">
          <h4>裁剪设置</h4>
          
          <div class="setting-group">
            <label>宽高比</label>
            <el-select v-model="aspectRatio" @change="setAspectRatio" size="small">
              <el-option label="自由" :value="NaN" />
              <el-option label="1:1" :value="1" />
              <el-option label="4:3" :value="4/3" />
              <el-option label="16:9" :value="16/9" />
              <el-option label="3:2" :value="3/2" />
              <el-option label="2:3" :value="2/3" />
            </el-select>
          </div>
          
          <div class="setting-group">
            <label>输出尺寸</label>
            <div class="size-inputs">
              <el-input
                v-model.number="outputWidth"
                placeholder="宽度"
                size="small"
                type="number"
                :min="1"
                :max="4096"
              >
                <template #suffix>px</template>
              </el-input>
              <span class="size-separator">×</span>
              <el-input
                v-model.number="outputHeight"
                placeholder="高度"
                size="small"
                type="number"
                :min="1"
                :max="4096"
              >
                <template #suffix>px</template>
              </el-input>
            </div>
          </div>
          
          <div class="setting-group">
            <label>输出质量</label>
            <el-slider
              v-model="outputQuality"
              :min="0.1"
              :max="1"
              :step="0.1"
              :format-tooltip="formatQuality"
            />
          </div>
          
          <div class="setting-group">
            <label>输出格式</label>
            <el-radio-group v-model="outputFormat" size="small">
              <el-radio label="image/jpeg">JPEG</el-radio>
              <el-radio label="image/png">PNG</el-radio>
              <el-radio label="image/webp">WebP</el-radio>
            </el-radio-group>
          </div>
        </div>
        
        <!-- 裁剪信息 -->
        <div class="info-section">
          <h4>裁剪信息</h4>
          <div class="info-item">
            <span class="info-label">X:</span>
            <span class="info-value">{{ Math.round(cropData.x) }}px</span>
          </div>
          <div class="info-item">
            <span class="info-label">Y:</span>
            <span class="info-value">{{ Math.round(cropData.y) }}px</span>
          </div>
          <div class="info-item">
            <span class="info-label">宽度:</span>
            <span class="info-value">{{ Math.round(cropData.width) }}px</span>
          </div>
          <div class="info-item">
            <span class="info-label">高度:</span>
            <span class="info-value">{{ Math.round(cropData.height) }}px</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  RefreshLeft,
  RefreshRight,
  Check,
  ZoomIn,
  ZoomOut,
  Sort,
  Rank,
  Crop
} from '@element-plus/icons-vue'
import { Cropper } from 'cropperjs'
import 'cropperjs/dist/cropper.css'
import '../../types/cropperjs'

interface Props {
  imageSrc: string
  aspectRatio?: number
  outputWidth?: number
  outputHeight?: number
  outputQuality?: number
  outputFormat?: string
}

const props = withDefaults(defineProps<Props>(), {
  aspectRatio: NaN,
  outputWidth: 800,
  outputHeight: 600,
  outputQuality: 0.9,
  outputFormat: 'image/jpeg'
})

const emit = defineEmits<{
  'confirm': [blob: Blob, cropData: any]
  'cancel': []
}>()

const imageRef = ref<HTMLImageElement>()
const cropperContainer = ref<HTMLDivElement>()
const previewLarge = ref<HTMLDivElement>()
const previewSmall = ref<HTMLDivElement>()

let cropper: Cropper | null = null

const aspectRatio = ref(props.aspectRatio)
const outputWidth = ref(props.outputWidth)
const outputHeight = ref(props.outputHeight)
const outputQuality = ref(props.outputQuality)
const outputFormat = ref(props.outputFormat)
const dragMode = ref<'move' | 'crop'>('crop')

const cropData = reactive({
  x: 0,
  y: 0,
  width: 0,
  height: 0,
  rotate: 0,
  scaleX: 1,
  scaleY: 1
})

// 初始化裁剪器
const initCropper = async () => {
  await nextTick()
  
  if (!imageRef.value) return
  
  // 销毁现有的cropper
  if (cropper) {
    cropper.destroy()
  }
  
  cropper = new Cropper(imageRef.value, {
    aspectRatio: aspectRatio.value,
    viewMode: 1,
    dragMode: dragMode.value,
    autoCropArea: 0.8,
    restore: false,
    guides: true,
    center: true,
    highlight: false,
    cropBoxMovable: true,
    cropBoxResizable: true,
    toggleDragModeOnDblclick: false,
    preview: [previewLarge.value, previewSmall.value].filter(Boolean),
    crop: (event:any) => {
      const data = event.detail
      Object.assign(cropData, {
        x: data.x,
        y: data.y,
        width: data.width,
        height: data.height,
        rotate: data.rotate,
        scaleX: data.scaleX,
        scaleY: data.scaleY
      })
    }
  })
}

// 工具栏操作
const zoomIn = () => {
  cropper?.zoom(0.1)
}

const zoomOut = () => {
  cropper?.zoom(-0.1)
}

const rotateLeft = () => {
  cropper?.rotate(-90)
}

const rotateRight = () => {
  cropper?.rotate(90)
}

const flipHorizontal = () => {
  const scaleX = cropData.scaleX === 1 ? -1 : 1
  cropper?.scaleX(scaleX)
}

const flipVertical = () => {
  const scaleY = cropData.scaleY === 1 ? -1 : 1
  cropper?.scaleY(scaleY)
}

const setDragMode = (mode: 'move' | 'crop') => {
  dragMode.value = mode
  cropper?.setDragMode(mode)
}

const resetCrop = () => {
  cropper?.reset()
}

// 设置宽高比
const setAspectRatio = () => {
  cropper?.setAspectRatio(aspectRatio.value)
}

// 确认裁剪
const confirmCrop = () => {
  if (!cropper) {
    ElMessage.error('裁剪器未初始化')
    return
  }
  
  try {
    const canvas = cropper.getCroppedCanvas({
      width: outputWidth.value,
      height: outputHeight.value,
      imageSmoothingEnabled: true,
      imageSmoothingQuality: 'high'
    })
    
    canvas.toBlob(
      (blob: any) => {
        if (blob) {
          const data = cropper!.getData()
          emit('confirm', blob, {
            ...data,
            outputWidth: outputWidth.value,
            outputHeight: outputHeight.value,
            outputQuality: outputQuality.value,
            outputFormat: outputFormat.value
          })
        } else {
          ElMessage.error('生成裁剪图片失败')
        }
      },
      outputFormat.value,
      outputQuality.value
    )
  } catch (error) {
    console.error('裁剪失败:', error)
    ElMessage.error('裁剪失败，请重试')
  }
}

// 格式化质量显示
const formatQuality = (value: number) => {
  return `${Math.round(value * 100)}%`
}

// 组件挂载
onMounted(() => {
  // 图片加载完成后会自动调用 initCropper
})

// 组件卸载
onUnmounted(() => {
  if (cropper) {
    cropper.destroy()
    cropper = null
  }
})
</script>

<style scoped>
.image-cropper {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.cropper-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f2f5;
  background: #fafbfc;
  flex-shrink: 0;
}

.cropper-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.cropper-content {
  display: flex;
  flex: 1;
  min-height: 0;
}

.cropper-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.cropper-container {
  flex: 1;
  position: relative;
  background: #f8f9fa;
  min-height: 400px;
}

.cropper-image {
  max-width: 100%;
  max-height: 100%;
  display: block;
}

.cropper-toolbar {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 12px 20px;
  border-top: 1px solid #f0f2f5;
  background: white;
  flex-shrink: 0;
}

.toolbar-group {
  display: flex;
  gap: 4px;
}

.cropper-sidebar {
  width: 280px;
  border-left: 1px solid #f0f2f5;
  background: #fafbfc;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.preview-section,
.settings-section,
.info-section {
  padding: 20px;
  border-bottom: 1px solid #f0f2f5;
}

.preview-section h4,
.settings-section h4,
.info-section h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.preview-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-item {
  text-align: center;
}

.preview-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.preview-box {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  margin: 0 auto;
  background: white;
}

.preview-box.large {
  width: 120px;
  height: 90px;
}

.preview-box.small {
  width: 60px;
  height: 45px;
}

.setting-group {
  margin-bottom: 16px;
}

.setting-group:last-child {
  margin-bottom: 0;
}

.setting-group label {
  display: block;
  font-size: 13px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 500;
}

.size-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.size-inputs .el-input {
  flex: 1;
}

.size-separator {
  color: #909399;
  font-size: 14px;
}

.info-section {
  flex: 1;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #909399;
}

.info-value {
  color: #303133;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .cropper-content {
    flex-direction: column;
  }
  
  .cropper-sidebar {
    width: 100%;
    border-left: none;
    border-top: 1px solid #f0f2f5;
  }
  
  .preview-container {
    flex-direction: row;
    justify-content: center;
    gap: 24px;
  }
  
  .settings-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
  
  .settings-section h4 {
    grid-column: 1 / -1;
    margin-bottom: 0;
  }
}

@media (max-width: 768px) {
  .cropper-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 16px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .cropper-container {
    min-height: 300px;
  }
  
  .cropper-toolbar {
    flex-direction: column;
    gap: 8px;
    padding: 12px 16px;
  }
  
  .toolbar-group {
    justify-content: center;
  }
  
  .preview-section,
  .settings-section,
  .info-section {
    padding: 16px;
  }
  
  .preview-container {
    flex-direction: column;
    gap: 16px;
  }
  
  .settings-section {
    display: block;
  }
  
  .size-inputs {
    flex-direction: column;
    gap: 8px;
  }
  
  .size-separator {
    display: none;
  }
}

/* Cropper.js 样式覆盖 */
:deep(.cropper-container) {
  direction: ltr;
}

:deep(.cropper-bg) {
  background-image: repeating-conic-gradient(
    #f8f9fa 0% 25%,
    #e9ecef 0% 50%
  ) 50% / 20px 20px;
}

:deep(.cropper-crop-box) {
  border-color: #409eff;
}

:deep(.cropper-view-box) {
  outline-color: #409eff;
}

:deep(.cropper-face) {
  background-color: inherit;
}

:deep(.cropper-line) {
  background-color: #409eff;
}

:deep(.cropper-point) {
  background-color: #409eff;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

:deep(.cropper-point.point-se) {
  width: 12px;
  height: 12px;
}
</style>