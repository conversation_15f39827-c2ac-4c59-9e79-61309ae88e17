<template>
  <div class="package-list-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="container">
        <h1 class="page-title">套餐包</h1>
        <p class="page-subtitle">选择适合您的套餐包，享受更优惠的价格和更多功能</p>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="container">
        <div class="filter-tabs">
          <el-tabs v-model="activeType" @tab-change="handleTypeChange">
            <el-tab-pane label="全部" name="all" />
            <el-tab-pane label="基础版" name="basic" />
            <el-tab-pane label="标准版" name="standard" />
            <el-tab-pane label="专业版" name="premium" />
            <el-tab-pane label="企业版" name="enterprise" />
          </el-tabs>
        </div>
        
        <div class="filter-controls">
          <el-select v-model="filters.billingType" placeholder="计费方式" clearable style="width: 120px;">
            <el-option label="一次性" value="one_time" />
            <el-option label="月付" value="monthly" />
            <el-option label="年付" value="yearly" />
            <el-option label="按量付费" value="usage_based" />
          </el-select>
          
          <el-input
            v-model="filters.keyword"
            placeholder="搜索套餐包"
            style="width: 200px;"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>
    </div>

    <!-- 推荐套餐包 -->
    <div v-if="packageStore.recommendedPackages.length > 0" class="recommended-section">
      <div class="container">
        <h2 class="section-title">推荐套餐</h2>
        <div class="packages-grid">
          <div 
            v-for="pkg in packageStore.recommendedPackages.slice(0, 3)" 
            :key="pkg.id"
            class="package-card recommended"
            @click="viewPackageDetail(pkg.id)"
          >
            <div class="package-badge">推荐</div>
            <PackageCard :package="pkg" :show-actions="true" @purchase="handlePurchase" />
          </div>
        </div>
      </div>
    </div>

    <!-- 套餐包列表 -->
    <div class="packages-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">所有套餐</h2>
          <div class="sort-controls">
            <el-select v-model="sortBy" placeholder="排序方式" style="width: 150px;">
              <el-option label="价格从低到高" value="price_asc" />
              <el-option label="价格从高到低" value="price_desc" />
              <el-option label="最受欢迎" value="popular" />
              <el-option label="最新发布" value="newest" />
            </el-select>
          </div>
        </div>
        
        <div class="packages-grid" v-loading="packageStore.loading">
          <div 
            v-for="pkg in filteredPackages" 
            :key="pkg.id"
            class="package-card"
            :class="{ popular: pkg.isPopular }"
            @click="viewPackageDetail(pkg.id)"
          >
            <div v-if="pkg.isPopular" class="package-badge popular">热门</div>
            <PackageCard :package="pkg" :show-actions="true" @purchase="handlePurchase" />
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="!packageStore.loading && filteredPackages.length === 0" class="empty-state">
          <el-empty description="暂无相关套餐包" />
        </div>
        
        <!-- 分页 -->
        <div v-if="packageStore.pagination.total > packageStore.pagination.pageSize" class="pagination-wrapper">
          <el-pagination
            v-model:current-page="packageStore.pagination.page"
            v-model:page-size="packageStore.pagination.pageSize"
            :total="packageStore.pagination.total"
            :page-sizes="[12, 24, 48]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 购买对话框 -->
    <el-dialog
      v-model="showPurchaseDialog"
      title="购买套餐包"
      width="500px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedPackage" class="purchase-content">
        <div class="package-summary">
          <h3>{{ selectedPackage.name }}</h3>
          <p class="package-price">
            <span class="price">¥{{ (selectedPackage.price / 100).toFixed(2) }}</span>
            <span v-if="selectedPackage.originalPrice" class="original-price">
              ¥{{ (selectedPackage.originalPrice / 100).toFixed(2) }}
            </span>
            <span class="billing-type">/ {{ getBillingTypeLabel(selectedPackage.billingType) }}</span>
          </p>
        </div>
        
        <el-form
          ref="purchaseFormRef"
          :model="purchaseForm"
          :rules="purchaseRules"
          label-width="100px"
        >
          <el-form-item label="购买数量" prop="quantity">
            <el-input-number
              v-model="purchaseForm.quantity"
              :min="1"
              :max="10"
              style="width: 100%;"
            />
          </el-form-item>
          
          <el-form-item label="支付方式" prop="paymentMethod">
            <el-select v-model="purchaseForm.paymentMethod" placeholder="请选择支付方式" style="width: 100%;">
              <el-option label="支付宝" value="alipay" />
              <el-option label="微信支付" value="wechat" />
              <el-option label="银行卡" value="bank_card" />
              <el-option label="余额" value="balance" />
            </el-select>
          </el-form-item>
          
          <el-form-item v-if="selectedPackage.billingType !== 'one_time'" label="自动续费">
            <el-switch v-model="purchaseForm.autoRenew" />
            <span class="auto-renew-tip">开启后将在到期前自动续费</span>
          </el-form-item>
          
          <el-form-item label="优惠券" prop="couponCode">
            <el-input
              v-model="purchaseForm.couponCode"
              placeholder="请输入优惠券代码（可选）"
            />
          </el-form-item>
        </el-form>
        
        <div class="total-amount">
          <div class="amount-row">
            <span>小计：</span>
            <span>¥{{ ((selectedPackage.price * (purchaseForm.quantity || 1)) / 100).toFixed(2) }}</span>
          </div>
          <div class="amount-row total">
            <span>总计：</span>
            <span>¥{{ ((selectedPackage.price * (purchaseForm.quantity || 1)) / 100).toFixed(2) }}</span>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showPurchaseDialog = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmPurchase" :loading="packageStore.loading">
          确认购买
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { usePackageStore } from '@/stores/package'
import PackageCard from '@/components/package/PackageCard.vue'
import type { Package, PurchasePackageForm } from '@/types/package'

const router = useRouter()
const packageStore = usePackageStore()

// 响应式数据
const activeType = ref('all')
const sortBy = ref('popular')
const showPurchaseDialog = ref(false)
const selectedPackage = ref<Package | null>(null)

// 筛选条件
const filters = reactive({
  billingType: '',
  keyword: ''
})

// 购买表单
const purchaseForm = reactive<PurchasePackageForm>({
  packageId: '',
  quantity: 1,
  paymentMethod: 'alipay',
  autoRenew: false,
  couponCode: ''
})

const purchaseFormRef = ref()
const purchaseRules = {
  quantity: [{ required: true, message: '请输入购买数量', trigger: 'blur' }],
  paymentMethod: [{ required: true, message: '请选择支付方式', trigger: 'change' }]
}

// 计算属性
const filteredPackages = computed(() => {
  let result = packageStore.packages
  
  // 按类型筛选
  if (activeType.value !== 'all') {
    result = result.filter(pkg => pkg.type === activeType.value)
  }
  
  // 按计费方式筛选
  if (filters.billingType) {
    result = result.filter(pkg => pkg.billingType === filters.billingType)
  }
  
  // 按关键词搜索
  if (filters.keyword) {
    const keyword = filters.keyword.toLowerCase()
    result = result.filter(pkg => 
      pkg.name.toLowerCase().includes(keyword) ||
      pkg.description.toLowerCase().includes(keyword) ||
      pkg.tags.some(tag => tag.toLowerCase().includes(keyword))
    )
  }
  
  // 排序
  switch (sortBy.value) {
    case 'price_asc':
      result = result.sort((a, b) => a.price - b.price)
      break
    case 'price_desc':
      result = result.sort((a, b) => b.price - a.price)
      break
    case 'popular':
      result = result.sort((a, b) => b.salesCount - a.salesCount)
      break
    case 'newest':
      result = result.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      break
  }
  
  return result
})

// 方法
const handleTypeChange = async () => {
  packageStore.pagination.page = 1
  await fetchPackages()
}

const handleSizeChange = async (size: number) => {
  packageStore.pagination.pageSize = size
  await fetchPackages()
}

const handleCurrentChange = async (page: number) => {
  packageStore.pagination.page = page
  await fetchPackages()
}

const fetchPackages = async () => {
  const params: any = {}
  
  if (activeType.value !== 'all') {
    params.type = activeType.value
  }
  
  if (filters.billingType) {
    params.billingType = filters.billingType
  }
  
  if (filters.keyword) {
    params.keyword = filters.keyword
  }
  
  await packageStore.fetchPackages(params)
}

const viewPackageDetail = (packageId: string) => {
  router.push(`/packages/${packageId}`)
}

const handlePurchase = (pkg: Package) => {
  selectedPackage.value = pkg
  purchaseForm.packageId = pkg.id
  purchaseForm.quantity = 1
  purchaseForm.paymentMethod = 'alipay'
  purchaseForm.autoRenew = false
  purchaseForm.couponCode = ''
  showPurchaseDialog.value = true
}

const handleConfirmPurchase = async () => {
  try {
    await purchaseFormRef.value?.validate()
    await packageStore.purchasePackage(purchaseForm)
    showPurchaseDialog.value = false
    ElMessage.success('购买成功！')
  } catch (error) {
    console.error('购买失败:', error)
  }
}

const getBillingTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    one_time: '一次性',
    monthly: '月',
    yearly: '年',
    usage_based: '按量'
  }
  return labels[type] || type
}

// 生命周期
onMounted(async () => {
  await fetchPackages()
  await packageStore.fetchRecommendations()
})

// 监听筛选条件变化
watch([() => filters.billingType, () => filters.keyword, sortBy], () => {
  fetchPackages()
})
</script>

<style scoped>
.package-list-page {
  min-height: 100vh;
  background: #f8fafc;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 0;
  text-align: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.page-title {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.page-subtitle {
  font-size: 18px;
  opacity: 0.9;
  margin: 0;
}

.filter-section {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 20px 0;
}

.filter-tabs {
  margin-bottom: 16px;
}

.filter-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.recommended-section {
  padding: 40px 0;
  background: white;
}

.packages-section {
  padding: 40px 0;
}

.section-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 24px 0;
  color: #1f2937;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.packages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.package-card {
  position: relative;
  background: white;
  border-radius: 16px;
  border: 2px solid transparent;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

.package-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.package-card.recommended {
  border-color: #10b981;
}

.package-card.popular {
  border-color: #f59e0b;
}

.package-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  background: #10b981;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  z-index: 1;
}

.package-badge.popular {
  background: #f59e0b;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.purchase-content {
  padding: 16px 0;
}

.package-summary {
  text-align: center;
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.package-summary h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1f2937;
}

.package-price {
  margin: 0;
}

.price {
  font-size: 28px;
  font-weight: 700;
  color: #3b82f6;
}

.original-price {
  font-size: 16px;
  color: #9ca3af;
  text-decoration: line-through;
  margin-left: 8px;
}

.billing-type {
  font-size: 14px;
  color: #6b7280;
  margin-left: 4px;
}

.auto-renew-tip {
  font-size: 12px;
  color: #6b7280;
  margin-left: 8px;
}

.total-amount {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.amount-row.total {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0;
  padding-top: 8px;
  border-top: 1px solid #e5e7eb;
}
</style>