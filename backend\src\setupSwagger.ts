import { INestApplication, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

export function setupSwagger(app: INestApplication): void {
  try {
    const logger = new Logger('SwaggerSetup');
    const configService: ConfigService<any> = app.get(ConfigService);

    // 默认为启用
    const enable = configService.get<boolean>('app.swagger.enable', true);

    // 判断是否需要启用
    if (!enable) {
      return;
    }

    const swaggerConfig = new DocumentBuilder()
      .setTitle(configService.get<string>('app.swagger.title') as string)
      .setDescription(configService.get<string>('app.swagger.desc') as string)
      .addBearerAuth()
      .addApiKey({ type: 'apiKey', name: 'X-API-KEY', in: 'header' }, 'X-API-KEY')
      .addApiKey({ type: 'apiKey', name: 'X-SECRET-KEY', in: 'header' }, 'X-SECRET-KEY')
      .addGlobalParameters({
        name: 'X-Request-ID',
        in: 'header',
        description: '请求追踪ID',
        required: false,
      })
      .setVersion(configService.get<string>('app.swagger.version') as string)
      .build();
    
    // 使用更安全的方式创建文档
    const document: any = SwaggerModule.createDocument(app, swaggerConfig, {
      deepScanRoutes: true,
      ignoreGlobalPrefix: false,
      extraModels: [],
      include: [], // 这里可以明确指定要包含的模块，避免扫描所有模块
    });

    // 添加全局响应描述
    if (document && document.components) {
      document.components.responses = {
        Success: {
          description: '请求成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean', example: true },
                  data: { type: 'object' },
                  message: { type: 'string', example: '操作成功' },
                  timestamp: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
                },
              },
            },
          },
        },
        Error: {
          description: '请求失败',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  statusCode: { type: 'number', example: 400 },
                  message: { type: 'string', example: '请求参数错误' },
                  error: { type: 'string', example: 'Bad Request' },
                  timestamp: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
                  path: { type: 'string', example: '/api/users' },
                },
              },
            },
          },
        },
      };
      
      // 为所有API添加默认的安全要求
      if (document.paths) {
        Object.keys(document.paths).forEach(path => {
          const pathItem = document.paths[path];
          Object.keys(pathItem).forEach(method => {
            if (!pathItem[method].security) {
              pathItem[method].security = [
                { 'X-API-KEY': [] },
                { 'X-SECRET-KEY': [] },
                { 'bearer': [] }
              ];
            }
            
            // 处理文件上传的请求体
            if (pathItem[method].requestBody && 
                pathItem[method].requestBody.content && 
                pathItem[method].requestBody.content['multipart/form-data']) {
              const schema = pathItem[method].requestBody.content['multipart/form-data'].schema;
              if (schema && schema.properties && schema.properties.file) {
                // 确保文件上传字段正确配置
                schema.properties.file = {
                  type: 'string',
                  format: 'binary',
                  description: '要上传的文件'
                };
              }
            }
          });
        });
      }
    } else {
      logger.warn('Swagger document or components is undefined');
    }

    SwaggerModule.setup(
      `${configService.get<string>('app.prefix')}/swagger-ui`,
      app,
      document,
      {
        swaggerOptions: {
          persistAuthorization: true,
          tryItOutEnabled: true,
          displayRequestDuration: true,
          filter: true,
          deepLinking: true,
          // 增加对文件上传的支持
          supportedSubmitMethods: ['get', 'put', 'post', 'delete', 'options', 'head', 'patch', 'trace'],
          // 增加请求体大小限制
          requestInterceptor: (req) => {
            // 增加请求拦截器，可以在这里处理大型请求
            return req;
          }
        },
        customSiteTitle: 'Nest-Open API Docs',
      },
    );
    
    logger.log('Swagger documentation setup successfully');
  } catch (error) {
    const logger = new Logger('SwaggerSetup');
    logger.error(`Failed to setup Swagger: ${error.message}`, error.stack);
    // 继续应用启动，不因Swagger设置失败而中断整个应用
  }
}
