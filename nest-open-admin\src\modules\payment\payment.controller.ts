import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { PaymentService, TopUpRequest, ServicePurchaseRequest, PaymentCallbackData } from './payment.service';
import { Roles } from '../../common/decorators/roles.decorator';
import { RoleEnum } from '../user/entities/user.entity';
import { CreateTopUpDto } from './dto/create-topup.dto';
import { CreateServicePurchaseDto } from './dto/create-service-purchase.dto';
import { PaymentCallbackDto } from './dto/payment-callback.dto';

@ApiTags('支付管理')
@Controller('payment')
export class PaymentController {
  private readonly logger = new Logger(PaymentController.name);

  constructor(private readonly paymentService: PaymentService) {}

  @Post('topup')
  async createTopUpOrder(
    @Request() req: any,
    @Body() createTopUpDto: CreateTopUpDto,
  ) {
    try {
      const request: TopUpRequest = {
        userId: req.user.id,
        amount: createTopUpDto.amount,
        paymentMethod: createTopUpDto.paymentMethod,
        returnUrl: createTopUpDto.returnUrl,
        notifyUrl: createTopUpDto.notifyUrl,
      };

      const result = await this.paymentService.createTopUpOrder(request);
      
      this.logger.log(`用户${req.user.id}创建充值订单: ${result.orderId}`);
      
      return {
        code: 200,
        message: '充值订单创建成功',
        data: result,
      };
    } catch (error) {
      this.logger.error('创建充值订单失败', error);
      throw error;
    }
  }

  @Post('service-purchase')
  @ApiOperation({ summary: '创建服务购买订单' })
  async createServicePurchaseOrder(
    @Request() req: any,
    @Body() createServicePurchaseDto: CreateServicePurchaseDto,
  ) {
    try {
      const request: ServicePurchaseRequest = {
        userId: req.user.id,
        serviceId: createServicePurchaseDto.serviceId,
        quantity: createServicePurchaseDto.quantity,
        paymentMethod: createServicePurchaseDto.paymentMethod,
        returnUrl: createServicePurchaseDto.returnUrl,
        notifyUrl: createServicePurchaseDto.notifyUrl,
      };

      const result = await this.paymentService.createServicePurchaseOrder(request);
      
      this.logger.log(`用户${req.user.id}创建服务购买订单: ${result.orderId}`);
      
      return {
        code: 200,
        message: '服务购买订单创建成功',
        data: result,
      };
    } catch (error) {
      this.logger.error('创建服务购买订单失败', error);
      throw error;
    }
  }

  @Post('callback')
  @ApiOperation({ summary: '支付回调接口' })
  async handlePaymentCallback(@Body() callbackDto: PaymentCallbackDto) {
    try {
      this.logger.log(`收到支付回调: 订单${callbackDto.orderId}, 状态${callbackDto.status}`);
      
      const callbackData: PaymentCallbackData = {
        orderId: callbackDto.orderId,
        paymentId: callbackDto.paymentId,
        amount: callbackDto.amount,
        status: callbackDto.status,
        paymentMethod: callbackDto.paymentMethod,
        transactionId: callbackDto.transactionId,
        paidAt: callbackDto.paidAt ? new Date(callbackDto.paidAt) : undefined,
        metadata: callbackDto.metadata,
      };

      const result = await this.paymentService.handlePaymentCallback(callbackData);
      
      return {
        code: 200,
        message: result.message,
        success: result.success,
      };
    } catch (error) {
      this.logger.error('支付回调处理失败', error);
      return {
        code: 500,
        message: '回调处理失败',
        success: false,
      };
    }
  }

  @Get('status/:orderNumber')
  @ApiOperation({ summary: '查询支付状态' })
  async queryPaymentStatus(
    @Request() req: any,
    @Param('orderNumber') orderNumber: string,
  ) {
    try {
      const result = await this.paymentService.queryPaymentStatus(orderNumber);
      
      return {
        code: 200,
        message: '查询成功',
        data: result,
      };
    } catch (error) {
      this.logger.error('查询支付状态失败', error);
      throw error;
    }
  }

  @Get('methods')
  @ApiOperation({ summary: '获取支持的支付方式' })
  async getPaymentMethods() {
    return {
      code: 200,
      message: '获取成功',
      data: [
        {
          code: 'alipay',
          name: '支付宝',
          icon: '/images/payment/alipay.png',
          enabled: true,
        },
        {
          code: 'wechat',
          name: '微信支付',
          icon: '/images/payment/wechat.png',
          enabled: true,
        },
        {
          code: 'bank',
          name: '银行卡',
          icon: '/images/payment/bank.png',
          enabled: false,
        },
      ],
    };
  }

  @Get('orders')
  @ApiOperation({ summary: '获取用户支付订单列表' })
  async getUserPaymentOrders(
    @Request() req: any,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('status') status?: string,
    @Query('type') type?: string,
  ) {
    try {
      // 这里可以调用 OrderService 来获取用户的支付订单
      // 暂时返回模拟数据
      return {
        code: 200,
        message: '获取成功',
        data: {
          list: [],
          total: 0,
          page: Number(page),
          limit: Number(limit),
        },
      };
    } catch (error) {
      this.logger.error('获取用户支付订单失败', error);
      throw error;
    }
  }

  @Post('test-callback')
  @Roles(RoleEnum.ADMIN, RoleEnum.SUPER)
  @ApiOperation({ summary: '测试支付回调（仅管理员）' })
  async testPaymentCallback(
    @Body() testData: {
      orderNumber: string;
      status: 'success' | 'failed' | 'cancelled';
      transactionId?: string;
    },
  ) {
    try {
      const callbackData: PaymentCallbackData = {
        orderId: testData.orderNumber,
        paymentId: `test_${Date.now()}`,
        amount: 0.01, // 测试金额
        status: testData.status,
        paymentMethod: 'alipay',
        transactionId: testData.transactionId || `test_txn_${Date.now()}`,
        paidAt: new Date(),
        metadata: { test: true },
      };

      const result = await this.paymentService.handlePaymentCallback(callbackData);
      
      return {
        code: 200,
        message: '测试回调处理成功',
        data: result,
      };
    } catch (error) {
      this.logger.error('测试支付回调失败', error);
      throw error;
    }
  }
}