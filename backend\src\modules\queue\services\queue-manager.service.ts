import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { StructuredLogger } from '../../../common/logging/structured-logger';
import { TaskService } from './task.service';
import { QueuePriority } from '../config/queue.config';
import { CreateTaskOptions } from '../interfaces/task.interface';

/**
 * 队列管理服务
 * 管理所有队列的操作，包括任务添加、暂停、恢复等
 */
@Injectable()
export class QueueManagerService {
  private queues: Record<string, Queue> = {};
  
  constructor(
    @InjectQueue('ocr') private readonly ocrQueue: Queue,
    @InjectQueue('sto-ocr') private readonly stoOcrQueue: Queue,
    @InjectQueue('extract-address') private readonly addressQueue: Queue,
    @InjectQueue('rev-geo') private readonly geoQueue: Queue,
    private readonly taskService: TaskService,
    private readonly logger: StructuredLogger,
  ) {
    this.queues = {
      'ocr': ocrQueue,
      'sto-ocr': stoOcrQueue,
      'extract-address': addressQueue,
      'rev-geo': geoQueue,
    };
    
    this.initQueueEvents();
  }
  
  /**
   * 初始化队列事件监听
   */
  private initQueueEvents(): void {
    Object.entries(this.queues).forEach(([name, queue]) => {
      queue.on('error', (error) => {
        this.logger.error(
          `队列${name}发生错误`,
          error,
          { module: 'QueueManagerService' }
        );
      });
      
      queue.on('stalled', (job) => {
        this.logger.warn(
          `队列${name}中的任务${job.id}已停滞`,
          { module: 'QueueManagerService', metadata: { jobId: job.id } }
        );
      });
    });
  }
  
  /**
   * 根据任务类型获取对应的队列
   */
  private getQueueByTaskType(taskType: string): Queue {
    switch (taskType) {
      case 'ocr':
        return this.ocrQueue;
      case 'sto-ocr':
        return this.stoOcrQueue;
      case 'extract-address':
        return this.addressQueue;
      case 'rev-geo':
        return this.geoQueue;
      default:
        throw new BadRequestException(`不支持的任务类型: ${taskType}`);
    }
  }
  
  /**
   * 添加任务到队列
   */
  async addTask(
    taskType: string,
    data: any,
    options: CreateTaskOptions = {},
  ): Promise<string> {
    // 创建任务记录并获取任务ID
    const taskId = await this.taskService.createTask(data, taskType);
    
    // 添加任务到队列
    const queue = this.getQueueByTaskType(taskType);
    const jobData = { taskId, ...data };
    
    await queue.add(jobData, {
      priority: options.priority || QueuePriority.NORMAL,
      delay: options.delay || 0,
      jobId: taskId,
    });
    
    this.logger.log(
      `添加${taskType}任务${taskId}到队列`,
      { 
        module: 'QueueManagerService',
        metadata: { 
          priority: options.priority, 
          delay: options.delay 
        }
      }
    );
    
    return taskId;
  }
  
  /**
   * 获取队列状态
   */
  async getQueueStatus(queueName?: string): Promise<Record<string, any>> {
    if (queueName) {
      const queue = this.queues[queueName];
      if (!queue) {
        throw new NotFoundException(`队列${queueName}不存在`);
      }
      
      const [waiting, active, completed, failed] = await Promise.all([
        queue.getWaitingCount(),
        queue.getActiveCount(),
        queue.getCompletedCount(),
        queue.getFailedCount(),
      ]);
      
      return {
        name: queueName,
        waiting,
        active,
        completed,
        failed,
      };
    }
    
    // 获取所有队列的状态
    const status = await Promise.all(
      Object.entries(this.queues).map(async ([name, queue]) => {
        const [waiting, active, completed, failed] = await Promise.all([
          queue.getWaitingCount(),
          queue.getActiveCount(),
          queue.getCompletedCount(),
          queue.getFailedCount(),
        ]);
        
        return {
          name,
          waiting,
          active,
          completed,
          failed,
        };
      })
    );
    
    return { queues: status };
  }
  
  /**
   * 暂停队列
   */
  async pauseQueue(queueName: string): Promise<void> {
    const queue = this.queues[queueName];
    if (!queue) {
      throw new NotFoundException(`队列${queueName}不存在`);
    }
    
    await queue.pause();
    this.logger.log(`队列${queueName}已暂停`, { module: 'QueueManagerService' });
  }
  
  /**
   * 恢复队列
   */
  async resumeQueue(queueName: string): Promise<void> {
    const queue = this.queues[queueName];
    if (!queue) {
      throw new NotFoundException(`队列${queueName}不存在`);
    }
    
    await queue.resume();
    this.logger.log(`队列${queueName}已恢复`, { module: 'QueueManagerService' });
  }
  
  /**
   * 清空队列
   */
  async cleanQueue(queueName: string): Promise<void> {
    const queue = this.queues[queueName];
    if (!queue) {
      throw new NotFoundException(`队列${queueName}不存在`);
    }
    
    await queue.clean(0, 'completed');
    await queue.clean(0, 'failed');
    this.logger.log(
      `队列${queueName}已清空已完成和失败的任务`, 
      { module: 'QueueManagerService' }
    );
  }
} 