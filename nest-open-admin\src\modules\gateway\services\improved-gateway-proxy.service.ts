import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
  ServiceUnavailableException,
  ForbiddenException,
  HttpStatus,
  Inject,
  forwardRef,
  UnauthorizedException,
} from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import { firstValueFrom } from 'rxjs';
import { AxiosRequestConfig } from 'axios';
import * as fs from 'fs';
import { RedisService } from '../../../shared/redis.service';
import { AuthUser, AuthApiKey } from '../../../common/types/auth.types';
import {
  getServiceConfig,
  ServiceDefinition,
} from '../config/service-registry.config';
import {
  ProcessingModeDecider,
  RequestOptions,
} from './processing-mode-decider.service';
import { ExternalServiceClient } from './external-service-client.service';
import { ApiUsageTrackerService } from '../../call-record/services/api-usage-tracker.service';
import { KeyManagementService } from '../../api-key/services/key-management.service';
import { EnhancedQueueManagerService } from '../../queue/services/enhanced-queue-manager.service';
import { GatewayService } from '../gateway.service';
import { ModuleRef } from '@nestjs/core';

/**
 * 请求选项接口
 */
export interface ProxyRequestOptions {
  method: string;
  serviceCode: string;
  path: string;
  body?: any;
  query?: Record<string, any>;
  headers?: Record<string, string>;
  user?: AuthUser;
  apiKey?: AuthApiKey;
  originalRequest?: Request;
  forceProcessingMode?: 'sync' | 'async';
}

/**
 * 响应接口
 */
export interface ProxyResponse {
  success?: boolean;
  data?: any;
  jobId?: string | number;
  message?: string;
  statusCode: number;
  status?: string;
  headers: Record<string, string>;
  responseTime: number;
  metadata: {
    serviceId: number;
    callRecordId: string;
    requestId: string;
    cached: boolean;
    processingMode?: 'sync' | 'async';
  };
}

/**
 * 改进的网关代理服务
 * 提供真实的请求代理、异步任务处理、任务状态查询等功能
 */
@Injectable()
export class ImprovedGatewayProxyService {
  private readonly logger = new Logger(ImprovedGatewayProxyService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly processingModeDecider: ProcessingModeDecider,
    private readonly externalServiceClient: ExternalServiceClient,
    @Inject(forwardRef(() => EnhancedQueueManagerService))
    private readonly queueManager: EnhancedQueueManagerService,
    @Inject(forwardRef(() => ApiUsageTrackerService))
    private readonly apiUsageTracker: ApiUsageTrackerService,
    @Inject(forwardRef(() => KeyManagementService))
    private readonly keyManagementService: KeyManagementService,
    private readonly moduleRef: ModuleRef,
  ) {
    this.logger.log('改进的网关代理服务已初始化');
  }

  /**
   * 代理请求
   */
  async proxyRequest(options: any | ProxyRequestOptions): Promise<ProxyResponse> {
    const startTime = Date.now();
    const requestId = this.generateRequestId();

    try {
      // 获取服务配置
      const serviceConfig = this.getServiceConfigOrThrow(options.serviceCode);

      // 检查服务权限
      await this.checkServiceAccess(
        options?.user?.id,
        String(options?.apiKey?.id),
        serviceConfig.id,
      );

      // 确定处理模式（同步/异步）
      const requestOptions: RequestOptions = {
        method: options.method,
        path: options.path,
        body: options.body,
        query: options.query,
      };

      const processingMode =
        options.forceProcessingMode ||
        this.processingModeDecider.decideProcessingMode(
          options.serviceCode,
          requestOptions,
          options.query?.mode as 'sync' | 'async',
        );

      // 记录请求开始
      this.logger.log(
        `[${requestId}] 代理请求开始: ${options.method} ${options.serviceCode}/${options.path}, 处理模式: ${processingMode}`,
      );

      // 检查文件上传情况
      if (options.body?.file) {
        this.logger.debug(
          `[${requestId}] 检测到文件上传: ${options.body.file.originalname}, 路径: ${options.body.file.path}`,
        );

        try {
          const fileExists = fs.existsSync(options.body.file.path);
          this.logger.debug(
            `[${requestId}] 文件检查: 路径=${options.body.file.path}, 存在=${fileExists}`,
          );

          if (fileExists) {
            const stats = fs.statSync(options.body.file.path);
            this.logger.debug(
              `[${requestId}] 文件状态: 大小=${stats.size}, 是否文件=${stats.isFile()}`,
            );
          }
        } catch (error) {
          this.logger.error(`[${requestId}] 检查文件失败: ${error.message}`);
        }
      }

      // 根据处理模式选择不同的处理路径
      if (processingMode === 'async') {
        return await this.handleAsyncRequest(
          options,
          serviceConfig,
          requestId,
          startTime,
        );
      } else {
        return await this.handleSyncRequest(
          options,
          serviceConfig,
          requestId,
          startTime,
        );
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;

      this.logger.error(
        `[${requestId}] 代理请求失败，耗时: ${responseTime}ms`,
        error.message,
        error.stack,
      );

      throw this.handleProxyError(error);
    }
  }

  /**
   * 获取任务状态
   */
  async getTaskStatus(jobId: string, serviceCode?: string): Promise<any> {
    if (!jobId) {
      throw new BadRequestException('任务ID不能为空');
    }

    try {
      this.logger.debug(
        `开始获取任务状态: jobId=${jobId}, serviceCode=${serviceCode || '未指定'}`,
      );

      // 从Redis中获取任务信息
      const jobInfo = await this.redisService.getJson<{
        queueName: string;
        userId: string;
        apiKeyId: string;
        serviceId: string;
        requestId: string;
        callRecordId: string;
      }>(`job:${jobId}`);

      this.logger.debug(`Redis中的任务信息: ${JSON.stringify(jobInfo)}`);

      if (!jobInfo) {
        this.logger.warn(`任务 ${jobId} 在Redis中不存在`);
        throw new NotFoundException(`任务 ${jobId} 不存在`);
      }

      const queueName = jobInfo.queueName;
      this.logger.debug(`任务队列名称: ${queueName}`);

      // 如果提供了serviceCode，验证任务是否属于该服务
      if (serviceCode) {
        const serviceConfig = this.getServiceConfigOrThrow(serviceCode);
        if (serviceConfig.queueName !== queueName) {
          this.logger.warn(
            `任务 ${jobId} 不属于服务 ${serviceCode}, 实际队列: ${queueName}, 期望队列: ${serviceConfig.queueName}`,
          );
          throw new BadRequestException(
            `任务 ${jobId} 不属于服务 ${serviceCode}`,
          );
        }
      }

      // 获取任务状态
      this.logger.debug(`从队列 ${queueName} 获取任务 ${jobId} 的状态`);
      const status = await this.queueManager.getJobStatus(queueName, jobId);
      this.logger.debug(`任务状态: ${JSON.stringify(status)}`);

      // 转换状态数据为标准格式，扁平化数据结构
      const result = {
        success: true,
        jobId,
        status: status.state || 'unknown',
        progress: status.progress || 0,
        queuedAt: status.timestamp?.created || Date.now(),
      };

      // 如果任务已完成并且有结果数据，将结果数据提升到顶层
      if (status.result) {
        // 根据队列名称判断业务类型，进行特殊处理
        switch (queueName) {
          case 'extract-address': // 地址提取服务
            if (typeof status.result === 'object') {
              // 处理地址数据
              if (status.result.data && Array.isArray(status.result.data)) {
                // 确保地址数据中包含detail_address字段
                const addresses = status.result.data.map(addr => {
                console.log('addr::::', addr)
                  // 如果原始数据中没有detailAddress字段，但有detail_address字段，使用detail_address
                  if (!addr.detailAddress && addr.detail_address) {
                    return { ...addr, detailAddress: addr.detail_address };
                  }
                  // 如果原始数据中没有detail_address字段，但有detailAddress字段，添加detail_address
                  else if (!addr.detail_address && addr.detailAddress) {
                    return { ...addr, detail_address: addr.detailAddress };
                  }
                  return addr;
                });
                result['addresses'] = addresses;
              }
              
              // 处理处理时间
              if (status.result.duration || status.result.processingTime) {
                result['duration'] = status.result.duration || status.result.processingTime;
              }
              
              // 处理错误情况
              if (status.result.success === false) {
                result['success'] = false;
                result['error'] = status.result.error || '地址提取失败';
              }
              
              // 其他可能的字段也一并提升
              if (status.result.message) {
                result['message'] = status.result.message;
              }
            }
            break;

          case 'ocr': // OCR服务
            if (typeof status.result === 'object') {
              // 处理识别结果
              if (status.result.data) {
                // 如果有原始OCR结果，使用GatewayService的本地解析方法处理
                if (status.result.data.rawData && status.result.data.rawData.results) {
                  try {
                    // 调用本地解析服务处理OCR结果
                    const gatewayService = await this.moduleRef.resolve(GatewayService);
                    const localResponse = await gatewayService.localParseOcrResult(
                      status.result.data.rawData.results
                    );
                    
                    // 使用与同步调用相同的格式
                    if (localResponse && localResponse.data) {
                      result['recognitionResults'] = {
                        ...localResponse.data,
                        process_time: status.result.data.process_time || 0,
                        image_info: status.result.data.image_info || {}
                      };
                    } else {
                      // 如果本地解析失败，使用原始数据
                      result['recognitionResults'] = status.result.data;
                    }
                  } catch (error) {
                    this.logger.error(`处理OCR结果失败: ${error.message}`, error.stack);
                    // 使用原始数据作为备选
                    result['recognitionResults'] = status.result.data;
                  }
                } else {
                  // 如果没有原始OCR结果，直接使用处理后的数据
                  result['recognitionResults'] = status.result.data;
                }
              }
              
              // 处理处理时间
              if (status.result.duration || status.result.processingTime) {
                result['duration'] = status.result.duration || status.result.processingTime;
              }

              // 如果有识别文本，直接提取到顶层
              if (status.result.text) {
                result['text'] = status.result.text;
              }

              // 如果有置信度，提取到顶层
              if (status.result.confidence !== undefined) {
                result['confidence'] = status.result.confidence;
              }
              
              // 处理错误情况
              if (status.result.success === false) {
                result['success'] = false;
                result['error'] = status.result.error || 'OCR识别失败';
              }
              
              // 其他可能的字段也一并提升
              if (status.result.message) {
                result['message'] = status.result.message;
              }
            }
            break;

          case 'rev-geo': // 坐标逆解析服务
            // 直接将所有结果字段提升到顶层，确保不丢失任何信息
            if (typeof status.result === 'object') {
              // 处理数据字段
              if (status.result.data) {
                result['locations'] = status.result.data;
              }
              
              // 处理地址字段
              if (status.result.address) {
                result['address'] = status.result.address;
              }
              
              // 处理坐标字段
              if (status.result.coordinates) {
                result['coordinates'] = status.result.coordinates;
              }
              
              // 处理处理时间
              if (status.result.duration || status.result.processingTime) {
                result['duration'] = status.result.duration || status.result.processingTime;
              }
              
              // 处理错误情况
              if (status.result.success === false) {
                result['success'] = false;
                result['error'] = status.result.error || '坐标逆解析失败';
              }
              
              // 其他可能的字段也一并提升
              if (status.result.message) {
                result['message'] = status.result.message;
              }

              // 记录完整的结果数据，帮助调试
              this.logger.debug(`坐标逆解析任务结果详情: ${JSON.stringify(status.result)}`);
            } else {
              // 如果结果不是对象，可能是直接的错误字符串
              if (typeof status.result === 'string') {
                result['error'] = status.result;
                result['success'] = false;
              }
            }
            break;

          default:
            // 通用处理，尝试将result中的所有属性提升到顶层
            if (typeof status.result === 'object') {
              // 提取关键数据
              if (status.result.data) {
                result['data'] = status.result.data;
              }

              // 提取处理时间
              if (status.result.duration || status.result.processingTime) {
                result['duration'] =
                  status.result.duration || status.result.processingTime;
              }
              
              // 处理错误情况
              if (status.result.success === false) {
                result['success'] = false;
                result['error'] = status.result.error || '任务处理失败';
              }
              
              // 提取消息字段
              if (status.result.message) {
                result['message'] = status.result.message;
              }

              // 其他属性也一并提升，但避免覆盖已有字段
              for (const key in status.result) {
                if (!['data', 'duration', 'processingTime'].includes(key) && !result[key]) {
                  result[key] = status.result[key];
                }
              }
            }
        }
      }
console.log("result>>>>>>>>>>::", result)
      return result;
    } catch (error) {
      this.logger.error(`获取任务状态失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 批量获取任务状态
   */
  async getBatchTaskStatus(jobIds: string[]): Promise<any> {
    if (!jobIds || jobIds.length === 0) {
      throw new BadRequestException('任务ID列表不能为空');
    }

    try {
      const results = await Promise.all(
        jobIds.map(async (jobId) => {
          try {
            const status = await this.getTaskStatus(jobId);
            return {
              jobId,
              success: true,
              ...status,
            };
          } catch (error) {
            return {
              jobId,
              success: false,
              error: error.message,
            };
          }
        }),
      );

      return {
        success: true,
        results,
      };
    } catch (error) {
      this.logger.error(`批量获取任务状态失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 取消任务
   */
  async cancelTask(jobId: string, user: AuthUser): Promise<any> {
    if (!jobId) {
      throw new BadRequestException('任务ID不能为空');
    }

    try {
      // 从Redis中获取任务信息
      const jobInfo = await this.redisService.getJson<{
        queueName: string;
        userId: string;
        apiKeyId: string;
        serviceId: string;
        requestId: string;
        callRecordId: string;
      }>(`job:${jobId}`);

      if (!jobInfo) {
        throw new NotFoundException(`任务 ${jobId} 不存在`);
      }

      const queueName = jobInfo.queueName;
      const userId = jobInfo.userId ? Number(jobInfo.userId) : null;

      // 检查任务是否属于该用户
      if (userId && userId !== user.id) {
        throw new ForbiddenException('无权操作此任务');
      }

      // 移除任务
      const removed = await this.queueManager.removeJob(queueName, jobId);

      if (!removed) {
        throw new ServiceUnavailableException(`无法取消任务 ${jobId}`);
      }

      return {
        success: true,
        jobId,
        message: '任务已取消',
      };
    } catch (error) {
      this.logger.error(`取消任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 处理同步请求
   */
  private async handleSyncRequest(
    options: any | ProxyRequestOptions,
    serviceConfig: ServiceDefinition,
    requestId: string,
    startTime: number,
  ): Promise<ProxyResponse> {
    let data: any;
    let cached = false;

    try {
      // 检查缓存
      const cacheKey = this.generateCacheKey(options);

      if (serviceConfig.cacheable && options.method === 'GET') {
        data = await this.redisService.get(cacheKey);
        cached = !!data;
      }

      // 准备请求参数（处理文件上传等）
      const requestParams = await this.prepareRequestParams(
        options,
        serviceConfig,
      );

      if (!cached) {
        // 调用外部服务
        data = await this.externalServiceClient.callExternalService(
          serviceConfig,
          options.path,
          options.method,
          requestParams,
          this.filterHeaders(options.headers),
        );
console.log('调用外部服务data:>>>>>>:', data)
        // OCR服务特殊处理
        if (serviceConfig.code === 'ocr') {
          // 检查是否为HTML格式的错误响应
          if (
            typeof data === 'string' &&
            data.trim().startsWith('<!doctype html>')
          ) {
            this.logger.debug(`OCR服务返回数据检查: ${data.substring(0, 200)}`);
            data = {
              success: false,
              error: 'OCR服务返回了无效的响应格式',
              message: '服务端错误，可能是路径或参数错误',
              html_response: data.substring(0, 500),
            };
          }
          // 处理OCR成功结果 - 构造符合Gateway返回格式的数据结构
          else if (data && data.results) {
            this.logger.debug(`OCR服务返回有效结果，进行数据格式化`);

            try {
              // 检查是否需要处理OCR结果
              if (
                options.path.includes('upload') ||
                options.path.includes('recognize')
              ) {
                this.logger.debug(`检测到OCR上传请求，设置合适的结果格式`);

                // 保存原始OCR结果
                const ocrResults = data.results;
                const processTime = data.process_time;
                const imageInfo = data.image_info;

                // 将原始OCR结果包装到响应中，但不包含过多冗余数据
                data = {
                  success: true,
                  rawData: {
                    results: ocrResults,
                  },
                  process_time: processTime,
                  image_info: imageInfo,
                };

                this.logger.debug(
                  `已将OCR结果格式化，结果将传递给本地解析服务`,
                );
              }
            } catch (parseError) {
              this.logger.error(
                `处理OCR结果时出错: ${parseError.message}`,
                parseError.stack,
              );
              // 返回原始数据，添加错误信息
              data = {
                success: false,
                error: `解析OCR结果失败: ${parseError.message}`,
                results: data.results,
                process_time: data.process_time,
                image_info: data.image_info,
              };
            }
          }
        }
        // 地理服务特殊处理
        else if (serviceConfig.code === 'geo') {
          // 检查是否为HTML格式的错误响应
          if (typeof data === 'string' && data.trim().startsWith('<!doctype html>')) {
            this.logger.debug(`地理服务返回数据检查: ${data.substring(0, 200)}`);
            data = {
              success: false,
              error: '坐标逆解析服务返回了无效的响应格式',
              message: '服务端错误，可能是路径或参数错误',
              html_response: data.substring(0, 500)
            };
          }
        }

        // 缓存响应（仅GET请求）
        if (serviceConfig.cacheable && options.method === 'GET') {
          await this.redisService.set(
            cacheKey,
            data,
            serviceConfig.cacheTimeout,
          );
        }
      }

      // 记录API调用
      const callRecordId = await this.apiUsageTracker.trackApiCall({
        userId: options.user.id,
        apiKeyId: String(options.apiKey.id),
        serviceId: serviceConfig.id,
        endpoint: `${options.method} ${options.serviceCode}/${options.path}`,
        responseTime: Date.now() - startTime,
        status: 'completed',
        requestId,
      });

      const responseTime = Date.now() - startTime;

      this.logger.log(
        `[${requestId}] 代理请求完成: ${options.method} ${options.serviceCode}/${options.path}, 耗时: ${responseTime}ms, 缓存: ${cached}`,
      );

      return {
        data: data,
        statusCode: HttpStatus.OK,
        headers: {
          'X-Request-Id': requestId,
          'X-Response-Time': `${responseTime}ms`,
          'X-Cache': cached ? 'HIT' : 'MISS',
        },
        responseTime,
        metadata: {
          serviceId: serviceConfig.id,
          callRecordId,
          requestId,
          cached: cached,
          processingMode: 'sync',
        },
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * 处理异步请求
   */
  private async handleAsyncRequest(
    options: any | ProxyRequestOptions,
    serviceConfig: ServiceDefinition,
    requestId: string,
    startTime: number,
  ): Promise<ProxyResponse> {
    this.logger.log(
      `[${requestId}] 处理异步请求: ${options.method} ${options.serviceCode}/${options.path}`,
    );

    try {
      // 打印原始请求体，帮助调试
      this.logger.debug(
        `[${requestId}] 原始请求体: ${JSON.stringify({
          ...options.body,
          file: options.body?.file
            ? {
                fieldname: options.body.file.fieldname,
                originalname: options.body.file.originalname,
                encoding: options.body.file.encoding,
                mimetype: options.body.file.mimetype,
                destination: options.body.file.destination,
                filename: options.body.file.filename,
                path: options.body.file.path,
                size: options.body.file.size,
              }
            : undefined,
        })}`,
      );

      // 特殊处理OCR文件上传
      let taskData;
      if (options.serviceCode === 'ocr' && options.body?.file) {
        try {
          // 直接读取文件内容并转换为Base64
          const filePath = options.body.file.path;

          // 检查文件是否存在
          if (!fs.existsSync(filePath)) {
            this.logger.error(`[${requestId}] 文件不存在: ${filePath}`);
            throw new Error(`文件不存在: ${filePath}`);
          }

          // 读取文件
          this.logger.debug(`[${requestId}] 开始读取文件: ${filePath}`);
          const fileData = fs.readFileSync(filePath);
          this.logger.debug(
            `[${requestId}] 文件读取成功，大小: ${fileData.length} 字节`,
          );

          // 转换为Base64
          const base64Data = (fileData.toString('base64') || '').replace(/^data:image\/\w+;base64,/, '');
          const padding = base64Data.length % 4;
          const paddedBase64 = padding ? base64Data + '='.repeat(4 - padding) : base64Data;
          this.logger.debug(
            `[${requestId}] 已直接读取文件并转换为Base64: ${options.body.file.originalname}, 长度: ${paddedBase64.length}`,
          );

          // 构建任务数据
          taskData = {
            ...options.body,
            file: options.body.file,
            image: paddedBase64,
            image_base64: paddedBase64,
            imageData: paddedBase64,
            filename: options.body.file.originalname,
            userId: options.user.id,
            apiKeyId: String(options.apiKey.id),
            serviceId: serviceConfig.id,
            requestId: requestId,
            callbackUrl:
              options.body?.callbackUrl || options.query?.callbackUrl,
          };
        } catch (error) {
          this.logger.error(
            `[${requestId}] 读取文件失败: ${error.message}`,
            error.stack,
          );
          throw new Error(`读取文件失败: ${error.message}`);
        }
      } else {
        // 准备任务数据
        taskData = await this.prepareRequestParams(options, serviceConfig);

        // 添加用户和API密钥信息
        taskData.userId = options.user.id;
        taskData.apiKeyId = String(options.apiKey.id);
        taskData.serviceId = serviceConfig.id;
        taskData.requestId = requestId;
        taskData.callbackUrl =
          options.body?.callbackUrl || options.query?.callbackUrl;

        // 确保OCR任务包含正确的图像数据字段
        if (options.serviceCode === 'ocr') {
          if (taskData.image) {
            taskData.imageData = taskData.image; // 确保imageData字段存在
            this.logger.debug(
              `[${requestId}] 已添加imageData字段，长度: ${taskData.imageData.length}`,
            );
          } else if (taskData.image_base64) {
            taskData.imageData = taskData.image_base64; // 备用字段
            this.logger.debug(
              `[${requestId}] 已添加imageData字段(从image_base64)，长度: ${taskData.imageData.length}`,
            );
          } else {
            this.logger.error(`[${requestId}] OCR任务缺少图像数据`);
            throw new Error('OCR任务缺少必要的图像数据');
          }
        }
      }

      // 打印处理后的任务数据
      this.logger.debug(
        `[${requestId}] 处理后的任务数据: ${JSON.stringify({
          ...taskData,
          image: taskData.image
            ? `[Base64数据，长度: ${taskData.image.length}]`
            : undefined,
          image_base64: taskData.image_base64
            ? `[Base64数据，长度: ${taskData.image_base64.length}]`
            : undefined,
          imageData: taskData.imageData
            ? `[Base64数据，长度: ${taskData.imageData.length}]`
            : undefined,
          file: taskData.file
            ? {
                fieldname: taskData.file.fieldname,
                originalname: taskData.file.originalname,
                path: taskData.file.path,
                size: taskData.file.size,
              }
            : undefined,
        })}`,
      );

      // 检查队列名称
      if (!serviceConfig.queueName) {
        throw new Error(`服务 ${options.serviceCode} 未配置队列名称`);
      }

      // 添加任务到队列
      const job = await this.queueManager.addJob(
        serviceConfig.queueName,
        this.getTaskTypeByServiceCode(options.serviceCode),
        taskData,
        {
          priority: 0,
          attempts: 3,
          removeOnComplete: false,
          removeOnFail: false,
        },
      );

      // 记录API调用
      const callRecordId = await this.apiUsageTracker.trackApiCall({
        userId: options?.user?.id,
        apiKeyId: String(options?.apiKey?.id),
        serviceId: serviceConfig?.id,
        endpoint: `${options.method} ${options.serviceCode}/${options.path}`,
        responseTime: Date.now() - startTime,
        status: 'queued',
        requestId: requestId,
        jobId: job.id,
      });

      // 在Redis中存储任务额外信息
      await this.redisService.set(
        `job:${job.id}`,
        {
          queueName: serviceConfig.queueName,
          userId: String(options?.user?.id),
          apiKeyId: String(options?.apiKey?.id),
          serviceId: String(serviceConfig?.id),
          requestId: requestId,
          callRecordId: callRecordId,
        },
        60 * 60 * 24, // 24小时
      );

      const responseTime = Date.now() - startTime;

      this.logger.log(
        `[${requestId}] 异步请求已排队，任务ID: ${job.id}, 耗时: ${responseTime}ms`,
      );

      // 直接返回简化的数据结构，与OCR服务保持一致
      return {
        success: true,
        jobId: job.id,
        message: '任务已提交到队列',
        status: 'queued',
        statusCode: HttpStatus.ACCEPTED,
        headers: {
          'X-Job-ID': job.id,
          'X-Processing-Mode': 'async',
        },
        responseTime,
        metadata: {
          serviceId: serviceConfig.id,
          callRecordId: callRecordId || '',
          requestId,
          cached: false,
          processingMode: 'async',
        },
      };
    } catch (error) {
      this.logger.error(
        `[${requestId}] 异步请求处理失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 准备请求参数
   */
  private async prepareRequestParams(
    options: ProxyRequestOptions,
    serviceConfig: ServiceDefinition,
  ): Promise<any> {
    // 根据不同的服务类型准备特定的请求参数
    if (options.serviceCode === 'ocr') {
      // OCR服务特殊处理
      if (options.path.includes('upload') && options.body?.file) {
        try {
          this.logger.debug(
            `OCR文件上传处理: ${options.body.file.originalname}`,
          );
          
          // 将文件对象重命名为images字段，以匹配Python OCR服务的预期
          const result = {
            ...options.body,
            images: options.body.file // 使用Python OCR服务期望的字段名
          };
          
          // 删除原始file字段，避免混淆
          delete result.file;
          
          this.logger.debug(`OCR请求数据处理完成，字段: ${Object.keys(result).join(', ')}`);
          
          return result;
        } catch (error) {
          this.logger.error(
            `转换文件为Base64失败: ${error.message}`,
            error.stack,
          );
          throw new Error(`处理文件失败: ${error.message}`);
        }
      } else if (options.body?.image_base64) {
        // 处理base64图片
        this.logger.debug(
          `处理Base64图像数据，长度: ${options.body.image_base64.length}`,
        );

        // 清理Base64数据 - 移除前缀，规范化填充
        const rawBase64 = options.body.image_base64.replace(/^data:image\/[^;]+;base64,/, '');
        this.logger.debug(`移除前缀后Base64长度: ${rawBase64.length}`);

        // 修复填充问题
        const padding = rawBase64.length % 4;
        const paddedBase64 = padding ? 
          rawBase64 + '='.repeat(4 - padding) : 
          rawBase64;
        
        // 清理非法字符
        const cleanBase64 = paddedBase64.replace(/[^A-Za-z0-9+/=]/g, '');
        
        this.logger.debug(`最终Base64数据长度: ${cleanBase64.length}`);
        
        return {
          ...options.body,
          image: cleanBase64, // 使用OCR服务期望的字段名
          image_base64: cleanBase64, // 替换为清理后的数据
          imageData: cleanBase64, // 直接添加imageData字段
        };
      }
    }

    return options.body;
  }

  /**
   * 将文件转换为Base64
   */
  private async fileToBase64(file: Express.Multer.File): Promise<string> {
    return new Promise((resolve, reject) => {
      fs.readFile(file.path, (err, data) => {
        if (err) {
          reject(err);
          return;
        }

        const base64 = `data:${file.mimetype};base64,${data.toString('base64')}`;

        // 不删除临时文件，因为它可能在后续处理中需要
        // 文件会在请求结束后由系统自动清理
        resolve(base64);
      });
    });
  }

  /**
   * 过滤请求头
   */
  private filterHeaders(
    headers: Record<string, string>,
  ): Record<string, string> {
    const allowedHeaders = [
      'content-type',
      'accept',
      'accept-language',
      'x-request-id',
      'user-agent',
    ];

    const filteredHeaders: Record<string, string> = {};

    for (const header of allowedHeaders) {
      if (headers[header]) {
        filteredHeaders[header] = headers[header];
      }
    }

    return filteredHeaders;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(options: ProxyRequestOptions): string {
    const queryString = options.query ? JSON.stringify(options.query) : '';
    const bodyHash = options.body
      ? this.hashString(JSON.stringify(options.body))
      : '';

    return `proxy:${options.method}:${options.serviceCode}:${options.path}:${queryString}:${bodyHash}`;
  }

  /**
   * 简单的字符串哈希函数
   */
  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return hash.toString(16);
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  }

  /**
   * 获取服务配置或抛出异常
   */
  private getServiceConfigOrThrow(serviceCode: string): ServiceDefinition {
    const serviceConfig = getServiceConfig(serviceCode);

    if (!serviceConfig) {
      throw new NotFoundException(`服务 ${serviceCode} 未找到`);
    }

    if (!serviceConfig.enabled) {
      throw new ServiceUnavailableException(`服务 ${serviceCode} 当前不可用`);
    }

    return serviceConfig;
  }

  /**
   * 检查服务访问权限
   */
  private async checkServiceAccess(
    userId: number,
    apiKeyId: string,
    serviceId: number,
  ): Promise<void> {
    // 检查API密钥是否有权限访问该服务
    const hasAccess = await this.keyManagementService.checkServiceAccess(
      userId,
      String(apiKeyId),
      serviceId,
    );

    if (!hasAccess) {
      throw new ForbiddenException(`API密钥无权访问服务ID: ${serviceId}`);
    }
  }

  /**
   * 处理代理错误
   */
  private handleProxyError(error: any): Error {
    // 记录详细错误信息
    this.logger.error(`代理请求错误: ${error.message}`, error.stack);

    // 根据错误类型返回适当的HTTP异常
    if (error.response) {
      // 外部服务返回的错误响应
      const status = error.response.status;
      const message = error.response.data?.message || error.message;

      if (status === 400) {
        return new BadRequestException(message);
      } else if (status === 401) {
        return new UnauthorizedException(message);
      } else if (status === 403) {
        return new ForbiddenException(message);
      } else if (status === 404) {
        return new NotFoundException(message);
      } else if (status >= 500) {
        return new ServiceUnavailableException('外部服务暂时不可用');
      }
    }

    // 特定错误类型处理
    if (
      error.code === 'ECONNREFUSED' ||
      error.code === 'ECONNABORTED' ||
      error.code === 'ETIMEDOUT'
    ) {
      return new ServiceUnavailableException('服务连接超时，请稍后重试');
    }

    if (error.message && error.message.includes('超出配额')) {
      return new ForbiddenException(error.message);
    }

    if (
      error.message &&
      error.message.includes('无效') &&
      error.message.includes('API')
    ) {
      return new UnauthorizedException(error.message);
    }

    if (error.message && error.message.includes('权限不足')) {
      return new ForbiddenException(error.message);
    }

    // 默认返回500错误
    return new ServiceUnavailableException('处理请求时发生错误，请稍后重试');
  }

  /**
   * 根据服务代码获取任务类型
   */
  private getTaskTypeByServiceCode(serviceCode: string): string {
    switch (serviceCode) {
      case 'ocr':
        return 'ocr_recognition_task';
      case 'address':
        return 'address_extraction';
      case 'geo':
        return 'reverse_geocoding';
      default:
        return 'generic_task';
    }
  }
}
