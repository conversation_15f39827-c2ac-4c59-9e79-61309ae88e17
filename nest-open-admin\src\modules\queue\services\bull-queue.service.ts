/**
 * Bull队列服务
 * 负责具体的任务调度和队列管理
 */

import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue, Job, JobOptions } from 'bull';
import { QUEUE_NAMES, TASK_PRIORITY, QueueName } from '../queue.constants';
import {
  ApiCallTaskData,
  AlertTaskData,
  OcrTaskData,
  AddressExtractionTaskData,
  ReverseGeocodingTaskData,
  QueueStatus,
  QueueStats,
  TaskResult,
} from '../interfaces/queue.interface';

@Injectable()
export class BullQueueService implements OnModuleInit {
  private readonly logger = new Logger(BullQueueService.name);

  constructor(
    @InjectQueue(QUEUE_NAMES.API_CALL)
    private readonly apiCallQueue: Queue<ApiCallTaskData>,
    @InjectQueue(QUEUE_NAMES.ALERT)
    private readonly alertQueue: Queue<AlertTaskData>,
    @InjectQueue(QUEUE_NAMES.OCR)
    private readonly ocrQueue: Queue<OcrTaskData>,
    @InjectQueue(QUEUE_NAMES.EXTRACT_ADDRESS)
    private readonly addressExtractionQueue: Queue<AddressExtractionTaskData>,
    @InjectQueue(QUEUE_NAMES.REV_GEO)
    private readonly reverseGeocodingQueue: Queue<ReverseGeocodingTaskData>,
  ) {}

  /**
   * 添加API调用任务
   */
  async addApiCallTask(
    data: ApiCallTaskData,
    options?: JobOptions,
  ): Promise<Job<ApiCallTaskData>> {
    try {
      const jobOptions: JobOptions = {
        priority: data.priority || TASK_PRIORITY.NORMAL,
        delay: data.delay || 0,
        attempts: data.attempts || 3,
        removeOnComplete: 20,
        removeOnFail: 10,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        ...options,
      };

      const job = await this.apiCallQueue.add(data, jobOptions);
      
      this.logger.log(`API调用任务已添加: ${job.id}, URL: ${data.url}`);
      return job;
    } catch (error) {
      this.logger.error(`添加API调用任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 添加告警任务
   */
  async addAlertTask(
    data: AlertTaskData,
    options?: JobOptions,
  ): Promise<Job<AlertTaskData>> {
    try {
      const jobOptions: JobOptions = {
        priority: this.getAlertPriority(data.level as string),
        delay: data.delay || 0,
        attempts: data.attempts || 2,
        removeOnComplete: 50,
        removeOnFail: 20,
        backoff: {
          type: 'fixed',
          delay: 1000,
        },
        ...options,
      };

      const job = await this.alertQueue.add(data, jobOptions);
      
      this.logger.log(`告警任务已添加: ${job.id}, 类型: ${data.alertType}, 级别: ${data.level}`);
      return job;
    } catch (error) {
      this.logger.error(`添加告警任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 添加OCR识别任务
   */
  async addOcrTask(
    data: OcrTaskData,
    options?: JobOptions,
  ): Promise<Job<OcrTaskData>> {
    try {
      const jobOptions: JobOptions = {
        priority: data.priority || TASK_PRIORITY.NORMAL,
        delay: data.delay || 0,
        attempts: data.attempts || 3,
        removeOnComplete: 100,
        removeOnFail: 50,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        ...options,
      };

      const job = await this.ocrQueue.add(data, jobOptions);
      
      this.logger.log(`OCR识别任务已添加: ${job.id}, 文件名: ${data.filename || '未知'}`);
      return job;
    } catch (error) {
      this.logger.error(`添加OCR识别任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 添加地址提取任务
   */
  async addAddressExtractionTask(
    data: AddressExtractionTaskData,
    options?: JobOptions,
  ): Promise<Job<AddressExtractionTaskData>> {
    try {
      const jobOptions: JobOptions = {
        priority: data.priority || TASK_PRIORITY.NORMAL,
        delay: data.delay || 0,
        attempts: data.attempts || 3,
        removeOnComplete: 100,
        removeOnFail: 50,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        ...options,
      };

      const job = await this.addressExtractionQueue.add(data, jobOptions);
      
      this.logger.log(`地址提取任务已添加: ${job.id}, 文本长度: ${data.text.length}`);
      return job;
    } catch (error) {
      this.logger.error(`添加地址提取任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 添加地理坐标逆解析任务
   */
  async addReverseGeocodingTask(
    data: ReverseGeocodingTaskData,
    options?: JobOptions,
  ): Promise<Job<ReverseGeocodingTaskData>> {
    try {
      const jobOptions: JobOptions = {
        priority: data.priority || TASK_PRIORITY.NORMAL,
        delay: data.delay || 0,
        attempts: data.attempts || 2,
        removeOnComplete: 100,
        removeOnFail: 50,
        backoff: {
          type: 'exponential',
          delay: 1000,
        },
        ...options,
      };

      const job = await this.reverseGeocodingQueue.add(data, jobOptions);
      
      this.logger.log(`地理坐标逆解析任务已添加: ${job.id}, 坐标数量: ${data.coordinates.length}`);
      return job;
    } catch (error) {
      this.logger.error(`添加地理坐标逆解析任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取任务状态
   */
  async getJobStatus(queueName: string, jobId: string): Promise<any> {
    try {
      const queue = this.getQueue(queueName);
      const job = await queue.getJob(jobId);
      
      if (!job) {
        return null;
      }

      const state = await job.getState();
      const progress = job.progress();
      
      return {
        id: job.id,
        name: job.name,
        data: job.data,
        state,
        progress,
        attemptsMade: job.attemptsMade,
        processedOn: job.processedOn,
        finishedOn: job.finishedOn,
        failedReason: job.failedReason,
        createdAt: new Date(job.timestamp),
      };
    } catch (error) {
      this.logger.error(`获取任务状态失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 取消任务
   */
  async cancelJob(queueName: string, jobId: string): Promise<boolean> {
    try {
      const queue = this.getQueue(queueName);
      const job = await queue.getJob(jobId);
      
      if (!job) {
        return false;
      }

      await job.remove();
      this.logger.log(`任务已取消: ${jobId}`);
      return true;
    } catch (error) {
      this.logger.error(`取消任务失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 重试任务
   */
  async retryJob(queueName: string, jobId: string): Promise<boolean> {
    try {
      const queue = this.getQueue(queueName);
      const job = await queue.getJob(jobId);
      
      if (!job) {
        return false;
      }

      await job.retry();
      this.logger.log(`任务已重试: ${jobId}`);
      return true;
    } catch (error) {
      this.logger.error(`重试任务失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取队列状态
   */
  async getQueueStatus(queueName: string): Promise<QueueStatus> {
    try {
      const queue = this.getQueue(queueName);
      
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        queue.getWaiting(),
        queue.getActive(),
        queue.getCompleted(),
        queue.getFailed(),
        queue.getDelayed(),
      ]);

      return {
        name: queueName,
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        paused: await queue.isPaused(),
      };
    } catch (error) {
      this.logger.error(`获取队列状态失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取队列统计
   */
  async getQueueStats(queueName: string): Promise<QueueStats> {
    try {
      const queue = this.getQueue(queueName);
      const completed = await queue.getCompleted();
      const failed = await queue.getFailed();
      
      const totalProcessed = completed.length;
      const totalFailed = failed.length;
      
      // 计算平均处理时间
      let avgProcessingTime = 0;
      if (completed.length > 0) {
        const totalTime = completed.reduce((sum, job) => {
          const finishedOn = job.finishedOn || 0;
          const processedOn = job.processedOn || 0;
          return sum + (finishedOn - processedOn);
        }, 0);
        avgProcessingTime = totalTime / completed.length;
      }

      // 计算吞吐量（每分钟处理的任务数）
      const now = Date.now();
      const oneMinuteAgo = now - 60 * 1000;
      const recentCompleted = completed.filter(job => (job.finishedOn || 0) > oneMinuteAgo);
      const throughput = recentCompleted.length;

      return {
        processed: totalProcessed,
        failed: totalFailed,
        avgProcessingTime,
        throughput,
      };
    } catch (error) {
      this.logger.error(`获取队列统计失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 暂停队列
   */
  async pauseQueue(queueName: string): Promise<void> {
    try {
      const queue = this.getQueue(queueName);
      await queue.pause();
      this.logger.log(`队列已暂停: ${queueName}`);
    } catch (error) {
      this.logger.error(`暂停队列失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 恢复队列
   */
  async resumeQueue(queueName: string): Promise<void> {
    try {
      const queue = this.getQueue(queueName);
      await queue.resume();
      this.logger.log(`队列已恢复: ${queueName}`);
    } catch (error) {
      this.logger.error(`恢复队列失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 清理队列
   */
  async cleanQueue(
    queueName: string,
    type: 'completed' | 'failed' | 'active' | 'waiting',
    age: number = 0,
  ): Promise<void> {
    try {
      const queue = this.getQueue(queueName);
      
      switch (type) {
        case 'completed':
          await queue.clean(age, 'completed');
          break;
        case 'failed':
          await queue.clean(age, 'failed');
          break;
        case 'active':
          await queue.clean(age, 'active');
          break;
        case 'waiting':
          await queue.clean(age, 'wait');
          break;
      }
      
      this.logger.log(`队列已清理: ${queueName}, 类型: ${type}`);
    } catch (error) {
      this.logger.error(`清理队列失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取队列
   */
  private getQueue(queueName: string): Queue {
    switch (queueName) {
      case QUEUE_NAMES.API_CALL:
        return this.apiCallQueue;
      case QUEUE_NAMES.ALERT:
        return this.alertQueue;
      case QUEUE_NAMES.OCR:
        return this.ocrQueue;
      case QUEUE_NAMES.EXTRACT_ADDRESS:
        return this.addressExtractionQueue;
      case QUEUE_NAMES.REV_GEO:
        return this.reverseGeocodingQueue;
      default:
        throw new Error(`未知队列: ${queueName}`);
    }
  }

  /**
   * 获取告警优先级
   */
  private getAlertPriority(level: string): number {
    switch (level) {
      case 'critical':
        return TASK_PRIORITY.CRITICAL;
      case 'error':
        return TASK_PRIORITY.HIGH;
      case 'warning':
        return TASK_PRIORITY.NORMAL;
      case 'info':
        return TASK_PRIORITY.LOW;
      default:
        return TASK_PRIORITY.NORMAL;
    }
  }

  /**
   * 获取所有队列状态
   */
  async getAllQueuesStatus(): Promise<QueueStatus[]> {
    const queueNames = Object.values(QUEUE_NAMES);
    const promises = queueNames.map(name => this.getQueueStatus(name));
    return Promise.all(promises);
  }

  async onModuleInit() {
    // 在模块初始化时调用清理队列方法
    try {
      this.logger.log('开始清理队列中的旧任务...');
      
      // 清理所有类型的任务
      const queueNames = [
        QUEUE_NAMES.API_CALL,
        QUEUE_NAMES.ALERT,
        QUEUE_NAMES.OCR,
        QUEUE_NAMES.EXTRACT_ADDRESS,
        QUEUE_NAMES.REV_GEO
      ] as string[];
      
      for (const queueName of queueNames) {
        await this.cleanQueue(queueName, 'completed', 0);
        await this.cleanQueue(queueName, 'failed', 0);
        await this.cleanQueue(queueName, 'waiting', 0);
        await this.cleanQueue(queueName, 'active', 0);
      }
      
      this.logger.log('队列清理完成');
    } catch (error) {
      this.logger.error(`清理队列失败: ${error.message}`, error.stack);
    }
  }
}