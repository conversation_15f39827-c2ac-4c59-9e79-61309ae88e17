#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
python-service 主应用（最简可运行实现）
- 地址解析：/address/extract、/address/normalize
- 申通面单OCR：/ocr/sto/upload、/ocr/sto/recognize
说明：
- 遵循 docs/模块需求与规格说明.md 与 docs/Python编码规范.md
- 不包含批量处理；地理编码端点预留在文档中，暂不实现
"""

import os
import re
import io
import time
import base64
import logging
import datetime
from typing import Dict, Optional, Tuple, List
from pathlib import Path

from flask import Flask, request, jsonify
from flask_cors import CORS

# 第三方依赖（带降级）
try:
    import cv2  # type: ignore
except Exception as e:  # pragma: no cover
    cv2 = None

try:
    from paddleocr import PaddleOCR  # type: ignore
except Exception as e:  # pragma: no cover
    PaddleOCR = None

try:
    from pyzbar import pyzbar  # type: ignore
except Exception:
    pyzbar = None

import numpy as np  # type: ignore
import jieba  # type: ignore
import jieba.posseg as pseg  # type: ignore
import pandas as pd  # type: ignore
import cpca  # type: ignore

# -------------------------
# 日志配置（Asia/Shanghai）
# -------------------------
class CNTimeFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        ct = datetime.datetime.fromtimestamp(
            record.created, datetime.timezone(datetime.timedelta(hours=8))
        )
        if datefmt:
            return ct.strftime(datefmt)
        return ct.strftime("%Y-%m-%d %H:%M:%S.%f")

logger = logging.getLogger("python-service")
logger.setLevel(logging.INFO)
_stream = logging.StreamHandler()
_stream.setFormatter(CNTimeFormatter("%(asctime)s [%(levelname)s] %(message)s"))
if not logger.handlers:
    logger.addHandler(_stream)

# -------------------------
# 统一响应与错误码
# -------------------------
OK = 0
INVALID_PARAM = 40001
MISSING_FIELD = 40002
PAYLOAD_TOO_LARGE = 41301
UNSUPPORTED_MEDIA = 41501
RATE_LIMITED = 42901
INTERNAL_ERROR = 50001
OCR_TIMEOUT = 40801


def resp_ok(data: dict, message: str = "OK"):
    return jsonify({"success": True, "code": OK, "message": message, "data": data})


def resp_err(code: int, message: str):
    return jsonify({"success": False, "code": code, "message": message, "data": None})


# -------------------------
# 配置
# -------------------------
TIMEOUT = int(os.environ.get("TIMEOUT", 300))
OCR_TIMEOUT_SEC = int(os.environ.get("OCR_TIMEOUT", 180))
DISABLE_PADDLE_DOWNLOAD = os.environ.get("DISABLE_PADDLE_DOWNLOAD", "false").lower() == "true"
IMAGE_MAX_SIZE_DEFAULT = int(os.environ.get("IMAGE_MAX_SIZE", 640))
IMAGE_QUALITY_DEFAULT = int(os.environ.get("IMAGE_QUALITY", 80))
IMAGE_ENHANCE_DEFAULT = os.environ.get("IMAGE_ENHANCE", "true").lower() == "true"

# -------------------------
# 地址解析实现（cpca + jieba + 规则）
# -------------------------
class AddressExtractor:
    def __init__(self) -> None:
        self.province_map: Dict[str, str] = {
            "北京": "北京市",
            "天津": "天津市",
            "上海": "上海市",
            "重庆": "重庆市",
            "河北": "河北省",
            "山西": "山西省",
            "辽宁": "辽宁省",
            "吉林": "吉林省",
            "黑龙江": "黑龙江省",
            "江苏": "江苏省",
            "浙江": "浙江省",
            "安徽": "安徽省",
            "福建": "福建省",
            "江西": "江西省",
            "山东": "山东省",
            "河南": "河南省",
            "湖北": "湖北省",
            "湖南": "湖南省",
            "广东": "广东省",
            "海南": "海南省",
            "四川": "四川省",
            "贵州": "贵州省",
            "云南": "云南省",
            "陕西": "陕西省",
            "甘肃": "甘肃省",
            "青海": "青海省",
            "台湾": "台湾省",
            "内蒙古": "内蒙古自治区",
            "广西": "广西壮族自治区",
            "西藏": "西藏自治区",
            "宁夏": "宁夏回族自治区",
            "新疆": "新疆维吾尔自治区",
            "香港": "香港特别行政区",
            "澳门": "澳门特别行政区",
        }
        self.keyword_role = ["寄件", "寄件人", "寄", "收件", "收件人", "收"]
        self._load_user_dict()

    def _load_user_dict(self) -> None:
        # 默认内置的常见地址词（小规模）
        words = [
            "建国门外大街",
            "陆家嘴环路",
            "天府大道",
            "解放南路",
            "科技园",
            "天府软件园",
            "国贸大厦",
            "上海中心大厦",
            "建外街道",
        ]
        for w in words:
            try:
                jieba.add_word(w)
            except Exception:
                pass
        # 从资源文件加载更多词条（若存在）
        # 优先环境变量 DICT_POI_PATH，其次默认路径 resources/addresses/poi_frequent.txt
        poi_path = os.environ.get("DICT_POI_PATH", "resources/addresses/poi_frequent.txt")
        path = Path(poi_path)
        if path.exists() and path.is_file():
            try:
                with path.open("r", encoding="utf-8") as f:
                    for line in f:
                        token = line.strip()
                        if token and len(token) >= 2:
                            try:
                                jieba.add_word(token)
                            except Exception:
                                continue
                logger.info(f"已加载自定义地址词典: {path}")
            except Exception as e:
                logger.warning(f"加载自定义地址词典失败 {path}: {e}")
        # 额外通过环境变量 DICT_EXTRA_WORDS=词1,词2,词3 注入
        extra = os.environ.get("DICT_EXTRA_WORDS")
        if extra:
            for token in [t.strip() for t in extra.split(",") if t.strip()]:
                try:
                    jieba.add_word(token)
                except Exception:
                    continue

    @staticmethod
    def _clean_text(text: str) -> str:
        text = text.replace("\r", "\n")
        text = re.sub(r"\s+", " ", text)
        text = text.strip()
        return text

    @staticmethod
    def _extract_phone(text: str) -> Optional[str]:
        m = re.search(r"1[3-9]\d{9}", text)
        return m.group(0) if m else None

    @staticmethod
    def _extract_name(text: str) -> Optional[str]:
        # 优先关键字后姓名
        for pat in [
            r"(?:收件人|寄件人|联系人)[：: \s]*([\u4e00-\u9fa5]{2,4})",
            r"([\u4e00-\u9fa5]{2,4})(?:先生|女士)",
        ]:
            m = re.search(pat, text)
            if m:
                return m.group(1)
        # 词性标注兜底
        try:
            for word, flag in pseg.cut(text):
                if flag == "nr" and 2 <= len(word) <= 4 and re.match(r"^[\u4e00-\u9fa5]+$", word):
                    return word
        except Exception:
            pass
        return None

    def _cpca_parse(self, text: str) -> Dict[str, Optional[str]]:
        res = {"province": None, "city": None, "district": None, "detail": None}
        try:
            df = cpca.transform([text])
            if not df.empty:
                row = df.iloc[0]
                res["province"] = row.get("省") or None
                res["city"] = row.get("市") or None
                res["district"] = row.get("区") or None
                res["detail"] = row.get("地址") or None
        except Exception as e:  # pragma: no cover
            logger.warning(f"cpca 解析失败: {e}")
        return res

    def _jieba_enhance(self, text: str, components: Dict[str, Optional[str]]) -> None:
        # 若cpca缺失字段，使用规则补齐
        if not components.get("province"):
            for short, full in self.province_map.items():
                if short in text or full in text:
                    components["province"] = full
                    break
        # 街道/社区
        if not components.get("street"):
            m = re.search(r"([\u4e00-\u9fa5\d]+(?:街道|路|街|大街|大道|巷|胡同|弄))", text)
            if m:
                components["street"] = m.group(1)
        if not components.get("community"):
            m = re.search(r"([\u4e00-\u9fa5\d]+(?:社区|乡|镇|村|园区|小区))", text)
            if m:
                components["community"] = m.group(1)
        # 详细地址
        if not components.get("detail"):
            m = re.search(
                r"([\u4e00-\u9fa5\d\w\-\s]+(?:号|栋|楼|层|室|座|幢|单元)[\u4e00-\u9fa5\d\w\-\s]*)",
                text,
            )
            if m:
                components["detail"] = m.group(1)

    def extract(self, text: str) -> Dict[str, Optional[str]]:
        text = self._clean_text(text)
        phone = self._extract_phone(text)
        name = self._extract_name(text)
        role = None
        if re.search(r"收件", text):
            role = "receiver"
        elif re.search(r"寄件", text):
            role = "sender"
        # 解析
        parts = self._cpca_parse(text)
        components: Dict[str, Optional[str]] = {
            "province": parts.get("province"),
            "city": parts.get("city"),
            "district": parts.get("district"),
            "street": None,
            "community": None,
            "detail": parts.get("detail"),
        }
        self._jieba_enhance(text, components)
        # 组装
        full_address = "".join(
            [
                components.get("province") or "",
                components.get("city") or "",
                components.get("district") or "",
                components.get("street") or "",
                components.get("community") or "",
                components.get("detail") or "",
            ]
        )
        confidence = 0.0
        confidence += 0.3 if components.get("province") and components.get("city") else 0
        confidence += 0.2 if components.get("district") else 0
        confidence += 0.15 if phone else 0
        confidence += 0.1 if name else 0
        has_detail = bool(components.get("street") or components.get("community") or components.get("detail"))
        confidence += 0.25 if has_detail else 0
        confidence = min(confidence, 1.0)
        return {
            "name": name,
            "phone": phone,
            "province": components.get("province"),
            "city": components.get("city"),
            "district": components.get("district"),
            "street": components.get("street"),
            "community": components.get("community"),
            "detail_address": components.get("detail"),
            "role": role or "unknown",
            "full_address": full_address,
            "confidence": round(confidence, 2),
            "extract_method": "cpca_jieba_rules",
        }

    def normalize(self, address: str) -> Dict[str, object]:
        res = self.extract(address)
        normalized = {
            "province": res.get("province"),
            "city": res.get("city"),
            "district": res.get("district"),
            "street": res.get("street"),
            "house_number": None,
            "poi": None,
            "full_address": res.get("full_address"),
        }
        return {"normalized": normalized, "confidence": res.get("confidence")}


address_extractor = AddressExtractor()

# -------------------------
# OCR: 图像预处理与面单字段抽取
# -------------------------

def _ensure_ocr() -> Optional["PaddleOCR"]:
    if PaddleOCR is None:
        return None
    try:
        # 单例初始化
        global _OCR_ENGINE
        if "_OCR_ENGINE" in globals() and _OCR_ENGINE is not None:
            return _OCR_ENGINE
        logger.info("初始化 PaddleOCR...")
        _OCR_ENGINE = PaddleOCR(
            use_textline_orientation=True,
            lang="ch",
            download_models=not DISABLE_PADDLE_DOWNLOAD,
            use_gpu=False,
            enable_mkldnn=True,
            cpu_threads=2,
            rec_batch_num=1,
            det_limit_side_len=480,
        )
        return _OCR_ENGINE
    except Exception as e:  # pragma: no cover
        logger.error(f"PaddleOCR 初始化失败: {e}")
        return None


def _decode_image_from_request() -> Tuple[Optional[np.ndarray], Optional[int], Optional[str]]:
    if cv2 is None:
        return None, None, "OpenCV not available"
    if "images" in request.files:
        file = request.files["images"]
        img_bytes = file.read()
    elif request.is_json and request.json and "image_base64" in request.json:
        try:
            img_bytes = base64.b64decode(request.json["image_base64"])
        except Exception as e:
            return None, None, f"Invalid base64: {e}"
    else:
        return None, None, "No image provided"
    nparr = np.frombuffer(img_bytes, np.uint8)
    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    if img is None:
        return None, None, "Invalid image data"
    return img, len(img_bytes), None


def _resize_with_long_side(img: np.ndarray, max_side: int) -> np.ndarray:
    h, w = img.shape[:2]
    long_side = max(h, w)
    if long_side <= max_side:
        return img
    scale = max_side / float(long_side)
    new_w, new_h = int(w * scale), int(h * scale)
    return cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_AREA)


def _enhance_image(img: np.ndarray) -> np.ndarray:
    # 简单增强：转灰 + 对比度受限自适应直方图均衡（CLAHE）
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    eq = clahe.apply(gray)
    return cv2.cvtColor(eq, cv2.COLOR_GRAY2BGR)


def _ocr_infer(img: np.ndarray) -> List[Tuple[List[List[int]], Tuple[str, float]]]:
    engine = _ensure_ocr()
    if engine is None:
        raise RuntimeError("OCR engine not initialized")
    result = engine.ocr(img)
    return result[0] or []


def _extract_sto_fields(text_lines: List[Tuple[str, float]]) -> Dict[str, object]:
    # 原始文本拼接，便于正则
    joined = "\n".join([t for t, conf in text_lines])
    # 面单号（条码优先，此处先用正则，条码单独解码）
    waybill_no = None
    m = re.search(r"\b(\d{12})\b", joined)
    if m:
        waybill_no = m.group(1)
    # 三段/四段码
    route_code = None
    m = re.search(r"\b\d{2,3}-\d{1,3}-\d{1,3}(?:-\d{1,3})?\b", joined)
    if m:
        route_code = m.group(0)
    # 电话
    phones = re.findall(r"1[3-9]\d{9}", joined)
    # 关键词定位段
    sender_block = None
    receiver_block = None
    for line, _c in text_lines:
        if re.search(r"寄件人|寄件|寄", line) and sender_block is None:
            sender_block = line
        if re.search(r"收件人|收件|收", line) and receiver_block is None:
            receiver_block = line
    # 姓名与地址（调用地址解析器）
    sender = {}
    receiver = {}
    if sender_block:
        ext = address_extractor.extract(sender_block)
        sender = {"name": ext.get("name"), "phone": ext.get("phone"), "address": ext.get("full_address")}
    if receiver_block:
        ext = address_extractor.extract(receiver_block)
        receiver = {"name": ext.get("name"), "phone": ext.get("phone"), "address": ext.get("full_address")}
    # 若块中缺失，尝试从全文回填电话
    if not sender.get("phone") and phones:
        sender["phone"] = phones[0]
    if not receiver.get("phone") and len(phones) > 1:
        receiver["phone"] = phones[1]
    return {
        "waybill_no": waybill_no,
        "sender": sender or None,
        "receiver": receiver or None,
        "route_code": route_code,
        "branch": None,  # 可根据 route_code 与词典映射
    }


def _decode_barcodes(img: np.ndarray) -> List[str]:
    if pyzbar is None:
        return []
    barcodes = pyzbar.decode(img)
    texts: List[str] = []
    for b in barcodes:
        try:
            data = b.data.decode("utf-8", errors="ignore")
            texts.append(data)
        except Exception:
            continue
    return texts


# -------------------------
# Flask 应用与端点
# -------------------------
app = Flask(__name__)
CORS(app)


@app.route("/health", methods=["GET"])  # 简易健康检查
def health():
    return resp_ok({"service": "python-service", "time": time.time()})


@app.route("/address/extract", methods=["POST"])  # 不支持批量
def address_extract():
    if not request.is_json:
        return resp_err(UNSUPPORTED_MEDIA, "Content-Type must be application/json"), 415
    payload = request.get_json(silent=True) or {}
    text = payload.get("text")
    if not text or not isinstance(text, str):
        return resp_err(MISSING_FIELD, "text is required"), 400
    result = address_extractor.extract(text)
    return resp_ok(result)


@app.route("/address/normalize", methods=["POST"])  # 标准化
def address_normalize():
    if not request.is_json:
        return resp_err(UNSUPPORTED_MEDIA, "Content-Type must be application/json"), 415
    payload = request.get_json(silent=True) or {}
    address = payload.get("address")
    if not address or not isinstance(address, str):
        return resp_err(MISSING_FIELD, "address is required"), 400
    result = address_extractor.normalize(address)
    return resp_ok(result)


def _handle_ocr_sto_common() -> Tuple[Optional[dict], int]:
    max_size = int(request.args.get("max_size", IMAGE_MAX_SIZE_DEFAULT))
    quality = int(request.args.get("quality", IMAGE_QUALITY_DEFAULT))
    enhance = str(request.args.get("enhance", str(IMAGE_ENHANCE_DEFAULT))).lower() == "true"
    img, original_size, err = _decode_image_from_request()
    if err:
        return resp_err(INVALID_PARAM, err), 400
    assert img is not None and original_size is not None
    # 预处理
    img_proc = _resize_with_long_side(img, max_size)
    if enhance:
        img_proc = _enhance_image(img_proc)
    # OCR 推理
    start = time.time()
    try:
        lines = _ocr_infer(img_proc)
    except RuntimeError as e:
        return resp_err(INTERNAL_ERROR, str(e)), 500
    process_time = time.time() - start
    # 结构化文本
    text_lines = [(t[1][0], float(t[1][1])) for t in lines]
    # 条码解码（用原图尝试）
    barcodes = _decode_barcodes(img)
    # 字段抽取
    shipment = _extract_sto_fields(text_lines)
    # 若条码文本里出现 12 位数字，优先作为面单号
    for b in barcodes:
        m = re.search(r"\b(\d{12})\b", b)
        if m:
            shipment["waybill_no"] = m.group(1)
            break
    data = {
        "results": [[[int(p[0][0]), int(p[0][1]), int(p[2][0]), int(p[2][1])], t, c] for p, (t, c) in zip([l[0] for l in lines], text_lines)] if lines else [],
        "shipment": shipment,
        "process_time": round(process_time, 3),
        "image_info": {"original_size": original_size, "processed_size": list(img_proc.shape)},
        "barcodes": barcodes,
    }
    return resp_ok(data), 200


@app.route("/ocr/sto/upload", methods=["POST"])  # 文件上传
def ocr_sto_upload():
    return _handle_ocr_sto_common()


@app.route("/ocr/sto/recognize", methods=["POST"])  # base64
def ocr_sto_recognize():
    return _handle_ocr_sto_common()


if __name__ == "__main__":
    port = int(os.environ.get("PORT", 8866))
    logger.info(f"Starting python-service on 0.0.0.0:{port}")
    app.run(host="0.0.0.0", port=port, debug=False) 