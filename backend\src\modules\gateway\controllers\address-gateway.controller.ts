import {
  Controller,
  Post,
  Body,
  Req,
  UseGuards,
  UseInterceptors,
  Logger,
} from '@nestjs/common';
import { Request } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { UseAuthStrategy, AuthStrategy } from '@/common/decorators/auth-strategy.decorator';
import { GatewayService } from '../services/gateway.service';
import { 
  AddressExtractionRequestDto,
  BatchRequestDto,
} from '../dto/gateway-request.dto';
import {
  SyncResponseDto,
  AsyncResponseDto,
  ProxyAsyncSuccessResponseDto,
  AddressExtractionResponseDto,
  ErrorResponseDto,
} from '../dto/gateway-response.dto';
import { IGatewayRequest } from '../interfaces/gateway.interfaces';
import { ProcessingMode } from '../config/gateway.constants';
import { getClientIp } from '@/common/utils/ip.utils';

/**
 * 地址网关控制器
 * 负责处理地址提取相关的网关请求
 * 遵循控制器职责，专注于HTTP请求处理和参数验证
 */
@ApiTags('地址网关')
@Controller('address')
@UseAuthStrategy(AuthStrategy.API_KEY)
@ApiBearerAuth()
export class AddressGatewayController {
  private readonly logger = new Logger(AddressGatewayController.name);

  constructor(private readonly gatewayService: GatewayService) {}

  /**
   * 地址提取
   */
  @Post('extract')
  @ApiOperation({ summary: '从文本中提取地址信息' })
  @ApiResponse({ 
    status: 200, 
    description: '同步处理成功',
    type: SyncResponseDto,
  })
  @ApiResponse({ 
    status: 202, 
    description: '异步处理已接受',
    type: AsyncResponseDto,
  })
  @ApiResponse({ 
    status: 400, 
    description: '请求参数错误',
    type: ErrorResponseDto,
  })
  async extractAddress(
    @Body() body: AddressExtractionRequestDto,
    @Req() req: Request
  ) {
    this.logger.log(`地址提取请求: ${body.text?.substring(0, 50)}...`);

    // 从请求体中获取serviceId
    const serviceId = body.serviceId;

    if (serviceId) {
      this.logger.debug(`使用前端传递的服务ID: ${serviceId}`);
    } else {
      this.logger.warn('前端未传递服务ID，将使用路径映射（不推荐）');
    }

    const gatewayRequest: IGatewayRequest = {
      path: '/v1/op/address/extract',
      method: 'POST',
      headers: req.headers as Record<string, string>,
      body: {
        text: body.text,
        options: body.options,
      },
      mode: body.mode,
      serviceId, // 传递服务ID
      user: (req as any).user ? {
        id: (req as any).user.id,
        username: (req as any).user.username,
        email: (req as any).user.email,
      } : undefined,
      clientInfo: {
        ip: getClientIp(req),
        userAgent: req.headers['user-agent'],
        referer: req.headers['referer'],
        apiKey: req.headers['x-api-key'] as string,
        userId: (req as any).user?.id,
      },
    };

    const response = await this.gatewayService.handleRequest(gatewayRequest);
    console.log('response-handleRequest:>>>>>', response)
    // 直接返回GatewayService的响应，避免重复包装
    return response;
  }

  /**
   * 批量地址提取
   */
  @Post('extract/batch')
  @ApiOperation({ summary: '批量地址提取' })
  @ApiResponse({ 
    status: 202, 
    description: '批量处理已接受',
    type: AsyncResponseDto,
  })
  @ApiResponse({ 
    status: 400, 
    description: '请求参数错误',
    type: ErrorResponseDto,
  })
  async batchExtractAddress(
    @Body() body: BatchRequestDto,
    @Req() req: Request
  ) {
    this.logger.log(`批量地址提取请求: ${body.items?.length || 0} 个项目`);

    // 批量请求强制使用异步模式
    const gatewayRequest: IGatewayRequest = {
      path: '/v1/op/address/extract/batch',
      method: 'POST',
      headers: req.headers as Record<string, string>,
      body: {
        items: body.items,
        batchOptions: body.batchOptions,
      },
      mode: ProcessingMode.ASYNC, // 强制异步
      user: (req as any).user ? {
        id: (req as any).user.id,
        username: (req as any).user.username,
        email: (req as any).user.email,
      } : undefined,
      clientInfo: {
        ip: getClientIp(req),
        userAgent: req.headers['user-agent'],
        referer: req.headers['referer'],
        apiKey: req.headers['x-api-key'] as string,
        userId: (req as any).user?.id,
      },
    };

    const response = await this.gatewayService.handleRequest(gatewayRequest);
    
    return {
      ...response,
      statusUrl: `/v1/op/tasks/${response.taskId}`,
      eventsUrl: `/v1/op/tasks/${response.taskId}/events`,
    };
  }

  /**
   * 地址标准化
   */
  @Post('normalize')
  @ApiOperation({ summary: '地址标准化' })
  @ApiResponse({
    status: 200,
    description: '服务开放中',
    type: SyncResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
    type: ErrorResponseDto,
  })
  async normalizeAddress(
    @Body() body: { address?: string; options?: any },
    @Req() req: Request
  ) {
    this.logger.log(`地址标准化请求: ${body?.address || '未提供地址'}`);

    // 返回开发中提示，不进行实际业务处理
    return {
      success: false,
      code: 503,
      message: '地址标准化服务正在开发中，敬请期待',
      data: {
        status: 'coming_soon',
        feature: 'address_normalize',
        description: '地址标准化功能正在开发中，将支持地址格式规范化、补全和验证',
        expectedLaunch: '2025年第一季度',
        alternatives: [
          {
            service: 'address_extract',
            description: '可使用地址提取服务获取结构化地址信息',
            endpoint: '/v1/op/address/extract'
          },
          {
            service: 'geo_reverse',
            description: '可使用逆地理编码服务获取标准地址',
            endpoint: '/v1/op/geo/reverse'
          }
        ]
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id'] || 'unknown'
    };
  }

  /**
   * 地址验证
   */
  @Post('validate')
  @ApiOperation({ summary: '地址验证' })
  @ApiResponse({ 
    status: 200, 
    description: '验证结果',
    type: SyncResponseDto,
  })
  @ApiResponse({ 
    status: 400, 
    description: '请求参数错误',
    type: ErrorResponseDto,
  })
  async validateAddress(
    @Body() body: { address: string; strict?: boolean },
    @Req() req: Request
  ) {
    this.logger.log(`地址验证请求: ${body.address}`);

    const gatewayRequest: IGatewayRequest = {
      path: '/v1/op/address/validate',
      method: 'POST',
      headers: req.headers as Record<string, string>,
      body: {
        address: body.address,
        strict: body.strict || false,
      },
      mode: ProcessingMode.SYNC,
      user: (req as any).user ? {
        id: (req as any).user.id,
        username: (req as any).user.username,
        email: (req as any).user.email,
      } : undefined,
      clientInfo: {
        ip: getClientIp(req),
        userAgent: req.headers['user-agent'],
        referer: req.headers['referer'],
        apiKey: req.headers['x-api-key'] as string,
        userId: (req as any).user?.id,
      },
    };

    const response = await this.gatewayService.handleRequest(gatewayRequest);
    
    return {
      ...response,
      isValid: response.data?.isValid || false,
      confidence: response.data?.confidence || 0,
      suggestions: response.data?.suggestions || [],
      standardized: this.formatAddressResponse(response.data?.standardized),
    };
  }

  /**
   * 格式化地址响应数据
   */
  private formatAddressResponse(data: any): AddressExtractionResponseDto {
    if (!data) {
      return {};
    }

    // 如果数据已经是标准格式，直接返回
    if (data.name !== undefined || data.phone !== undefined || data.address !== undefined) {
      return data;
    }

    // 尝试从不同的数据结构中提取信息
    const result: AddressExtractionResponseDto = {};

    // 提取姓名
    if (data.name || data.recipient_name || data.contact_name) {
      result.name = data.name || data.recipient_name || data.contact_name;
    }

    // 提取电话
    if (data.phone || data.mobile || data.telephone || data.contact_phone) {
      result.phone = data.phone || data.mobile || data.telephone || data.contact_phone;
    }

    // 提取地址信息
    if (data.address || data.full_address) {
      const addressStr = data.address || data.full_address;
      result.address = {
        full: addressStr,
        province: data.province,
        city: data.city,
        district: data.district || data.county,
        street: data.street,
        detail: data.detail || data.address_detail,
      };
    } else if (data.province || data.city || data.district) {
      result.address = {
        province: data.province,
        city: data.city,
        district: data.district || data.county,
        street: data.street,
        detail: data.detail || data.address_detail,
        full: [data.province, data.city, data.district, data.street, data.detail]
          .filter(Boolean)
          .join(''),
      };
    }

    // 提取邮政编码
    if (data.postalCode || data.postal_code || data.zipcode) {
      result.postalCode = data.postalCode || data.postal_code || data.zipcode;
    }

    return result;
  }

  /**
   * 获取地址处理能力信息
   */
  @Post('capabilities')
  @ApiOperation({ summary: '获取地址处理能力信息' })
  @ApiResponse({ 
    status: 200, 
    description: '处理能力信息',
  })
  async getAddressCapabilities() {
    this.logger.log('获取地址处理能力信息');

    return {
      success: true,
      code: 200,
      data: {
        supportedOperations: ['extract', 'normalize', 'validate'],
        maxTextLength: 10000,
        processingModes: [
          {
            mode: ProcessingMode.SYNC,
            description: '同步处理，适合单个地址快速提取',
            maxTextLength: 1000,
            avgResponseTime: '1-3秒',
          },
          {
            mode: ProcessingMode.ASYNC,
            description: '异步处理，适合长文本或批量处理',
            maxTextLength: 10000,
            avgResponseTime: '3-10秒',
          },
          {
            mode: ProcessingMode.PROXY_ASYNC,
            description: '代理异步，网关等待结果后返回',
            maxTextLength: 5000,
            avgResponseTime: '3-30秒',
            maxWaitTime: '30秒',
          },
        ],
        extractionFeatures: [
          '姓名提取',
          '电话号码提取',
          '地址结构化解析',
          '省市区识别',
          '街道门牌号提取',
          '邮政编码识别',
        ],
        supportedRegions: [
          '中国大陆',
          '港澳台地区',
        ],
        addressFormats: [
          '标准行政区划格式',
          '详细地址格式',
          '简化地址格式',
        ],
      },
    };
  }
}
