import { Module, NestModule, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { QUEUE_CONFIG } from './config/queue.config';
import { SharedModule } from '../../shared/shared.module';
import { RedisPubSubService } from './services/pub-sub.service';
import { TaskService } from './services/task.service';
import { QueueManagerService } from './services/queue-manager.service';
import { BullBoardService } from './services/bull-board.service';
import { OcrProcessor } from './processors/ocr.processor';
import { StoOcrProcessor } from './processors/sto-ocr.processor';
import { AddressProcessor } from './processors/address.processor';
import { GeoProcessor } from './processors/geo.processor';
import { TaskEventsController } from './controllers/task-events.controller';
import { QueueAdminController } from './controllers/queue-admin.controller';
import { QueueHealthController } from './controllers/queue-health.controller';
import { BullBoardAuthMiddleware } from './middlewares/bull-board.middleware';

/**
 * 队列模块
 * 统一管理队列相关的组件
 */
@Module({
  imports: [
    // 导入共享模块以使用共享配置
    SharedModule,
    
    // 使用共享模块配置的Redis连接
    BullModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        // 使用与共享模块相同的配置逻辑
        const redisUrl = configService.get('REDIS_URL');
        let redisConfig;
        
        if (redisUrl) {
          redisConfig = {
            url: redisUrl,
          };
        } else {
          const password = configService.get('redis.password');
          redisConfig = {
            host: configService.get('redis.host', 'localhost'),
            port: configService.get('redis.port', 6379),
            password: password || undefined, // 如果密码为空字符串，设置为undefined
            db: configService.get('redis.db', 0),
          };
        }
        
        return {
          redis: redisConfig,
          defaultJobOptions: {
            removeOnComplete: true,
            removeOnFail: false,
          },
        };
      },
    }),
    
    // 注册OCR队列
    BullModule.registerQueue({
      name: QUEUE_CONFIG.queues.OCR_QUEUE.name,
      defaultJobOptions: QUEUE_CONFIG.queues.OCR_QUEUE.defaultJobOptions,
      limiter: {
        max: 50,
        duration: 1000 * 60, // 每分钟最多处理50个任务
      },
    }),
    // 注册申通OCR队列
    BullModule.registerQueue({
      name: QUEUE_CONFIG.queues.STO_OCR_QUEUE.name,
      defaultJobOptions: QUEUE_CONFIG.queues.STO_OCR_QUEUE.defaultJobOptions,
      limiter: {
        max: 20,
        duration: 1000 * 60, // 每分钟最多处理20个任务（3秒一次）
      },
    }),
    // 注册地址解析队列
    BullModule.registerQueue({
      name: QUEUE_CONFIG.queues.ADDRESS_QUEUE.name,
      defaultJobOptions: QUEUE_CONFIG.queues.ADDRESS_QUEUE.defaultJobOptions,
      limiter: {
        max: 100,
        duration: 1000 * 60, // 每分钟最多处理100个任务
      },
    }),
    // 注册地理坐标处理队列
    BullModule.registerQueue({
      name: QUEUE_CONFIG.queues.GEO_QUEUE.name,
      defaultJobOptions: QUEUE_CONFIG.queues.GEO_QUEUE.defaultJobOptions,
      limiter: {
        max: 120,
        duration: 1000 * 60, // 每分钟最多处理120个任务
      },
    }),
  ],
  controllers: [
    TaskEventsController,
    QueueAdminController,
    QueueHealthController,
  ],
  providers: [
    // 注册服务
    RedisPubSubService,
    TaskService,
    QueueManagerService,
    BullBoardService,
    
    // 注册处理器
    OcrProcessor,
    StoOcrProcessor,
    AddressProcessor,
    GeoProcessor,
  ],
  exports: [
    // 导出可供其他模块使用的服务
    TaskService,
    QueueManagerService,
    BullBoardService,
  ],
})
export class QueueModule implements NestModule {
  constructor(private readonly bullBoardService: BullBoardService) {}
  
  /**
   * 配置中间件
   */
  configure(consumer: MiddlewareConsumer): void {
    // 添加Bull面板认证中间件
    consumer
      .apply(BullBoardAuthMiddleware)
      .forRoutes({
        path: `${this.bullBoardService.getBasePath()}/*path`,
        method: RequestMethod.ALL,
      });

    // 注册Bull面板路由
    const app = consumer.apply(this.bullBoardService.getRouter()).forRoutes({
      path: `${this.bullBoardService.getBasePath()}/*path`,
      method: RequestMethod.ALL,
    });
  }
} 