# 队列模块使用指南

## 概述

队列模块是一个基于 **Bull 队列** 的异步任务处理系统，提供了强大的任务调度和异步处理能力。

## 架构设计

### 核心组件

1. **Bull 队列系统**
   - 基于 Redis 的任务队列
   - 支持任务优先级、延迟执行、重试机制
   - 适用于异步任务处理

### 职责分工

#### Bull 队列职责
- 异步任务处理（api调用、调用次数预警等）
- 定时任务调度
- 任务重试和失败处理
- 任务进度跟踪
- 批量数据处理
- 文件处理任务

### 核心服务

1. **QueueManagerService** - 队列管理器
   - 统一的任务调度入口
   - 管理 Bull 队列服务
   - 提供任务管理 API

2. **BullQueueService** - Bull 队列服务
   - 处理异步任务
   - 管理任务生命周期
   - 提供任务监控

## 目录结构

```
src/modules/queue/
├── config/
│   └── bull.config.ts          # Bull 队列配置
├── dto/
│   └── task.dto.ts             # 任务相关 DTO
├── interfaces/
│   └── queue.interface.ts      # 队列接口定义
├── processors/
│   ├── api.processor.ts      # api调用处理器
│   ├── alert.processor.ts      # 告警处理器
├── services/
│   ├── bull-queue.service.ts   # Bull 队列服务
│   └── queue-manager.service.ts # 队列管理服务
├── queue.constants.ts          # 队列常量
├── queue.module.ts            # 队列模块
└── QUEUE_USAGE_GUIDE.md       # 使用指南
```

## 使用方法


### 2. 系统告警任务

```typescript
// 添加告警任务
await this.queueManager.addAlertTask({
  userId: 1,
  type: 'system_error',
  level: 'error',
  title: '系统异常',
  message: '数据库连接失败',
});
```


### 7. 任务管理

```typescript
// 取消任务
await this.queueManager.cancelTask('job-id');

// 重试任务
await this.queueManager.retryTask('job-id');

// 获取任务状态
const status = await this.queueManager.getTaskStatus('job-id');

// 获取队列统计
const stats = await this.queueManager.getQueueStats('email');

// 获取系统健康状态
const health = await this.queueManager.getSystemHealth();

// 暂停队列
await this.queueManager.pauseQueue('email');

// 恢复队列
await this.queueManager.resumeQueue('email');

// 清理队列
await this.queueManager.cleanQueue('email', 'completed');
```

### 8. 事件监听

```typescript
// 监听任务完成事件
this.queueManager.onTaskCompleted((data) => {
  console.log('任务完成:', data);
});

// 监听任务失败事件
this.queueManager.onTaskFailed((data) => {
  console.log('任务失败:', data);
});

// 监听任务进度事件
this.queueManager.onTaskProgress((data) => {
  console.log('任务进度:', data);
});
```





## 配置说明

### Bull 队列配置

```typescript
// config/bull.config.ts
export const bullConfig = {
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD,
  },
  defaultJobOptions: {
    removeOnComplete: 10,
    removeOnFail: 5,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
  },
};
```

## 最佳实践

### 1. 任务设计

- **幂等性**：确保任务可以安全重试
- **小任务**：将大任务拆分为小任务
- **超时设置**：为长时间运行的任务设置合理的超时时间

### 2. 性能优化

- **批量处理**: 对于大量相似任务，使用批量处理
- **优先级设置**: 为重要任务设置高优先级
- **资源控制**: 合理设置并发数和内存使用
- **监控告警**: 设置队列长度和处理时间告警

### 3. 错误处理

- **分类处理**: 区分临时错误和永久错误
- **重试策略**: 使用指数退避算法
- **死信队列**: 处理最终失败的任务
- **告警机制**: 及时通知关键任务失败

### 4. 监控和运维

- **健康检查**: 定期检查队列状态
- **性能指标**: 监控任务处理速度和成功率
- **容量规划**: 根据业务增长调整资源配置
- **备份恢复**: 确保任务数据的可靠性



## 故障排查

### 常见问题

1. **Redis 连接失败**
   - 检查 Redis 服务状态
   - 验证连接配置
   - 检查网络连通性

2. **任务处理缓慢**
   - 检查处理器性能
   - 调整并发数设置
   - 优化任务逻辑

3. **内存使用过高**
   - 检查任务数据大小
   - 调整队列清理策略
   - 优化数据结构

### 调试技巧

- 启用详细日志记录
- 使用 Bull Dashboard 监控队列状态
- 分析任务执行时间分布
- 检查错误日志和堆栈跟踪

## 扩展开发

### 添加新的任务类型

1. 创建任务 DTO
2. 实现任务处理器
3. 在队列管理器中添加任务方法
4. 注册处理器到模块

## 版本更新

### v3.0.0 更新内容

- 简化架构，专注于 Bull 队列
- 优化任务处理性能
- 增强日志记录和错误处理
- 简化配置和部署