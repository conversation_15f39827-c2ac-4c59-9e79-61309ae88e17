// 系统状态类型
export type SystemStatus = 'healthy' | 'warning' | 'critical' | 'unknown'

// 服务状态类型
export type ServiceStatus = 'running' | 'stopped' | 'error' | 'maintenance' | 'healthy' | 'warning'

// 告警级别
export type AlertLevel = 'info' | 'warning' | 'error' | 'critical'

// 告警状态
export type AlertStatus = 'active' | 'acknowledged' | 'resolved'

// 指标类型
export type MetricType = 'system' | 'api' | 'database' | 'cache'

// 系统状态概览
export interface SystemStatusOverview {
  overall: SystemStatus
  lastUpdate: Date
  activeServices: number
  totalServices: number
  apiCalls: number
  alerts: number
}

// 系统指标
export interface SystemMetrics {
  cpu: number // CPU使用率 (0-100)
  memory: number // 内存使用率 (0-100)
  disk: number // 磁盘使用率 (0-100)
  loadAverage: number // 系统负载
  memoryUsed: number // 已使用内存 (bytes)
  memoryTotal: number // 总内存 (bytes)
  diskFree: number // 可用磁盘空间 (bytes)
  networkIn: number // 网络入流量 (bytes/s)
  networkOut: number // 网络出流量 (bytes/s)
}

// API指标
export interface ApiMetrics {
  responseTime: number // 平均响应时间 (ms)
  successRate: number // 成功率 (0-100)
  qps: number // 每秒查询数
  errorRate: number // 错误率 (0-100)
  totalRequests: number // 总请求数
  errorRequests: number // 错误请求数
}

// 数据库指标
export interface DatabaseMetrics {
  connections: number // 连接数
  queryTime: number // 查询响应时间 (ms)
  lockWaits: number // 锁等待数
  activeConnections: number // 活跃连接数
  maxConnections: number // 最大连接数
}

// 缓存指标
export interface CacheMetrics {
  connections: number // 连接数
  memoryUsage: number // 内存使用 (bytes)
  hitRate: number // 命中率 (0-100)
  opsPerSec: number // 每秒操作数
  evictions: number // 驱逐数
}

// 性能指标集合
export interface PerformanceMetrics {
  system: SystemMetrics
  api: ApiMetrics
  database: DatabaseMetrics
  cache: CacheMetrics
}

// 服务信息
export interface ServiceInfo {
  id: string
  name: string
  version: string
  status: ServiceStatus
  responseTime: number // 响应时间 (ms)
  cpu: number // CPU使用率 (0-100)
  memory: number // 内存使用率 (0-100)
  uptime: number // 运行时间 (seconds)
  lastCheck: Date // 最后检查时间
  endpoint?: string // 服务端点
  description?: string // 服务描述
}

// 告警信息
export interface AlertInfo {
  id: string
  title: string
  message: string
  level: AlertLevel
  status: AlertStatus
  source: string // 告警来源
  timestamp: Date // 告警时间
  acknowledgedAt?: Date // 确认时间
  resolvedAt?: Date // 解决时间
  acknowledgedBy?: string // 确认人
  resolvedBy?: string // 解决人
  metadata?: Record<string, any> // 额外元数据
}

// 告警信息别名（用于API兼容）
export type Alert = AlertInfo

// 告警历史记录
export interface AlertHistory {
  id?: string
  title?: string
  message?: string
  level?: AlertLevel
  status?: AlertStatus
  source?: string
  triggeredAt?: Date
  acknowledgedAt?: Date
  timestamp?: Date
  resolvedAt?: Date
  resolved?: boolean
  acknowledgedBy?: string
  resolvedBy?: string
  duration?: number // 持续时间（毫秒）
  metadata?: Record<string, any>
}

// 监控面板数据
export interface MonitoringDashboard {
  systemStatus: SystemStatusOverview
  metrics: PerformanceMetrics
  services: ServiceInfo[]
  alerts: AlertInfo[]
  lastUpdated: Date
}

// 历史指标数据点
export interface MetricDataPoint {
  timestamp: Date
  value: number
  label?: string
}

// 历史指标数据
export interface HistoricalMetrics {
  metric: string
  timeRange: string
  dataPoints: MetricDataPoint[]
  unit?: string
  description?: string
}

// 监控查询参数
export interface MonitoringQueryParams {
  timeRange?: string
  metric?: string
  service?: string
  level?: AlertLevel
  status?: AlertStatus
  startTime?: Date
  endTime?: Date
}

// 健康检查结果
export interface HealthCheckResult {
  name?: string
  description?: string
  lastCheck?: Date
  service?: string
  status?: ServiceStatus
  responseTime?: number
  timestamp?: Date
  details?: Record<string, any>
  error?: string
}

// 健康检查别名（用于API兼容）
export type HealthCheck = HealthCheckResult

// 历史指标数据别名（用于API兼容）
export type MetricsHistory = HistoricalMetrics

// 监控配置
export interface MonitoringConfig {
  refreshInterval: number // 刷新间隔 (ms)
  alertThresholds: {
    cpu: number
    memory: number
    disk: number
    responseTime: number
    errorRate: number
  }
  enabledMetrics: MetricType[]
  autoRefresh: boolean
}