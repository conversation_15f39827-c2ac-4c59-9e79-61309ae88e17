# 网关模块异步接口调用使用指南

## 概述

本文档详细介绍如何使用网关模块提供的异步API接口，包括物流面单OCR识别、地址提取和坐标逆解析三大核心功能。异步调用模式适用于批量处理、长耗时任务，可以有效避免API调用超时，提高系统整体吞吐量。

## 目录

- [通用说明](#通用说明)
- [异步调用流程](#异步调用流程)
- [物流面单OCR识别](#物流面单OCR识别)
- [地址提取](#地址提取)
- [坐标逆解析](#坐标逆解析)
- [任务状态查询](#任务状态查询)
- [SSE实时状态推送](#SSE实时状态推送)
- [取消任务](#取消任务)
- [常见问题](#常见问题)

## 通用说明

### 异步调用与同步调用的区别

| 特性 | 异步调用 | 同步调用 |
|------|---------|---------|
| 响应时间 | 立即返回任务ID | 等待处理完成 |
| 适用场景 | 批量处理、长耗时任务 | 单次查询、实时性要求高 |
| 结果获取 | 通过任务ID查询或SSE推送 | 直接在响应中返回 |
| 超时风险 | 低 | 高 |
| 并发处理 | 支持高并发 | 受系统资源限制 |

### 认证方式

所有接口调用需要使用API密钥进行认证，支持以下两种方式：

1. **请求头认证**：在HTTP请求头中添加`X-API-KEY`字段
2. **查询参数认证**：在URL查询参数中添加`apiKey`参数

## 异步调用流程

异步API调用的完整流程如下：

1. 发起异步API请求，在请求参数中设置`mode=async`
2. 服务器立即返回包含`jobId`的响应
3. 客户端使用`jobId`通过以下方式获取结果：
   - 主动轮询任务状态API
   - 订阅SSE实时推送
   - 配置回调URL（如果API支持）
4. 处理最终结果或错误信息

### 异步响应格式

所有异步调用的初始响应格式统一如下：

```json
{
  "success": true,
  "jobId": "job_xxxxxxxxxx",
  "status": "queued",
  "statusUrl": "/v1/op/api/tasks/job_xxxxxxxxxx",
  "message": "任务已提交到队列，请通过statusUrl查询结果",
  "requestId": "req_xxxxxxxxxx",
  "responseTime": 120
}
```

## 物流面单OCR识别

物流面单OCR识别支持两种调用方式：文件上传和Base64编码图片。

### 文件上传方式

**接口地址**：`POST /api/ocr/upload`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|------|-----|
| file | File | 是 | 要识别的图片文件 |
| mode | String | 否 | 处理模式，固定值`async` |
| callbackUrl | String | 否 | 处理完成后的回调URL |

**请求示例**：

```bash
curl -X POST "https://api.example.com/api/ocr/upload" \
  -H "X-API-KEY: your_api_key" \
  -F "file=@/path/to/logistics_label.jpg" \
  -F "mode=async"
```

### Base64方式

**接口地址**：`POST /api/ocr/base64`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|------|-----|
| image_base64 | String | 是 | Base64编码的图片数据 |
| filename | String | 否 | 文件名（含扩展名） |
| mode | String | 否 | 处理模式，固定值`async` |
| callbackUrl | String | 否 | 处理完成后的回调URL |

**请求示例**：

```bash
curl -X POST "https://api.example.com/api/ocr/base64" \
  -H "X-API-KEY: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "image_base64": "data:image/jpeg;base64,/9j/4AAQSkZJRgABA...",
    "filename": "logistics_label.jpg",
    "mode": "async"
  }'
```

## 地址提取

地址提取接口支持单条地址和批量地址提取。

### 单条地址提取

**接口地址**：`POST /api/address/extract`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|------|-----|
| text | String | 是 | 包含地址的文本 |
| mode | String | 否 | 处理模式，固定值`async` |
| options | Object | 否 | 提取选项，详见下文 |

**options参数说明**：

```json
{
  "includeOriginal": true,     // 是否包含原始文本
  "extractPhone": true,        // 是否提取电话
  "extractName": true,         // 是否提取姓名
  "normalizeAddress": true     // 是否规范化地址
}
```

**请求示例**：

```bash
curl -X POST "https://api.example.com/api/address/extract" \
  -H "X-API-KEY: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "张三，13800138000，广东省深圳市南山区科技园南区高新南四道1号",
    "mode": "async",
    "options": {
      "extractPhone": true,
      "extractName": true
    }
  }'
```

### 批量地址提取

**接口地址**：`POST /api/address/extract/batch`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|------|-----|
| texts | Array | 是 | 地址文本数组 |
| mode | String | 否 | 处理模式，固定值`async` |
| options | Object | 否 | 提取选项，同单条地址 |

**请求示例**：

```bash
curl -X POST "https://api.example.com/api/address/extract/batch" \
  -H "X-API-KEY: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "texts": [
      "张三，13800138000，广东省深圳市南山区科技园南区高新南四道1号",
      "李四，13900139000，北京市海淀区中关村南大街5号"
    ],
    "mode": "async"
  }'
```

## 坐标逆解析

坐标逆解析接口支持单个坐标和批量坐标处理。

### 单个坐标逆解析

**接口地址**：`POST /api/geocode/reverse`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|------|-----|
| latitude | Number | 是 | 纬度 |
| longitude | Number | 是 | 经度 |
| coordType | String | 否 | 坐标类型，可选值：wgs84/gcj02/bd09 |
| mode | String | 否 | 处理模式，固定值`async` |

**请求示例**：

```bash
curl -X POST "https://api.example.com/api/geocode/reverse" \
  -H "X-API-KEY: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "latitude": 22.543099,
    "longitude": 114.057868,
    "coordType": "wgs84",
    "mode": "async"
  }'
```

### 批量坐标逆解析

**接口地址**：`POST /api/geocode/reverse/batch`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|------|-----|
| coordinates | Array | 是 | 坐标数组，格式见下文 |
| coordType | String | 否 | 坐标类型，可选值：wgs84/gcj02/bd09 |
| mode | String | 否 | 处理模式，固定值`async` |

**coordinates参数格式**：

```json
[
  {"latitude": 22.543099, "longitude": 114.057868},
  {"latitude": 39.908715, "longitude": 116.397389}
]
```

**请求示例**：

```bash
curl -X POST "https://api.example.com/api/geocode/reverse/batch" \
  -H "X-API-KEY: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "coordinates": [
      {"latitude": 22.543099, "longitude": 114.057868},
      {"latitude": 39.908715, "longitude": 116.397389}
    ],
    "coordType": "wgs84",
    "mode": "async"
  }'
```

## 任务状态查询

### 获取任务状态

**接口地址**：`GET /api/tasks/{jobId}`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|------|-----|
| jobId | String | 是 | 任务ID，路径参数 |
| service | String | 否 | 服务类型，可选值：ocr/address/geocode |

**请求示例**：

```bash
curl -X GET "https://api.example.com/api/tasks/job_xxxxxxxxxx?service=ocr" \
  -H "X-API-KEY: your_api_key"
```

**响应格式**：

```json
{
  "success": true,
  "jobId": "job_xxxxxxxxxx",
  "status": "completed",
  "progress": 100,
  "result": {
    // 任务结果，与同步API返回格式一致
  },
  "timestamp": {
    "created": "2023-07-01T12:00:00Z",
    "processed": "2023-07-01T12:00:05Z",
    "finished": "2023-07-01T12:00:10Z"
  }
}
```

### 批量获取任务状态

**接口地址**：`POST /api/tasks/batch`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|------|-----|
| jobIds | Array | 是 | 任务ID数组 |

**请求示例**：

```bash
curl -X POST "https://api.example.com/api/tasks/batch" \
  -H "X-API-KEY: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "jobIds": ["job_aaaaaa", "job_bbbbbb", "job_cccccc"]
  }'
```

## SSE实时状态推送

服务器发送事件(SSE)允许客户端接收任务状态的实时更新，无需频繁轮询API。

**接口地址**：`GET /api/tasks/{jobId}/stream`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|------|-----|
| jobId | String | 是 | 任务ID，路径参数 |
| service | String | 是 | 服务类型，可选值：ocr/address/geocode |
| timeout | Number | 否 | 超时时间(ms)，默认值：30000 |
| interval | Number | 否 | 检查间隔(ms)，默认值：1000 |

**请求示例**：

```bash
curl -X GET "https://api.example.com/api/tasks/job_xxxxxxxxxx/stream?service=ocr&timeout=60000&interval=2000" \
  -H "X-API-KEY: your_api_key" \
  -H "Accept: text/event-stream"
```

**客户端示例代码（JavaScript）**：

```javascript
const jobId = "job_xxxxxxxxxx";
const service = "ocr";
const eventSource = new EventSource(`/api/tasks/${jobId}/stream?service=${service}`);

eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log(`任务状态更新: ${data.status}, 进度: ${data.progress}%`);
  
  if (data.status === 'completed' || data.status === 'failed') {
    console.log('任务结束，关闭SSE连接');
    eventSource.close();
    
    if (data.status === 'completed') {
      console.log('任务结果:', data.result);
    } else {
      console.error('任务失败:', data.error);
    }
  }
};

eventSource.onerror = (error) => {
  console.error('SSE连接错误:', error);
  eventSource.close();
};
```

## 取消任务

**接口地址**：`DELETE /api/tasks/{jobId}`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|------|-----|
| jobId | String | 是 | 任务ID，路径参数 |

**请求示例**：

```bash
curl -X DELETE "https://api.example.com/api/tasks/job_xxxxxxxxxx" \
  -H "X-API-KEY: your_api_key"
```

## 常见问题

### 1. 任务超时怎么处理？

异步任务默认有60秒的处理超时时间。如果任务在此时间内未完成，系统会自动将任务标记为失败。对于大型图片或批量处理，建议设置更长的超时时间或分批处理。

### 2. 如何处理任务失败？

当任务失败时，可以通过以下步骤处理：

1. 检查任务状态API的错误信息
2. 根据错误信息调整请求参数
3. 重新提交任务

### 3. SSE连接断开如何处理？

SSE连接可能因为网络问题或服务器重启而断开。客户端应该实现重连逻辑：

```javascript
let retryCount = 0;
const maxRetries = 5;

function connectSSE() {
  const eventSource = new EventSource(`/api/tasks/${jobId}/stream?service=${service}`);
  
  eventSource.onopen = () => {
    retryCount = 0; // 连接成功，重置重试计数
  };
  
  eventSource.onerror = (error) => {
    eventSource.close();
    
    if (retryCount < maxRetries) {
      retryCount++;
      const delay = Math.min(1000 * Math.pow(2, retryCount), 30000);
      console.log(`连接断开，${delay/1000}秒后重试(${retryCount}/${maxRetries})...`);
      setTimeout(connectSSE, delay);
    } else {
      console.error('达到最大重试次数，放弃连接');
    }
  };
  
  // 其他事件处理...
}

connectSSE();
```

### 4. 任务结果保存多久？

任务结果在系统中保存7天，超过保存期限后将无法查询。对于重要数据，请及时获取并保存。

---

如有更多问题，请联系技术支持。 