# 开放平台全栈项目

## 项目简介

本项目是一个完整的开放平台全栈解决方案，包含三个主要组件：

1. **NestJS后端服务** (nest-open-admin)：提供API接口、业务逻辑处理和数据库操作
2. **Python服务** (python-service)：提供地址提取、地理编码等AI功能
3. **Vue3前端** (vue3-open)：提供用户界面和交互体验

## 技术栈

### 后端技术栈
- NestJS框架
- TypeScript
- MySQL数据库
- Redis缓存
- Bull队列
- JWT认证

### Python服务技术栈
- Flask
- Pandas
- NumPy
- 地理编码库

### 前端技术栈
- Vue 3
- TypeScript
- Pinia状态管理
- Vue Router
- Axios

## 系统架构

本系统采用微服务架构，通过Docker Compose进行容器编排，各服务之间通过HTTP API进行通信。系统架构如下：

```
                    +----------------+
                    |     Nginx      |
                    | (反向代理服务器) |
                    +--------+-------+
                             |
              +--------------|-------------+
              |              |             |
    +---------v---+   +------v------+   +--v----------+
    |   Vue前端    |   | NestJS后端  |   | Python服务  |
    | (vue3-open) |   |(nest-open-  |   |(python-    |
    |             |   |   admin)    |   |  service)   |
    +-------------+   +------+------+   +-------------+
                             |
                      +------v------+
                      |   数据库     |
                      | (MySQL/Redis)|
                      +-------------+
```

## 部署指南

详细的部署步骤请参考：

- [Ubuntu服务器部署指南](./DEPLOY_GUIDE.md) - 适用于阿里云等云服务器环境

## 开发指南

### 本地开发环境设置

1. 克隆仓库
```bash
git clone <仓库URL>
cd open-backend-frontend
```

2. 安装依赖
```bash
# 安装NestJS后端依赖
cd nest-open-admin
pnpm install

# 安装Python服务依赖
cd ../python-service
pip install -r requirements.txt

# 安装Vue前端依赖
cd ../vue3-open
pnpm install
```

3. 启动开发服务器
```bash
# 启动NestJS后端
cd nest-open-admin
pnpm run start:dev

# 启动Python服务
cd ../python-service
python app.py

# 启动Vue前端
cd ../vue3-open
pnpm run dev
```

## 许可证

[MIT](LICENSE)

## 联系方式

如有问题，请联系项目维护者。 


curl -X PATCH https://api.aiszyl.cn/v1/op/service/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************.0H5o866_oRfwaOIVMgHZXjmBZpiRn5LnOHj-E6sc6fQ" \
  -d '{
    "name": "物流面单OCR识别",
    "description": "智能识别物流面单信息，提取收发件人、地址、电话等关键信息，支持多种面单格式，准确率高达95%以上。适用于物流企业、电商平台等自动化数据录入场景。",
    "features": "高精度识别,多格式支持,实时处理,结构化输出,批量处理",
    "endpoint": "/op/ocr/upload"
  }'

  # 更新 ID 为 2 的服务
curl -X PATCH https://api.aiszyl.cn/v1/op/service/2 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************.0H5o866_oRfwaOIVMgHZXjmBZpiRn5LnOHj-E6sc6fQ" \
  -d '{
    "name": "物流文本精确提取省市区详细地址",
    "description": "从文本中精确提取省市区详细地址信息，支持非结构化文本解析，可处理各种格式的地址信息，并进行标准化输出，支持省市区到街道社区的精确识别。",
    "features": "智能分词,地址标准化,高准确率,支持模糊地址,批量处理,多维度纠错",
    "endpoint": "/op/address/extract"
  }'

  # 更新 ID 为 3 的服务
curl -X PATCH https://api.aiszyl.cn/v1/op/service/3 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************.0H5o866_oRfwaOIVMgHZXjmBZpiRn5LnOHj-E6sc6fQ" \
  -d '{
    "name": "地理坐标逆解析出地址",
    "description": "根据经纬度坐标逆向解析出详细地址信息，支持多种坐标系，可输出丰富的POI信息，精确到建筑物级别。适用于物流配送、位置服务、用户轨迹分析等场景。",
    "features": "高精度定位,实时解析,多坐标系支持,POI识别,跨平台兼容,批量处理",
    "endpoint": "/op/address/rev-geo"
  }'