import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
  HttpException,
  HttpStatus,
  Inject,
  forwardRef,
  ConflictException,
} from '@nestjs/common';
import { ApiException } from '@/common/exceptions/api.exception';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In, Query<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'typeorm';
import {
  UserEntity,
  UserType,
  UserVerificationStatus,
  UserStatus,
} from './entities/user.entity';
import { UserVerificationEntity, ReviewStatus } from './entities/user-verification.entity';
import { UserCompanyEntity } from './entities/user-company.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { PersonalVerificationDto, EnterpriseVerificationDto, ReviewVerificationDto } from './dto/user-verification.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as bcrypt from 'bcrypt';
import { plainToClass } from 'class-transformer';
import { UserResponseDto } from './dto/user-response.dto';

@Injectable()
export class UserService {
  private logger = new Logger(UserService.name);

  constructor(
    @InjectRepository(UserEntity)
    private userRepository: Repository<UserEntity>,
    @InjectRepository(UserVerificationEntity)
    private userVerificationRepository: Repository<UserVerificationEntity>,
    @InjectRepository(UserCompanyEntity)
    private userCompanyRepository: Repository<UserCompanyEntity>,
    private dataSource: DataSource,
    private eventEmitter: EventEmitter2
  ) {}

  /**
   * 创建用户
   * @param createUserDto 用户创建DTO
   * @returns 创建的用户实体
   */
  async create(createUserDto: CreateUserDto): Promise<UserResponseDto> {
    // 检查用户名是否已存在
    const existingUsername = await this.userRepository.findOne({
      where: { username: createUserDto.username },
    });
    if (existingUsername) {
      throw new ConflictException('用户名已存在');
    }

    // 检查邮箱是否已存在
    if (createUserDto.email) {
      const existingEmail = await this.userRepository.findOne({
        where: { email: createUserDto.email },
      });
      if (existingEmail) {
        throw new ConflictException('邮箱已存在');
      }
    }

    // 检查手机号是否已存在
    if (createUserDto.phone) {
      const existingPhone = await this.userRepository.findOne({
        where: { phone: createUserDto.phone },
      });
      if (existingPhone) {
        throw new ConflictException('手机号已存在');
      }
    }

    // 创建新用户实体
    const user = this.userRepository.create({
      ...createUserDto,
      userType: createUserDto.userType || UserType.INDIVIDUAL,
      verificationStatus: UserVerificationStatus.UNVERIFIED,
      userStatus: UserStatus.ACTIVE,
      lastDailyResetDate: new Date(),
      isFreeQuotaEligible: true,
    });

    // 保存用户
    const savedUser = await this.userRepository.save(user);
    this.logger.log(`用户创建成功: ${savedUser.username} (ID: ${savedUser.id})`);

    // 发送用户创建事件
    this.eventEmitter.emit('user.created', savedUser);

    return plainToClass(UserResponseDto, savedUser, { excludeExtraneousValues: true });
  }

  /**
   * 根据ID查找用户
   * @param id 用户ID
   * @returns 用户实体
   * @throws NotFoundException 当用户不存在时
   */
  async findById(id: number): Promise<UserEntity> {
    const user = await this.userRepository.findOne({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException(`用户 #${id} 不存在`);
    }

    return user;
  }

  /**
   * 根据ID查找用户并转换为响应DTO
   * @param id 用户ID
   * @returns 用户响应DTO
   */
  async findOne(id: number): Promise<UserResponseDto> {
    const user = await this.findById(id);
    return plainToClass(UserResponseDto, user, { excludeExtraneousValues: true });
  }

  /**
   * 根据邮箱查询用户
   * @param email 用户邮箱
   * @returns 用户实体
   */
  async findByEmail(email: string): Promise<UserEntity> {
    const user = await this.userRepository.findOne({
      where: { email },
    });

    if (!user) {
      throw new NotFoundException(`邮箱为 ${email} 的用户不存在`);
    }

    return user;
  }

  /**
   * 根据手机号查询用户
   * @param phone 用户手机号
   * @returns 用户实体
   */
  async findByPhone(phone: string): Promise<UserEntity> {
    const user = await this.userRepository.findOne({
      where: { phone },
    });

    if (!user) {
      throw new NotFoundException(`手机号为 ${phone} 的用户不存在`);
    }

    return user;
  }

  /**
   * 根据用户名查询用户
   * @param username 用户名
   * @returns 用户实体
   */
  async findByUsername(username: string): Promise<UserEntity> {
    const user = await this.userRepository.findOne({
      where: { username },
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    return user;
  }

  /**
   * 根据邮箱查询用户（不抛出异常）
   * @param email 用户邮箱
   * @returns 用户实体或null
   */
  async findByEmailSafe(email: string): Promise<UserEntity | null> {
    return await this.userRepository.findOne({
      where: { email },
    });
  }

  /**
   * 根据手机号查询用户（不抛出异常）
   * @param phone 用户手机号
   * @returns 用户实体或null
   */
  async findByPhoneSafe(phone: string): Promise<UserEntity | null> {
    return await this.userRepository.findOne({
      where: { phone },
    });
  }

  /**
   * 根据用户名查询用户（不抛出异常）
   * @param username 用户名
   * @returns 用户实体或null
   */
  async findByUsernameSafe(username: string): Promise<UserEntity | null> {
    return await this.userRepository.findOne({
      where: { username },
    });
  }

  /**
   * 分页查询用户
   * @param options 查询选项
   * @returns 用户列表和总数
   */
  async findAll(
    page = 1,
    limit = 10,
    options: {
      username?: string;
      email?: string;
      phone?: string;
      userType?: UserType;
      verificationStatus?: UserVerificationStatus;
      userStatus?: UserStatus;
    } = {},
  ): Promise<{ data: UserResponseDto[]; total: number; page: number; limit: number; totalPages: number }> {
    const queryBuilder = this.userRepository.createQueryBuilder('user');

    // 添加查询条件
    if (options.username) {
      queryBuilder.andWhere('user.username LIKE :username', { username: `%${options.username}%` });
    }

    if (options.email) {
      queryBuilder.andWhere('user.email LIKE :email', { email: `%${options.email}%` });
    }

    if (options.phone) {
      queryBuilder.andWhere('user.phone LIKE :phone', { phone: `%${options.phone}%` });
    }

    if (options.userType) {
      queryBuilder.andWhere('user.userType = :userType', { userType: options.userType });
    }

    if (options.verificationStatus) {
      queryBuilder.andWhere('user.verificationStatus = :verificationStatus', { verificationStatus: options.verificationStatus });
    }

    if (options.userStatus) {
      queryBuilder.andWhere('user.userStatus = :userStatus', { userStatus: options.userStatus });
    }

    // 计算分页参数
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // 添加排序
    queryBuilder.orderBy('user.createdAt', 'DESC');

    // 执行查询
    const [users, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    // 转换为响应DTO
    const data = users.map(user => plainToClass(UserResponseDto, user, { excludeExtraneousValues: true }));

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * 更新用户信息
   * @param id 用户ID
   * @param updateUserDto 用户更新DTO
   * @returns 更新后的用户实体
   */
  async update(id: number, updateUserDto: UpdateUserDto): Promise<UserResponseDto> {
    const user = await this.findById(id);

    // 处理密码更新
    if (updateUserDto.password) {
      const salt = await bcrypt.genSalt(10);
      updateUserDto.password = await bcrypt.hash(updateUserDto.password, salt);
    }

    // 更新用户信息
    const updatedUser = Object.assign(user, updateUserDto);
    const savedUser = await this.userRepository.save(updatedUser);
    
    this.logger.log(`用户 #${id} 信息已更新`);

    // 发送用户更新事件
    this.eventEmitter.emit('user.updated', savedUser);

    return plainToClass(UserResponseDto, savedUser, { excludeExtraneousValues: true });
  }

  /**
   * 更新用户状态
   * @param id 用户ID
   * @param status 新状态
   * @returns 更新后的用户实体
   */
  async updateStatus(id: number, status: UserStatus): Promise<UserResponseDto> {
    const user = await this.findById(id);
    user.userStatus = status;
    
    // 如果用户被锁定，设置锁定时间
    if (status === UserStatus.LOCKED) {
      const lockDuration = 24 * 60 * 60 * 1000; // 24小时
      user.lockedUntil = new Date(Date.now() + lockDuration);
    } else if (status === UserStatus.ACTIVE) {
      user.lockedUntil = undefined;
      user.loginFailCount = 0;
    }
    
    const savedUser = await this.userRepository.save(user);
    
    this.logger.log(`用户 #${id} 状态已更新为 ${status}`);
    
    return plainToClass(UserResponseDto, savedUser, { excludeExtraneousValues: true });
  }

  /**
   * 删除用户
   * @param id 用户ID
   * @returns 删除结果
   */
  async remove(id: number): Promise<{ success: boolean; message: string }> {
    const user = await this.findById(id);
    await this.userRepository.softDelete(id);

    this.logger.log(`用户 #${id} 已删除`);

    // 发送用户删除事件
    this.eventEmitter.emit('user.deleted', id);

    return {
      success: true,
      message: `用户 ${user.username} 已成功删除`,
    };
  }

  /**
   * 更新用户最后登录信息
   * @param userId 用户ID
   * @param ip 客户端IP地址
   */
  async updateLastLogin(userId: number, ip: string): Promise<void> {
    try {
      await this.userRepository.update(userId, {
        lastLoginAt: new Date(),
        lastLoginIp: ip,
      });

      this.logger.log(`用户 #${userId} 最后登录信息已更新，IP: ${ip}`);
    } catch (error) {
      this.logger.error(`更新用户最后登录信息失败: ${error.message}`, error.stack);
      throw new BadRequestException('更新登录信息失败');
    }
  }

  /**
   * 增加登录失败计数
   * @param userId 用户ID
   * @param maxAttempts 最大尝试次数
   * @param lockDuration 锁定时长（秒）
   */
  async incrementLoginFailCount(userId: number, maxAttempts = 5, lockDuration = 900): Promise<void> {
    try {
      const user = await this.findById(userId);
      const newFailCount = user.loginFailCount + 1;
      const updates: any = { loginFailCount: newFailCount };

      // 如果失败次数达到阈值，锁定账户
      if (newFailCount >= maxAttempts) {
        const lockedUntil = new Date(Date.now() + lockDuration * 1000);
        updates.lockedUntil = lockedUntil;
        updates.userStatus = UserStatus.LOCKED;
      }

      await this.userRepository.update(userId, updates);

      this.logger.warn(`用户 #${userId} 登录失败次数增加到 ${newFailCount}${newFailCount >= maxAttempts ? '，账户已锁定' : ''}`);
    } catch (error) {
      this.logger.error(`增加登录失败计数失败: ${error.message}`, error.stack);
      throw new BadRequestException('更新登录失败计数失败');
    }
  }

  /**
   * 重置登录失败计数
   * @param userId 用户ID
   */
  async resetLoginFailCount(userId: number): Promise<void> {
    try {
      await this.userRepository.update(userId, {
        loginFailCount: 0,
        lockedUntil: undefined,
        userStatus: UserStatus.ACTIVE,
      });

      this.logger.log(`用户 #${userId} 登录失败计数已重置`);
    } catch (error) {
      this.logger.error(`重置登录失败计数失败: ${error.message}`, error.stack);
      throw new BadRequestException('重置登录失败计数失败');
    }
  }

  /**
   * 更新用户的第三方账号信息
   * @param userId 用户ID
   * @param thirdPartyData 第三方账号数据
   */
  async updateThirdPartyInfo(userId: number, thirdPartyData: {
    wechatOpenid?: string;
    wechatUnionid?: string;
    alipayUserId?: string;
    githubId?: string;
    googleId?: string;
  }): Promise<void> {
    try {
      await this.userRepository.update(userId, thirdPartyData);
      this.logger.log(`用户 #${userId} 第三方账号信息已更新`);
    } catch (error) {
      this.logger.error(`更新第三方账号信息失败: ${error.message}`, error.stack);
      throw new BadRequestException('更新第三方账号信息失败');
    }
  }

  /**
   * 清除用户的第三方账号信息
   * @param userId 用户ID
   * @param fields 要清除的字段
   */
  async clearThirdPartyInfo(userId: number, fields: string[]): Promise<void> {
    try {
      const updateData: any = {};
      fields.forEach(field => {
        updateData[field] = undefined;
      });

      await this.userRepository.update(userId, updateData);
      this.logger.log(`用户 #${userId} 第三方账号信息已清除: ${fields.join(', ')}`);
    } catch (error) {
      this.logger.error(`清除第三方账号信息失败: ${error.message}`, error.stack);
      throw new BadRequestException('清除第三方账号信息失败');
    }
  }

  /**
   * 提交个人实名认证
   * @param userId 用户ID
   * @param verificationDto 认证信息
   * @returns 认证记录
   */
  async submitPersonalVerification(
    userId: number,
    verificationDto: PersonalVerificationDto,
  ): Promise<{ success: boolean; message: string; data: any }> {
    const user = await this.findById(userId);

    // 检查用户是否已提交认证
    const existingVerification = await this.userVerificationRepository.findOne({
      where: { userId },
    });

    if (existingVerification && existingVerification.reviewStatus === ReviewStatus.PENDING) {
      throw new BadRequestException('您已提交实名认证申请，请等待审核');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 创建或更新认证记录
      let verification: UserVerificationEntity;
      
      if (existingVerification) {
        verification = Object.assign(existingVerification, {
          ...verificationDto,
          reviewStatus: ReviewStatus.PENDING,
          reviewRemark: null,
          reviewedAt: null,
          reviewerId: null,
        });
      } else {
        verification = this.userVerificationRepository.create({
          userId: user.id,
          ...verificationDto,
          reviewStatus: ReviewStatus.PENDING,
        });
      }

      // 更新用户认证状态
      user.verificationStatus = UserVerificationStatus.VERIFICATION_PENDING;
      user.realName = verificationDto.realName;

      // 保存变更
      await queryRunner.manager.save(user);
      const savedVerification = await queryRunner.manager.save(verification);

      // 提交事务
      await queryRunner.commitTransaction();

      // 发送认证提交事件
      this.eventEmitter.emit('user.verification.submitted', {
        userId: user.id,
        verificationType: 'personal',
        verificationId: savedVerification.id,
      });

      return {
        success: true,
        message: '个人实名认证申请已提交，请等待审核',
        data: {
          verificationId: savedVerification.id,
          reviewStatus: savedVerification.reviewStatus,
        },
      };
    } catch (error) {
      // 回滚事务
      await queryRunner.rollbackTransaction();
      this.logger.error(`提交个人实名认证失败: ${error.message}`, error.stack);
      throw new HttpException('提交实名认证失败', HttpStatus.INTERNAL_SERVER_ERROR);
    } finally {
      // 释放查询运行器
      await queryRunner.release();
    }
  }

  /**
   * 提交企业实名认证
   * @param userId 用户ID
   * @param verificationDto 认证信息
   * @returns 认证记录
   */
  async submitEnterpriseVerification(
    userId: number,
    verificationDto: EnterpriseVerificationDto,
  ): Promise<{ success: boolean; message: string; data: any }> {
    const user = await this.findById(userId);

    // 检查用户是否已提交企业认证
    const existingVerification = await this.userCompanyRepository.findOne({
      where: { userId },
    });

    if (existingVerification && existingVerification.reviewedStatus === ReviewStatus.PENDING) {
      throw new BadRequestException('您已提交企业认证申请，请等待审核');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 创建或更新认证记录
      let verification: UserCompanyEntity;
      
      if (existingVerification) {
        verification = Object.assign(existingVerification, {
          ...verificationDto,
          reviewedStatus: ReviewStatus.PENDING,
          reviewedAt: null,
          reviewerId: null,
        });
      } else {
        verification = this.userCompanyRepository.create({
          userId: user.id,
          ...verificationDto,
          reviewedStatus: ReviewStatus.PENDING,
        });
      }

      // 更新用户认证状态
      user.verificationStatus = UserVerificationStatus.VERIFICATION_PENDING;
      user.userType = UserType.ENTERPRISE;

      // 保存变更
      await queryRunner.manager.save(user);
      const savedVerification = await queryRunner.manager.save(verification);

      // 提交事务
      await queryRunner.commitTransaction();

      // 发送认证提交事件
      this.eventEmitter.emit('user.verification.submitted', {
        userId: user.id,
        verificationType: 'enterprise',
        verificationId: savedVerification.id,
      });

      return {
        success: true,
        message: '企业实名认证申请已提交，请等待审核',
        data: {
          verificationId: savedVerification.id,
          reviewStatus: savedVerification.reviewedStatus,
        },
      };
    } catch (error) {
      // 回滚事务
      await queryRunner.rollbackTransaction();
      this.logger.error(`提交企业实名认证失败: ${error.message}`, error.stack);
      throw new HttpException('提交实名认证失败', HttpStatus.INTERNAL_SERVER_ERROR);
    } finally {
      // 释放查询运行器
      await queryRunner.release();
    }
  }

  /**
   * 审核实名认证
   * @param verificationId 认证记录ID
   * @param reviewDto 审核信息
   * @returns 审核结果
   */
  async reviewVerification(
    verificationId: number,
    verificationType: 'personal' | 'enterprise',
    reviewDto: ReviewVerificationDto,
  ): Promise<{ success: boolean; message: string }> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      let verification;
      let userId;
      
      // 根据认证类型获取认证记录
      if (verificationType === 'personal') {
        verification = await queryRunner.manager.findOne(UserVerificationEntity, {
          where: { id: verificationId },
        });
        
        if (!verification) {
          throw new NotFoundException(`个人认证记录 #${verificationId} 不存在`);
        }
        
        userId = verification.userId;
        
        // 更新认证状态
        verification.reviewStatus = reviewDto.status;
        verification.reviewRemark = reviewDto.remark;
        verification.reviewedAt = new Date();
        verification.reviewerId = reviewDto.reviewerId;
        
        await queryRunner.manager.save(verification);
      } else {
        verification = await queryRunner.manager.findOne(UserCompanyEntity, {
          where: { id: verificationId },
        });
        
        if (!verification) {
          throw new NotFoundException(`企业认证记录 #${verificationId} 不存在`);
        }
        
        userId = verification.userId;
        
        // 更新认证状态
        verification.reviewedStatus = reviewDto.status;
        verification.reviewedAt = new Date();
        verification.reviewerId = reviewDto.reviewerId;
        
        await queryRunner.manager.save(verification);
      }
      
      // 获取用户信息
      const user = await queryRunner.manager.findOne(UserEntity, {
        where: { id: userId },
      });
      
      if (!user) {
        throw new NotFoundException(`用户 #${userId} 不存在`);
      }
      
      // 根据审核结果更新用户状态
      if (reviewDto.status === ReviewStatus.APPROVED) {
        // 认证通过，更新用户认证状态
        if (verificationType === 'personal') {
          user.verificationStatus = UserVerificationStatus.PERSONAL_VERIFIED;
        } else {
          user.verificationStatus = UserVerificationStatus.ENTERPRISE_VERIFIED;
        }
      } else if (reviewDto.status === ReviewStatus.REJECTED) {
        // 认证被拒绝
        user.verificationStatus = UserVerificationStatus.VERIFICATION_REJECTED;
      }
      
      await queryRunner.manager.save(user);
      
      // 提交事务
      await queryRunner.commitTransaction();
      
      // 如果审核通过，发送认证通过事件
      if (reviewDto.status === ReviewStatus.APPROVED) {
        // 发送认证通过事件，由UserServiceService监听并处理奖励发放
        this.eventEmitter.emit('user.verification.approved', {
          userId,
          verificationType,
          verificationId,
          reviewerId: reviewDto.reviewerId,
        });
        
        this.logger.log(`用户 #${userId} ${verificationType}实名认证已通过，由审核员 #${reviewDto.reviewerId} 审核`);
        
        return {
          success: true,
          message: `${verificationType === 'personal' ? '个人' : '企业'}实名认证审核通过`,
        };
      } else {
        // 发送认证拒绝事件
        this.eventEmitter.emit('user.verification.rejected', {
          userId,
          verificationType,
          verificationId,
          reviewerId: reviewDto.reviewerId,
          remark: reviewDto.remark,
        });
        
        this.logger.log(`用户 #${userId} ${verificationType}实名认证被拒绝，由审核员 #${reviewDto.reviewerId} 审核`);
        
        return {
          success: true,
          message: `${verificationType === 'personal' ? '个人' : '企业'}实名认证审核不通过`,
        };
      }
    } catch (error) {
      // 回滚事务
      await queryRunner.rollbackTransaction();
      this.logger.error(`审核实名认证失败: ${error.message}`, error.stack);
      throw error;
    } finally {
      // 释放查询运行器
      await queryRunner.release();
    }
  }

  /**
   * 获取用户认证信息
   * @param userId 用户ID
   * @returns 用户认证信息
   */
  async getVerificationInfo(userId: number): Promise<any> {
    const user = await this.findById(userId);
    
    // 根据用户类型获取不同的认证信息
    if (user.userType === UserType.INDIVIDUAL) {
      const personalVerification = await this.userVerificationRepository.findOne({
        where: { userId },
      });
      
      return {
        verificationType: 'personal',
        verificationStatus: user.verificationStatus,
        verificationInfo: personalVerification,
      };
    } else if (user.userType === UserType.ENTERPRISE) {
      const enterpriseVerification = await this.userCompanyRepository.findOne({
        where: { userId },
      });
      
      return {
        verificationType: 'enterprise',
        verificationStatus: user.verificationStatus,
        verificationInfo: enterpriseVerification,
      };
    }
    
    return {
      verificationType: null,
      verificationStatus: user.verificationStatus,
      verificationInfo: null,
    };
  }

  /**
   * 重置用户每日免费额度
   * 此方法由定时任务调用，每天凌晨执行
   * @returns 重置结果
   */
  async resetDailyFreeQuota(): Promise<{ success: boolean; message: string; count: number }> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    try {
      // 查询所有有资格获取免费额度的用户
      const users = await this.userRepository.find({
        where: { isFreeQuotaEligible: true },
      });

      if (users.length === 0) {
        return { success: true, message: '没有符合条件的用户需要重置每日免费额度', count: 0 };
      }

      let count = 0;

      // 更新用户每日重置日期
      for (const user of users) {
        user.lastDailyResetDate = today;
        await this.userRepository.save(user);
        count++;
      }

      // 发送重置事件，让UserServiceService来处理每个用户的freeUsedToday字段
      this.eventEmitter.emit('user.daily_quota.reset', {
        date: today,
        userIds: users.map(u => u.id),
      });

      this.logger.log(`已重置 ${count} 个用户的每日免费额度`);

      return {
        success: true,
        message: `已重置 ${count} 个用户的每日免费额度`,
        count,
      };
    } catch (error) {
      this.logger.error(`重置每日免费额度失败: ${error.message}`, error.stack);
      throw new HttpException('重置每日免费额度失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 检查用户免费额度资格
   * 此方法由定时任务调用，每个月执行
   * @returns 检查结果
   */
  async checkFreeQuotaEligibility(): Promise<{ success: boolean; message: string; count: number }> {
    try {
      // 计算半年前的日期
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

      // 查找已超过半年的用户
      const users = await this.userRepository.createQueryBuilder('user')
        .where('user.createdAt < :sixMonthsAgo', { sixMonthsAgo })
        .andWhere('user.isFreeQuotaEligible = :eligible', { eligible: true })
        .getMany();

      if (users.length === 0) {
        return { success: true, message: '没有需要更新免费额度资格的用户', count: 0 };
      }

      // 批量更新用户免费额度资格
      await this.userRepository.update(
        { id: In(users.map(u => u.id)) },
        { isFreeQuotaEligible: false }
      );

      // 发送免费额度资格更新事件
      this.eventEmitter.emit('user.free_quota_eligibility.updated', {
        date: new Date(),
        userIds: users.map(u => u.id),
        eligible: false,
      });

      this.logger.log(`已更新 ${users.length} 个用户的免费额度资格`);

      return {
        success: true,
        message: `已更新 ${users.length} 个用户的免费额度资格`,
        count: users.length,
      };
    } catch (error) {
      this.logger.error(`检查用户免费额度资格失败: ${error.message}`, error.stack);
      throw new HttpException('检查用户免费额度资格失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 查找符合免费额度重置条件的新用户
   * 条件：注册时间在7天内且没有购买记录的用户
   */
  async findNewUsersForFreeQuota(): Promise<UserEntity[]> {
    try {
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      // 查找7天内注册的用户
      const newUsers = await this.userRepository.find({
        where: {
          createdAt: MoreThan(sevenDaysAgo),
          userStatus: UserStatus.ACTIVE, // 只处理活跃用户
        },
        select: ['id', 'email', 'createdAt', 'userStatus']
      });

      // 这里可以进一步过滤掉有购买记录的用户
      // 由于涉及到其他模块的数据，这里简化处理
      // 实际实现中可以联查订单表或用户服务表来确定是否有购买记录

      this.logger.log(`找到 ${newUsers.length} 个符合免费额度重置条件的新用户`);
      return newUsers;
    } catch (error) {
      this.logger.error(`查找新用户失败: ${error.message}`, error.stack);
      throw new HttpException('查找新用户失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 增加用户余额
   */
  async addBalance(userId: number, amount: number, reason: string = '充值'): Promise<void> {
    try {
      this.logger.log(`为用户 ${userId} 增加余额: ${amount}, 原因: ${reason}`);

      const result = await this.userRepository
        .createQueryBuilder()
        .update(UserEntity)
        .set({ balance: () => `balance + ${amount}` })
        .where('id = :userId', { userId })
        .execute();

      if (result.affected === 0) {
        throw new NotFoundException('用户不存在');
      }

      this.logger.log(`用户 ${userId} 余额增加成功: +${amount}`);
    } catch (error) {
      this.logger.error(`增加用户余额失败: ${error.message}`, error.stack);
      throw new HttpException('增加用户余额失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 扣减用户余额
   */
  async deductBalance(userId: number, amount: number, reason: string = '消费'): Promise<boolean> {
    try {
      this.logger.log(`为用户 ${userId} 扣减余额: ${amount}, 原因: ${reason}`);

      const result = await this.userRepository
        .createQueryBuilder()
        .update(UserEntity)
        .set({ balance: () => `balance - ${amount}` })
        .where('id = :userId AND balance >= :amount', { userId, amount })
        .execute();

      const success = (result.affected || 0) > 0;

      if (success) {
        this.logger.log(`用户 ${userId} 余额扣减成功: -${amount}`);
      } else {
        this.logger.warn(`用户 ${userId} 余额扣减失败: 余额不足或用户不存在`);
      }

      return success;
    } catch (error) {
      this.logger.error(`扣减用户余额失败: ${error.message}`, error.stack);
      throw new HttpException('扣减用户余额失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取用户余额
   */
  async getBalance(userId: number): Promise<number> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
        select: ['balance'],
      });

      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      return user.balance || 0;
    } catch (error) {
      this.logger.error(`获取用户余额失败: ${error.message}`, error.stack);
      throw new HttpException('获取用户余额失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
