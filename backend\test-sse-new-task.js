// 测试SSE功能 - 创建新任务
const axios = require('axios');
const crypto = require('crypto');

// 生成签名
function generateSignature(canonical, secretKey) {
  return crypto.createHmac('sha256', secretKey).update(canonical).digest('base64');
}

// 生成随机字符串
function generateNonce() {
  return crypto.randomBytes(16).toString('hex');
}

// 构建规范请求
function buildCanonicalRequest(method, path, queryParams, headers, timestamp) {
  const sortedHeaders = Object.keys(headers)
    .filter(key => key.toLowerCase().startsWith('x-'))
    .sort()
    .map(key => `${key.toLowerCase()}:${headers[key]}`)
    .join('\n');

  const sortedQuery = Object.keys(queryParams)
    .sort()
    .map(key => `${key}=${encodeURIComponent(queryParams[key])}`)
    .join('&');

  return `${method}\n${path}\n${sortedQuery}\n${sortedHeaders}\n${timestamp}`;
}

async function testSSEWithNewTask() {
  console.log('=== 测试SSE功能 - 创建新任务 ===');
  
  const apiKey = 'ak-579b27759508f152525a4e5a567efd5a';
  const secretKey = 'sk-579b27759508f152525a4e5a567efd5a';
  const baseUrl = 'http://127.0.0.1:8088';
  
  try {
    // 1. 创建新的地址解析任务
    console.log('1. 创建新的地址解析任务...');
    
    const path = '/v1/op/address/extract';
    const timestamp = Math.floor(Date.now() / 1000).toString();
    const nonce = generateNonce();
    
    const headers = {
      'X-API-KEY': apiKey,
      'X-Timestamp': timestamp,
      'X-Nonce': nonce,
    };
    
    const canonical = buildCanonicalRequest('POST', path, {}, headers, timestamp);
    const signature = generateSignature(canonical, secretKey);
    headers['X-Signature'] = signature;
    
    const requestBody = {
      text: '李四，13900139000，北京市朝阳区建国门外大街1号',
      mode: 'async',
      serviceId: 5
    };
    
    const response = await axios.post(`${baseUrl}${path}`, requestBody, {
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('   ✅ 任务创建成功');
    console.log('   响应状态:', response.status);
    console.log('   响应数据:', JSON.stringify(response.data, null, 2));
    
    if (!response.data.success || !response.data.data?.jobId) {
      console.log('   ❌ 任务创建失败，无法获取jobId');
      return;
    }
    
    const jobId = response.data.data.jobId;
    console.log(`   任务ID: ${jobId}`);
    
    // 2. 测试SSE连接
    console.log('\n2. 测试SSE连接...');
    
    const sseUrl = `${baseUrl}/v1/op/tasks/${jobId}/events`;
    console.log(`   SSE URL: ${sseUrl}`);
    
    // 使用curl命令测试SSE（因为Node.js的EventSource支持有限）
    console.log('   请在浏览器中打开以下URL测试SSE:');
    console.log(`   ${sseUrl}`);
    
    // 或者使用简单的HTTP请求测试
    console.log('\n3. 使用HTTP请求测试SSE端点...');
    
    try {
      const sseResponse = await axios.get(sseUrl, {
        timeout: 5000,
        headers: {
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        }
      });
      
      console.log('   ✅ SSE端点响应成功');
      console.log('   响应状态:', sseResponse.status);
      console.log('   响应头:', sseResponse.headers);
      console.log('   响应数据:', sseResponse.data);
      
    } catch (sseError) {
      if (sseError.code === 'ECONNABORTED') {
        console.log('   ⚠️  SSE连接超时（这是正常的，因为SSE是长连接）');
      } else {
        console.log('   ❌ SSE连接失败:', sseError.message);
      }
    }
    
    // 3. 检查任务状态
    console.log('\n4. 检查任务状态...');
    
    const statusPath = `/v1/op/tasks/${jobId}`;
    const statusTimestamp = Math.floor(Date.now() / 1000).toString();
    const statusNonce = generateNonce();
    
    const statusHeaders = {
      'X-API-KEY': apiKey,
      'X-Timestamp': statusTimestamp,
      'X-Nonce': statusNonce,
    };
    
    const statusCanonical = buildCanonicalRequest('GET', statusPath, {}, statusHeaders, statusTimestamp);
    const statusSignature = generateSignature(statusCanonical, secretKey);
    statusHeaders['X-Signature'] = statusSignature;
    
    try {
      const statusResponse = await axios.get(`${baseUrl}${statusPath}`, {
        headers: statusHeaders
      });
      
      console.log('   ✅ 任务状态查询成功');
      console.log('   状态数据:', JSON.stringify(statusResponse.data, null, 2));
      
    } catch (statusError) {
      console.log('   ❌ 任务状态查询失败:', statusError.message);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('   响应状态:', error.response.status);
      console.error('   响应数据:', error.response.data);
    }
  }
}

testSSEWithNewTask().catch(console.error);
