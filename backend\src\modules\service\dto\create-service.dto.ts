import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsOptional,
  IsNumber,
  MaxLength,
  <PERSON><PERSON>ength,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ServiceStatus, ServiceType, PricingModel } from '../enums/service.enum';

export class CreateServiceDto {

  @ApiProperty({
    description: '服务唯一代码',
    example: 'OCR_UPLOAD',
  })
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  code: string;

  @ApiProperty({
    description: '服务名称',
    example: 'OCR文件上传识别',
  })
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: '服务类型',
    enum: ServiceType,
    example: ServiceType.OCR,
  })
  @IsEnum(ServiceType)
  type: ServiceType;

  @ApiPropertyOptional({
    description: '服务状态',
    enum: ServiceStatus,
    default: ServiceStatus.DRAFT,
    example: ServiceStatus.DRAFT,
  })
  @IsEnum(ServiceStatus)
  @IsOptional()
  status?: ServiceStatus;

  @ApiPropertyOptional({
    description: '服务描述',
    example: '支持图片文件上传进行OCR文字识别，提取图片中的文字信息',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: '定价模式',
    enum: PricingModel,
    default: PricingModel.PER_REQUEST,
    example: PricingModel.PER_REQUEST,
  })
  @IsEnum(PricingModel)
  pricingModel: PricingModel;

  @ApiPropertyOptional({
    description: '单价（元）',
    default: 0,
    example: 0.1,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  unitPrice?: number;

  @ApiPropertyOptional({
    description: '服务端点URL',
    example: '/v1/op/ocr/upload',
  })
  @IsString()
  @IsOptional()
  endpoint?: string;

  @ApiPropertyOptional({
    description: '排序顺序',
    default: 0,
    example: 100,
  })
  @IsNumber()
  @IsOptional()
  sortOrder?: number;
} 