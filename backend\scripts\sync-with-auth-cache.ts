import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { ApiKeyService } from '../src/modules/api-key/services/api-key.service';

/**
 * 使用新的AuthCacheService同步API密钥
 */
async function syncWithAuthCache() {
  console.log('开始使用AuthCacheService同步API密钥...');
  
  const app = await NestFactory.createApplicationContext(AppModule);
  
  try {
    const apiKeyService = app.get(ApiKeyService);
    
    console.log('正在同步API密钥...');
    const result = await apiKeyService.syncAllKeysToCache();
    
    console.log(`✅ 同步完成！成功同步 ${result.syncedCount} 个密钥`);
    
    if (result.errors.length > 0) {
      console.log('❌ 同步过程中的错误:');
      result.errors.forEach(error => console.log(`  - ${error}`));
    }
    
  } catch (error) {
    console.error('❌ 同步失败:', error.message);
  } finally {
    await app.close();
    console.log('应用已关闭');
  }
}

// 运行脚本
syncWithAuthCache().catch(console.error);
