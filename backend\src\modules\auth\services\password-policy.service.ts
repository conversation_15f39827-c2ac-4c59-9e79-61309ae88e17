import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';

/**
 * 密码策略服务
 * 专门负责密码相关的验证和管理
 */
@Injectable()
export class PasswordPolicyService {
  constructor(private readonly configService: ConfigService) {}

  /**
   * 验证密码复杂度
   */
  validatePasswordComplexity(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    const minLength = this.configService.get<number>('auth.passwordPolicy.minLength', 8);
    const requireLowercase = this.configService.get<boolean>('auth.passwordPolicy.requireLowercase', true);
    const requireUppercase = this.configService.get<boolean>('auth.passwordPolicy.requireUppercase', true);
    const requireNumbers = this.configService.get<boolean>('auth.passwordPolicy.requireNumbers', true);
    const requireSpecialChars = this.configService.get<boolean>('auth.passwordPolicy.requireSpecialChars', true);

    // 长度检查
    if (password.length < minLength) {
      errors.push(`密码长度至少需要${minLength}位`);
    }

    if (password.length > 128) {
      errors.push('密码长度不能超过128位');
    }

    // 小写字母检查
    if (requireLowercase && !/[a-z]/.test(password)) {
      errors.push('密码必须包含小写字母');
    }

    // 大写字母检查
    if (requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('密码必须包含大写字母');
    }

    // 数字检查
    if (requireNumbers && !/\d/.test(password)) {
      errors.push('密码必须包含数字');
    }

    // 特殊字符检查
    if (requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('密码必须包含特殊字符');
    }

    // 常见弱密码检查
    if (this.isCommonPassword(password)) {
      errors.push('密码过于简单，请使用更复杂的密码');
    }

    // 连续字符检查
    if (this.hasConsecutiveChars(password)) {
      errors.push('密码不能包含连续的字符或数字');
    }

    // 重复字符检查
    if (this.hasRepeatingChars(password)) {
      errors.push('密码不能包含过多重复字符');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 检查是否为常见弱密码
   */
  private isCommonPassword(password: string): boolean {
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', '12345678', '111111', '123123', 'admin',
      'letmein', 'welcome', 'monkey', '1234567890', 'dragon',
      'sunshine', 'princess', 'football', 'charlie', 'aa123456',
      'donald', 'password1', 'qwerty123'
    ];
    
    return commonPasswords.includes(password.toLowerCase());
  }

  /**
   * 检查是否有连续字符
   */
  private hasConsecutiveChars(password: string): boolean {
    for (let i = 0; i < password.length - 2; i++) {
      const char1 = password.charCodeAt(i);
      const char2 = password.charCodeAt(i + 1);
      const char3 = password.charCodeAt(i + 2);
      
      // 检查连续递增或递减
      if ((char2 === char1 + 1 && char3 === char2 + 1) ||
          (char2 === char1 - 1 && char3 === char2 - 1)) {
        return true;
      }
    }
    return false;
  }

  /**
   * 检查是否有过多重复字符
   */
  private hasRepeatingChars(password: string): boolean {
    const charCount = new Map<string, number>();
    
    for (const char of password) {
      charCount.set(char, (charCount.get(char) || 0) + 1);
    }
    
    // 如果任何字符重复超过密码长度的一半，认为是弱密码
    const maxRepeats = Math.floor(password.length / 2);
    return Array.from(charCount.values()).some(count => count > maxRepeats);
  }

  /**
   * 验证密码一致性
   */
  validatePasswordConfirmation(password: string, confirmPassword: string): void {
    if (password !== confirmPassword) {
      throw new BadRequestException('两次输入的密码不一致');
    }
  }

  /**
   * 验证当前密码
   */
  async validateCurrentPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }

  /**
   * 哈希密码
   */
  async hashPassword(password: string): Promise<string> {
    const saltRounds = this.configService.get<number>('auth.passwordPolicy.saltRounds', 12);
    return await bcrypt.hash(password, saltRounds);
  }

  /**
   * 生成安全的随机密码
   */
  generateSecurePassword(length: number = 12): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    const allChars = lowercase + uppercase + numbers + symbols;
    
    let password = '';
    
    // 确保包含每种类型的字符
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];
    
    // 填充剩余长度
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }
    
    // 打乱字符顺序
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }

  /**
   * 检查密码是否需要更新（基于密码年龄）
   */
  shouldUpdatePassword(lastPasswordChange: Date): boolean {
    const maxPasswordAge = this.configService.get<number>('auth.passwordPolicy.maxPasswordAgeDays', 90);
    const daysSinceChange = Math.floor((Date.now() - lastPasswordChange.getTime()) / (1000 * 60 * 60 * 24));
    
    return daysSinceChange >= maxPasswordAge;
  }

  /**
   * 检查密码是否与历史密码重复
   */
  async checkPasswordHistory(newPassword: string, passwordHistory: string[]): Promise<boolean> {
    for (const oldPassword of passwordHistory) {
      if (await bcrypt.compare(newPassword, oldPassword)) {
        return true; // 密码重复
      }
    }
    return false; // 密码不重复
  }

  /**
   * 获取密码强度评分
   */
  getPasswordStrength(password: string): {
    score: number;
    level: 'weak' | 'fair' | 'good' | 'strong';
    feedback: string[];
  } {
    let score = 0;
    const feedback: string[] = [];

    // 长度评分
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    if (password.length >= 16) score += 1;
    else if (password.length < 8) feedback.push('密码长度至少需要8位');

    // 字符类型评分
    if (/[a-z]/.test(password)) score += 1;
    else feedback.push('添加小写字母');

    if (/[A-Z]/.test(password)) score += 1;
    else feedback.push('添加大写字母');

    if (/\d/.test(password)) score += 1;
    else feedback.push('添加数字');

    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 1;
    else feedback.push('添加特殊字符');

    // 复杂性评分
    if (!this.isCommonPassword(password)) score += 1;
    else feedback.push('避免使用常见密码');

    if (!this.hasConsecutiveChars(password)) score += 1;
    else feedback.push('避免连续字符');

    // 确定强度等级
    let level: 'weak' | 'fair' | 'good' | 'strong';
    if (score <= 3) level = 'weak';
    else if (score <= 5) level = 'fair';
    else if (score <= 7) level = 'good';
    else level = 'strong';

    return { score, level, feedback };
  }
}
