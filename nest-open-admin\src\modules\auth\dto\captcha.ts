import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEmail, IsPhoneNumber, IsEnum, IsNumber, IsOptional, IsObject, IsArray } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 发送短信验证码DTO
 */
export class SendSmsCodeDto {
  @ApiProperty({ description: '手机号', example: '13800138000' })
  @IsString()
  @IsNotEmpty({ message: '手机号不能为空' })
  phone: string;

  @ApiProperty({ 
    description: '验证码类型', 
    enum: ['register', 'login', 'reset'], 
    example: 'register' 
  })
  @IsEnum(['register', 'login', 'reset'], { message: '验证码类型无效' })
  type: 'register' | 'login' | 'reset';
}

/**
 * 验证短信验证码DTO
 */
export class VerifySmsCodeDto {
  @ApiProperty({ description: '手机号', example: '13800138000' })
  @IsString()
  @IsNotEmpty({ message: '手机号不能为空' })
  phone: string;

  @ApiProperty({ description: '验证码', example: '123456' })
  @IsString()
  @IsNotEmpty({ message: '验证码不能为空' })
  code: string;

  @ApiProperty({ 
    description: '验证码类型', 
    enum: ['register', 'login', 'reset'], 
    example: 'register' 
  })
  @IsEnum(['register', 'login', 'reset'], { message: '验证码类型无效' })
  type: 'register' | 'login' | 'reset';
}

/**
 * 发送邮箱验证码DTO
 */
export class SendEmailCodeDto {
  @ApiProperty({ description: '邮箱地址', example: '<EMAIL>' })
  @IsEmail({}, { message: '邮箱格式不正确' })
  @IsNotEmpty({ message: '邮箱不能为空' })
  email: string;

  @ApiProperty({ 
    description: '验证码类型', 
    enum: ['register', 'login', 'reset'], 
    example: 'register' 
  })
  @IsEnum(['register', 'login', 'reset'], { message: '验证码类型无效' })
  type: 'register' | 'login' | 'reset';

  @ApiPropertyOptional({ 
    description: '设备指纹信息'
  })
  @IsOptional()
  @IsObject()
  fingerprint?: {
    id: string;
    confidence: number;
    components: Record<string, any>;
  };

  @ApiPropertyOptional({ 
    description: '行为模式数据'
  })
  @IsOptional()
  @IsObject()
  behavior?: {
    mouseMovements: Array<{ x: number; y: number; timestamp: number }>;
    keystrokes: Array<{ key: string; timestamp: number; duration: number }>;
    clickPatterns: Array<{ x: number; y: number; timestamp: number }>;
    scrollBehavior: Array<{ scrollY: number; timestamp: number }>;
    focusEvents: Array<{ element: string; timestamp: number; duration: number }>;
    pageInteractions: Array<{ type: string; target: string; timestamp: number }>;
  };
}

/**
 * 验证邮箱验证码DTO
 */
export class VerifyEmailCodeDto {
  @ApiProperty({ description: '邮箱地址', example: '<EMAIL>' })
  @IsEmail({}, { message: '邮箱格式不正确' })
  @IsNotEmpty({ message: '邮箱不能为空' })
  email: string;

  @ApiProperty({ description: '验证码', example: '123456' })
  @IsString()
  @IsNotEmpty({ message: '验证码不能为空' })
  code: string;

  @ApiProperty({ 
    description: '验证码类型', 
    enum: ['register', 'login', 'reset'], 
    example: 'register' 
  })
  @IsEnum(['register', 'login', 'reset'], { message: '验证码类型无效' })
  type: 'register' | 'login' | 'reset';
}

/**
 * 验证图像验证码DTO
 */
export class VerifyImageCaptchaDto {
  @ApiProperty({ description: '验证码ID', example: 'uuid-string' })
  @IsString()
  @IsNotEmpty({ message: '验证码ID不能为空' })
  captchaId: string;

  @ApiProperty({ description: '验证码内容', example: 'abcd' })
  @IsString()
  @IsNotEmpty({ message: '验证码内容不能为空' })
  captchaCode: string;
}

/**
 * 验证滑动拼图验证码DTO
 */
export class VerifySlideVerifyDto {
  @ApiProperty({ description: '移动距离', example: 120 })
  @IsNumber({}, { message: '移动距离必须是数字' })
  moveDistance: number;

  @ApiProperty({ description: '移动耗时(毫秒)', example: 2500 })
  @IsNumber({}, { message: '移动耗时必须是数字' })
  duration: number;

  @ApiPropertyOptional({ description: '设备指纹' })
  @IsOptional()
  deviceFingerprint?: any;

  @ApiPropertyOptional({ description: '行为模式' })
  @IsOptional()
  behaviorPattern?: any;

  @ApiPropertyOptional({ description: '用户代理' })
  @IsOptional()
  @IsString()
  userAgent?: string;

  @ApiPropertyOptional({ description: '屏幕分辨率' })
  @IsOptional()
  @IsString()
  screenResolution?: string;

  @ApiPropertyOptional({ description: '时区' })
  @IsOptional()
  @IsString()
  timezone?: string;

  @ApiPropertyOptional({ description: '语言' })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiPropertyOptional({ description: '时间戳' })
  @IsOptional()
  @IsNumber()
  timestamp?: number;

  @ApiPropertyOptional({ description: '会话ID' })
  @IsOptional()
  @IsString()
  sessionId?: string;

  @ApiPropertyOptional({ description: '客户端IP' })
  @IsOptional()
  @IsString()
  clientIP?: string;

  @ApiPropertyOptional({ description: '浏览器指纹' })
  @IsOptional()
  @IsString()
  browserFingerprint?: string;

  @ApiPropertyOptional({ description: '鼠标轨迹' })
  @IsOptional()
  @IsArray()
  mouseTrajectory?: any[];

  @ApiPropertyOptional({ description: '键盘模式' })
  @IsOptional()
  @IsArray()
  keyboardPattern?: any[];

  @ApiPropertyOptional({ description: '触摸模式' })
  @IsOptional()
  @IsArray()
  touchPattern?: any[];
}

/**
 * 验证reCAPTCHA DTO
 */
export class VerifyRecaptchaDto {
  @ApiProperty({ description: 'reCAPTCHA令牌', example: 'recaptcha-token-string' })
  @IsString()
  @IsNotEmpty({ message: 'reCAPTCHA令牌不能为空' })
  recaptchaToken: string;

  @ApiProperty({ 
    description: '操作类型', 
    enum: ['login', 'register', 'submit'], 
    example: 'login' 
  })
  @IsEnum(['login', 'register', 'submit'], { message: '操作类型无效' })
  action: 'login' | 'register' | 'submit';
}

/**
 * 拼图验证码数据接口
 */
export interface PuzzleCaptchaData {
  captchaId: string;
  moveDistance: number;
  duration: number;
  trajectory: Array<{ x: number; y: number; t: number }>;
}

/**
 * 企业级多重验证DTO
 */
export class EnterpriseCaptchaVerifyDto {
  @ApiProperty({ 
    description: '拼图验证码数据'
  })
  @IsObject()
  @IsNotEmpty({ message: '拼图验证码数据不能为空' })
  puzzleCaptcha: PuzzleCaptchaData;

  @ApiProperty({ description: 'reCAPTCHA令牌', example: 'recaptcha-token-string' })
  @IsString()
  @IsNotEmpty({ message: 'reCAPTCHA令牌不能为空' })
  recaptchaToken: string;

  @ApiProperty({ 
    description: '操作类型', 
    enum: ['login', 'register', 'submit'], 
    example: 'login' 
  })
  @IsEnum(['login', 'register', 'submit'], { message: '操作类型无效' })
  action: 'login' | 'register' | 'submit';
}