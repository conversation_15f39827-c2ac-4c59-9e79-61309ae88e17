import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { OcrController } from './ocr.controller';
import { OcrService } from './ocr.service';
import { OcrEntity } from './entities/ocr.entity';
import { ConfigModule } from '@nestjs/config';
import LogisticsParser from './logisticsParser';
import { OcrExecutorService } from './services/ocr-executor.service';
import { CallRecordModule } from '../call-record/call-record.module';

/**
 * OCR模块
 * 提供OCR识别相关功能
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([OcrEntity]),
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 3,
    }),
    ConfigModule,
    forwardRef(() => CallRecordModule),
  ],
  controllers: [OcrController],
  providers: [
    OcrService,
    LogisticsParser,
    OcrExecutorService,
  ],
  exports: [
    OcrService,
    OcrExecutorService,
    LogisticsParser,
  ],
})
export class OcrModule {}