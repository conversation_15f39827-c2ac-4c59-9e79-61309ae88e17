import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Api<PERSON>eyController } from './api-key.controller';
import { ApiKeyService } from './services/api-key.service';
import { ApiKeyCacheService } from './services/api-key-cache.service';
import { ApiKeyEntity } from './entities/api-key.entity';
import { CallRecordModule } from '../call-record/call-record.module';
import { LayeredCacheService } from '../../shared/layered-cache.service';
import { SharedModule } from '../../shared/shared.module';

/**
 * API密钥模块
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([ApiKeyEntity]),
    CallRecordModule,
    SharedModule
  ],
  controllers: [ApiKeyController],
  providers: [
    ApiKeyService,
    ApiKeyCacheService,
    LayeredCacheService
  ],
  exports: [
    ApiKeyService,
    ApiKeyCacheService
  ]
})
export class ApiKeyModule {}