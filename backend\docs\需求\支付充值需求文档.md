# 支付充值需求文档

## 1. 文档概述

本文档描述了开放平台支付充值模块的需求规范，该模块基于单一职责原则设计，专注于用户充值和服务次数购买功能。支付充值模块负责处理用户的充值请求、支付过程追踪和支付结果处理，支持多种支付方式，并与用户服务模块和订单模块协作，确保用户可以便捷地购买API调用次数。

## 2. 功能需求

### 2.1 核心功能

- **账户余额充值**
  - 用户可以向账户充值指定金额
  - 生成充值订单并跳转到支付页面
  - 处理支付回调并更新账户余额
  - 支持多种支付方式（支付宝、微信支付）

- **服务次数直接购买**
  - 用户可以直接购买指定服务的调用次数
  - 生成购买订单并跳转到支付页面
  - 处理支付回调并增加服务调用次数
  - 支持多种支付方式（支付宝、微信支付）

- **余额购买服务次数**
  - 用户可以使用账户余额购买服务调用次数
  - 验证余额充足性
  - 执行余额扣减和调用次数增加
  - 生成内部订单记录

- **支付记录查询**
  - 按用户查询支付历史
  - 按订单查询支付状态
  - 按时间范围查询支付记录

### 2.2 非功能需求

- **安全性**：支付过程安全可靠，防止欺诈和重复支付
- **可靠性**：支付处理高可用，确保支付状态一致性
- **高性能**：支付处理响应时间<1s，支付回调处理时间<2s
- **可扩展性**：支持便捷添加新的支付渠道
- **可追溯性**：完整的支付日志和审计记录
- **合规性**：符合支付行业规范和安全标准

## 3. 技术规范

### 3.1 支付模型

```typescript
// 简化示意
export enum PaymentMethod {
  ALIPAY = 'alipay',
  WECHAT = 'wechat',
  BALANCE = 'balance'
}

export enum PaymentType {
  RECHARGE = 'recharge',  // 充值到余额
  PURCHASE = 'purchase'   // 直接购买服务次数
}

export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REFUNDED = 'refunded',
  CANCELLED = 'cancelled'
}

@Entity('payments')
export class PaymentEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: number;

  @Column()
  orderId: string;

  @Column()
  amount: number;

  @Column({ type: 'enum', enum: PaymentMethod })
  method: PaymentMethod;

  @Column({ type: 'enum', enum: PaymentType })
  type: PaymentType;

  @Column({ type: 'enum', enum: PaymentStatus, default: PaymentStatus.PENDING })
  status: PaymentStatus;

  @Column({ nullable: true })
  serviceId?: number; // 购买的服务ID，充值时为null

  @Column({ nullable: true })
  count?: number; // 购买的次数，充值时为null

  @Column({ nullable: true })
  tradeNo?: string; // 支付平台交易号

  @Column({ type: 'jsonb', nullable: true })
  paymentDetails?: Record<string, any>; // 支付详情，存储支付平台返回的数据

  @Column()
  createdAt: Date;

  @Column({ nullable: true })
  completedAt: Date;
}
```

### 3.2 支付流程处理

```typescript
// 简化示意
@Injectable()
export class PaymentService {
  constructor(
    @InjectRepository(PaymentEntity)
    private paymentRepository: Repository<PaymentEntity>,
    private orderService: OrderService,
    private userService: UserService,
    private userServiceService: UserServiceService,
    private alipayService: AlipayService,
    private wechatPayService: WechatPayService,
    private logger: LoggerService,
    private eventEmitter: EventEmitter2
  ) {}

  /**
   * 创建充值支付
   */
  async createRechargePayment(
    userId: number,
    amount: number,
    method: PaymentMethod
  ): Promise<PaymentResponseDto> {
    // 创建订单
    const order = await this.orderService.createOrder({
      userId,
      type: OrderType.RECHARGE,
      amount,
      description: `账户充值 ¥${amount}`
    });

    // 创建支付记录
    const payment = await this.paymentRepository.save({
      userId,
      orderId: order.id,
      amount,
      method,
      type: PaymentType.RECHARGE,
      status: PaymentStatus.PENDING,
      createdAt: new Date()
    });

    // 根据支付方式获取支付链接
    let paymentUrl: string;
    let formData: Record<string, any> | null = null;

    if (method === PaymentMethod.ALIPAY) {
      paymentUrl = await this.alipayService.createPayment(payment.id, amount, `账户充值 ¥${amount}`);
    } else if (method === PaymentMethod.WECHAT) {
      const result = await this.wechatPayService.createPayment(payment.id, amount, `账户充值 ¥${amount}`);
      paymentUrl = result.url;
      formData = result.formData;
    }

    this.logger.log(
      `创建充值支付 - 用户ID: ${userId}, 金额: ${amount}, 支付方式: ${method}, 支付ID: ${payment.id}`,
      'PaymentService'
    );

    return {
      paymentId: payment.id,
      orderId: order.id,
      paymentUrl,
      formData,
      qrCodeUrl: method === PaymentMethod.WECHAT ? paymentUrl : null
    };
  }

  /**
   * 创建服务次数购买支付
   */
  async createServicePurchasePayment(
    userId: number,
    serviceId: number,
    count: number,
    method: PaymentMethod
  ): Promise<PaymentResponseDto> {
    // 获取服务信息和价格
    const service = await this.userServiceService.getServiceById(serviceId);
    const amount = service.unitPrice * count;

    // 创建订单
    const order = await this.orderService.createOrder({
      userId,
      type: OrderType.SERVICE_PURCHASE,
      amount,
      description: `购买${service.name} ${count}次调用`,
      metadata: {
        serviceId,
        count,
        unitPrice: service.unitPrice
      }
    });

    // 创建支付记录
    const payment = await this.paymentRepository.save({
      userId,
      orderId: order.id,
      amount,
      method,
      type: PaymentType.PURCHASE,
      status: PaymentStatus.PENDING,
      serviceId,
      count,
      createdAt: new Date()
    });

    // 余额支付直接处理
    if (method === PaymentMethod.BALANCE) {
      return await this.processBalancePayment(payment.id);
    }

    // 第三方支付处理
    let paymentUrl: string;
    let formData: Record<string, any> | null = null;

    if (method === PaymentMethod.ALIPAY) {
      paymentUrl = await this.alipayService.createPayment(
        payment.id, 
        amount, 
        `购买${service.name} ${count}次调用`
      );
    } else if (method === PaymentMethod.WECHAT) {
      const result = await this.wechatPayService.createPayment(
        payment.id, 
        amount, 
        `购买${service.name} ${count}次调用`
      );
      paymentUrl = result.url;
      formData = result.formData;
    }

    this.logger.log(
      `创建服务购买支付 - 用户ID: ${userId}, 服务ID: ${serviceId}, 次数: ${count}, 金额: ${amount}, 支付方式: ${method}, 支付ID: ${payment.id}`,
      'PaymentService'
    );

    return {
      paymentId: payment.id,
      orderId: order.id,
      paymentUrl,
      formData,
      qrCodeUrl: method === PaymentMethod.WECHAT ? paymentUrl : null
    };
  }

  /**
   * 处理支付回调
   */
  async handlePaymentCallback(
    paymentId: string,
    tradeNo: string,
    status: string,
    paymentDetails: Record<string, any>
  ): Promise<void> {
    // 获取支付记录
    const payment = await this.paymentRepository.findOne({ where: { id: paymentId } });
    
    if (!payment) {
      throw new NotFoundException(`支付记录 ${paymentId} 不存在`);
    }
    
    // 避免重复处理
    if (payment.status === PaymentStatus.COMPLETED) {
      this.logger.warn(
        `支付回调重复处理 - 支付ID: ${paymentId}, 交易号: ${tradeNo}`,
        'PaymentService'
      );
      return;
    }

    // 更新支付状态和详情
    payment.tradeNo = tradeNo;
    payment.paymentDetails = paymentDetails;
    
    if (status === 'success') {
      payment.status = PaymentStatus.COMPLETED;
      payment.completedAt = new Date();
      await this.paymentRepository.save(payment);
      
      // 处理支付成功后的业务逻辑
      await this.processPaymentSuccess(payment);
    } else {
      payment.status = PaymentStatus.FAILED;
      await this.paymentRepository.save(payment);
      
      // 更新订单状态
      await this.orderService.updateOrderStatus(payment.orderId, OrderStatus.PAYMENT_FAILED);
    }

    // 发送支付完成事件
    this.eventEmitter.emit('payment.completed', {
      paymentId: payment.id,
      userId: payment.userId,
      status: payment.status
    });
  }

  /**
   * 处理支付成功的业务逻辑
   */
  private async processPaymentSuccess(payment: PaymentEntity): Promise<void> {
    // 更新订单状态
    await this.orderService.updateOrderStatus(payment.orderId, OrderStatus.PAID);
    
    if (payment.type === PaymentType.RECHARGE) {
      // 充值到余额
      await this.userService.addBalance(payment.userId, payment.amount);
      
      this.logger.log(
        `充值成功 - 用户ID: ${payment.userId}, 金额: ${payment.amount}, 支付ID: ${payment.id}`,
        'PaymentService'
      );
    } else if (payment.type === PaymentType.PURCHASE) {
      // 购买服务次数
      await this.userServiceService.addServiceCount(
        payment.userId,
        payment.serviceId,
        payment.count
      );
      
      this.logger.log(
        `服务次数购买成功 - 用户ID: ${payment.userId}, 服务ID: ${payment.serviceId}, 次数: ${payment.count}, 支付ID: ${payment.id}`,
        'PaymentService'
      );
    }
  }

  /**
   * 处理余额支付
   */
  private async processBalancePayment(paymentId: string): Promise<PaymentResponseDto> {
    // 获取支付记录
    const payment = await this.paymentRepository.findOne({ where: { id: paymentId } });
    
    if (!payment) {
      throw new NotFoundException(`支付记录 ${paymentId} 不存在`);
    }

    // 检查余额是否充足
    const user = await this.userService.getUserById(payment.userId);
    
    if (user.balance < payment.amount) {
      // 更新支付状态为失败
      payment.status = PaymentStatus.FAILED;
      await this.paymentRepository.save(payment);
      
      // 更新订单状态
      await this.orderService.updateOrderStatus(payment.orderId, OrderStatus.PAYMENT_FAILED);
      
      throw new BadRequestException('账户余额不足');
    }

    // 扣减余额
    await this.userService.deductBalance(payment.userId, payment.amount);
    
    // 更新支付状态为完成
    payment.status = PaymentStatus.COMPLETED;
    payment.completedAt = new Date();
    await this.paymentRepository.save(payment);
    
    // 处理支付成功后的业务逻辑
    await this.processPaymentSuccess(payment);
    
    // 发送支付完成事件
    this.eventEmitter.emit('payment.completed', {
      paymentId: payment.id,
      userId: payment.userId,
      status: payment.status
    });
    
    return {
      paymentId: payment.id,
      orderId: payment.orderId,
      status: PaymentStatus.COMPLETED,
      message: '支付成功'
    };
  }
}
```

### 3.3 支付宝集成

```typescript
// 简化示意
@Injectable()
export class AlipayService {
  private alipay: any;
  
  constructor(
    private configService: ConfigService,
    private logger: LoggerService
  ) {
    // 初始化支付宝SDK
    this.alipay = new AlipaySdk({
      appId: this.configService.get<string>('ALIPAY_APP_ID'),
      privateKey: this.configService.get<string>('ALIPAY_PRIVATE_KEY'),
      alipayPublicKey: this.configService.get<string>('ALIPAY_PUBLIC_KEY'),
      gateway: this.configService.get<string>('ALIPAY_GATEWAY', 'https://openapi.alipay.com/gateway.do'),
      signType: 'RSA2'
    });
  }
  
  /**
   * 创建支付宝支付
   */
  async createPayment(paymentId: string, amount: number, subject: string): Promise<string> {
    const notifyUrl = this.configService.get<string>('ALIPAY_NOTIFY_URL');
    const returnUrl = this.configService.get<string>('ALIPAY_RETURN_URL');
    
    const formData = {
      bizContent: {
        outTradeNo: paymentId,
        productCode: 'FAST_INSTANT_TRADE_PAY',
        totalAmount: amount.toFixed(2),
        subject: subject,
        body: subject
      },
      notifyUrl,
      returnUrl
    };
    
    try {
      const result = await this.alipay.pageExec('alipay.trade.page.pay', formData);
      return result;
    } catch (error) {
      this.logger.error(
        `支付宝支付创建失败 - ${error.message}`,
        error.stack,
        'AlipayService'
      );
      throw new InternalServerErrorException('创建支付宝支付失败');
    }
  }
  
  /**
   * 验证支付宝回调
   */
  async verifyCallback(params: Record<string, string>): Promise<{
    verified: boolean;
    paymentId?: string;
    tradeNo?: string;
    status?: string;
  }> {
    try {
      const signVerified = this.alipay.checkNotifySign(params);
      
      if (!signVerified) {
        return { verified: false };
      }
      
      const paymentId = params.out_trade_no;
      const tradeNo = params.trade_no;
      const tradeStatus = params.trade_status;
      
      let status = 'unknown';
      
      if (tradeStatus === 'TRADE_SUCCESS' || tradeStatus === 'TRADE_FINISHED') {
        status = 'success';
      } else if (tradeStatus === 'TRADE_CLOSED') {
        status = 'closed';
      } else {
        status = 'pending';
      }
      
      return {
        verified: true,
        paymentId,
        tradeNo,
        status
      };
    } catch (error) {
      this.logger.error(
        `支付宝回调验证失败 - ${error.message}`,
        error.stack,
        'AlipayService'
      );
      return { verified: false };
    }
  }
}
```

### 3.4 微信支付集成

```typescript
// 简化示意
@Injectable()
export class WechatPayService {
  private payConfig: any;
  
  constructor(
    private configService: ConfigService,
    private logger: LoggerService
  ) {
    // 初始化微信支付配置
    this.payConfig = {
      appId: this.configService.get<string>('WECHAT_APP_ID'),
      mchId: this.configService.get<string>('WECHAT_MCH_ID'),
      apiKey: this.configService.get<string>('WECHAT_API_KEY'),
      pfx: fs.readFileSync(this.configService.get<string>('WECHAT_CERT_PATH'))
    };
  }
  
  /**
   * 创建微信支付
   */
  async createPayment(
    paymentId: string, 
    amount: number, 
    subject: string
  ): Promise<{ url: string; formData: Record<string, any> }> {
    const notifyUrl = this.configService.get<string>('WECHAT_NOTIFY_URL');
    
    // 微信支付金额单位是分，需要转换
    const totalFee = Math.round(amount * 100);
    
    const params = {
      appid: this.payConfig.appId,
      mch_id: this.payConfig.mchId,
      nonce_str: this.generateNonceStr(),
      body: subject,
      out_trade_no: paymentId,
      total_fee: totalFee,
      spbill_create_ip: '127.0.0.1',
      notify_url: notifyUrl,
      trade_type: 'NATIVE' // 原生扫码支付
    };
    
    // 添加签名
    params.sign = this.generateSign(params);
    
    try {
      const result = await this.requestWechatPay('/pay/unifiedorder', params);
      
      if (result.return_code !== 'SUCCESS' || result.result_code !== 'SUCCESS') {
        throw new Error(`微信支付请求失败: ${result.return_msg || result.err_code_des}`);
      }
      
      return {
        url: result.code_url, // 用于生成二维码的URL
        formData: {
          appId: this.payConfig.appId,
          timeStamp: String(Math.floor(Date.now() / 1000)),
          nonceStr: this.generateNonceStr(),
          package: `prepay_id=${result.prepay_id}`,
          signType: 'MD5'
        }
      };
    } catch (error) {
      this.logger.error(
        `微信支付创建失败 - ${error.message}`,
        error.stack,
        'WechatPayService'
      );
      throw new InternalServerErrorException('创建微信支付失败');
    }
  }
  
  /**
   * 验证微信支付回调
   */
  async verifyCallback(xml: string): Promise<{
    verified: boolean;
    paymentId?: string;
    tradeNo?: string;
    status?: string;
  }> {
    try {
      const result = await this.parseXml(xml);
      
      if (!result || result.return_code !== 'SUCCESS') {
        return { verified: false };
      }
      
      // 验证签名
      const sign = result.sign;
      delete result.sign;
      const calculatedSign = this.generateSign(result);
      
      if (sign !== calculatedSign) {
        return { verified: false };
      }
      
      const paymentId = result.out_trade_no;
      const tradeNo = result.transaction_id;
      const tradeState = result.result_code;
      
      let status = 'unknown';
      
      if (tradeState === 'SUCCESS') {
        status = 'success';
      } else {
        status = 'failed';
      }
      
      return {
        verified: true,
        paymentId,
        tradeNo,
        status
      };
    } catch (error) {
      this.logger.error(
        `微信支付回调验证失败 - ${error.message}`,
        error.stack,
        'WechatPayService'
      );
      return { verified: false };
    }
  }
  
  /**
   * 生成随机字符串
   */
  private generateNonceStr(length = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }
  
  /**
   * 生成签名
   */
  private generateSign(params: Record<string, any>): string {
    // 按ASCII码排序参数
    const sortedParams = {};
    Object.keys(params)
      .sort()
      .forEach(key => {
        if (params[key] !== undefined && params[key] !== '') {
          sortedParams[key] = params[key];
        }
      });
    
    // 拼接字符串
    let stringA = '';
    Object.keys(sortedParams).forEach(key => {
      stringA += `${key}=${sortedParams[key]}&`;
    });
    
    // 添加key
    const stringSignTemp = `${stringA}key=${this.payConfig.apiKey}`;
    
    // MD5并转大写
    return crypto
      .createHash('md5')
      .update(stringSignTemp)
      .digest('hex')
      .toUpperCase();
  }
  
  // 省略请求微信支付API和XML解析等辅助方法
}
```

## 4. 系统架构

### 4.1 模块结构

```
payment/
├── dto/
│   ├── payment-request.dto.ts        # 支付请求DTO
│   ├── payment-response.dto.ts       # 支付响应DTO
│   └── payment-callback.dto.ts       # 支付回调DTO
├── entities/
│   └── payment.entity.ts             # 支付实体
├── services/
│   ├── payment.service.ts            # 支付核心服务
│   ├── alipay.service.ts             # 支付宝服务
│   └── wechat-pay.service.ts         # 微信支付服务
├── controllers/
│   ├── payment.controller.ts         # 支付控制器
│   └── payment-callback.controller.ts # 支付回调控制器
├── payment.module.ts                 # 支付模块定义
└── payment-status.enum.ts            # 支付状态枚举
```

### 4.2 支付流程图

```
graph TD
    A[用户发起支付请求] --> B{支付类型}
    B -->|充值| C[创建充值订单]
    B -->|服务购买| D[创建服务购买订单]
    
    C --> E{选择支付方式}
    D --> E
    
    E -->|支付宝| F1[创建支付宝支付]
    E -->|微信| F2[创建微信支付]
    E -->|余额| F3[处理余额支付]
    
    F1 --> G1[跳转支付宝支付页面]
    F2 --> G2[生成微信支付二维码]
    F3 --> H{余额是否充足}
    
    H -->|是| I1[扣减余额]
    H -->|否| I2[返回余额不足]
    
    G1 --> J[用户完成第三方支付]
    G2 --> J
    I1 --> K[支付完成]
    
    J --> L[支付平台回调通知]
    L --> M[验证支付回调]
    M --> N{支付是否成功}
    
    N -->|是| O1[更新支付状态为成功]
    N -->|否| O2[更新支付状态为失败]
    
    O1 --> P{支付类型}
    P -->|充值| Q1[增加账户余额]
    P -->|服务购买| Q2[增加服务调用次数]
    
    Q1 --> R[完成支付流程]
    Q2 --> R
    O2 --> R
    I2 --> R
```

## 5. 接口定义

### 5.1 创建充值支付

```
POST /payments/recharge
Content-Type: application/json
Authorization: Bearer <token>

Request:
{
  "amount": 100,
  "method": "alipay"  // "alipay" 或 "wechat"
}

Response:
{
  "paymentId": "550e8400-e29b-41d4-a716-446655440000",
  "orderId": "ord-a1b2c3d4e5f6",
  "paymentUrl": "https://openapi.alipay.com/gateway.do?...",
  "formData": null,
  "qrCodeUrl": null
}
```

### 5.2 创建服务购买支付

```
POST /payments/purchase
Content-Type: application/json
Authorization: Bearer <token>

Request:
{
  "serviceId": 1,
  "count": 1000,
  "method": "wechat"  // "alipay", "wechat" 或 "balance"
}

Response:
{
  "paymentId": "550e8400-e29b-41d4-a716-446655440001",
  "orderId": "ord-b2c3d4e5f6g7",
  "paymentUrl": "weixin://wxpay/bizpayurl?pr=XZF1bfP",
  "formData": {
    "appId": "wx123456789",
    "timeStamp": "1633456789",
    "nonceStr": "abcdef123456",
    "package": "prepay_id=wx123456789abcdef",
    "signType": "MD5"
  },
  "qrCodeUrl": "weixin://wxpay/bizpayurl?pr=XZF1bfP"
}
```

### 5.3 余额购买服务

```
POST /payments/balance-purchase
Content-Type: application/json
Authorization: Bearer <token>

Request:
{
  "serviceId": 1,
  "count": 500
}

Response:
{
  "paymentId": "550e8400-e29b-41d4-a716-446655440002",
  "orderId": "ord-c3d4e5f6g7h8",
  "status": "completed",
  "message": "支付成功"
}
```

### 5.4 支付宝回调接口

```
POST /payments/callbacks/alipay
Content-Type: application/x-www-form-urlencoded

Response:
"success"  // 成功处理回调后必须返回字符串 "success"
```

### 5.5 微信支付回调接口

```
POST /payments/callbacks/wechat
Content-Type: text/xml

Response:
<xml>
  <return_code><![CDATA[SUCCESS]]></return_code>
  <return_msg><![CDATA[OK]]></return_msg>
</xml>
```

## 6. 实现要点

### 6.1 支付安全

- **签名验证**：对所有支付请求和回调进行数字签名验证
- **支付状态一致性**：使用事务确保支付状态和业务操作的原子性
- **防重放攻击**：记录并检查支付回调的唯一性，避免重复处理
- **敏感信息保护**：支付凭证和密钥信息加密存储

### 6.2 并发处理

- **支付回调幂等性**：确保同一支付回调多次处理也不会导致多次业务操作
- **余额扣减原子性**：使用数据库事务和悲观锁确保余额扣减的原子性
- **状态更新一致性**：确保支付状态、订单状态和余额更新的一致性

### 6.3 支付集成最佳实践

- **独立的支付服务**：将支付逻辑封装在专门的服务中，与业务逻辑解耦
- **统一的回调处理**：所有支付方式使用统一的回调处理流程
- **异步事件通知**：支付完成后通过事件通知相关模块进行业务处理
- **支付超时处理**：定期检查未完成的支付并处理超时情况

## 7. 与其他模块的集成

### 7.1 与订单模块集成

- 支付创建时，调用订单模块创建相应订单
- 支付状态变更时，更新订单状态
- 从订单模块获取订单详情用于支付描述

### 7.2 与用户服务模块集成

- 支付成功后，调用用户服务模块增加账户余额或服务调用次数
- 使用余额支付时，验证用户余额并执行扣减
- 查询用户服务使用情况和余额信息

## 8. 后续优化方向

- **多币种支持**：扩展支持多种货币的支付处理
- **订阅计费模式**：添加服务订阅模式，定期自动扣款
- **优惠券和折扣系统**：支持各类促销活动和折扣应用
- **发票管理**：支持电子发票和纸质发票管理
- **支付风控系统**：建立风险控制机制，识别和防范欺诈行为
- **更多支付方式**：集成更多支付渠道，如银联、信用卡等
- **支付流程优化**：优化支付体验，减少支付步骤
- **支付数据分析**：提供支付数据分析和报告功能 