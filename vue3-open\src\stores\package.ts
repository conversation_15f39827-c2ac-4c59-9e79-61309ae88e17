import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import { request } from '../utils/request'
import type {
  Package,
  UserPackage,
  PackageStats,
  PackageQueryParams,
  CreatePackageForm,
  PurchasePackageForm,
  PackageRecommendation,
  PackageUsage
} from '../types/package'

export const usePackageStore = defineStore('package', () => {
  // 状态
  const loading = ref(false)
  const packages = ref<Package[]>([])
  const userPackages = ref<UserPackage[]>([])
  const currentPackage = ref<Package | null>(null)
  const stats = ref<PackageStats>({
    totalPackages: 0,
    activePackages: 0,
    soldPackages: 0,
    totalRevenue: 0,
    monthlyRevenue: 0,
    popularPackages: [],
    recentSales: []
  })
  const recommendations = ref<PackageRecommendation[]>([])
  const usageData = ref<PackageUsage[]>([])
  const pagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })
  const lastUpdated = ref<Date | null>(null)

  // 计算属性
  const activePackages = computed(() => 
    packages.value.filter(pkg => pkg.status === 'active')
  )
  
  const popularPackages = computed(() => 
    packages.value
      .filter(pkg => pkg.isPopular)
      .sort((a, b) => b.salesCount - a.salesCount)
  )
  
  const recommendedPackages = computed(() => 
    packages.value.filter(pkg => pkg.isRecommended)
  )
  
  const activeUserPackages = computed(() => 
    userPackages.value.filter(pkg => pkg.status === 'active')
  )
  
  const hasData = computed(() => packages.value.length > 0)

  // 获取套餐包列表
  const fetchPackages = async (params?: PackageQueryParams) => {
    try {
      loading.value = true
      const queryParams = {
        page: pagination.value.page,
        pageSize: pagination.value.pageSize,
        ...params
      }
      
      const response: Record<string, any> = await request.get('/op/packages', { params: queryParams })
      
      packages.value = response.records || response.data || []
      pagination.value.total = response.total || 0
      lastUpdated.value = new Date()
      
      return response
    } catch (error) {
      console.error('获取套餐包列表失败:', error)
      ElMessage.error('获取套餐包列表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取套餐包详情
  const fetchPackageDetail = async (packageId: string) => {
    try {
      loading.value = true
      const response: Record<string, any> = await request.get(`/op/packages/${packageId}`)
      currentPackage.value = response as any
      return response
    } catch (error) {
      console.error('获取套餐包详情失败:', error)
      ElMessage.error('获取套餐包详情失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取用户套餐包
  const fetchUserPackages = async () => {
    try {
      const response: Record<string, any> = await request.get('/op/user/packages')
      userPackages.value = response.records || response.data || []
      return response
    } catch (error) {
      console.error('获取用户套餐包失败:', error)
      ElMessage.error('获取用户套餐包失败')
      throw error
    }
  }

  // 获取套餐包统计
  const fetchPackageStats = async (timeRange?: { start: string; end: string }) => {
    try {
      const response: Record<string, any> = await request.get('/op/packages/stats', {
        params: timeRange
      })
      stats.value = response as any
      return response
    } catch (error) {
      console.error('获取套餐包统计失败:', error)
      throw error
    }
  }

  // 获取套餐包推荐
  const fetchRecommendations = async (userId?: string) => {
    try {
      const response: Record<string, any> = await request.get('/op/packages/recommendations', {
        params: { userId }
      })
      recommendations.value = response as any
      return response
    } catch (error) {
      console.error('获取套餐包推荐失败:', error)
      throw error
    }
  }

  // 获取套餐包使用情况
  const fetchPackageUsage = async (packageId?: string) => {
    try {
      const response: Record<string, any> = await request.get('/op/user/packages/usage', {
        params: { packageId }
      })
      usageData.value = response as any
      return response
    } catch (error) {
      console.error('获取套餐包使用情况失败:', error)
      throw error
    }
  }

  // 创建套餐包（管理员功能）
  const createPackage = async (form: CreatePackageForm) => {
    try {
      loading.value = true
      const response = await request.post('/op/packages', form)
      ElMessage.success('套餐包创建成功')
      await fetchPackages() // 刷新列表
      return response
    } catch (error) {
      console.error('创建套餐包失败:', error)
      ElMessage.error('创建套餐包失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新套餐包（管理员功能）
  const updatePackage = async (packageId: string, updateData: Partial<Package>) => {
    try {
      loading.value = true
      const response = await request.put(`/op/packages/${packageId}`, updateData)
      ElMessage.success('套餐包更新成功')
      await fetchPackages() // 刷新列表
      return response
    } catch (error) {
      console.error('更新套餐包失败:', error)
      ElMessage.error('更新套餐包失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 购买套餐包
  const purchasePackage = async (form: PurchasePackageForm) => {
    try {
      loading.value = true
      const response = await request.post('/op/packages/purchase', form)
      ElMessage.success('套餐包购买成功')
      await fetchUserPackages() // 刷新用户套餐包列表
      return response
    } catch (error) {
      console.error('购买套餐包失败:', error)
      ElMessage.error('购买套餐包失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 取消套餐包
  const cancelPackage = async (userPackageId: string, reason?: string) => {
    try {
      loading.value = true
      const response = await request.post(`/op/user/packages/${userPackageId}/cancel`, {
        reason
      })
      ElMessage.success('套餐包已取消')
      await fetchUserPackages() // 刷新用户套餐包列表
      return response
    } catch (error) {
      console.error('取消套餐包失败:', error)
      ElMessage.error('取消套餐包失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 续费套餐包
  const renewPackage = async (userPackageId: string, paymentMethod: string) => {
    try {
      loading.value = true
      const response = await request.post(`/op/user/packages/${userPackageId}/renew`, {
        paymentMethod
      })
      ElMessage.success('套餐包续费成功')
      await fetchUserPackages() // 刷新用户套餐包列表
      return response
    } catch (error) {
      console.error('续费套餐包失败:', error)
      ElMessage.error('续费套餐包失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 设置自动续费
  const setAutoRenew = async (userPackageId: string, autoRenew: boolean) => {
    try {
      const response = await request.put(`/op/user/packages/${userPackageId}/auto-renew`, {
        autoRenew
      })
      ElMessage.success(`已${autoRenew ? '开启' : '关闭'}自动续费`)
      await fetchUserPackages() // 刷新用户套餐包列表
      return response
    } catch (error) {
      console.error('设置自动续费失败:', error)
      ElMessage.error('设置自动续费失败')
      throw error
    }
  }

  // 删除套餐包（管理员功能）
  const deletePackage = async (packageId: string) => {
    try {
      loading.value = true
      await request.delete(`/op/packages/${packageId}`)
      ElMessage.success('套餐包删除成功')
      await fetchPackages() // 刷新列表
    } catch (error) {
      console.error('删除套餐包失败:', error)
      ElMessage.error('删除套餐包失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 重置状态
  const resetState = () => {
    packages.value = []
    userPackages.value = []
    currentPackage.value = null
    stats.value = {
      totalPackages: 0,
      activePackages: 0,
      soldPackages: 0,
      totalRevenue: 0,
      monthlyRevenue: 0,
      popularPackages: [],
      recentSales: []
    }
    recommendations.value = []
    usageData.value = []
    pagination.value = {
      page: 1,
      pageSize: 20,
      total: 0
    }
    lastUpdated.value = null
  }

  return {
    // 状态
    loading,
    packages,
    userPackages,
    currentPackage,
    stats,
    recommendations,
    usageData,
    pagination,
    lastUpdated,
    
    // 计算属性
    activePackages,
    popularPackages,
    recommendedPackages,
    activeUserPackages,
    hasData,
    
    // 方法
    fetchPackages,
    fetchPackageDetail,
    fetchUserPackages,
    fetchPackageStats,
    fetchRecommendations,
    fetchPackageUsage,
    createPackage,
    updatePackage,
    purchasePackage,
    cancelPackage,
    renewPackage,
    setAutoRenew,
    deletePackage,
    resetState
  }
})