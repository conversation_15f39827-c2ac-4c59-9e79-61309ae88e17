<template>
  <div class="docs-page">
    <div class="page-header">
      <div class="container">
        <h1 class="page-title">API 文档</h1>
        <p class="page-subtitle">详细的API接口文档和使用指南</p>
      </div>
    </div>

    <div class="page-content">
      <div class="container">
        <el-row :gutter="24">
          <!-- 左侧导航 -->
          <el-col :lg="6" :md="8" :sm="24">
            <div class="docs-nav">
              <el-menu :default-active="activeSection" mode="vertical" @select="handleMenuSelect" class="docs-menu">
                <el-menu-item index="getting-started">
                  <el-icon>
                    <Document />
                  </el-icon>
                  <span>快速开始</span>
                </el-menu-item>

                <el-sub-menu index="authentication">
                  <template #title>
                    <el-icon>
                      <Key />
                    </el-icon>
                    <span>身份认证</span>
                  </template>
                  <el-menu-item index="auth-overview">认证概述</el-menu-item>
                  <el-menu-item index="api-keys">API密钥</el-menu-item>
                  <el-menu-item index="auth-examples">认证示例</el-menu-item>
                </el-sub-menu>

                <el-sub-menu index="ocr-apis">
                  <template #title>
                    <el-icon>
                      <Picture />
                    </el-icon>
                    <span>OCR 接口</span>
                  </template>
                  <el-menu-item index="ocr-express">物流面单OCR识别</el-menu-item>
                  <el-menu-item index="sto-ocr">申通面单OCR识别</el-menu-item>
                  <el-menu-item index="ocr-general">通用OCR识别</el-menu-item>
                </el-sub-menu>

                <el-sub-menu index="nlp-apis">
                  <template #title>
                    <el-icon>
                      <ChatDotRound />
                    </el-icon>
                    <span>NLP 接口</span>
                  </template>
                  <el-menu-item index="address-extract">地址信息提取</el-menu-item>
                  <el-menu-item index="text-analysis">文本分析</el-menu-item>
                </el-sub-menu>

                <el-sub-menu index="data-apis">
                  <template #title>
                    <el-icon>
                      <DataAnalysis />
                    </el-icon>
                    <span>数据服务</span>
                  </template>
                  <el-menu-item index="geo-reverse">地理坐标逆解析</el-menu-item>
                  <el-menu-item index="geo-forward">地理坐标正解析</el-menu-item>
                </el-sub-menu>

                <el-sub-menu index="ai-apis">
                  <template #title>
                    <el-icon>
                      <MagicStick />
                    </el-icon>
                    <span>AI 生成</span>
                  </template>
                  <el-menu-item index="ai-text">AI文本生成</el-menu-item>
                  <el-menu-item index="ai-code">AI代码生成</el-menu-item>
                </el-sub-menu>

                <el-menu-item index="error-codes">
                  <el-icon>
                    <Warning />
                  </el-icon>
                  <span>错误码</span>
                </el-menu-item>

                <el-menu-item index="rate-limits">
                  <el-icon>
                    <Timer />
                  </el-icon>
                  <span>频率限制</span>
                </el-menu-item>

                <el-menu-item index="sdks">
                  <el-icon>
                    <Box />
                  </el-icon>
                  <span>SDK下载</span>
                </el-menu-item>

                <el-menu-item index="faq">
                  <el-icon>
                    <QuestionFilled />
                  </el-icon>
                  <span>常见问题</span>
                </el-menu-item>

                <el-menu-item index="async-mode" class="new-feature-menu-item">
                  <el-icon>
                    <Connection />
                  </el-icon>
                  <span>异步调用模式</span>
                </el-menu-item>
              </el-menu>
            </div>
          </el-col>

          <!-- 右侧内容 -->
          <el-col :lg="18" :md="16" :sm="24">
            <div class="docs-content">
              <!-- 快速开始 -->
              <div v-show="activeSection === 'getting-started'" class="doc-section">
                <h2>快速开始</h2>
                <p>欢迎使用我们的开放平台API！本指南将帮助您快速上手。</p>

                <h3>1. 注册账号</h3>
                <p>首先，您需要<router-link to="/register">注册一个账号</router-link>。</p>

                <h3>2. 获取API密钥</h3>
                <p>登录后，前往<router-link to="/console/api-keys">控制台</router-link>创建API密钥。</p>

                <h3>3. 发起第一个请求</h3>
                <div class="code-example">
                  <h4>示例：物流面单OCR识别</h4>
                  <pre><code>curl -X POST "http://localhost:8088/v1/op/ocr/upload" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/express_label.jpg" \
  -F "mode=async"</code></pre>
                </div>

                <h3>4. 处理响应</h3>
                <p>API将返回JSON格式的响应：</p>
                <div class="code-example">
                  <pre><code>{
  "success": true,
  "jobId": "job_xxxxxxxxxx",
  "status": "queued",
  "statusUrl": "/v1/op/tasks/job_xxxxxxxxxx",
  "message": "任务已提交到队列，请通过statusUrl查询结果",
  "requestId": "req_xxxxxxxxxx",
  "responseTime": 120
}</code></pre>
                </div>
              </div>

              <!-- 认证概述 -->
              <div v-show="activeSection === 'auth-overview'" class="doc-section">
                <h2>认证概述</h2>
                <p>我们的API使用API密钥进行身份认证。您需要在每个请求的Header中包含您的API密钥。</p>

                <h3>认证方式</h3>
                <div class="auth-method">
                  <h4>Bearer Token认证</h4>
                  <p>在请求头中添加以下字段：</p>
                  <div class="code-example">
                    <pre><code>Authorization: Bearer YOUR_API_KEY</code></pre>
                  </div>
                </div>

                <h3>安全建议</h3>
                <el-alert title="安全提示" type="warning" :closable="false">
                  <ul>
                    <li>请妥善保管您的API密钥，不要在客户端代码中暴露</li>
                    <li>建议定期更换API密钥</li>
                    <li>为不同的应用创建不同的API密钥</li>
                    <li>监控API密钥的使用情况</li>
                  </ul>
                </el-alert>
              </div>

              <!-- API密钥 -->
              <div v-show="activeSection === 'api-keys'" class="doc-section">
                <h2>API密钥管理</h2>
                <p>API密钥是访问我们服务的凭证，您可以在控制台中管理您的密钥。</p>

                <h3>创建API密钥</h3>
                <ol>
                  <li>登录控制台</li>
                  <li>进入"API密钥"页面</li>
                  <li>点击"创建密钥"按钮</li>
                  <li>填写密钥名称和描述</li>
                  <li>设置权限范围（可选）</li>
                  <li>保存并复制密钥</li>
                </ol>

                <h3>密钥权限</h3>
                <el-table :data="keyPermissions" style="width: 100%">
                  <el-table-column prop="permission" label="权限" />
                  <el-table-column prop="description" label="描述" />
                </el-table>
              </div>

              <!-- 认证示例 -->
              <div v-show="activeSection === 'auth-examples'" class="doc-section">
                <h2>认证示例</h2>
                <p>以下是不同编程语言的认证示例：</p>

                <h3>cURL示例</h3>
                <div class="code-example">
                  <pre><code>curl -X POST "http://localhost:8088/v1/op/ocr/upload" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@image.jpg"</code></pre>
                </div>

                <h3>JavaScript示例</h3>
                <div class="code-example">
                  <pre><code>const response = await fetch('http://localhost:8088/v1/op/ocr/upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: formData
});</code></pre>
                </div>

                <h3>Python示例</h3>
                <div class="code-example">
                  <pre><code>import requests

headers = {
    'Authorization': 'Bearer YOUR_API_KEY'
}

response = requests.post(
    'http://localhost:8088/v1/op/ocr/upload',
    headers=headers,
    files={'file': open('image.jpg', 'rb')}
)</code></pre>
                </div>
              </div>

              <!-- 物流面单OCR识别 -->
              <div v-show="activeSection === 'ocr-express'" class="doc-section">
                <h2>物流面单OCR识别</h2>
                <p>智能识别物流面单信息，提取收发件人、地址、电话等关键信息，支持多种面单格式，准确率高达95%以上。适用于物流企业、电商平台等自动化数据录入场景。</p>

                <div class="api-info">
                  <h3>接口信息</h3>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="请求方式">POST</el-descriptions-item>
                    <el-descriptions-item label="接口地址">/v1/op/ocr/upload</el-descriptions-item>
                    <el-descriptions-item label="内容类型">multipart/form-data</el-descriptions-item>
                    <el-descriptions-item label="频率限制">10000次/天</el-descriptions-item>
                    <el-descriptions-item label="支持格式">JPG, PNG, PDF, BMP, TIFF</el-descriptions-item>
                    <el-descriptions-item label="文件大小">最大10MB</el-descriptions-item>
                  </el-descriptions>
                </div>

                <h3>请求参数</h3>
                <el-table :data="ocrExpressParams" style="width: 100%">
                  <el-table-column prop="name" label="参数名" />
                  <el-table-column prop="type" label="类型" />
                  <el-table-column prop="required" label="必填" />
                  <el-table-column prop="description" label="说明" />
                </el-table>

                <h3>响应格式</h3>
                <div class="code-example">
                  <pre><code>{
  "success": true,
  "data": {
    "sender": {
      "name": "张三",
      "phone": "***********",
      "address": "广东省深圳市南山区科技园"
    },
    "receiver": {
      "name": "李四",
      "phone": "***********",
      "address": "北京市朝阳区建国门外大街"
    },
    "express_info": {
      "tracking_number": "SF1234567890",
      "courier_name": "顺丰速运",
      "weight": "1.5kg",
      "package_count": 1
    },
    "confidence": 0.98
  },
  "requestId": "req_xxxxxxxxxx",
  "responseTime": 2345
}</code></pre>
                </div>

                <h3>代码示例</h3>
                <el-tabs v-model="ocrAsyncTab" class="demo-tabs">
                  <el-tab-pane label="cURL" name="curl">
                    <pre><code>curl -X POST "http://localhost:8088/v1/op/ocr/upload" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@express_label.jpg" \
  -F "mode=async"</code></pre>
                  </el-tab-pane>
                  <el-tab-pane label="JavaScript" name="javascript">
                    <pre><code>const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('mode', 'async');

const response = await fetch('http://localhost:8088/v1/op/ocr/upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: formData
});

const result = await response.json();
console.log(result);</code></pre>
                  </el-tab-pane>
                  <el-tab-pane label="Python" name="python">
                    <pre><code>import requests

url = 'http://localhost:8088/v1/op/ocr/upload'
headers = {
    'Authorization': 'Bearer YOUR_API_KEY'
}

files = {
    'file': open('express_label.jpg', 'rb')
}

data = {
    'mode': 'async'
}

response = requests.post(url, headers=headers, files=files, data=data)
result = response.json()
print(result)</code></pre>
                  </el-tab-pane>
                </el-tabs>
              </div>

              <!-- 申通面单OCR识别 -->
              <div v-show="activeSection === 'sto-ocr'" class="doc-section">
                <h2>申通面单OCR识别</h2>
                <p>专门针对申通物流面单的OCR识别服务，优化识别准确率和字段提取，支持申通特有字段识别如分拣码、路由码等。相比通用OCR服务，具有更高的识别精度和更完整的字段提取能力。</p>

                <div class="api-info">
                  <h3>接口信息</h3>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="请求方式">POST</el-descriptions-item>
                    <el-descriptions-item label="接口地址">/v1/op/ocr/sto</el-descriptions-item>
                    <el-descriptions-item label="内容类型">multipart/form-data</el-descriptions-item>
                    <el-descriptions-item label="频率限制">5000次/天</el-descriptions-item>
                    <el-descriptions-item label="支持格式">JPG, PNG, PDF, BMP, TIFF</el-descriptions-item>
                    <el-descriptions-item label="文件大小">最大10MB</el-descriptions-item>
                  </el-descriptions>
                </div>

                <h3>请求参数</h3>
                <el-table :data="stoOcrParams" style="width: 100%">
                  <el-table-column prop="name" label="参数名" />
                  <el-table-column prop="type" label="类型" />
                  <el-table-column prop="required" label="必填" />
                  <el-table-column prop="description" label="说明" />
                </el-table>

                <h3>响应格式</h3>
                <div class="code-example">
                  <pre><code>{
  "success": true,
  "data": {
    "sender": {
      "name": "张三",
      "phone": "***********",
      "address": "广东省深圳市南山区科技园"
    },
    "receiver": {
      "name": "李四",
      "phone": "***********",
      "address": "北京市朝阳区建国门外大街"
    },
    "express_info": {
      "tracking_number": "STO1234567890",
      "courier_name": "申通快递",
      "weight": "1.5kg",
      "package_count": 1,
      "sorting_code": "STO001", // 申通特有字段
      "routing_code": "RT001"   // 申通特有字段
    },
    "confidence": 0.99
  },
  "requestId": "req_xxxxxxxxxx",
  "responseTime": 3456
}</code></pre>
                </div>

                <h3>代码示例</h3>
                <el-tabs v-model="stoOcrAsyncTab" class="demo-tabs">
                  <el-tab-pane label="cURL" name="curl">
                    <pre><code>curl -X POST "http://localhost:8088/v1/op/ocr/sto" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@sto_express_label.jpg" \
  -F "mode=async"</code></pre>
                  </el-tab-pane>
                  <el-tab-pane label="JavaScript" name="javascript">
                    <pre><code>const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('mode', 'async');

const response = await fetch('http://localhost:8088/v1/op/ocr/sto', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: formData
});

const result = await response.json();
console.log(result);</code></pre>
                  </el-tab-pane>
                  <el-tab-pane label="Python" name="python">
                    <pre><code>import requests

url = 'http://localhost:8088/v1/op/ocr/sto'
headers = {
    'Authorization': 'Bearer YOUR_API_KEY'
}

files = {
    'file': open('sto_express_label.jpg', 'rb')
}

data = {
    'mode': 'async'
}

response = requests.post(url, headers=headers, files=files, data=data)
result = response.json()
print(result)</code></pre>
                  </el-tab-pane>
                </el-tabs>
              </div>

              <!-- 通用OCR识别 -->
              <div v-show="activeSection === 'ocr-general'" class="doc-section">
                <h2>通用OCR识别</h2>
                <p>通用OCR识别服务，支持多种图片格式的文字识别，适用于文档扫描、图片文字提取等场景。</p>

                <div class="api-info">
                  <h3>接口信息</h3>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="请求方式">POST</el-descriptions-item>
                    <el-descriptions-item label="接口地址">/v1/op/ocr/general</el-descriptions-item>
                    <el-descriptions-item label="内容类型">multipart/form-data</el-descriptions-item>
                    <el-descriptions-item label="频率限制">20000次/天</el-descriptions-item>
                    <el-descriptions-item label="支持格式">JPG, PNG, PDF, BMP, TIFF</el-descriptions-item>
                    <el-descriptions-item label="文件大小">最大10MB</el-descriptions-item>
                  </el-descriptions>
                </div>

                <h3>请求参数</h3>
                <el-table :data="ocrGeneralParams" style="width: 100%">
                  <el-table-column prop="name" label="参数名" />
                  <el-table-column prop="type" label="类型" />
                  <el-table-column prop="required" label="必填" />
                  <el-table-column prop="description" label="说明" />
                </el-table>

                <h3>响应格式</h3>
                <div class="code-example">
                  <pre><code>{
  "success": true,
  "data": {
    "text": "识别出的文字内容",
    "confidence": 0.95,
    "regions": [
      {
        "text": "部分文字",
        "confidence": 0.98,
        "bbox": [x1, y1, x2, y2]
      }
    ]
  },
  "requestId": "req_xxxxxxxxxx",
  "responseTime": 1234
}</code></pre>
                </div>
              </div>

              <!-- 地址信息提取 -->
              <div v-show="activeSection === 'address-extract'" class="doc-section">
                <h2>地址信息提取</h2>
                <p>从文本中精确提取省市区详细地址信息，支持非结构化文本解析，可处理各种格式的地址信息，并进行标准化输出，支持省市区到街道社区的精确识别。</p>

                <div class="api-info">
                  <h3>接口信息</h3>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="请求方式">POST</el-descriptions-item>
                    <el-descriptions-item label="接口地址">/v1/op/address/extract</el-descriptions-item>
                    <el-descriptions-item label="内容类型">application/json</el-descriptions-item>
                    <el-descriptions-item label="频率限制">20000次/天</el-descriptions-item>
                  </el-descriptions>
                </div>

                <h3>请求参数</h3>
                <el-table :data="addressExtractParams" style="width: 100%">
                  <el-table-column prop="name" label="参数名" />
                  <el-table-column prop="type" label="类型" />
                  <el-table-column prop="required" label="必填" />
                  <el-table-column prop="description" label="说明" />
                </el-table>

                <h3>响应格式</h3>
                <div class="code-example">
                  <pre><code>{
  "success": true,
  "data": {
    "addresses": [
      {
        "province": "广东省",
        "city": "深圳市",
        "district": "南山区",
        "street": "科技园",
        "detail": "南区科技南十二路2号金蝶软件园A座5楼",
        "full_address": "广东省深圳市南山区科技园南区科技南十二路2号金蝶软件园A座5楼",
        "confidence": 0.98
      }
    ],
    "phones": ["***********"],
    "names": ["张三"],
    "confidence": 0.95
  },
  "requestId": "req_xxxxxxxxxx",
  "responseTime": 567
}</code></pre>
                </div>

                <h3>代码示例</h3>
                <el-tabs v-model="addressAsyncTab" class="demo-tabs">
                  <el-tab-pane label="cURL" name="curl">
                    <pre><code>curl -X POST "http://localhost:8088/v1/op/address/extract" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "张三，***********，广东省深圳市南山区科技园南区科技南十二路2号金蝶软件园A座5楼",
    "mode": "async"
  }'</code></pre>
                  </el-tab-pane>
                  <el-tab-pane label="JavaScript" name="javascript">
                    <pre><code>const response = await fetch('http://localhost:8088/v1/op/address/extract', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    text: '张三，***********，广东省深圳市南山区科技园南区科技南十二路2号金蝶软件园A座5楼',
    mode: 'async'
  })
});

const result = await response.json();
console.log(result);</code></pre>
                  </el-tab-pane>
                  <el-tab-pane label="Python" name="python">
                    <pre><code>import requests
import json

url = 'http://localhost:8088/v1/op/address/extract'
headers = {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
}

data = {
    'text': '张三，***********，广东省深圳市南山区科技园南区科技南十二路2号金蝶软件园A座5楼',
    'mode': 'async'
}

response = requests.post(url, headers=headers, json=data)
result = response.json()
print(result)</code></pre>
                  </el-tab-pane>
                </el-tabs>
              </div>

              <!-- 文本分析 -->
              <div v-show="activeSection === 'text-analysis'" class="doc-section">
                <h2>文本分析</h2>
                <p>智能文本分析服务，支持情感分析、关键词提取、文本分类等功能。</p>

                <div class="api-info">
                  <h3>接口信息</h3>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="请求方式">POST</el-descriptions-item>
                    <el-descriptions-item label="接口地址">/v1/op/nlp/analyze</el-descriptions-item>
                    <el-descriptions-item label="内容类型">application/json</el-descriptions-item>
                    <el-descriptions-item label="频率限制">30000次/天</el-descriptions-item>
                  </el-descriptions>
                </div>

                <h3>请求参数</h3>
                <el-table :data="textAnalysisParams" style="width: 100%">
                  <el-table-column prop="name" label="参数名" />
                  <el-table-column prop="type" label="类型" />
                  <el-table-column prop="required" label="必填" />
                  <el-table-column prop="description" label="说明" />
                </el-table>

                <h3>响应格式</h3>
                <div class="code-example">
                  <pre><code>{
  "success": true,
  "data": {
    "sentiment": "positive",
    "sentiment_score": 0.85,
    "keywords": ["人工智能", "发展", "技术"],
    "categories": ["科技", "AI"],
    "summary": "文本摘要内容"
  },
  "requestId": "req_xxxxxxxxxx",
  "responseTime": 789
}</code></pre>
                </div>
              </div>

              <!-- 地理坐标逆解析 -->
              <div v-show="activeSection === 'geo-reverse'" class="doc-section">
                <h2>地理坐标逆解析</h2>
                <p>根据经纬度坐标逆向解析出详细地址信息，支持多种坐标系，可输出丰富的POI信息，精确到建筑物级别。适用于物流配送、位置服务、用户轨迹分析等场景。</p>

                <div class="api-info">
                  <h3>接口信息</h3>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="请求方式">GET/POST</el-descriptions-item>
                    <el-descriptions-item label="接口地址">/v1/op/geo/reverse</el-descriptions-item>
                    <el-descriptions-item label="内容类型">application/json</el-descriptions-item>
                    <el-descriptions-item label="频率限制">50000次/天</el-descriptions-item>
                  </el-descriptions>
                </div>

                <h3>请求参数</h3>
                <el-table :data="geoReverseParams" style="width: 100%">
                  <el-table-column prop="name" label="参数名" />
                  <el-table-column prop="type" label="类型" />
                  <el-table-column prop="required" label="必填" />
                  <el-table-column prop="description" label="说明" />
                </el-table>

                <h3>响应格式</h3>
                <div class="code-example">
                  <pre><code>{
  "success": true,
  "data": {
    "address": {
      "province": "北京市",
      "city": "北京市",
      "district": "朝阳区",
      "street": "建国门外大街",
      "detail": "1号国贸大厦",
      "full_address": "北京市朝阳区建国门外大街1号国贸大厦"
    },
    "location": {
      "latitude": 39.9085,
      "longitude": 116.3975
    },
    "poi": {
      "name": "国贸大厦",
      "type": "商务楼宇",
      "distance": 0
    },
    "confidence": 0.99
  },
  "requestId": "req_xxxxxxxxxx",
  "responseTime": 234
}</code></pre>
                </div>

                <h3>代码示例</h3>
                <el-tabs v-model="geoAsyncTab" class="demo-tabs">
                  <el-tab-pane label="cURL" name="curl">
                    <pre><code>curl -X GET "http://localhost:8088/v1/op/geo/reverse?latitude=39.9085&longitude=116.3975" \
  -H "Authorization: Bearer YOUR_API_KEY"</code></pre>
                  </el-tab-pane>
                  <el-tab-pane label="JavaScript" name="javascript">
                    <pre><code>const response = await fetch('http://localhost:8088/v1/op/geo/reverse?latitude=39.9085&longitude=116.3975', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY'
  }
});

const result = await response.json();
console.log(result);</code></pre>
                  </el-tab-pane>
                  <el-tab-pane label="Python" name="python">
                    <pre><code>import requests

url = 'http://localhost:8088/v1/op/geo/reverse'
headers = {
    'Authorization': 'Bearer YOUR_API_KEY'
}

params = {
    'latitude': 39.9085,
    'longitude': 116.3975
}

response = requests.get(url, headers=headers, params=params)
result = response.json()
print(result)</code></pre>
                  </el-tab-pane>
                </el-tabs>
              </div>

              <!-- 地理坐标正解析 -->
              <div v-show="activeSection === 'geo-forward'" class="doc-section">
                <h2>地理坐标正解析</h2>
                <p>根据地址信息解析出对应的经纬度坐标，支持模糊地址匹配，适用于地址标准化、地理编码等场景。</p>

                <div class="api-info">
                  <h3>接口信息</h3>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="请求方式">GET/POST</el-descriptions-item>
                    <el-descriptions-item label="接口地址">/v1/op/geo/forward</el-descriptions-item>
                    <el-descriptions-item label="内容类型">application/json</el-descriptions-item>
                    <el-descriptions-item label="频率限制">50000次/天</el-descriptions-item>
                  </el-descriptions>
                </div>

                <h3>请求参数</h3>
                <el-table :data="geoForwardParams" style="width: 100%">
                  <el-table-column prop="name" label="参数名" />
                  <el-table-column prop="type" label="类型" />
                  <el-table-column prop="required" label="必填" />
                  <el-table-column prop="description" label="说明" />
                </el-table>

                <h3>响应格式</h3>
                <div class="code-example">
                  <pre><code>{
  "success": true,
  "data": {
    "location": {
      "latitude": 39.9085,
      "longitude": 116.3975
    },
    "address": {
      "province": "北京市",
      "city": "北京市",
      "district": "朝阳区",
      "street": "建国门外大街",
      "detail": "1号国贸大厦",
      "full_address": "北京市朝阳区建国门外大街1号国贸大厦"
    },
    "confidence": 0.99
  },
  "requestId": "req_xxxxxxxxxx",
  "responseTime": 345
}</code></pre>
                </div>
              </div>

              <!-- AI文本生成 -->
              <div v-show="activeSection === 'ai-text'" class="doc-section">
                <h2>AI文本生成</h2>
                <p>基于大语言模型的智能文本生成服务，支持多种文本生成任务，如文章创作、内容摘要、对话生成等。</p>

                <div class="api-info">
                  <h3>接口信息</h3>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="请求方式">POST</el-descriptions-item>
                    <el-descriptions-item label="接口地址">/v1/op/ai/text</el-descriptions-item>
                    <el-descriptions-item label="内容类型">application/json</el-descriptions-item>
                    <el-descriptions-item label="频率限制">10000次/天</el-descriptions-item>
                  </el-descriptions>
                </div>

                <h3>请求参数</h3>
                <el-table :data="aiTextParams" style="width: 100%">
                  <el-table-column prop="name" label="参数名" />
                  <el-table-column prop="type" label="类型" />
                  <el-table-column prop="required" label="必填" />
                  <el-table-column prop="description" label="说明" />
                </el-table>

                <h3>响应格式</h3>
                <div class="code-example">
                  <pre><code>{
  "success": true,
  "data": {
    "generated_text": "生成的文本内容",
    "tokens_used": 150,
    "finish_reason": "stop",
    "model": "gpt-3.5-turbo"
  },
  "requestId": "req_xxxxxxxxxx",
  "responseTime": 2345
}</code></pre>
                </div>
              </div>

              <!-- AI代码生成 -->
              <div v-show="activeSection === 'ai-code'" class="doc-section">
                <h2>AI代码生成</h2>
                <p>基于AI的智能代码生成服务，支持多种编程语言，可根据自然语言描述生成相应的代码片段。</p>

                <div class="api-info">
                  <h3>接口信息</h3>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="请求方式">POST</el-descriptions-item>
                    <el-descriptions-item label="接口地址">/v1/op/ai/code</el-descriptions-item>
                    <el-descriptions-item label="内容类型">application/json</el-descriptions-item>
                    <el-descriptions-item label="频率限制">5000次/天</el-descriptions-item>
                  </el-descriptions>
                </div>

                <h3>请求参数</h3>
                <el-table :data="aiCodeParams" style="width: 100%">
                  <el-table-column prop="name" label="参数名" />
                  <el-table-column prop="type" label="类型" />
                  <el-table-column prop="required" label="必填" />
                  <el-table-column prop="description" label="说明" />
                </el-table>

                <h3>响应格式</h3>
                <div class="code-example">
                  <pre><code>{
  "success": true,
  "data": {
    "generated_code": "function hello() {\n  console.log('Hello, World!');\n}",
    "language": "javascript",
    "explanation": "代码说明",
    "tokens_used": 200
  },
  "requestId": "req_xxxxxxxxxx",
  "responseTime": 3456
}</code></pre>
                </div>
              </div>

              <!-- 错误码 -->
              <div v-show="activeSection === 'error-codes'" class="doc-section">
                <h2>错误码说明</h2>
                <p>当API请求失败时，会返回相应的错误码和错误信息。</p>

                <h3>HTTP状态码</h3>
                <el-table :data="httpStatusCodes" style="width: 100%">
                  <el-table-column prop="code" label="状态码" />
                  <el-table-column prop="message" label="说明" />
                </el-table>

                <h3>业务错误码</h3>
                <el-table :data="businessErrorCodes" style="width: 100%">
                  <el-table-column prop="code" label="错误码" />
                  <el-table-column prop="message" label="错误信息" />
                  <el-table-column prop="description" label="解决方案" />
                </el-table>
              </div>

              <!-- 频率限制 -->
              <div v-show="activeSection === 'rate-limits'" class="doc-section">
                <h2>频率限制</h2>
                <p>为了保护服务稳定性，我们对API调用频率进行了限制。</p>

                <h3>限制规则</h3>
                <el-table :data="rateLimits" style="width: 100%">
                  <el-table-column prop="service" label="服务" />
                  <el-table-column prop="daily" label="每日限制" />
                  <el-table-column prop="minute" label="每分钟限制" />
                  <el-table-column prop="description" label="说明" />
                </el-table>
              </div>

              <!-- SDK下载 -->
              <div v-show="activeSection === 'sdks'" class="doc-section">
                <h2>SDK下载</h2>
                <p>我们提供了多种编程语言的SDK，方便您快速集成API服务。</p>

                <div class="sdk-list">
                  <el-card v-for="sdk in sdks" :key="sdk.name" class="sdk-card">
                    <template #header>
                      <div class="sdk-header">
                        <h3>{{ sdk.name }}</h3>
                        <el-tag :type="sdk.status">{{ sdk.statusText }}</el-tag>
                      </div>
                    </template>
                    <p>{{ sdk.description }}</p>
                    <div class="sdk-actions">
                      <el-button type="primary" @click="downloadSdk(sdk)">下载</el-button>
                      <el-button @click="viewSdkDocs(sdk)">文档</el-button>
                    </div>
                  </el-card>
                </div>
              </div>

              <!-- 常见问题 -->
              <div v-show="activeSection === 'faq'" class="doc-section">
                <h2>常见问题</h2>

                <el-collapse>
                  <el-collapse-item v-for="faq in faqs" :key="faq.id" :title="faq.question" :name="faq.id">
                    <div>{{ faq.answer }}</div>
                  </el-collapse-item>
                </el-collapse>
              </div>

              <!-- 异步调用模式 -->
              <div v-show="activeSection === 'async-mode'" class="doc-section">
                <h2>异步调用模式</h2>
                <p>对于处理时间较长的任务，我们支持异步调用模式。异步模式可以避免请求超时，并提供更好的用户体验。</p>

                <h3>异步调用流程</h3>
                <div class="async-flow">
                  <ol>
                    <li>客户端发送异步请求</li>
                    <li>服务器返回任务ID</li>
                    <li>客户端通过任务ID查询状态</li>
                    <li>任务完成后获取结果</li>
                  </ol>
                </div>

                <h3>轮询方式</h3>
                <p>客户端可以定期轮询任务状态接口来获取任务进度和结果。</p>

                <div class="api-info">
                  <h4>接口信息</h4>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="请求方式">GET</el-descriptions-item>
                    <el-descriptions-item label="接口地址">/v1/op/tasks/{jobId}</el-descriptions-item>
                    <el-descriptions-item label="内容类型">application/json</el-descriptions-item>
                  </el-descriptions>
                </div>

                <h4>请求参数</h4>
                <el-table :data="taskStatusParams" style="width: 100%">
                  <el-table-column prop="name" label="参数名" />
                  <el-table-column prop="type" label="类型" />
                  <el-table-column prop="required" label="必填" />
                  <el-table-column prop="description" label="说明" />
                </el-table>

                <h4>响应格式</h4>
                <div class="code-example">
                  <pre><code>{
  "success": true,
  "status": "completed",
  "result": {
    // 识别结果结构，具体见业务返回
  },
  "process_time": 1234,
  "image_info": {},
  "requestId": "{jobId}",
  "responseTime": 567
}</code></pre>
                </div>

                <h3>SSE实时状态推送(推荐)</h3>
                <p>服务器发送事件(SSE)允许客户端接收任务状态的实时更新，无需频繁轮询API。</p>

                <div class="api-info">
                  <h4>接口信息</h4>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="请求方式">GET</el-descriptions-item>
                    <el-descriptions-item label="接口地址">/v1/op/tasks/{jobId}/stream</el-descriptions-item>
                    <el-descriptions-item label="内容类型">text/event-stream</el-descriptions-item>
                  </el-descriptions>
                </div>

                <h4>请求参数</h4>
                <el-table :data="sseParams" style="width: 100%">
                  <el-table-column prop="name" label="参数名" />
                  <el-table-column prop="type" label="类型" />
                  <el-table-column prop="required" label="必填" />
                  <el-table-column prop="description" label="说明" />
                </el-table>

                <h4>SSE响应格式</h4>
                <div class="code-example">
                  <pre><code>data: {
  "success": true,
  "status": "completed",
  "result": {
    // 识别结果结构，具体见业务返回
  },
  "process_time": 1234,
  "image_info": {},
  "requestId": "{jobId}",
  "responseTime": 567
}</code></pre>
                </div>

                <h4>前端代码示例</h4>
                <div class="code-example">
                  <pre><code>function subscribeToTaskStatus(jobId, service) {
  const eventSource = new EventSource(`http://localhost:8088/v1/op/tasks/${jobId}/stream?service=${service}`);

  eventSource.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log('任务状态更新:', data);
    
    if (data.status === 'completed') {
      console.log('任务完成:', data.result);
      eventSource.close();
    } else if (data.status === 'failed') {
      console.error('任务失败:', data.error);
      eventSource.close();
    }
  };

  eventSource.onerror = (error) => {
    console.error('SSE连接错误:', error);
    eventSource.close();
  };
}</code></pre>
                </div>

                <h3>异步任务状态说明</h3>
                <el-table :data="taskStatusList" style="width: 100%">
                  <el-table-column prop="status" label="状态" />
                  <el-table-column prop="description" label="说明" />
                </el-table>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Document,
  Key,
  Picture,
  ChatDotRound,
  MagicStick,
  Warning,
  Timer,
  Box,
  QuestionFilled,
  DataAnalysis,
  Connection
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const activeSection = ref('getting-started')
const ocrAsyncTab = ref('curl')
const stoOcrAsyncTab = ref('curl')
const addressAsyncTab = ref('curl')
const geoAsyncTab = ref('curl')

// 处理菜单选择
const handleMenuSelect = (index: string) => {
  activeSection.value = index
  // 更新URL hash
  router.replace({ hash: `#${index}` })
}

// API密钥权限表
const keyPermissions = ref([
  { permission: 'ocr:read', description: 'OCR识别服务读取权限' },
  { permission: 'nlp:read', description: 'NLP处理服务读取权限' },
  { permission: 'ai:read', description: 'AI生成服务读取权限' },
  { permission: 'admin', description: '管理员权限（包含所有权限）' }
])

// SSE参数
const sseParams = ref([
  { name: 'jobId', type: 'string', required: '是', description: '任务ID，路径参数' },
  { name: 'service', type: 'string', required: '否', description: '服务类型，可选值：ocr、address、geo' },
  { name: 'timeout', type: 'number', required: '否', description: '超时时间(ms)，默认值：30000' },
  { name: 'interval', type: 'number', required: '否', description: '检查间隔(ms)，默认值：1000' }
])

// OCR物流面单参数
const ocrExpressParams = ref([
  { name: 'file', type: 'File', required: '是', description: '要识别的图片文件' },
  { name: 'mode', type: 'String', required: '是', description: '处理模式，固定值async' }
])

// 申通面单OCR参数
const stoOcrParams = ref([
  { name: 'file', type: 'File', required: '是', description: '要识别的申通面单图片文件' },
  { name: 'mode', type: 'String', required: '是', description: '处理模式，固定值async' }
])

// 通用OCR参数
const ocrGeneralParams = ref([
  { name: 'file', type: 'File', required: '是', description: '要识别的图片文件' },
  { name: 'mode', type: 'String', required: '是', description: '处理模式，固定值async' },
  { name: 'language', type: 'String', required: '否', description: '识别语言，默认auto' }
])

// 地址提取参数
const addressExtractParams = ref([
  { name: 'text', type: 'String', required: '是', description: '包含地址的文本' },
  { name: 'mode', type: 'String', required: '是', description: '处理模式，固定值async' }
])

// 文本分析参数
const textAnalysisParams = ref([
  { name: 'text', type: 'String', required: '是', description: '要分析的文本' },
  { name: 'mode', type: 'String', required: '是', description: '处理模式，固定值async' },
  { name: 'analysis_type', type: 'String', required: '否', description: '分析类型：sentiment/keywords/categories' }
])

// 地理坐标逆解析参数
const geoReverseParams = ref([
  { name: 'latitude', type: 'number', required: '是', description: '纬度坐标' },
  { name: 'longitude', type: 'number', required: '是', description: '经度坐标' },
  { name: 'coord_type', type: 'string', required: '否', description: '坐标系类型，默认wgs84' },
  { name: 'with_poi', type: 'boolean', required: '否', description: '是否返回POI信息，默认true' }
])

// 地理坐标正解析参数
const geoForwardParams = ref([
  { name: 'address', type: 'string', required: '是', description: '要解析的地址' },
  { name: 'city', type: 'string', required: '否', description: '城市名称' },
  { name: 'coord_type', type: 'string', required: '否', description: '坐标系类型，默认wgs84' }
])

// AI文本生成参数
const aiTextParams = ref([
  { name: 'prompt', type: 'string', required: '是', description: '生成提示词' },
  { name: 'mode', type: 'string', required: '是', description: '处理模式，固定值async' },
  { name: 'max_tokens', type: 'number', required: '否', description: '最大生成token数，默认1000' },
  { name: 'temperature', type: 'number', required: '否', description: '生成温度，默认0.7' }
])

// AI代码生成参数
const aiCodeParams = ref([
  { name: 'prompt', type: 'string', required: '是', description: '代码生成提示词' },
  { name: 'language', type: 'string', required: '是', description: '编程语言' },
  { name: 'mode', type: 'string', required: '是', description: '处理模式，固定值async' }
])

// 任务状态参数
const taskStatusParams = ref([
  { name: 'jobId', type: 'string', required: '是', description: '任务ID，路径参数' },
  { name: 'service', type: 'string', required: '否', description: '服务类型' }
])

// HTTP状态码
const httpStatusCodes = ref([
  { code: 200, message: '请求成功' },
  { code: 400, message: '请求参数错误' },
  { code: 401, message: '未授权，请检查API密钥' },
  { code: 403, message: '禁止访问' },
  { code: 404, message: '资源不存在' },
  { code: 429, message: '请求频率超限' },
  { code: 500, message: '服务器内部错误' }
])

// 业务错误码
const businessErrorCodes = ref([
  { code: 'INVALID_API_KEY', message: '无效的API密钥', description: '请检查API密钥是否正确' },
  { code: 'QUOTA_EXCEEDED', message: '配额已用完', description: '请升级套餐或等待配额重置' },
  { code: 'FILE_TOO_LARGE', message: '文件过大', description: '请压缩文件或使用更小的文件' },
  { code: 'UNSUPPORTED_FORMAT', message: '不支持的文件格式', description: '请使用支持的文件格式' },
  { code: 'TASK_NOT_FOUND', message: '任务不存在', description: '请检查任务ID是否正确' },
  { code: 'TASK_TIMEOUT', message: '任务超时', description: '请稍后重试或联系客服' }
])

// 频率限制
const rateLimits = ref([
  { service: '物流面单OCR识别', daily: '10000次/天', minute: '100次/分钟', description: '适用于物流面单识别' },
  { service: '申通面单OCR识别', daily: '5000次/天', minute: '50次/分钟', description: '专门针对申通面单优化' },
  { service: '通用OCR识别', daily: '20000次/天', minute: '200次/分钟', description: '通用文字识别' },
  { service: '地址信息提取', daily: '20000次/天', minute: '200次/分钟', description: '地址信息提取' },
  { service: '文本分析', daily: '30000次/天', minute: '300次/分钟', description: '文本分析服务' },
  { service: '地理坐标服务', daily: '50000次/天', minute: '500次/分钟', description: '地理坐标解析' },
  { service: 'AI文本生成', daily: '10000次/天', minute: '100次/分钟', description: 'AI文本生成' },
  { service: 'AI代码生成', daily: '5000次/天', minute: '50次/分钟', description: 'AI代码生成' }
])

// SDK列表
const sdks = ref([
  { name: 'JavaScript SDK', status: 'success', statusText: '可用', description: '适用于Node.js和浏览器环境', downloadUrl: '#', docsUrl: '#' },
  { name: 'Python SDK', status: 'success', statusText: '可用', description: '适用于Python 3.7+环境', downloadUrl: '#', docsUrl: '#' },
  { name: 'Java SDK', status: 'warning', statusText: '开发中', description: '适用于Java 8+环境', downloadUrl: '#', docsUrl: '#' },
  { name: 'Go SDK', status: 'info', statusText: '计划中', description: '适用于Go 1.16+环境', downloadUrl: '#', docsUrl: '#' }
])

// 常见问题
const faqs = ref([
  {
    id: '1',
    question: '如何获取API密钥？',
    answer: '登录控制台后，进入"API密钥"页面，点击"创建密钥"按钮即可创建新的API密钥。'
  },
  {
    id: '2',
    question: '支持哪些图片格式？',
    answer: '我们支持JPG、PNG、PDF、BMP、TIFF等常见图片格式，文件大小不超过10MB。'
  },
  {
    id: '3',
    question: '异步任务如何处理？',
    answer: '异步任务会返回任务ID，您可以通过轮询或SSE方式获取任务状态和结果。'
  },
  {
    id: '4',
    question: '如何处理请求频率限制？',
    answer: '我们提供了详细的频率限制说明，建议您根据业务需求合理规划请求频率。'
  },
  {
    id: '5',
    question: '如何获取技术支持？',
    answer: '您可以通过邮件、在线客服或提交工单的方式获取技术支持。'
  }
])

// 任务状态列表
const taskStatusList = ref([
  { status: 'queued', description: '任务已提交到队列，等待处理' },
  { status: 'processing', description: '任务正在处理中' },
  { status: 'completed', description: '任务已完成，可获取结果数据' },
  { status: 'failed', description: '任务处理失败，可查看错误信息' },
  { status: 'timeout', description: '任务处理超时，默认超时时间为60秒' }
])

// 下载SDK
const downloadSdk = (sdk: any) => {
  console.log('下载SDK:', sdk.name)
  // 这里可以添加实际的下载逻辑
}

// 查看SDK文档
const viewSdkDocs = (sdk: any) => {
  console.log('查看SDK文档:', sdk.name)
  // 这里可以添加跳转到SDK文档的逻辑
}

// 初始化
onMounted(() => {
  // 服务代码到文档区域ID的映射
  const serviceToDocMap: Record<string, string> = {
    // OCR服务
    'OCR_EXPRESS': 'ocr-express',    // 物流面单OCR识别
    'sto-ocr': 'sto-ocr',            // 申通面单OCR识别
    'ocr-general': 'ocr-general',     // 通用OCR识别

    // NLP服务
    'ADDRESS_EXTRACT': 'address-extract',  // 地址信息提取
    'text-analysis': 'text-analysis',      // 文本分析

    // 数据服务
    'GEO_REVERSE': 'geo-reverse',      // 地理坐标逆解析
    'geo-forward': 'geo-forward',      // 地理坐标正解析

    // AI服务
    'ai-text': 'ai-text',              // AI文本生成
    'ai-code': 'ai-code'               // AI代码生成
  }

  // 根据URL hash设置活动section
  let hash = route.hash.replace('#', '')

  // 如果hash是服务代码，则映射到对应的文档区域ID
  if (hash && serviceToDocMap[hash]) {
    hash = serviceToDocMap[hash]
  }

  // 如果hash有效，则设置为活动section
  if (hash) {
    // 检查是否需要打开子菜单
    const subMenuMap: Record<string, string> = {
      'ocr-express': 'ocr-apis',
      'sto-ocr': 'ocr-apis',
      'ocr-general': 'ocr-apis',
      'address-extract': 'nlp-apis',
      'text-analysis': 'nlp-apis',
      'geo-reverse': 'data-apis',
      'geo-forward': 'data-apis',
      'ai-text': 'ai-apis',
      'ai-code': 'ai-apis',
      'auth-overview': 'authentication',
      'api-keys': 'authentication',
      'auth-examples': 'authentication'
    }

    // 设置活动section
    activeSection.value = hash

    // 如果需要，展开相应的子菜单
    nextTick(() => {
      // 这里使用DOM操作来展开子菜单，因为ElementPlus菜单没有直接的API来做这个
      if (subMenuMap[hash]) {
        const subMenuEl = document.querySelector(`.el-sub-menu[index="${subMenuMap[hash]}"]`)
        if (subMenuEl) {
          // 添加展开类
          subMenuEl.classList.add('is-opened')
          // 找到子菜单内容并显示
          const subMenuContent = subMenuEl.querySelector('.el-menu--inline') as HTMLElement
          if (subMenuContent) {
            subMenuContent.style.display = 'block'
          }
        }
      }
    })
  }
})
</script>

<style scoped>
.docs-page {
  min-height: calc(100vh - 70px);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.page-header {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-bottom: none;
  padding: 60px 0;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.page-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 16px 0;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  text-align: center;
  font-weight: 400;
}

.page-content {
  padding: 40px 0;
}

.docs-nav {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  padding: 24px 0;
  position: sticky;
  top: 24px;
  max-height: calc(100vh - 48px);
  overflow-y: auto;
}

.docs-menu {
  border: none;
  background: transparent;
}

.docs-menu .el-menu-item,
.docs-menu .el-sub-menu__title {
  margin: 2px 12px;
  padding: 12px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.docs-menu .el-menu-item:hover,
.docs-menu .el-sub-menu__title:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.docs-menu .el-menu-item.is-active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.docs-menu .el-sub-menu.is-opened .el-sub-menu__title {
  background: rgba(59, 130, 246, 0.05);
  color: #3b82f6;
}

.docs-menu .el-menu--inline .el-menu-item {
  margin: 2px 12px 2px 32px;
  padding: 8px 16px;
  font-size: 0.9rem;
}

.docs-content {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  padding: 40px;
  min-height: 600px;
}

.doc-section {
  margin-bottom: 48px;
}

.doc-section:last-child {
  margin-bottom: 0;
}

.doc-section h2 {
  font-size: 2.2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 16px 0;
  padding-bottom: 12px;
  border-bottom: 3px solid #3b82f6;
}

.doc-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #334155;
  margin: 32px 0 16px 0;
}

.doc-section h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #475569;
  margin: 24px 0 12px 0;
}

.doc-section p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #64748b;
  margin: 0 0 16px 0;
}

.code-example {
  background: #1e293b;
  border-radius: 8px;
  padding: 24px;
  margin: 20px 0;
  position: relative;
  overflow: hidden;
}

.code-example::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.code-example h4 {
  color: #ffffff;
  margin: 0 0 16px 0;
  font-size: 1.1rem;
}

.code-example pre {
  margin: 0;
  color: #e2e8f0;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  overflow-x: auto;
}

.code-example code {
  background: transparent;
  color: inherit;
  padding: 0;
  border-radius: 0;
}

.api-info {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 24px;
  margin: 20px 0;
}

.api-info h3 {
  margin: 0 0 16px 0;
  color: #1e293b;
}

.demo-tabs {
  margin: 20px 0;
}

.demo-tabs .el-tabs__content {
  padding: 20px 0;
}

.demo-tabs pre {
  background: #1e293b;
  color: #e2e8f0;
  padding: 16px;
  border-radius: 6px;
  margin: 0;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.85rem;
  line-height: 1.4;
  overflow-x: auto;
}

.sdk-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin: 24px 0;
}

.sdk-card {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.sdk-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.sdk-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sdk-header h3 {
  margin: 0;
  color: #1e293b;
}

.sdk-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.async-flow {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 24px;
  margin: 20px 0;
}

.async-flow ol {
  margin: 0;
  padding-left: 20px;
}

.async-flow li {
  margin: 8px 0;
  color: #475569;
  font-weight: 500;
}

.new-feature-menu-item {
  background: linear-gradient(135deg, #10b981, #059669) !important;
  color: #ffffff !important;
  margin: 8px 12px;
  border-radius: 8px;
}

.new-feature-menu-item:hover {
  background: linear-gradient(135deg, #059669, #047857) !important;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .docs-content {
    padding: 32px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 40px 0;
  }

  .page-title {
    font-size: 2.2rem;
  }

  .page-subtitle {
    font-size: 1.1rem;
  }

  .docs-nav {
    position: static;
    margin-bottom: 24px;
  }

  .docs-content {
    padding: 24px;
  }

  .doc-section h2 {
    font-size: 1.8rem;
  }

  .doc-section h3 {
    font-size: 1.4rem;
  }

  .sdk-list {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }

  .page-header {
    padding: 30px 0;
  }

  .page-title {
    font-size: 1.8rem;
  }

  .page-subtitle {
    font-size: 1rem;
  }

  .docs-content {
    padding: 20px;
  }

  .docs-nav {
    padding: 16px 0;
  }

  .docs-menu .el-menu-item,
  .docs-menu .el-sub-menu__title {
    margin: 2px 8px;
    padding: 8px 12px;
    font-size: 0.9rem;
  }

  .doc-section h2 {
    font-size: 1.6rem;
  }

  .doc-section h3 {
    font-size: 1.3rem;
  }

  .code-example {
    padding: 16px;
  }

  .code-example pre {
    font-size: 0.8rem;
  }

  .api-info {
    padding: 16px;
  }
}
</style>