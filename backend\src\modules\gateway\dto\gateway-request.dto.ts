import { IsOptional, IsString, IsEnum, IsObject, ValidateNested, IsNumber, IsPositive } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ProcessingMode } from '../config/gateway.constants';

/**
 * 网关请求基础DTO
 */
export class GatewayRequestDto {
  @ApiPropertyOptional({
    description: '服务ID，用于标识具体的服务',
    example: 5,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  serviceId?: number;

  @ApiPropertyOptional({
    description: '处理模式',
    enum: ProcessingMode,
    example: ProcessingMode.PROXY_ASYNC,
  })
  @IsOptional()
  @IsEnum(ProcessingMode)
  mode?: ProcessingMode;

  @ApiPropertyOptional({
    description: '请求ID，用于追踪',
    example: 'req_1234567890',
  })
  @IsOptional()
  @IsString()
  requestId?: string;
}

/**
 * OCR识别请求DTO
 */
export class OcrRequestDto extends GatewayRequestDto {
  @ApiPropertyOptional({
    description: 'Base64编码的图片数据',
    example: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...',
  })
  @IsOptional()
  @IsString()
  imageBase64?: string;

  @ApiPropertyOptional({
    description: '识别类型',
    example: 'logistics',
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({
    description: '额外参数',
    example: { accuracy: 'high' },
  })
  @IsOptional()
  @IsObject()
  options?: Record<string, any>;
}

/**
 * 地址提取请求DTO
 */
export class AddressExtractionRequestDto extends GatewayRequestDto {
  @ApiProperty({
    description: '需要提取地址的文本',
    example: '收件人:李四，电话:13900139000，地址:上海市浦东新区张江高科技园区1号楼3层',
  })
  @IsString()
  text: string;

  @ApiPropertyOptional({
    description: '提取选项',
    example: { includePhone: true, includePostalCode: false },
  })
  @IsOptional()
  @IsObject()
  options?: Record<string, any>;
}

/**
 * 地理坐标请求DTO
 */
export class GeoCoordinateRequestDto extends GatewayRequestDto {
  @ApiPropertyOptional({
    description: '纬度（逆地理编码时使用）',
    example: 31.230416,
  })
  @IsOptional()
  lat?: number;

  @ApiPropertyOptional({
    description: '经度（逆地理编码时使用）',
    example: 121.473701,
  })
  @IsOptional()
  lng?: number;

  @ApiPropertyOptional({
    description: '地址（正地理编码时使用）',
    example: '上海市黄浦区南京东路1号',
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({
    description: '坐标系类型',
    example: 'wgs84',
    enum: ['wgs84', 'gcj02', 'bd09'],
  })
  @IsOptional()
  @IsString()
  coordType?: string;
}

/**
 * 文件上传请求DTO
 */
export class FileUploadRequestDto extends GatewayRequestDto {
  @ApiPropertyOptional({
    description: '文件处理选项',
    example: { quality: 'high', format: 'json' },
  })
  @IsOptional()
  @IsObject()
  options?: Record<string, any>;
}

/**
 * 批量请求DTO
 */
export class BatchRequestDto extends GatewayRequestDto {
  @ApiProperty({
    description: '批量请求数据',
    type: [Object],
    example: [
      { text: '地址1' },
      { text: '地址2' },
    ],
  })
  @IsObject({ each: true })
  items: any[];

  @ApiPropertyOptional({
    description: '批量处理选项',
    example: { parallel: true, maxConcurrency: 5 },
  })
  @IsOptional()
  @IsObject()
  batchOptions?: Record<string, any>;
}

/**
 * 任务查询DTO
 */
export class TaskQueryDto {
  @ApiPropertyOptional({
    description: '任务状态过滤',
    example: 'completed',
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiPropertyOptional({
    description: '开始时间',
    example: '2023-01-01T00:00:00Z',
  })
  @IsOptional()
  @IsString()
  startTime?: string;

  @ApiPropertyOptional({
    description: '结束时间',
    example: '2023-12-31T23:59:59Z',
  })
  @IsOptional()
  @IsString()
  endTime?: string;

  @ApiPropertyOptional({
    description: '页码',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  page?: number;

  @ApiPropertyOptional({
    description: '每页数量',
    example: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  limit?: number;
}
