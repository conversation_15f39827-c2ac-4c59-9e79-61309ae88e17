import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { 
  ExtractionResult, 
  ReverseGeocodeDto, 
  BatchReverseGeocodeDto, 
  ReverseGeocodeResult, 
  BatchReverseGeocodeResult 
} from './dto/address-extraction.dto';
import { EnhancedQueueManagerService } from '../queue/services/enhanced-queue-manager.service';
import { AddressExecutorService } from './services/address-executor.service';

/**
 * 地址服务
 * 负责处理地址相关的业务逻辑，包括权限验证、队列管理等
 */
@Injectable()
export class AddressService {
  private readonly logger = new Logger(AddressService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly queueManagerService: EnhancedQueueManagerService,
    private readonly addressExecutorService: AddressExecutorService,
  ) {}

  /**
   * 从单个文本中提取地址信息
   */
  async extractFromText(text: string, mode: 'single' | 'multiple' = 'single', userId?: number, apiKeyId?: string, serviceId?: number): Promise<ExtractionResult> {
    try {
      // 调用执行器服务处理具体业务
      return await this.addressExecutorService.extractFromText(text, mode, {
        userId,
        apiKey: apiKeyId,
        serviceCode: serviceId?.toString()
      });
    } catch (error) {
      this.logger.error(`地址提取服务失败: ${error.message}`, error.stack);
      return {
        success: false,
        addresses: [],
        error: error.message,
        originalText: text,
      };
    }
  }

  /**
   * 从多个文本中提取地址信息
   */
  async extractFromMultipleTexts(texts: string[], userId?: number, apiKeyId?: string, serviceId?: number): Promise<ExtractionResult[]> {
    try {
      // 调用执行器服务处理具体业务
      return await this.addressExecutorService.extractFromMultipleTexts(texts, {
        userId,
        apiKey: apiKeyId,
        serviceCode: serviceId?.toString()
      });
    } catch (error) {
      this.logger.error(`批量地址提取服务失败: ${error.message}`, error.stack);
      return texts.map(text => ({
        success: false,
        addresses: [],
        error: error.message,
        originalText: text,
      }));
    }
  }

  /**
   * 异步从单个文本中提取地址信息
   */
  async extractFromTextAsync(text: string, mode: 'single' | 'multiple' = 'single', callbackUrl?: string, userId?: number, apiKeyId?: string, serviceId?: number): Promise<any> {
    try {
      // 提交到队列
      const job = await this.queueManagerService.addJob(
        'extract-address',
        'address_extraction',
        {
          text,
          mode,
          callbackUrl,
          userId: userId || 0,
          apiKeyId: apiKeyId || '',
          serviceId: serviceId || 0,
        }
      );
      
      return {
        success: true,
        jobId: job.id,
        message: '任务已提交到队列',
      };
    } catch (error) {
      this.logger.error(`提交地址提取任务失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 异步从多个文本中提取地址信息
   */
  async extractFromMultipleTextsAsync(texts: string[], callbackUrl?: string, userId?: number, apiKeyId?: string, serviceId?: number): Promise<any> {
    try {
      // 批量提交到队列
      const jobs = await Promise.all(texts.map(text => 
        this.queueManagerService.addJob(
          'extract-address',
          'address_extraction',
          {
            text,
            mode: 'single',
            callbackUrl,
            userId: userId || 0,
            apiKeyId: apiKeyId || '',
            serviceId: serviceId || 0,
          }
        )
      ));
      
      return {
        success: true,
        jobIds: jobs.map(job => job.id),
        message: '任务已提交到队列',
      };
    } catch (error) {
      this.logger.error(`批量提交地址提取任务失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 获取地址提取任务状态
   */
  async getAddressExtractionTaskStatus(jobId: string): Promise<any> {
    try {
      const status = await this.queueManagerService.getJobStatus('extract-address', jobId);
      return {
        success: true,
        status,
      };
    } catch (error) {
      this.logger.error(`获取地址提取任务状态失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 地理坐标逆解析
   */
  async reverseGeocode(coordinates: ReverseGeocodeDto, userId?: number, apiKeyId?: string, serviceId?: number): Promise<ReverseGeocodeResult> {
    try {
      // 调用执行器服务处理具体业务
      return await this.addressExecutorService.reverseGeocode(coordinates, {
        userId,
        apiKey: apiKeyId,
        serviceCode: serviceId?.toString()
      });
    } catch (error) {
      this.logger.error(`地理坐标逆解析失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 批量地理坐标逆解析
   */
  async batchReverseGeocode(batchData: BatchReverseGeocodeDto, userId?: number, apiKeyId?: string, serviceId?: number): Promise<BatchReverseGeocodeResult> {
    try {
      // 调用执行器服务处理具体业务
      return await this.addressExecutorService.batchReverseGeocode(batchData, {
        userId,
        apiKey: apiKeyId,
        serviceCode: serviceId?.toString()
      });
    } catch (error) {
      this.logger.error(`批量地理坐标逆解析失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 异步地理坐标逆解析
   */
  async reverseGeocodeAsync(coordinates: ReverseGeocodeDto, callbackUrl?: string, userId?: number, apiKeyId?: string, serviceId?: number): Promise<any> {
    try {
      // 提交到队列
      const job = await this.queueManagerService.addJob(
        'reverse-geocode',
        'reverse_geocoding',
        {
          coordinates,
          callbackUrl,
          userId: userId || 0,
          apiKeyId: apiKeyId || '',
          serviceId: serviceId || 0,
        }
      );
      
      return {
        success: true,
        jobId: job.id,
        message: '任务已提交到队列',
      };
    } catch (error) {
      this.logger.error(`提交地理坐标逆解析任务失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 异步批量地理坐标逆解析
   */
  async batchReverseGeocodeAsync(batchData: BatchReverseGeocodeDto, callbackUrl?: string, userId?: number, apiKeyId?: string, serviceId?: number): Promise<any> {
    try {
      // 提交到队列
      const job = await this.queueManagerService.addJob(
        'reverse-geocode',
        'batch_reverse_geocoding',
        {
          coordinates: batchData.coordinates,
          callbackUrl,
          userId: userId || 0,
          apiKeyId: apiKeyId || '',
          serviceId: serviceId || 0,
        }
      );
      
      return {
        success: true,
        jobId: job.id,
        message: '任务已提交到队列',
      };
    } catch (error) {
      this.logger.error(`提交批量地理坐标逆解析任务失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 获取逆地理编码任务状态
   */
  async getReverseGeocodeTaskStatus(jobId: string): Promise<any> {
    try {
      const status = await this.queueManagerService.getJobStatus('reverse-geocode', jobId);
      return {
        success: true,
        status,
      };
    } catch (error) {
      this.logger.error(`获取逆地理编码任务状态失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 测试地理坐标逆解析
   * 使用预设的测试坐标验证地理坐标逆解析功能
   */
  async testReverseGeocode(): Promise<ReverseGeocodeResult> {
    try {
      // 使用北京天安门的坐标作为测试数据
      const testCoordinates: ReverseGeocodeDto = {
        latitude: 39.9042,
        longitude: 116.4074
      };
      
      this.logger.log(`执行测试地理坐标逆解析: ${testCoordinates.latitude}, ${testCoordinates.longitude}`);
      
      // 调用执行器服务处理具体业务
      return await this.addressExecutorService.reverseGeocode(testCoordinates, {
        userId: 0,
        apiKey: 'test-api-key',
        serviceCode: 'test-service'
      });
    } catch (error) {
      this.logger.error(`测试地理坐标逆解析失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: `测试地理坐标逆解析失败: ${error.message}`,
      };
    }
  }

  /**
   * 检查Python服务健康状态
   */
  async checkPythonServiceHealth(): Promise<boolean> {
    return await this.addressExecutorService.checkPythonServiceHealth();
  }
}