# 网关和队列系统配置
gateway:
  # 基础配置
  name: 'nest-open-gateway'
  version: '1.0.0'
  environment: 'development'
  
  # 性能配置
  performance:
    # 请求超时时间(ms)
    request_timeout: 30000
    # 最大并发请求数
    max_concurrent_requests: 1000
    # 连接池大小
    connection_pool_size: 50
    # 保持连接时间(ms)
    keep_alive_timeout: 60000
    
  # 限流配置
  rate_limiting:
    # 全局限流
    global:
      requests_per_minute: 1000
      requests_per_hour: 10000
      requests_per_day: 100000
    # 服务级别限流
    service_level:
      default_requests_per_minute: 100
      default_requests_per_hour: 1000
    # IP级别限流
    ip_level:
      requests_per_minute: 60
      requests_per_hour: 600
      
  # 熔断器配置
  circuit_breaker:
    # 失败阈值
    failure_threshold: 5
    # 失败率阈值(百分比)
    failure_rate_threshold: 50
    # 最小请求数
    minimum_requests: 10
    # 熔断器超时时间(ms)
    timeout: 60000
    # 半开状态允许的请求数
    half_open_max_calls: 3
    # 重置超时时间(ms)
    reset_timeout: 300000
    
  # 重试配置
  retry:
    # 最大重试次数
    max_attempts: 3
    # 重试延迟(ms)
    delay: 1000
    # 指数退避倍数
    backoff_multiplier: 2
    # 最大延迟(ms)
    max_delay: 10000
    # 可重试的HTTP状态码
    retryable_status_codes: [500, 502, 503, 504, 408, 429]
    
  # 负载均衡配置
  load_balancing:
    # 策略: round_robin, random, least_connections, weighted_round_robin
    strategy: 'round_robin'
    # 健康检查间隔(ms)
    health_check_interval: 30000
    # 健康检查超时(ms)
    health_check_timeout: 5000
    # 健康检查路径
    health_check_path: '/health'
    # 不健康阈值
    unhealthy_threshold: 3
    # 健康阈值
    healthy_threshold: 2
    
  # 缓存配置
  cache:
    # 启用缓存
    enabled: true
    # 默认TTL(秒)
    default_ttl: 300
    # 最大缓存大小(MB)
    max_size: 100
    # 缓存策略: lru, lfu, fifo
    eviction_policy: 'lru'
    # 缓存预热
    preload_enabled: true
    
  # 监控配置
  monitoring:
    # 启用监控
    enabled: true
    # 指标收集间隔(ms)
    metrics_interval: 10000
    # 保留历史数据天数
    retention_days: 30
    # 慢请求阈值(ms)
    slow_request_threshold: 5000
    # 错误率告警阈值(百分比)
    error_rate_alert_threshold: 5
    
  # 安全配置
  security:
    # API密钥验证
    api_key_validation:
      enabled: true
      header_name: 'X-API-Key'
      # 密钥过期检查
      check_expiration: true
    # IP白名单
    ip_whitelist:
      enabled: false
      allowed_ips: []
    # 请求签名验证
    request_signing:
      enabled: false
      algorithm: 'HMAC-SHA256'
    # CORS配置
    cors:
      enabled: true
      allowed_origins: ['*']
      allowed_methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
      allowed_headers: ['Content-Type', 'Authorization', 'X-API-Key']
      
# 队列系统配置
queue:
  # Redis配置
  redis:
    host: 'localhost'
    port: 6379
    password: 'root'
    db: 1
    # 连接池配置
    pool:
      min: 5
      max: 20
    # 重连配置
    retry:
      attempts: 3
      delay: 1000
      
  # 队列处理配置
  processing:
    # 处理间隔(ms)
    interval: 5000
    # 批处理大小
    batch_size: 10
    # 最大并发处理数
    max_concurrency: 5
    # 处理超时时间(ms)
    timeout: 300000
    
  # 任务配置
  jobs:
    # 默认配置
    default:
      # 最大重试次数
      max_attempts: 3
      # 重试延迟(ms)
      retry_delay: 5000
      # 任务超时时间(ms)
      timeout: 60000
      # 优先级(1-10)
      priority: 5
      
    # 异步请求任务
    async_request:
      max_attempts: 3
      retry_delay: 2000
      timeout: 30000
      priority: 7
      
    # 批量请求任务
    batch_request:
      max_attempts: 2
      retry_delay: 10000
      timeout: 300000
      priority: 5
      
    # 定时任务
    scheduled_task:
      max_attempts: 1
      retry_delay: 60000
      timeout: 120000
      priority: 3
      
  # 延迟队列配置
  delayed:
    # 检查间隔(ms)
    check_interval: 10000
    # 最大延迟时间(ms)
    max_delay: 86400000  # 24小时
    
  # 死信队列配置
  dead_letter:
    # 启用死信队列
    enabled: true
    # 死信队列名称
    queue_name: 'dead_letter_queue'
    # 保留时间(ms)
    retention_time: 604800000  # 7天
    
  # 监控配置
  monitoring:
    # 启用监控
    enabled: true
    # 统计间隔(ms)
    stats_interval: 60000
    # 保留统计数据天数
    stats_retention_days: 7
    # 队列长度告警阈值
    queue_length_alert_threshold: 1000
    # 处理时间告警阈值(ms)
    processing_time_alert_threshold: 60000
    
# 日志配置
logging:
  # 日志级别: error, warn, info, debug
  level: 'info'
  # 日志格式: json, text
  format: 'json'
  # 日志输出
  outputs:
    - type: 'console'
      enabled: true
    - type: 'file'
      enabled: true
      filename: 'logs/gateway.log'
      max_size: '100MB'
      max_files: 10
    - type: 'elasticsearch'
      enabled: false
      host: 'localhost:9200'
      index: 'gateway-logs'
      
  # 访问日志
  access_log:
    enabled: true
    format: 'combined'
    filename: 'logs/access.log'
    
  # 错误日志
  error_log:
    enabled: true
    filename: 'logs/error.log'
    include_stack_trace: true
    
# 告警配置
alerting:
  # 启用告警
  enabled: true
  # 告警渠道
  channels:
    - type: 'email'
      enabled: false
      smtp:
        host: 'smtp.example.com'
        port: 587
        username: '<EMAIL>'
        password: 'password'
      recipients: ['<EMAIL>']
    - type: 'webhook'
      enabled: false
      url: 'https://hooks.slack.com/services/xxx'
      
  # 告警规则
  rules:
    - name: 'high_error_rate'
      condition: 'error_rate > 5'
      duration: '5m'
      severity: 'critical'
    - name: 'high_response_time'
      condition: 'avg_response_time > 5000'
      duration: '3m'
      severity: 'warning'
    - name: 'queue_backlog'
      condition: 'queue_length > 1000'
      duration: '2m'
      severity: 'warning'
      
# 健康检查配置
health_check:
  # 启用健康检查
  enabled: true
  # 检查间隔(ms)
  interval: 30000
  # 超时时间(ms)
  timeout: 5000
  # 检查项目
  checks:
    - name: 'database'
      enabled: true
      timeout: 3000
    - name: 'redis'
      enabled: true
      timeout: 2000
    - name: 'external_services'
      enabled: true
      timeout: 5000
      
# 指标配置
metrics:
  # 启用指标收集
  enabled: true
  # 收集间隔(ms)
  collection_interval: 10000
  # 指标保留时间(天)
  retention_days: 30
  # 导出配置
  exporters:
    - type: 'prometheus'
      enabled: false
      port: 9090
      path: '/metrics'
    - type: 'influxdb'
      enabled: false
      host: 'localhost:8086'
      database: 'gateway_metrics'
      
# 开发环境特定配置
development:
  # 启用调试模式
  debug: true
  # 详细日志
  verbose_logging: true
  # 模拟延迟(ms)
  simulate_delay: 0
  # 模拟错误率(百分比)
  simulate_error_rate: 0
  
# 生产环境特定配置
production:
  # 启用性能优化
  performance_optimization: true
  # 启用压缩
  compression: true
  # 启用缓存
  caching: true
  # 安全模式
  security_mode: 'strict'