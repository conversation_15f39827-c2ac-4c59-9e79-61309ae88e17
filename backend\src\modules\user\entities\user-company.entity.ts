import { Enti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON>olumn } from 'typeorm';
import { BaseEntity } from '@/common/entities/base.entity';
import { UserEntity } from './user.entity';
import { ReviewStatus } from './user-verification.entity';

@Entity('user_company')
export class UserCompanyEntity extends BaseEntity {
  @Column({ type: 'int', name: 'user_id', comment: '用户ID' })
  userId: number;

  @Column({
    type: 'enum',
    enum: ReviewStatus,
    name: 'reviewed_status',
    default: ReviewStatus.PENDING,
    comment: '审核状态'
  })
  reviewedStatus: ReviewStatus;

  @Column({ type: 'varchar', comment: '企业名称', nullable: true })
  enterprise: string;

  @Column({ type: 'varchar', name: 'license_no', length: 50, default: '', comment: '营业执照号' })
  licenseNo: string;

  @Column({ type: 'varchar', name: 'license_image', default: '', comment: '营业执照照片' })
  licenseImage: string;

  @Column({ type: 'varchar', name: 'legal_person', length: 30, default: '', comment: '法人姓名' })
  legalPerson: string;

  @Column({ type: 'varchar', name: 'address', length: 200, default: '', comment: '企业地址' })
  address: string;

  @Column({ type: 'varchar', name: 'contact_name', length: 30, default: '', comment: '联系人姓名' })
  contactName: string;

  @Column({ type: 'varchar', name: 'contact_number', length: 30, default: '', comment: '联系人电话' })
  contactNumber: string;

  @Column({ type: 'varchar', name: 'contact_phone', length: 20, default: '', comment: '联系人手机' })
  contactPhone: string;

  @Column({ name: 'reviewed_at', type: 'datetime', nullable: true, comment: '审核时间' })
  reviewedAt: Date;

  @Column({ name: 'reviewer_id', type: 'int', nullable: true, comment: '审核人ID' })
  reviewerId: number;

  // 关联关系
  @ManyToOne(() => UserEntity, user => user.id)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;
}
