<template>
  <div class="usage-page">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">使用统计</h1>
        <p class="page-subtitle">查看您的API使用情况和统计数据</p>
      </div>
      <div class="header-right">
        <!-- <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="loadUsageData" /> -->
      </div>
    </div>

    <!-- 服务使用情况概览 -->
    <div class="service-usage-overview">
      <div class="section-title">
        <h2>服务使用概览</h2>
      </div>
      <div v-if="serviceUsageData.length > 0" class="service-cards">
        <div v-for="service in serviceUsageData" :key="service.id" class="service-card">
          <div class="service-card-header">
            <h3>{{ service?.service?.name }}</h3>
            <el-tag size="small" :type="getServiceTypeColor(service?.service?.type)">{{ service?.service?.type
            }}</el-tag>
          </div>
          <div class="service-card-body">
            <div class="usage-stat">
              <div class="stat-label">可调用总次数</div>
              <div class="stat-value">{{ formatNumber(service?.totalCount) }}</div>
            </div>
            <div class="usage-stat">
              <div class="stat-label">已使用次数</div>
              <div class="stat-value">{{ formatNumber(service?.usedCount) }}</div>
            </div>
            <div class="usage-stat">
              <div class="stat-label">剩余次数</div>
              <div class="stat-value" :class="{ 'warning': isQuotaLow(service) }">
                {{ formatNumber(~~service?.totalCount - ~~service?.usedCount) }}
              </div>
            </div>
            <div class="usage-progress">
              <div class="progress-label">使用进度</div>
              <el-progress :percentage="calculateUsagePercentage(service)" :status="getProgressStatus(service)"
                :stroke-width="10" :format="percentageFormatter" />
            </div>
          </div>
        </div>
      </div>
      <div v-else class="empty-data">
        <el-empty description="暂无服务使用数据" :image-size="120">
          <template #image>
            <el-icon :size="64" color="#C0C4CC">
              <DataAnalysis />
            </el-icon>
          </template>
        </el-empty>
      </div>
    </div>

    <!-- 概览统计 -->
    <div class="overview-section">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon color="#409eff">
            <DataAnalysis />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatNumber(totalCalls) }}</div>
          <div class="stat-label">总调用次数</div>
          <div class="stat-change positive">+12.5%</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <el-icon color="#67c23a">
            <SuccessFilled />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatNumber(successCalls) }}</div>
          <div class="stat-label">成功调用</div>
          <div class="stat-change positive">+8.3%</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <el-icon color="#f56c6c">
            <CircleClose />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatNumber(errorCalls) }}</div>
          <div class="stat-label">错误调用</div>
          <div class="stat-change negative">-2.1%</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <el-icon color="#e6a23c">
            <Timer />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ avgResponseTime }}ms</div>
          <div class="stat-label">平均响应时间</div>
          <div class="stat-change positive">-5.2%</div>
        </div>
      </div>
    </div>


    <!-- 服务使用详情 -->
    <div class="service-usage-detail">
      <div class="section-title">
        <h2>服务使用详情</h2>
      </div>
      <div v-if="serviceUsageData.length > 0">
        <el-tabs v-model="activeServiceTab">
          <el-tab-pane v-for="service in serviceUsageData" :key="service.id" :label="service?.service?.name"
            :name="service.id">
            <div class="service-detail-card">
              <div class="service-info">
                <div class="info-row">
                  <span class="info-label">服务描述:</span>
                  <span class="info-value">{{ service?.service?.description }}</span>
                </div>
                <!-- <div class="info-row">
                  <span class="info-label">API地址:</span>
                  <span class="info-value code">{{ service?.service?.endpoint }}</span>
                </div> -->
                <div class="info-row">
                  <span class="info-label">单价:</span>
                  <span class="info-value code">{{ service?.service?.unitPrice }}/每次</span>
                </div>
                <div class="info-row">
                  <span class="info-label">服务状态:</span>
                  <el-tag size="small" :type="service?.service?.status === 'active' ? 'success' : 'danger'">
                    {{ service?.service?.status === 'active' ? '正常' : '维护中' }}
                  </el-tag>
                </div>
              </div>

              <div class="quota-info">
                <div class="quota-card">
                  <div class="quota-title">套餐用量</div>
                  <div class="quota-stats">
                    <div class="quota-stat">
                      <div class="stat-name">总次数</div>
                      <div class="stat-value">{{ formatNumber(service?.totalCount) }}</div>
                    </div>
                    <div class="quota-stat">
                      <div class="stat-name">已使用</div>
                      <div class="stat-value">{{ formatNumber(service?.usedCount) }}</div>
                    </div>
                    <div class="quota-stat">
                      <div class="stat-name">剩余</div>
                      <div class="stat-value" :class="{ 'warning': isQuotaLow(service) }">
                        {{ formatNumber(~~service?.totalCount - ~~service?.usedCount) }}
                      </div>
                    </div>
                  </div>
                  <div class="quota-progress">
                    <el-progress :percentage="calculateUsagePercentage(service)" :status="getProgressStatus(service)"
                      :stroke-width="15" />
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div v-else class="empty-data">
        <el-empty description="暂无服务使用详情" :image-size="120">
          <template #image>
            <el-icon :size="64" color="#C0C4CC">
              <Document />
            </el-icon>
          </template>
        </el-empty>
      </div>
    </div>

    <!-- 详细记录 -->
    <!-- <div class="records-section">
      <div class="section-header">
        <h2>调用记录</h2>
        <div class="filters">
          <el-select v-model="serviceFilter" placeholder="服务类型" style="width: 150px;">
            <el-option v-for="service in services" :key="service.value" :label="service.label" :value="service.value" />
          </el-select>

          <el-select v-model="statusFilter" placeholder="状态" style="width: 100px;">
            <el-option v-for="status in statusOptions" :key="status.value" :label="status.label"
              :value="status.value" />
          </el-select>

          <el-input v-model="searchKeyword" placeholder="搜索服务或API密钥" style="width: 200px;" clearable
            @keyup.enter="handleSearch">
            <template #prefix>
              <el-icon>
                <Search />
              </el-icon>
            </template>
</el-input>

</div>
</div>

<div class="records-table">
  <el-table :data="filteredRecords" v-loading="loading" stripe style="width: 100%">
    <el-table-column prop="timestamp" label="时间" width="180">
      <template #default="{ row }">
              {{ dayjs(row.timestamp).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
    </el-table-column>

    <el-table-column prop="serviceName" label="服务" width="120">
      <template #default="{ row }">
              <el-tag type="primary" size="small">
                {{ row.serviceName }}
              </el-tag>
            </template>
    </el-table-column>

    <el-table-column prop="endpoint" label="接口" min-width="200" />

    <el-table-column prop="method" label="方法" width="80">
      <template #default="{ row }">
              <el-tag :type="row.method === 'GET' ? 'success' : 'primary'" size="small">
                {{ row.method }}
              </el-tag>
            </template>
    </el-table-column>

    <el-table-column prop="statusCode" label="状态" width="80">
      <template #default="{ row }">
              <el-tag :type="getStatusType(row.statusCode)" size="small">
                {{ getStatusText(row.statusCode) }}
              </el-tag>
            </template>
    </el-table-column>

    <el-table-column prop="responseTime" label="响应时间" width="100">
      <template #default="{ row }">
              {{ row.responseTime }}ms
            </template>
    </el-table-column>

    <el-table-column prop="cost" label="费用" width="80">
      <template #default="{ row }">
              {{ formatCurrency(row.cost) }}
            </template>
    </el-table-column>

    <el-table-column prop="apiKeyName" label="API密钥" width="150">
      <template #default="{ row }">
              <span class="api-key-text">{{ row.apiKeyName }}</span>
            </template>
    </el-table-column>

    <el-table-column label="操作" width="100">
      <template #default="{ row }">
              <el-button text size="small" @click="viewRecordDetail(row)">
                详情
              </el-button>
            </template>
    </el-table-column>
  </el-table>

  <div class="pagination">
    <el-pagination v-model:current-page="pagination.page" v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange" @current-change="handlePageChange" />
  </div>
</div>
</div> -->

    <!-- 记录详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="调用详情" width="600px">
      <div v-if="selectedRecord" class="record-detail">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">时间:</span>
              <span class="value">{{ dayjs(selectedRecord.timestamp).format('YYYY-MM-DD HH:mm:ss') }}</span>
            </div>
            <div class="detail-item">
              <span class="label">服务:</span>
              <span class="value">{{ selectedRecord.serviceName }}</span>
            </div>
            <div class="detail-item">
              <span class="label">接口:</span>
              <span class="value">{{ selectedRecord.endpoint }}</span>
            </div>
            <div class="detail-item">
              <span class="label">方法:</span>
              <span class="value">{{ selectedRecord.method }}</span>
            </div>
            <div class="detail-item">
              <span class="label">状态:</span>
              <span class="value">
                <el-tag :type="getStatusType(selectedRecord.statusCode)" size="small">
                  {{ getStatusText(selectedRecord.statusCode) }}
                </el-tag>
              </span>
            </div>
            <div class="detail-item">
              <span class="label">响应时间:</span>
              <span class="value">{{ selectedRecord.responseTime }}ms</span>
            </div>
            <div class="detail-item">
              <span class="label">费用:</span>
              <span class="value">{{ formatCurrency(selectedRecord.cost) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">API密钥:</span>
              <span class="value">{{ selectedRecord.apiKeyName }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>请求信息</h4>
          <div class="code-block">
            <pre>{{ JSON.stringify(selectedRecord.request, null, 2) }}</pre>
          </div>
        </div>

        <div class="detail-section">
          <h4>响应信息</h4>
          <div class="code-block">
            <pre>{{ JSON.stringify(selectedRecord.response, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import {
  DataAnalysis,
  SuccessFilled,
  CircleClose,
  Timer,
  Download,
  Refresh,
  Search,
  Document
} from '@element-plus/icons-vue'
import { useUsageStore } from '@/stores/usage'
import { useServiceStore } from '@/stores/service'
import { useUserStore } from '@/stores/user'
import type { TimeRange, UsageQueryParams } from '@/types/usage'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'

const usageStoreInstance = useUsageStore()
const serviceStore = useServiceStore()
const userStore = useUserStore()


// 响应式数据
const showDetailDialog = ref(false)
const selectedRecord = ref<any>(null)
const dateRange = ref<[string, string]>()
const trendPeriod = ref<TimeRange>('30d')
const searchKeyword = ref('')
const serviceFilter = ref('')
const statusFilter = ref('')

// 新增服务使用统计相关数据
const activeServiceTab = ref('')
const serviceUsageData = ref<any[]>([])
const lastWeekDays = computed(() => {
  return Array(7).fill(0).map((_, i) => {
    return dayjs().subtract(6 - i, 'day').format('MM-DD')
  })
})

// 响应时间范围和错误统计数据
const responseTimeRanges = ref([
  { label: '< 100ms', percentage: 45, count: 1250 },
  { label: '100-500ms', percentage: 35, count: 980 },
  { label: '500ms-1s', percentage: 15, count: 420 },
  { label: '> 1s', percentage: 5, count: 140 }
])

const errorStats = ref([
  { code: '400', message: '请求参数错误', count: 45 },
  { code: '401', message: '认证失败', count: 23 },
  { code: '403', message: '权限不足', count: 12 },
  { code: '500', message: '服务器内部错误', count: 8 }
])

// 从store获取数据
const {
  loading,
  stats: statsRef,
  records: recordsRef,
  pagination,
  totalCalls
} = usageStoreInstance

// 获取其他属性
const serviceDistribution = computed(() => usageStoreInstance.serviceDistribution || [])
const currentTimeRange = computed(() => usageStoreInstance.currentTimeRange)

// 服务列表
const services = computed(() => {
  const allServices = [{ value: '', label: '全部服务' }]
  if (serviceStore.services) {
    allServices.push(...serviceStore.services.map((service: any) => ({
      value: service.id,
      label: service.name
    } as any)))
  }
  return allServices
})

// 状态列表
const statusOptions = ref([
  { value: '', label: '全部状态' },
  { value: 'success', label: '成功' },
  { value: 'error', label: '失败' }
])

// 计算属性
const successCalls = computed(() => {
  return statsRef?.successCalls || 0
})

const errorCalls = computed(() => {
  return statsRef?.errorCalls || 0
})

const avgResponseTime = computed(() => {
  return statsRef?.averageResponseTime ? Math.round(statsRef.averageResponseTime) : 0
})

const filteredRecords = computed(() => {
  let filteredData = recordsRef || []

  if (searchKeyword.value) {
    filteredData = filteredData.filter((record: any) =>
      record.serviceName.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      record.apiKeyName.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  return filteredData
})

// 计算使用百分比
const calculateUsagePercentage = (service: any) => {
  if (!service.totalCount) return 0
  return Math.min(100, Math.round((service.usedCount / service.totalCount) * 100))
}

// 判断配额是否较低
const isQuotaLow = (service: any) => {
  return service.remainingQuota / service.totalCount < 0.2
}

// 获取进度条状态
const getProgressStatus = (service: any) => {
  const percentage = calculateUsagePercentage(service)
  if (percentage >= 90) return 'exception'
  if (percentage >= 70) return 'warning'
  return 'success'
}

// 获取服务类型颜色
const getServiceTypeColor = (type: string) => {
  const typeMap: Record<string, string> = {
    'OCR': 'primary',
    'NLP': 'success',
    'DATA': 'warning',
  }

  for (const key in typeMap) {
    if (type && type.includes(key)) {
      return typeMap[key]
    }
  }

  return 'info'
}

// 格式化百分比
const percentageFormatter = (percentage: number) => {
  return `${percentage}%`
}

// 计算柱状图高度
const calculateBarHeight = (value: number, values: number[]) => {
  const max = Math.max(...values, 1)
  return `${Math.max(10, (value / max) * 100)}%`
}

// 方法
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num?.toString()
}

const formatCurrency = (amount: number) => {
  return `¥${amount.toFixed(2)}`
}

const getStatusType = (statusCode: number) => {
  return statusCode >= 200 && statusCode < 300 ? 'success' : 'danger'
}

const getStatusText = (statusCode: number) => {
  return statusCode >= 200 && statusCode < 300 ? '成功' : '失败'
}

// 查看记录详情
const viewRecordDetail = (record: any) => {
  selectedRecord.value = record
  showDetailDialog.value = true
}

// 防抖标志
const isLoadingData = ref(false)

const loadUsageData = async () => {
  // 防止重复调用
  if (isLoadingData.value) {
    console.log('数据正在加载中，跳过重复调用')
    return
  }

  try {
    isLoadingData.value = true

    // 如果userInfo还没加载，先拉取
    if (!userStore.userInfo?.id) {
      await userStore.checkAndFetchUserInfo()
    }
    const params: any = {
      userId: userStore.userInfo?.id
    }
    try {
      const serviceData: any = await usageStoreInstance.fetchUsageRecords(params)
      serviceUsageData.value = serviceData
      activeServiceTab.value = serviceData[0]?.id
      console.log('serviceUsageData', serviceUsageData.value)

    } catch (error) {
      console.error('Failed to load usage records:', error)
    }

  } catch (error) {
    console.error('Error loading usage data:', error)
    ElMessage.error('加载使用数据失败，请稍后重试')
  } finally {
    isLoadingData.value = false
  }
}

const handleSearch = () => {
  usageStoreInstance.setPagination(1)
  loadUsageData()
}

const handlePageChange = (page: number) => {
  usageStoreInstance.setPagination(page)
  loadUsageData()
}

const handleSizeChange = (size: number) => {
  usageStoreInstance.setPagination(1, size)
  loadUsageData()
}

const handleTimeRangeChange = (range: TimeRange) => {
  usageStoreInstance.setTimeRange(range)
  loadUsageData()
}

// 初始化完成标志
const isInitialized = ref(false)

// 监听器
watch(trendPeriod, (newPeriod) => {
  if (isInitialized.value) {
    handleTimeRangeChange(newPeriod)
  }
})

watch([serviceFilter, statusFilter], () => {
  if (isInitialized.value) {
    loadUsageData()
  }
})

watch(dateRange, () => {
  if (isInitialized.value && dateRange.value) {
    loadUsageData()
  }
}, { deep: true })

// 初始化
onMounted(async () => {
  try {
    // 设置默认日期范围
    const endDate = dayjs().format('YYYY-MM-DD')
    const startDate = dayjs().subtract(30, 'day').format('YYYY-MM-DD')
    dateRange.value = [startDate, endDate]

    // 加载服务列表
    await serviceStore.getServices()

    // 等待下一个tick，确保所有响应式数据都已设置
    await nextTick()

    // 标记初始化完成
    isInitialized.value = true

    // 加载使用数据
    await loadUsageData()
  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage.error('页面初始化失败')
  }
})
</script>

<style scoped>
.usage-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #606266;
  margin: 0;
  font-size: 1rem;
}

.header-right {
  flex-shrink: 0;
}

.overview-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #67c23a;
}

.stat-change.negative {
  color: #f56c6c;
}

.charts-section {
  margin-bottom: 32px;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.chart-content {
  height: 300px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mock-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-bars {
  flex: 1;
  display: flex;
  align-items: flex-end;
  gap: 8px;
  padding: 20px 0;
}

.bar {
  flex: 1;
  background: linear-gradient(to top, #409eff, #79bbff);
  border-radius: 4px 4px 0 0;
  min-height: 20px;
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #606266;
}

.service-distribution {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px 0;
}

.service-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.service-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.service-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.service-calls {
  font-size: 12px;
  color: #606266;
}

.service-bar {
  flex: 1;
  height: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  overflow: hidden;
}

.service-progress {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s;
}

.service-percentage {
  width: 40px;
  text-align: right;
  font-size: 12px;
  color: #606266;
}

.response-time-chart {
  padding: 20px 0;
}

.time-ranges {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.time-range {
  display: flex;
  align-items: center;
  gap: 12px;
}

.range-label {
  width: 80px;
  font-size: 14px;
  color: #303133;
}

.range-bar {
  flex: 1;
  height: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  overflow: hidden;
}

.range-fill {
  height: 100%;
  background: #409eff;
  border-radius: 4px;
  transition: width 0.3s;
}

.range-value {
  width: 60px;
  text-align: right;
  font-size: 12px;
  color: #606266;
}

.error-analysis {
  padding: 20px 0;
}

.error-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f2f5;
}

.error-item:last-child {
  border-bottom: none;
}

.error-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.error-code {
  font-size: 14px;
  font-weight: 600;
  color: #f56c6c;
}

.error-message {
  font-size: 12px;
  color: #606266;
}

.error-count {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.records-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.filters {
  display: flex;
  gap: 12px;
}

.records-table {
  margin-bottom: 24px;
}

.api-key-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #606266;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.record-detail {
  max-height: 60vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.detail-item {
  display: flex;
  gap: 8px;
}

.detail-item .label {
  color: #909399;
  font-size: 14px;
  width: 80px;
  flex-shrink: 0;
}

.detail-item .value {
  color: #303133;
  font-size: 14px;
  flex: 1;
}

.code-block {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;
}

.code-block pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .overview-section {
    grid-template-columns: 1fr;
  }

  .chart-row {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filters {
    flex-direction: column;
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }
}

/* 新增服务使用概览样式 */
.section-title {
  margin-bottom: 20px;
}

.section-title h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.service-usage-overview {
  margin-bottom: 32px;
}

.service-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.service-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.service-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.service-card-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.service-card-body {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.usage-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.stat-value {
  font-weight: 600;
  color: #303133;
}

.stat-value.warning {
  color: #e6a23c;
}

.usage-progress {
  margin-top: 8px;
}

.progress-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

/* 服务使用详情样式 */
.service-usage-detail {
  margin-bottom: 32px;
}

.service-detail-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.service-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: flex;
  gap: 8px;
}

.info-label {
  color: #909399;
  font-size: 14px;
  width: 80px;
  flex-shrink: 0;
}

.info-value {
  color: #303133;
  font-size: 14px;
  flex: 1;
}

.info-value.code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f5f7fa;
  padding: 2px 4px;
  border-radius: 4px;
}

.quota-info {
  grid-column: 1 / -1;
}

.quota-card {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
}

.quota-title {
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
}

.quota-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.quota-stat {
  text-align: center;
}

.stat-name {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.quota-progress {
  margin-top: 8px;
}

.usage-trend {
  grid-column: 1 / -1;
}

.trend-title {
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .service-cards {
    grid-template-columns: 1fr;
  }

  .service-detail-card {
    grid-template-columns: 1fr;
  }
}

.feature-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.empty-data {
  background: white;
  border-radius: 12px;
  padding: 40px 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  margin-bottom: 32px;
}

/* 记录列表 */
.records-section {
  margin-bottom: 32px;
}
</style>