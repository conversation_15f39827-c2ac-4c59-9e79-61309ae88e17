services:
  vue-frontend:
    build:
      context: ./vue3-open
      args:
        - NODE_OPTIONS=--max_old_space_size=2048  # 限制Node.js内存使用
    container_name: open-platform-frontend
    restart: always
    ports:
      - "3000:80"
    # 添加资源限制
    deploy:
      resources:
        limits:
          cpus: '1.5'  # 限制CPU使用
          memory: 2G   # 限制内存使用
    networks:
      - open-platform-network

# 添加这个网络定义部分
networks:
  open-platform-network:
    driver: bridge