import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
} from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';

import { ServiceType, ServiceStatus, PricingModel } from '../dto/service.dto';

/**
 * API服务实体
 */
@Entity('open_service', {
  comment: 'API服务表',
})
export class ServiceEntity extends BaseEntity {
  @Column({ type: 'int', name: 'user_id', comment: '用户ID' })
  public userId: number;

  @Column({ type: 'int', name: 'service_id', comment: '服务ID' })
  public serviceId: number;

  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
    comment: '服务代码',
  })
  code: string;

  @Column({
    type: 'varchar',
    length: 200,
    comment: '服务名称',
  })
  name: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: '服务描述',
  })
  description?: string;

  @Column({
    type: 'enum',
    enum: ServiceType,
    comment: '服务类型：OCR、NLP、CV、AI、DATA、OTHER等',
  })
  type: ServiceType;

  @Column({
    type: 'enum',
    enum: ServiceStatus,
    comment: '服务状态枚举：active、inactive、maintenance、deprecated等',
  })
  serviceStatus: ServiceStatus;

  @Column({
    type: 'enum',
    enum: PricingModel,
    comment: '定价模式枚举：free、pay_per_use、subscription、tiered等',
  })
  pricingModel: PricingModel;

  @Column({
    type: 'varchar',
    length: 20,
    default: 'v1.0.0',
    comment: '当前版本',
  })
  currentVersion: string;

  @Column({
    type: 'varchar',
    length: 200,
    default: '',
    comment: '服务特性',
  })
  features: string;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '服务端点URL',
  })
  endpoint?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: '服务配置参数',
    transformer: {
      to: (value: Record<string, any>): string => value ? JSON.stringify(value) : '{}',
      from: (value: string): Record<string, any> => value ? JSON.parse(value) : {},
    }
  })
  config?: Record<string, any>;

  @Column({
    type: 'int',
    default: 0,
    comment: '调用次数',
  })
  callCount: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0.0,
    comment: '服务费用（每次调用）',
  })
  price: number;

  @Column({
    type: 'int',
    default: 1000,
    comment: '每日调用限制',
  })
  dailyLimit: number;

  @Column({
    type: 'int',
    default: 100,
    comment: '每分钟调用限制',
  })
  minuteLimit: number;

  @Column({
    type: 'tinyint',
    default: 1,
    comment: '是否需要认证：0-否，1-是',
  })
  requireAuth: number;

  @Column({
    type: 'tinyint',
    default: 0,
    comment: '是否异步处理：0-否，1-是',
  })
  isAsync: number;

  @Column({
    type: 'int',
    default: 30,
    comment: '超时时间（秒）',
  })
  timeout: number;

  // 关联关系 - 使用字符串关系定义，避免循环引用

  @OneToMany('ApiKeyEntity', 'service')
  apiKeys: any[];

  @OneToMany('UserServiceEntity', 'service')
  userServices: any[];

  @OneToMany('OrderEntity', 'service')
  orders: any[];

  @OneToMany('CallRecordEntity', 'service')
  callRecords: any[];

  @OneToMany('AlertRecordEntity', 'service')
  alertRecords: any[];
}
