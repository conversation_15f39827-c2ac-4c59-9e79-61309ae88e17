# Python 开发编码规范（python-service）

目标
- 产出高质量、可扩展、易维护、可观测、可测试的代码
- 与阿里云部署环境、现有依赖与网关契约保持一致

语言与运行时
- Python 版本：优先 3.10（容器内），兼容 3.8+；避免使用 3.11+ 的新语法特性
- 字符编码：UTF-8；时区：Asia/Shanghai

项目结构与分层
- 分层目录（建议）：
  - `app/`
    - `api/`（路由与请求响应模型，仅编排，不写业务）
    - `services/`（业务逻辑：地址解析、OCR、坐标转换）
    - `adapters/`（第三方适配：Nominatim、条码库、存储、缓存）
    - `core/`（通用能力：配置、异常、日志、校验、缓存）
    - `schemas/`（数据模型与类型：Pydantic 或 dataclasses）
    - `utils/`（与业务无关的纯工具：时间、IO、图像处理）
    - `resources/`（词典、映射、模板、小型 JSON/CSV 资源）
  - `tests/`（单元 / 集成 / 端到端）
- 原则：
  - API 不依赖具体第三方库，调用 `services`/`adapters` 接口
  - 业务代码可通过依赖注入传入适配器（便于替换与测试）

代码风格与工具
- 统一风格：
  - 格式化：Black（行宽 100）
  - 导入排序：isort（profile=black）
  - 质量检查：Ruff（替代大部分 flake8 规则）
  - 类型检查：mypy（严格模式，忽略第三方缺失类型）
- 命名：
  - 模块/包：小写 + 下划线；类：PascalCase；函数/变量：snake_case
  - 常量：UPPER_SNAKE_CASE；避免 1-2 字母短名
- 注释与文档：
  - 函数/类使用 Google 风格 docstring；解释“为何”而非“如何”
  - 复杂分支前给出简短注释；禁止无意义注释

类型标注与数据模型
- 公共接口与服务层函数必须完整类型标注（参数 + 返回值）
- 优先使用 `typing`：`TypedDict`/`NamedTuple`/`Literal`/`Union`/`Optional`/`Annotated`
- 请求/响应与内部对象：推荐使用 Pydantic v1/v2（如暂不引入，则使用 `dataclasses` + 验证器）
- 禁用 `Any` 与裸字典在跨层传递；必要时用 `dict[str, Any]` 严格收口

错误处理与返回规范
- 统一异常类型：
  - `AppError(code: int, message: str, http_status: int=400)`
  - 子类：`ValidationError`、`RateLimitError`、`UpstreamError`、`TimeoutError`、`FeatureInDevelopment`
- 统一响应包装：`{"success": bool, "code": int, "message": str, "data": any}`
- 错误码：与《模块需求与规格说明.md/统一 API 约定》保持一致（0、40001、40002、41301、41501、42901、50001、50201、50401、40801、40003、50101）

日志与可观测性
- 结构化日志：JSON 行，字段包含 `timestamp`、`level`、`event`、`message`、`x_request_id`、`latency_ms`
- 入口统一生成/接收 `X-Request-Id`；上下文透传至服务层与适配器
- 关键事件：
  - OCR：模型加载、推理耗时、图片尺寸、压缩参数、识别结果条数
  - 地址解析：文本长度、解析阶段耗时、置信度、字典命中率
  - 地理（预留）：坐标系与自动识别分数、Nominatim 调用耗时

配置管理
- 配置来源：环境变量（优先）+ `.env`（开发）
- 统一 `Settings`：
  - 服务端口、超时（TIMEOUT、OCR_TIMEOUT）、镜像源（PIP 源）、文件限制
  - 开关：`DISABLE_PADDLE_DOWNLOAD`、`ENABLE_BARCODE`
- 不在代码中硬编码路径/密钥；通过配置或资源文件提供

依赖与环境
- 依赖固定版本，按季度评估升级；国内镜像优先（阿里云）
- 模型与词典缓存采用本地路径（可配置），启动期预热
- 禁止运行时在线下载模型（生产）

性能与并发
- Gunicorn 建议：`workers = CPU核数`，`threads = 2`；OCR 可考虑 `gevent`/多进程
- 模型单例：进程内懒加载，健康检查检测状态，失败熔断
- 图像处理：限制 `max_size`、`quality` 并做早限流；避免无界内存分配
- 地址解析：词典与正则预编译；批量接口使用向量化/并行（受限于 GIL，批量走多进程）

安全规范
- 输入校验：
  - 图片：MIME、扩展名、像素、体积、Base64 长度
  - 文本：长度、非法字符、控制字符过滤
- 敏感信息脱敏：日志/错误返回中对手机号等脱敏
- 文件安全：使用临时目录，处理后即清理；禁止将上传文件落盘长期保存

测试规范
- 测试框架：pytest + pytest-cov；覆盖率目标：业务核心 ≥ 85%
- 层次：
  - 单元：纯函数/服务层（mock 适配器）
  - 集成：端点到服务（mock 外部服务）
  - 端到端：真实 OCR/条码/地址词典（小样本集）
- 基准：典型图片/文本样例库（正例/反例/边界）

提交与评审
- 提交前：`ruff check --fix`、`black`、`isort`、`pytest -q`、`mypy`
- PR 要求：
  - 变更说明、影响范围、回滚方案
  - 新增/更新测试用例与文档
- 代码评审关注：命名、分层、异常处理、日志、性能与边界条件

接口与约定
- API 响应契约与错误码严格遵循《模块需求与规格说明.md》
- 端点命名与参数：使用无前缀路径（/ocr/*、/address/*、/geo/*）

目录示例（建议）
```
python-service/
  app/
    api/
      ocr_api.py
      address_api.py
      geo_api.py
    services/
      ocr_service.py
      address_service.py
      geo_service.py  # 预留（开发中）
    adapters/
      barcode_adapter.py
      nominatim_adapter.py  # 预留
    core/
      settings.py
      logging.py
      exceptions.py
      validators.py
    schemas/
      ocr.py
      address.py
      geo.py
    utils/
      image.py
      text.py
    resources/
      addresses/
        alias.json
        poi_frequent.txt
      sto/
        branch_map.json
      keywords/
        address_keywords.txt
  tests/
    unit/
    integration/
    e2e/
```

文档与示例
- 所有公共函数提供最小可运行示例或伪代码片段
- 对复杂策略（如坐标自动识别、面单后处理）在 docs 中给出决策流程图与测试样例链接 