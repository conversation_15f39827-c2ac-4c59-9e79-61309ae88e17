-- 种子数据SQL语句（仅数据，不含表结构）
-- 生成时间: 2025-07-13T14:19:01.929Z

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS=0;


-- 管理员用户
INSERT INTO open_user (username, email, nickname, realName, password, role, userStatus, userType, verificationStatus, tier, emailVerified, phoneVerified, bio, gender, loginFailCount, remainingUsage, totalUsage, dailyFreeUsageRemaining, balance, preferences, settings) 
VALUES ('admin', '<EMAIL>', '系统管理员', '系统管理员', '$2b$10$PTWGL.f4i2yqZmpOA8w/bOrGkuq/9mQvmV.GBFFQ40VOV9PSUJsHq', 'admin', 'active', 'organization', 'enterprise_verified', 'enterprise', 1, 0, '系统默认管理员账户', 'unknown', 0, 999999999, 0, 999999, 10000.00, '{"language":"zh-CN","theme":"light","timezone":"Asia/Shanghai"}', '{"notifications":{"email":true,"sms":false,"push":true},"security":{"twoFactorAuth":false,"loginNotification":true}}');

-- 测试用户
INSERT INTO open_user (username, email, nickname, realName, password, role, userStatus, userType, verificationStatus, tier, emailVerified, phoneVerified, phone, bio, gender, loginFailCount, remainingUsage, totalUsage, dailyFreeUsageRemaining, balance, preferences, settings) 
VALUES ('testuser', '<EMAIL>', '测试用户', '张测试', '$2b$10$aQitxudmoAhCmPDw4FFOfuUU0UUqdXCpJ061ZcLsP7.udfMbzV52W', 'user', 'active', 'individual', 'personal_verified', 'basic', 1, 1, '13800138000', '测试账户，用于演示和测试', 'male', 0, 1000, 0, 100, 100.00, '{"language":"zh-CN","theme":"light","timezone":"Asia/Shanghai"}', '{"notifications":{"email":true,"sms":true,"push":true},"security":{"twoFactorAuth":false,"loginNotification":true}}');

-- 管理员API密钥
INSERT INTO open_api_key (user_id, service_id, name, apiKey, secretHash, keyType, `key-status`, description, permissions, is_secret_viewed) 
VALUES (1, 0, '管理员主密钥', 'ak_admin_test_key_12345678901234567890', '$2b$10$lbTUrWLVR9hgwKJyos.1S.z2nesGr3CMTnyR5iLaYhcLf9t8Zh92u', 'enterprise', 'active', '管理员全局访问密钥，可以访问所有服务', '["*:*:*"]', 1);

-- 测试用户API密钥
INSERT INTO open_api_key (user_id, service_id, name, apiKey, secretHash, keyType, `key-status`, description, permissions, is_secret_viewed) 
VALUES (2, 0, '测试用户主密钥', 'ak_a1fb793ecce140d3851dbae3e5568dbf', '$2b$10$xw/QOVu4jVgYyBggTWBh.O708oUnJGUqtCE96Y3FF7UsKlk82rH6O', 'basic', 'active', '测试用户访问密钥，有调用次数限制', '["service:OCR_EXPRESS:*","service:ADDRESS_EXTRACT:*","service:GEO_REVERSE:*"]', 1);

-- 服务数据
INSERT INTO open_service (user_id, service_id, code, name, description, type, serviceStatus, pricingModel, currentVersion, features, endpoint, config, callCount, price, dailyLimit, minuteLimit, requireAuth, isAsync, timeout) 
VALUES (1, 0, 'OCR_EXPRESS', '物流面单OCR识别', '智能识别物流面单信息，提取收发件人、地址、电话等关键信息，支持多种面单格式，准确率高达95%以上。适用于物流企业、电商平台等自动化数据录入场景。', 'OCR', 'active', 'pay_per_use', 'v1.0.0', '高精度识别,多格式支持,实时处理,结构化输出,批量处理', '/op/ocr/upload', '{"supportedFormats":["jpg","jpeg","png","pdf","bmp","tiff"],"maxFileSize":"10MB","accuracy":0.95,"supportedCouriers":["顺丰","圆通","中通","申通","韵达","百世","邮政","京东","天天","德邦"],"outputFields":["sender_name","sender_phone","sender_address","receiver_name","receiver_phone","receiver_address","tracking_number","courier_name","weight","package_count"],"batchLimit":10,"preProcessing":{"autoRotate":true,"enhanceImage":true,"cropToContent":true},"returnOriginalImage":false,"accessMethods":[{"type":"file_upload","endpoint":"/op/ocr/upload","method":"POST","contentType":"multipart/form-data","maxFileSize":"5MB","supportedFormats":["jpg","jpeg","png","gif","bmp","webp"]},{"type":"base64","endpoint":"/op/ocr/recognize","method":"POST","contentType":"application/json","maxBase64Length":7000000}]}', 0, 0.05, 10000, 100, 1, 0, 30);
INSERT INTO open_service (user_id, service_id, code, name, description, type, serviceStatus, pricingModel, currentVersion, features, endpoint, config, callCount, price, dailyLimit, minuteLimit, requireAuth, isAsync, timeout) 
VALUES (1, 0, 'ADDRESS_EXTRACT', '物流文本精确提取省市区详细地址', '从文本中精确提取省市区详细地址信息，支持非结构化文本解析，可处理各种格式的地址信息，并进行标准化输出，支持省市区到街道社区的精确识别。', 'NLP', 'active', 'pay_per_use', 'v1.0.0', '智能分词,地址标准化,高准确率,支持模糊地址,批量处理,多维度纠错', '/op/address/extract', '{"supportedRegions":["全国省市区县"],"outputFormat":"standard","accuracy":0.98,"maxTextLength":2000,"batchLimit":50,"supportedOutputFields":{"province":true,"city":true,"district":true,"street":true,"community":true,"building":true,"room":true,"detail":true,"postalCode":true,"formatted":true},"addressNormalization":true,"fuzzyMatching":true,"confidenceScoreThreshold":0.7,"returnMultipleCandidates":true,"candidateLimit":3}', 0, 0.05, 20000, 200, 1, 0, 15);
INSERT INTO open_service (user_id, service_id, code, name, description, type, serviceStatus, pricingModel, currentVersion, features, endpoint, config, callCount, price, dailyLimit, minuteLimit, requireAuth, isAsync, timeout) 
VALUES (1, 0, 'GEO_REVERSE', '地理坐标逆解析出地址', '根据经纬度坐标逆向解析出详细地址信息，支持多种坐标系，可输出丰富的POI信息，精确到建筑物级别。适用于物流配送、位置服务、用户轨迹分析等场景。', 'DATA', 'active', 'pay_per_use', 'v1.0.0', '高精度定位,实时解析,多坐标系支持,POI识别,跨平台兼容,批量处理', '/op/address/rev-geo', '{"supportedCoordSystems":["WGS84","GCJ02","BD09"],"precision":"building","language":["zh-CN","en-US"],"radius":50,"batchLimit":100,"includePoiInfo":true,"maxPois":10,"poiTypes":["餐饮","住宿","购物","交通","教育","医疗","金融","休闲娱乐","旅游景点","商务办公"],"includeRoadInfo":true,"includeAddressComponents":true,"includeBusinessAreas":true,"formatOptions":{"formatted":true,"short":true,"long":true},"distanceCalculation":true}', 0, 0.05, 50000, 500, 1, 0, 10);

-- 用户服务关联
-- 管理员用户服务关联
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (1, 1, 999999999, 0, 0, NOW(), 0);
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (1, 2, 999999999, 0, 0, NOW(), 0);
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (1, 3, 999999999, 0, 0, NOW(), 0);
-- 测试用户服务关联
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (2, 1, 1000, 0, 0, NOW(), 0);
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (2, 2, 1000, 0, 0, NOW(), 0);
INSERT INTO user_service (userId, serviceId, total_count, used_count, free_used_today, last_reset_date, alert_sent) 
VALUES (2, 3, 1000, 0, 0, NOW(), 0);

SET FOREIGN_KEY_CHECKS=1;