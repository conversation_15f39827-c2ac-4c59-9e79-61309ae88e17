import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OcrEntity, OcrSourceType } from '../entities/ocr.entity';
import LogisticsParser from '../logisticsParser';
import * as fs from 'fs';
import * as path from 'path';
import FormData from 'form-data';

/**
 * OCR执行器服务
 * 负责执行具体的OCR业务操作，不包含权限验证等
 */
@Injectable()
export class OcrExecutorService {
  private readonly logger = new Logger(OcrExecutorService.name);
  private readonly ocrServiceUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectRepository(OcrEntity)
    private readonly ocrRepository: Repository<OcrEntity>,
    private readonly logisticsParser: LogisticsParser,
  ) {
    this.ocrServiceUrl = this.configService.get<string>('OCR_SERVICE_URL', 'http://127.0.0.1:8866/predict/ocr_system');
    this.logger.log(`OCR服务URL: ${this.ocrServiceUrl}`);
  }

  /**
   * 处理OCR请求（Base64格式）
   * @param imageData Base64格式的图片数据
   * @param filename 文件名（可选）
   * @param requestInfo 请求信息（可选）
   * @returns OCR处理结果
   */
  async processOcrFromBase64(
    imageData: string,
    filename?: string,
    requestInfo?: { userId?: number; apiKey?: string; serviceCode?: string }
  ): Promise<any> {
    let tempFilePath: string | null = null;
    const startTime = Date.now();
    
    try {
      this.logger.log(`开始处理OCR请求，文件名: ${filename || '未提供'}, 图片数据长度: ${imageData?.length || 0}`);
      
      // 验证Base64格式
      if (!this.isValidBase64Image(imageData)) {
        this.logger.error(`无效的Base64图片格式: ${imageData?.substring(0, 30)}...`);
        throw new Error('无效的Base64图片格式');
      }

      this.logger.debug(`图片类型: ${this.getImageType(imageData)}`);
      
      // 将Base64转换为临时文件
      tempFilePath = await this.saveBase64AsFile(imageData, filename);
      this.logger.debug(`已将Base64数据保存为临时文件: ${tempFilePath}`);
      
      // 使用FormData发送文件
      const formData = new FormData();
      formData.append('images', fs.createReadStream(tempFilePath));
      
      this.logger.debug(`调用OCR服务URL: ${this.ocrServiceUrl}`);
      this.logger.debug(`使用FormData字段: images, 文件路径: ${tempFilePath}`);
      
      // 调用OCR服务
      const response = await firstValueFrom(
        this.httpService.post(this.ocrServiceUrl, formData, {
          headers: {
            ...formData.getHeaders(),
            'Accept': 'application/json',
          },
          maxContentLength: Infinity, // 允许大文件
          maxBodyLength: Infinity,
          timeout: 60000, // 增加超时时间到60秒
        }),
      );

      this.logger.debug(`OCR服务响应状态: ${response.status}`);
      this.logger.debug(`OCR服务响应数据: ${JSON.stringify(response.data).substring(0, 200)}...`);

      const result = response.data;
      
      // 记录OCR结果
      if (requestInfo?.userId) {
        await this.saveOcrRecord({
          originalFilename: filename || 'base64-image',
          sourceType: OcrSourceType.BASE64,
          ocrResult: JSON.stringify(result),
          isSuccess: true,
          processingTime: Date.now() - startTime,
        });
      }

      // 解析物流单号（如果需要）
      if (result.words_result && result.words_result.length > 0) {
        const logisticsInfo = this.extractLogisticsInfo(result.words_result);
        if (logisticsInfo) {
          result.logistics_info = logisticsInfo;
        }
      } else if (result.results && result.results.length > 0) {
        // 兼容不同的返回格式
        const logisticsInfo = this.extractLogisticsInfo(result.results);
        if (logisticsInfo) {
          result.logistics_info = logisticsInfo;
        }
      }

      return {
        success: true,
        data: result,
        processingTime: Date.now() - startTime,
      };
    } catch (error) {
      this.logger.error(`OCR处理失败: ${error.message}`, error.stack);
      
      // 记录更多错误信息
      if (error.response) {
        this.logger.error(`OCR服务响应错误: 状态码=${error.response.status}, 数据=${JSON.stringify(error.response.data)}`);
      } else if (error.request) {
        this.logger.error(`OCR服务请求错误: ${error.request}`);
      }
      
      // 记录失败
      if (requestInfo?.userId) {
        await this.saveOcrRecord({
          originalFilename: filename || 'base64-image',
          sourceType: OcrSourceType.BASE64,
          ocrResult: JSON.stringify({ error: error.message }),
          isSuccess: false,
          errorMessage: error.message,
          processingTime: 0,
        });
      }
      
      return {
        success: false,
        error: error.message,
        processingTime: Date.now() - startTime,
      };
    } finally {
      // 清理临时文件
      if (tempFilePath) {
        try {
          if (fs.existsSync(tempFilePath)) {
            fs.unlinkSync(tempFilePath);
            this.logger.debug(`已删除临时文件: ${tempFilePath}`);
          }
        } catch (err) {
          this.logger.warn(`删除临时文件失败: ${err.message}`);
        }
      }
    }
  }
  
  /**
   * 将Base64字符串保存为临时文件
   * @param image_base64 Base64编码的图片数据
   * @param filename 可选的文件名
   * @returns 临时文件路径
   */
  private async saveBase64AsFile(image_base64: string, filename?: string): Promise<string> {
    // 移除可能存在的Base64前缀
    const base64Image = image_base64.replace(/^data:image\/(png|jpg|jpeg|gif|bmp|webp);base64,/, '');

    // 创建临时文件名
    const randomName = Array(32)
      .fill(null)
      .map(() => Math.round(Math.random() * 16).toString(16))
      .join('');

    // 确定文件扩展名
    let ext = '.png';
    if (filename) {
      const match = filename.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/);
      if (match) {
        ext = match[0];
      }
    }

    // 确保上传目录存在
    const uploadDir = './up-ocr';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // 保存文件路径
    const filePath = path.join(uploadDir, `${randomName}${ext}`);

    // 将Base64数据写入文件
    fs.writeFileSync(filePath, Buffer.from(base64Image, 'base64'));

    return filePath;
  }

  /**
   * 保存OCR记录
   */
  private async saveOcrRecord(data: Partial<OcrEntity>): Promise<void> {
    try {
      const record = this.ocrRepository.create(data);
      await this.ocrRepository.save(record);
    } catch (error) {
      this.logger.error(`保存OCR记录失败: ${error.message}`);
    }
  }

  /**
   * 验证Base64图片格式
   */
  private isValidBase64Image(data: string): boolean {
    if (!data) {
      this.logger.error('Base64数据为空');
      return false;
    }
    
    // 检查是否为有效的Base64图片格式
    const isValid = data.startsWith('data:image/') || 
           data.startsWith('data:application/octet-stream') || 
           data.startsWith('data:application/pdf');
            
    if (!isValid) {
      this.logger.warn(`无效的Base64格式，前缀: ${data.substring(0, 30)}`);
    }
    
    return isValid;
  }

  /**
   * 获取图片类型
   */
  private getImageType(data: string): string {
    if (data.startsWith('data:image/jpeg')) return 'jpeg';
    if (data.startsWith('data:image/png')) return 'png';
    if (data.startsWith('data:image/gif')) return 'gif';
    if (data.startsWith('data:application/pdf')) return 'pdf';
    return 'unknown';
  }

  /**
   * 从OCR结果中提取物流信息
   */
  private extractLogisticsInfo(wordsResult: any[]): any {
    try {
      // 提取文本内容
      const texts = wordsResult.map(item => {
        if (typeof item === 'string') return item;
        if (item.words) return item.words;
        if (item[1] && typeof item[1] === 'string') return item[1];
        return '';
      }).filter(text => text.trim().length > 0);
      
      this.logger.debug(`提取的文本内容: ${JSON.stringify(texts)}`);
      
      // 使用LogisticsParser解析物流信息
      const result = this.logisticsParser.extract(texts);
      this.logger.debug(`解析的物流信息: ${JSON.stringify(result)}`);
      
      return result;
    } catch (error) {
      this.logger.error(`提取物流信息失败: ${error.message}`);
      return null;
    }
  }
} 