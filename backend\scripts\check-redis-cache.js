const Redis = require('ioredis');

/**
 * 检查Redis缓存内容
 */
async function checkRedisCache() {
  console.log('检查Redis缓存内容...');
  
  const redis = new Redis({
    host: 'localhost',
    port: 6379,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
  });

  try {
    // 检查API密钥缓存
    const apiKeyPattern = 'api_key:*';
    const apiKeys = await redis.keys(apiKeyPattern);
    console.log(`\n找到 ${apiKeys.length} 个API密钥缓存:`);
    
    for (const key of apiKeys) {
      const value = await redis.get(key);
      console.log(`  ${key}: ${value ? value.substring(0, 100) + '...' : 'null'}`);
    }

    // 检查Secret Key缓存
    const secretKeyPattern = 'api_key_secret:*';
    const secretKeys = await redis.keys(secretKeyPattern);
    console.log(`\n找到 ${secretKeys.length} 个Secret Key缓存:`);
    
    for (const key of secretKeys) {
      const value = await redis.get(key);
      console.log(`  ${key}: ${value ? value.substring(0, 20) + '...' : 'null'}`);
    }

    // 检查特定的API密钥
    const testApiKey = 'ak-579b27759508f152525a4e5a567efd5a';
    const testApiKeyCache = await redis.get(`api_key:${testApiKey}`);
    console.log(`\n测试API密钥缓存:`);
    console.log(`  api_key:${testApiKey}: ${testApiKeyCache ? 'EXISTS' : 'NOT FOUND'}`);
    
    if (testApiKeyCache) {
      try {
        const parsed = JSON.parse(testApiKeyCache);
        console.log(`  解析成功: ID=${parsed.id}, userId=${parsed.userId}`);
      } catch (error) {
        console.log(`  解析失败: ${error.message}`);
      }
    }

    // 检查对应的Secret Key
    const testSecretKeyCache = await redis.get(`api_key_secret:4`);
    console.log(`\n测试Secret Key缓存:`);
    console.log(`  api_key_secret:4: ${testSecretKeyCache ? testSecretKeyCache.substring(0, 20) + '...' : 'NOT FOUND'}`);
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    redis.disconnect();
    console.log('\nRedis连接已关闭');
  }
}

// 运行脚本
checkRedisCache().catch(console.error);
