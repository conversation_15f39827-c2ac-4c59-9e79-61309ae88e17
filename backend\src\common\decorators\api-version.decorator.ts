import { SetMetadata, applyDecorators } from '@nestjs/common';
import { ApiHeader, ApiOperation } from '@nestjs/swagger';

/**
 * API版本元数据键
 */
export const API_VERSION_KEY = 'api_version';

/**
 * API弃用元数据键
 */
export const API_DEPRECATED_KEY = 'api_deprecated';

/**
 * API版本装饰器
 * @param version API版本号
 */
export const ApiVersion = (version: string) => SetMetadata(API_VERSION_KEY, version);

/**
 * API弃用装饰器
 * @param deprecatedSince 弃用版本
 * @param removeIn 移除版本
 * @param alternative 替代方案
 */
export const ApiDeprecated = (
  deprecatedSince: string,
  removeIn?: string,
  alternative?: string,
) => {
  const deprecationInfo = {
    deprecatedSince,
    removeIn,
    alternative,
  };

  return applyDecorators(
    SetMetadata(API_DEPRECATED_KEY, deprecationInfo),
    ApiHeader({
      name: 'X-API-Deprecated',
      description: `API已弃用，自版本 ${deprecatedSince}${removeIn ? `，将在版本 ${removeIn} 中移除` : ''}${alternative ? `，请使用 ${alternative}` : ''}`,
      required: false,
    }),
    ApiOperation({
      deprecated: true,
      description: `⚠️ 此API已弃用，自版本 ${deprecatedSince}${removeIn ? `，将在版本 ${removeIn} 中移除` : ''}${alternative ? `，请使用 ${alternative}` : ''}`,
    }),
  );
};

/**
 * API版本兼容性装饰器
 * @param supportedVersions 支持的版本列表
 */
export const ApiVersionCompatibility = (supportedVersions: string[]) =>
  SetMetadata('supported_versions', supportedVersions);

/**
 * 实验性API装饰器
 */
export const ApiExperimental = () =>
  applyDecorators(
    SetMetadata('experimental', true),
    ApiHeader({
      name: 'X-API-Experimental',
      description: '此API为实验性功能，可能在未来版本中发生变化',
      required: false,
    }),
    ApiOperation({
      description: '🧪 此API为实验性功能，可能在未来版本中发生变化，请谨慎在生产环境中使用',
    }),
  );

/**
 * Beta API装饰器
 */
export const ApiBeta = () =>
  applyDecorators(
    SetMetadata('beta', true),
    ApiHeader({
      name: 'X-API-Beta',
      description: '此API为Beta版本，功能相对稳定但可能存在小幅调整',
      required: false,
    }),
    ApiOperation({
      description: '🚧 此API为Beta版本，功能相对稳定但可能存在小幅调整',
    }),
  );
