import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '@/shared/redis.service';
import { TaskExecutionStatus } from '../entities/task-execution.entity';

/**
 * 任务执行缓存服务
 * 使用Redis存储任务执行记录，避免数据库积累大量非业务数据
 */
@Injectable()
export class TaskExecutionCacheService {
  private readonly logger = new Logger(TaskExecutionCacheService.name);
  
  // 缓存键前缀
  private readonly TASK_EXECUTION_PREFIX = 'task_execution:';
  private readonly TASK_STATS_PREFIX = 'task_stats:';
  private readonly TASK_RECENT_PREFIX = 'task_recent:';
  
  // 缓存过期时间
  private readonly EXECUTION_TTL = 7 * 24 * 60 * 60; // 7天
  private readonly STATS_TTL = 30 * 24 * 60 * 60; // 30天
  private readonly RECENT_TTL = 24 * 60 * 60; // 1天

  constructor(private readonly redisService: RedisService) {}

  /**
   * 创建任务执行记录
   */
  async createExecution(
    taskName: string,
    taskType: string,
    parameters?: any,
  ): Promise<string> {
    const executionId = `${taskName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const execution = {
      id: executionId,
      taskName,
      taskType,
      status: TaskExecutionStatus.PENDING,
      parameters,
      createdAt: new Date().toISOString(),
    };

    await this.redisService.setex(
      `${this.TASK_EXECUTION_PREFIX}${executionId}`,
      this.EXECUTION_TTL,
      JSON.stringify(execution)
    );

    return executionId;
  }

  /**
   * 开始执行任务
   */
  async startExecution(executionId: string): Promise<void> {
    const key = `${this.TASK_EXECUTION_PREFIX}${executionId}`;
    const executionData = await this.redisService.get(key);
    
    if (executionData) {
      const execution = JSON.parse(executionData);
      execution.status = TaskExecutionStatus.RUNNING;
      execution.startTime = new Date().toISOString();
      
      await this.redisService.setex(key, this.EXECUTION_TTL, JSON.stringify(execution));
    }
  }

  /**
   * 完成任务执行
   */
  async completeExecution(
    executionId: string,
    success: boolean,
    result?: any,
    errorMessage?: string,
  ): Promise<void> {
    const key = `${this.TASK_EXECUTION_PREFIX}${executionId}`;
    const executionData = await this.redisService.get(key);
    
    if (executionData) {
      const execution = JSON.parse(executionData);
      const endTime = new Date();
      
      execution.status = success ? TaskExecutionStatus.SUCCESS : TaskExecutionStatus.FAILED;
      execution.endTime = endTime.toISOString();
      execution.result = result ? JSON.stringify(result) : null;
      execution.errorMessage = errorMessage || null;
      
      // 计算执行耗时
      if (execution.startTime) {
        execution.duration = endTime.getTime() - new Date(execution.startTime).getTime();
      }

      await this.redisService.setex(key, this.EXECUTION_TTL, JSON.stringify(execution));
      
      // 更新统计信息
      await this.updateTaskStats(execution.taskName, success, execution.duration);
      
      // 添加到最近执行记录
      await this.addToRecentExecutions(execution);
    }
  }

  /**
   * 更新任务统计信息
   */
  private async updateTaskStats(taskName: string, success: boolean, duration?: number): Promise<void> {
    const statsKey = `${this.TASK_STATS_PREFIX}${taskName}`;
    const statsData = await this.redisService.get(statsKey);
    
    let stats = statsData ? JSON.parse(statsData) : {
      taskName,
      totalExecutions: 0,
      successCount: 0,
      failureCount: 0,
      totalDuration: 0,
      lastExecutionTime: null,
    };

    stats.totalExecutions++;
    if (success) {
      stats.successCount++;
    } else {
      stats.failureCount++;
    }
    
    if (duration) {
      stats.totalDuration += duration;
    }
    
    stats.lastExecutionTime = new Date().toISOString();
    stats.averageDuration = stats.totalExecutions > 0 ? Math.round(stats.totalDuration / stats.totalExecutions) : 0;
    stats.successRate = stats.totalExecutions > 0 ? (stats.successCount / stats.totalExecutions) * 100 : 0;

    await this.redisService.setex(statsKey, this.STATS_TTL, JSON.stringify(stats));
  }

  /**
   * 添加到最近执行记录
   */
  private async addToRecentExecutions(execution: any): Promise<void> {
    const recentKey = this.TASK_RECENT_PREFIX + 'all';
    
    // 获取现有的最近执行记录
    const recentData = await this.redisService.get(recentKey);
    let recentExecutions = recentData ? JSON.parse(recentData) : [];
    
    // 添加新记录到开头
    recentExecutions.unshift({
      taskName: execution.taskName,
      status: execution.status,
      startTime: execution.startTime,
      duration: execution.duration,
      errorMessage: execution.errorMessage,
    });
    
    // 只保留最近20条记录
    recentExecutions = recentExecutions.slice(0, 20);
    
    await this.redisService.setex(recentKey, this.RECENT_TTL, JSON.stringify(recentExecutions));
  }

  /**
   * 获取任务统计信息
   */
  async getTaskStats(taskName?: string): Promise<any[]> {
    const pattern = taskName ? `${this.TASK_STATS_PREFIX}${taskName}` : `${this.TASK_STATS_PREFIX}*`;
    const keys = await this.redisService.keys(pattern);
    
    const stats: any[] = [];
    for (const key of keys) {
      const data = await this.redisService.get(key);
      if (data) {
        stats.push(JSON.parse(data));
      }
    }
    
    return stats;
  }

  /**
   * 获取今日统计
   */
  async getTodayStats(): Promise<{ totalExecutions: number; successCount: number; failureCount: number }> {
    // 简化实现：从所有任务统计中汇总
    const allStats = await this.getTaskStats();
    
    return {
      totalExecutions: allStats.reduce((sum, stat) => sum + stat.totalExecutions, 0),
      successCount: allStats.reduce((sum, stat) => sum + stat.successCount, 0),
      failureCount: allStats.reduce((sum, stat) => sum + stat.failureCount, 0),
    };
  }

  /**
   * 获取最近执行记录
   */
  async getRecentExecutions(limit: number = 10): Promise<any[]> {
    const recentKey = this.TASK_RECENT_PREFIX + 'all';
    const data = await this.redisService.get(recentKey);
    
    if (data) {
      const executions = JSON.parse(data);
      return executions.slice(0, limit);
    }
    
    return [];
  }

  /**
   * 清理过期数据（手动清理，Redis TTL会自动处理）
   */
  async cleanupExpiredData(): Promise<number> {
    // Redis的TTL会自动清理过期数据，这里只是记录日志
    this.logger.log('Redis TTL会自动清理过期的任务执行记录');
    return 0;
  }
}
