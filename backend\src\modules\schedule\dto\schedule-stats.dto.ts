import { ApiProperty } from '@nestjs/swagger';
import { TaskExecutionStatus } from '../entities/task-execution.entity';

/**
 * 任务执行统计DTO
 */
export class TaskExecutionStatsDto {
  @ApiProperty({ description: '任务名称' })
  taskName: string;

  @ApiProperty({ description: '总执行次数' })
  totalExecutions: number;

  @ApiProperty({ description: '成功次数' })
  successCount: number;

  @ApiProperty({ description: '失败次数' })
  failureCount: number;

  @ApiProperty({ description: '成功率' })
  successRate: number;

  @ApiProperty({ description: '平均执行时间(毫秒)' })
  averageDuration: number;

  @ApiProperty({ description: '最后执行时间' })
  lastExecutionTime: Date;

  @ApiProperty({ description: '最后执行状态', enum: TaskExecutionStatus })
  lastExecutionStatus: TaskExecutionStatus;

  @ApiProperty({ description: '下次执行时间' })
  nextExecutionTime?: Date;
}

/**
 * 定时任务统计信息DTO
 */
export class ScheduleStatsDto {
  @ApiProperty({ description: '系统启动时间' })
  systemStartTime: Date;

  @ApiProperty({ description: '总任务数' })
  totalTasks: number;

  @ApiProperty({ description: '活跃任务数' })
  activeTasks: number;

  @ApiProperty({ description: '今日执行次数' })
  todayExecutions: number;

  @ApiProperty({ description: '今日成功次数' })
  todaySuccessCount: number;

  @ApiProperty({ description: '今日失败次数' })
  todayFailureCount: number;

  @ApiProperty({ description: '各任务执行统计', type: [TaskExecutionStatsDto] })
  taskStats: TaskExecutionStatsDto[];

  @ApiProperty({ description: '最近执行记录' })
  recentExecutions: {
    taskName: string;
    status: TaskExecutionStatus;
    startTime: Date;
    duration: number;
    errorMessage?: string;
  }[];
}

/**
 * 任务状态DTO
 */
export class TaskStatusDto {
  @ApiProperty({ description: '任务名称' })
  taskName: string;

  @ApiProperty({ description: '任务类型' })
  taskType: string;

  @ApiProperty({ description: '是否启用' })
  enabled: boolean;

  @ApiProperty({ description: 'Cron表达式' })
  cronExpression: string;

  @ApiProperty({ description: '下次执行时间' })
  nextExecutionTime: Date;

  @ApiProperty({ description: '最后执行时间' })
  lastExecutionTime?: Date;

  @ApiProperty({ description: '最后执行状态', enum: TaskExecutionStatus })
  lastExecutionStatus?: TaskExecutionStatus;

  @ApiProperty({ description: '是否正在执行' })
  isRunning: boolean;
}

/**
 * 手动执行任务结果DTO
 */
export class ManualExecutionResultDto {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '消息' })
  message: string;

  @ApiProperty({ description: '任务执行ID' })
  executionId?: number;

  @ApiProperty({ description: '开始时间' })
  startTime: Date;

  @ApiProperty({ description: '结束时间' })
  endTime?: Date;

  @ApiProperty({ description: '执行耗时(毫秒)' })
  duration?: number;

  @ApiProperty({ description: '错误信息' })
  error?: string;
}

/**
 * 任务执行历史查询DTO
 */
export class TaskExecutionQueryDto {
  @ApiProperty({ description: '任务名称', required: false })
  taskName?: string;

  @ApiProperty({ description: '任务类型', required: false })
  taskType?: string;

  @ApiProperty({ description: '执行状态', enum: TaskExecutionStatus, required: false })
  status?: TaskExecutionStatus;

  @ApiProperty({ description: '开始日期', required: false })
  startDate?: Date;

  @ApiProperty({ description: '结束日期', required: false })
  endDate?: Date;

  @ApiProperty({ description: '页码', default: 1 })
  page: number = 1;

  @ApiProperty({ description: '每页数量', default: 20 })
  limit: number = 20;
}

/**
 * 任务执行历史分页结果DTO
 */
export class TaskExecutionPageDto {
  @ApiProperty({ description: '执行记录列表' })
  items: any[];

  @ApiProperty({ description: '总数量' })
  total: number;

  @ApiProperty({ description: '当前页码' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  limit: number;

  @ApiProperty({ description: '总页数' })
  totalPages: number;
}
