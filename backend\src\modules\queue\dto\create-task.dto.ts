import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsObject, IsEnum, IsInt, Min, IsBase64, ValidateIf, IsNumber, IsLatitude, IsLongitude } from 'class-validator';

/**
 * OCR任务创建DTO
 */
export class CreateOcrTaskDto {
  @ApiProperty({
    description: 'Base64编码的图片数据',
    example: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEA...',
  })
  @IsNotEmpty({ message: '图片数据不能为空' })
  @IsString({ message: '图片数据必须是字符串' })
  imageData: string;

  @ApiPropertyOptional({
    description: '任务选项',
    example: { type: 'logistics', language: 'zh' },
  })
  @IsOptional()
  @IsObject({ message: '选项必须是一个对象' })
  options?: Record<string, any>;
}

/**
 * 地址解析任务创建DTO
 */
export class CreateAddressTaskDto {
  @ApiProperty({
    description: '待解析的地址文本',
    example: '北京市海淀区中关村南大街5号',
  })
  @IsNotEmpty({ message: '地址文本不能为空' })
  @IsString({ message: '地址文本必须是字符串' })
  addressText: string;

  @ApiPropertyOptional({
    description: '任务选项',
    example: { format: true, extractComponents: true },
  })
  @IsOptional()
  @IsObject({ message: '选项必须是一个对象' })
  options?: Record<string, any>;
}

/**
 * 地理坐标处理任务创建DTO - 地址转坐标
 */
export class CreateGeocodeTaskDto {
  @ApiProperty({
    description: '需要转换为坐标的地址文本',
    example: '北京市海淀区中关村南大街5号',
  })
  @IsNotEmpty({ message: '地址文本不能为空' })
  @IsString({ message: '地址文本必须是字符串' })
  address: string;

  @ApiPropertyOptional({
    description: '任务选项',
    example: { coordType: 'wgs84' },
  })
  @IsOptional()
  @IsObject({ message: '选项必须是一个对象' })
  options?: Record<string, any>;
}

/**
 * 地理坐标处理任务创建DTO - 坐标转地址
 */
export class CreateReverseGeocodeTaskDto {
  @ApiProperty({
    description: '纬度',
    example: 39.908823,
  })
  @IsNotEmpty({ message: '纬度不能为空' })
  @IsNumber({}, { message: '纬度必须是数字' })
  @IsLatitude({ message: '纬度必须在-90到90之间' })
  latitude: number;

  @ApiProperty({
    description: '经度',
    example: 116.397470,
  })
  @IsNotEmpty({ message: '经度不能为空' })
  @IsNumber({}, { message: '经度必须是数字' })
  @IsLongitude({ message: '经度必须在-180到180之间' })
  longitude: number;

  @ApiPropertyOptional({
    description: '任务选项',
    example: { coordType: 'wgs84' },
  })
  @IsOptional()
  @IsObject({ message: '选项必须是一个对象' })
  options?: Record<string, any>;
} 