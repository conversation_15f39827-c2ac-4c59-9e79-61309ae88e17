import { Injectable, Logger, Optional, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '../../shared/redis.service';
import LogisticsParser from '../ocr/logisticsParser';

/**
 * 网关健康状态接口
 */
interface GatewayHealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  uptime: number;
  version: string;
  services: {
    proxy: boolean;
    redis: boolean;
  };
  statistics: {
    totalRequests: number;
    activeConnections: number;
    averageResponseTime: number;
  };
}

/**
 * 网关统计信息接口
 */
interface GatewayStatistics {
  totalRequests: number;
  totalServices: number;
  averageResponseTime: number;
  successRate: number;
  cacheHitRate: number;
  requestsPerMinute: number;
  topServices: Array<{
    serviceCode: string;
    requestCount: number;
    averageResponseTime: number;
  }>;
  recentErrors: Array<{
    timestamp: number;
    serviceCode: string;
    errorMessage: string;
  }>;
}

/**
 * 服务统计信息接口
 */
interface ServiceStatistics {
  totalRequests: number;
  successRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  cacheHitRate: number;
  lastRequestTime: number;
}

/**
 * 请求指标接口
 */
interface RequestMetrics {
  requestId: string;
  serviceCode: string;
  method: string;
  path: string;
  startTime: number;
  endTime: number;
  responseTime: number;
  status: number;
  success: boolean;
  cached: boolean;
  processingMode: 'sync' | 'async';
}

/**
 * 网关服务
 * 提供网关的核心功能和管理功能
 */
@Injectable()
export class GatewayService {
  private readonly logger = new Logger(GatewayService.name);
  private readonly startTime: number;
  private readonly metricsPrefix = 'gateway:metrics';
  private readonly statsPrefix = 'gateway:stats';
  private readonly errorPrefix = 'gateway:errors';

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    @Inject(LogisticsParser) private readonly logisticsParser: LogisticsParser,
  ) {
    this.startTime = Date.now();
    this.logger.log('网关服务已启动');
  }

  /**
   * 获取网关健康状态
   */
  async getHealthStatus(): Promise<GatewayHealthStatus> {
    const uptime = Math.floor((Date.now() - this.startTime) / 1000);
    const version = this.configService.get('APP_VERSION', '1.0.0');
    
    // 检查Redis连接
    const redisHealth = await this.checkRedisHealth();
    
    // 获取统计数据
    const totalRequests = await this.getCounter(`${this.statsPrefix}:global:requests:total`) || 0;
    const activeConnections = await this.getCounter(`${this.statsPrefix}:global:active_connections`) || 0;
    const totalResponseTime = await this.getCounter(`${this.statsPrefix}:global:response_time:total`) || 0;
    const averageResponseTime = totalRequests > 0 ? totalResponseTime / totalRequests : 0;
    
    // 确定整体状态
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    
    if (!redisHealth) {
      status = 'unhealthy';
    }
    
    return {
      status,
      uptime,
      version,
      services: {
        proxy: true, // 如果服务在运行，则代理服务可用
        redis: redisHealth,
      },
      statistics: {
        totalRequests,
        activeConnections,
        averageResponseTime: Math.round(averageResponseTime),
      },
    };
  }

  /**
   * 获取网关统计信息
   */
  async getStatistics(): Promise<GatewayStatistics> {
    try {
      const [totalRequests, services, recentErrors] = await Promise.all([
        this.getCounter(`${this.statsPrefix}:global:requests:total`),
        this.getActiveServices(),
        this.getRecentErrors(10),
      ]);

      const successRequests = await this.getCounter(`${this.statsPrefix}:global:requests:success`);
      const totalResponseTime = await this.getCounter(`${this.statsPrefix}:global:response_time:total`);
      const cacheHits = await this.getCounter(`${this.statsPrefix}:global:cache:hits`);
      
      const successRate = totalRequests > 0 ? (successRequests / totalRequests) * 100 : 0;
      const averageResponseTime = totalRequests > 0 ? totalResponseTime / totalRequests : 0;
      const cacheHitRate = totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0;
      
      // 获取每分钟请求数（最近1分钟）
      const requestsPerMinute = await this.getRequestsPerMinute();
      
      // 获取热门服务
      const topServices = await this.getTopServices(5);

      return {
        totalRequests,
        totalServices: services.length,
        averageResponseTime: Math.round(averageResponseTime),
        successRate: Math.round(successRate * 100) / 100,
        cacheHitRate: Math.round(cacheHitRate * 100) / 100,
        requestsPerMinute,
        topServices,
        recentErrors,
      };
    } catch (error) {
      this.logger.error('获取网关统计失败', error);
      throw error;
    }
  }

  /**
   * 获取服务统计信息
   */
  async getServiceStatistics(serviceCode: string): Promise<ServiceStatistics> {
    try {
      const totalRequests = await this.getCounter(`${this.statsPrefix}:${serviceCode}:requests:total`);
      const successRequests = await this.getCounter(`${this.statsPrefix}:${serviceCode}:requests:success`);
      const totalResponseTime = await this.getCounter(`${this.statsPrefix}:${serviceCode}:response_time:total`);
      const cacheHits = await this.getCounter(`${this.statsPrefix}:${serviceCode}:cache:hits`);
      const lastRequestTime = await this.getCounter(`${this.statsPrefix}:${serviceCode}:last_request`);

      const failedRequests = totalRequests - successRequests;
      const averageResponseTime = totalRequests > 0 ? totalResponseTime / totalRequests : 0;
      const cacheHitRate = totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0;

      return {
        totalRequests,
        successRequests,
        failedRequests,
        averageResponseTime: Math.round(averageResponseTime),
        cacheHitRate: Math.round(cacheHitRate * 100) / 100,
        lastRequestTime,
      };
    } catch (error) {
      this.logger.error(`获取服务 ${serviceCode} 统计失败`, error);
      throw error;
    }
  }

  /**
   * 获取请求指标
   */
  async getRequestMetrics(requestId: string): Promise<RequestMetrics | null> {
    try {
      const key = `${this.metricsPrefix}:request:${requestId}`;
      const data = await this.redisService.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      this.logger.error(`获取请求 ${requestId} 指标失败`, error);
      return null;
    }
  }

  /**
   * 检查Redis连接健康状态
   */
  private async checkRedisHealth(): Promise<boolean> {
    try {
      await this.redisService.ping();
      return true;
    } catch (error) {
      this.logger.warn('Redis健康检查失败', error);
      return false;
    }
  }

  /**
   * 获取计数器值
   */
  private async getCounter(key: string): Promise<number> {
    try {
      const value = await this.redisService.get(key);
      return value ? Number(value) : 0;
    } catch (error) {
      this.logger.error(`获取计数器 ${key} 失败`, error);
      return 0;
    }
  }

  /**
   * 获取活跃服务列表
   */
  private async getActiveServices(): Promise<string[]> {
    try {
      const pattern = `${this.statsPrefix}:*:requests:total`;
      const keys = await this.redisService.keys(pattern);
      return keys.map(key => {
        const parts = key.split(':');
        return parts[2]; // 提取服务代码
      }).filter(service => service !== 'global');
    } catch (error) {
      this.logger.error('获取活跃服务失败', error);
      return [];
    }
  }

  /**
   * 获取每分钟请求数
   */
  private async getRequestsPerMinute(): Promise<number> {
    try {
      const now = Date.now();
      const oneMinuteAgo = now - 60 * 1000;
      
      const recentRequests = await this.redisService.zcount(
        `${this.statsPrefix}:requests:timeline`,
        oneMinuteAgo,
        now
      );
      
      return recentRequests;
    } catch (error) {
      this.logger.error('获取每分钟请求数失败', error);
      return 0;
    }
  }

  /**
   * 获取热门服务
   */
  private async getTopServices(limit: number): Promise<Array<any>> {
    try {
      const services = await this.getActiveServices();
      const serviceStats = await Promise.all(
        services.map(async (service) => {
          const requestCount = await this.getCounter(`${this.statsPrefix}:${service}:requests:total`);
          const totalResponseTime = await this.getCounter(`${this.statsPrefix}:${service}:response_time:total`);
          const averageResponseTime = requestCount > 0 ? totalResponseTime / requestCount : 0;
          
          return {
            serviceCode: service,
            requestCount,
            averageResponseTime: Math.round(averageResponseTime),
          };
        })
      );
      
      return serviceStats
        .sort((a, b) => b.requestCount - a.requestCount)
        .slice(0, limit);
    } catch (error) {
      this.logger.error('获取热门服务失败', error);
      return [];
    }
  }

  /**
   * 获取最近错误
   */
  private async getRecentErrors(limit: number): Promise<Array<{
    timestamp: number;
    serviceCode: string;
    errorMessage: string;
  }>> {
    try {
      const errors = await this.redisService.zrevrange(
        `${this.errorPrefix}:recent`,
        0,
        limit - 1,
        'WITHSCORES'
      );
      
      const result: Array<{
        timestamp: number;
        serviceCode: string;
        errorMessage: string;
      }> = [];
      
      for (let i = 0; i < errors.length; i += 2) {
        const [errorData, timestamp] = [errors[i], errors[i + 1]];
        try {
          const error = JSON.parse(errorData);
          
          result.push({
            timestamp: Number(timestamp),
            serviceCode: error.serviceCode || 'unknown',
            errorMessage: error.message || 'Unknown error',
          });
        } catch (e) {
          // 忽略无法解析的错误数据
        }
      }
      
      return result;
    } catch (error) {
      this.logger.error('获取最近错误失败', error);
      return [];
    }
  }

  /**
   * 本地解析OCR结果
   * @param ocrResults OCR识别结果
   * @returns 解析后的物流数据
   */
  async localParseOcrResult(ocrResults: any[]): Promise<any> {
    try {
      this.logger.debug(`开始本地解析OCR结果, 共${ocrResults?.length || 0}条记录`);
      
      if (!this.logisticsParser) {
        this.logger.warn('未找到LogisticsParser实例，无法解析物流单');
        return {
          success: true,
          data: {
            logisticsCompany: '未知',
            bestCode: '',
            trackingNumber: '',
            mailNetwork: '',
            sender: { name: '', phone: '', address: '' },
            receiver: { name: '', phone: '', address: '' }
          }
        };
      }
      
      // 1. 数据清洗：过滤有效文本（置信度>0.5且为字符串）
      const validTexts = ocrResults?.filter((item) => 
        item[1] && 
        typeof item[1] === 'string' && 
        item[2] > 0.5 && 
        (item[1].length >= 3 || /^[A-Z0-9-]{4,}$/i.test(item[1]))
      ).map((item) => item[1]) || [];
      
      this.logger.debug(`过滤后有效文本数量: ${validTexts.length}`);
      
      if (validTexts.length === 0) {
        this.logger.warn('OCR结果中没有有效文本，无法进行物流单解析');
        return {
          success: true,
          data: {
            logisticsCompany: '未知',
            bestCode: '',
            trackingNumber: '',
            mailNetwork: '',
            sender: { name: '', phone: '', address: '' },
            receiver: { name: '', phone: '', address: '' }
          }
        };
      }
      
      // 2. 使用LogisticsParser解析数据
      const parsedResult = this.logisticsParser.extract(validTexts);
      this.logger.debug(`物流解析结果: ${JSON.stringify(parsedResult)}`);
      
      // 3. 构建结构化数据
      const formattedResult: any = {
        logisticsCompany: parsedResult?.company || '未知',
        bestCode: parsedResult?.bestCode || '',
        trackingNumber: parsedResult?.trackingNumber || '',
        mailNetwork: parsedResult?.mailNetwork || '',
        sender: this.formatContact(parsedResult?.sender),
        receiver: this.formatContact(parsedResult?.receiver)
      };
      
      return {
        success: true,
        data: formattedResult
      };
    } catch (error) {
      this.logger.error(`解析物流数据失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: `解析物流数据失败: ${error.message}`,
        data: {
          logisticsCompany: '未知',
          bestCode: '',
          trackingNumber: '',
          mailNetwork: '',
          sender: { name: '', phone: '', address: '' },
          receiver: { name: '', phone: '', address: '' }
        }
      };
    }
  }

  /**
   * 格式化联系人信息
   * @param contact 原始联系人数据
   * @returns 格式化后的联系人数据
   */
  private formatContact(contact: any): any {
    if (!contact) {
      return { name: '', phone: '', address: '' };
    }
    
    return {
      name: contact.name || '',
      phone: contact.phones && contact.phones.length > 0 ? contact.phones[0] : '',
      address: contact.address || ''
    };
  }
}
