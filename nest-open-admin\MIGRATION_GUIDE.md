# 队列服务迁移指南

## 概述

本指南说明如何从独立的 `AlertQueueService` 和 `ApiCallQueueService` 迁移到统一的 `UnifiedQueueService`。

## 迁移步骤

### 1. AlertQueueService 迁移

#### 原有用法：
```typescript
// 旧方式
@Injectable()
export class SomeService {
  constructor(
    private readonly alertQueueService: AlertQueueService,
  ) {}

  async sendAlert() {
    await this.alertQueueService.handleAlertProcess({
      messageId: 'alert_123',
      userId: 1,
      serviceId: 2,
      alertType: 'QUOTA_WARNING',
      threshold: 100,
      currentValue: 80,
      message: '额度预警',
    });
  }
}
```

#### 新用法：
```typescript
// 新方式
@Injectable()
export class SomeService {
  constructor(
    private readonly unifiedQueueService: UnifiedQueueService,
  ) {}

  async sendAlert() {
    await this.unifiedQueueService.processAlert({
      id: 'alert_123',
      userId: 1,
      serviceId: 2,
      alertType: 'QUOTA_WARNING',
      threshold: 100,
      currentValue: 80,
      message: '额度预警',
      priority: 'HIGH',
    });
  }
}
```

### 2. ApiCallQueueService 迁移

#### 原有用法：
```typescript
// 旧方式
@Injectable()
export class SomeService {
  constructor(
    private readonly apiCallQueueService: ApiCallQueueService,
  ) {}

  async processApiCall() {
    await this.apiCallQueueService.handleApiCallProcess({
      messageId: 'api_call_123',
      userId: 1,
      serviceId: 2,
      apiKeyId: 3,
      method: 'POST',
      url: 'https://api.example.com/v1/data',
      headers: { 'Content-Type': 'application/json' },
      body: { data: 'test' },
    });
  }
}
```

#### 新用法：
```typescript
// 新方式
@Injectable()
export class SomeService {
  constructor(
    private readonly unifiedQueueService: UnifiedQueueService,
  ) {}

  async processApiCall() {
    await this.unifiedQueueService.processApiCall({
      id: 'api_call_123',
      userId: 1,
      serviceId: 2,
      apiKeyId: 3,
      method: 'POST',
      url: 'https://api.example.com/v1/data',
      headers: { 'Content-Type': 'application/json' },
      body: { data: 'test' },
      priority: 5,
    });
  }
}
```

### 3. 模块依赖更新

#### 更新模块导入：
```typescript
// 旧方式
@Module({
  imports: [
    // 其他导入
  ],
  providers: [
    AlertQueueService,
    ApiCallQueueService,
    // 其他服务
  ],
})
export class SomeModule {}

// 新方式
@Module({
  imports: [
    // 其他导入
    // UnifiedQueueService 通过 SharedModule 或直接导入
  ],
  providers: [
    // 移除 AlertQueueService 和 ApiCallQueueService
    // 其他服务
  ],
})
export class SomeModule {}
```

## 主要变化

### 1. 接口变化

- **AlertData**: 新增 `priority` 字段，移除 `messageId`，改为 `id`
- **ApiCallData**: 新增 `priority` 字段，移除 `messageId`，改为 `id`

### 2. 方法名变化

- `AlertQueueService.handleAlertProcess()` → `UnifiedQueueService.processAlert()`
- `ApiCallQueueService.handleApiCallProcess()` → `UnifiedQueueService.processApiCall()`

### 3. 事件系统

新的统一队列服务提供了更好的事件系统：

```typescript
// 监听处理完成事件
this.eventEmitter.on('alert.processed', (alert) => {
  console.log('预警处理完成:', alert.id);
});

this.eventEmitter.on('api.call.processed', (apiCall) => {
  console.log('API调用处理完成:', apiCall.id);
});
```

## 优势

1. **统一管理**: 所有队列操作通过一个服务管理
2. **更好的监控**: 统一的状态监控和统计
3. **事件驱动**: 更好的事件系统支持
4. **缓存优化**: 统一的缓存策略
5. **错误处理**: 更完善的错误处理和重试机制

## 注意事项

1. **向后兼容**: 原有的 `QueueService.addAlertTask()` 和 `QueueService.addApiCallTask()` 方法仍然可用，但建议迁移到新的统一服务
2. **配置更新**: 确保相关配置文件已更新以支持新的队列服务
3. **测试**: 迁移后请充分测试所有相关功能

## 迁移检查清单

- [ ] 更新所有使用 `AlertQueueService` 的地方
- [ ] 更新所有使用 `ApiCallQueueService` 的地方
- [ ] 更新模块依赖
- [ ] 更新事件监听器
- [ ] 运行测试确保功能正常
- [ ] 更新相关文档