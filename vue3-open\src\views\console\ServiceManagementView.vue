<template>
  <div class="service-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>服务管理</h1>
        <p>管理开放平台的API服务</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon>
            <Plus />
          </el-icon>
          新建服务
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-input v-model="searchKeyword" placeholder="搜索服务名称、代码或描述" clearable @input="handleSearch">
            <template #prefix>
              <el-icon>
                <Search />
              </el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="filterStatus" placeholder="状态筛选" clearable @change="handleFilter">
            <el-option label="全部" value="" />
            <el-option label="活跃" value="active" />
            <el-option label="停用" value="inactive" />
            <el-option label="维护中" value="maintenance" />
            <el-option label="已废弃" value="deprecated" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="filterType" placeholder="类型筛选" clearable @change="handleFilter">
            <el-option label="全部" value="" />
            <el-option label="OCR" value="ocr" />
            <el-option label="NLP" value="nlp" />
            <el-option label="视觉" value="vision" />
            <el-option label="语音" value="speech" />
            <el-option label="翻译" value="translation" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button @click="resetFilters">重置</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 服务列表 -->
    <div class="service-table">
      <el-table v-loading="loading" :data="services" stripe @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />

        <el-table-column prop="name" label="服务名称" min-width="150">
          <template #default="{ row }">
            <div class="service-name">
              <strong>{{ row.name }}</strong>
              <div class="service-code">{{ row.code }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">{{ getTypeLabel(row.type) }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">{{ getStatusLabel(row.status) }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="version" label="版本" width="100" />

        <el-table-column prop="callCount" label="调用次数" width="120">
          <template #default="{ row }">
            {{ row.callCount?.toLocaleString() || 0 }}
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewService(row)">查看</el-button>
            <el-button size="small" type="primary" @click="editService(row)">编辑</el-button>
            <el-dropdown @command="(command: string) => handleAction(command, row)">
              <el-button size="small" type="info">
                更多<el-icon>
                  <ArrowDown />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="toggle-status">
                    {{ row.status === 'active' ? '停用' : '启用' }}
                  </el-dropdown-item>
                  <el-dropdown-item command="versions">版本管理</el-dropdown-item>
                  <el-dropdown-item command="stats">统计信息</el-dropdown-item>
                  <el-dropdown-item command="test">测试服务</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
          :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedServices.length > 0" class="batch-actions">
      <el-alert :title="`已选择 ${selectedServices.length} 个服务`" type="info" show-icon :closable="false">
        <template #default>
          <el-button size="small" @click="batchToggleStatus">批量启用/停用</el-button>
          <el-button size="small" type="danger" @click="batchDelete">批量删除</el-button>
        </template>
      </el-alert>
    </div>

    <!-- 创建/编辑服务对话框 -->
    <el-dialog v-model="dialogVisible" :title="isEditing ? '编辑服务' : '创建服务'" width="800px" @close="resetForm">
      <el-form ref="formRef" :model="form" :rules="formRules" label-width="120px">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="服务名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入服务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务代码" prop="code">
              <el-input v-model="form.code" placeholder="请输入服务代码" :disabled="isEditing" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="服务描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入服务描述" />
        </el-form-item>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="服务类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择服务类型">
                <el-option
                  v-for="item in ServiceTypeOpts"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择服务状态">
                <el-option
                  v-for="item in ServiceStatusOpts"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="服务版本" prop="version">
              <el-input v-model="form.version" placeholder="请输入版本号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务端点" prop="endpoint">
              <el-input v-model="form.endpoint" placeholder="请输入服务端点URL" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="定价模式" prop="pricingModel">
          <el-select v-model="form.pricingModel" placeholder="请选择定价模式">
            <el-option
                  v-for="item in PricingModelOpts"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
          </el-select>
        </el-form-item>

        <el-form-item v-if="form.pricingModel !== 'free'" label="基础价格">
          <el-input-number v-model="form.basePrice" :min="0" :precision="4" placeholder="请输入基础价格" />
        </el-form-item>

        <el-form-item label="服务特性">
          <el-tag v-for="(feature, index) in form.features" :key="index" closable @close="removeFeature(index)"
            style="margin-right: 8px; margin-bottom: 8px;">
            {{ feature }}
          </el-tag>
          <el-input v-if="showFeatureInput" ref="featureInputRef" v-model="newFeature" size="small"
            style="width: 120px;" @keyup.enter="addFeature" @blur="addFeature" />
          <el-button v-else size="small" @click="showAddFeature">+ 添加特性</el-button>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ isEditing ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 服务详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="服务详情" width="900px">
      <div v-if="currentService" class="service-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="服务名称">{{ currentService.name }}</el-descriptions-item>
          <el-descriptions-item label="服务代码">{{ currentService.code }}</el-descriptions-item>
          <el-descriptions-item label="服务类型">
            <el-tag :type="getTypeTagType(currentService.type)">{{ getTypeLabel(currentService.type)
            }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="服务状态">
            <el-tag :type="getStatusTagType(currentService.status)">{{ getStatusLabel(currentService.status)
            }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="版本">{{ currentService.version }}</el-descriptions-item>
          <el-descriptions-item label="调用次数">{{ currentService.callCount?.toLocaleString() || 0
          }}</el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">{{ formatDate(currentService.createdAt)
          }}</el-descriptions-item>
          <el-descriptions-item label="服务端点" :span="2">{{ currentService.endpoint }}</el-descriptions-item>
          <el-descriptions-item label="服务描述" :span="2">{{ currentService.description }}</el-descriptions-item>
        </el-descriptions>

        <div v-if="currentService.features?.length" class="features-section">
          <h4>服务特性</h4>
          <el-tag v-for="feature in currentService.features" :key="feature"
            style="margin-right: 8px; margin-bottom: 8px;">
            {{ feature }}
          </el-tag>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import {
  Plus,
  Search,
  ArrowDown
} from '@element-plus/icons-vue'
import { serviceApi } from '@/api/service'
import type { Service } from '@/types/service'

// 响应式数据
const loading = ref(false)
const services = ref<Service[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const searchKeyword = ref('')
const filterStatus = ref('')
const filterType = ref('')
const selectedServices = ref<Service[]>([])

// 对话框相关
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const isEditing = ref(false)
const submitting = ref(false)
const currentService = ref<Service | null>(null)

const ServiceTypeOpts = [
  { label: 'OCR识别', value: 'OCR' },
  { label: 'NLP处理', value: 'NLP' },
  { label: 'CV服务', value: 'CV' },
  { label: 'AI生成', value: 'AI' },
  { label: 'DATA服务', value: 'DATA' },
  { label: '其他服务', value: 'OTHER' },
]

const ServiceStatusOpts = [
  { label: '活跃', value: 'active' },
  { label: '未激活', value: 'inactive' },
  { label: '维护中', value: 'maintenance' },
  { label: '已弃用', value: 'deprecated' },
]
const PricingModelOpts = [
  { label: '免费', value: 'free' },
  { label: '按次付费', value: 'pay_per_use' },
  { label: '订阅制', value: 'subscription' },
  { label: '分层定价', value: 'tiered' },
]


// 表单相关
const formRef = ref<FormInstance>()
const form = reactive({
  name: '',
  code: '',
  description: '',
  type: '',
  status: 'active',
  version: '',
  endpoint: '',
  pricingModel: 'free',
  basePrice: 0,
  features: [] as string[]
})

// 特性输入相关
const showFeatureInput = ref(false)
const newFeature = ref('')
const featureInputRef = ref()

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入服务名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入服务代码', trigger: 'blur' }],
  description: [{ required: true, message: '请输入服务描述', trigger: 'blur' }],
  type: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
  status: [{ required: true, message: '请选择服务状态', trigger: 'change' }],
  version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
  endpoint: [{ required: true, message: '请输入服务端点', trigger: 'blur' }]
}

// 获取服务列表
const fetchServices = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      status: filterStatus.value || undefined,
      type: filterType.value || undefined,
      keyword: searchKeyword.value || undefined
    }

    const response = await serviceApi.getServices(params)
    services.value = response.data
    total.value = response.total
  } catch (error) {
    ElMessage.error('获取服务列表失败')
    console.error('获取服务列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchServices()
}

// 筛选处理
const handleFilter = () => {
  currentPage.value = 1
  fetchServices()
}

// 重置筛选
const resetFilters = () => {
  searchKeyword.value = ''
  filterStatus.value = ''
  filterType.value = ''
  currentPage.value = 1
  fetchServices()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchServices()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchServices()
}

// 选择处理
const handleSelectionChange = (selection: Service[]) => {
  selectedServices.value = selection
}

// 显示创建对话框
const showCreateDialog = () => {
  isEditing.value = false
  dialogVisible.value = true
}

// 查看服务
const viewService = (service: Service) => {
  currentService.value = service
  detailDialogVisible.value = true
}

// 编辑服务
const editService = (service: Service) => {
  isEditing.value = true
  Object.assign(form, {
    name: service.name,
    code: service.code,
    description: service.description,
    type: service.type,
    status: service.status,
    version: service.version,
    endpoint: service.endpoint,
    pricingModel: service.pricing?.model || 'free',
    basePrice: service.pricing?.basePrice || 0,
    features: [...(service.features || [])]
  })
  currentService.value = service
  dialogVisible.value = true
}

// 处理操作
const handleAction = async (command: string, service: Service) => {
  switch (command) {
    case 'toggle-status':
      await toggleServiceStatus(service)
      break
    case 'versions':
      ElMessage.info('版本管理功能开发中')
      break
    case 'stats':
      ElMessage.info('统计信息功能开发中')
      break
    case 'test':
      ElMessage.info('测试服务功能开发中')
      break
    case 'delete':
      await deleteService(service)
      break
  }
}

// 切换服务状态
const toggleServiceStatus = async (service: Service) => {
  try {
    const newStatus = service.status === 'active' ? 'inactive' : 'active'
    await serviceApi.updateService(service.id, { status: newStatus })
    ElMessage.success(`服务已${newStatus === 'active' ? '启用' : '停用'}`)
    fetchServices()
  } catch (error) {
    ElMessage.error('操作失败')
    console.error('切换服务状态失败:', error)
  }
}

// 删除服务
const deleteService = async (service: Service) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除服务 "${service.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await serviceApi.deleteService(service.id)
    ElMessage.success('删除成功')
    fetchServices()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除服务失败:', error)
    }
  }
}

// 批量操作
const batchToggleStatus = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要批量切换 ${selectedServices.value.length} 个服务的状态吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 这里需要后端支持批量操作接口
    ElMessage.info('批量操作功能开发中')
  } catch (error) {
    // 用户取消
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要批量删除 ${selectedServices.value.length} 个服务吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 这里需要后端支持批量操作接口
    ElMessage.info('批量删除功能开发中')
  } catch (error) {
    // 用户取消
  }
}

// 表单提交
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const serviceData = {
      name: form.name,
      code: form.code,
      description: form.description,
      type: form.type,
      status: form.status,
      version: form.version,
      endpoint: form.endpoint,
      pricing: {
        model: form.pricingModel,
        basePrice: form.pricingModel !== 'free' ? form.basePrice : undefined
      },
      features: form.features
    }

    if (isEditing.value && currentService.value) {
      await serviceApi.updateService(currentService.value.id, serviceData as any)
      ElMessage.success('更新成功')
    } else {
      await serviceApi.createService(serviceData as any)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    fetchServices()
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(isEditing.value ? '更新失败' : '创建失败')
      console.error('提交表单失败:', error)
    }
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    name: '',
    code: '',
    description: '',
    type: '',
    status: 'active',
    version: '',
    endpoint: '',
    pricingModel: 'free',
    basePrice: 0,
    features: []
  })
  currentService.value = null
  showFeatureInput.value = false
  newFeature.value = ''
}

// 特性管理
const showAddFeature = () => {
  showFeatureInput.value = true
  nextTick(() => {
    featureInputRef.value?.focus()
  })
}

const addFeature = () => {
  if (newFeature.value.trim()) {
    form.features.push(newFeature.value.trim())
    newFeature.value = ''
  }
  showFeatureInput.value = false
}

const removeFeature = (index: number) => {
  form.features.splice(index, 1)
}

// 工具函数
const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    ocr: 'OCR',
    nlp: 'NLP',
    vision: '视觉',
    speech: '语音',
    translation: '翻译',
    other: '其他'
  }
  return labels[type] || type
}

const getTypeTagType = (type: string) => {
  const types: Record<string, string> = {
    ocr: 'primary',
    nlp: 'success',
    vision: 'warning',
    speech: 'info',
    translation: 'danger',
    other: ''
  }
  return types[type] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    active: '活跃',
    inactive: '停用',
    maintenance: '维护中',
    deprecated: '已废弃'
  }
  return labels[status] || status
}

const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    active: 'success',
    inactive: 'info',
    maintenance: 'warning',
    deprecated: 'danger'
  }
  return types[status] || ''
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 初始化
onMounted(() => {
  fetchServices()
})
</script>

<style scoped>
.service-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.search-filters {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.service-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.service-name {
  line-height: 1.4;
}

.service-code {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.pagination {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  min-width: 400px;
}

.service-detail {
  padding: 16px 0;
}

.features-section {
  margin-top: 20px;
}

.features-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
}

:deep(.el-table) {
  border: none;
}

:deep(.el-table th) {
  background-color: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafafa;
}
</style>