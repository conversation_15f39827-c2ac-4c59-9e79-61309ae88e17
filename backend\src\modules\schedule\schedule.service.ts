import { Injectable, Logger, Optional } from '@nestjs/common';
import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';

import { RedisService } from '@/shared/redis.service';
import { CallRecordService } from '../call-record/call-record.service';
import { UserService } from '../user/user.service';
import { ApiKeyService } from '../api-key/services/api-key.service';
import { UserServiceService } from '../user-service/user-service.service';
import { TaskHistoryService } from './services/task-history.service';
import { ScheduleStatsDto, TaskStatusDto } from './dto';
import { TaskExecutionStatus } from './entities/task-execution.entity';

import dayjs from 'dayjs';

@Injectable()
export class ScheduleService {
  private readonly logger = new Logger(ScheduleService.name);
  private readonly DAILY_FREE_QUOTA = 5; // 每日免费额度
  private readonly ALERT_THRESHOLD = 0.2; // 20%预警阈值

  constructor(
    private readonly redisService: RedisService,
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly callRecordService: CallRecordService,
    private readonly userService: UserService,
    private readonly apiKeyService: ApiKeyService,
    private readonly userServiceService: UserServiceService,
    private readonly taskHistoryService: TaskHistoryService,
  ) {}

  /**
   * 重置每日免费额度
   * 只为新用户（未有过购买或充值记录的用户）重置免费额度
   */
  @Cron('0 0 * * *', { name: 'resetDailyFreeQuota' }) // 每天凌晨执行
  async resetDailyFreeQuota(): Promise<void> {
    const execution = await this.taskHistoryService.createExecution(
      'resetDailyFreeQuota',
      'daily_maintenance'
    );

    try {
      await this.taskHistoryService.startExecution(execution.id);
      this.logger.log('开始执行每日免费额度重置');

      // 查找符合条件的新用户（注册时间在7天内且没有购买记录的用户）
      const newUsers = await this.userService.findNewUsersForFreeQuota();

      let processedCount = 0;
      const errors: string[] = [];

      // 为每个新用户重置免费额度
      for (const user of newUsers) {
        try {
          await this.userServiceService.addFreeCountForAllServices(
            user.id,
            this.DAILY_FREE_QUOTA
          );
          processedCount++;
        } catch (error) {
          const errorMsg = `用户 ${user.id} 免费额度重置失败: ${error.message}`;
          this.logger.error(errorMsg);
          errors.push(errorMsg);
        }
      }

      const result = {
        totalUsers: newUsers.length,
        processedCount,
        errors: errors.length > 0 ? errors : undefined
      };

      this.logger.log(`每日免费额度重置完成，共处理 ${processedCount}/${newUsers.length} 个用户`);

      await this.taskHistoryService.completeExecution(
        execution.id,
        errors.length === 0,
        result,
        errors.length > 0 ? `部分用户处理失败: ${errors.length} 个错误` : undefined
      );
    } catch (error) {
      this.logger.error(`每日免费额度重置失败: ${error.message}`, error.stack);
      await this.taskHistoryService.completeExecution(
        execution.id,
        false,
        null,
        error.message,
        error.stack
      );
      throw error;
    }
  }

  /**
   * 定期同步API密钥到缓存
   * 确保缓存中的API密钥数据始终是最新的
   */
  @Cron('0 */30 * * * *', { name: 'syncApiKeysToCache' }) // 每30分钟执行一次
  async syncApiKeysToCache(): Promise<void> {
    const execution = await this.taskHistoryService.createExecution(
      'syncApiKeysToCache',
      'cache_sync'
    );

    try {
      await this.taskHistoryService.startExecution(execution.id);
      this.logger.log('开始同步API密钥到缓存');

      const result = await this.apiKeyService.syncAllKeysToCache();

      this.logger.log(`API密钥同步到缓存完成，同步了 ${result.syncedCount} 个密钥`);

      await this.taskHistoryService.completeExecution(
        execution.id,
        true,
        result
      );
    } catch (error) {
      this.logger.error(`API密钥同步到缓存失败: ${error.message}`, error.stack);
      await this.taskHistoryService.completeExecution(
        execution.id,
        false,
        null,
        error.message,
        error.stack
      );
      throw error;
    }
  }


  /**
   * 清理过期数据
   * 包括过期缓存、过期会话、临时文件等
   */
  @Cron('0 2 * * *', { name: 'cleanupExpiredData' }) // 每天凌晨2点执行
  async cleanupExpiredData(): Promise<void> {
    const execution = await this.taskHistoryService.createExecution(
      'cleanupExpiredData',
      'data_cleanup'
    );

    try {
      await this.taskHistoryService.startExecution(execution.id);
      this.logger.log('开始清理过期数据');

      const results = {
        expiredCache: 0,
        expiredSessions: 0,
        expiredFiles: 0,
        expiredTaskExecutions: 0
      };

      // 清理过期缓存
      results.expiredCache = await this.cleanupExpiredCache();

      // 清理过期会话
      results.expiredSessions = await this.cleanupExpiredSessions();

      // 清理过期文件
      results.expiredFiles = await this.cleanupExpiredFiles();

      // 清理过期的任务执行记录（只保留7天，减少数据库负担）
      results.expiredTaskExecutions = await this.taskHistoryService.cleanupOldExecutions(7);

      // 按数量清理，每个任务只保留最近5条成功记录
      const cleanupByCount = await this.taskHistoryService.cleanupExecutionsByCount(5);
      results.expiredTaskExecutions += cleanupByCount;

      this.logger.log(`过期数据清理完成: 缓存${results.expiredCache}项, 会话${results.expiredSessions}项, 文件${results.expiredFiles}项, 任务记录${results.expiredTaskExecutions}项`);

      await this.taskHistoryService.completeExecution(
        execution.id,
        true,
        results
      );
    } catch (error) {
      this.logger.error(`过期数据清理失败: ${error.message}`, error.stack);
      await this.taskHistoryService.completeExecution(
        execution.id,
        false,
        null,
        error.message,
        error.stack
      );
      throw error;
    }
  }



  /**
   * 清理过期缓存
   */
  private async cleanupExpiredCache(): Promise<number> {
    try {
      // 清理过期的API密钥缓存
      const apiKeyPattern = 'api_key:*';
      const keys = await this.redisService.keys(apiKeyPattern);

      let expiredCount = 0;
      for (const key of keys) {
        const ttl = await this.redisService.ttl(key);
        if (ttl === -1) { // 没有过期时间的key，设置默认过期时间
          await this.redisService.expire(key, 3600); // 1小时
        } else if (ttl === -2) { // 已过期的key
          await this.redisService.del(key);
          expiredCount++;
        }
      }

      // 清理其他过期缓存模式
      const patterns = ['session:*', 'temp:*', 'cache:*'];
      for (const pattern of patterns) {
        const patternKeys = await this.redisService.keys(pattern);
        for (const key of patternKeys) {
          const ttl = await this.redisService.ttl(key);
          if (ttl === -2) {
            await this.redisService.del(key);
            expiredCount++;
          }
        }
      }

      this.logger.log(`清理了 ${expiredCount} 个过期缓存项`);
      return expiredCount;
    } catch (error) {
      this.logger.error(`清理过期缓存失败: ${error.message}`);
      return 0;
    }
  }

  /**
   * 清理过期会话
   */
  private async cleanupExpiredSessions(): Promise<number> {
    try {
      // 这里可以根据实际的会话存储机制来实现
      // 例如清理Redis中的过期会话数据
      const sessionKeys = await this.redisService.keys('session:*');
      let expiredCount = 0;

      for (const key of sessionKeys) {
        const ttl = await this.redisService.ttl(key);
        if (ttl === -2) {
          await this.redisService.del(key);
          expiredCount++;
        }
      }

      this.logger.log(`清理了 ${expiredCount} 个过期会话`);
      return expiredCount;
    } catch (error) {
      this.logger.error(`清理过期会话失败: ${error.message}`);
      return 0;
    }
  }

  /**
   * 清理过期文件
   */
  private async cleanupExpiredFiles(): Promise<number> {
    try {
      // 这里可以实现文件系统的清理逻辑
      // 例如清理临时上传文件、日志文件等
      // 由于涉及文件系统操作，这里只是示例

      this.logger.log('文件清理功能待实现');
      return 0;
    } catch (error) {
      this.logger.error(`清理过期文件失败: ${error.message}`);
      return 0;
    }
  }

  /**
   * 获取调度任务统计信息
   */
  async getScheduleStats(): Promise<ScheduleStatsDto> {
    try {
      // 获取系统启动时间（这里简化处理）
      const systemStartTime = new Date(Date.now() - process.uptime() * 1000);

      // 获取任务统计
      const taskStats = await this.taskHistoryService.getTaskStats();

      // 获取今日统计
      const todayStats = await this.taskHistoryService.getTodayStats();

      // 获取最近执行记录
      const recentExecutions = await this.taskHistoryService.getRecentExecutions(10);

      // 获取活跃任务数（这里简化为已注册的定时任务数）
      const registeredJobs = this.schedulerRegistry.getCronJobs();
      const activeTasks = registeredJobs.size;

      return {
        systemStartTime,
        totalTasks: taskStats.length,
        activeTasks,
        todayExecutions: todayStats.totalExecutions,
        todaySuccessCount: todayStats.successCount,
        todayFailureCount: todayStats.failureCount,
        taskStats,
        recentExecutions: recentExecutions.map(execution => ({
          taskName: execution.taskName,
          status: execution.status,
          startTime: execution.startTime || new Date(),
          duration: execution.duration || 0,
          errorMessage: execution.errorMessage || undefined
        }))
      };
    } catch (error) {
      this.logger.error(`获取调度任务统计信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取任务状态列表
   */
  async getTaskStatusList(): Promise<TaskStatusDto[]> {
    try {
      const registeredJobs = this.schedulerRegistry.getCronJobs();
      const taskStatusList: TaskStatusDto[] = [];

      for (const [jobName, cronJob] of registeredJobs) {
        const lastExecution = await this.taskHistoryService.getLastExecution(jobName);

        taskStatusList.push({
          taskName: jobName,
          taskType: this.getTaskType(jobName),
          enabled: true, // 简化处理，所有注册的任务都认为是启用的
          cronExpression: this.getCronExpression(jobName),
          nextExecutionTime: cronJob.nextDate()?.toJSDate() || new Date(),
          lastExecutionTime: lastExecution?.startTime || undefined,
          lastExecutionStatus: lastExecution?.status,
          isRunning: lastExecution?.status === TaskExecutionStatus.RUNNING
        });
      }

      return taskStatusList;
    } catch (error) {
      this.logger.error(`获取任务状态列表失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 根据任务名称获取任务类型
   */
  private getTaskType(taskName: string): string {
    const typeMap: Record<string, string> = {
      'resetDailyFreeQuota': 'daily_maintenance',
      'syncApiKeysToCache': 'cache_sync',
      'cleanupExpiredData': 'data_cleanup',
      'generateDailyCallStatistics': 'statistics',
      'cleanupCallRecords': 'data_cleanup'
    };
    return typeMap[taskName] || 'unknown';
  }

  /**
   * 根据任务名称获取Cron表达式
   */
  private getCronExpression(taskName: string): string {
    const cronMap: Record<string, string> = {
      'resetDailyFreeQuota': '0 0 * * *',
      'syncApiKeysToCache': '0 */30 * * * *',
      'cleanupExpiredData': '0 2 * * *',
      'generateDailyCallStatistics': '0 0 2 * * *',
      'cleanupCallRecords': '0 30 3 1 1,3,5,7,9,11 *'
    };
    return cronMap[taskName] || 'unknown';
  }

  /**
   * 每天凌晨2:00执行生成每日调用统计
   */
  @Cron('0 0 2 * * *', { name: 'generateDailyCallStatistics' })
  async handleDailyCallStatistics() {
    this.logger.log('开始执行每日调用统计任务');
    await this.callRecordService.generateDailyStatistics();
    this.logger.log('每日调用统计任务完成');
  }

  /**
   * 每两个月执行一次清理过期调用记录
   * 在每单月的1号凌晨3:30执行
   */
  @Cron('0 30 3 1 1,3,5,7,9,11 *', { name: 'cleanupCallRecords' })
  async handleCallRecordCleanup() {
    this.logger.log('开始执行调用记录清理任务');
    await this.callRecordService.cleanupCallRecords();
    this.logger.log('调用记录清理任务完成');
  }

  /**
   * 每天凌晨4点清理任务执行记录
   * 减少非业务数据积累
   */
  @Cron('0 0 4 * * *', { name: 'cleanupTaskExecutions' })
  async handleTaskExecutionCleanup() {
    const execution = await this.taskHistoryService.createExecution(
      'cleanupTaskExecutions',
      'data_cleanup'
    );

    try {
      await this.taskHistoryService.startExecution(execution.id);
      this.logger.log('开始清理任务执行记录');

      // 清理7天前的记录
      const oldRecords = await this.taskHistoryService.cleanupOldExecutions(7);

      // 按数量清理，每个任务只保留最近3条成功记录
      const countCleanup = await this.taskHistoryService.cleanupExecutionsByCount(3);

      const result = {
        oldRecordsDeleted: oldRecords,
        countCleanupDeleted: countCleanup,
        totalDeleted: oldRecords + countCleanup
      };

      this.logger.log(`任务执行记录清理完成，共删除 ${result.totalDeleted} 条记录`);

      await this.taskHistoryService.completeExecution(
        execution.id,
        true,
        result
      );
    } catch (error) {
      this.logger.error(`任务执行记录清理失败: ${error.message}`, error.stack);
      await this.taskHistoryService.completeExecution(
        execution.id,
        false,
        null,
        error.message,
        error.stack
      );
      throw error;
    }
  }
}
