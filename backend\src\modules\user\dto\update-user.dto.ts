import { ApiProperty, PartialType, OmitType } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, Length, Matches } from 'class-validator';
import { CreateUserDto } from './create-user.dto';
import { UserStatus, UserType, UserVerificationStatus } from '../entities/user.entity';

export class UpdateUserDto extends PartialType(
  OmitType(CreateUserDto, ['username'] as const),
) {
  @ApiProperty({ description: '昵称', required: false })
  @IsOptional()
  @IsString({ message: '昵称必须是字符串' })
  @Length(2, 30, { message: '昵称长度必须在2-30之间' })
  nickname?: string;

  @ApiProperty({ description: '密码', required: false, format: 'password' })
  @IsOptional()
  @IsString({ message: '密码必须是字符串' })
  @Length(8, 30, { message: '密码长度必须在8-30之间' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/, {
    message: '密码必须包含至少一个大写字母，一个小写字母和一个数字',
  })
  password?: string;

  @ApiProperty({ description: '真实姓名', required: false })
  @IsOptional()
  @IsString({ message: '真实姓名必须是字符串' })
  @Length(2, 50, { message: '真实姓名长度必须在2-50之间' })
  realName?: string;

  @ApiProperty({ description: '头像URL', required: false })
  @IsOptional()
  @IsString({ message: '头像URL必须是字符串' })
  avatar?: string;

  @ApiProperty({ description: '手机号', required: false })
  @IsOptional()
  @IsString({ message: '手机号必须是字符串' })
  @Matches(/^1[3-9]\d{9}$/, { message: '手机号格式不正确' })
  phone?: string;

  @ApiProperty({ description: '用户类型', enum: UserType, required: false })
  @IsOptional()
  @IsEnum(UserType, { message: '用户类型无效' })
  userType?: UserType;

  @ApiProperty({ description: '用户状态', enum: UserStatus, required: false })
  @IsOptional()
  @IsEnum(UserStatus, { message: '用户状态无效' })
  userStatus?: UserStatus;

  @ApiProperty({ description: '认证状态', enum: UserVerificationStatus, required: false })
  @IsOptional()
  @IsEnum(UserVerificationStatus, { message: '认证状态无效' })
  verificationStatus?: UserVerificationStatus;
} 