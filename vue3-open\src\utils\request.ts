import axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  type AxiosResponse,
  type InternalAxiosRequestConfig,
  AxiosHeaders,
} from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import router from '@/router'

// 创建主要的axios实例（用于业务API）
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
  },
})

// 创建API测试专用的axios实例（无baseURL限制，用于测试任意API）
const apiTestService: AxiosInstance = axios.create({
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
  },
})

/**
 * 配置请求拦截器
 * @param instance axios实例
 * @param options 配置选项
 */
function setupRequestInterceptor(instance: AxiosInstance, options: { addToken?: boolean } = {}) {
  instance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      const userStore = useUserStore()

      // 根据配置决定是否添加token（API测试可能不需要token）
      if (options.addToken !== false && userStore.token) {
        if (!config.headers) {
          config.headers = new AxiosHeaders()
        }
        config.headers.Authorization = `Bearer ${userStore.token}`
        console.log('请求发送token:', userStore.token.substring(0, 20) + '...')
      } else if (options.addToken !== false) {
        console.log('请求未发送token，userStore.token为空')
      }

      console.log('请求配置:', {
        url: config.url,
        method: config.method,
        headers: config.headers
      })

      return config
    },
    (error) => {
      console.error('请求错误:', error)
      return Promise.reject(error)
    },
  )
}

// 是否正在刷新token
let isRefreshing = false
// 失败请求队列
let failedQueue: Array<{
  resolve: (value?: unknown) => void
  reject: (reason?: unknown) => void
}> = []

// 处理队列中的请求
const processQueue = (error: unknown, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error)
    } else {
      resolve(token)
    }
  })

  failedQueue = []
}

// 防重复弹窗标记
let showingLogoutConfirm = false

/**
 * 配置响应拦截器
 * @param instance axios实例
 * @param options 配置选项
 */
function setupResponseInterceptor(instance: AxiosInstance, options: {
  handleBusinessLogic?: boolean,
  handleTokenRefresh?: boolean
} = {}) {
  instance.interceptors.response.use(
    async (response: AxiosResponse) => {
      // 如果不处理业务逻辑（如API测试），直接返回原始响应
      if (options.handleBusinessLogic === false) {
        return response
      }

      // HTTP状态码200表示请求成功
      if (response.status === 200) {
        const { success, code, message, data } = response.data
        console.log('响应数据:', { success, code, message, url: response.config?.url })

        // 检查业务状态
        if (success === true) {
          return data
        }

        // 处理业务错误（除了401，401统一在error拦截器中处理）
        if (code === 401) {
          console.log('检测到401业务错误，转换为HTTP 401错误')
          // 将401错误转换为HTTP 401错误，统一在error拦截器中处理
          const error: any = new Error(message || '登录状态已过期')
          error.response = {
            status: 401,
            data: response.data
          }
          error.config = response.config
          const userStore = useUserStore()
          console.log('检测到401错误，准备处理登出逻辑', {
            showingLogoutConfirm,
            hasToken: !!userStore.token
          })

          // 防止多次弹窗
          if (showingLogoutConfirm) {
            console.log('已有登出确认弹窗，跳过重复处理')
            return Promise.reject(error)
          }
          showingLogoutConfirm = true

          try {
            console.log('显示登出确认弹窗')
            await ElMessageBox.confirm('登录状态已过期，是否重新登录？', '系统提示', {
              confirmButtonText: '重新登录',
              cancelButtonText: '取消',
              type: 'warning',
            })
            console.log('用户确认重新登录，执行登出操作')
            await userStore.logout()
            const redirect = router.currentRoute.value.fullPath || '/'
            console.log('跳转到登录页面', { redirect })
            router.replace({ name: 'login', query: { redirect } })
          } catch (e) {
            console.log('用户取消重新登录或登出失败', e)
            // 用户点击取消，不跳转，但仍拒绝本次请求
          } finally {
            showingLogoutConfirm = false
          }
          return Promise.reject(error)
        }

        // 其他业务错误
        console.warn('业务错误:', { success, code, message, url: response.config?.url })
        return Promise.reject(new Error(message || '请求失败'))
      }

      // HTTP状态码非200，直接抛出错误
      console.warn('HTTP错误:', { status: response.status, url: response.config?.url })
      return Promise.reject(new Error(`HTTP ${response.status}: ${response.statusText}`))
    },
    async (error) => {
      console.error('响应错误:', error)
      const originalRequest = error.config
      // 处理401错误（token过期）
      // 但不处理API签名认证的401错误，避免重复请求导致Nonce冲突
      if (error.response?.status === 401 && !originalRequest._retry) {
        // 检查是否是API签名认证请求（包含X-API-KEY头）
        const isApiSignatureRequest = originalRequest.headers &&
          (originalRequest.headers['X-API-KEY'] || originalRequest.headers['x-api-key']);

        // 如果是API签名认证请求，直接拒绝，不进行重试
        if (isApiSignatureRequest) {
          console.log('API签名认证请求401错误，不进行重试')
          return Promise.reject(error)
        }

        // 如果不处理token刷新（如API测试），直接拒绝
        if (options.handleTokenRefresh === false) {
          console.log('API测试请求401错误，不处理token刷新')
          return Promise.reject(error)
        }

        const userStore = useUserStore()
        console.log('检测到401错误，准备处理登出逻辑', {
          showingLogoutConfirm,
          url: originalRequest.url,
          hasToken: !!userStore.token
        })

        // 防止多次弹窗
        if (showingLogoutConfirm) {
          console.log('已有登出确认弹窗，跳过重复处理')
          return Promise.reject(error)
        }
        showingLogoutConfirm = true

        try {
          console.log('显示登出确认弹窗')
          await ElMessageBox.confirm('登录状态已过期，是否重新登录？', '系统提示', {
            confirmButtonText: '重新登录',
            cancelButtonText: '取消',
            type: 'warning',
          })
          console.log('用户确认重新登录，执行登出操作')
          await userStore.logout()
          const redirect = router.currentRoute.value.fullPath || '/'
          console.log('跳转到登录页面', { redirect })
          router.replace({ name: 'login', query: { redirect } })
        } catch (e) {
          console.log('用户取消重新登录或登出失败', e)
          // 用户点击取消，不跳转，但仍拒绝本次请求
        } finally {
          showingLogoutConfirm = false
        }
        return Promise.reject(error)
      }

      // 处理其他HTTP错误
      let message = '网络错误'
      if (error.response) {
        switch (error.response.status) {
          case 400:
            message = '请求参数错误'
            break
          case 403:
            message = '拒绝访问'
            break
          case 404:
            message = '请求地址不存在'
            break
          case 500:
            message = '服务器内部错误'
            break
          default:
            message = `连接错误${error.response.status}`
        }
      } else if (error.code === 'ECONNABORTED') {
        message = '请求超时'
      }

      // 如果不处理业务逻辑（如API测试），不显示错误消息
      if (options.handleBusinessLogic !== false) {
        ElMessage.error(message)
      }
      return Promise.reject(error)
    },
  )
}

// 配置主要服务实例的拦截器（用于业务API）
setupRequestInterceptor(service, { addToken: true })
setupResponseInterceptor(service, { handleBusinessLogic: true, handleTokenRefresh: true })

// 配置API测试实例的拦截器（用于API测试）
setupRequestInterceptor(apiTestService, { addToken: false })
setupResponseInterceptor(apiTestService, { handleBusinessLogic: false, handleTokenRefresh: false })

export default service

// 导出API测试专用实例
export { apiTestService }

// 导出请求方法
export const request = {
  get<T = unknown>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return service.get(url, config)
  },

  post<T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    return service.post(url, data, config)
  },

  put<T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    return service.put(url, data, config)
  },

  patch<T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    return service.patch(url, data, config)
  },

  delete<T = unknown>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return service.delete(url, config)
  },
}
