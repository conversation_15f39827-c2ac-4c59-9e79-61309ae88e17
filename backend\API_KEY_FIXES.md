# API密钥查看功能修复总结

## 问题描述

用户通过手机验证码快捷注册登录后，进入API密钥页面，点击安全密钥的"查看密钥"按钮没有成功显示安全密钥。接口返回数据中没有 `secretKey` 字段，但 `isViewed` 为 `false`，说明密钥确实未被查看过。

## 问题分析

### 1. 根本原因
种子数据中的加密方式与API密钥服务中的加密方式不一致：

- **种子数据**：使用 `crypto.createHash('sha256')` - 哈希加密（不可逆）
- **API密钥服务**：使用 `aes-256-cbc` - 对称加密（可逆）

### 2. 具体问题
1. 种子数据生成的 `encryptedSecretKey` 是哈希值，无法解密
2. API密钥服务的 `findOne` 方法尝试解密时失败
3. 缓存服务不包含 `secretKey` 字段，导致缓存命中时无法返回密钥

## 修复方案

### 1. 修复种子数据加密方式

**文件**: `backend/src/database/seeds/003-api-keys.seed.ts`

#### 修复前
```typescript
// 生成加密的密钥
const encryptedSecretKey = crypto.createHash('sha256').update(secretKey).digest('hex');
```

#### 修复后
```typescript
// 生成加密的密钥（使用与API密钥服务相同的加密方式）
const algorithm = 'aes-256-cbc';
const key = crypto.scryptSync('api-key-secret', 'salt', 32);
const iv = crypto.randomBytes(16);

const cipher = crypto.createCipheriv(algorithm, key, iv);
let encrypted = cipher.update(secretKey, 'utf8', 'hex');
encrypted += cipher.final('hex');

// 将iv和加密数据组合
const encryptedSecretKey = iv.toString('hex') + ':' + encrypted;
```

### 2. 优化API密钥服务查询逻辑

**文件**: `backend/src/modules/api-key/services/api-key.service.ts`

#### 修复前
```typescript
async findOne(id: number, userId: number): Promise<ApiKeyDto> {
  // 先从缓存获取
  const cachedApiKey = await this.apiKeyCacheService.getApiKeyById(id);
  if (cachedApiKey && cachedApiKey.userId === userId) {
    return cachedApiKey; // 缓存命中时直接返回，不包含secretKey
  }
  // ...
}
```

#### 修复后
```typescript
async findOne(id: number, userId: number): Promise<ApiKeyDto> {
  // 从数据库获取（不依赖缓存，确保能获取到完整数据）
  const apiKey = await this.apiKeyRepository.findOne({
    where: { id, userId }
  });
  
  if (!apiKey) {
    throw new NotFoundException('API密钥不存在');
  }
  
  // 缓存API密钥（用于其他查询）
  await this.apiKeyCacheService.cacheApiKey(apiKey);

  // 返回DTO
  const apiKeyDto = await this.toApiKeyDto(apiKey, true);

  // 如果密钥还未被查看过，返回解密的密钥凭证
  if (!apiKey.isViewed && apiKey.encryptedSecretKey) {
    try {
      const decryptedSecretKey = await this.decryptSecretKey(apiKey.encryptedSecretKey);
      apiKeyDto.secretKey = decryptedSecretKey;
      this.logger.log(`返回未查看密钥的secretKey: ${apiKey.id}`);
    } catch (error) {
      this.logger.warn(`解密密钥失败: ${apiKey.id}, ${error.message}`);
    }
  } else {
    this.logger.log(`不返回secretKey: ${apiKey.id}, isViewed: ${apiKey.isViewed}, hasEncryptedSecretKey: ${!!apiKey.encryptedSecretKey}`);
  }

  return apiKeyDto;
}
```

### 3. 创建修复脚本

**文件**: `backend/scripts/fix-api-keys.ts`

创建了一个脚本来修复现有数据库中的API密钥：

```typescript
// 检查是否已经是正确的加密格式（包含冒号分隔符）
if (apiKey.encryptedSecretKey && apiKey.encryptedSecretKey.includes(':')) {
  console.log(`API密钥 ${apiKey.id} 已经是正确的加密格式，跳过`);
  continue;
}

// 生成新的密钥
const secretKey = crypto.randomBytes(32).toString('hex');

// 使用正确的加密方式
const algorithm = 'aes-256-cbc';
const key = crypto.scryptSync('api-key-secret', 'salt', 32);
const iv = crypto.randomBytes(16);

const cipher = crypto.createCipheriv(algorithm, key, iv);
let encrypted = cipher.update(secretKey, 'utf8', 'hex');
encrypted += cipher.final('hex');

// 将iv和加密数据组合
const encryptedSecretKey = iv.toString('hex') + ':' + encrypted;

// 更新API密钥
await apiKeyRepository.update(
  { id: apiKey.id },
  { 
    encryptedSecretKey,
    isViewed: false, // 重置为未查看状态
    updatedAt: new Date()
  }
);
```

## 部署和测试步骤

### 1. 重新运行种子数据
```bash
cd backend
npm run db:seed
```

### 2. 或者运行修复脚本（如果已有数据）
```bash
cd backend
npx ts-node scripts/fix-api-keys.ts
```

### 3. 测试验证
1. 重新注册/登录用户
2. 进入API密钥页面
3. 点击"查看密钥"按钮
4. 确认能正确显示安全密钥

## 技术细节

### 加密方式对比

| 方式 | 可逆性 | 用途 | 示例 |
|------|--------|------|------|
| 哈希 (SHA256) | 不可逆 | 密码存储 | `crypto.createHash('sha256').update(data).digest('hex')` |
| 对称加密 (AES) | 可逆 | 敏感数据存储 | `crypto.createCipheriv('aes-256-cbc', key, iv)` |

### 加密格式
- **IV**: 16字节随机初始化向量
- **加密数据**: AES-256-CBC加密的密钥数据
- **组合格式**: `iv_hex:encrypted_hex`

### 安全考虑
1. **密钥分离**: `secretKey` 仅在创建和查看时返回
2. **缓存策略**: 缓存中不包含 `secretKey` 字段
3. **访问控制**: 只有密钥所有者才能查看
4. **一次性查看**: 查看后标记为已查看，不再返回

## 预期效果

修复后，用户应该能够：
1. ✅ 成功查看未查看过的API密钥的 `secretKey`
2. ✅ 看到正确的密钥格式（`sk-` 前缀）
3. ✅ 复制密钥到剪贴板
4. ✅ 在API测试工具中使用密钥进行签名验证

## 注意事项

1. **数据迁移**: 现有用户的API密钥需要重新生成
2. **密钥安全**: 新生成的密钥需要用户重新保存
3. **兼容性**: 确保前端能正确处理新的密钥格式
4. **日志监控**: 添加了详细的日志记录，便于调试 