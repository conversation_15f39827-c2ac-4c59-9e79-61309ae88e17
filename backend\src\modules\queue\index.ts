/**
 * 队列模块入口文件
 * 导出所有公共组件
 */

// 导出模块
export { QueueModule } from './queue.module';

// 导出服务
export { TaskService } from './services/task.service';
export { QueueManagerService } from './services/queue-manager.service';
export { RedisPubSubService } from './services/pub-sub.service';

// 导出接口和枚举
export { TaskStatus } from './interfaces/task-status.enum';
export { 
  CreateTaskOptions, 
  TaskDetails, 
  TaskStatusUpdate 
} from './interfaces/task.interface';
export {
  OcrJobData,
  AddressJobData,
  OcrResult
} from './interfaces/queue-job.interface';

// 导出配置
export { QUEUE_CONFIG, QueuePriority } from './config/queue.config'; 