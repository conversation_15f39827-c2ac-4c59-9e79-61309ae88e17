import { DataSource } from 'typeorm';
import { Logger } from '@nestjs/common';
import { ISeed, SeedRunOptions, SeedResult, SeedStatus } from './base/seed.interface';

/**
 * 种子数据管理器
 * 负责种子数据的注册、依赖解析、执行调度等
 */
export class SeedManager {
  private readonly logger = new Logger(SeedManager.name);
  private readonly seeds = new Map<string, ISeed>();
  
  /**
   * 注册种子数据
   */
  register(seed: ISeed): void {
    this.seeds.set(seed.config.name, seed);
    this.logger.debug(`注册种子数据: ${seed.config.name}`);
  }
  
  /**
   * 批量注册种子数据
   */
  registerAll(seeds: ISeed[]): void {
    seeds.forEach(seed => this.register(seed));
  }
  
  /**
   * 获取种子数据
   */
  get(name: string): ISeed | undefined {
    return this.seeds.get(name);
  }
  
  /**
   * 获取所有种子数据
   */
  getAll(): ISeed[] {
    return Array.from(this.seeds.values());
  }
  
  /**
   * 执行种子数据
   */
  async run(dataSource: DataSource, options: SeedRunOptions = {}): Promise<SeedResult[]> {
    const {
      names,
      environment = process.env.NODE_ENV || 'development',
      force = false,
      skipDependencies = false,
      parallel = false,
      verbose = false,
    } = options;
    
    this.logger.log(`开始执行种子数据，环境: ${environment}`);
    
    // 获取要执行的种子数据
    let seedsToRun = this.getSeedsToRun(names, environment);
    
    if (seedsToRun.length === 0) {
      this.logger.warn('没有找到要执行的种子数据');
      return [];
    }
    
    // 解析依赖关系
    if (!skipDependencies) {
      seedsToRun = this.resolveDependencies(seedsToRun);
    }
    
    // 按优先级排序
    seedsToRun.sort((a, b) => (a.config.priority || 100) - (b.config.priority || 100));
    
    if (verbose) {
      this.logger.log(`执行顺序: ${seedsToRun.map(s => s.config.name).join(' -> ')}`);
    }
    
    // 执行种子数据
    const results: SeedResult[] = [];
    
    if (parallel) {
      // 并行执行（忽略依赖关系）
      const promises = seedsToRun.map(seed => this.executeSeed(seed, dataSource, force));
      const parallelResults = await Promise.allSettled(promises);
      
      parallelResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            success: false,
            message: `种子数据 ${seedsToRun[index].config.name} 执行失败`,
            error: result.reason?.message || String(result.reason),
          });
        }
      });
    } else {
      // 串行执行
      for (const seed of seedsToRun) {
        const result = await this.executeSeed(seed, dataSource, force);
        results.push(result);
        
        // 如果执行失败且不是强制模式，停止后续执行
        if (!result.success && !force) {
          this.logger.error(`种子数据 ${seed.config.name} 执行失败，停止后续执行`);
          break;
        }
      }
    }
    
    // 输出执行摘要
    this.logExecutionSummary(results);
    
    return results;
  }
  
  /**
   * 获取种子数据执行状态（简化版）
   */
  async getStatus(dataSource: DataSource, names?: string[]): Promise<SeedStatus[]> {
    const seeds = names ?
      names.map(name => this.seeds.get(name)).filter(Boolean) as ISeed[] :
      this.getAll();

    const statuses: SeedStatus[] = [];

    for (const seed of seeds) {
      try {
        // 使用种子数据自己的shouldRun方法来判断状态
        const shouldRun = seed.shouldRun ? await seed.shouldRun(dataSource) : true;
        const executed = !shouldRun; // 如果不需要运行，说明已经执行过了

        statuses.push({
          name: seed.config.name,
          executed,
          executedAt: executed ? new Date() : undefined,
          version: seed.config.version,
        });
      } catch (error) {
        statuses.push({
          name: seed.config.name,
          executed: false,
        });
      }
    }

    return statuses;
  }
  
  /**
   * 回滚种子数据
   */
  async revert(dataSource: DataSource, names: string[]): Promise<SeedResult[]> {
    const results: SeedResult[] = [];
    
    for (const name of names) {
      const seed = this.seeds.get(name);
      if (!seed) {
        results.push({
          success: false,
          message: `种子数据 ${name} 不存在`,
        });
        continue;
      }
      
      if (!seed.revert) {
        results.push({
          success: false,
          message: `种子数据 ${name} 不支持回滚`,
        });
        continue;
      }
      
      try {
        const result = await seed.revert(dataSource);
        results.push(result);
        
        if (result.success) {
          this.logger.log(`种子数据 ${seed.config.name} 回滚成功`);
        }
      } catch (error) {
        results.push({
          success: false,
          message: `回滚种子数据 ${name} 失败`,
          error: error.message,
        });
      }
    }
    
    return results;
  }
  
  /**
   * 获取要执行的种子数据
   */
  private getSeedsToRun(names?: string[], environment?: string): ISeed[] {
    let seeds = this.getAll();
    
    // 按名称过滤
    if (names && names.length > 0) {
      seeds = seeds.filter(seed => names.includes(seed.config.name));
    }
    
    // 按环境过滤
    if (environment) {
      seeds = seeds.filter(seed => 
        seed.config.environments.includes(environment) ||
        seed.config.environments.includes('all')
      );
    }
    
    return seeds;
  }
  
  /**
   * 解析依赖关系
   */
  private resolveDependencies(seeds: ISeed[]): ISeed[] {
    const resolved: ISeed[] = [];
    const resolving = new Set<string>();
    
    const resolve = (seed: ISeed): void => {
      if (resolved.includes(seed)) {
        return;
      }
      
      if (resolving.has(seed.config.name)) {
        throw new Error(`检测到循环依赖: ${seed.config.name}`);
      }
      
      resolving.add(seed.config.name);
      
      // 先解析依赖
      for (const depName of seed.config.dependencies) {
        const dependency = this.seeds.get(depName);
        if (!dependency) {
          throw new Error(`种子数据 ${seed.config.name} 的依赖 ${depName} 不存在`);
        }
        resolve(dependency);
      }
      
      resolving.delete(seed.config.name);
      resolved.push(seed);
    };
    
    seeds.forEach(resolve);
    
    return resolved;
  }
  
  /**
   * 执行单个种子数据
   */
  private async executeSeed(seed: ISeed, dataSource: DataSource, force: boolean): Promise<SeedResult> {
    try {
      // 临时设置强制执行标志
      const originalForce = seed.config.force;
      seed.config.force = force || originalForce;
      
      const result = await seed.run(dataSource);
      
      // 恢复原始设置
      seed.config.force = originalForce;
      
      return result;
    } catch (error) {
      return {
        success: false,
        message: `执行种子数据 ${seed.config.name} 失败`,
        error: error.message,
      };
    }
  }
  

  
  /**
   * 输出执行摘要
   */
  private logExecutionSummary(results: SeedResult[]): void {
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    const totalTime = results.reduce((sum, r) => sum + (r.executionTime || 0), 0);
    
    this.logger.log(`种子数据执行完成: 成功 ${successful}, 失败 ${failed}, 总耗时 ${totalTime}ms`);
    
    if (failed > 0) {
      const failedSeeds = results.filter(r => !r.success);
      this.logger.error(`失败的种子数据: ${failedSeeds.map(r => r.message).join(', ')}`);
    }
  }
}
