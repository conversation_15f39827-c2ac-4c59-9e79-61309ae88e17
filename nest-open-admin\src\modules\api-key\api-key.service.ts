import { Injectable, NotFoundException, BadRequestException, ConflictException, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, In, DeepPartial } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { ApiKeyEntity, ApiKeyStatus } from './entities/api-key.entity';
import { CreateApiKeyDto, UpdateApiKeyDto, QueryApiKeyDto, RegenerateApiKeyDto, ApiKeyResponseDto, ApiKeyListResponseDto, ApiKeyStatsDto } from './dto/api-key.dto';
import { RedisService } from '@/shared/redis.service';
import { GenerateUUID } from '@/common/utils';
import { UserService } from '../user/user.service';
import { ServiceService } from '../service/service.service';
import { BaseCrudService } from '../../common/base/base-crud.service';
import { KeyManagementService } from './services/key-management.service';
import * as crypto from 'crypto';
import { ConfigService } from '@nestjs/config';

// 默认API密钥有效期为一年
const DEFAULT_API_KEY_VALIDITY_PERIOD = 365; // 天数

@Injectable()
export class ApiKeyService extends BaseCrudService<
  ApiKeyEntity,
  CreateApiKeyDto,
  UpdateApiKeyDto,
  QueryApiKeyDto,
  ApiKeyResponseDto,
  ApiKeyListResponseDto
> {
  protected readonly repository: Repository<ApiKeyEntity>;
  protected readonly entityName = 'ApiKey';

  constructor(
    @InjectRepository(ApiKeyEntity)
    apiKeyRepository: Repository<ApiKeyEntity>,
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    private readonly serviceService: ServiceService,
    private readonly redisService: RedisService,
    @Inject(forwardRef(() => KeyManagementService))
    private readonly keyManagementService: KeyManagementService,
    private readonly configService: ConfigService,
  ) {
    super();
    this.repository = apiKeyRepository;
  }

  // 实现基类的抽象方法
  protected async validateBeforeCreate(createDto: CreateApiKeyDto): Promise<void> {
    // 验证用户是否存在
    const user = await this.userService.findById(createDto.userId);
    if (!user) {
      throw new NotFoundException(`用户 #${createDto.userId} 不存在`);
    }

    // 验证服务是否存在，serviceId为0表示平台级密钥，不需要验证服务
    if (createDto.serviceId !== 0) {
      const service = await this.serviceService.findOne(createDto.serviceId);
      if (!service) {
        throw new NotFoundException(`服务 #${createDto.serviceId} 不存在`);
      }
    }

    // 检查用户是否已达到API密钥数量限制
    const existingKeysCount = await this.repository.count({
      where: { userId: createDto.userId, keyStatus: ApiKeyStatus.ACTIVE }
    });

    const maxKeysPerUser = 10; // 可以从配置中获取
    if (existingKeysCount >= maxKeysPerUser) {
      throw new BadRequestException(`用户最多只能创建 ${maxKeysPerUser} 个API密钥`);
    }
  }

  protected async validateBeforeUpdate(id: number, updateDto: UpdateApiKeyDto, entity: ApiKeyEntity): Promise<void> {
    // 可以添加更新前的验证逻辑
  }

  protected async validateBeforeDelete(id: number, entity: ApiKeyEntity): Promise<void> {
    // 可以添加删除前的验证逻辑
  }

  protected transformCreateDto(createDto: CreateApiKeyDto): DeepPartial<ApiKeyEntity> {
    // 如果没有指定过期时间，默认设置为一年后
    const defaultExpiresAt = new Date();
    defaultExpiresAt.setDate(defaultExpiresAt.getDate() + DEFAULT_API_KEY_VALIDITY_PERIOD);
    
    return {
      userId: createDto.userId,
      serviceId: createDto.serviceId,
      name: createDto.name,
      description: createDto.description,
      keyType: createDto.keyType,
      permissions: createDto.permissions || [],
      expiresAt: createDto.expiresAt || defaultExpiresAt,
    };
  }

  protected transformUpdateDto(updateDto: UpdateApiKeyDto, entity: ApiKeyEntity): DeepPartial<ApiKeyEntity> {
    return {
      name: updateDto.name ?? entity.name,
      description: updateDto.description ?? entity.description,
      permissions: updateDto.permissions ?? entity.permissions,
      expiresAt: updateDto.expiresAt ?? entity.expiresAt,
    };
  }

  /**
   * 应用查询条件
   */
  protected applyQueryConditions(
    queryBuilder: any,
    queryDto: QueryApiKeyDto,
  ): void {
    const {
      userId,
      serviceId,
      name,
      keyStatus,
      keyType,
      isExpired,
    } = queryDto;

    if (userId) {
      queryBuilder.andWhere('entity.userId = :userId', { userId });
    }

    if (serviceId) {
      queryBuilder.andWhere('entity.serviceId = :serviceId', { serviceId });
    }

    if (name) {
      queryBuilder.andWhere('entity.name LIKE :name', { name: `%${name}%` });
    }

    if (keyStatus) {
      queryBuilder.andWhere('entity.keyStatus = :keyStatus', { keyStatus });
    }

    if (keyType) {
      queryBuilder.andWhere('entity.keyType = :keyType', { keyType });
    }

    if (isExpired !== undefined) {
      if (isExpired) {
        queryBuilder.andWhere('entity.expiresAt IS NOT NULL AND entity.expiresAt < :now', { now: new Date() });
      } else {
        queryBuilder.andWhere('entity.expiresAt IS NULL OR entity.expiresAt >= :now', { now: new Date() });
      }
    }
  }

  /**
   * 转换实体为响应DTO
   */
  protected transformToResponseDto(entity: ApiKeyEntity): ApiKeyResponseDto {
    return this.transformToResponseDtoInternal(entity, false);
  }

  /**
   * 转换实体列表为响应DTO
   */
  protected transformToListResponseDto(
    entities: ApiKeyEntity[],
    total: number,
    page: number,
    limit: number,
  ): ApiKeyListResponseDto {
    const data = entities.map(item => this.transformToResponseDtoInternal(item, false));
    const totalPages = Math.ceil(total / limit);
    return {
      data,
      items: data,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    };
  }

  /**
   * 查询API密钥列表
   */
  async findAll(query: QueryApiKeyDto): Promise<ApiKeyListResponseDto> {
    // 使用父类的findAll方法，但保留自定义的关联查询
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = query;

    const queryBuilder = this.repository
      .createQueryBuilder('entity')
      .leftJoinAndSelect('entity.user', 'user')
      .leftJoinAndSelect('entity.service', 'service');

    this.applyQueryConditions(queryBuilder, query);

    // 排序
    queryBuilder.orderBy(`entity.${sortBy}`, sortOrder);

    // 分页
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [entities, total] = await queryBuilder.getManyAndCount();

    return this.transformToListResponseDto(entities, total, page, limit);
  }

  /**
   * 根据ID查询单个API密钥
   */
  async findOne(id: number): Promise<ApiKeyResponseDto> {
    const apiKey = await this.repository.findOne({
      where: { id },
      relations: ['user', 'service'],
    });

    if (!apiKey) {
      throw new NotFoundException(`API密钥 #${id} 不存在`);
    }

    return this.transformToResponseDtoInternal(apiKey, false);
  }

  /**
   * 更新API密钥
   */
  async update(id: number, updateApiKeyDto: UpdateApiKeyDto): Promise<ApiKeyResponseDto> {
    try {
      const entity = await this.repository.findOne({
        where: { id },
        relations: ['user', 'service'],
      });

      if (!entity) {
        throw new NotFoundException(`API密钥 #${id} 不存在`);
      }

      await this.validateBeforeUpdate(id, updateApiKeyDto, entity);

      // 更新实体
      const updatedData = this.transformUpdateDto(updateApiKeyDto, entity);
      Object.assign(entity, updatedData);
      entity.updatedAt = new Date();

      const updatedEntity = await this.repository.save(entity);

      // 同步到Redis缓存
      await this.syncApiKeyToCache(updatedEntity);

      return this.transformToResponseDto(updatedEntity);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 删除API密钥
   */
  async remove(id: number): Promise<{ message: string }> {
    try {
      const entity = await this.repository.findOne({ where: { id } });

      if (!entity) {
        throw new NotFoundException(`API密钥 #${id} 不存在`);
      }

      await this.validateBeforeDelete(id, entity);

      // 先清除缓存
      await this.clearApiKeyCache(entity.apiKey);

      // 软删除
      await this.repository.softDelete(id);

      return { message: `API密钥 #${id} 已成功删除` };
    } catch (error) {
      throw error;
    }
  }

  /**
   * 创建API密钥
   */
  async createApiKey(dto: CreateApiKeyDto): Promise<ApiKeyResponseDto> {
    try {
      await this.validateBeforeCreate(dto);

      // 生成API密钥和密钥对
      const { apiKey, secretKey, secretHash, encryptedSecret } = await this.generateApiKeyPair();

      // 创建API密钥实体
      const entityData = this.transformCreateDto(dto);
      const newApiKey = this.repository.create({
        ...entityData,
        apiKey,
        secretHash,
        tempSecretKey: secretKey, // 临时存储明文密钥，供用户查看
        encryptedSecretKey: encryptedSecret, // 永久存储加密密钥
        keyStatus: ApiKeyStatus.ACTIVE,
        isSecretViewed: false,
      });

      // 保存到数据库
      const savedApiKey = await this.repository.save(newApiKey);

      // 同步到Redis缓存
      await this.syncApiKeyToCache(savedApiKey);

      return this.transformToResponseDtoInternal(savedApiKey, true);
    } catch (error) {
      this.logger.error(`创建API密钥失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 重新生成API密钥
   */
  async regenerateApiKey(dto: RegenerateApiKeyDto): Promise<ApiKeyResponseDto> {
    try {
      const apiKey = await this.repository.findOne({
        where: { id: dto.id },
        relations: ['user', 'service']
      });

      if (!apiKey) {
        throw new NotFoundException(`API密钥 #${dto.id} 不存在`);
      }

      // 清除旧的缓存
      await this.clearApiKeyCache(apiKey.apiKey);

      // 生成新的密钥对
      const { apiKey: newApiKey, secretKey: newSecretKey, secretHash } = await this.generateApiKeyPair();

      // 设置默认过期时间为一年后
      const defaultExpiresAt = new Date();
      defaultExpiresAt.setDate(defaultExpiresAt.getDate() + DEFAULT_API_KEY_VALIDITY_PERIOD);

      // 更新数据库记录
      apiKey.secretHash = secretHash;
      apiKey.apiKey = newApiKey;
      apiKey.keyStatus = ApiKeyStatus.ACTIVE;
      apiKey.isSecretViewed = false;
      apiKey.tempSecretKey = newSecretKey; // 存储明文secretKey到数据库
      apiKey.updatedAt = new Date();
      
      // 如果提供了过期时间，则使用提供的过期时间，否则使用默认的一年有效期
      apiKey.expiresAt = dto.expiresAt || defaultExpiresAt;

      const updatedApiKey = await this.repository.save(apiKey);

      // 同步到Redis缓存
      await this.syncApiKeyToCache(updatedApiKey);

      // 返回包含新密钥的响应
      const response = this.transformToResponseDtoInternal(updatedApiKey, true);
      response.apiKey = newApiKey;
      response.secretKey = newSecretKey;

      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 根据API密钥查找API密钥实体
   * @param apiKey API密钥
   * @returns API密钥实体
   */
  async findByApiKey(apiKey: string): Promise<ApiKeyEntity | null> {
    try {
      const apiKeyEntity = await this.repository.findOne({
        where: { apiKey }
      });
      
      if (apiKeyEntity) {
        return apiKeyEntity;
      }
      
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 更新API密钥最后使用时间
   * @param id API密钥ID
   * @param lastUsedIp 最后使用IP
   */
  async updateLastUsed(id: number, lastUsedIp: string): Promise<void> {
    try {
      await this.repository.update(id, {
        lastUsedAt: new Date(),
        lastUsedIp,
        updatedAt: new Date(),
      });

      // 清除缓存
      const apiKey = await this.repository.findOne({ where: { id } });
      if (apiKey) {
        await this.clearApiKeyCache(apiKey.apiKey);
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取用户的API密钥列表（包含未查看过的secretKey）
   */
  async getUserApiKeysWithSecret(userId: number): Promise<ApiKeyResponseDto[]> {
    const apiKeys = await this.repository.find({
      where: { userId },
      relations: ['service'],
      order: { createdAt: 'DESC' }
    });

    const result: ApiKeyResponseDto[] = [];
    for (const apiKey of apiKeys) {
      let secretKey: string | undefined = undefined;
      if (!apiKey.isSecretViewed) {
        // 从数据库获取临时密钥
        secretKey = apiKey.tempSecretKey ?? undefined;
      }
      const dto = this.transformToResponseDtoInternal(apiKey, true);
      dto.apiKey = apiKey.apiKey;
      dto.secretKey = secretKey;
      result.push(dto);
    }
    return result;
  }

  /**
   * 获取用户的API密钥列表（不包含secretKey，保持向后兼容）
   */
  async getUserApiKeys(userId: number): Promise<ApiKeyResponseDto[]> {
    const apiKeys = await this.repository.find({
      where: { userId },
      relations: ['service'],
      order: { createdAt: 'DESC' }
    });

    return apiKeys.map(apiKey => this.transformToResponseDtoInternal(apiKey, false));
  }

  /**
   * 获取API密钥统计信息
   */
  async getStats(userId?: number): Promise<any> {
    // 构建查询条件
    const where: any = {};
    if (userId) {
      where.userId = userId;
    }

    // 统计总数
    const total = await this.repository.count({ where });

    // 统计各状态数量
    const active = await this.repository.count({
      where: { ...where, keyStatus: ApiKeyStatus.ACTIVE },
    });

    const expired = await this.repository.count({
      where: { ...where, keyStatus: ApiKeyStatus.EXPIRED },
    });

    const revoked = await this.repository.count({
      where: { ...where, keyStatus: ApiKeyStatus.REVOKED },
    });

    // 按类型统计
    const typeStats = await this.repository
      .createQueryBuilder('api_key')
      .select('api_key.keyType', 'keyType')
      .addSelect('COUNT(*)', 'count')
      .where(where)
      .groupBy('api_key.keyType')
      .getRawMany();

    const byType = typeStats.reduce((acc, item) => {
      acc[item.keyType || 'Unknown'] = parseInt(item.count);
      return acc;
    }, {});

    // 按服务统计
    const serviceStats = await this.repository
      .createQueryBuilder('api_key')
      .leftJoin('api_key.service', 'service')
      .where(where)
      .select('service.name', 'serviceName')
      .addSelect('COUNT(*)', 'count')
      .groupBy('service.name')
      .getRawMany();

    const byService = serviceStats.reduce((acc, item) => {
      acc[item.serviceName || 'Unknown'] = parseInt(item.count);
      return acc;
    }, {});

    return {
      totalCount: total,  // 修改为与ApiKeyStatsDto一致的属性名
      activeCount: active,
      expiredCount: expired,
      revokedCount: revoked,
      byType,
      byService
    };
  }

  /**
   * 生成API密钥对
   */
  private async generateApiKeyPair(): Promise<{
    apiKey: string;
    secretKey: string;
    secretHash: string;
    encryptedSecret: string; // 新增加密密钥
  }> {
    // 生成API密钥 (ak_开头)
    const apiKeyId = GenerateUUID();
    const apiKey = `ak_${apiKeyId}`;

    // 生成秘钥 (sk_开头)
    const secretKeyId = GenerateUUID();
    const secretKey = `sk_${secretKeyId}`;

    // 只对秘钥进行哈希处理，API密钥保持明文
    const secretHash = await this.hashSecret(secretKey);
    const encryptedSecret = this.encryptSecretKey(secretKey);

    return {
      apiKey,
      secretKey,
      secretHash,
      encryptedSecret
    };
  }

  /**
   * 对API密钥进行哈希处理
   */
  private async hashApiKey(apiKey: string): Promise<string> {
    const salt = await bcrypt.genSalt(10);
    return await bcrypt.hash(apiKey, salt);
  }

  /**
   * 对秘钥进行哈希处理
   */
  private async hashSecret(secret: string): Promise<string> {
    const salt = await bcrypt.genSalt(10);
    return await bcrypt.hash(secret, salt);
  }

  /**
   * 验证API密钥是否匹配
   */
  private async verifyApiKey(apiKey: string, hash: string): Promise<boolean> {
    return await bcrypt.compare(apiKey, hash);
  }

  /**
   * 验证秘钥是否匹配
   */
  private async verifySecret(secret: string, hash: string): Promise<boolean> {
    return await bcrypt.compare(secret, hash);
  }

  /**
   * 公开验证秘钥方法，供守卫使用
   */
  async verifySecretKey(secret: string, hash: string): Promise<boolean> {
    try {
      console.log(`[验证Secret Key] 尝试验证密钥: ${secret.substring(0, 5)}... 与哈希: ${hash.substring(0, 20)}...`);
      const result = await bcrypt.compare(secret, hash);
      console.log(`[验证Secret Key] 结果: ${result ? '成功' : '失败'}`);
      return result;
    } catch (error) {
      console.error(`[验证Secret Key] 错误: ${error.message}`);
      return false;
    }
  }

  /**
   * 清除API密钥缓存
   */
  private async clearApiKeyCache(apiKey: string): Promise<void> {
    try {
      await this.redisService.del(`api_key:${apiKey}`);
    } catch (error) {
      throw error;
    }
  }

  /**
  * 转换实体为响应DTO（内部方法）
  */
  private transformToResponseDtoInternal(entity: ApiKeyEntity, includeSecrets = false): ApiKeyResponseDto {
    const response: ApiKeyResponseDto = {
      id: entity.id,
      userId: entity.userId,
      serviceId: entity.serviceId,
      name: entity.name,
      keyStatus: entity.keyStatus,
      keyType: entity.keyType,
      description: entity.description,
      permissions: entity.permissions,
      expiresAt: entity.expiresAt,
      lastUsedAt: entity.lastUsedAt,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      isSecretViewed: entity.isSecretViewed
    };

    // 添加关联信息
    if (entity.user) {
      response.user = {
        id: entity.user.id,
        username: entity.user.username,
        email: entity.user.email
      };
    }

    if (entity.service) {
      response.service = {
        id: entity.service.id,
        code: entity.service.code,
        name: entity.service.name,
        type: entity.service.type
      };
    }

    return response;
  }

  /**
   * 撤销API密钥
   */
  async revokeApiKey(id: number): Promise<void> {
    try {
      const apiKey = await this.repository.findOne({ where: { id } });
      
      if (!apiKey) {
        throw new NotFoundException(`API密钥 #${id} 不存在`);
      }
      
      apiKey.keyStatus = ApiKeyStatus.REVOKED;
      apiKey.updatedAt = new Date();
      
      await this.repository.save(apiKey);
      
      // 清除缓存
      await this.clearApiKeyCache(apiKey.apiKey);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 激活API密钥
   */
  async activateApiKey(id: number): Promise<void> {
    try {
      const apiKey = await this.repository.findOne({ where: { id }, relations: ['user', 'service'] });
      
      if (!apiKey) {
        throw new NotFoundException(`API密钥 #${id} 不存在`);
      }
      
      apiKey.keyStatus = ApiKeyStatus.ACTIVE;
      apiKey.updatedAt = new Date();
      
      const updatedApiKey = await this.repository.save(apiKey);
      
      // 同步到缓存
      await this.syncApiKeyToCache(updatedApiKey);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 标记密钥已被查看
   */
  async markSecretAsViewed(id: number): Promise<void> {
    try {
      // 标记密钥为已查看，清除明文密钥，但保留加密存储
      const apiKey = await this.repository.findOne({
        where: { id }
      });
    
      if (apiKey) {
        apiKey.tempSecretKey = ''; // 清除临时明文密钥
    apiKey.isSecretViewed = true;
    await this.repository.save(apiKey);
        this.logger.debug(`API密钥 #${id} 已标记为已查看，临时明文密钥已清除`);
      }
    } catch (error) {
      this.logger.error(`标记密钥已查看失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 批量删除API密钥
   */
  async batchRemove(ids: number[]): Promise<void> {
    const apiKeys = await this.repository.find({
      where: { id: In(ids) }
    });

    if (apiKeys.length === 0) {
      throw new NotFoundException('未找到要删除的API密钥');
    }

    // 软删除
    await this.repository.softDelete(ids);

    // 清除缓存
    for (const apiKey of apiKeys) {
      await this.clearApiKeyCache(apiKey.apiKey);
    }
  }

  /**
   * 加载所有活跃的API密钥到Redis缓存
   * 通常在应用启动时调用，确保认证系统能正常工作
   */
  async loadApiKeysToCache(): Promise<number> {
    try {
      // 查询所有活跃的API密钥
      const activeApiKeys = await this.repository.find({
        where: { 
          keyStatus: ApiKeyStatus.ACTIVE,
        },
        relations: ['user', 'service']
      });
      
      if (activeApiKeys.length === 0) {
        return 0;
      }
      
      console.log('📋 找到', activeApiKeys.length, '个活跃的API密钥');
      
      // 批量加载到缓存
      let cachedCount = 0;
      for (const apiKey of activeApiKeys) {
        try {
          console.log(`🔄 处理API密钥 #${apiKey.id}, API密钥: ${apiKey.apiKey.substring(0, 10)}...`);
          await this.syncApiKeyToCache(apiKey);
          console.log(`✅ API密钥 #${apiKey.id} 已缓存, 缓存键: api_key:${apiKey.apiKey}`);
          
          // 验证缓存
          const cacheKey = `api_key:${apiKey.apiKey}`;
          const cachedData = await this.redisService.get(cacheKey);
          if (cachedData) {
            const cachedApiKey = JSON.parse(cachedData);
            console.log(`✓ 缓存验证成功: ${cacheKey}, keyStatus=${cachedApiKey.keyStatus}`);
          } else {
            console.log(`❌ 缓存验证失败: ${cacheKey}`);
          }
          
          cachedCount++;
        } catch (error) {
          console.error(`❌ 缓存API密钥 #${apiKey.id} 失败:`, error.message);
        }
      }
      
      console.log(`✅ 成功加载 ${cachedCount}/${activeApiKeys.length} 个API密钥到缓存,${activeApiKeys}`);
      return cachedCount;
    } catch (error) {
      console.error('❌ 加载API密钥到缓存失败:', error.message);
      throw error;
    }
  }
  
  /**
   * 加载单个API密钥到Redis缓存
   * @param id API密钥ID
   */
  async loadApiKeyToCache(id: number): Promise<boolean> {
    try {
      const apiKey = await this.repository.findOne({
        where: { id },
        relations: ['user', 'service']
      });
      
      if (!apiKey) {
        throw new NotFoundException(`API密钥 #${id} 不存在`);
      }
      
      await this.syncApiKeyToCache(apiKey);
      return true;
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * 同步API密钥到Redis缓存
   * @param apiKey API密钥实体
   */
  async syncApiKeyToCache(apiKey: ApiKeyEntity): Promise<void> {
    try {
      const cacheKey = `api_key:${apiKey.apiKey}`;
      const cacheData = {
        id: apiKey.id,
        userId: apiKey.userId,
        serviceId: apiKey.serviceId,
        name: apiKey.name,
        keyStatus: apiKey.keyStatus,
        keyType: apiKey.keyType,
        description: apiKey.description,
        permissions: apiKey.permissions,
        expiresAt: apiKey.expiresAt,
        lastUsedAt: apiKey.lastUsedAt,
        createdAt: apiKey.createdAt,
        updatedAt: apiKey.updatedAt,
      };
      
      // 缓存24小时
      await this.redisService.setex(cacheKey, 86400, JSON.stringify(cacheData));
      await this.redisService.setex(`api_key_id:${apiKey.id}`, 86400, apiKey.apiKey);
      
      // 同步Secret Key到Redis - 优先级: tempSecretKey > 解密的encryptedSecretKey
      let secretKeyValue = apiKey.tempSecretKey;
      
      // 如果没有临时密钥，尝试解密永久存储的密钥
      if (!secretKeyValue && apiKey.encryptedSecretKey) {
        secretKeyValue = this.decryptSecretKey(apiKey.encryptedSecretKey);
        if (secretKeyValue) {
          this.logger.debug(`从加密存储中解密API密钥 #${apiKey.id} 的Secret Key`);
        }
      }
      
      // 如果仍然没有密钥，尝试从数据库获取完整实体
      if (!secretKeyValue) {
        const fullEntity = await this.repository.findOne({
          where: { id: apiKey.id }
        });
        
        // 尝试从临时密钥获取
        if (fullEntity?.tempSecretKey) {
          secretKeyValue = fullEntity.tempSecretKey;
          this.logger.debug(`从数据库临时密钥获取到API密钥 #${apiKey.id} 的Secret Key`);
        } 
        // 尝试从加密密钥解密
        else if (fullEntity?.encryptedSecretKey) {
          secretKeyValue = this.decryptSecretKey(fullEntity.encryptedSecretKey);
          if (secretKeyValue) {
            this.logger.debug(`从数据库加密存储中解密API密钥 #${apiKey.id} 的Secret Key`);
          }
        }
      }
      
      if (secretKeyValue) {
        const secretKey = `api_key_secret:${apiKey.id}`;
        await this.redisService.setex(secretKey, 86400, secretKeyValue);
        this.logger.debug(`已同步API密钥 #${apiKey.id} 的Secret Key到Redis`);
      } else {
        this.logger.warn(`API密钥 #${apiKey.id} 没有可用的Secret Key同步到Redis`);
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * 更新API密钥
   * @param id API密钥ID
   * @param newApiKey 新的API密钥
   */
  async updateApiKey(id: number, newApiKey: string): Promise<void> {
    try {
      const apiKey = await this.repository.findOne({
        where: { id },
        relations: ['user', 'service']
      });
      
      if (!apiKey) {
        throw new NotFoundException(`API密钥 #${id} 不存在`);
      }
      
      // 先清除旧的缓存
      await this.clearApiKeyCache(apiKey.apiKey);
      
      // 更新API密钥
      apiKey.apiKey = newApiKey;
      apiKey.updatedAt = new Date();
      
      // 保存到数据库
      await this.repository.save(apiKey);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取API密钥使用统计
   * @param userId 用户ID
   */
  async getApiKeyStats(userId: number): Promise<ApiKeyStatsDto> {
    try {
      // 查询用户的API密钥数量
      const totalCount = await this.repository.count({
        where: { userId }
      });
      
      // 查询活跃的API密钥数量
      const activeCount = await this.repository.count({
        where: { userId, keyStatus: ApiKeyStatus.ACTIVE }
      });
      
      // 查询过期的API密钥数量
      const expiredCount = await this.repository.count({
        where: { userId, keyStatus: ApiKeyStatus.EXPIRED }
      });
      
      // 查询已撤销的API密钥数量
      const revokedCount = await this.repository.count({
        where: { userId, keyStatus: ApiKeyStatus.REVOKED }
      });
      
      return {
        userId,
        totalCount,
        activeCount,
        expiredCount,
        revokedCount
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * 根据ID查找完整的API密钥实体
   * @param id API密钥ID
   * @returns 完整的API密钥实体，包含secretHash
   */
  async findEntityById(id: number): Promise<ApiKeyEntity | null> {
    try {
      return await this.repository.findOne({
        where: { id }
      });
    } catch (error) {
      return null;
    }
  }

  /**
   * 为指定用户手动创建API密钥（用于调试和修复）
   */
  async createApiKeyForUser(userId: number, keyName: string = '用户主密钥'): Promise<ApiKeyResponseDto> {
    this.logger.debug(`手动为用户 #${userId} 创建API密钥: ${keyName}`);
    
    // 验证用户是否存在
    const user = await this.userService.findById(userId);
    if (!user) {
      throw new NotFoundException(`用户 #${userId} 不存在`);
    }
    
    // 创建API密钥DTO
    const createDto: CreateApiKeyDto = {
      userId,
      serviceId: 0, // 平台级密钥
      name: keyName,
      keyType: 'master', // 主密钥
      permissions: ['*'], // 所有权限
      description: '系统自动创建的主密钥',
    };
    
    // 创建API密钥
    const result = await this.createApiKey(createDto);
    this.logger.log(`成功为用户 #${userId} 创建API密钥: ${result.id}`);
    
    return result;
  }

  /**
   * 删除用户的所有API密钥
   * 当用户被删除时调用此方法
   */
  async deleteUserApiKeys(userId: number): Promise<void> {
    this.logger.log(`开始删除用户 #${userId} 的所有API密钥`);
    
    try {
      // 查找用户的所有API密钥
      const apiKeys = await this.repository.find({
        where: { userId: userId },
      });
      
      if (apiKeys.length === 0) {
        this.logger.log(`用户 #${userId} 没有API密钥需要删除`);
        return;
      }
      
      this.logger.log(`找到用户 #${userId} 的 ${apiKeys.length} 个API密钥`);
      
      // 先从缓存中删除
      for (const apiKey of apiKeys) {
        try {
          if (apiKey.apiKey) {
            // 删除API密钥的缓存
            await this.redisService.del(`api_key:${apiKey.apiKey}`);
            this.logger.debug(`已从缓存中删除API密钥: ${apiKey.apiKey}`);
          }
        } catch (error) {
          this.logger.error(`从缓存中删除API密钥 ${apiKey.apiKey} 失败: ${error.message}`);
        }
      }
      
      // 从数据库中删除
      await this.repository.remove(apiKeys);
      
      this.logger.log(`成功删除用户 #${userId} 的 ${apiKeys.length} 个API密钥`);
    } catch (error) {
      this.logger.error(`删除用户 #${userId} 的API密钥失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取API密钥对应的Secret Key
   * 用于签名认证，根据API密钥ID获取对应的Secret Key
   * @param apiKeyId API密钥ID
   * @returns Secret Key或null
   */
  async getSecretKeyForApiKey(apiKeyId: number): Promise<string | null> {
    try {
      const apiKeyEntity = await this.repository.findOne({
        where: { id: apiKeyId }
      });
      
      if (!apiKeyEntity) {
        this.logger.warn(`API密钥实体不存在: ${apiKeyId}`);
        return null;
      }
      
      // 如果存储了临时明文密钥（通常是刚创建的密钥）
      if (apiKeyEntity.tempSecretKey) {
        this.logger.debug(`使用临时明文密钥: ${apiKeyEntity.id}`);
        return apiKeyEntity.tempSecretKey;
      }
      
      // 尝试从密钥管理服务获取解密后的密钥
      try {
        const secretKey = await this.keyManagementService.getDecryptedSecretKey(apiKeyId);
        if (secretKey) {
          this.logger.debug(`从密钥管理服务获取到解密密钥: ${apiKeyId}`);
          return secretKey;
        }
      } catch (error) {
        this.logger.error(`从密钥管理服务获取密钥失败: ${error.message}`);
      }
      
      // 如果无法获取明文密钥，返回null
      // 这种情况下需要用户重新生成密钥
      this.logger.warn(`无法获取API密钥的Secret Key: ${apiKeyId}`);
      return null;
    } catch (error) {
      this.logger.error(`获取Secret Key失败: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 重新同步所有API密钥的Secret Key到Redis
   * 用于修复Redis中缺失Secret Key的问题
   */
  async resyncAllSecretKeysToRedis(): Promise<number> {
    try {
      this.logger.log('开始重新同步所有API密钥的Secret Key到Redis');
      
      // 查询所有活跃的API密钥
      const apiKeys = await this.repository.find({
        where: { keyStatus: ApiKeyStatus.ACTIVE }
      });
      
      this.logger.log(`找到 ${apiKeys.length} 个活跃API密钥`);
      
      let syncedCount = 0;
      
      // 遍历所有API密钥，同步Secret Key到Redis
      for (const apiKey of apiKeys) {
        try {
          // 尝试按优先级获取密钥：临时密钥 > 加密密钥
          let secretKeyValue = apiKey.tempSecretKey;
          
          // 如果没有临时密钥，尝试解密
          if (!secretKeyValue && apiKey.encryptedSecretKey) {
            secretKeyValue = this.decryptSecretKey(apiKey.encryptedSecretKey);
          }
          
          if (secretKeyValue) {
            const secretKey = `api_key_secret:${apiKey.id}`;
            await this.redisService.setex(secretKey, 86400, secretKeyValue);
            syncedCount++;
            this.logger.debug(`已同步API密钥 #${apiKey.id} (${apiKey.apiKey}) 的Secret Key到Redis`);
          } else {
            this.logger.warn(`API密钥 #${apiKey.id} (${apiKey.apiKey}) 没有可用的Secret Key`);
          }
        } catch (error) {
          this.logger.error(`同步API密钥 #${apiKey.id} 的Secret Key失败: ${error.message}`);
        }
      }
      
      this.logger.log(`成功同步 ${syncedCount}/${apiKeys.length} 个API密钥的Secret Key到Redis`);
      return syncedCount;
    } catch (error) {
      this.logger.error(`重新同步API密钥Secret Key失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 加密 Secret Key 用于永久存储
   * 使用系统密钥加密而非哈希，以便将来能够解密
   * @param secretKey 明文密钥
   * @returns 加密后的密钥
   */
  private encryptSecretKey(secretKey: string): string {
    try {
      const systemEncryptionKey = this.configService.get<string>('APP_ENCRYPTION_KEY') || 'default-system-encryption-key';
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(systemEncryptionKey.slice(0, 32).padEnd(32, '0')), iv);
      let encrypted = cipher.update(secretKey, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      return `${iv.toString('hex')}:${encrypted}`;
    } catch (error) {
      this.logger.error(`加密Secret Key失败: ${error.message}`);
      return '';
    }
  }

  /**
   * 解密 Secret Key
   * @param encryptedKey 加密后的密钥
   * @returns 解密后的明文密钥
   */
  private decryptSecretKey(encryptedKey: string): string {
    try {
      const systemEncryptionKey = this.configService.get<string>('APP_ENCRYPTION_KEY') || 'default-system-encryption-key';
      const [ivHex, encryptedText] = encryptedKey.split(':');
      if (!ivHex || !encryptedText) return '';
      
      const iv = Buffer.from(ivHex, 'hex');
      const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(systemEncryptionKey.slice(0, 32).padEnd(32, '0')), iv);
      let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch (error) {
      this.logger.error(`解密Secret Key失败: ${error.message}`);
      return '';
    }
  }
}
