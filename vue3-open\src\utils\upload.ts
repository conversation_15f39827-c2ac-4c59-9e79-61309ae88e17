import { ElMessage } from 'element-plus'
import type { UploadFile, UploadRawFile } from 'element-plus'

// 文件类型配置
export interface FileTypeConfig {
  accept: string
  maxSize: number // MB
  description: string
}

// 预定义的文件类型
export const FILE_TYPES: Record<string, FileTypeConfig> = {
  image: {
    accept: '.jpg,.jpeg,.png,.gif,.bmp,.webp',
    maxSize: 10,
    description: '图片文件'
  },
  document: {
    accept: '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt',
    maxSize: 50,
    description: '文档文件'
  },
  video: {
    accept: '.mp4,.avi,.mov,.wmv,.flv,.mkv',
    maxSize: 500,
    description: '视频文件'
  },
  audio: {
    accept: '.mp3,.wav,.flac,.aac,.ogg',
    maxSize: 100,
    description: '音频文件'
  },
  archive: {
    accept: '.zip,.rar,.7z,.tar,.gz',
    maxSize: 100,
    description: '压缩文件'
  },
  all: {
    accept: '*',
    maxSize: 100,
    description: '所有文件'
  }
}

// 文件大小格式化
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取文件扩展名
export function getFileExtension(filename: string): string {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2).toLowerCase()
}

// 获取文件类型
export function getFileType(filename: string): string {
  const ext = getFileExtension(filename)
  
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
  const documentExts = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']
  const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv']
  const audioExts = ['mp3', 'wav', 'flac', 'aac', 'ogg']
  const archiveExts = ['zip', 'rar', '7z', 'tar', 'gz']
  
  if (imageExts.includes(ext)) return 'image'
  if (documentExts.includes(ext)) return 'document'
  if (videoExts.includes(ext)) return 'video'
  if (audioExts.includes(ext)) return 'audio'
  if (archiveExts.includes(ext)) return 'archive'
  
  return 'unknown'
}

// 验证文件类型
export function validateFileType(file: File, allowedTypes: string[]): boolean {
  const fileType = getFileType(file.name)
  return allowedTypes.includes(fileType) || allowedTypes.includes('all')
}

// 验证文件大小
export function validateFileSize(file: File, maxSizeMB: number): boolean {
  const maxSizeBytes = maxSizeMB * 1024 * 1024
  return file.size <= maxSizeBytes
}

// 文件上传前验证
export function beforeUploadValidate(
  file: UploadRawFile,
  options: {
    allowedTypes?: string[]
    maxSize?: number // MB
    customValidator?: (file: File) => boolean | string
  } = {}
): boolean {
  const { allowedTypes = ['all'], maxSize = 100, customValidator } = options
  
  // 文件类型验证
  if (!validateFileType(file, allowedTypes)) {
    const typeNames = allowedTypes.map(type => FILE_TYPES[type]?.description || type).join('、')
    ElMessage.error(`只能上传 ${typeNames} 格式的文件`)
    return false
  }
  
  // 文件大小验证
  if (!validateFileSize(file, maxSize)) {
    ElMessage.error(`文件大小不能超过 ${maxSize}MB`)
    return false
  }
  
  // 自定义验证
  if (customValidator) {
    const result = customValidator(file)
    if (typeof result === 'string') {
      ElMessage.error(result)
      return false
    }
    if (!result) {
      return false
    }
  }
  
  return true
}

// 图片文件预览
export function createImagePreview(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    if (!file.type.startsWith('image/')) {
      reject(new Error('不是图片文件'))
      return
    }
    
    const reader = new FileReader()
    reader.onload = (e) => {
      resolve(e.target?.result as string)
    }
    reader.onerror = () => {
      reject(new Error('读取文件失败'))
    }
    reader.readAsDataURL(file)
  })
}

// 文件转换为Base64
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result as string
      // 移除data:image/jpeg;base64,前缀
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = () => {
      reject(new Error('文件转换失败'))
    }
    reader.readAsDataURL(file)
  })
}

// 压缩图片
export function compressImage(
  file: File,
  options: {
    maxWidth?: number
    maxHeight?: number
    quality?: number // 0-1
    outputFormat?: string
  } = {}
): Promise<File> {
  return new Promise((resolve, reject) => {
    const {
      maxWidth = 1920,
      maxHeight = 1080,
      quality = 0.8,
      outputFormat = 'image/jpeg'
    } = options
    
    if (!file.type.startsWith('image/')) {
      reject(new Error('不是图片文件'))
      return
    }
    
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    
    img.onload = () => {
      // 计算压缩后的尺寸
      let { width, height } = img
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width
        width = maxWidth
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height
        height = maxHeight
      }
      
      canvas.width = width
      canvas.height = height
      
      // 绘制压缩后的图片
      ctx?.drawImage(img, 0, 0, width, height)
      
      // 转换为Blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: outputFormat,
              lastModified: Date.now()
            })
            resolve(compressedFile)
          } else {
            reject(new Error('图片压缩失败'))
          }
        },
        outputFormat,
        quality
      )
    }
    
    img.onerror = () => {
      reject(new Error('图片加载失败'))
    }
    
    img.src = URL.createObjectURL(file)
  })
}

// 批量上传文件
export async function batchUpload(
  files: File[],
  uploadFn: (file: File) => Promise<any>,
  options: {
    concurrency?: number // 并发数
    onProgress?: (completed: number, total: number) => void
    onError?: (error: Error, file: File) => void
  } = {}
): Promise<any[]> {
  const { concurrency = 3, onProgress, onError } = options
  const results: any[] = []
  let completed = 0
  
  // 分批处理
  for (let i = 0; i < files.length; i += concurrency) {
    const batch = files.slice(i, i + concurrency)
    
    const batchPromises = batch.map(async (file) => {
      try {
        const result = await uploadFn(file)
        completed++
        onProgress?.(completed, files.length)
        return result
      } catch (error) {
        completed++
        onProgress?.(completed, files.length)
        onError?.(error as Error, file)
        throw error
      }
    })
    
    const batchResults = await Promise.allSettled(batchPromises)
    results.push(...batchResults)
  }
  
  return results
}

// 获取文件图标
export function getFileIcon(filename: string): string {
  const type = getFileType(filename)
  
  const iconMap: Record<string, string> = {
    image: 'Picture',
    document: 'Document',
    video: 'VideoPlay',
    audio: 'AudioFilled',
    archive: 'FolderOpened',
    unknown: 'Document'
  }
  
  return iconMap[type] || iconMap.unknown
}

// 上传状态文本
export function getUploadStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    ready: '准备上传',
    uploading: '上传中',
    success: '上传成功',
    error: '上传失败'
  }
  
  return statusMap[status] || '未知状态'
}

// 创建上传任务
export interface UploadTask {
  id: string
  file: File
  status: 'pending' | 'uploading' | 'success' | 'error'
  progress: number
  result?: any
  error?: Error
}

export class UploadManager {
  private tasks: Map<string, UploadTask> = new Map()
  private concurrency: number
  private running: number = 0
  
  constructor(concurrency: number = 3) {
    this.concurrency = concurrency
  }
  
  // 添加上传任务
  addTask(file: File): string {
    const id = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const task: UploadTask = {
      id,
      file,
      status: 'pending',
      progress: 0
    }
    
    this.tasks.set(id, task)
    return id
  }
  
  // 开始上传
  async startUpload(
    uploadFn: (file: File, onProgress: (progress: number) => void) => Promise<any>
  ): Promise<void> {
    const pendingTasks = Array.from(this.tasks.values()).filter(
      task => task.status === 'pending'
    )
    
    for (const task of pendingTasks) {
      if (this.running >= this.concurrency) {
        await this.waitForSlot()
      }
      
      this.runTask(task, uploadFn)
    }
  }
  
  // 运行单个任务
  private async runTask(
    task: UploadTask,
    uploadFn: (file: File, onProgress: (progress: number) => void) => Promise<any>
  ): Promise<void> {
    this.running++
    task.status = 'uploading'
    
    try {
      const result = await uploadFn(task.file, (progress) => {
        task.progress = progress
      })
      
      task.status = 'success'
      task.result = result
      task.progress = 100
    } catch (error) {
      task.status = 'error'
      task.error = error as Error
    } finally {
      this.running--
    }
  }
  
  // 等待空闲槽位
  private async waitForSlot(): Promise<void> {
    return new Promise((resolve) => {
      const check = () => {
        if (this.running < this.concurrency) {
          resolve()
        } else {
          setTimeout(check, 100)
        }
      }
      check()
    })
  }
  
  // 获取任务状态
  getTask(id: string): UploadTask | undefined {
    return this.tasks.get(id)
  }
  
  // 获取所有任务
  getAllTasks(): UploadTask[] {
    return Array.from(this.tasks.values())
  }
  
  // 清除已完成的任务
  clearCompletedTasks(): void {
    for (const [id, task] of this.tasks.entries()) {
      if (task.status === 'success' || task.status === 'error') {
        this.tasks.delete(id)
      }
    }
  }
  
  // 清除所有任务
  clearAllTasks(): void {
    this.tasks.clear()
  }
}