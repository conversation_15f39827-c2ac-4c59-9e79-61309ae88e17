import { Modu<PERSON> } from '@nestjs/common';
import { ScheduleModule as NestScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleService } from './schedule.service';
import { ScheduleController } from './schedule.controller';
import { TaskHistoryService } from './services/task-history.service';
import { TaskExecution } from './entities/task-execution.entity';
import { CallRecordModule } from '../call-record/call-record.module';
import { UserModule } from '../user/user.module';
import { ApiKeyModule } from '../api-key/api-key.module';
import { UserServiceModule } from '../user-service/user-service.module';

@Module({
  imports: [
    NestScheduleModule.forRoot(),
    TypeOrmModule.forFeature([TaskExecution]),
    CallRecordModule,
    UserModule,
    ApiKeyModule,
    UserServiceModule,
  ],
  providers: [ScheduleService, TaskHistoryService],
  controllers: [ScheduleController],
  exports: [ScheduleService, TaskHistoryService],
})
export class ScheduleModule {}
