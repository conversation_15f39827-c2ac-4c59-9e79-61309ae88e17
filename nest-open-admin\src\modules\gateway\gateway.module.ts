import { Module, forwardRef } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { GatewayService } from './gateway.service';
import { CallRecordModule } from '../call-record/call-record.module';
import { ApiKeyModule } from '../api-key/api-key.module';
import { ServiceModule } from '../service/service.module';
import { ProcessingModeDecider } from './services/processing-mode-decider.service';
import { ExternalServiceClient } from './services/external-service-client.service';
import { ImprovedGatewayProxyService } from './services/improved-gateway-proxy.service';
import { UnifiedGatewayController } from './controllers/unified-gateway.controller';
import { MulterModule } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { existsSync, mkdirSync } from 'fs';
import { QueueModule } from '../queue/queue.module';
import LogisticsParser from '../ocr/logisticsParser';
import { OcrModule } from '../ocr/ocr.module';

/**
 * 网关模块
 * 统一管理API网关功能，包括请求代理、负载均衡、监控、限流等
 * 提供统一的API入口，支持多种服务的代理和路由
 */
@Module({
  imports: [
    ConfigModule,
    ScheduleModule.forRoot(),
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 3,
      validateStatus: (status) => status < 500, // 允许4xx状态码通过
    }),
    MulterModule.register({
      storage: diskStorage({
        destination: (req, file, cb) => {
          const uploadPath = './uploads/temp';
          if (!existsSync(uploadPath)) {
            mkdirSync(uploadPath, { recursive: true });
          }
          cb(null, uploadPath);
        },
        filename: (req, file, cb) => {
          const randomName = Array(32)
            .fill(null)
            .map(() => Math.round(Math.random() * 16).toString(16))
            .join('');
          cb(null, `${randomName}${extname(file.originalname)}`);
        },
      }),
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
    forwardRef(() => CallRecordModule),
    forwardRef(() => ApiKeyModule),
    forwardRef(() => ServiceModule),
    forwardRef(() => QueueModule),
    forwardRef(() => OcrModule),
  ],
  controllers: [
    UnifiedGatewayController,
  ],
  providers: [
    GatewayService,
    ProcessingModeDecider,
    ExternalServiceClient,
    ImprovedGatewayProxyService,
  ],
  exports: [
    GatewayService,
    ProcessingModeDecider,
    ExternalServiceClient,
    ImprovedGatewayProxyService,
  ],
})
export class GatewayModule {}