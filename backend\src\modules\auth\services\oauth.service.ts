import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
  ConflictException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import axios from 'axios';
import { UserOauthEntity } from '@/modules/user/entities/user-oauth.entity';
import { ThirdPartyType } from '@/modules/user/entities/user.entity';
import { UserService } from '@/modules/user/user.service';
import { AuthService } from './auth.service';
import { ApiKeyService } from '@/modules/api-key/services/api-key.service';
import { OAuthBindDto } from '../dto/oauth/oauth-bind.dto';
import { OAuthUnbindDto } from '../dto/oauth/oauth-unbind.dto';
import { WechatLoginDto } from '../dto/oauth/wechat-login.dto';
import { GoogleLoginDto } from '../dto/oauth/google-login.dto';
import { AlipayLoginDto } from '../dto/oauth/alipay-login.dto';
import { GithubLoginDto } from '../dto/oauth/github-login.dto';
import { CoreAuthService } from '@/shared/core-auth.service';

@Injectable()
export class OAuthService {
  private readonly logger = new Logger(OAuthService.name);

  constructor(
    @InjectRepository(UserOauthEntity)
    private readonly userOauthRepository: Repository<UserOauthEntity>,
    private readonly userService: UserService,
    private readonly authService: AuthService,
    private readonly apiKeyService: ApiKeyService,
    private readonly configService: ConfigService,
    private readonly coreAuthService: CoreAuthService,
  ) {}

  /**
   * 微信登录
   * @param dto 微信登录参数
   * @returns 登录结果
   */
  async wechatLogin(dto: WechatLoginDto): Promise<any> {
    try {
      const { code } = dto;

      // 获取微信应用配置
      const appId = this.configService.get<string>('auth.oauth.wechat.appId');
      const appSecret = this.configService.get<string>('auth.oauth.wechat.appSecret');

      if (!appId || !appSecret) {
        throw new BadRequestException('微信应用配置缺失');
      }

      // 通过code获取用户的access_token和openid
      const tokenResponse = await axios.get(
        `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${appId}&secret=${appSecret}&code=${code}&grant_type=authorization_code`,
      );

      if (tokenResponse.data.errcode) {
        this.logger.error(`微信授权失败: ${JSON.stringify(tokenResponse.data)}`);
        throw new BadRequestException(`微信授权失败: ${tokenResponse.data.errmsg}`);
      }

      const { access_token, refresh_token, openid, unionid, expires_in } = tokenResponse.data;

      // 获取用户信息
      const userInfoResponse = await axios.get(
        `https://api.weixin.qq.com/sns/userinfo?access_token=${access_token}&openid=${openid}&lang=zh_CN`,
      );

      if (userInfoResponse.data.errcode) {
        this.logger.error(`获取微信用户信息失败: ${JSON.stringify(userInfoResponse.data)}`);
        throw new BadRequestException(`获取微信用户信息失败: ${userInfoResponse.data.errmsg}`);
      }

      const { nickname, headimgurl } = userInfoResponse.data;

      // 查找是否存在已绑定该微信账号的用户
      let oauthRecord = await this.userOauthRepository.findOne({
        where: {
          providerType: ThirdPartyType.WECHAT,
          providerId: openid,
        },
        relations: ['user'],
      });

      // 如果已存在绑定记录，直接登录
      if (oauthRecord) {
        const user = oauthRecord.user;

        // 更新 OAuth 记录
        oauthRecord.providerUsername = nickname;
        oauthRecord.providerAvatar = headimgurl;
        oauthRecord.accessToken = access_token;
        oauthRecord.refreshToken = refresh_token;
        oauthRecord.tokenExpiresAt = new Date(Date.now() + expires_in * 1000);
        oauthRecord.lastLoginAt = new Date();
        oauthRecord.profileData = JSON.stringify(userInfoResponse.data);
        await this.userOauthRepository.save(oauthRecord);

        // 更新用户信息
        await this.userService.updateLastLogin(user.id, '127.0.0.1'); // TODO: 获取真实IP

        // 生成令牌
        const tokens = await this.authService.generateTokens(user);

        return {
          ...tokens,
          userId: user.id,
          username: user.username,
          nickname: user.nickname,
          email: user.email,
          phone: user.phone,
          avatar: user.avatar || headimgurl,
          isNewUser: false,
        };
      }

      // 不存在绑定记录，创建新用户并绑定
      // 生成随机用户名
      const username = `wx_${Date.now().toString(36)}_${Math.random().toString(36).substr(2, 5)}`;
      // 生成随机密码
      const password = Math.random().toString(36).substr(2, 10);

      // 创建新用户
      const newUser = await this.userService.create({
        username,
        password,
        nickname: nickname || username,
        avatar: headimgurl,
      });

      // 创建默认API密钥
      await this.apiKeyService.createApiKey(newUser.id, {
        name: '默认密钥',
      });

      // 创建 OAuth 绑定记录
      const newOAuthRecord = this.userOauthRepository.create({
        userId: newUser.id,
        providerType: ThirdPartyType.WECHAT,
        providerId: openid,
        providerUsername: nickname,
        providerAvatar: headimgurl,
        accessToken: access_token,
        refreshToken: refresh_token,
        tokenExpiresAt: new Date(Date.now() + expires_in * 1000),
        lastLoginAt: new Date(),
        profileData: JSON.stringify(userInfoResponse.data),
      });

      await this.userOauthRepository.save(newOAuthRecord);

      // 如果有unionid，更新用户实体中的wechatUnionid
      if (unionid) {
        await this.userService.updateThirdPartyInfo(newUser.id, { wechatUnionid: unionid, wechatOpenid: openid });
      } else {
        await this.userService.updateThirdPartyInfo(newUser.id, { wechatOpenid: openid });
      }

      // 生成令牌
      const tokens = await this.authService.generateTokens(newUser);

      return {
        ...tokens,
        userId: newUser.id,
        username: newUser.username,
        nickname: newUser.nickname,
        email: newUser.email,
        phone: newUser.phone,
        avatar: headimgurl,
        isNewUser: true,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`微信登录失败: ${error.message}`, error.stack);
      throw new InternalServerErrorException('微信登录失败，请稍后再试');
    }
  }

  /**
   * 绑定第三方账号
   * @param userId 用户ID
   * @param bindDto 绑定参数
   * @returns 绑定结果
   */
  async bindOAuthAccount(userId: number, bindDto: OAuthBindDto): Promise<{ message: string }> {
    try {
      const { providerType, code } = bindDto;

      // 验证用户是否存在
      const user = await this.userService.findById(userId);

      // 检查是否已经绑定过该类型的第三方账号
      const existingBind = await this.userOauthRepository.findOne({
        where: {
          userId,
          providerType,
        },
      });

      if (existingBind) {
        throw new ConflictException(`您已绑定过${this.getProviderName(providerType)}账号`);
      }

      // 根据不同的提供商处理不同的绑定逻辑
      switch (providerType) {
        case ThirdPartyType.WECHAT:
          return await this.bindWechat(user, code);
        case ThirdPartyType.ALIPAY:
          return await this.bindAlipay(user, code);
        case ThirdPartyType.GITHUB:
          return await this.bindGithub(user, code);
        case ThirdPartyType.GOOGLE:
          return await this.bindGoogle(user, code);
        default:
          throw new BadRequestException('不支持的第三方平台类型');
      }
    } catch (error) {
      if (error instanceof BadRequestException || error instanceof ConflictException) {
        throw error;
      }
      this.logger.error(`绑定第三方账号失败: ${error.message}`, error.stack);
      throw new InternalServerErrorException('绑定第三方账号失败，请稍后再试');
    }
  }

  /**
   * 解绑第三方账号
   * @param userId 用户ID
   * @param unbindDto 解绑参数
   * @returns 解绑结果
   */
  async unbindOAuthAccount(userId: number, unbindDto: OAuthUnbindDto): Promise<{ message: string }> {
    try {
      const { providerType } = unbindDto;

      // 验证用户是否存在
      await this.userService.findById(userId);

      // 检查是否绑定了该类型的第三方账号
      const existingBind = await this.userOauthRepository.findOne({
        where: {
          userId,
          providerType,
        },
      });

      if (!existingBind) {
        throw new NotFoundException(`您尚未绑定${this.getProviderName(providerType)}账号`);
      }

      // 删除绑定记录
      await this.userOauthRepository.remove(existingBind);

      // 清除用户实体中的第三方账号相关字段
      switch (providerType) {
          case ThirdPartyType.WECHAT:
            await this.userService.clearThirdPartyInfo(userId, ['wechatOpenid', 'wechatUnionid']);
            break;
          case ThirdPartyType.ALIPAY:
            await this.userService.clearThirdPartyInfo(userId, ['alipayUserId']);
            break;
          case ThirdPartyType.GITHUB:
            await this.userService.clearThirdPartyInfo(userId, ['githubId']);
            break;
          case ThirdPartyType.GOOGLE:
            await this.userService.clearThirdPartyInfo(userId, ['googleId']);
            break;
        }

      return { message: `成功解绑${this.getProviderName(providerType)}账号` };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`解绑第三方账号失败: ${error.message}`, error.stack);
      throw new InternalServerErrorException('解绑第三方账号失败，请稍后再试');
    }
  }

  /**
   * 获取用户绑定的第三方账号列表
   * @param userId 用户ID
   * @returns 绑定的第三方账号列表
   */
  async getUserOAuthAccounts(userId: number): Promise<any[]> {
    try {
      // 验证用户是否存在
      await this.userService.findById(userId);

      // 查询用户绑定的第三方账号
      const oauthAccounts = await this.userOauthRepository.find({
        where: { userId },
      });

      return oauthAccounts.map(account => ({
        providerType: account.providerType,
        providerName: this.getProviderName(account.providerType),
        providerUsername: account.providerUsername,
        providerAvatar: account.providerAvatar,
        boundAt: account.createdAt,
        lastLoginAt: account.lastLoginAt,
      }));
    } catch (error) {
      this.logger.error(`获取用户绑定的第三方账号列表失败: ${error.message}`, error.stack);
      throw new InternalServerErrorException('获取用户绑定的第三方账号列表失败，请稍后再试');
    }
  }

  /**
   * 绑定微信账号
   * @param user 用户实体
   * @param code 微信授权码
   * @returns 绑定结果
   * @private
   */
  private async bindWechat(user: any, code: string): Promise<{ message: string }> {
    // 获取微信应用配置
    const appId = this.configService.get<string>('auth.oauth.wechat.appId');
    const appSecret = this.configService.get<string>('auth.oauth.wechat.appSecret');

    if (!appId || !appSecret) {
      throw new BadRequestException('微信应用配置缺失');
    }

    // 通过code获取用户的access_token和openid
    const tokenResponse = await axios.get(
      `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${appId}&secret=${appSecret}&code=${code}&grant_type=authorization_code`,
    );

    if (tokenResponse.data.errcode) {
      this.logger.error(`微信授权失败: ${JSON.stringify(tokenResponse.data)}`);
      throw new BadRequestException(`微信授权失败: ${tokenResponse.data.errmsg}`);
    }

    const { access_token, refresh_token, openid, unionid, expires_in } = tokenResponse.data;

    // 检查该微信账号是否已被其他用户绑定
    const existingBind = await this.userOauthRepository.findOne({
      where: {
        providerType: ThirdPartyType.WECHAT,
        providerId: openid,
      },
    });

    if (existingBind) {
      throw new ConflictException('该微信账号已被其他用户绑定');
    }

    // 获取用户信息
    const userInfoResponse = await axios.get(
      `https://api.weixin.qq.com/sns/userinfo?access_token=${access_token}&openid=${openid}&lang=zh_CN`,
    );

    if (userInfoResponse.data.errcode) {
      this.logger.error(`获取微信用户信息失败: ${JSON.stringify(userInfoResponse.data)}`);
      throw new BadRequestException(`获取微信用户信息失败: ${userInfoResponse.data.errmsg}`);
    }

    const { nickname, headimgurl } = userInfoResponse.data;

    // 创建 OAuth 绑定记录
    const oauthRecord = this.userOauthRepository.create({
      userId: user.id,
      providerType: ThirdPartyType.WECHAT,
      providerId: openid,
      providerUsername: nickname,
      providerAvatar: headimgurl,
      accessToken: access_token,
      refreshToken: refresh_token,
      tokenExpiresAt: new Date(Date.now() + expires_in * 1000),
      lastLoginAt: new Date(),
      profileData: JSON.stringify(userInfoResponse.data),
    });

    await this.userOauthRepository.save(oauthRecord);

    // 更新用户实体中的微信相关字段
    if (unionid) {
      await this.userService.updateThirdPartyInfo(user.id, { wechatUnionid: unionid, wechatOpenid: openid });
    } else {
      await this.userService.updateThirdPartyInfo(user.id, { wechatOpenid: openid });
    }

    return { message: '微信账号绑定成功' };
  }

  /**
   * 获取提供商名称
   * @param providerType 提供商类型
   * @returns 提供商名称
   * @private
   */
  /**
   * 支付宝登录
   * @param dto 支付宝登录参数
   * @returns 登录结果
   */
  async alipayLogin(dto: AlipayLoginDto): Promise<any> {
    try {
      const { code } = dto;

      // 获取支付宝应用配置
      const appId = this.configService.get<string>('auth.oauth.alipay.appId');
      const privateKey = this.configService.get<string>('auth.oauth.alipay.privateKey');

      if (!appId || !privateKey) {
        throw new BadRequestException('支付宝应用配置缺失');
      }

      // 通过code获取用户的access_token和user_id
      // 注意：这里使用的是支付宝的SDK，实际实现可能需要根据支付宝开放平台的文档进行调整
      const tokenResponse = await axios.post(
        'https://openapi.alipay.com/gateway.do',
        {
          app_id: appId,
          method: 'alipay.system.oauth.token',
          charset: 'utf-8',
          sign_type: 'RSA2',
          timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
          version: '1.0',
          grant_type: 'authorization_code',
          code: code,
          // 签名参数需要根据支付宝开放平台的要求生成
        }
      );

      if (tokenResponse.data.error_response) {
        this.logger.error(`支付宝授权失败: ${JSON.stringify(tokenResponse.data)}`);
        throw new BadRequestException(`支付宝授权失败: ${tokenResponse.data.error_response.sub_msg || tokenResponse.data.error_response.msg}`);
      }

      const { access_token, refresh_token, user_id, expires_in } = tokenResponse.data.alipay_system_oauth_token_response;

      // 获取用户信息
      const userInfoResponse = await axios.post(
        'https://openapi.alipay.com/gateway.do',
        {
          app_id: appId,
          method: 'alipay.user.info.share',
          charset: 'utf-8',
          sign_type: 'RSA2',
          timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
          version: '1.0',
          auth_token: access_token,
          // 签名参数需要根据支付宝开放平台的要求生成
        }
      );

      if (userInfoResponse.data.error_response) {
        this.logger.error(`获取支付宝用户信息失败: ${JSON.stringify(userInfoResponse.data)}`);
        throw new BadRequestException(`获取支付宝用户信息失败: ${userInfoResponse.data.error_response.sub_msg || userInfoResponse.data.error_response.msg}`);
      }

      const { nick_name, avatar } = userInfoResponse.data.alipay_user_info_share_response;

      // 查找是否存在已绑定该支付宝账号的用户
      let oauthRecord = await this.userOauthRepository.findOne({
        where: {
          providerType: ThirdPartyType.ALIPAY,
          providerId: user_id,
        },
        relations: ['user'],
      });

      // 如果已存在绑定记录，直接登录
      if (oauthRecord) {
        const user = oauthRecord.user;

        // 更新 OAuth 记录
        oauthRecord.providerUsername = nick_name;
        oauthRecord.providerAvatar = avatar;
        oauthRecord.accessToken = access_token;
        oauthRecord.refreshToken = refresh_token;
        oauthRecord.tokenExpiresAt = new Date(Date.now() + expires_in * 1000);
        oauthRecord.lastLoginAt = new Date();
        oauthRecord.profileData = JSON.stringify(userInfoResponse.data.alipay_user_info_share_response);
        await this.userOauthRepository.save(oauthRecord);

        // 更新用户信息
        await this.userService.updateLastLogin(user.id, '127.0.0.1'); // TODO: 获取真实IP

        // 生成令牌
        const tokens = await this.authService.generateTokens(user);

        return {
          ...tokens,
          userId: user.id,
          username: user.username,
          nickname: user.nickname,
          email: user.email,
          phone: user.phone,
          avatar: user.avatar || avatar,
          isNewUser: false,
        };
      }

      // 不存在绑定记录，创建新用户并绑定
      // 生成随机用户名
      const username = `alipay_${Date.now().toString(36)}_${Math.random().toString(36).substr(2, 5)}`;
      // 生成随机密码
      const password = Math.random().toString(36).substr(2, 10);

      // 创建新用户
      const newUser = await this.userService.create({
        username,
        password,
        nickname: nick_name || username,
        avatar: avatar,
      });

      // 创建默认API密钥
      await this.apiKeyService.createApiKey(newUser.id, {
        name: '默认密钥',
      });

      // 创建 OAuth 绑定记录
      const newOAuthRecord = this.userOauthRepository.create({
        userId: newUser.id,
        providerType: ThirdPartyType.ALIPAY,
        providerId: user_id,
        providerUsername: nick_name,
        providerAvatar: avatar,
        accessToken: access_token,
        refreshToken: refresh_token,
        tokenExpiresAt: new Date(Date.now() + expires_in * 1000),
        lastLoginAt: new Date(),
        profileData: JSON.stringify(userInfoResponse.data.alipay_user_info_share_response),
      });

      await this.userOauthRepository.save(newOAuthRecord);

      // 更新用户实体中的支付宝相关字段
      await this.userService.updateThirdPartyInfo(newUser.id, { alipayUserId: user_id });

      // 生成令牌
      const tokens = await this.authService.generateTokens(newUser);

      return {
        ...tokens,
        userId: newUser.id,
        username: newUser.username,
        nickname: newUser.nickname,
        email: newUser.email,
        phone: newUser.phone,
        avatar: avatar,
        isNewUser: true,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`支付宝登录失败: ${error.message}`, error.stack);
      throw new InternalServerErrorException('支付宝登录失败，请稍后再试');
    }
  }

  /**
   * GitHub登录
   * @param dto GitHub登录参数
   * @returns 登录结果
   */
  async githubLogin(dto: GithubLoginDto): Promise<any> {
    try {
      const { code } = dto;

      // 获取GitHub应用配置
      const clientId = this.configService.get<string>('auth.oauth.github.clientId');
      const clientSecret = this.configService.get<string>('auth.oauth.github.clientSecret');

      if (!clientId || !clientSecret) {
        throw new BadRequestException('GitHub应用配置缺失');
      }

      // 通过code获取用户的access_token
      const tokenResponse = await axios.post(
        'https://github.com/login/oauth/access_token',
        {
          client_id: clientId,
          client_secret: clientSecret,
          code: code,
        },
        {
          headers: {
            Accept: 'application/json',
          },
        }
      );

      if (tokenResponse.data.error) {
        this.logger.error(`GitHub授权失败: ${JSON.stringify(tokenResponse.data)}`);
        throw new BadRequestException(`GitHub授权失败: ${tokenResponse.data.error_description || tokenResponse.data.error}`);
      }

      const { access_token } = tokenResponse.data;

      // 获取用户信息
      const userInfoResponse = await axios.get('https://api.github.com/user', {
        headers: {
          Authorization: `token ${access_token}`,
        },
      });

      const { id, login, name, avatar_url, email } = userInfoResponse.data;

      // 查找是否存在已绑定该GitHub账号的用户
      let oauthRecord = await this.userOauthRepository.findOne({
        where: {
          providerType: ThirdPartyType.GITHUB,
          providerId: id.toString(),
        },
        relations: ['user'],
      });

      // 如果已存在绑定记录，直接登录
      if (oauthRecord) {
        const user = oauthRecord.user;

        // 更新 OAuth 记录
        oauthRecord.providerUsername = login;
        oauthRecord.providerAvatar = avatar_url;
        oauthRecord.accessToken = access_token;
        oauthRecord.lastLoginAt = new Date();
        oauthRecord.profileData = JSON.stringify(userInfoResponse.data);
        await this.userOauthRepository.save(oauthRecord);

        // 更新用户信息
        await this.userService.updateLastLogin(user.id, '127.0.0.1'); // TODO: 获取真实IP

        // 生成令牌
        const tokens = await this.authService.generateTokens(user);

        return {
          ...tokens,
          userId: user.id,
          username: user.username,
          nickname: user.nickname,
          email: user.email,
          phone: user.phone,
          avatar: user.avatar || avatar_url,
          isNewUser: false,
        };
      }

      // 不存在绑定记录，创建新用户并绑定
      // 生成随机用户名
      const username = `gh_${login}_${Math.random().toString(36).substr(2, 5)}`;
      // 生成随机密码
      const password = Math.random().toString(36).substr(2, 10);

      // 创建新用户
      const newUser = await this.userService.create({
        username,
        password,
        nickname: name || login || username,
        email: email, // GitHub可能返回用户邮箱
        avatar: avatar_url,
      });

      // 创建默认API密钥
      await this.apiKeyService.createApiKey(newUser.id, {
        name: '默认密钥',
      });

      // 创建 OAuth 绑定记录
      const newOAuthRecord = this.userOauthRepository.create({
        userId: newUser.id,
        providerType: ThirdPartyType.GITHUB,
        providerId: id.toString(),
        providerUsername: login,
        providerAvatar: avatar_url,
        accessToken: access_token,
        lastLoginAt: new Date(),
        profileData: JSON.stringify(userInfoResponse.data),
      });

      await this.userOauthRepository.save(newOAuthRecord);

      // 更新用户实体中的GitHub相关字段
      await this.userService.updateThirdPartyInfo(newUser.id, { githubId: id.toString() });

      // 生成令牌
      const tokens = await this.authService.generateTokens(newUser);

      return {
        ...tokens,
        userId: newUser.id,
        username: newUser.username,
        nickname: newUser.nickname,
        email: newUser.email,
        phone: newUser.phone,
        avatar: avatar_url,
        isNewUser: true,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`GitHub登录失败: ${error.message}`, error.stack);
      throw new InternalServerErrorException('GitHub登录失败，请稍后再试');
    }
  }

  /**
   * 绑定支付宝账号
   * @param user 用户实体
   * @param code 支付宝授权码
   * @returns 绑定结果
   * @private
   */
  private async bindAlipay(user: any, code: string): Promise<{ message: string }> {
    // 获取支付宝应用配置
    const appId = this.configService.get<string>('auth.oauth.alipay.appId');
    const privateKey = this.configService.get<string>('auth.oauth.alipay.privateKey');

    if (!appId || !privateKey) {
      throw new BadRequestException('支付宝应用配置缺失');
    }

    // 通过code获取用户的access_token和user_id
    const tokenResponse = await axios.post(
      'https://openapi.alipay.com/gateway.do',
      {
        app_id: appId,
        method: 'alipay.system.oauth.token',
        charset: 'utf-8',
        sign_type: 'RSA2',
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
        version: '1.0',
        grant_type: 'authorization_code',
        code: code,
        // 签名参数需要根据支付宝开放平台的要求生成
      }
    );

    if (tokenResponse.data.error_response) {
      this.logger.error(`支付宝授权失败: ${JSON.stringify(tokenResponse.data)}`);
      throw new BadRequestException(`支付宝授权失败: ${tokenResponse.data.error_response.sub_msg || tokenResponse.data.error_response.msg}`);
    }

    const { access_token, refresh_token, user_id, expires_in } = tokenResponse.data.alipay_system_oauth_token_response;

    // 检查该支付宝账号是否已被其他用户绑定
    const existingBind = await this.userOauthRepository.findOne({
      where: {
        providerType: ThirdPartyType.ALIPAY,
        providerId: user_id,
      },
    });

    if (existingBind) {
      throw new ConflictException('该支付宝账号已被其他用户绑定');
    }

    // 获取用户信息
    const userInfoResponse = await axios.post(
      'https://openapi.alipay.com/gateway.do',
      {
        app_id: appId,
        method: 'alipay.user.info.share',
        charset: 'utf-8',
        sign_type: 'RSA2',
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
        version: '1.0',
        auth_token: access_token,
        // 签名参数需要根据支付宝开放平台的要求生成
      }
    );

    if (userInfoResponse.data.error_response) {
      this.logger.error(`获取支付宝用户信息失败: ${JSON.stringify(userInfoResponse.data)}`);
      throw new BadRequestException(`获取支付宝用户信息失败: ${userInfoResponse.data.error_response.sub_msg || userInfoResponse.data.error_response.msg}`);
    }

    const { nick_name, avatar } = userInfoResponse.data.alipay_user_info_share_response;

    // 创建 OAuth 绑定记录
    const oauthRecord = this.userOauthRepository.create({
      userId: user.id,
      providerType: ThirdPartyType.ALIPAY,
      providerId: user_id,
      providerUsername: nick_name,
      providerAvatar: avatar,
      accessToken: access_token,
      refreshToken: refresh_token,
      tokenExpiresAt: new Date(Date.now() + expires_in * 1000),
      lastLoginAt: new Date(),
      profileData: JSON.stringify(userInfoResponse.data.alipay_user_info_share_response),
    });

    await this.userOauthRepository.save(oauthRecord);

    // 更新用户实体中的支付宝相关字段
    await this.userService.updateThirdPartyInfo(user.id, { alipayUserId: user_id });

    return { message: '支付宝账号绑定成功' };
  }

  /**
   * 绑定GitHub账号
   * @param user 用户实体
   * @param code GitHub授权码
   * @returns 绑定结果
   * @private
   */
  private async bindGithub(user: any, code: string): Promise<{ message: string }> {
    // 获取GitHub应用配置
    const clientId = this.configService.get<string>('auth.oauth.github.clientId');
    const clientSecret = this.configService.get<string>('auth.oauth.github.clientSecret');

    if (!clientId || !clientSecret) {
      throw new BadRequestException('GitHub应用配置缺失');
    }

    // 通过code获取用户的access_token
    const tokenResponse = await axios.post(
      'https://github.com/login/oauth/access_token',
      {
        client_id: clientId,
        client_secret: clientSecret,
        code: code,
      },
      {
        headers: {
          Accept: 'application/json',
        },
      }
    );

    if (tokenResponse.data.error) {
      this.logger.error(`GitHub授权失败: ${JSON.stringify(tokenResponse.data)}`);
      throw new BadRequestException(`GitHub授权失败: ${tokenResponse.data.error_description || tokenResponse.data.error}`);
    }

    const { access_token } = tokenResponse.data;

    // 获取用户信息
    const userInfoResponse = await axios.get('https://api.github.com/user', {
      headers: {
        Authorization: `token ${access_token}`,
      },
    });

    const { id, login, avatar_url } = userInfoResponse.data;

    // 检查该GitHub账号是否已被其他用户绑定
    const existingBind = await this.userOauthRepository.findOne({
      where: {
        providerType: ThirdPartyType.GITHUB,
        providerId: id.toString(),
      },
    });

    if (existingBind) {
      throw new ConflictException('该GitHub账号已被其他用户绑定');
    }

    // 创建 OAuth 绑定记录
    const oauthRecord = this.userOauthRepository.create({
      userId: user.id,
      providerType: ThirdPartyType.GITHUB,
      providerId: id.toString(),
      providerUsername: login,
      providerAvatar: avatar_url,
      accessToken: access_token,
      lastLoginAt: new Date(),
      profileData: JSON.stringify(userInfoResponse.data),
    });

    await this.userOauthRepository.save(oauthRecord);

    // 更新用户实体中的GitHub相关字段
    await this.userService.updateThirdPartyInfo(user.id, { githubId: id.toString() });

    return { message: 'GitHub账号绑定成功' };
  }

  /**
   * 获取提供商名称
   * @param providerType 提供商类型
   * @returns 提供商名称
   * @private
   */
  /**
   * Google登录
   * @param dto Google登录参数
   * @returns 登录结果
   */
  async googleLogin(dto: GoogleLoginDto): Promise<any> {
    try {
      const { code } = dto;

      // 获取Google应用配置
      const clientId = this.configService.get<string>('auth.oauth.google.clientId');
      const clientSecret = this.configService.get<string>('auth.oauth.google.clientSecret');
      const redirectUri = this.configService.get<string>('auth.oauth.google.redirectUri');

      if (!clientId || !clientSecret) {
        throw new BadRequestException('Google应用配置缺失');
      }

      // 通过code获取用户的access_token
      const tokenResponse = await axios.post(
        'https://oauth2.googleapis.com/token',
        {
          code,
          client_id: clientId,
          client_secret: clientSecret,
          redirect_uri: redirectUri,
          grant_type: 'authorization_code',
        }
      );

      if (tokenResponse.data.error) {
        this.logger.error(`Google授权失败: ${JSON.stringify(tokenResponse.data)}`);
        throw new BadRequestException(`Google授权失败: ${tokenResponse.data.error_description || tokenResponse.data.error}`);
      }

      const { access_token, refresh_token, expires_in } = tokenResponse.data;

      // 获取用户信息
      const userInfoResponse = await axios.get('https://www.googleapis.com/oauth2/v2/userinfo', {
        headers: {
          Authorization: `Bearer ${access_token}`,
        },
      });

      const { id, email, name, picture } = userInfoResponse.data;

      // 查找是否存在已绑定该Google账号的用户
      let oauthRecord = await this.userOauthRepository.findOne({
        where: {
          providerType: ThirdPartyType.GOOGLE,
          providerId: id.toString(),
        },
        relations: ['user'],
      });

      // 如果已存在绑定记录，直接登录
      if (oauthRecord) {
        const user = oauthRecord.user;

        // 更新 OAuth 记录
        oauthRecord.providerUsername = name || email;
        oauthRecord.providerAvatar = picture;
        oauthRecord.accessToken = access_token;
        oauthRecord.refreshToken = refresh_token;
        oauthRecord.tokenExpiresAt = expires_in ? new Date(Date.now() + expires_in * 1000) : undefined;
        oauthRecord.lastLoginAt = new Date();
        oauthRecord.profileData = JSON.stringify(userInfoResponse.data);
        await this.userOauthRepository.save(oauthRecord);

        // 更新用户信息
        await this.userService.updateLastLogin(user.id, '127.0.0.1'); // TODO: 获取真实IP

        // 生成令牌
        const tokens = await this.authService.generateTokens(user);

        return {
          ...tokens,
          userId: user.id,
          username: user.username,
          nickname: user.nickname,
          email: user.email,
          phone: user.phone,
          avatar: user.avatar || picture,
          isNewUser: false,
        };
      }

      // 不存在绑定记录，创建新用户并绑定
      // 生成随机用户名
      const username = `google_${email.split('@')[0]}_${Math.random().toString(36).substr(2, 5)}`;
      // 生成随机密码
      const password = Math.random().toString(36).substr(2, 10);

      // 创建新用户
      const newUser = await this.userService.create({
        username,
        password,
        nickname: name || email.split('@')[0] || username,
        email: email, // Google通常会返回用户邮箱
        avatar: picture,
      });

      // 创建默认API密钥
      await this.apiKeyService.createApiKey(newUser.id, {
        name: '默认密钥',
      });

      // 创建 OAuth 绑定记录
      const newOAuthRecord = this.userOauthRepository.create({
        userId: newUser.id,
        providerType: ThirdPartyType.GOOGLE,
        providerId: id.toString(),
        providerUsername: name || email,
        providerAvatar: picture,
        accessToken: access_token,
        refreshToken: refresh_token,
        tokenExpiresAt: expires_in ? new Date(Date.now() + expires_in * 1000) : undefined,
        lastLoginAt: new Date(),
        profileData: JSON.stringify(userInfoResponse.data),
      });

      await this.userOauthRepository.save(newOAuthRecord);

      // 更新用户实体中的Google相关字段
      await this.userService.updateThirdPartyInfo(newUser.id, { googleId: id.toString() });

      // 生成令牌
      const tokens = await this.authService.generateTokens(newUser);

      return {
        ...tokens,
        userId: newUser.id,
        username: newUser.username,
        nickname: newUser.nickname,
        email: newUser.email,
        phone: newUser.phone,
        avatar: picture,
        isNewUser: true,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Google登录失败: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Google登录失败，请稍后再试');
    }
  }

  /**
   * 绑定Google账号
   * @param user 用户实体
   * @param code Google授权码
   * @returns 绑定结果
   * @private
   */
  private async bindGoogle(user: any, code: string): Promise<{ message: string }> {
    // 获取Google应用配置
    const clientId = this.configService.get<string>('auth.oauth.google.clientId');
    const clientSecret = this.configService.get<string>('auth.oauth.google.clientSecret');
    const redirectUri = this.configService.get<string>('auth.oauth.google.redirectUri');

    if (!clientId || !clientSecret) {
      throw new BadRequestException('Google应用配置缺失');
    }

    // 通过code获取用户的access_token
    const tokenResponse = await axios.post(
      'https://oauth2.googleapis.com/token',
      {
        code,
        client_id: clientId,
        client_secret: clientSecret,
        redirect_uri: redirectUri,
        grant_type: 'authorization_code',
      }
    );

    if (tokenResponse.data.error) {
      this.logger.error(`Google授权失败: ${JSON.stringify(tokenResponse.data)}`);
      throw new BadRequestException(`Google授权失败: ${tokenResponse.data.error_description || tokenResponse.data.error}`);
    }

    const { access_token, refresh_token, expires_in } = tokenResponse.data;

    // 获取用户信息
    const userInfoResponse = await axios.get('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        Authorization: `Bearer ${access_token}`,
      },
    });

    const { id, name, picture } = userInfoResponse.data;

    // 检查该Google账号是否已被其他用户绑定
    const existingBind = await this.userOauthRepository.findOne({
      where: {
        providerType: ThirdPartyType.GOOGLE,
        providerId: id.toString(),
      },
    });

    if (existingBind) {
      throw new ConflictException('该Google账号已被其他用户绑定');
    }

    // 创建 OAuth 绑定记录
    const oauthRecord = this.userOauthRepository.create({
      userId: user.id,
      providerType: ThirdPartyType.GOOGLE,
      providerId: id.toString(),
      providerUsername: name,
      providerAvatar: picture,
      accessToken: access_token,
      refreshToken: refresh_token,
      tokenExpiresAt: expires_in ? new Date(Date.now() + expires_in * 1000) : undefined,
      lastLoginAt: new Date(),
      profileData: JSON.stringify(userInfoResponse.data),
    });

    await this.userOauthRepository.save(oauthRecord);

    // 更新用户实体中的Google相关字段
    await this.userService.updateThirdPartyInfo(user.id, { googleId: id.toString() });

    return { message: 'Google账号绑定成功' };
  }

  /**
   * 获取提供商名称
   * @param providerType 提供商类型
   * @returns 提供商名称
   * @private
   */
  private getProviderName(providerType: ThirdPartyType): string {
    switch (providerType) {
      case ThirdPartyType.WECHAT:
        return '微信';
      case ThirdPartyType.ALIPAY:
        return '支付宝';
      case ThirdPartyType.GITHUB:
        return 'GitHub';
      case ThirdPartyType.GOOGLE:
        return 'Google';
      default:
        return '未知提供商';
    }
  }
}