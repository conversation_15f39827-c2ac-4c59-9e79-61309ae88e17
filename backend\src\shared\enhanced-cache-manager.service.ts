import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from './redis.service';

/**
 * 增强缓存管理服务
 * 提供统一的缓存操作接口
 */
@Injectable()
export class EnhancedCacheManagerService {
  private readonly logger = new Logger(EnhancedCacheManagerService.name);

  constructor(private readonly redisService: RedisService) {}

  /**
   * 设置缓存
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      if (ttl) {
        await this.redisService.setex(key, ttl, serializedValue);
      } else {
        await this.redisService.set(key, serializedValue);
      }
    } catch (error) {
      this.logger.error(`设置缓存失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 获取缓存
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.redisService.get(key);
      if (!value) {
        return null;
      }
      return JSON.parse(value) as T;
    } catch (error) {
      this.logger.error(`获取缓存失败: ${key}`, error);
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string): Promise<void> {
    try {
      await this.redisService.del(key);
    } catch (error) {
      this.logger.error(`删除缓存失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 检查缓存是否存在
   */
  async exists(key: string): Promise<boolean> {
    try {
      return await this.redisService.exists(key);
    } catch (error) {
      this.logger.error(`检查缓存存在性失败: ${key}`, error);
      return false;
    }
  }

  /**
   * 设置缓存过期时间
   */
  async expire(key: string, ttl: number): Promise<void> {
    try {
      await this.redisService.expire(key, ttl);
    } catch (error) {
      this.logger.error(`设置缓存过期时间失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 批量删除缓存
   */
  async delPattern(pattern: string): Promise<void> {
    try {
      const keys = await this.redisService.keys(pattern);
      if (keys.length > 0) {
        await this.redisService.delMany(keys);
      }
    } catch (error) {
      this.logger.error(`批量删除缓存失败: ${pattern}`, error);
      throw error;
    }
  }

  /**
   * 清理用户相关缓存
   */
  async clearUserRelatedCache(userId: string | number): Promise<void> {
    try {
      const patterns = [
        `user:${userId}*`,
        `user_roles:${userId}`,
        `user_permissions:${userId}`,
        `user_balance:${userId}`,
        `user_quota:${userId}*`,
      ];
      
      for (const pattern of patterns) {
        await this.delPattern(pattern);
      }
    } catch (error) {
      this.logger.error(`清理用户相关缓存失败: ${userId}`, error);
      throw error;
    }
  }

  /**
   * 清理计费相关缓存
   */
  async clearBillingRelatedCache(userId: string | number, serviceId?: string | number): Promise<void> {
    try {
      const patterns = [
        `user_balance:${userId}`,
        `billing:${userId}*`,
      ];
      
      if (serviceId) {
        patterns.push(
          `user_quota:${userId}:${serviceId}`,
          `billing:${userId}:${serviceId}*`,
        );
      } else {
        patterns.push(`user_quota:${userId}*`);
      }
      
      for (const pattern of patterns) {
        await this.delPattern(pattern);
      }
    } catch (error) {
      this.logger.error(`清理计费相关缓存失败: ${userId}, ${serviceId}`, error);
      throw error;
    }
  }

  /**
   * 清理API密钥相关缓存
   */
  async clearApiKeyRelatedCache(apiKeyId?: string | number, keyHash?: string, userId?: string | number): Promise<void> {
    try {
      const patterns: string[] = [];
      
      if (keyHash) {
        patterns.push(`api_key:${keyHash}`);
      }
      
      if (apiKeyId) {
        patterns.push(`api_key_id:${apiKeyId}`);
      }
      
      if (userId) {
        patterns.push(`user_api_keys:${userId}*`);
      }
      
      for (const pattern of patterns) {
        await this.delPattern(pattern);
      }
    } catch (error) {
      this.logger.error(`清理API密钥相关缓存失败: ${apiKeyId}, ${keyHash}, ${userId}`, error);
      throw error;
    }
  }
}