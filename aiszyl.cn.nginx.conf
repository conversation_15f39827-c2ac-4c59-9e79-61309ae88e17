server {
    server_name aiszyl.cn www.aiszyl.cn;

    # 访问日志
    access_log /var/log/nginx/aiszyl.cn.access.log;
    error_log /var/log/nginx/aiszyl.cn.error.log;

    # 静态资源处理 - 直接从前端容器获取
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }

    # HTML5 History模式的关键配置
    # 所有非静态资源请求都转发到前端容器
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
        
        # 禁用缓存，确保始终获取最新内容
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/aiszyl.cn/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/aiszyl.cn/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
}

server {
    if ($host = www.aiszyl.cn) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    if ($host = aiszyl.cn) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    listen 80;
    server_name aiszyl.cn www.aiszyl.cn;
    return 404; # managed by Certbot
}