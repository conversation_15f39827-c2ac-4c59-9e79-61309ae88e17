import { Processor, Process, OnQueueActive, OnQueueCompleted, OnQueueFailed } from '@nestjs/bull';
import { Job } from 'bull';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { StructuredLogger } from '../../../common/logging/structured-logger';
import { TaskService } from '../services/task.service';
import { TaskStatus } from '../interfaces/task-status.enum';
import { AddressJobData } from '../interfaces/queue-job.interface';

/**
 * 地址解析结果接口
 */
interface AddressResult {
  province?: string;
  city?: string;
  district?: string;
  street?: string;
  streetNumber?: string;
  community?: string;
  building?: string;
  room?: string;
  name?: string;
  phone?: string;
  postalCode?: string;
  formatted?: string;
  confidence?: number;
}

/**
 * 地址解析任务处理器
 * 处理地址文本解析任务
 */
@Processor('extract-address')
export class AddressProcessor {
  constructor(
    private readonly taskService: TaskService,
    private readonly logger: StructuredLogger,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * 处理地址解析任务
   */
  @Process()
  async processAddressTask(job: Job<AddressJobData>): Promise<AddressResult> {
    const { taskId, addressText, options, body } = job.data;

    // 兼容处理：如果没有addressText，从body中提取text字段
    const textToProcess = addressText || (body && body.text) || '';

    if (!textToProcess) {
      throw new Error('缺少要解析的地址文本');
    }
    
    try {
      // 更新任务状态为处理中
      await this.taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING, {
        progress: 0,
        message: '开始解析地址',
      });
      
      // 模拟处理过程
      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟处理耗时
      
      // 更新进度
      await this.taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING, {
        progress: 0.5,
        message: '提取地址组件',
      });
      
      // 再次模拟处理
      await new Promise(resolve => setTimeout(resolve, 1000)); 
      
      // 生成模拟结果
      const result = this.parseAddress(textToProcess, options);
      
      // 更新任务状态为完成
      await this.taskService.updateTaskStatus(taskId, TaskStatus.COMPLETED, {
        result,
        message: '解析完成',
        progress: 1,
      });
      
      this.logger.log(
        `地址解析任务${taskId}完成`,
        { 
          module: 'AddressProcessor',
          metadata: { confidence: result.confidence } 
        }
      );
      
      return result;
    } catch (error) {
      // 更新任务状态为失败
      await this.taskService.updateTaskStatus(taskId, TaskStatus.FAILED, {
        error: error.message,
        message: '解析失败',
      });
      
      this.logger.error(
        `地址解析任务${taskId}失败`,
        error,
        { module: 'AddressProcessor' }
      );
      
      throw error;
    }
  }
  
  /**
   * 任务开始处理时触发
   */
  @OnQueueActive()
  async onActive(job: Job): Promise<void> {
    const { taskId } = job.data;
    await this.taskService.updateTaskStatus(taskId, TaskStatus.ACTIVE, {
      message: '任务开始处理',
    });
  }

  /**
   * 任务完成时触发
   */
  @OnQueueCompleted()
  async onCompleted(job: Job<AddressJobData>, result: AddressResult): Promise<void> {
    const { taskId, userId, serviceId } = job.data;

    this.logger.log(
      `地址解析任务${taskId}完成`,
      {
        module: 'AddressProcessor',
        metadata: { confidence: result.confidence }
      }
    );

    // 任务成功完成，发布事件通知扣减调用次数
    if (userId && serviceId) {
      this.eventEmitter.emit('queue.task.completed', {
        taskId,
        userId,
        serviceId,
        taskType: 'extract-address',
        result
      });
      this.logger.debug(`发布任务完成事件，用户${userId}服务${serviceId}将扣减调用次数`);
    } else {
      this.logger.warn(`任务${taskId}缺少用户或服务信息，无法扣减次数`);
    }
  }

  /**
   * 任务失败时触发
   */
  @OnQueueFailed()
  async onFailed(job: Job<AddressJobData>, error: Error): Promise<void> {
    const { taskId, userId, serviceId } = job.data;

    this.logger.error(
      `地址解析任务${taskId}失败`,
      error,
      { module: 'AddressProcessor' }
    );

    // 任务失败，不扣减次数
    this.logger.debug(`任务${taskId}失败，不扣减用户${userId}服务${serviceId}的调用次数`);
  }

  /**
   * 模拟地址解析逻辑
   * 实际项目中应替换为真实的地址解析逻辑
   */
  private parseAddress(text: string, options?: any): AddressResult {
    // 检查输入参数
    if (!text || typeof text !== 'string') {
      throw new Error('地址文本不能为空');
    }

    // 这里是模拟的地址解析逻辑，实际项目中应该调用专业的地址解析服务
    const hasName = text.includes('收件人') || text.includes('姓名');
    const hasPhone = /1[3-9]\d{9}/.test(text);
    
    // 模拟解析结果
    const result: AddressResult = {
      confidence: 0.9,
    };
    
    // 假设的省份城市提取
    if (text.includes('北京')) {
      result.province = '北京市';
      
      if (text.includes('海淀')) {
        result.city = '北京市';
        result.district = '海淀区';
        
        if (text.includes('中关村')) {
          result.street = '中关村大街';
          result.streetNumber = '1号';
        }
      }
    } else if (text.includes('上海')) {
      result.province = '上海市';
      
      if (text.includes('浦东')) {
        result.city = '上海市';
        result.district = '浦东新区';
        
        if (text.includes('张江')) {
          result.street = '张江高科技园区';
        }
      }
    }
    
    // 提取姓名和电话
    if (hasName) {
      const nameMatch = text.match(/[收件人|姓名][:：\s]*([\u4e00-\u9fa5]{2,4})/);
      if (nameMatch && nameMatch[1]) {
        result.name = nameMatch[1];
      }
    }
    
    if (hasPhone) {
      const phoneMatch = text.match(/1[3-9]\d{9}/);
      if (phoneMatch) {
        result.phone = phoneMatch[0];
      }
    }
    
    // 格式化地址
    if (options?.format && result.province) {
      result.formatted = [
        result.province,
        result.city,
        result.district,
        result.street,
        result.streetNumber,
        result.building,
        result.room,
      ]
        .filter(Boolean)
        .join('');
    }
    
    return result;
  }
} 