import { Processor, Process, OnQueueActive, OnQueueCompleted, OnQueueFailed } from '@nestjs/bull';
import { Job } from 'bull';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { StructuredLogger } from '../../../common/logging/structured-logger';
import { TaskService } from '../services/task.service';
import { TaskStatus } from '../interfaces/task-status.enum';
import { AddressJobData } from '../interfaces/queue-job.interface';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { SERVICE_ENDPOINTS } from '../../../shared/constants/api.constants';

/**
 * 地址解析结果接口
 */
interface AddressResult {
  province?: string;
  city?: string;
  district?: string;
  street?: string;
  streetNumber?: string;
  community?: string;
  building?: string;
  room?: string;
  name?: string;
  phone?: string;
  postalCode?: string;
  formatted?: string;
  confidence?: number;
}

/**
 * 地址解析任务处理器
 * 处理地址文本解析任务
 */
@Processor('extract-address')
export class AddressProcessor {
  constructor(
    private readonly taskService: TaskService,
    private readonly logger: StructuredLogger,
    private readonly eventEmitter: EventEmitter2,
    private readonly http: HttpService,
  ) {}

  /**
   * 处理地址解析任务
   */
  @Process()
  async processAddressTask(job: Job<AddressJobData>): Promise<AddressResult> {
    const { taskId, addressText, options, body } = job.data;

    // 兼容处理：如果没有addressText，从body中提取text字段
    const textToProcess = addressText || (body && body.text) || '';

    if (!textToProcess) {
      throw new Error('缺少要解析的地址文本');
    }
    
    try {
      // 更新任务状态为处理中
      await this.taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING, {
        progress: 0,
        message: '开始解析地址',
      });

      // 调用下游 python-service 地址提取接口
      const baseUrl = SERVICE_ENDPOINTS.ADDRESS_SERVICE || 'http://localhost:8866';
      const url = `${baseUrl}/address/extract`;

      // 进度更新
      await this.taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING, {
        progress: 0.3,
        message: '调用地址解析服务',
      });

      const resp = await firstValueFrom(
        this.http.post(url, { text: textToProcess }, {
          headers: { 'Content-Type': 'application/json', 'Accept-Language': 'zh-CN' },
          timeout: 10000,
        })
      );

      const data = resp?.data;
      if (!data || data.success !== true || !data.data) {
        throw new Error(`下游服务返回异常: ${JSON.stringify(data)}`);
      }

      // 结果映射
      const d = data.data;
      const result: AddressResult = {
        province: d.province || undefined,
        city: d.city || undefined,
        district: d.district || undefined,
        street: d.street || undefined,
        community: d.community || undefined,
        name: d.name || undefined,
        phone: d.phone || undefined,
        formatted: d.full_address || undefined,
        confidence: typeof d.confidence === 'number' ? d.confidence : undefined,
      };

      await this.taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING, {
        progress: 0.8,
        message: '聚合解析结果',
      });

      // 完成
      await this.taskService.updateTaskStatus(taskId, TaskStatus.COMPLETED, {
        result,
        message: '解析完成',
        progress: 1,
      });

      this.logger.log(
        `地址解析任务${taskId}完成`,
        { 
          module: 'AddressProcessor',
          metadata: { confidence: result.confidence }
        }
      );
      
      return result;
    } catch (error) {
      // 更新任务状态为失败
      await this.taskService.updateTaskStatus(taskId, TaskStatus.FAILED, {
        error: (error as Error).message,
        message: '解析失败',
      });
      
      this.logger.error(
        `地址解析任务${taskId}失败`,
        error as Error,
        { module: 'AddressProcessor' }
      );
      
      throw error;
    }
  }
  
  /**
   * 任务开始处理时触发
   */
  @OnQueueActive()
  async onActive(job: Job): Promise<void> {
    const { taskId } = job.data;
    await this.taskService.updateTaskStatus(taskId, TaskStatus.ACTIVE, {
      message: '任务开始处理',
    });
  }

  /**
   * 任务完成时触发
   */
  @OnQueueCompleted()
  async onCompleted(job: Job<AddressJobData>, result: AddressResult): Promise<void> {
    const { taskId, userId, serviceId } = job.data;

    this.logger.log(
      `地址解析任务${taskId}完成`,
      {
        module: 'AddressProcessor',
        metadata: { confidence: result.confidence }
      }
    );

    // 任务成功完成，发布事件通知扣减调用次数
    if (userId && serviceId) {
      this.eventEmitter.emit('queue.task.completed', {
        taskId,
        userId,
        serviceId,
        taskType: 'extract-address',
        result
      });
      this.logger.debug(`发布任务完成事件，用户${userId}服务${serviceId}将扣减调用次数`);
    } else {
      this.logger.warn(`任务${taskId}缺少用户或服务信息，无法扣减次数`);
    }
  }

  /**
   * 任务失败时触发
   */
  @OnQueueFailed()
  async onFailed(job: Job<AddressJobData>, error: Error): Promise<void> {
    const { taskId, userId, serviceId } = job.data;

    this.logger.error(
      `地址解析任务${taskId}失败`,
      error,
      { module: 'AddressProcessor' }
    );

    // 任务失败，不扣减次数
    this.logger.debug(`任务${taskId}失败，不扣减用户${userId}服务${serviceId}的调用次数`);
  }
} 