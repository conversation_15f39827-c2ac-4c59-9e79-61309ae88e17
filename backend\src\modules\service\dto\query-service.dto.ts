import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsInt, Min, IsIn } from 'class-validator';
import { Type } from 'class-transformer';
import { ServiceStatus, ServiceType } from '../enums/service.enum';

export class QueryServiceDto {
  @ApiPropertyOptional({
    description: '页码',
    default: 1,
  })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  page?: number = 1;

  @ApiPropertyOptional({
    description: '每页数量',
    default: 10,
  })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  limit?: number = 10;

  @ApiPropertyOptional({
    description: '服务类型',
    enum: ServiceType,
  })
  @IsEnum(ServiceType)
  @IsOptional()
  type?: ServiceType;

  @ApiPropertyOptional({
    description: '服务状态',
    enum: ServiceStatus,
  })
  @IsEnum(ServiceStatus)
  @IsOptional()
  status?: ServiceStatus;

  @ApiPropertyOptional({
    description: '搜索关键词（搜索名称和描述）',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({
    description: '排序字段',
    default: 'sortOrder',
    enum: ['id', 'name', 'sortOrder', 'createdAt', 'updatedAt'],
  })
  @IsString()
  @IsIn(['id', 'name', 'sortOrder', 'createdAt', 'updatedAt'])
  @IsOptional()
  sortBy?: string = 'sortOrder';

  @ApiPropertyOptional({
    description: '排序方向',
    default: 'ASC',
    enum: ['ASC', 'DESC'],
  })
  @IsString()
  @IsIn(['ASC', 'DESC'])
  @IsOptional()
  sortOrder?: 'ASC' | 'DESC' = 'ASC';
} 