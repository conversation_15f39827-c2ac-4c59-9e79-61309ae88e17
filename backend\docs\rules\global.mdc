---
description: 
globs: 
alwaysApply: true
---

---
description: 
globs: 
alwaysApply: true
---
# 项目通用规范

## 技术栈
- 使用vue3+vant框架，使用原生的js语言，不需要使用typeScript
- 尽量使用vant现有的组件
- 使用Pinia管理用户登录态、购物车数据
- 所有调用后端服务都必须使用API，目录在src/api
- 页面的组件嵌套不要超过三层
- 你在进行页面开发时，可以扫描 [README.md](mdc:README.md) 的项目结构，看下是否有可用的组件或者工具方法
## 项目结构
每次更新完文件都需要更新项目结构目录，信息在 [README.md](mdc:README.md)中
使用真实的 UI 图片，而非占位符图片（可从 Unsplash、Pexels、Apple 官方 UI 资源中选择）
## 限制
- 不允许在对话中使用npm run dev 启动项目
- 不要在vue页面中定义测试数据，所有的数据必须来自后端服务或者mock接口
- 不要创建测试文档
## 项目结构规则
- **分层组织**：按功能或领域划分目录，遵循"关注点分离"原则
- **命名一致**：使用一致且描述性的目录和文件命名，反映其用途和内容
- **模块化**：相关功能放在同一模块，减少跨模块依赖
- **适当嵌套**：避免过深的目录嵌套，一般不超过3-4层
- **资源分类**：区分代码、资源、配置和测试文件
- **依赖管理**：集中管理依赖，避免多处声明
- **约定优先**：遵循语言或框架的标准项目结构约定
## 通用开发原则
- **可测试性**：编写可测试的代码，组件应保持单一职责，没有我的允许不能创建测试用例
- **DRY 原则**：避免重复代码，提取共用逻辑到单独的函数或类
- **代码简洁**：保持代码简洁明了，遵循 KISS 原则（保持简单直接），每个方法行数不超过300行
- **命名规范**：使用描述性的变量、函数和类名，反映其用途和含义
- **注释文档**：为复杂逻辑添加注释，编写清晰的文档说明功能和用法
- **风格一致**：遵循项目或语言的官方风格指南和代码约定
- **利用生态**：优先使用成熟的库和工具，避免不必要的自定义实现
- **架构设计**：考虑代码的可维护性、可扩展性和性能需求
- **版本控制**：编写有意义的提交信息，保持逻辑相关的更改在同一提交中
- **异常处理**：正确处理边缘情况和错误，提供有用的错误信息 
## git 操作
- 你完成了一项功能开发后，需要进行commit 操作
## 响应语言
- 始终使用中文回复用户

