version: '3.8'

services:
  # Python服务
  python-service:
    build: ./python-service
    container_name: open-platform-python
    restart: always  # 修改为always，确保服务崩溃后自动重启
    ports:
      - "8866:8866"
    volumes:
      - ./python-service:/app
      # 挂载日志目录
      - ./logs/python-service:/app/logs
    networks:
      - open-platform-network
    dns:
      - *********
      - *********
    environment:
      # 增加超时设置
      - TIMEOUT=300
      # 增加OCR处理超时
      - OCR_TIMEOUT=180
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8866/health"]
      # 增加健康检查间隔，避免频繁重启
      interval: 30s
      # 增加超时时间，允许处理大图像
      timeout: 180s
      # 增加重试次数
      retries: 3
      # 增加启动时间，确保模型完全加载
      start_period: 600s

# 添加这个网络定义部分
networks:
  open-platform-network:
    driver: bridge