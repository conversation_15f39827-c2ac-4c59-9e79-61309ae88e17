# 调用记录模块 (Call Record Module)

## 概述

调用记录模块负责管理系统中所有API调用的记录，包括调用的输入输出、消耗次数、金额、成功状态等信息。该模块提供完整的CRUD操作、统计分析和数据导出功能。

## 主要功能

### 1. 记录管理
- **调用记录创建**：记录每次API调用的详细信息
- **记录查询**：支持多维度查询和筛选
- **记录更新**：更新调用结果和响应信息
- **记录删除**：支持单个和批量删除

### 2. 统计分析
- **基础统计**：总调用次数、成功率、失败率
- **消耗统计**：总消耗次数、总金额、平均响应时间
- **分组统计**：按服务、用户、时间、HTTP状态码分组
- **趋势分析**：按小时统计调用趋势

### 3. 数据查询
- **灵活筛选**：支持按用户、服务、时间范围、成功状态等筛选
- **性能筛选**：支持按响应时间、消耗次数、金额范围筛选
- **分页查询**：支持大数据量的分页查询
- **排序功能**：支持多字段排序

## API 接口

### 基础 CRUD

#### 创建调用记录
```http
POST /call-record
```

#### 查询记录列表
```http
GET /call-record?page=1&limit=10&userId=1&serviceId=1
```

#### 查询单个记录
```http
GET /call-record/:id
```

#### 根据请求ID查询
```http
GET /call-record/request/:requestId
```

#### 更新记录
```http
PATCH /call-record/:id
```

#### 删除记录
```http
DELETE /call-record/:id
```

### 批量操作

#### 批量删除记录
```http
DELETE /call-record/batch/remove
```

### 统计功能

#### 获取统计数据
```http
GET /call-record/stats?startDate=2024-01-01&endDate=2024-01-31
```

## 数据模型

### CallRecordEntity
```typescript
{
  id: number;                    // 记录ID
  requestId: string;             // 唯一请求ID
  input: string;                 // 请求输入
  output?: string;               // 响应输出
  cost: number;                  // 消耗次数
  amount: number;                // 调用金额
  success: boolean;              // 是否成功
  errorMessage?: string;         // 错误信息
  responseTime?: number;         // 响应时间(ms)
  httpStatus?: number;           // HTTP状态码
  clientIp?: string;             // 客户端IP
  userAgent?: string;            // User-Agent
  requestHeaders?: object;       // 请求头
  responseHeaders?: object;      // 响应头
  user: UserEntity;              // 关联用户
  service: ServiceEntity;        // 关联服务
  createdAt: Date;               // 创建时间
  updatedAt: Date;               // 更新时间
}
```

### 查询参数
```typescript
{
  page?: number;                 // 页码
  limit?: number;                // 每页数量
  userId?: number;               // 用户ID
  serviceId?: number;            // 服务ID
  requestId?: string;            // 请求ID搜索
  success?: boolean;             // 是否成功
  minCost?: number;              // 最小消耗次数
  maxCost?: number;              // 最大消耗次数
  minAmount?: number;            // 最小金额
  maxAmount?: number;            // 最大金额
  minResponseTime?: number;      // 最小响应时间
  maxResponseTime?: number;      // 最大响应时间
  httpStatus?: number;           // HTTP状态码
  clientIp?: string;             // 客户端IP
  startDate?: string;            // 开始时间
  endDate?: string;              // 结束时间
  sortBy?: string;               // 排序字段
  sortOrder?: 'ASC' | 'DESC';    // 排序方向
}
```

## 业务规则

### 1. 数据验证
- 请求ID必须唯一
- 用户ID和服务ID必须存在
- 消耗次数和金额不能为负数
- HTTP状态码范围：100-599

### 2. 关联检查
- 创建记录前验证用户和服务是否存在
- 确保请求ID的唯一性
- 支持软删除，保留历史数据

### 3. 统计计算
- 成功率 = 成功调用次数 / 总调用次数 × 100%
- 平均响应时间基于有效的响应时间数据
- 分组统计支持多维度聚合

## 依赖关系

### 模块依赖
- **UserModule**：获取用户信息
- **ServiceModule**：获取服务信息
- **TypeOrmModule**：数据库操作

### 服务依赖
- **UserService**：用户数据验证
- **ServiceService**：服务数据验证
- **Repository<CallRecordEntity>**：数据库操作

## 错误处理

### 常见错误
- `404 Not Found`：记录、用户或服务不存在
- `409 Conflict`：请求ID已存在
- `400 Bad Request`：参数验证失败

### 错误响应格式
```json
{
  "statusCode": 404,
  "message": "调用记录不存在",
  "error": "Not Found"
}
```

## 日志记录

### 操作日志
- 记录创建、更新、删除操作
- 记录批量操作的数量
- 记录关键业务操作的结果

### 日志格式
```
[CallRecordService] 创建调用记录成功: 请求ID=req_123, 用户ID=1, 服务ID=1
[CallRecordService] 更新调用记录成功: ID=123
[CallRecordService] 批量删除调用记录成功: 数量=5
```

## 使用示例

### 创建调用记录
```typescript
const createDto: CreateCallRecordDto = {
  userId: 1,
  serviceId: 1,
  requestId: 'req_1234567890',
  input: '{"text": "Hello World"}',
  output: '{"result": "Hello World processed"}',
  cost: 1,
  amount: 0.10,
  success: true,
  responseTime: 1500,
  httpStatus: 200
};

const record = await callRecordService.create(createDto);
```

### 查询记录列表
```typescript
const queryDto: QueryCallRecordDto = {
  page: 1,
  limit: 20,
  userId: 1,
  success: true,
  startDate: '2024-01-01',
  endDate: '2024-01-31',
  sortBy: 'createdAt',
  sortOrder: 'DESC'
};

const result = await callRecordService.findAll(queryDto);
```

### 获取统计数据
```typescript
const statsDto: QueryCallRecordDto = {
  startDate: '2024-01-01',
  endDate: '2024-01-31'
};

const stats = await callRecordService.getStats(statsDto);
console.log(`总调用次数: ${stats.totalCalls}`);
console.log(`成功率: ${stats.successRate}%`);
```

## 注意事项

### 1. 性能优化
- 大数据量查询时建议使用分页
- 统计查询可能较慢，建议异步处理
- 考虑对常用查询字段建立索引

### 2. 数据安全
- 敏感信息（如请求头）需要谨慎处理
- 支持软删除，避免数据丢失
- 定期清理过期的调用记录

### 3. 扩展性
- 支持自定义字段扩展
- 统计维度可根据需求调整
- 支持数据导出功能扩展