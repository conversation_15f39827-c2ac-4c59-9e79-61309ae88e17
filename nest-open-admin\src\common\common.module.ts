import { Module, Global, forwardRef } from '@nestjs/common';
import { UnifiedAuthGuard } from './guards/unified-auth.guard';
import { UnifiedInterceptor } from './interceptors/unified.interceptor';
import { GlobalExceptionFilter } from './filters/global-exception.filter';
import { SharedModule } from '../shared/shared.module';
import { ApiKeyModule } from '../modules/api-key/api-key.module';

@Global()
@Module({
  imports: [
    SharedModule,
    forwardRef(() => ApiKeyModule),
  ],
  providers: [
    UnifiedAuthGuard,
    UnifiedInterceptor,
    GlobalExceptionFilter,
  ],
  exports: [
    UnifiedAuthGuard,
    UnifiedInterceptor,
    GlobalExceptionFilter,
  ],
})
export class CommonModule {} 