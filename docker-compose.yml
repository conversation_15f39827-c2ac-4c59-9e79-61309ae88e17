version: '3.8'

services:
  # MySQL服务
  mysql:
    image: mysql:5.7
    container_name: open-platform-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-4iF6TSENcSaH8pSz}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-openapidb}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./my.cnf:/etc/mysql/conf.d/my.cnf
    ports:
      - "3306:3306"
    networks:
      - open-platform-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-4iF6TSENcSaH8pSz}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis服务
  redis:
    image: redis:7.0
    container_name: open-platform-redis
    restart: always
    command: redis-server --requirepass ${REDIS_PASSWORD:-BamsxmSesxBDBDEX}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - open-platform-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-BamsxmSesxBDBDEX}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Python服务
  python-service:
    build: ./python-service
    container_name: open-platform-python
    restart: always  # 修改为always，确保服务崩溃后自动重启
    ports:
      - "8866:8866"
    volumes:
      - ./python-service:/app
      # 挂载日志目录
      - ./logs/python-service:/app/logs
    networks:
      - open-platform-network
    dns:
      - *********
      - *********
    environment:
      # 增加超时设置
      - TIMEOUT=300
      # 增加OCR处理超时
      - OCR_TIMEOUT=180
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8866/health"]
      # 增加健康检查间隔，避免频繁重启
      interval: 30s
      # 增加超时时间，允许处理大图像
      timeout: 180s
      # 增加重试次数
      retries: 3
      # 增加启动时间，确保模型完全加载
      start_period: 600s

  # NestJS后端服务
  nest-backend:
    build:
      context: ./nest-open-admin
    container_name: open-platform-backend
    restart: always
    volumes:
      - ./logs/nest-backend:/app/logs
    ports:
      - "8088:8088"
    networks:
      - open-platform-network
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      python-service:
        condition: service_started
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD:-4iF6TSENcSaH8pSz}
      - DB_DATABASE=${MYSQL_DATABASE:-openapidb}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-BamsxmSesxBDBDEX}
      - PYTHON_SERVICE_URL=http://python-service:8866
      - OCR_SERVICE_URL=http://python-service:8866
      - ADDRESS_SERVICE_URL=http://python-service:8866
      - GEO_SERVICE_URL=http://python-service:8866

networks:
  open-platform-network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
    driver_opts:
      type: none
      device: ./data/mysql
      o: bind
  redis_data:
    driver: local
    driver_opts:
      type: none
      device: ./data/redis
      o: bind
