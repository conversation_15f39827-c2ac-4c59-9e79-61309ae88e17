import { DataSource, Repository, ObjectLiteral } from 'typeorm';
import { ISeed, SeedConfig, SeedResult } from './seed.interface';
import { Logger } from '@nestjs/common';

/**
 * 种子数据基础类
 * 提供通用的种子数据执行逻辑
 */
export abstract class BaseSeed implements ISeed {
  protected readonly logger = new Logger(this.constructor.name);
  
  /** 种子数据配置 */
  abstract config: SeedConfig;
  
  /**
   * 执行种子数据的具体实现
   * 子类必须实现此方法
   */
  protected abstract execute(dataSource: DataSource): Promise<SeedResult>;

  /**
   * 检查种子数据是否需要执行（可选实现）
   */
  shouldRun?(dataSource: DataSource): Promise<boolean>;

  /**
   * 执行种子数据
   */
  async run(dataSource: DataSource): Promise<SeedResult> {
    const startTime = Date.now();
    
    try {
      this.logger.log(`开始执行种子数据: ${this.config.name}`);
      
      // 检查是否需要执行
      if (await this.shouldSkip(dataSource)) {
        return {
          success: true,
          message: '种子数据已存在，跳过执行',
          executionTime: Date.now() - startTime,
        };
      }
      
      // 执行种子数据
      const result = await this.execute(dataSource);
      
      // 记录执行状态（简化版，不创建额外表）
      this.logger.log(`种子数据 ${this.config.name} 执行成功`);
      
      this.logger.log(`种子数据执行完成: ${this.config.name}, 耗时: ${result.executionTime}ms`);
      
      return {
        ...result,
        executionTime: Date.now() - startTime,
      };
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`种子数据执行失败: ${this.config.name}, 错误: ${errorMessage}`);
      
      return {
        success: false,
        message: '种子数据执行失败',
        error: errorMessage,
        executionTime: Date.now() - startTime,
      };
    }
  }
  
  /**
   * 回滚种子数据（默认实现）
   */
  async revert(dataSource: DataSource): Promise<SeedResult> {
    this.logger.warn(`种子数据 ${this.config.name} 不支持回滚操作`);
    return {
      success: false,
      message: '不支持回滚操作',
    };
  }
  
  /**
   * 检查是否应该跳过执行
   */
  protected async shouldSkip(dataSource: DataSource): Promise<boolean> {
    if (this.config.force) {
      return false;
    }
    
    if (this.shouldRun) {
      return !(await this.shouldRun(dataSource));
    }
    
    // 默认检查：查看执行记录表
    return await this.hasBeenExecuted(dataSource);
  }
  
  /**
   * 检查种子数据是否已执行（简化版）
   * 子类可以重写此方法实现自己的检查逻辑
   */
  protected async hasBeenExecuted(dataSource: DataSource): Promise<boolean> {
    // 默认返回false，让子类的shouldRun方法来决定
    return false;
  }
  

  
  /**
   * 获取仓库实例
   */
  protected getRepository<T extends ObjectLiteral>(dataSource: DataSource, entity: new () => T): Repository<T> {
    return dataSource.getRepository(entity);
  }
  
  /**
   * 执行原生SQL查询
   */
  protected async query(dataSource: DataSource, sql: string, parameters?: any[]): Promise<any> {
    return await dataSource.query(sql, parameters);
  }
  
  /**
   * 检查记录是否存在
   */
  protected async exists<T extends ObjectLiteral>(
    dataSource: DataSource,
    entity: new () => T,
    conditions: Record<string, any>
  ): Promise<boolean> {
    const repository = this.getRepository(dataSource, entity);
    const count = await repository.count({ where: conditions });
    return count > 0;
  }
  
  /**
   * 插入或更新记录（幂等操作）
   */
  protected async upsert<T extends ObjectLiteral>(
    dataSource: DataSource,
    entity: new () => T,
    data: Partial<T>,
    uniqueFields: (keyof T)[]
  ): Promise<T> {
    const repository = this.getRepository(dataSource, entity);
    
    // 构建查询条件
    const whereConditions = {};
    uniqueFields.forEach(field => {
      if (data[field] !== undefined) {
        whereConditions[field as string] = data[field];
      }
    });
    
    // 查找现有记录
    let existingRecord = await repository.findOne({ where: whereConditions });
    
    if (existingRecord) {
      // 更新现有记录
      await repository.update(whereConditions, data as any);
      const updatedRecord = await repository.findOne({ where: whereConditions });
      return updatedRecord!;
    } else {
      // 创建新记录
      const newRecord = repository.create(data as any);
      const savedRecord = await repository.save(newRecord);
      return Array.isArray(savedRecord) ? savedRecord[0] : savedRecord;
    }
  }
}
