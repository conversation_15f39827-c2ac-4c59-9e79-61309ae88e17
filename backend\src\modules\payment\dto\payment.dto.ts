import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsEnum,
  IsNumber,
  IsString,
  IsOptional,
  IsPositive,
  Min,
  Max,
  Length,
  IsDateString,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PaymentMethod, PaymentStatus } from '../../order/enums/order.enum';

/**
 * 创建支付DTO
 */
export class CreatePaymentDto {
  @ApiProperty({ description: '订单ID', example: 1 })
  @IsNumber()
  @IsPositive()
  orderId: number;

  @ApiProperty({ description: '支付方式', enum: PaymentMethod, example: PaymentMethod.ALIPAY })
  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;

  @ApiProperty({ description: '支付金额', example: 100.00 })
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  amount: number;

  @ApiPropertyOptional({ description: '支付配置参数' })
  @IsOptional()
  @IsObject()
  paymentConfig?: Record<string, any>;

  @ApiPropertyOptional({ description: '回调地址', example: 'https://example.com/callback' })
  @IsOptional()
  @IsString()
  @Length(1, 500)
  callbackUrl?: string;

  @ApiPropertyOptional({ description: '返回地址', example: 'https://example.com/return' })
  @IsOptional()
  @IsString()
  @Length(1, 500)
  returnUrl?: string;
}

/**
 * 支付回调DTO
 */
export class PaymentCallbackDto {
  @ApiProperty({ description: '支付单号', example: 'PAY202312010001' })
  @IsString()
  paymentNo: string;

  @ApiProperty({ description: '第三方支付单号', example: '2023120122001234567890123456' })
  @IsString()
  thirdPartyNo: string;

  @ApiProperty({ description: '支付状态', enum: PaymentStatus })
  @IsEnum(PaymentStatus)
  status: PaymentStatus;

  @ApiPropertyOptional({ description: '支付时间', example: '2023-12-01T10:30:00Z' })
  @IsOptional()
  @IsDateString()
  paidAt?: string;

  @ApiPropertyOptional({ description: '失败原因', example: '余额不足' })
  @IsOptional()
  @IsString()
  @Length(0, 500)
  failureReason?: string;

  @ApiPropertyOptional({ description: '回调数据' })
  @IsOptional()
  @IsObject()
  callbackData?: Record<string, any>;
}

/**
 * 查询支付DTO
 */
export class QueryPaymentDto {
  @ApiPropertyOptional({ description: '页码', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', example: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({ description: '用户ID', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  userId?: number;

  @ApiPropertyOptional({ description: '订单ID', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  orderId?: number;

  @ApiPropertyOptional({ description: '支付单号', example: 'PAY202312010001' })
  @IsOptional()
  @IsString()
  paymentNo?: string;

  @ApiPropertyOptional({ description: '第三方支付单号', example: '2023120122001234567890123456' })
  @IsOptional()
  @IsString()
  thirdPartyNo?: string;

  @ApiPropertyOptional({ description: '支付方式', enum: PaymentMethod })
  @IsOptional()
  @IsEnum(PaymentMethod)
  paymentMethod?: PaymentMethod;

  @ApiPropertyOptional({ description: '支付状态', enum: PaymentStatus })
  @IsOptional()
  @IsEnum(PaymentStatus)
  status?: PaymentStatus;

  @ApiPropertyOptional({ description: '开始时间', example: '2023-12-01T00:00:00Z' })
  @IsOptional()
  @IsDateString()
  startTime?: string;

  @ApiPropertyOptional({ description: '结束时间', example: '2023-12-31T23:59:59Z' })
  @IsOptional()
  @IsDateString()
  endTime?: string;
}

/**
 * 更新支付DTO
 */
export class UpdatePaymentDto extends PartialType(CreatePaymentDto) {
  @ApiPropertyOptional({ description: '支付状态', enum: PaymentStatus })
  @IsOptional()
  @IsEnum(PaymentStatus)
  status?: PaymentStatus;

  @ApiPropertyOptional({ description: '第三方支付单号', example: '2023120122001234567890123456' })
  @IsOptional()
  @IsString()
  thirdPartyNo?: string;

  @ApiPropertyOptional({ description: '支付时间', example: '2023-12-01T10:30:00Z' })
  @IsOptional()
  @IsDateString()
  paidAt?: string;

  @ApiPropertyOptional({ description: '失败原因', example: '余额不足' })
  @IsOptional()
  @IsString()
  @Length(0, 500)
  failureReason?: string;

  @ApiPropertyOptional({ description: '支付数据' })
  @IsOptional()
  @IsObject()
  paymentData?: Record<string, any>;

  @ApiPropertyOptional({ description: '回调数据' })
  @IsOptional()
  @IsObject()
  callbackData?: Record<string, any>;
}

/**
 * 支付响应DTO
 */
export class PaymentResponseDto {
  @ApiProperty({ description: '支付ID', example: 1 })
  id: number;

  @ApiProperty({ description: '订单ID', example: 1 })
  orderId: number;

  @ApiProperty({ description: '用户ID', example: 1 })
  userId: number;

  @ApiProperty({ description: '支付单号', example: 'PAY202312010001' })
  paymentNo: string;

  @ApiPropertyOptional({ description: '第三方支付单号', example: '2023120122001234567890123456' })
  thirdPartyNo?: string;

  @ApiProperty({ description: '支付方式', enum: PaymentMethod, example: PaymentMethod.ALIPAY })
  paymentMethod: PaymentMethod;

  @ApiProperty({ description: '支付状态', enum: PaymentStatus, example: PaymentStatus.PENDING })
  status: PaymentStatus;

  @ApiProperty({ description: '支付金额', example: 100.00 })
  amount: number;

  @ApiPropertyOptional({ description: '支付数据' })
  paymentData?: Record<string, any>;

  @ApiPropertyOptional({ description: '支付时间', example: '2023-12-01T10:30:00Z' })
  paidAt?: Date;

  @ApiPropertyOptional({ description: '失败原因', example: '余额不足' })
  failureReason?: string;

  @ApiPropertyOptional({ description: '回调数据' })
  callbackData?: Record<string, any>;

  @ApiProperty({ description: '创建时间', example: '2023-12-01T10:00:00Z' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间', example: '2023-12-01T10:30:00Z' })
  updatedAt: Date;

  // 计算属性
  @ApiProperty({ description: '是否支付成功', example: false })
  isSuccess: boolean;

  @ApiProperty({ description: '是否支付失败', example: false })
  isFailed: boolean;

  @ApiProperty({ description: '是否可以重试', example: true })
  canRetry: boolean;
}

/**
 * 支付列表响应DTO
 */
export class PaymentListResponseDto {
  @ApiProperty({ description: '支付列表', type: [PaymentResponseDto] })
  items: PaymentResponseDto[];

  @ApiProperty({ description: '分页信息' })
  meta: {
    page: number;
    limit: number;
    totalItems: number;
    totalPages: number;
  };
}

/**
 * 支付结果DTO
 */
export class PaymentResultDto {
  @ApiProperty({ description: '支付ID', example: 1 })
  paymentId: number;

  @ApiProperty({ description: '支付单号', example: 'PAY202312010001' })
  paymentNo: string;

  @ApiProperty({ description: '支付方式', enum: PaymentMethod, example: PaymentMethod.ALIPAY })
  paymentMethod: PaymentMethod;

  @ApiProperty({ description: '支付金额', example: 100.00 })
  amount: number;

  @ApiPropertyOptional({ description: '支付链接', example: 'https://openapi.alipay.com/gateway.do?...' })
  paymentUrl?: string;

  @ApiPropertyOptional({ description: '二维码内容', example: 'weixin://wxpay/bizpayurl?pr=xxx' })
  qrCode?: string;

  @ApiPropertyOptional({ description: '支付参数' })
  paymentParams?: Record<string, any>;

  @ApiProperty({ description: '过期时间', example: '2023-12-01T10:30:00Z' })
  expiresAt: Date;
}
