import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { OrderEntity } from '../entities/order.entity';
import { OrderItemEntity } from '../entities/order-item.entity';
import { UserService } from '../../user/user.service';
import {
  CreateRechargeOrderDto,
  RechargeConfigDto,
  RechargeStatsDto,
  UserRechargeRecordDto,
} from '../dto/recharge.dto';
import { OrderType, OrderStatus, PaymentMethod } from '../enums/order.enum';

@Injectable()
export class RechargeService {
  private readonly logger = new Logger(RechargeService.name);

  constructor(
    @InjectRepository(OrderEntity)
    private readonly orderRepository: Repository<OrderEntity>,
    @InjectRepository(OrderItemEntity)
    private readonly orderItemRepository: Repository<OrderItemEntity>,
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * 创建充值订单
   */
  async createRechargeOrder(
    userId: number,
    createRechargeOrderDto: CreateRechargeOrderDto,
  ): Promise<OrderEntity> {
    try {
      this.logger.log(`用户 ${userId} 创建充值订单，金额: ${createRechargeOrderDto.amount}`);

      // 验证用户是否存在
      const user = await this.userService.findById(userId);
      if (!user) {
        throw new BadRequestException('用户不存在');
      }

      // 验证充值金额
      this.validateRechargeAmount(createRechargeOrderDto.amount);

      // 创建充值订单
      const order = this.orderRepository.create({
        userId,
        orderType: OrderType.RECHARGE,
        status: OrderStatus.PENDING,
        totalAmount: createRechargeOrderDto.amount,
        remark: createRechargeOrderDto.remark || '账户充值',
        expiresAt: new Date(Date.now() + 30 * 60 * 1000), // 30分钟后过期
      });

      const savedOrder = await this.orderRepository.save(order);

      // 创建订单项
      const orderItem = this.orderItemRepository.create({
        orderId: savedOrder.id,
        itemName: '账户充值',
        itemDescription: `充值金额: ${createRechargeOrderDto.amount}元`,
        unitPrice: createRechargeOrderDto.amount,
        quantity: 1,
        totalPrice: createRechargeOrderDto.amount,
        itemConfig: {
          rechargeType: 'balance',
          paymentMethod: createRechargeOrderDto.paymentMethod,
        },
      });

      await this.orderItemRepository.save(orderItem);

      this.logger.log(`充值订单创建成功: ${savedOrder.orderNo}`);

      // 发送订单创建事件
      this.eventEmitter.emit('order.created', {
        orderId: savedOrder.id,
        userId,
        orderType: OrderType.RECHARGE,
        totalAmount: createRechargeOrderDto.amount,
      });

      return savedOrder;
    } catch (error) {
      this.logger.error(`创建充值订单失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取充值配置
   */
  async getRechargeConfig(): Promise<RechargeConfigDto> {
    return {
      presetAmounts: [10, 50, 100, 200, 500, 1000],
      minAmount: 1,
      maxAmount: 10000,
      promotions: [
        {
          amount: 100,
          bonus: 10,
          description: '充值100元送10元',
        },
        {
          amount: 500,
          bonus: 60,
          description: '充值500元送60元',
        },
        {
          amount: 1000,
          bonus: 150,
          description: '充值1000元送150元',
        },
      ],
    };
  }

  /**
   * 获取用户充值记录
   */
  async getUserRechargeRecords(
    userId: number,
    page: number = 1,
    limit: number = 20,
  ): Promise<{
    items: UserRechargeRecordDto[];
    meta: {
      page: number;
      limit: number;
      totalItems: number;
      totalPages: number;
    };
  }> {
    const skip = (page - 1) * limit;

    const [orders, totalItems] = await this.orderRepository
      .createQueryBuilder('order')
      .where('order.userId = :userId', { userId })
      .andWhere('order.orderType = :orderType', { orderType: OrderType.RECHARGE })
      .orderBy('order.createdAt', 'DESC')
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    const items = orders.map(order => ({
      orderId: order.id,
      orderNo: order.orderNo,
      amount: order.totalAmount,
      paymentMethod: order.paymentMethod as PaymentMethod,
      status: order.status,
      createdAt: order.createdAt,
      completedAt: order.status === OrderStatus.COMPLETED ? order.updatedAt : undefined,
      remark: order.remark,
    }));

    return {
      items,
      meta: {
        page,
        limit,
        totalItems,
        totalPages: Math.ceil(totalItems / limit),
      },
    };
  }

  /**
   * 获取充值统计
   */
  async getRechargeStats(): Promise<RechargeStatsDto> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // 基础统计
    const [
      totalRecharges,
      totalAmountResult,
      todayRechargesResult,
      todayAmountResult,
    ] = await Promise.all([
      this.orderRepository.count({
        where: {
          orderType: OrderType.RECHARGE,
          status: OrderStatus.COMPLETED,
        },
      }),
      this.orderRepository
        .createQueryBuilder('order')
        .select('SUM(order.totalAmount)', 'total')
        .where('order.orderType = :orderType', { orderType: OrderType.RECHARGE })
        .andWhere('order.status = :status', { status: OrderStatus.COMPLETED })
        .getRawOne(),
      this.orderRepository.count({
        where: {
          orderType: OrderType.RECHARGE,
          status: OrderStatus.COMPLETED,
          createdAt: Between(today, tomorrow),
        },
      }),
      this.orderRepository
        .createQueryBuilder('order')
        .select('SUM(order.totalAmount)', 'total')
        .where('order.orderType = :orderType', { orderType: OrderType.RECHARGE })
        .andWhere('order.status = :status', { status: OrderStatus.COMPLETED })
        .andWhere('order.createdAt BETWEEN :start AND :end', { start: today, end: tomorrow })
        .getRawOne(),
    ]);

    const totalAmount = parseFloat(totalAmountResult?.total) || 0;
    const todayAmount = parseFloat(todayAmountResult?.total) || 0;
    const averageAmount = totalRecharges > 0 ? totalAmount / totalRecharges : 0;

    // 按支付方式统计
    const paymentMethodStats = await this.orderRepository
      .createQueryBuilder('order')
      .select('order.paymentMethod', 'paymentMethod')
      .addSelect('COUNT(*)', 'count')
      .addSelect('SUM(order.totalAmount)', 'amount')
      .where('order.orderType = :orderType', { orderType: OrderType.RECHARGE })
      .andWhere('order.status = :status', { status: OrderStatus.COMPLETED })
      .andWhere('order.paymentMethod IS NOT NULL')
      .groupBy('order.paymentMethod')
      .getRawMany();

    const byPaymentMethod = {};
    paymentMethodStats.forEach(stat => {
      byPaymentMethod[stat.paymentMethod] = {
        count: parseInt(stat.count),
        amount: parseFloat(stat.amount) || 0,
      };
    });

    // 按金额区间统计
    const amountRangeStats = await this.orderRepository
      .createQueryBuilder('order')
      .select('order.totalAmount', 'amount')
      .where('order.orderType = :orderType', { orderType: OrderType.RECHARGE })
      .andWhere('order.status = :status', { status: OrderStatus.COMPLETED })
      .getRawMany();

    const byAmountRange = {
      '1-50': { count: 0, amount: 0 },
      '51-100': { count: 0, amount: 0 },
      '101-500': { count: 0, amount: 0 },
      '501+': { count: 0, amount: 0 },
    };

    amountRangeStats.forEach(stat => {
      const amount = parseFloat(stat.amount);
      if (amount <= 50) {
        byAmountRange['1-50'].count++;
        byAmountRange['1-50'].amount += amount;
      } else if (amount <= 100) {
        byAmountRange['51-100'].count++;
        byAmountRange['51-100'].amount += amount;
      } else if (amount <= 500) {
        byAmountRange['101-500'].count++;
        byAmountRange['101-500'].amount += amount;
      } else {
        byAmountRange['501+'].count++;
        byAmountRange['501+'].amount += amount;
      }
    });

    return {
      totalRecharges,
      totalAmount,
      todayRecharges: todayRechargesResult,
      todayAmount,
      averageAmount,
      byPaymentMethod,
      byAmountRange,
    };
  }

  /**
   * 验证充值金额
   */
  private validateRechargeAmount(amount: number): void {
    if (amount < 1) {
      throw new BadRequestException('充值金额不能少于1元');
    }
    if (amount > 10000) {
      throw new BadRequestException('单次充值金额不能超过10000元');
    }
    // 验证金额精度（最多2位小数）
    if (!/^\d+(\.\d{1,2})?$/.test(amount.toString())) {
      throw new BadRequestException('充值金额格式不正确，最多支持2位小数');
    }
  }
}
