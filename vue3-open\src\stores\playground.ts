import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { request } from '@/utils/request'
import { useServiceStore } from './service'
import { useApiKeyStore } from './apiKey'
import type { ServiceType } from '@/types/service'

// API配置接口
export interface ApiConfig {
  url: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  apiKey: string
  headers: { key: string; value: string }[]
  params: { key: string; value: string; type: 'string' | 'number' | 'boolean' | 'file' }[]
  body: string
  bodyType: 'json' | 'form-data' | 'x-www-form-urlencoded'
}

// 请求历史记录
export interface RequestHistory {
  id: string
  timestamp: string
  serviceId: string
  serviceName: string
  config: ApiConfig
  response: {
    status: number
    statusText: string
    headers: Record<string, string>
    data: unknown
    duration: number
  } | null
  error?: string
}

// 代码生成语言
export type CodeLanguage = 'curl' | 'javascript' | 'python' | 'java' | 'php' | 'go'

// 服务示例
export interface ServiceExample {
  name: string
  description: string
  config: Partial<ApiConfig>
}

export const usePlaygroundStore = defineStore('playground', () => {
  // 状态
  const isLoading = ref(false)
  const loading = ref(false) // 向后兼容
  const selectedService = ref<any>(null)
  const currentService = ref<string>('') // 向后兼容
  
  // 新的请求配置结构
  const requestConfig = ref({
    method: 'POST' as 'GET' | 'POST' | 'PUT' | 'DELETE',
    url: '',
    headers: {} as Record<string, string>,
    body: ''
  })
  
  // 向后兼容的API配置
  const apiConfig = ref<ApiConfig>({
    url: '',
    method: 'POST',
    apiKey: '',
    headers: [
      { key: 'Content-Type', value: 'application/json' },
      { key: 'Accept', value: 'application/json' }
    ],
    params: [],
    body: '',
    bodyType: 'json'
  })
  
  const requestHistory = ref<RequestHistory[]>([])
  const selectedApiKey = ref<string>('')
  const showResponse = ref(false)
  const currentResponse = ref<RequestHistory['response']>(null)
  const currentError = ref<string>('')
  const apiKeys = ref<any[]>([])

  // 获取其他store
  const serviceStore = useServiceStore()
  const apiKeyStore = useApiKeyStore()

  // 计算属性
  const availableServices = computed(() => {
    return serviceStore.services?.filter(service => service.serviceStatus === 'active') || []
  })

  const availableApiKeys = computed(() => {
    return apiKeyStore.apiKeys?.filter(key => key.keyStatus === 'active') || []
  })

  const currentServiceInfo = computed(() => {
    return availableServices.value.find(service => service.id === currentService.value)
  })

  const canSendRequest = computed(() => {
    return apiConfig.value.url && apiConfig.value.apiKey && !loading.value
  })

  // 切换服务，支持id和code
  const switchService = (serviceKey: string | number) => {
    let service = availableServices.value.find(s => s.id === serviceKey || s.code === serviceKey)
    if (!service && typeof serviceKey === 'string') {
      // 兼容字符串id
      service = availableServices.value.find(s => String(s.id) === serviceKey)
    }
    if (service) {
      currentService.value = String(service.id)
      selectedService.value = service
      apiConfig.value.url = service.endpoint || ''
      requestConfig.value.url = service.endpoint || ''
      loadServiceExample(service.code || String(service.id))
    }
  }
  
  // 新的方法：更新请求配置
  const updateRequestConfig = (updates: Partial<typeof requestConfig.value>) => {
    Object.assign(requestConfig.value, updates)
    // 同步到旧的配置结构
    if (updates.method) apiConfig.value.method = updates.method
    if (updates.url) apiConfig.value.url = updates.url
    if (updates.body) apiConfig.value.body = updates.body
    if (updates.headers) {
      apiConfig.value.headers = Object.entries(updates.headers).map(([key, value]) => ({ key, value }))
    }
  }
  
  // 选择API密钥（新版本）
  const selectApiKey = (apiKeyId: string) => {
    const apiKey = availableApiKeys.value.find(key => String(key.id) === apiKeyId)
    if (apiKey) {
      selectedApiKey.value = apiKeyId
      apiConfig.value.apiKey = apiKey.apiKey
      requestConfig.value.headers = {
        ...requestConfig.value.headers,
        'Authorization': `Bearer ${apiKey.apiKey}`
      }
    }
  }
  
  // 加载示例
  const loadExample = () => {
    if (currentService.value) {
      loadServiceExample(currentService.value)
    }
  }
  
  // 重置配置（新版本）
  const resetConfigNew = () => {
    requestConfig.value = {
      method: 'POST',
      url: selectedService.value?.endpoint || '',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: ''
    }
    
    // 同步到旧配置
    apiConfig.value = {
      url: selectedService.value?.endpoint || '',
      method: 'POST',
      apiKey: '',
      headers: [
        { key: 'Content-Type', value: 'application/json' },
        { key: 'Accept', value: 'application/json' }
      ],
      params: [],
      body: '',
      bodyType: 'json'
    }
    
    selectedApiKey.value = ''
    showResponse.value = false
    currentResponse.value = null
    currentError.value = ''
  }
  
  // 清空当前响应
  const clearCurrentResponse = () => {
    currentResponse.value = null
    currentError.value = ''
    showResponse.value = false
  }
  
  // 删除历史记录项
  const deleteHistoryRecord = (index: number) => {
    requestHistory.value.splice(index, 1)
  }
  
  // 初始化（新版本）
  const init = async () => {
    try {
      isLoading.value = true
      await Promise.all([
        serviceStore.fetchServices(),
        apiKeyStore.getApiKeysByUserId(0) // 使用默认用户ID 0，实际使用时应该传入当前用户ID
      ])
      
      // 同步API密钥数据
      apiKeys.value = availableApiKeys.value
      
      // 设置默认服务
      if (availableServices.value.length > 0) {
        switchService(availableServices.value[0].id.toString())
      }
    } catch (error) {
      console.error('初始化playground失败:', error)
    } finally {
      isLoading.value = false
    }
  }

  // 加载服务示例
  const loadServiceExample = (serviceId: string) => {
    const examples: Record<string, ServiceExample> = {
      'ocr-general': {
        name: 'OCR通用识别',
        description: '识别图片中的文字内容',
        config: {
          url: '/api/ocr/general',
          method: 'POST',
          body: JSON.stringify({
            image: 'base64_encoded_image_data',
            options: {
              language: 'auto',
              output_format: 'text'
            }
          }, null, 2),
          bodyType: 'json'
        }
      },
      'ocr-table': {
        name: 'OCR表格识别',
        description: '识别表格结构和内容',
        config: {
          url: '/api/ocr/table',
          method: 'POST',
          body: JSON.stringify({
            image: 'base64_encoded_image_data',
            options: {
              return_structure: true,
              output_format: 'json'
            }
          }, null, 2),
          bodyType: 'json'
        }
      },
      'nlp-sentiment': {
        name: 'NLP情感分析',
        description: '分析文本的情感倾向',
        config: {
          url: '/api/nlp/sentiment',
          method: 'POST',
          body: JSON.stringify({
            text: '这是一个很棒的产品，我非常喜欢！',
            options: {
              language: 'zh',
              detailed: true
            }
          }, null, 2),
          bodyType: 'json'
        }
      }
    }

    const example = examples[serviceId]
    if (example) {
      Object.assign(apiConfig.value, example.config)
    }
  }


  // 添加请求头
  const addHeader = () => {
    apiConfig.value.headers.push({ key: '', value: '' })
  }

  // 删除请求头
  const removeHeader = (index: number) => {
    apiConfig.value.headers.splice(index, 1)
  }

  // 添加参数
  const addParam = () => {
    apiConfig.value.params.push({ key: '', value: '', type: 'string' })
  }

  // 删除参数
  const removeParam = (index: number) => {
    apiConfig.value.params.splice(index, 1)
  }

  // 发送请求
  const sendRequest = async () => {
    if (!canSendRequest.value) {
      ElMessage.warning('请完善请求配置')
      return
    }

    loading.value = true
    showResponse.value = true
    currentError.value = ''
    
    const startTime = Date.now()
    
    try {
      // 构建请求配置
      const requestConfig: Record<string, any> = {
        method: apiConfig.value.method,
        url: apiConfig.value.url,
        headers: {
          'Authorization': `Bearer ${apiConfig.value.apiKey}`,
          ...Object.fromEntries(
            apiConfig.value.headers
              .filter(h => h.key && h.value)
              .map(h => [h.key, h.value])
          )
        }
      }

      // 添加请求参数
      if (apiConfig.value.params.length > 0) {
        const params = Object.fromEntries(
          apiConfig.value.params
            .filter(p => p.key && p.value)
            .map(p => [p.key, p.value])
        )
        if (apiConfig.value.method === 'GET') {
          requestConfig.params = params
        }
      }

      // 添加请求体
      if (apiConfig.value.method !== 'GET' && apiConfig.value.body) {
        if (apiConfig.value.bodyType === 'json') {
          try {
            requestConfig.data = JSON.parse(apiConfig.value.body)
          } catch (e) {
            throw new Error('请求体JSON格式错误')
          }
        } else {
          requestConfig.data = apiConfig.value.body
        }
      }

      // 发送请求
      const response:any = await request.post(requestConfig.url, JSON.parse(requestConfig.body || '{}'), {
        headers: requestConfig.headers
      })
      const duration = Date.now() - startTime

      // 构建响应对象
      const responseData = {
        status: response.status || 200,
        statusText: response.statusText || 'OK',
        headers: response.headers || {},
        data: response.data,
        duration
      }

      currentResponse.value = responseData

      // 添加到历史记录
      const historyItem: RequestHistory = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        serviceId: currentService.value,
        serviceName: currentServiceInfo.value?.name || '未知服务',
        config: { ...apiConfig.value },
        response: responseData
      }
      
      requestHistory.value.unshift(historyItem)
      
      // 限制历史记录数量
      if (requestHistory.value.length > 50) {
        requestHistory.value = requestHistory.value.slice(0, 50)
      }

      ElMessage.success('请求发送成功')
    } catch (error: unknown) {
      const duration = Date.now() - startTime
      const errorMessage = (error as any)?.response?.data?.message || (error as any)?.message || '请求失败'
      
      currentError.value = errorMessage
      currentResponse.value = null

      // 添加错误记录到历史
      const historyItem: RequestHistory = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        serviceId: currentService.value,
        serviceName: currentServiceInfo.value?.name || '未知服务',
        config: { ...apiConfig.value },
        response: null,
        error: errorMessage
      }
      
      requestHistory.value.unshift(historyItem)
      
      ElMessage.error(errorMessage)
    } finally {
      loading.value = false
    }
  }

  // 从历史记录加载配置
  const loadFromHistory = (historyItem: RequestHistory) => {
    currentService.value = historyItem.serviceId
    apiConfig.value = { ...historyItem.config }
    currentResponse.value = historyItem.response
    currentError.value = historyItem.error || ''
    showResponse.value = true
  }

  // 清空历史记录
  const clearHistory = () => {
    requestHistory.value = []
    ElMessage.success('历史记录已清空')
  }

  // 生成代码
  const generateCode = (language: CodeLanguage): string => {
    const { url, method, headers, body, apiKey } = apiConfig.value
    
    const headersObj = Object.fromEntries(
      headers.filter(h => h.key && h.value).map(h => [h.key, h.value])
    )
    headersObj['Authorization'] = `Bearer ${apiKey}`

    switch (language) {
      case 'curl':
        let curlCmd = `curl -X ${method} \\
  "${url}"`
        
        Object.entries(headersObj).forEach(([key, value]) => {
          curlCmd += ` \\
  -H "${key}: ${value}"`
        })
        
        if (method !== 'GET' && body) {
          curlCmd += ` \\
  -d '${body}'`
        }
        
        return curlCmd

      case 'javascript':
        return `fetch('${url}', {
  method: '${method}',
  headers: ${JSON.stringify(headersObj, null, 2)},${method !== 'GET' && body ? `\n  body: ${JSON.stringify(body)}` : ''}
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));`

      case 'python':
        return `import requests
import json

url = "${url}"
headers = ${JSON.stringify(headersObj, null, 2).replace(/"/g, "'")}
${method !== 'GET' && body ? `data = ${JSON.stringify(body, null, 2).replace(/"/g, "'")}\n` : ''}
response = requests.${method.toLowerCase()}(url, headers=headers${method !== 'GET' && body ? ', json=data' : ''})
print(response.json())`

      default:
        return '// 暂不支持该语言'
    }
  }

  // 重置配置
  const resetConfig = () => {
    apiConfig.value = {
      url: currentServiceInfo.value?.endpoint || '',
      method: 'POST',
      apiKey: '',
      headers: [
        { key: 'Content-Type', value: 'application/json' },
        { key: 'Accept', value: 'application/json' }
      ],
      params: [],
      body: '',
      bodyType: 'json'
    }
    selectedApiKey.value = ''
    showResponse.value = false
    currentResponse.value = null
    currentError.value = ''
  }

  // 初始化
  const initialize = async () => {
    try {
      // 加载服务列表
      await serviceStore.fetchServices()
      
      // 加载API密钥列表
      await apiKeyStore.getApiKeysByUserId(0) // 使用默认用户ID 0，实际使用时应该传入当前用户ID
      
      // 设置默认服务
      if (availableServices.value.length > 0) {
        switchService(availableServices.value[0].id.toString())
      }
    } catch (error) {
      console.error('初始化playground失败:', error)
    }
  }

  return {
    // 新状态
    isLoading,
    selectedService,
    requestConfig,
    apiKeys,
    
    // 向后兼容状态
    loading,
    currentService,
    apiConfig,
    requestHistory,
    selectedApiKey,
    showResponse,
    currentResponse,
    currentError,
    
    // 计算属性
    availableServices,
    availableApiKeys,
    currentServiceInfo,
    canSendRequest,
    
    // 新方法
    updateRequestConfig,
    loadExample,
    clearCurrentResponse,
    deleteHistoryRecord,
    init,
    resetConfigNew,
    
    // 向后兼容方法
     switchService,
     loadServiceExample,
     selectApiKey,
     addHeader,
     removeHeader,
     addParam,
     removeParam,
     sendRequest,
     loadFromHistory,
     clearHistory,
     generateCode,
     resetConfig,
     initialize
  }
})