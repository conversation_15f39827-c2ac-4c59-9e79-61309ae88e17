import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  Length,
  IsOptional,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsBoolean,
  Min<PERSON>ength,
  <PERSON><PERSON>ength,
  Min,
  IsInt,
  Max,
  IsIn,
  IsDateString,
  IsDecimal,
} from 'class-validator';
import { OrderType, PaymentStatus } from '../entities/order.entity';

/**
 * 创建订单DTO
 */
export class CreateOrderDto {
  @ApiProperty({ description: '用户ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  userId: number;

  @ApiPropertyOptional({ description: '服务ID（服务购买订单必填）', example: 1 })
  @IsOptional()
  @IsInt()
  serviceId?: number;

  @ApiProperty({ description: '订单类型', enum: OrderType })
  @IsEnum(OrderType)
  @IsNotEmpty()
  type: OrderType;

  @ApiProperty({ description: '订单金额', example: 99.99, minimum: 0 })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  amount: number;

  @ApiPropertyOptional({ description: '购买次数（服务购买订单）', example: 1000, minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  purchaseCount?: number;

  @ApiPropertyOptional({ description: '订单备注', example: '充值1000元' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  remark?: string;

  @ApiPropertyOptional({ description: '支付方式', example: 'alipay' })
  @IsOptional()
  @IsString()
  @IsIn(['alipay', 'wechat', 'bank_card', 'balance'])
  paymentMethod?: string;
}

/**
 * 更新订单DTO
 */
export class UpdateOrderDto {
  @ApiPropertyOptional({ description: '订单状态', enum: PaymentStatus })
  @IsOptional()
  @IsEnum(PaymentStatus)
  status?: PaymentStatus;

  @ApiPropertyOptional({ description: '支付方式', example: 'alipay' })
  @IsOptional()
  @IsString()
  @IsIn(['alipay', 'wechat', 'bank_card', 'balance'])
  paymentMethod?: string;

  @ApiPropertyOptional({ description: '支付时间' })
  @IsOptional()
  @IsDateString()
  paidAt?: Date;

  @ApiPropertyOptional({ description: '第三方支付订单号', example: 'pay_1234567890' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  thirdPartyOrderId?: string;

  @ApiPropertyOptional({ description: '订单备注', example: '支付成功' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  remark?: string;
}

/**
 * 订单查询DTO
 */
export class QueryOrderDto {
  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', example: 10, minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: '用户ID', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  userId?: number;

  @ApiPropertyOptional({ description: '服务ID', example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  serviceId?: number;

  @ApiPropertyOptional({ description: '订单号搜索', example: 'ORD' })
  @IsOptional()
  @IsString()
  orderNumber?: string;

  @ApiPropertyOptional({ description: '订单类型', enum: OrderType })
  @IsOptional()
  @IsEnum(OrderType)
  type?: OrderType;

  @ApiPropertyOptional({ description: '订单状态', enum: PaymentStatus })
  @IsOptional()
  @IsEnum(PaymentStatus)
  status?: PaymentStatus;

  @ApiPropertyOptional({ description: '支付方式', example: 'alipay' })
  @IsOptional()
  @IsString()
  @IsIn(['alipay', 'wechat', 'bank_card', 'balance'])
  paymentMethod?: string;

  @ApiPropertyOptional({ description: '最小金额', example: 0 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minAmount?: number;

  @ApiPropertyOptional({ description: '最大金额', example: 1000 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxAmount?: number;

  @ApiPropertyOptional({ description: '开始时间' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: '结束时间' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: '排序字段', example: 'createdAt' })
  @IsOptional()
  @IsString()
  @IsIn(['id', 'orderNumber', 'type', 'amount', 'status', 'createdAt', 'updatedAt'])
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', example: 'DESC' })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}

/**
 * 订单支付DTO
 */
export class PayOrderDto {
  @ApiProperty({ description: '订单ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  orderId: number;

  @ApiProperty({ description: '支付方式', example: 'alipay' })
  @IsString()
  @IsNotEmpty()
  @IsIn(['alipay', 'wechat', 'bank_card', 'balance'])
  paymentMethod: string;

  @ApiPropertyOptional({ description: '支付密码（余额支付时需要）', example: '123456' })
  @IsOptional()
  @IsString()
  @Length(6, 6)
  paymentPassword?: string;

  @ApiPropertyOptional({ description: '客户端IP', example: '***********' })
  @IsOptional()
  @IsString()
  clientIp?: string;

  @ApiPropertyOptional({ description: '回调地址', example: 'https://example.com/callback' })
  @IsOptional()
  @IsString()
  callbackUrl?: string;
}

/**
 * 订单退款DTO
 */
export class RefundOrderDto {
  @ApiProperty({ description: '订单ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  orderId: number;

  @ApiPropertyOptional({ description: '退款金额（不填则全额退款）', example: 50.00 })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  refundAmount?: number;

  @ApiProperty({ description: '退款原因', example: '用户申请退款' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(200)
  refundReason: string;

  @ApiPropertyOptional({ description: '操作员ID', example: 1 })
  @IsOptional()
  @IsInt()
  operatorId?: number;
}

/**
 * 订单响应DTO
 */
export class OrderResponseDto {
  @ApiProperty({ description: '订单ID', example: 1 })
  id: number;

  @ApiProperty({ description: '订单号', example: 'ORD202312010001' })
  orderNumber: string;

  @ApiProperty({ description: '用户ID', example: 1 })
  userId: number;

  @ApiPropertyOptional({ description: '服务ID', example: 1 })
  serviceId?: number;

  @ApiProperty({ description: '订单类型', enum: OrderType })
  type: OrderType;

  @ApiProperty({ description: '订单金额', example: 99.99 })
  amount: number;

  @ApiPropertyOptional({ description: '购买次数', example: 1000 })
  purchaseCount?: number;

  @ApiProperty({ description: '订单状态', enum: PaymentStatus })
  status: PaymentStatus;

  @ApiPropertyOptional({ description: '支付方式', example: 'alipay' })
  paymentMethod?: string;

  @ApiPropertyOptional({ description: '支付时间' })
  paidAt?: Date;

  @ApiPropertyOptional({ description: '第三方支付订单号' })
  thirdPartyOrderId?: string;

  @ApiPropertyOptional({ description: '订单备注' })
  remark?: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 关联信息
  @ApiPropertyOptional({ description: '用户信息' })
  user?: {
    id: number;
    username: string;
    email: string;
  };

  @ApiPropertyOptional({ description: '服务信息' })
  service?: {
    id: number;
    code: string;
    name: string;
    type: string;
  };
}

/**
 * 订单列表响应DTO
 */
export class OrderListResponseDto {
  @ApiProperty({ description: '订单列表', type: [OrderResponseDto] })
  data: OrderResponseDto[];

  @ApiProperty({ description: '总数', example: 150 })
  total: number;

  @ApiProperty({ description: '当前页', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  limit: number;

  @ApiProperty({ description: '总页数', example: 15 })
  totalPages: number;
}

/**
 * 订单统计DTO
 */
export class OrderStatsDto {
  @ApiProperty({ description: '总订单数', example: 150 })
  totalOrders: number;

  @ApiProperty({ description: '总金额', example: 15000.00 })
  totalAmount: number;

  @ApiProperty({ description: '已完成订单数', example: 120 })
  completedOrders: number;

  @ApiProperty({ description: '已完成金额', example: 12000.00 })
  completedAmount: number;

  @ApiProperty({ description: '待支付订单数', example: 20 })
  pendingOrders: number;

  @ApiProperty({ description: '待支付金额', example: 2000.00 })
  pendingAmount: number;

  @ApiProperty({ description: '失败订单数', example: 5 })
  failedOrders: number;

  @ApiProperty({ description: '退款订单数', example: 5 })
  refundedOrders: number;

  @ApiProperty({ description: '退款金额', example: 1000.00 })
  refundedAmount: number;

  @ApiProperty({ description: '按类型分组统计' })
  byType: Record<string, { count: number; amount: number }>;

  @ApiProperty({ description: '按状态分组统计' })
  byStatus: Record<string, { count: number; amount: number }>;

  @ApiProperty({ description: '按支付方式分组统计' })
  byPaymentMethod: Record<string, { count: number; amount: number }>;
}

/**
 * 支付结果DTO
 */
export class PaymentResultDto {
  @ApiProperty({ description: '是否成功', example: true })
  success: boolean;

  @ApiProperty({ description: '订单ID', example: 1 })
  orderId: number;

  @ApiProperty({ description: '订单号', example: 'ORD202312010001' })
  orderNumber: string;

  @ApiPropertyOptional({ description: '支付URL（第三方支付）', example: 'https://pay.example.com/pay?id=123' })
  paymentUrl?: string;

  @ApiPropertyOptional({ description: '支付二维码（第三方支付）' })
  qrCode?: string;

  @ApiPropertyOptional({ description: '第三方支付订单号' })
  thirdPartyOrderId?: string;

  @ApiPropertyOptional({ description: '错误信息' })
  errorMessage?: string;

  @ApiProperty({ description: '支付状态', enum: PaymentStatus })
  status: PaymentStatus;
}