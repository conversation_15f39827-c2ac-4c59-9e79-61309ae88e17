# 统一限流配置
rateLimit:
  # 默认算法
  algorithm: sliding_window # fixed_window, sliding_window, token_bucket, leaky_bucket
  
  # Redis配置
  redis:
    keyPrefix: "rl"
    ttl: 3600
  
  # 监控配置
  monitoring:
    enabled: true
    logBlocked: true
    metricsEnabled: true
  
  # 错误处理配置
  errorHandling:
    defaultMessage: "请求过于频繁，请稍后再试"
    includeDetails: false
    statusCode: 429
  
  # 限流规则
  rules:
    # 全局限流规则
    - name: "global_rate_limit"
      type: "global"
      windowSize: 60
      maxRequests: 1000
      enabled: false
      priority: 100
      errorMessage: "全局请求频率过高，请稍后再试"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit"
    
    # IP限流规则
    - name: "ip_rate_limit"
      type: "ip"
      windowSize: 60
      maxRequests: 100
      enabled: true
      priority: 90
      errorMessage: "IP请求频率过高，请稍后再试"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit-IP"
    
    # 用户限流规则
    - name: "user_rate_limit"
      type: "user"
      windowSize: 60
      maxRequests: 200
      enabled: true
      priority: 80
      errorMessage: "用户请求频率过高，请稍后再试"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit-User"
    
    # API Key限流规则
    - name: "api_key_rate_limit"
      type: "api_key"
      windowSize: 3600
      maxRequests: 10000
      enabled: true
      priority: 70
      errorMessage: "API Key请求频率过高，请稍后再试"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit-API"
    
    # 服务限流规则
    - name: "service_rate_limit"
      type: "service"
      windowSize: 60
      maxRequests: 500
      enabled: true
      priority: 60
      errorMessage: "服务请求频率过高，请稍后再试"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit-Service"
    
    # 邮件发送限流
    - name: "email_rate_limit"
      type: "custom"
      windowSize: 300
      maxRequests: 8
      enabled: true
      priority: 10
      matcher:
        path: ["/op/email/*", "/op/captcha/email"]
      errorMessage: "邮件发送频率过高，请5分钟后再试"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit-Email"
    
    # 短信发送限流
    - name: "sms_rate_limit"
      type: "custom"
      windowSize: 60
      maxRequests: 1
      enabled: true
      priority: 10
      matcher:
        path: ["/op/captcha/sms"]
      errorMessage: "短信发送频率过高，请1分钟后再试"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit-SMS"
    
    # 验证码限流
    - name: "captcha_rate_limit"
      type: "custom"
      windowSize: 60
      maxRequests: 30
      enabled: true
      priority: 20
      matcher:
        path: ["/op/captcha/*"]
      errorMessage: "验证码请求频率过高，请稍后再试"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit-Captcha"
    
    # 登录限流
    - name: "login_rate_limit"
      type: "custom"
      windowSize: 900
      maxRequests: 20
      enabled: true
      priority: 10
      matcher:
        path: ["/op/auth/login"]
      errorMessage: "登录尝试次数过多，请15分钟后再试"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit-Login"
    
    # 注册限流
    - name: "register_rate_limit"
      type: "custom"
      windowSize: 3600
      maxRequests: 20
      enabled: true
      priority: 10
      matcher:
        path: ["/op/auth/register"]
      errorMessage: "注册频率过高，请1小时后再试"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit-Register"
    
    # 支付限流
    - name: "payment_rate_limit"
      type: "custom"
      windowSize: 60
      maxRequests: 3
      enabled: true
      priority: 5
      matcher:
        path: ["/op/order/payment/*", "/op/order/pay"]
      errorMessage: "支付请求频率过高，请稍后再试"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit-Payment"
    
    # 文件上传限流
    - name: "upload_rate_limit"
      type: "custom"
      windowSize: 60
      maxRequests: 10
      enabled: true
      priority: 30
      matcher:
        path: ["/op/*/upload", "/op/*/file"]
      errorMessage: "文件上传频率过高，请稍后再试"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit-Upload"
    
    # 网关转发限流
    - name: "gateway_rate_limit"
      type: "custom"
      windowSize: 60
      maxRequests: 1000
      enabled: true
      priority: 50
      matcher:
        path: ["/op/gateway/*"]
      errorMessage: "网关请求频率过高，请稍后再试"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit-Gateway"
    
    # 管理员接口限流
    - name: "admin_rate_limit"
      type: "custom"
      windowSize: 60
      maxRequests: 200
      enabled: true
      priority: 40
      matcher:
        path: ["/op/admin/*", "/op/monitoring/*"]
        userRole: ["admin", "super_admin"]
      errorMessage: "管理员操作频率过高，请稍后再试"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit-Admin"
    
    # 高频API限流
    - name: "high_frequency_api_limit"
      type: "api_key"
      windowSize: 1
      maxRequests: 10
      enabled: true
      priority: 20
      errorMessage: "高频API调用限制，请降低调用频率"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit-HF"
    
    # OCR服务限流
    - name: "ocr_service_limit"
      type: "service"
      windowSize: 60
      maxRequests: 100
      enabled: true
      priority: 30
      matcher:
        serviceCode: ["ocr", "image_recognition"]
      errorMessage: "OCR服务调用频率过高，请稍后再试"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit-OCR"
    
    # NLP服务限流
    - name: "nlp_service_limit"
      type: "service"
      windowSize: 60
      maxRequests: 200
      enabled: true
      priority: 30
      matcher:
        serviceCode: ["nlp", "text_analysis"]
      errorMessage: "NLP服务调用频率过高，请稍后再试"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit-NLP"
    
    # AI生成服务限流
    - name: "ai_generation_limit"
      type: "service"
      windowSize: 300
      maxRequests: 10
      enabled: false
      priority: 20
      matcher:
        serviceCode: ["ai_generation", "text_generation", "image_generation"]
      errorMessage: "AI生成服务调用频率过高，请5分钟后再试"
      headers:
        includeLimit: true
        includeRemaining: true
        includeResetTime: true
        headerPrefix: "X-RateLimit-AI"