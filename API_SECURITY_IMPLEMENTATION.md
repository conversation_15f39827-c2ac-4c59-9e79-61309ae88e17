# API密钥安全实施方案

## 实施概述

根据API_SECURITY_BEST_PRACTICES.md文档中的最佳实践方案，我们已经完成了前端和后端部分的实施，将API密钥认证从明文传输改为基于HMAC-SHA256的签名认证。本文档详细记录实施过程和注意事项。

## 已完成的修改

### 1. 前端修改

#### 1.1 添加依赖

在`vue3-open/package.json`中添加了以下依赖：

```json
"crypto-js": "^4.2.0"
```

安装依赖：

```bash
cd vue3-open
pnpm install
```

#### 1.2 修改ApiTestView.vue

在`vue3-open/src/views/console/ApiTestView.vue`中：

1. 引入CryptoJS库
2. 添加签名生成函数：
   - `buildCanonicalRequest`: 构建规范化请求字符串
   - `generateSignature`: 生成HMAC-SHA256签名
   - `generateNonce`: 生成随机nonce值
3. 修改`sendRequest`方法，使用签名认证代替直接传输密钥
4. 修改`checkTaskStatus`和`connectSSE`方法，使用签名认证

#### 1.3 修改apiTest.ts

在`vue3-open/src/api/apiTest.ts`中：

1. 更新`createTaskStatusStream`方法，添加对签名认证的支持
2. 保留对旧认证方式的支持，实现平滑过渡

### 2. 后端修改

#### 2.1 修改request.utils.ts

在`nest-open-admin/src/common/utils/request.utils.ts`中：

1. 添加`extractSignatureAuth`方法，用于从请求中提取签名认证信息（apiKey、timestamp、nonce、signature）

```typescript
static extractSignatureAuth(request: Request): { apiKey: string; timestamp: string; nonce: string; signature: string } | null {
  const apiKey = request.headers['x-api-key'] as string;
  const timestamp = request.headers['x-timestamp'] as string;
  const nonce = request.headers['x-nonce'] as string;
  const signature = request.headers['x-signature'] as string;
  
  if (!apiKey || !timestamp || !nonce || !signature) {
    console.log(`[RequestUtils] 签名认证信息不完整: apiKey=${!!apiKey}, timestamp=${!!timestamp}, nonce=${!!nonce}, signature=${!!signature}`);
    return null;
  }
  
  console.log(`[RequestUtils] 提取到签名认证信息: apiKey=${apiKey}, timestamp=${timestamp}, nonce=${nonce}, signature=${signature.substring(0, 10)}...`);
  return { apiKey, timestamp, nonce, signature };
}
```

#### 2.2 修改auth.types.ts

在`nest-open-admin/src/common/types/auth.types.ts`中：

1. 修改`AuthResult`接口，添加`api-key-signature`作为`authType`的可选值

```typescript
export interface AuthResult {
  /** 是否验证成功 */
  isValid: boolean;
  /** 认证类型 */
  authType?: 'jwt' | 'api-key' | 'api-key-signature';
  /** 用户信息 */
  user?: AuthUser;
  /** API密钥信息 */
  apiKey?: AuthApiKey;
  /** 载荷数据 */
  payload?: Record<string, any>;
  /** 错误信息 */
  error?: string;
}
```

#### 2.3 修改unified-auth.guard.ts

在`nest-open-admin/src/common/guards/unified-auth.guard.ts`中：

1. 移除对ApiKeyService的依赖，改为直接从Redis获取密钥信息
2. 修改认证流程，只使用签名认证，不保留原有认证方式
3. 添加从Redis获取密钥信息的逻辑
4. 清理不必要的代码

主要变更：

```typescript
// 验证签名认证
private async validateSignatureAuth(authInfo: AuthInfo, request: Request): Promise<AuthResult> {
  // ...
  
  // 从Redis中获取API密钥信息
  const cacheKey = `api_key:${authInfo.apiKey}`;
  const cachedData = await this.redisService.get(cacheKey);
  
  if (!cachedData) {
    this.logger.warn(`API密钥在缓存中不存在: ${authInfo.apiKey}`);
    return { isValid: false, error: 'API密钥不存在' };
  }
  
  // 解析缓存数据
  let cachedApiKey: any;
  try {
    cachedApiKey = JSON.parse(cachedData);
  } catch (error) {
    this.logger.error(`解析API密钥缓存数据失败: ${error.message}`);
    return { isValid: false, error: '无法解析API密钥信息' };
  }
  
  // 检查API密钥状态
  if (!this.coreAuthService.isApiKeyActive(cachedApiKey)) {
    this.logger.warn(`API密钥非活跃状态: ${authInfo.apiKey}, 状态: ${cachedApiKey.keyStatus || 'unknown'}`);
    return { isValid: false, error: 'API密钥非活跃状态' };
  }
  
  // 从Redis中获取Secret Key
  const secretKey = await this.redisService.get(`api_key_secret:${cachedApiKey.id}`);
  
  if (!secretKey) {
    this.logger.warn(`无法获取Secret Key: ${authInfo.apiKey}`);
    return { isValid: false, error: '无法获取密钥信息' };
  }
  
  // ...
}
```

#### 2.4 修改api-key.service.ts

在`nest-open-admin/src/modules/api-key/api-key.service.ts`中：

1. 修改`syncApiKeyToCache`方法，添加将secretKey同步到Redis的功能

```typescript
async syncApiKeyToCache(apiKey: ApiKeyEntity): Promise<void> {
  try {
    const cacheKey = `api_key:${apiKey.apiKey}`;
    const cacheData = {
      id: apiKey.id,
      userId: apiKey.userId,
      serviceId: apiKey.serviceId,
      name: apiKey.name,
      keyStatus: apiKey.keyStatus,
      keyType: apiKey.keyType,
      description: apiKey.description,
      permissions: apiKey.permissions,
      expiresAt: apiKey.expiresAt,
      lastUsedAt: apiKey.lastUsedAt,
      createdAt: apiKey.createdAt,
      updatedAt: apiKey.updatedAt,
    };
    
    // 缓存24小时
    await this.redisService.setex(cacheKey, 86400, JSON.stringify(cacheData));
    await this.redisService.setex(`api_key_id:${apiKey.id}`, 86400, apiKey.apiKey);
    
    // 同步Secret Key到Redis（如果有临时密钥）
    if (apiKey.tempSecretKey) {
      const secretKey = `api_key_secret:${apiKey.id}`;
      await this.redisService.setex(secretKey, 86400, apiKey.tempSecretKey);
      this.logger.debug(`已同步API密钥 #${apiKey.id} 的Secret Key到Redis`);
    }
  } catch (error) {
    throw error;
  }
}
```

## 实施注意事项

### 1. 架构优化

本次改造优化了认证架构，主要变更：

1. **移除了层间依赖**：UnifiedAuthGuard不再依赖业务层的ApiKeyService，而是直接从Redis获取密钥信息
2. **简化认证流程**：移除了旧的API密钥认证方式，只使用签名认证
3. **提高性能**：直接从Redis获取密钥信息，减少了数据库查询

### 2. 密钥管理

目前系统对于已经创建的密钥，只有在创建或重新生成密钥时才会将Secret Key同步到Redis，这意味着只有新创建的密钥或重新生成的密钥才能使用签名认证。

在实际生产环境中，可以考虑以下方案：

1. 实现密钥管理系统(KMS)，安全地存储和管理密钥
2. 为所有用户重新生成密钥，确保所有密钥都能使用签名认证
3. 设置密钥过期时间，强制用户定期更新密钥

### 3. 安全性提升

本次实施主要解决了以下安全问题：

1. **消除明文传输**：Secret Key不再在请求头中传输，只用于本地计算签名
2. **防止重放攻击**：通过时间戳和nonce机制确保请求的时效性和唯一性
3. **确保请求完整性**：签名包含请求的所有关键信息，任何篡改都会导致签名验证失败

## 后续工作

1. **完善文档**：更新API文档，说明新的认证方式
2. **添加监控**：监控签名认证的使用情况，确保平滑过渡
3. **设置截止日期**：设定一个截止日期，之后完全移除旧的API密钥认证
4. **密钥轮换机制**：实现密钥定期轮换机制，提高系统安全性
5. **密钥管理系统**：实现更完善的密钥管理系统，支持安全地存储和管理密钥

## 测试计划

1. **单元测试**：测试签名生成和验证的正确性
2. **集成测试**：测试前后端交互的正确性
3. **兼容性测试**：测试新旧认证方式的兼容性
4. **性能测试**：测试签名认证对系统性能的影响
5. **安全测试**：测试签名认证的安全性，包括防重放攻击、防篡改等

## 总结

本次API密钥安全改进项目已经成功实施了基于HMAC-SHA256的签名认证机制，显著提高了系统的安全性。主要成果包括：

1. **前端实现**：在API测试工具中实现了签名生成和请求发送功能
2. **后端实现**：在统一认证守卫中添加了签名验证逻辑，并优化了架构，移除了层间依赖
3. **安全增强**：解决了明文传输密钥、缺少请求时效性验证和重放攻击等安全隐患

通过这次改进，我们的API调用安全性已经达到了行业标准水平，符合最佳安全实践。接下来，我们将继续完善文档、添加监控和告警机制，并在适当的时候完全移除旧的认证方式，进一步提高系统的安全性和可维护性。 