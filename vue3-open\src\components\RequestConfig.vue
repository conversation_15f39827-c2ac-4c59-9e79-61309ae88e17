<template>
  <div class="request-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>请求配置</span>
          <el-button type="primary" @click="sendRequest" :loading="loading">
            发送请求
          </el-button>
        </div>
      </template>

      <!-- HTTP方法选择 -->
      <div class="config-section">
        <label class="config-label">请求方法</label>
        <el-select v-model="config.method" placeholder="选择请求方法">
          <el-option label="GET" value="GET" />
          <el-option label="POST" value="POST" />
          <el-option label="PUT" value="PUT" />
          <el-option label="DELETE" value="DELETE" />
          <el-option label="PATCH" value="PATCH" />
        </el-select>
      </div>

      <!-- URL输入 -->
      <div class="config-section">
        <label class="config-label">请求URL</label>
        <el-input
          v-model="config.url"
          placeholder="输入API端点URL"
          clearable
        />
      </div>

      <!-- 请求头配置 -->
      <div class="config-section">
        <label class="config-label">
          请求头
          <el-button type="text" @click="addHeader" size="small">
            <el-icon><Plus /></el-icon>
            添加
          </el-button>
        </label>
        <div class="headers-list">
          <div
            v-for="(header, index) in config.headers"
            :key="index"
            class="header-item"
          >
            <el-input
              v-model="header.key"
              placeholder="Header名称"
              size="small"
            />
            <el-input
              v-model="header.value"
              placeholder="Header值"
              size="small"
            />
            <el-button
              type="danger"
              size="small"
              @click="removeHeader(index)"
              :icon="Delete"
            />
          </div>
        </div>
      </div>

      <!-- 查询参数配置 -->
      <div class="config-section">
        <label class="config-label">
          查询参数
          <el-button type="text" @click="addParam" size="small">
            <el-icon><Plus /></el-icon>
            添加
          </el-button>
        </label>
        <div class="params-list">
          <div
            v-for="(param, index) in config.params"
            :key="index"
            class="param-item"
          >
            <el-input
              v-model="param.key"
              placeholder="参数名"
              size="small"
            />
            <el-input
              v-model="param.value"
              placeholder="参数值"
              size="small"
            />
            <el-button
              type="danger"
              size="small"
              @click="removeParam(index)"
              :icon="Delete"
            />
          </div>
        </div>
      </div>

      <!-- 请求体配置 -->
      <div class="config-section" v-if="['POST', 'PUT', 'PATCH'].includes(config.method)">
        <label class="config-label">请求体</label>
        <el-tabs v-model="bodyType" class="body-tabs">
          <el-tab-pane label="JSON" name="json">
            <el-input
              v-model="config.body.json"
              type="textarea"
              :rows="8"
              placeholder="输入JSON数据"
            />
          </el-tab-pane>
          <el-tab-pane label="Form Data" name="form">
            <div class="form-data-list">
              <div
                v-for="(item, index) in config.body.formData"
                :key="index"
                class="form-data-item"
              >
                <el-input
                  v-model="item.key"
                  placeholder="字段名"
                  size="small"
                />
                <el-input
                  v-model="item.value"
                  placeholder="字段值"
                  size="small"
                />
                <el-select v-model="item.type" size="small">
                  <el-option label="文本" value="text" />
                  <el-option label="文件" value="file" />
                </el-select>
                <el-button
                  type="danger"
                  size="small"
                  @click="removeFormData(index)"
                  :icon="Delete"
                />
              </div>
              <el-button type="text" @click="addFormData" size="small">
                <el-icon><Plus /></el-icon>
                添加字段
              </el-button>
            </div>
          </el-tab-pane>
          <el-tab-pane label="Raw" name="raw">
            <el-input
              v-model="config.body.raw"
              type="textarea"
              :rows="8"
              placeholder="输入原始数据"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>

    <!-- 响应结果 -->
    <el-card class="response-card" v-if="response">
      <template #header>
        <div class="card-header">
          <span>响应结果</span>
          <div class="response-status">
            <el-tag
              :type="response.status >= 200 && response.status < 300 ? 'success' : 'danger'"
            >
              {{ response.status }} {{ response.statusText }}
            </el-tag>
            <span class="response-time">{{ response.time }}ms</span>
          </div>
        </div>
      </template>

      <el-tabs class="response-tabs">
        <el-tab-pane label="响应体" name="body">
          <pre class="response-body">{{ formatResponseBody(response.data) }}</pre>
        </el-tab-pane>
        <el-tab-pane label="响应头" name="headers">
          <div class="response-headers">
            <div
              v-for="(value, key) in response.headers"
              :key="key"
              class="header-row"
            >
              <strong>{{ key }}:</strong> {{ value }}
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import axios from 'axios'

interface RequestConfig {
  method: string
  url: string
  headers: { key: string; value: string }[]
  params: { key: string; value: string }[]
  body: {
    json: string
    formData: { key: string; value: string; type: 'text' | 'file' }[]
    raw: string
  }
}

interface ResponseData {
  status: number
  statusText: string
  headers: Record<string, string>
  data: any
  time: number
}

const loading = ref(false)
const bodyType = ref('json')
const response = ref<ResponseData | null>(null)

const config = reactive<RequestConfig>({
  method: 'GET',
  url: '',
  headers: [{ key: 'Content-Type', value: 'application/json' }],
  params: [],
  body: {
    json: '',
    formData: [],
    raw: ''
  }
})

const addHeader = () => {
  config.headers.push({ key: '', value: '' })
}

const removeHeader = (index: number) => {
  config.headers.splice(index, 1)
}

const addParam = () => {
  config.params.push({ key: '', value: '' })
}

const removeParam = (index: number) => {
  config.params.splice(index, 1)
}

const addFormData = () => {
  config.body.formData.push({ key: '', value: '', type: 'text' })
}

const removeFormData = (index: number) => {
  config.body.formData.splice(index, 1)
}

const sendRequest = async () => {
  if (!config.url) {
    ElMessage.warning('请输入请求URL')
    return
  }

  try {
    loading.value = true
    const startTime = Date.now()

    // 构建请求配置
    const requestConfig: any = {
      method: config.method,
      url: config.url,
      timeout: 30000
    }

    // 添加请求头
    const headers: Record<string, string> = {}
    config.headers.forEach(header => {
      if (header.key && header.value) {
        headers[header.key] = header.value
      }
    })
    if (Object.keys(headers).length > 0) {
      requestConfig.headers = headers
    }

    // 添加查询参数
    const params: Record<string, string> = {}
    config.params.forEach(param => {
      if (param.key && param.value) {
        params[param.key] = param.value
      }
    })
    if (Object.keys(params).length > 0) {
      requestConfig.params = params
    }

    // 添加请求体
    if (['POST', 'PUT', 'PATCH'].includes(config.method)) {
      if (bodyType.value === 'json' && config.body.json) {
        try {
          requestConfig.data = JSON.parse(config.body.json)
        } catch (e) {
          ElMessage.error('JSON格式错误')
          return
        }
      } else if (bodyType.value === 'form' && config.body.formData.length > 0) {
        const formData = new FormData()
        config.body.formData.forEach(item => {
          if (item.key && item.value) {
            formData.append(item.key, item.value)
          }
        })
        requestConfig.data = formData
      } else if (bodyType.value === 'raw' && config.body.raw) {
        requestConfig.data = config.body.raw
      }
    }

    // 发送请求
    const res = await axios(requestConfig)
    const endTime = Date.now()

    response.value = {
      status: res.status,
      statusText: res.statusText,
      headers: res.headers as any,
      data: res.data,
      time: endTime - startTime
    }

    ElMessage.success('请求发送成功')
  } catch (error: any) {
    const endTime = Date.now()
    
    if (error.response) {
      response.value = {
        status: error.response.status,
        statusText: error.response.statusText,
        headers: error.response.headers,
        data: error.response.data,
        time: endTime - Date.now()
      }
    } else {
      ElMessage.error(error.message || '请求失败')
    }
  } finally {
    loading.value = false
  }
}

const formatResponseBody = (data: any) => {
  if (typeof data === 'object') {
    return JSON.stringify(data, null, 2)
  }
  return data
}
</script>

<style scoped>
.request-config {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.config-card,
.response-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-section {
  margin-bottom: 20px;
}

.config-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.headers-list,
.params-list,
.form-data-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-item,
.param-item,
.form-data-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.header-item .el-input,
.param-item .el-input {
  flex: 1;
}

.form-data-item .el-input {
  flex: 1;
}

.form-data-item .el-select {
  width: 100px;
}

.body-tabs {
  margin-top: 8px;
}

.response-status {
  display: flex;
  align-items: center;
  gap: 12px;
}

.response-time {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.response-body {
  background: var(--el-fill-color-light);
  padding: 16px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 400px;
  overflow-y: auto;
}

.response-headers {
  background: var(--el-fill-color-light);
  padding: 16px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.header-row {
  margin-bottom: 8px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.header-row strong {
  color: var(--el-color-primary);
}
</style>