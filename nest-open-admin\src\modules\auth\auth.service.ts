import {
  Injectable,
  Logger,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { ApiException } from '@/common/exceptions/api.exception';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UserService } from '../user/user.service';
import { UserType, UserStatus } from '../user/entities/user.entity';
import { ApiKeyService } from '../api-key/api-key.service';
import { CaptchaService } from '../../shared/captcha.service';
import { RedisService } from '../../shared/redis.service';
import * as bcrypt from 'bcrypt';
import { LoginDto, RegisterDto } from './dto';
import { CreateUserDto } from '../user/dto'
import { ApiKeyStatus } from '../api-key/entities/api-key.entity';
import { AuthR<PERSON><PERSON>, AuthA<PERSON><PERSON><PERSON>, AuthUser } from '@/common/types/auth.types';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly userService: UserService,
    private readonly apiKeyService: ApiKeyService,
    private readonly captchaService: CaptchaService,
    private readonly redisService: RedisService,
  ) {}
  /**
   * 用户注册 - 支持两种注册方式
   * 1. 邮箱 + 邮箱验证码 + 拖拽验证码注册
   * 2. 手机号 + 短信验证码 + 拖拽验证码注册
   */
  async register(registerDto: RegisterDto): Promise<Record<string, any>> {
    const {
      email,
      phone,
      password,
      confirmPassword,
      emailCode,
      smsCode,
      securityVerification,
      userType = UserType.INDIVIDUAL,
    } = registerDto;

    // 验证必须提供邮箱或手机号
    if (!email && !phone) {
      throw new ApiException(50001); // 缺少必要参数
    }

    // 验证密码确认
    if (password !== confirmPassword) {
      throw new ApiException(50008); // 密码不一致
    }

    // 验证安全验证数据
    if (!securityVerification) {
      throw new ApiException(50002); // 缺少安全验证
    }

    // 验证拖拽验证码（根据提供的数据类型进行验证）
    if (securityVerification.level && securityVerification.behaviorPattern && securityVerification.verificationTimestamp) {
      // 新版本行为验证
      const { level, behaviorPattern, verificationTimestamp } = securityVerification;
      console.log('安全验证通过:', { level, timestamp: verificationTimestamp });
    } else {
      throw new ApiException(50002);
    }

    let user: Record<string ,any>;

    // 方式1: 邮箱 + 邮箱验证码注册
    if (email && emailCode) {
      // （验证邮箱验证码在userService.register中进行）
      // 检查邮箱是否已存在
      const existingUser = await this.userService.findByEmail(email);
      if (existingUser) {
        throw new ApiException(30006); // 邮箱已存在
      }

      // 创建用户
      const createUserDto: Record<string,any> = {
        email,
        password,
        userType,
        emailCode, // 添加邮箱验证码字段
      };
      user = await this.userService.register(createUserDto as CreateUserDto);
    }
    // 方式2: 手机号 + 短信验证码注册
    else if (phone && smsCode) {
      // 检查手机号是否已存在
      const existingUser = await this.userService.findByPhone(phone);
      if (existingUser) {
        throw new ApiException(30005); // 手机号已存在
      }

      // 创建用户（验证码验证在userService.phoneRegister中进行）
      const phoneRegisterDto: Record<string ,any> = {
        phone,
        smsCode,
        password,
        confirmPassword,
        userType,
      };
      user = await this.userService.phoneRegister(phoneRegisterDto as RegisterDto);
    } else {
      // 缺少验证码
      throw new ApiException(50004); // 缺少登录凭证
    }

    // 确保user不为null
    if (!user) {
      throw new ApiException(50006); // 注册失败
    }
    return {
      id: user?.id,
      message: '注册成功',
    };
  }


  /**
   * 用户登录 - 支持两种登录方式
   * 1. 邮箱/用户名/手机号 + 密码 + 拖拽验证码登录
   * 2. 手机号 + 短信验证码快捷登录（支持静默注册）
   */
  async login(loginDto: LoginDto): Promise<Record<string, any>> {
    const {
      email,
      username,
      phone,
      password,
      captchaId,
      captchaCode,
      smsCode,
      securityVerification,
      rememberMe = false,
    } = loginDto;

    // 验证必须提供邮箱、用户名或手机号
    if (!email && !username && !phone) {
      throw new ApiException(50001);
    }

    let user: Record<string,any> | null = null;

    // 方式1: 手机号 + 短信验证码快捷登录（支持静默注册）
    if (phone && smsCode && !password) {
      // 验证短信验证码
      const smsValid = await this.captchaService.verifySmsCode(
        phone,
        smsCode,
        'login',
      );
      if (!smsValid) {
        throw new ApiException(30009);
      }

      // 查找用户
      user = await this.userService.findByPhone(phone);
      
      // 如果用户不存在，进行静默注册
      if (!user) {
        try {
          this.logger.log(`手机号 ${phone} 开始静默注册流程`);
          user = await this.userService.createUserByPhone({
            phone,
            userType: UserType.INDIVIDUAL, // 默认个人用户
            userStatus: UserStatus.ACTIVE,
            level: 'basic',
            role: 'user'
          });
          this.logger.log(`手机号 ${phone} 静默注册成功，默认密码为: szyl_${phone}`);
        } catch (error) {
          this.logger.error(`静默注册失败: ${error.message}`, error);
          throw new ApiException(50006); // 注册失败
        }
      }
    } 
    // 方式2: 邮箱/用户名/手机号 + 密码 + 拖拽验证码登录
    else if (password) {
      // 验证必须提供拖拽验证码数据
      if (!securityVerification) {
        throw new ApiException(50002); // 缺少安全验证
      }

      // 验证拖拽验证码（根据提供的数据类型进行验证）
      if (securityVerification.moveDistance !== undefined && securityVerification.duration !== undefined) {
        // 旧版本滑动验证
        const securityValid = await this.captchaService.verifySlideVerify(
          securityVerification.moveDistance,
          securityVerification.duration,
        );
        if (!securityValid) {
          throw new ApiException(50002);
        }
      } else {
        // 新版本行为验证（可以根据需要实现更复杂的验证逻辑）
        const { level, behaviorPattern, verificationTimestamp } = securityVerification;
        if (!level || !behaviorPattern || !verificationTimestamp) {
          throw new ApiException(50002);
        }
        // 这里可以添加更复杂的行为验证逻辑
        console.log('安全验证通过:', { level, timestamp: verificationTimestamp });
      }

      // 验证图像验证码（如果提供）
      if (captchaId && captchaCode) {
        const captchaValid = await this.captchaService.verifyImageCaptcha(
          captchaId,
          captchaCode,
        );
        if (!captchaValid) {
          throw new ApiException(20001);
        }
      }

      // 根据提供的字段进行密码验证
      if (email) {
        user = await this.validateUser(email, password);
      } else if (username) {
        user = await this.validateUserByUsername(username, password);
      } else if (phone) {
        user = await this.validateUserByPhone(phone, password);
      }

      if (!user) {
         throw new ApiException(50005); // 用户名或密码错误
       }
     } else {
       // 既没有密码也没有短信验证码
       throw new ApiException(50004); // 缺少登录凭证
     }

     // 确保user不为null
     if (!user) {
       throw new ApiException(50006);
     }

    // 生成JWT令牌（包含访问令牌和刷新令牌）
    const tokens = await this.generateTokens(user);

    // 检查用户是否有未查看的API密钥
    let hasUnviewedApiKey = false;
    try {
      const apiKeys = await this.apiKeyService.getUserApiKeysWithSecret(user?.id);
      if (apiKeys && apiKeys.length > 0) {
        hasUnviewedApiKey = apiKeys.some((key) => !key.isSecretViewed);
      }
    } catch (error) {
      this.logger.error(`检查用户 ${user.id} API密钥状态失败:`, error);
    }

    return {
      user,
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      token: tokens.accessToken, // 保持向后兼容
      hasUnviewedApiKey,
    };
  }

  /**
   * 用户登出
   */
  async logout(accessToken: string): Promise<any> {
    this.logger.log(
      `User logout with token: ${accessToken?.substring(0, 10)}...`,
    );

    try {
      // 将token加入黑名单，设置过期时间为24小时
      // 这样可以防止已登出的token被重复使用
      const blacklistKey = `blacklist:${accessToken}`;
      await this.redisService.set(blacklistKey, '1', 24 * 60 * 60); // 24小时过期

      this.logger.log('Token已加入黑名单');
      return { message: '登出成功' };
    } catch (error) {
      this.logger.error('登出失败:', error);
      throw new ApiException(50007);
    }
  }

  /**
   * 验证用户凭据（邮箱）
   */
  async validateUser(
    email: string,
    password: string,
  ): Promise<Record<string,any> | null> {
    const user = await this.userService.findByEmail(email);
    if (user && (await this.comparePassword(password, user.password))) {
      return user;
    }
    return null;
  }

  /**
   * 验证用户凭据（用户名）
   */
  async validateUserByUsername(
    username: string,
    password: string,
  ): Promise<Record<string,any> | null> {
    const user = await this.userService.findByUsername(username);
    if (user && (await this.comparePassword(password, user.password))) {
      return user;
    }
    return null;
  }

  /**
   * 验证用户凭据（手机号）
   */
  async validateUserByPhone(
    phone: string,
    password: string,
  ): Promise<Record<string,any> | null> {
    const user = await this.userService.findByPhone(phone);
    if (user && (await this.comparePassword(password, user.password))) {
      return user;
    }
    return null;
  }

  /**
   * 通过JWT payload验证用户
   */
  async validateUserById(userId: number): Promise<Record<string,any> | null> {
    return await this.userService.findById(userId);
  }

  /**
   * 比较密码
   */
  private async comparePassword(
    password: string,
    hashedPassword: string,
  ): Promise<boolean> {
    return await bcrypt.compare(password, hashedPassword);
  }


  /**
   * 生成访问令牌和刷新令牌
   */
  private async generateTokens(
    user: Record<string,any>,
  ): Promise<{ accessToken: string; refreshToken: string, token: string }> {
    const payload = {
      sub: user.id,
      username: user.username,
      nickname: user.nickname || user.username,
      email: user.email,
      phone: user.phone,
      userType: user.userType,
      status: user.userStatus,
      tier: user.tier,
      role: user.role,
      verificationStatus: user.verificationStatus,
      balance: user.balance,
    };

    // 生成访问令牌（短期有效）
    const accessToken = this.jwtService.sign(payload, {
      expiresIn: this.configService.get('jwt.expiresin', '2h'),
    });

    // 生成刷新令牌（长期有效）
    const refreshToken = this.jwtService.sign(
      { ...payload, tokenType: 'refresh' },
      {
        secret: this.configService.get(
          'jwt.refreshTokenSecret',
          this.configService.get('jwt.secretkey'),
        ),
        expiresIn: this.configService.get('jwt.refreshExpiresIn', '7d'),
      },
    );

    return { accessToken, refreshToken, token: accessToken };
  }

  /**
   * 刷新访问令牌
   */
  async refreshToken(
    refreshToken: string,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      // 使用刷新令牌的密钥验证
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get(
          'jwt.refreshTokenSecret',
          this.configService.get('jwt.secretkey'),
        ),
      });

      // 验证是否为刷新令牌
      if (payload.tokenType !== 'refresh') {
        throw new ApiException(50008);
      }

      // 获取用户信息
      const user = await this.userService.findById(payload.sub);
      if (!user?.id) {
        throw new ApiException(50009);
      }

      // 生成新的访问令牌和刷新令牌
      const tokens = await this.generateTokens(user);

      return tokens;
    } catch (error) {
      this.logger.error('刷新令牌失败:', error);
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new ApiException(50010);
    }
  }

  async getProfile(userId: number) {
    const user = { id: userId };
    return await this.userService.profile(user);
  }

  /**
   * 验证API密钥
   * @param apiKey API密钥
   * @param secretKey 密钥秘钥
   * @returns 验证结果
   */
  async validateApiKey(apiKey: string, secretKey: string): Promise<AuthResult> {
    try {
      // 验证API密钥格式
      if (!apiKey || !apiKey.startsWith('ak_') || apiKey.length < 10) {
        return { isValid: false, error: 'Invalid API key format' };
      }
      
      // 验证密钥秘钥格式
      if (!secretKey || !secretKey.startsWith('sk_') || secretKey.length < 10) {
        return { isValid: false, error: 'Invalid secret key format' };
      }
      
      // 从缓存获取API密钥信息
      const cacheKey = `api_key:${apiKey}`;
      const cachedData = await this.redisService.get(cacheKey);
      
      let apiKeyEntity;
      
      if (cachedData) {
        // 缓存命中，解析数据
        const cachedApiKey = JSON.parse(cachedData);
        
        // 检查API密钥状态
        if (cachedApiKey.status !== ApiKeyStatus.ACTIVE) {
          return { isValid: false, error: 'API key is inactive or expired' };
        }
        
        // 获取完整的API密钥实体，包含secretHash
        apiKeyEntity = await this.apiKeyService.findEntityById(cachedApiKey.id);
      } else {
        // 如果缓存中没有，从数据库查询
        apiKeyEntity = await this.apiKeyService.findByApiKey(apiKey);
        
        if (!apiKeyEntity) {
          return { isValid: false, error: 'API key not found' };
        }
        
        // 检查API密钥状态
        if (apiKeyEntity.keyStatus !== ApiKeyStatus.ACTIVE) {
          return { isValid: false, error: 'API key is inactive or expired' };
        }
        
        // 同步到缓存
        await this.apiKeyService.syncApiKeyToCache(apiKeyEntity);
      }
      
      // 验证密钥秘钥
      if (!apiKeyEntity) {
        return { isValid: false, error: 'API key entity not found' };
      }
      
      const isSecretValid = await this.apiKeyService.verifySecretKey(secretKey, apiKeyEntity.secretHash);
      if (!isSecretValid) {
        return { isValid: false, error: 'Invalid secret key' };
      }
      
      // 构建API密钥数据
      const apiKeyData: AuthApiKey = {
        id: apiKeyEntity.id,
        userId: apiKeyEntity.userId,
        serviceId: apiKeyEntity.serviceId,
        name: apiKeyEntity.name,
        keyStatus: apiKeyEntity.keyStatus,
        keyType: apiKeyEntity.keyType,
        permissions: apiKeyEntity.permissions,
        expiresAt: apiKeyEntity.expiresAt,
        lastUsedAt: apiKeyEntity.lastUsedAt,
        createdAt: apiKeyEntity.createdAt,
        updatedAt: apiKeyEntity.updatedAt
      };
      
      return {
        isValid: true,
        authType: 'api-key',
        apiKey: apiKeyData,
        user: { id: apiKeyEntity.userId } as AuthUser,
      };
    } catch (error) {
      this.logger.error(`API密钥验证失败: ${error.message}`);
      return { isValid: false, error: 'API key validation failed' };
    }
  }

  /**
   * 验证JWT令牌
   * 统一的JWT令牌验证逻辑，支持黑名单检查
   */
  async validateJwtToken(token: string): Promise<{
    isValid: boolean;
    payload?: Record<string,any>;
    user?: Record<string,any>;
    error?: string;
  }> {
    try {
      if (!token) {
        return { isValid: false, error: 'Token is required' };
      }

      // 检查令牌是否在黑名单中
      const blacklistKey = `blacklist:${token}`;
      try {
        const isBlacklisted = await this.redisService.exists(blacklistKey);
        if (isBlacklisted) {
          return { isValid: false, error: 'Token has been revoked' };
        }
      } catch (redisError) {
        this.logger.warn('Redis黑名单检查失败，继续验证:', redisError.message);
      }

      // 验证JWT令牌
      const payload = this.jwtService.verify(token, {
        secret: this.configService.get('jwt.secretkey')
      });

      if (!payload || !payload.sub) {
        return { isValid: false, error: 'Invalid token payload' };
      }

      // 获取用户信息
      const user = await this.userService.findById(payload.sub);
      if (!user) {
        return { isValid: false, error: 'User not found' };
      }

      if (user.userStatus !== UserStatus.ACTIVE) {
        return { isValid: false, error: 'User is not active' };
      }

      return {
        isValid: true,
        payload,
        user
      };
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        return { isValid: false, error: 'Token has expired' };
      }
      if (error.name === 'JsonWebTokenError') {
        return { isValid: false, error: 'Invalid token' };
      }
      this.logger.error('JWT令牌验证时发生错误:', error);
      return { isValid: false, error: 'Token validation error' };
    }
  }

  /**
   * 更新API密钥最后使用时间
   * @param apiKey API密钥字符串
   * @param clientIp 客户端IP
   */
  async updateApiKeyLastUsed(apiKey: string, clientIp: string): Promise<void> {
    try {
      // 先验证API密钥获取实体
      const apiKeyEntity = await this.apiKeyService.findByApiKey(apiKey);
      if (apiKeyEntity) {
        // 调用ApiKeyService的updateLastUsed方法
        await this.apiKeyService.updateLastUsed(apiKeyEntity.id, clientIp);
      }
    } catch (error) {
      this.logger.error('更新API密钥使用记录失败:', error);
      throw error;
    }
  }
}
