import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RedisModule } from '@nestjs-modules/ioredis';
import { ServeStaticModule } from '@nestjs/serve-static';
import { JwtModule } from '@nestjs/jwt';
import { HttpModule } from '@nestjs/axios';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { join } from 'path';
import { RedisService } from './redis.service';
import { ErrorHandlerService } from './error-handler.service';
import { EnhancedCacheManagerService } from './enhanced-cache-manager.service';
import { EmailService } from './email.service';
import { SmsService } from './sms.service'
import { CaptchaService } from './captcha.service'
import { UserCacheService } from './user-cache.service';
import { CoreAuthService } from './core-auth.service'

/**
 * 共享模块
 * 提供全局公共服务，包括Redis、静态文件服务、JWT、邮件发送、短信发送、验证码、错误码服务、缓存封装服务、认证封装、用户封装处理服务、http等
 */
@Global()
@Module({
  imports: [
    // 配置模块 - 全局配置服务
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
    }),
    // 事件模块配置
    EventEmitterModule.forRoot({
      // 设置为true以使用通配符
      wildcard: false,
      // 分隔符
      delimiter: '.',
      // 最大监听器数量
      maxListeners: 10,
      // 是否详细输出内存泄漏警告
      verboseMemoryLeak: false,
    }),
    // Redis模块配置
    RedisModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        // 支持URL配置或分离配置
        const redisUrl = configService.get('REDIS_URL');
        if (redisUrl) {
          return {
            type: 'single',
            url: redisUrl,
            options: {
              retryDelayOnFailover: 100,
              enableReadyCheck: false,
              maxRetriesPerRequest: null,
              lazyConnect: true,
            },
          };
        }
        // 分离配置方式
        return {
          type: 'single',
          options: {
            host: configService.get('redis.host', 'localhost'),
            port: configService.get('redis.port', 6379),
            password: configService.get('redis.password'),
            db: configService.get('redis.db', 0),
            retryDelayOnFailover: 100,
            enableReadyCheck: false,
            maxRetriesPerRequest: null,
            lazyConnect: true,
            connectTimeout: 10000,
            commandTimeout: 5000,
          },
        };
      },
      inject: [ConfigService],
    }),
    // 静态文件服务模块
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', '..', 'docs'),
      serveRoot: '/docs',
    }),
    // JWT模块配置
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('jwt.secretkey', 'zANDwNQVFzxlfG9myPxVWAkq4iXJEPhy'),
        signOptions: {
          expiresIn: configService.get('jwt.expiresin', '2h'),
        },
        global: true,
      }),
      inject: [ConfigService],
    }),
    HttpModule.register({
      timeout: 8000,
      maxRedirects: 5,
    }),
  ],
  providers: [
    RedisService,
    CoreAuthService,
    ErrorHandlerService,
    EnhancedCacheManagerService,
    UserCacheService,
    EmailService,
    SmsService,
    CaptchaService,
  ],
  exports: [
    ConfigModule,
    RedisModule,
    ServeStaticModule,
    JwtModule,
    HttpModule,
    EventEmitterModule,
    RedisService,
    CoreAuthService,
    ErrorHandlerService,
    EnhancedCacheManagerService,
    UserCacheService,
    EmailService,
    SmsService,
    CaptchaService,
  ],
})
export class SharedModule {}