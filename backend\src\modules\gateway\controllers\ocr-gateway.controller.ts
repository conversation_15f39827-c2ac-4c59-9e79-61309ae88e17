import {
  Controller,
  Post,
  Body,
  Req,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Logger,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Request } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBearerAuth } from '@nestjs/swagger';
import { UseAuthStrategy, AuthStrategy } from '@/common/decorators/auth-strategy.decorator';
import { ThrottleConfig } from '@/common/guards/custom-throttler.guard';
import { GatewayService } from '../services/gateway.service';
import { 
  OcrRequestDto,
  FileUploadRequestDto,
} from '../dto/gateway-request.dto';
import {
  SyncResponseDto,
  AsyncResponseDto,
  ProxyAsyncSuccessResponseDto,
  ProxyAsyncTimeoutResponseDto,
  OcrResponseDto,
  ErrorResponseDto,
} from '../dto/gateway-response.dto';
import { IGatewayRequest } from '../interfaces/gateway.interfaces';
import { ProcessingMode } from '../config/gateway.constants';
import { getClientIp } from '@/common/utils/ip.utils';

/**
 * OCR网关控制器
 * 负责处理OCR识别相关的网关请求
 * 遵循控制器职责，专注于HTTP请求处理和参数验证
 */
@ApiTags('OCR网关')
@Controller('ocr')
@UseAuthStrategy(AuthStrategy.API_KEY)
@ApiBearerAuth()
export class OcrGatewayController {
  private readonly logger = new Logger(OcrGatewayController.name);

  constructor(private readonly gatewayService: GatewayService) {}

  /**
   * OCR文件上传识别
   */
  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ThrottleConfig(20, 60) // 每分钟最多10次文件上传
  @ApiOperation({ summary: 'OCR文件上传识别' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ 
    status: 200, 
    description: '同步处理成功',
    type: SyncResponseDto,
  })
  @ApiResponse({ 
    status: 202, 
    description: '异步处理已接受',
    type: AsyncResponseDto,
  })
  @ApiResponse({ 
    status: 400, 
    description: '请求参数错误',
    type: ErrorResponseDto,
  })
  async uploadOcr(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: FileUploadRequestDto,
    @Req() req: Request
  ) {
    this.logger.log(`OCR文件上传请求: ${file?.originalname || 'unknown'}`);

    // 从请求体中获取serviceId
    const serviceId = body.serviceId;

    if (serviceId) {
      this.logger.debug(`使用前端传递的服务ID: ${serviceId}`);
    } else {
      this.logger.warn('前端未传递服务ID，将使用路径映射（不推荐）');
    }

    const gatewayRequest: IGatewayRequest = {
      path: '/v1/op/ocr/upload',
      method: 'POST',
      headers: req.headers as Record<string, string>,
      body: {
        ...body,
        type: body.options?.type || 'logistics',
      },
      file,
      mode: body.mode,
      serviceId, // 传递服务ID
      user: (req as any).user ? {
        id: (req as any).user.id,
        username: (req as any).user.username,
        email: (req as any).user.email,
      } : undefined,
      clientInfo: {
        ip: getClientIp(req),
        userAgent: req.headers['user-agent'],
        referer: req.headers['referer'],
        apiKey: req.headers['x-api-key'] as string,
        userId: (req as any).user?.id,
      },
    };

    const response = await this.gatewayService.handleRequest(gatewayRequest);
    
    // 根据处理模式返回不同的响应格式
    if (response.taskId) {
      if (response.status === 'completed') {
        // 代理异步成功
        return {
          ...response,
          data: this.formatOcrResponse(response.data),
        };
      } else if (response.status === 'timeout') {
        // 代理异步超时
        return {
          ...response,
          statusUrl: `/v1/op/tasks/${response.taskId}`,
          eventsUrl: `/v1/op/tasks/${response.taskId}/events`,
        };
      } else {
        // 纯异步
        return {
          ...response,
          statusUrl: `/v1/op/tasks/${response.taskId}`,
          eventsUrl: `/v1/op/tasks/${response.taskId}/events`,
        };
      }
    } else {
      // 同步响应
      return {
        ...response,
        data: this.formatOcrResponse(response.data),
      };
    }
  }

  /**
   * OCR Base64图片识别
   */
  @Post('recognize')
  @ApiOperation({ summary: 'OCR Base64图片识别' })
  @ApiResponse({ 
    status: 200, 
    description: '同步处理成功',
    type: SyncResponseDto,
  })
  @ApiResponse({ 
    status: 202, 
    description: '异步处理已接受',
    type: AsyncResponseDto,
  })
  @ApiResponse({ 
    status: 400, 
    description: '请求参数错误',
    type: ErrorResponseDto,
  })
  async recognizeOcr(
    @Body() body: OcrRequestDto,
    @Req() req: Request
  ) {
    this.logger.log('OCR Base64识别请求');

    const gatewayRequest: IGatewayRequest = {
      path: '/v1/op/ocr/recognize',
      method: 'POST',
      headers: req.headers as Record<string, string>,
      body: {
        imageBase64: body.imageBase64,
        type: body.type || 'logistics',
        options: body.options,
      },
      mode: body.mode,
      user: (req as any).user ? {
        id: (req as any).user.id,
        username: (req as any).user.username,
        email: (req as any).user.email,
      } : undefined,
      clientInfo: {
        ip: getClientIp(req),
        userAgent: req.headers['user-agent'],
        referer: req.headers['referer'],
        apiKey: req.headers['x-api-key'] as string,
        userId: (req as any).user?.id,
      },
    };

    const response = await this.gatewayService.handleRequest(gatewayRequest);
    
    // 根据处理模式返回不同的响应格式
    if (response.taskId) {
      if (response.status === 'completed') {
        // 代理异步成功
        return {
          ...response,
          data: this.formatOcrResponse(response.data),
        };
      } else if (response.status === 'timeout') {
        // 代理异步超时
        return {
          ...response,
          statusUrl: `/v1/op/tasks/${response.taskId}`,
          eventsUrl: `/v1/op/tasks/${response.taskId}/events`,
        };
      } else {
        // 纯异步
        return {
          ...response,
          statusUrl: `/v1/op/tasks/${response.taskId}`,
          eventsUrl: `/v1/op/tasks/${response.taskId}/events`,
        };
      }
    } else {
      // 同步响应
      return {
        ...response,
        data: this.formatOcrResponse(response.data),
      };
    }
  }

  /**
   * OCR批量识别
   */
  @Post('batch')
  @ApiOperation({ summary: 'OCR批量识别' })
  @ApiResponse({ 
    status: 202, 
    description: '批量处理已接受',
    type: AsyncResponseDto,
  })
  @ApiResponse({ 
    status: 400, 
    description: '请求参数错误',
    type: ErrorResponseDto,
  })
  async batchOcr(
    @Body() body: { items: OcrRequestDto[]; batchOptions?: any },
    @Req() req: Request
  ) {
    this.logger.log(`OCR批量识别请求: ${body.items?.length || 0} 个项目`);

    // 批量请求强制使用异步模式
    const gatewayRequest: IGatewayRequest = {
      path: '/v1/op/ocr/batch',
      method: 'POST',
      headers: req.headers as Record<string, string>,
      body: {
        items: body.items,
        batchOptions: body.batchOptions,
      },
      mode: ProcessingMode.ASYNC, // 强制异步
      user: (req as any).user ? {
        id: (req as any).user.id,
        username: (req as any).user.username,
        email: (req as any).user.email,
      } : undefined,
      clientInfo: {
        ip: getClientIp(req),
        userAgent: req.headers['user-agent'],
        referer: req.headers['referer'],
        apiKey: req.headers['x-api-key'] as string,
        userId: (req as any).user?.id,
      },
    };

    const response = await this.gatewayService.handleRequest(gatewayRequest);
    
    return {
      ...response,
      statusUrl: `/v1/op/tasks/${response.taskId}`,
      eventsUrl: `/v1/op/tasks/${response.taskId}/events`,
    };
  }

  /**
   * 格式化OCR响应数据
   */
  private formatOcrResponse(data: any): OcrResponseDto {
    if (!data) {
      return {};
    }

    // 如果数据已经是标准格式，直接返回
    if (data.sender || data.receiver || data.expressInfo) {
      return data;
    }

    // 尝试从不同的数据结构中提取信息
    const result: OcrResponseDto = {};

    // 提取发件人信息
    if (data.sender_name || data.sender_phone || data.sender_address) {
      result.sender = {
        name: data.sender_name,
        phone: data.sender_phone,
        address: data.sender_address,
      };
    }

    // 提取收件人信息
    if (data.receiver_name || data.receiver_phone || data.receiver_address) {
      result.receiver = {
        name: data.receiver_name,
        phone: data.receiver_phone,
        address: data.receiver_address,
      };
    }

    // 提取快递信息
    if (data.express_company || data.tracking_number) {
      result.expressInfo = {
        company: data.express_company,
        trackingNumber: data.tracking_number,
      };
    }

    // 提取置信度
    if (data.confidence !== undefined) {
      result.confidence = data.confidence;
    }

    // 如果没有提取到任何结构化信息，返回原始数据
    if (!result.sender && !result.receiver && !result.expressInfo) {
      return data;
    }

    return result;
  }

  /**
   * 获取OCR处理能力信息
   */
  @Post('capabilities')
  @ApiOperation({ summary: '获取OCR处理能力信息' })
  @ApiResponse({ 
    status: 200, 
    description: '处理能力信息',
  })
  async getOcrCapabilities() {
    this.logger.log('获取OCR处理能力信息');

    return {
      success: true,
      code: 200,
      data: {
        supportedFormats: ['jpeg', 'png', 'gif', 'pdf'],
        maxFileSize: '5MB',
        supportedTypes: ['logistics', 'general', 'document'],
        processingModes: [
          {
            mode: ProcessingMode.SYNC,
            description: '同步处理，适合小文件快速识别',
            maxFileSize: '1MB',
            avgResponseTime: '3-8秒',
          },
          {
            mode: ProcessingMode.ASYNC,
            description: '异步处理，适合大文件或批量处理',
            maxFileSize: '5MB',
            avgResponseTime: '10-30秒',
          },
          {
            mode: ProcessingMode.PROXY_ASYNC,
            description: '代理异步，网关等待结果后返回',
            maxFileSize: '3MB',
            avgResponseTime: '10-60秒',
            maxWaitTime: '60秒',
          },
        ],
        features: [
          '物流单据识别',
          '发件人/收件人信息提取',
          '快递公司识别',
          '运单号识别',
          '地址标准化',
          '置信度评分',
        ],
      },
    };
  }
}
