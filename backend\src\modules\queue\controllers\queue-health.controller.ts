import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { QueueManagerService } from '../services/queue-manager.service';
import { StructuredLogger } from '../../../common/logging/structured-logger';

/**
 * 队列健康检查控制器
 * 提供队列健康状态接口
 */
@ApiTags('队列健康检查')
@Controller('queue-health')
export class QueueHealthController {
  constructor(
    private readonly queueManagerService: QueueManagerService,
    private readonly logger: StructuredLogger,
  ) {}

  /**
   * 获取队列健康状态
   */
  @Get()
  @ApiOperation({
    summary: '获取队列健康状态',
    description: '返回所有队列的健康状态信息',
  })
  @ApiResponse({
    status: 200,
    description: '健康状态信息',
  })
  async getHealthStatus(): Promise<any> {
    try {
      // 获取所有队列状态
      const queueStatus = await this.queueManagerService.getQueueStatus();
      
      // 计算总任务数和运行状态
      const queues = queueStatus.queues;
      let totalActive = 0;
      let totalWaiting = 0;
      let hasError = false;
      
      for (const queue of queues) {
        totalActive += queue.active;
        totalWaiting += queue.waiting;
        
        // 检查队列是否有异常状态
        if (queue.failed > 100) { // 超过100个失败任务视为异常
          hasError = true;
        }
      }
      
      // 构造健康状态响应
      const healthStatus = {
        status: hasError ? 'warning' : 'healthy',
        timestamp: new Date().toISOString(),
        queues: queues.map(queue => ({
          name: queue.name,
          status: queue.failed > 100 ? 'warning' : 'healthy',
          active: queue.active,
          waiting: queue.waiting,
          completed: queue.completed,
          failed: queue.failed,
        })),
        summary: {
          activeJobs: totalActive,
          waitingJobs: totalWaiting,
          totalQueues: queues.length,
        }
      };
      
      this.logger.debug(
        '队列健康检查',
        { module: 'QueueHealthController', metadata: { status: healthStatus.status } }
      );
      
      return healthStatus;
    } catch (error) {
      this.logger.error(
        '获取队列健康状态失败',
        error,
        { module: 'QueueHealthController' }
      );
      
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }
} 