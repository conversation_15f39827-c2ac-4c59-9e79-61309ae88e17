import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ServiceEntity } from '../entities/service.entity';
import { ServiceStatus, ServiceType, PricingModel } from '../enums/service.enum';

export class ServiceResponseDto {
  @ApiProperty({
    description: '服务ID',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: '服务唯一代码',
    example: 'OCR_UPLOAD',
  })
  code: string;

  @ApiProperty({
    description: '服务名称',
    example: 'OCR文件上传识别',
  })
  name: string;

  @ApiProperty({
    description: '服务类型',
    enum: ServiceType,
    example: ServiceType.OCR,
  })
  type: ServiceType;

  @ApiProperty({
    description: '服务状态',
    enum: ServiceStatus,
    example: ServiceStatus.ACTIVE,
  })
  status: ServiceStatus;

  @ApiPropertyOptional({
    description: '服务描述',
    example: '支持图片文件上传进行OCR文字识别，提取图片中的文字信息',
  })
  description?: string;

  @ApiProperty({
    description: '定价模式',
    enum: PricingModel,
    example: PricingModel.PER_REQUEST,
  })
  pricingModel: PricingModel;

  @ApiProperty({
    description: '单价（元）',
    example: 0.1,
  })
  unitPrice: number;

  @ApiPropertyOptional({
    description: '服务端点URL',
    example: '/v1/op/ocr/upload',
  })
  endpoint?: string;

  @ApiProperty({
    description: '排序顺序',
    example: 100,
  })
  sortOrder: number;

  @ApiProperty({
    description: '创建时间',
    example: '2023-10-01T08:00:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
    example: '2023-10-10T14:30:00Z',
  })
  updatedAt: Date;

  constructor(entity: ServiceEntity) {
    this.id = entity.id;
    this.code = entity.code;
    this.name = entity.name;
    this.type = entity.type;
    this.status = entity.status;
    this.description = entity.description;
    this.pricingModel = entity.pricingModel;
    this.unitPrice = entity.unitPrice;
    this.endpoint = entity.endpoint;
    this.sortOrder = entity.sortOrder;
    this.createdAt = entity.createdAt;
    this.updatedAt = entity.updatedAt;
  }
}

export class ServiceListResponseDto {
  @ApiProperty({
    description: '服务列表',
    type: [ServiceResponseDto],
  })
  data: ServiceResponseDto[];

  @ApiProperty({
    description: '分页信息',
  })
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };

  constructor(data: ServiceEntity[], total: number, page: number, limit: number) {
    this.data = data.map(entity => new ServiceResponseDto(entity));
    this.meta = {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    };
  }
} 