// 仪表盘相关类型定义

// 统计卡片数据
export interface StatCard {
  title: string
  value: number | string
  unit?: string
  trend?: {
    value: number
    type: 'up' | 'down'
    period: string
  }
  icon?: string
  color?: string
}

// 使用趋势数据点
export interface TrendDataPoint {
  date: string
  value: number
  label?: string
}

// 使用趋势图表数据
export interface UsageTrendData {
  dates: string[]
  series: {
    name: string
    data: number[]
    color?: string
  }[]
}

// 服务使用分布数据
export interface ServiceDistributionItem {
  name: string
  value: number
  percentage: number
  color?: string
}

// 快速操作项
export interface QuickAction {
  id: string
  title: string
  description: string
  icon: string
  route?: string
  action?: () => void
  color?: string
}

// 最近活动项
export interface RecentActivity {
  id: string
  type: 'api_call' | 'payment' | 'error' | 'system'
  title: string
  description: string
  timestamp: string
  status?: 'success' | 'error' | 'warning' | 'info'
  metadata?: Record<string, any>
}

// 仪表盘概览数据
export interface DashboardOverview {
  // 统计卡片
  stats: {
    totalCalls: StatCard
    totalAmount: StatCard
    activeKeys: StatCard
    errorRate: StatCard
  }
  
  // 使用趋势
  usageTrend: UsageTrendData
  
  // 服务分布
  serviceDistribution: ServiceDistributionItem[]
  
  // 快速操作
  quickActions: QuickAction[]
  
  // 最近活动
  recentActivities: RecentActivity[]
}

// API 响应类型
export interface DashboardApiResponse {
  success: boolean
  data: DashboardOverview
  message?: string
}

// 时间范围选项
export type TimeRange = '7d' | '30d' | '90d' | '1y'

// 图表配置
export interface ChartConfig {
  height?: number
  showLegend?: boolean
  showTooltip?: boolean
  colors?: string[]
  animation?: boolean
}

// 仪表盘设置
export interface DashboardSettings {
  autoRefresh: boolean
  refreshInterval: number // 秒
  defaultTimeRange: TimeRange
  showQuickActions: boolean
  showRecentActivities: boolean
  theme: 'light' | 'dark' | 'auto'
}