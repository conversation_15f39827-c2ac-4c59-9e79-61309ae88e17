import {
  <PERSON><PERSON><PERSON>,
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BeforeI<PERSON>rt,
  Index
} from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';
import { ApiKeyStatus } from '../enums/api-key-status.enum';

/**
 * API密钥实体
 */
@Entity('api_keys')
export class ApiKeyEntity extends BaseEntity {
  @Column({ name: 'api_key', length: 64, unique: true })
  key: string;

  @Column({ name: 'user_id' })
  @Index()
  userId: number;

  @Column({ length: 100, nullable: true })
  name: string;

  @Column({
    type: 'enum',
    enum: ApiKeyStatus,
    default: ApiKeyStatus.ACTIVE
  })
  status: ApiKeyStatus;

  @Column('simple-array', { nullable: true })
  scopes: string[];

  @Column({ name: 'last_used_at', nullable: true })
  lastUsedAt: Date;

  @Column({ name: 'is_viewed', default: false })
  isViewed: boolean;

  @Column('simple-array', { name: 'allowed_ips', nullable: true })
  allowedIps: string[];

  @Column({ name: 'encrypted_secret_key', length: 255 })
  encryptedSecretKey: string;

  @Column({ name: 'expires_at', nullable: true })
  expiresAt: Date;

  @Column({ name: 'service_id', nullable: true })
  serviceId?: number;

  @Column({ name: 'key_type', default: 'user', length: 20 })
  keyType: string;

  /**
   * 检查API密钥是否已过期
   */
  isExpired(): boolean {
    if (!this.expiresAt) return false;
    return this.expiresAt < new Date();
  }

  /**
   * 检查API密钥是否具有指定的权限范围
   */
  hasScope(scope: string): boolean {
    if (!this.scopes || !this.scopes.length) return false;
    return this.scopes.includes(scope) || this.scopes.includes('*');
  }

  /**
   * 检查API密钥是否允许从指定IP访问
   */
  isAllowedIp(ip: string): boolean {
    if (!this.allowedIps || !this.allowedIps.length) return true;
    
    return this.allowedIps.some(allowedIp => {
      // 精确匹配
      if (allowedIp === ip) return true;
      
      // 通配符匹配 (例如 192.168.1.*)
      if (allowedIp.endsWith('*')) {
        const prefix = allowedIp.slice(0, -1);
        return ip.startsWith(prefix);
      }
      
      // CIDR匹配 (例如 ***********/24)
      if (allowedIp.includes('/')) {
        try {
          const [network, bits] = allowedIp.split('/');
          const mask = ~(2 ** (32 - parseInt(bits)) - 1);
          
          // 简单的IP转换为数字
          const ipToNum = (ip: string) => {
            return ip.split('.').reduce((sum, octet) => (sum << 8) + parseInt(octet), 0) >>> 0;
          };
          
          const ipNum = ipToNum(ip);
          const networkNum = ipToNum(network);
          
          return (ipNum & mask) === (networkNum & mask);
        } catch (e) {
          return false;
        }
      }
      
      return false;
    });
  }

  /**
   * 检查API密钥是否有效
   */
  isValid(): boolean {
    return (
      this.status === ApiKeyStatus.ACTIVE &&
      !this.isExpired() &&
      this.baseStatus === 1 &&
      this.delFlag === '0'
    );
  }
} 