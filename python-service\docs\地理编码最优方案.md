# 地理坐标逆解析与正解析最优解决方案（阿里云部署）

本文档提供在阿里云服务器（Ubuntu 24.04.2 LTS）上部署开放平台全栈应用的详细步骤。

### 系统环境
- **操作系统**: Ubuntu 24.04.2 LTS (Noble Numbat)
- **架构**: x86_64
- **CPU**: Intel Xeon Platinum 8369B (4核)
- **虚拟化**: KVM

## 1. 背景与目标
- **背景**: 现有 `python-service` 已提供地理编码与 OCR 能力，目标是在中国大陆环境实现高可用、免费开源、自主可控的地理编码服务。
- **目标**: 部署本地 Nominatim（OSM 数据）并在应用层实现坐标系自动识别与转换（WGS84/GCJ-02/BD-09），以提升国内使用准确性与兼容性。

## 2. 方案选型评估
- **Nominatim（基于 OpenStreetMap，PostgreSQL + PostGIS）**
  - 优点：官方权威、生态成熟、社区广、免费开源、自托管、语言本地化良好（支持 `accept-language=zh-CN`）。
  - 缺点：数据导入与更新成本较高；对存储与内存有要求。
- **Pelias（Elasticsearch 生态）**
  - 优点：扩展强、搜索体验好；
  - 缺点：依赖 ES/Node.js，多组件部署复杂；对中文地址解析需额外插件调优；资源占用较高。
- **Photon（基于 Elasticsearch，使用 OpenStreetMap 数据）**
  - 优点：部署较简单；
  - 缺点：中文地址体验相对一般，正向搜索更友好，逆解析能力不如 Nominatim 精细。
- **第三方商业/公共 API（高德/腾讯/百度）**
  - 优点：国内可用性高，POI 丰富；
  - 缺点：非纯开源自托管，需密钥与额度，存在合规与成本约束。

**推荐**：以自建本地 Nominatim 为主，满足“免费开源、自主可控”；可保留第三方 API 作为可选兜底（生产默认关闭）。

## 3. 推荐架构与拓扑
- **部署拓扑**：
  - `python-service`（Flask + geopy）→ 通过环境变量 `NOMINATIM_LOCAL_URL` 调用 → `Nominatim`（内网 HTTP）→ `PostgreSQL + PostGIS`（同 VPC 内）。
  - 可选：仅在灰度/应急时启用高德/腾讯/百度兜底（关闭默认 API KEY 即可禁用）。
- **通信**：全部通过专有网络（VPC）访问，避免公网。
- **本地化**：调用设置 `accept-language=zh-CN`，返回尽量使用中文字段。

## 4. 数据库与数据源
- **数据库**：PostgreSQL 13/14 + PostGIS 3.x（Aliyun 自建或 ECS 自建）。
- **数据源**：导入中国区 PBF（推荐 `asia/china-latest.osm.pbf`），不需全量地球数据。
- **规模估算（仅供参考）**：
  - 中国区数据导入后，数据库磁盘约 50–120 GB；
  - 导入期间临时中间文件与索引会更大，预留 200 GB+ 更稳妥。
- **更新机制**：
  - 使用 Nominatim 提供的 `replication` 增量更新；
  - 更新周期建议每日/每周；
  - 设定维护窗口，监控失败重试。

## 5. 阿里云资源与容量规划
- **计算规格（参考）**：
  - 导入阶段：8 vCPU / 32 GB RAM 起，SSD ≥ 300 GB；
  - 运行阶段：4–8 vCPU / 16–32 GB RAM，SSD ≥ 200 GB；
  - 若峰值 QPS ≥ 100，可前置 Nginx + 缓存（可选）。
- **网络**：同 VPC 下子网互通；关闭公网访问或加白名单。
- **系统**：建议 Ubuntu 20.04/22.04，时区 Asia/Shanghai。

## 6. 部署步骤（推荐使用容器）
### 6.1 Nominatim（Docker 方式）
- 拉取镜像：`nominatim/nominatim:<version>` 或 `mediagis/nominatim:<version>`（选择稳定版本，例如 4.3）。
- 准备数据：将 `china-latest.osm.pbf` 上传至同一台 ECS（可先下载到本地，再通过 OSS/内网拷贝至 ECS，避免跨境网络不稳定）。
- 初始化与导入（简化示例）：
  ```bash
  docker run -e PBF_URL=/data/china-latest.osm.pbf \
             -e NOMINATIM_DATABASE=nominatim \
             -e POSTGRES_SHARED_BUFFERS=2GB \
             -e POSTGRES_MAINTENANCE_WORK_MEM=2GB \
             -v /data/osm:/data \
             -v /var/lib/postgresql/14/main:/var/lib/postgresql/14/main \
             -p 8080:8080 \
             --name nominatim \
             nominatim/nominatim:4.3
  ```
- 健康检查：访问 `http://<ECS内网IP>:8080/search?q=北京&format=json` 验证。
- 更新：配置增量更新任务（crontab 或容器内置 updater）。

### 6.2 与 `python-service` 对接
- 在 `python-service` 环境设置：
  - `NOMINATIM_LOCAL_URL=http://<ECS内网IP或服务名>:8080`
  - `TIMEOUT=300`（已支持）
  - `DISABLE_PADDLE_DOWNLOAD=true`（生产一般离线，不在启动时下载模型）
- 现有 `app.py` 已优先使用本地 Nominatim（见 `EnhancedFallbackNominatim`），无需改代码即可接入。
- 生产建议：关闭外部兜底（不配置商业 API KEY；或在网关层做域名黑白名单限制）。

### 6.3 运行时与依赖版本建议（Ubuntu 24.04 环境）
为兼容 OCR（PaddlePaddle/PaddleOCR）生态与国内镜像，推荐使用以下容器与依赖矩阵：
- **容器基线（推荐其一）**：
  - `python:3.10-slim`（首选；体积小、与 Paddle 2.5.0 兼容性好）
  - 或 `ubuntu:22.04` + `python3.10`（避免 24.04 系统 Python 3.12 的兼容问题）
- **系统包**：`tzdata`、`curl`、`gcc`、`g++`、`libgl1`、`libglib2.0-0`、`libsm6`、`libxrender1`、`libxext6`
  - 注意：不要再安装 `libssl1.1`（在 22.04+ 已弃用）。
- **Python 版本**：3.10.x（建议固定小版本）
- **Python 依赖建议版本（与现有兼容且适配 3.10）**：
  - Web 基础：`flask==2.0.1`、`flask-cors==3.0.10`、`Werkzeug==2.0.1`、`gunicorn==20.1.0`
  - 通信工具：`requests==2.31.0`（或保留 `2.27.1`，如镜像锁定）
  - 数据计算：`numpy==1.24.4`、`pandas==1.5.3`
  - NLP：`jieba==0.42.1`、`cpca==0.5.5`
  - 地理处理：`geopy==2.3.0`、`shapely==1.8.0`、`geojson==2.5.0`
  - OCR：`opencv-python-headless==********`、`paddlepaddle==2.5.0`、`paddleocr==*******`
  - 其他：`python-dotenv==0.19.2`、`python-multipart==0.0.5`、`psutil==5.9.0`
- **国内加速**：PIP 源使用阿里云镜像；Paddle 模型建议预热缓存，生产禁用启动期在线下载（`DISABLE_PADDLE_DOWNLOAD=true`）。

> 说明：Ubuntu 24.04 系统自带 Python 3.12，但 Paddle 2.5.0 对 3.12 支持不足。因此采用容器内 Python 3.10，既与现有依赖兼容，又便于在阿里云环境稳定落地。

## 7. 接口契约（对外，统一无 v1/op 前缀）
- `GET/POST /geo/reverse`
  - 请求：
    - `latitude`（float，必填）
    - `longitude`（float，必填）
    - `coordType`（可选：`wgs84|gcj02|bd09`，默认 `auto`）
    - `convertTo`（可选：`wgs84|gcj02|bd09`，默认 `wgs84`）
    - `autoDetect`（可选：bool，默认 `true`）
  - 响应（预留阶段）：
    ```json
    {
      "success": false,
      "code": 50101,
      "message": "Geo service under development",
      "data": null
    }
    ```
- `GET/POST /geo/forward`
  - 请求：
    - `address`（string，必填）
    - `coordType`（可选：`wgs84|gcj02|bd09`，默认 `wgs84`，仅决定输出）
    - `convertTo`（同上，若提供则以该坐标系输出）
  - 响应（预留阶段）：
    ```json
    {
      "success": false,
      "code": 50101,
      "message": "Geo service under development",
      "data": null
    }
    ```

### 7.1 坐标系与转换（中国大陆自动化最佳实践）
- 背景：
  - 国内主流地图（高德/腾讯/百度/天地图）多返回 GCJ-02 或 BD-09；
  - Nominatim/OSM 基于 WGS84；
  - 台湾及海外地区常用 WGS84。
- 输入处理策略（逆解析 `/geo/reverse`）：
  1) 若显式提供 `coordType`：按其将坐标转换为 WGS84 再调用 Nominatim。
  2) 若 `coordType=auto`（默认）：
     - 判断坐标是否落于中国大陆边界（使用多边形/包围盒快速判定）；
     - 若在大陆范围内：
       - 首选假定为 GCJ-02，转换为 WGS84 调用；
       - 同时可选进行“双路径验证”（原值当作 WGS84 与 GCJ→WGS 双方案比较行政区/距离，选择更可信结果）。
     - 若不在大陆范围：按 WGS84 处理。
- 输出处理策略：
  - 统一内部处理为 WGS84；
  - 若请求携带 `convertTo=gcj02|bd09`，则在返回前做一次坐标变换；
  - 响应中返回最终 `coord_type` 字段，指明输出坐标系。
- 正解析 `/geo/forward`：
  - Nominatim 得到 WGS84；
  - 若携带 `coordType/convertTo` 为 `gcj02|bd09`，再做坐标转换后输出。
- 实现方式（优先免费开源）：
  - 使用内置的轻量级纯 Python 转换函数（MIT 开源实现），提供 `wgs84<->gcj02`、`gcj02<->bd09`、`wgs84<->bd09`；
  - 不依赖 `pyproj`（其不支持 GCJ-02/BD-09 专有算法），避免引入额外大型依赖；
  - 算法精度：误差典型 5–20m，满足逆/正解析业务需求。

### 7.2 统一错误码与限额
- 错误码：`0/40001/40002/42901/50001/50201/50401/40003`（详见《模块需求与规格说明》0 章）
- 新增：`50101`：FeatureInDevelopment（功能开发中）
- 限额（网关侧）：每 IP 120 rpm；
- 新增错误码建议：
  - `40003`：UnsupportedCoordType（不支持的坐标系）

### 7.3 坐标系自动识别实现方案（Auto-Detect）
- **目标**：在未显式提供 `coordType` 时，自动判断坐标是否为 GCJ-02/BD-09 或 WGS84，并在调用 Nominatim 前转换成 WGS84。
- **步骤**：
  1. 边界判定：使用中国大陆多边形 GeoJSON（不含港澳台）配合 `shapely` 的 `Point.within(Polygon)`，若不在大陆范围，直接按 WGS84 处理。
  2. 候选构造：
     - 候选 A（WGS84 假设）：直接使用输入坐标调用 Nominatim（无需转换）。
     - 候选 B（GCJ-02 假设）：先执行 `gcj02_to_wgs84` 转换，再调用 Nominatim。
     - 候选 C（BD-09 假设，可选）：先 `bd09_to_gcj02` 再 `gcj02_to_wgs84`，再调用 Nominatim。
  3. 评分挑选：对候选结果基于以下信号打分，选择最高者：
     - 地址完整度：是否包含省/市/区（中文）等关键组件；
     - 返回点与输入点距离：将 Nominatim 的 `display_name` 进行一次 `forward` 验证得到坐标，计算 Haversine 距离（越小越好）；
     - 行政区一致性：候选返回的 `state/city/district` 是否匹配输入点所在行政边界。
  4. 输出转换：内部统一 WGS84 处理后，若请求带有 `convertTo`，再转换为 `gcj02|bd09` 返回，`coord_type` 字段标注最终输出坐标系。
- **库选择（免费开源）**：
  - 多边形判定：`shapely`（已在依赖中）+ 简化的中国大陆 GeoJSON 边界（可内置小文件，来源 OSM/开源仓库）。
  - 坐标转换：自实现轻量级纯 Python 函数（MIT 协议）或引入体积小的第三方实现（如开源实现片段），提供 `wgs84<->gcj02`、`gcj02<->bd09`、`wgs84<->bd09`。
  - 不使用 `pyproj`（不覆盖 GCJ/BD 专有算法），避免引入不必要的大依赖。
- **注意**：自动识别是启发式，少量边界场景可能误判，建议开放 `coordType` 显式参数用于强制指定，供高可靠调用使用。

### 7.1 对接后端统一端点定义（无 v1/op 前缀）
为与 backend 网关端点保持一致（去掉 `v1/op` 前缀），python-service 对外文档与网关约定如下：
- **GEO_REVERSE**: `/geo/reverse`
  - 方法：GET/POST
  - 入参：`latitude`、`longitude`、可选 `coordType`（默认 `wgs84`）
  - 行为：等价当前 `/rev-geo`；推荐迁移至新路径
- **GEO_FORWARD**: `/geo/forward`
  - 方法：GET/POST
  - 入参：`address`、可选 `coordType`（默认 `wgs84`）
  - 行为：等价当前 `/forward-geo`；推荐迁移至新路径

### 7.2 兼容性与迁移建议
- 兼容端点（现存）：`/rev-geo`、`/forward-geo` 继续可用；
- 推荐在网关层将 `/geo/reverse` → python-service `/rev-geo`，`/geo/forward` → `/forward-geo`（或直连新路径后端实现时）；
- 文档与 SDK 新增以 `/geo/*` 为主，老端点标注“兼容保留”。

## 8. 性能与调优
- Postgres/OSM 导入参数根据内存与磁盘 IO 调整（`shared_buffers`、`