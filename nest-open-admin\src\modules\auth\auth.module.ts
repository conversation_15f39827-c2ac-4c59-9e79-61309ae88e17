
/**
 * 认证模块
 * 处理用户认证、注册、登录、登出、验证码
 */
import { Module } from '@nestjs/common';
import { GoogleRecaptchaModule } from '@nestlab/google-recaptcha';
import { ConfigService } from '@nestjs/config'
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { UserModule } from '../user/user.module';
import { ApiKeyModule } from '../api-key/api-key.module';

@Module({
  imports:[
    GoogleRecaptchaModule.forRootAsync({
      useFactory: (configService) => ({
        secretKey: configService.get('RECAPTCHA_SECRET_KEY') || '6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe',
        response: (req) => req.headers['x-recaptcha-token'] || req.body.recaptchaToken,
        skipIf: configService.get('NODE_ENV') === 'development',
        score: 0.5,
        actions: ['login', 'register', 'submit'],
      }),
      inject: [ConfigService],
    }),
    UserModule,
    ApiKeyModule,
  ],
  controllers: [AuthController],
  providers: [AuthService],
  exports: [],
})
export class AuthModule {}