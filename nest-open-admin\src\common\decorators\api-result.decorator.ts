import { applyDecorators } from '@nestjs/common';
import { ApiResponse, ApiResponseOptions } from '@nestjs/swagger';

/**
 * API结果装饰器
 * 用于标准化API响应格式的Swagger文档
 */
export function ApiResult(options?: ApiResponseOptions & { dataSchema?: any }) {
  return applyDecorators(
    ApiResponse({
      status: 200,
      description: '请求成功',
      schema: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            description: '请求是否成功',
            example: true
          },
          code: {
            type: 'number',
            description: '响应状态码',
            example: 200
          },
          message: {
            type: 'string',
            description: '响应消息',
            example: '操作成功'
          },
          data: {
            description: '响应数据',
            ...(options?.dataSchema || {})
          },
          timestamp: {
            type: 'string',
            description: '响应时间戳',
            example: '2023-12-01T12:00:00.000Z'
          }
        }
      },
      ...options
    })
  );
}