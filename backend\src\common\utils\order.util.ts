import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';

/**
 * 生成订单号
 * 格式: ORD + YYYYMMDD + 8位随机数
 * 例如: ORD202312251234ABCD
 */
export function generateOrderNo(): string {
  const dateStr = dayjs().format('YYYYMMDD');
  const randomStr = Math.random().toString(36).substring(2, 10).toUpperCase();
  return `ORD${dateStr}${randomStr}`;
}

/**
 * 生成支付流水号
 * 格式: PAY + YYYYMMDD + 8位随机数
 * 例如: PAY202312251234ABCD
 */
export function generatePaymentNo(): string {
  const dateStr = dayjs().format('YYYYMMDD');
  const randomStr = Math.random().toString(36).substring(2, 10).toUpperCase();
  return `PAY${dateStr}${randomStr}`;
}

/**
 * 生成退款流水号
 * 格式: REF + YYYYMMDD + 8位随机数
 * 例如: REF202312251234ABCD
 */
export function generateRefundNo(): string {
  const dateStr = dayjs().format('YYYYMMDD');
  const randomStr = Math.random().toString(36).substring(2, 10).toUpperCase();
  return `REF${dateStr}${randomStr}`;
}

/**
 * 生成唯一请求ID
 * 使用UUID v4格式
 */
export function generateRequestId(): string {
  return uuidv4();
}

/**
 * 验证订单号格式
 * @param orderNo 订单号
 * @returns 是否有效
 */
export function validateOrderNo(orderNo: string): boolean {
  const pattern = /^ORD\d{8}[A-Z0-9]{8}$/;
  return pattern.test(orderNo);
}

/**
 * 验证支付流水号格式
 * @param paymentNo 支付流水号
 * @returns 是否有效
 */
export function validatePaymentNo(paymentNo: string): boolean {
  const pattern = /^PAY\d{8}[A-Z0-9]{8}$/;
  return pattern.test(paymentNo);
}

/**
 * 计算订单过期时间
 * @param minutes 过期分钟数，默认30分钟
 * @returns 过期时间
 */
export function calculateOrderExpireTime(minutes: number = 30): Date {
  return dayjs().add(minutes, 'minute').toDate();
}

/**
 * 格式化金额
 * @param amount 金额
 * @param precision 精度，默认2位小数
 * @returns 格式化后的金额
 */
export function formatAmount(amount: number, precision: number = 2): number {
  return Number(amount.toFixed(precision));
}

/**
 * 计算折扣金额
 * @param originalAmount 原金额
 * @param discountRate 折扣率（0-1之间）
 * @returns 折扣金额
 */
export function calculateDiscountAmount(originalAmount: number, discountRate: number): number {
  if (discountRate < 0 || discountRate > 1) {
    throw new Error('折扣率必须在0-1之间');
  }
  return formatAmount(originalAmount * discountRate);
}

/**
 * 计算实付金额
 * @param originalAmount 原金额
 * @param discountAmount 折扣金额
 * @returns 实付金额
 */
export function calculatePaidAmount(originalAmount: number, discountAmount: number): number {
  const paidAmount = originalAmount - discountAmount;
  return formatAmount(Math.max(0, paidAmount));
}