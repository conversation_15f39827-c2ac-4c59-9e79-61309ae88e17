import { defineStore } from 'pinia'
import { ref } from 'vue'
import { request } from '@/utils/request'
import { ElMessage } from 'element-plus'

export interface ApiKey {
  id: number
  name: string
  apiKey: string
  secretKey?: string
  isSecretViewed?: boolean
  description?: string
  permissions: string[]
  keyStatus: 'active' | 'disabled'
  lastUsedAt?: string
  createdAt: string
  updatedAt: string
  totalCalls?: number
  monthlyCalls?: number
  todayCalls?: number
}

export interface CreateApiKeyForm {
  name: string
  description?: string
  permissions: string[]
}

export const useApiKeyStore = defineStore('apiKey', () => {
  // 状态
  const apiKeys = ref<ApiKey[]>([])
  const loading = ref(false)

  // 获取API密钥列表
  const getApiKeysByUserId = async (userId?: number) => {
    try {
      loading.value = true
      const response: Record<string, any> = await request.get('/api-keys')
      // 新backend返回的数据结构：{ success: true, data: { items: [...], meta: {...} } }
      apiKeys.value = response.data?.items || response.items || response || []
      return response
    } catch (error) {
      console.error('获取API密钥列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建API密钥
  const createApiKey = async (data: CreateApiKeyForm) => {
    try {
      loading.value = true
      const response = await request.post('/api-keys', {
        name: data.name,
        description: data.description,
        scopes: data.permissions || []
      })
      return response
    } catch (error) {
      console.error('创建API密钥失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除API密钥
  const deleteApiKey = async (id: number) => {
    try {
      loading.value = true
      await request.delete(`/api-keys/${id}`)
      return true
    } catch (error) {
      console.error('删除API密钥失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 启用/禁用API密钥
  const toggleApiKeyStatus = async (id: number, status: 'active' | 'disabled') => {
    try {
      const response = await request.patch(`/api-keys/${id}`, { status })
      return response
    } catch (error) {
      console.error('切换API密钥状态失败:', error)
      throw error
    }
  }

  // 重置API密钥秘钥
  const resetApiKeySecret = async (id: number) => {
    try {
      loading.value = true
      const response = await request.patch(`/api-keys/${id}/regenerate`)
      return response
    } catch (error) {
      console.error('重置API密钥秘钥失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 标记密钥已查看
  const markSecretAsViewed = async (id: number) => {
    try {
      const response = await request.patch(`/api-keys/${id}/mark-viewed`)
      return response
    } catch (error) {
      console.error('标记密钥已查看失败:', error)
      throw error
    }
  }

  return {
    // 状态
    apiKeys,
    loading,

    // 方法
    getApiKeysByUserId,
    createApiKey,
    deleteApiKey,
    toggleApiKeyStatus,
    resetApiKeySecret,
    markSecretAsViewed,
  }
})
