<template>
  <div class="home-page">
      <!-- 英雄区域 -->
      <section class="hero-section">
        <div class="hero-background">
          <div class="floating-elements">
            <div class="floating-icon icon-1">
              <el-icon size="40"><DataAnalysis /></el-icon>
            </div>
            <div class="floating-icon icon-2">
              <el-icon size="35"><Setting /></el-icon>
            </div>
            <div class="floating-icon icon-3">
              <el-icon size="45"><Platform /></el-icon>
            </div>
            <div class="floating-icon icon-4">
              <el-icon size="30"><Document /></el-icon>
            </div>
            <div class="floating-icon icon-5">
              <el-icon size="38"><Picture /></el-icon>
            </div>
          </div>
        </div>
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">专业的AI服务解决方案</h1>
            <p class="hero-subtitle">
              专业的AI服务平台，为开发者提供高质量的人工智能API服务， 助力企业数字化转型和智能化升级
            </p>
            <div class="hero-actions">
              <el-button type="primary" size="large" @click="getStarted" class="primary-btn">
                立即开始
              </el-button>
              <el-button size="large" @click="viewDocs" class="secondary-btn">
                查看文档
              </el-button>
            </div>
          </div>
          <div class="hero-image">
            <div class="hero-illustration">
              <div class="main-circle">
                <el-icon size="80" color="#ffffff"><Platform /></el-icon>
                <!-- <img class="logo-icon" :src="logo" alt=""/> -->
              </div>
              <div class="orbit-circle circle-1">
                <el-icon size="24" color="#60a5fa"><DataAnalysis /></el-icon>
              </div>
              <div class="orbit-circle circle-2">
                <el-icon size="24" color="#34d399"><Setting /></el-icon>
              </div>
              <div class="orbit-circle circle-3">
                <el-icon size="24" color="#fbbf24"><Document /></el-icon>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 特性介绍 -->
      <section class="features-section">
        <div class="container">
          <h2 class="section-title">平台特色</h2>
          <div class="features-grid">
            <div class="feature-card" v-for="feature in features" :key="feature.title">
              <div class="feature-icon">
                <el-icon size="40" :color="feature.color">
                  <component :is="feature.icon" />
                </el-icon>
              </div>
              <h3 class="feature-title">{{ feature.title }}</h3>
              <p class="feature-description">{{ feature.description }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 服务展示 -->
      <section class="services-section">
        <div class="container">
          <h2 class="section-title">核心服务</h2>
          <div class="services-grid">
            <div class="service-card" v-for="service in services" :key="service.name">
              <div class="service-header">
                <el-icon size="32" :color="service.color">
                  <component :is="service.icon" />
                </el-icon>
                <h3 class="service-name">{{ service.name }}</h3>
              </div>
              <p class="service-description">{{ service.description }}</p>
              <div class="service-features">
                <el-tag
                  v-for="tag in service.tags"
                  :key="tag"
                  size="small"
                  class="service-tag"
                >
                  {{ tag }}
                </el-tag>
              </div>
              <div class="service-actions">
                <el-button type="primary" text @click="tryService(service.key)">
                  立即体验
                </el-button>
                <el-button text @click="viewServiceDocs(service.key)">
                  查看文档
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 使用统计 -->
      <section class="stats-section">
        <div class="container">
          <div class="stats-grid">
            <div class="stat-item" v-for="stat in stats" :key="stat.label">
              <div class="stat-number">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 快速开始 -->
      <section class="quickstart-section">
        <div class="container">
          <h2 class="section-title">快速开始</h2>
          <div class="quickstart-steps">
            <div class="step" v-for="(step, index) in quickstartSteps" :key="index">
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-content">
                <h3 class="step-title">{{ step.title }}</h3>
                <p class="step-description">{{ step.description }}</p>
                <div class="step-code" v-if="step.code">
                  <el-input
                    v-model="step.code"
                    type="textarea"
                    :rows="3"
                    readonly
                    class="code-input"
                  />
                  <el-button
                    size="small"
                    text
                    @click="copyCode(step.code)"
                    class="copy-btn"
                  >
                    <el-icon><CopyDocument /></el-icon>
                    复制
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Platform,
  Lightning,
  Lock,
  DataAnalysis,
  Setting,
  Document,
  Picture,
  ChatDotRound,
  MagicStick,
  CopyDocument
} from '@element-plus/icons-vue'
import logo from '@/assets/logo.svg'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 平台特色
const features = ref([
  {
    icon: Lightning,
    color: '#409eff',
    title: '高性能',
    description: '基于现代化架构设计，提供毫秒级响应速度，支持高并发访问'
  },
  {
    icon: Lock,
    color: '#67c23a',
    title: '安全可靠',
    description: '企业级安全保障，数据加密传输，完善的权限控制机制'
  },
  {
    icon: DataAnalysis,
    color: '#e6a23c',
    title: '智能分析',
    description: '提供详细的使用统计和分析报告，帮助优化业务决策'
  },
  {
    icon: Setting,
    color: '#f56c6c',
    title: '易于集成',
    description: 'RESTful API设计，提供多语言SDK，快速集成到您的应用中'
  }
])

// 核心服务
const services = ref([
  {
    key: 'ocr',
    name: 'OCR识别',
    icon: Picture,
    color: '#409eff',
    description: '支持多种图片格式的文字识别，包括身份证、发票、票据等专业场景',
    tags: ['文字识别', '票据识别', '身份证识别', '发票识别']
  },
  {
    key: 'nlp',
    name: 'NLP处理',
    icon: ChatDotRound,
    color: '#67c23a',
    description: '提供文本分析、情感分析、关键词提取、文本分类等自然语言处理服务',
    tags: ['文本分析', '情感分析', '关键词提取', '文本分类']
  },
  {
    key: 'ai-generation',
    name: 'AI生成',
    icon: MagicStick,
    color: '#e6a23c',
    description: '基于先进的AI模型，提供文本生成、图像生成、代码生成等创作服务',
    tags: ['文本生成', '图像生成', '代码生成', '创意写作']
  }
])

// 使用统计
const stats = ref([
  { label: '注册用户', value: '10,000+' },
  { label: 'API调用次数', value: '1,000,000+' },
  { label: '服务可用性', value: '99.9%' },
  { label: '平均响应时间', value: '<100ms' }
])

// 快速开始步骤
const quickstartSteps = ref([
  {
    title: '注册账号',
    description: '创建您的开发者账号，获取访问平台的权限',
    code: ''
  },
  {
    title: '获取API密钥',
    description: '在控制台中创建API密钥，用于身份验证',
    code: ''
  },
  {
    title: '调用API',
    description: '使用您的API密钥调用我们的服务',
    code: `curl -X POST "${import.meta.env.VITE_API_BASE_URL}/open/v1/ocr/recognize" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{"image": "base64_encoded_image"}'`
  }
])

// 立即开始
const getStarted = () => {
  if (userStore.isLoggedIn) {
    router.push('/console')
  } else {
    router.push('/register')
  }
}

// 查看文档
const viewDocs = () => {
  router.push('/docs')
}

// 体验服务
const tryService = (serviceKey: string) => {
  router.push('/playground')
}

// 查看服务文档
const viewServiceDocs = (serviceKey: string) => {
  router.push(`/docs#${serviceKey}`)
}

// 复制代码
const copyCode = async (code: string) => {
  try {
    await navigator.clipboard.writeText(code)
    ElMessage.success('代码已复制到剪贴板')
  } catch {
    ElMessage.error('复制失败，请手动复制')
  }
}
</script>

<style scoped>
.home-page {
  min-height: calc(100vh - 70px);
}

/* 英雄区域 */
.hero-section {
  position: relative;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  color: white;
  padding: 120px 0;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.floating-elements {
  position: relative;
  width: 100%;
  height: 100%;
}

.floating-icon {
  position: absolute;
  color: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.icon-1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.icon-2 {
  top: 20%;
  right: 15%;
  animation-delay: 1s;
}

.icon-3 {
  bottom: 30%;
  left: 5%;
  animation-delay: 2s;
}

.icon-4 {
  bottom: 15%;
  right: 10%;
  animation-delay: 3s;
}

.icon-5 {
  top: 50%;
  left: 50%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 3fr 2fr; /* 3:2 比例，接近 60%:40% */
  gap: 80px;
  align-items: center;
  padding: 0 20px;
}
.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 30px;
  line-height: 1.2;
  background: linear-gradient(45deg, #ffffff, #e0e7ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 40px;
  line-height: 1.6;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.9);
}

.hero-actions {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.primary-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 14px 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  position: relative;
  overflow: hidden;
}

.primary-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s;
}

.primary-btn:hover::before {
  opacity: 1;
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.secondary-btn {
  background: transparent;
  color: #ffffff;
  border: 2px solid rgba(255, 255, 255, 0.8);
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s;
}

.secondary-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 1);
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-illustration {
  position: relative;
  width: 300px;
  height: 300px;
}

.main-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 160px;
  height: 160px;
  background: linear-gradient(45deg, #60a5fa, #3b82f6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.3);
  animation: pulse 3s ease-in-out infinite;
}

.orbit-circle {
  position: absolute;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.circle-1 {
  top: 20px;
  right: 40px;
  animation: orbit 8s linear infinite;
}

.circle-2 {
  bottom: 20px;
  left: 20px;
  animation: orbit 10s linear infinite reverse;
}

.circle-3 {
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  animation: orbit 12s linear infinite;
}

@keyframes pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.05); }
}

@keyframes orbit {
  from { transform: rotate(0deg) translateX(100px) rotate(0deg); }
  to { transform: rotate(360deg) translateX(100px) rotate(-360deg); }
}

/* 通用容器 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 60px;
  color: #303133;
}

/* 特性区域 */
.features-section {
  padding: 100px 0;
  background: white;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.feature-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(59, 130, 246, 0.15);
  border-radius: 16px;
  padding: 32px;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
}

.feature-card:hover {
  transform: translateY(-8px);
  background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(248, 250, 252, 1) 100%);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.15);
}

.feature-icon {
  margin-bottom: 20px;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #1e293b;
}

.feature-description {
  color: #64748b;
  line-height: 1.6;
}

/* 服务区域 */
.services-section {
  padding: 100px 0;
  background: #f8f9fa;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.service-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  transition: all 0.3s;
  cursor: pointer;
  border: 1px solid rgba(59, 130, 246, 0.12);
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.25);
  background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(248, 250, 252, 1) 100%);
}

.service-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.service-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-left: 12px;
  color: #303133;
}

.service-description {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 20px;
}

.service-features {
  margin-bottom: 20px;
}

.service-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.service-actions {
  display: flex;
  gap: 15px;
}

/* 统计区域 */
.stats-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.stats-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.05) 100%);
  pointer-events: none;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 10px;
  color: #60a5fa;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: 1.1rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.9);
}

/* 快速开始区域 */
.quickstart-section {
  padding: 100px 0;
  background: white;
}

.quickstart-steps {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.step {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

.step-number {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  font-weight: 600;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #303133;
}

.step-description {
  color: #606266;
  margin-bottom: 15px;
  line-height: 1.6;
}

.step-code {
  position: relative;
}

.code-input {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.copy-btn {
  position: absolute;
  top: 10px;
  right: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-section {
    padding: 80px 0;
  }
  
  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .hero-actions {
    justify-content: center;
  }
  
  .hero-illustration {
    width: 250px;
    height: 250px;
  }
  
  .main-circle {
    width: 120px;
    height: 120px;
  }
  
  .orbit-circle {
    width: 45px;
    height: 45px;
  }
  
  .floating-icon {
    display: none;
  }
  
  .features-grid,
  .services-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .step {
    flex-direction: column;
    gap: 15px;
  }
  
  .step-number {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 60px 0;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .hero-illustration {
    width: 200px;
    height: 200px;
  }
  
  .main-circle {
    width: 100px;
    height: 100px;
  }
  
  .primary-btn,
  .secondary-btn {
    padding: 10px 24px;
    font-size: 14px;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}
</style>
