# 开发平台前端项目介绍

## 一、平台定位
本项目是开放平台的前端实现，面向开发者和企业用户，提供一站式API服务接入、管理和监控能力。前端采用 Vue3 + Element Plus + Pinia + TypeScript 技术栈，注重用户体验和高效开发。

## 二、主要功能
- 用户注册、登录、认证
- API密钥管理与权限控制
- API服务浏览、订购、试用
- 调用记录与用量统计
- 订单管理与在线支付
- 平台公告、文档、帮助中心
- 支持异步任务、队列、批量处理

## 三、技术架构
- 前端：Vue3、Element Plus、Pinia、TypeScript、Vite
- 后端：NestJS（见 backend 目录），通过 RESTful API 提供全部数据和业务能力
- 前后端完全分离，所有数据交互均通过 API 完成
- 统一接口管理，接口定义见 `src/api` 目录

## 四、用户流程
1. 访问平台首页，了解平台能力和服务介绍
2. 注册/登录账号，进入控制台
3. 生成API密钥，选择所需服务
4. 订购服务或试用，获取调用额度
5. 通过API调用服务，查看调用记录和用量
6. 如需扩容或续费，进入订单/支付页面操作

## 五、页面结构
- 公共区（未登录）：首页、服务介绍、文档、隐私政策、服务条款、登录/注册
- 控制台（登录后）：API密钥管理、服务管理、调用记录、订单管理、个人中心、消息通知等
- 其它：错误页、帮助中心、公告栏等

## 六、与后端协作方式
- 所有页面数据均通过后端API获取，禁止前端写死测试数据
- API接口统一封装在 `src/api` 目录，便于维护和复用
- 遵循 RESTful 设计，接口权限、认证、错误处理与后端保持一致
- 前端根据后端接口文档和返回结构进行开发，接口变更需同步更新

## 七、开发规范
- 代码和目录结构遵循 `documents/rules/global.md`、`documents/rules/vue.md` 规范
- 组件分层、命名一致、接口统一、异常处理完善
- 提倡复用、简洁、可维护的开发风格

---

如需详细功能说明，请参考《功能需求.md》及后端接口文档。 