import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsInt, Min, Max, IsDateString } from 'class-validator';
import { Type } from 'class-transformer';
import { CallStatus } from '../enums/call-status.enum';

export class CallRecordQueryDto {
  @ApiPropertyOptional({ description: '页码', default: 1 })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', default: 10 })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  @IsOptional()
  limit?: number = 10;

  @ApiPropertyOptional({ description: '用户ID' })
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  userId?: number;

  @ApiPropertyOptional({ description: '服务ID' })
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  serviceId?: number;

  @ApiPropertyOptional({ description: '调用状态', enum: CallStatus })
  @IsEnum(CallStatus)
  @IsOptional()
  status?: CallStatus;

  @ApiPropertyOptional({ description: '请求ID' })
  @IsString()
  @IsOptional()
  requestId?: string;

  @ApiPropertyOptional({ description: 'API密钥ID' })
  @IsString()
  @IsOptional()
  apiKeyId?: string;

  @ApiPropertyOptional({ description: '开始日期' })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiPropertyOptional({ description: '结束日期' })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @ApiPropertyOptional({ description: '最小耗时(ms)' })
  @Type(() => Number)
  @IsInt()
  @Min(0)
  @IsOptional()
  minDuration?: number;

  @ApiPropertyOptional({ description: '最大耗时(ms)' })
  @Type(() => Number)
  @IsInt()
  @Min(0)
  @IsOptional()
  maxDuration?: number;

  @ApiPropertyOptional({ description: 'IP地址' })
  @IsString()
  @IsOptional()
  ipAddress?: string;

  @ApiPropertyOptional({
    description: '排序字段',
    default: 'createdAt'
  })
  @IsString()
  @IsOptional()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({
    description: '排序方向',
    default: 'DESC'
  })
  @IsString()
  @IsOptional()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
} 