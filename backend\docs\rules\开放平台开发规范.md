# NestJS 14 + MySQL 5 开放平台开发规范

## 目录

- [API设计规范](#api设计规范)
- [认证与授权机制](#认证与授权机制)
- [API限流与保护](#api限流与保护)
- [版本控制策略](#版本控制策略)
- [错误处理与状态码](#错误处理与状态码)
- [API文档规范](#api文档规范)
- [SDK开发规范](#sdk开发规范)
- [监控与日志](#监控与日志)
- [开发者体验优化](#开发者体验优化)

## API设计规范

### RESTful API设计

1. **资源命名**：使用名词复数形式，例如`/users`而非`/user`
   ```
   // 正确
   GET /api/v1/services
   POST /api/v1/users
   
   // 避免
   GET /api/v1/getServices
   POST /api/v1/addUser
   ```

2. **HTTP动词使用**：
   - GET: 获取资源
   - POST: 创建资源
   - PUT: 完全更新资源
   - PATCH: 部分更新资源
   - DELETE: 删除资源

3. **嵌套资源表示**：最多使用两级嵌套
   ```
   // 正确
   GET /api/v1/users/{userId}/services
   
   // 避免过深嵌套
   GET /api/v1/users/{userId}/services/{serviceId}/features/{featureId}
   ```

4. **查询参数规范**：

   | 参数名 | 用途 | 示例 |
   |-------|------|------|
   | page  | 分页页码 | ?page=1 |
   | limit | 每页数量 | ?limit=20 |
   | sort  | 排序字段 | ?sort=createdAt |
   | order | 排序方向 | ?order=desc |
   | fields | 字段筛选 | ?fields=id,name,email |
   | q     | 全局搜索 | ?q=keyword |

   ```typescript
   // controller.ts
   @Get()
   async findAll(
     @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
     @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
     @Query('sort') sort: string,
     @Query('order', new DefaultValuePipe('asc')) order: 'asc' | 'desc',
   ) {
     return this.service.findAll({
       page,
       limit,
       sort,
       order,
     });
   }
   ```

5. **一致的响应格式**：

   ```typescript
   // 成功响应
   {
     "code": 200,
     "message": "操作成功",
     "data": {
       // 数据内容
     },
     "timestamp": "2023-10-01T12:00:00Z"
   }
   
   // 分页响应
   {
     "code": 200,
     "message": "操作成功",
     "data": {
       "items": [
         // 数据内容
       ],
       "meta": {
         "page": 1,
         "limit": 20,
         "totalItems": 100,
         "totalPages": 5
       }
     },
     "timestamp": "2023-10-01T12:00:00Z"
   }
   ```

### API接口规范化装饰器

创建API响应装饰器统一处理响应:

```typescript
// common/decorators/api-result.decorator.ts
export function ApiResult() {
  return applyDecorators(
    ApiResponse({ 
      schema: {
        properties: {
          code: { type: 'number', example: 200 },
          message: { type: 'string', example: '操作成功' },
          data: { type: 'object', example: {} },
          timestamp: { type: 'string', example: '2023-10-01T12:00:00Z' }
        }
      }
    }),
  );
}

// 使用方式
@Get()
@ApiResult()
async findAll() {
  return this.service.findAll();
}
```

## 认证与授权机制

### 多认证策略

1. **API密钥认证**: 为第三方应用提供简单认证

   ```typescript
   // api-key.strategy.ts
   @Injectable()
   export class ApiKeyStrategy extends PassportStrategy(Strategy, 'api-key') {
     constructor(private apiKeyService: ApiKeyService) {
       super({ 
         header: 'X-API-KEY', 
         prefix: '' 
       });
     }
   
     async validate(apiKey: string): Promise<any> {
       const key = await this.apiKeyService.validateApiKey(apiKey);
       if (!key) {
         throw new UnauthorizedException('无效的API密钥');
       }
       return key.user;
     }
   }
   ```

2. **JWT认证**: 为用户提供基于令牌的认证

   ```typescript
   // jwt.strategy.ts
   @Injectable()
   export class JwtStrategy extends PassportStrategy(Strategy) {
     constructor(
       private configService: ConfigService,
     ) {
       super({
         jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
         ignoreExpiration: false,
         secretOrKey: configService.get<string>('jwt.secret'),
       });
     }
   
     async validate(payload: any) {
       return { 
         userId: payload.sub, 
         username: payload.username,
         roles: payload.roles 
       };
     }
   }
   ```

3. **OAuth2认证**: 支持第三方登录和授权

   ```typescript
   // oauth2.strategy.ts
   @Injectable()
   export class OAuth2Strategy extends PassportStrategy(OAuth2Strategy, 'oauth2') {
     constructor(
       private configService: ConfigService,
       private userService: UserService,
     ) {
       super({
         authorizationURL: configService.get<string>('oauth.authorizationURL'),
         tokenURL: configService.get<string>('oauth.tokenURL'),
         clientID: configService.get<string>('oauth.clientId'),
         clientSecret: configService.get<string>('oauth.clientSecret'),
         callbackURL: configService.get<string>('oauth.callbackURL'),
         scope: ['profile', 'email'],
       });
     }
   
     async validate(accessToken: string, refreshToken: string, profile: any) {
       const user = await this.userService.findOrCreateByOAuthProfile(profile);
       return user;
     }
   }
   ```

### 统一认证守卫

创建统一认证守卫，支持多种认证方式：

```typescript
// unified-auth.guard.ts
@Injectable()
export class UnifiedAuthGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private jwtAuthGuard: AuthGuard('jwt'),
    private apiKeyAuthGuard: AuthGuard('api-key'),
    private oauth2AuthGuard: AuthGuard('oauth2'),
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const strategies = this.reflector.get<string[]>(
      'auth-strategies',
      context.getHandler(),
    ) || ['jwt']; // 默认使用JWT

    const guards = strategies.map(strategy => {
      switch (strategy) {
        case 'jwt':
          return this.jwtAuthGuard;
        case 'api-key':
          return this.apiKeyAuthGuard;
        case 'oauth2':
          return this.oauth2AuthGuard;
        default:
          return this.jwtAuthGuard;
      }
    });

    // 尝试每种认证方式
    for (const guard of guards) {
      try {
        const canActivate = await guard.canActivate(context);
        if (canActivate) {
          return true;
        }
      } catch (error) {
        // 认证失败，继续尝试下一种方式
      }
    }

    throw new UnauthorizedException('未授权访问');
  }
}
```

### 权限控制

基于RBAC(基于角色的访问控制)实现权限管理：

```typescript
// 权限装饰器
// auth-permission.decorator.ts
export function RequirePermissions(permissions: string[]) {
  return applyDecorators(
    SetMetadata('permissions', permissions),
    UseGuards(PermissionsGuard),
  );
}

// 权限守卫
// permissions.guard.ts
@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredPermissions = this.reflector.get<string[]>(
      'permissions',
      context.getHandler(),
    );
    
    if (!requiredPermissions) {
      return true;
    }
    
    const { user } = context.switchToHttp().getRequest();
    return requiredPermissions.some(permission => 
      user.permissions?.includes(permission)
    );
  }
}

// 使用方式
@Get('admin-data')
@RequirePermissions(['read:admin-data'])
getAdminData() {
  // ...
}
```

## API限流与保护

### 限流策略

1. **全局限流**: 应用于所有API的基本限制

   ```typescript
   // main.ts
   import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler';
   
   async function bootstrap() {
     const app = await NestFactory.create(AppModule);
     
     app.useGlobalGuards(app.get(ThrottlerGuard));
     
     await app.listen(3000);
   }
   ```

2. **基于用户的限流**: 根据用户身份设置不同限制

   ```typescript
   // throttler.guard.ts
   @Injectable()
   export class CustomThrottlerGuard extends ThrottlerGuard {
     protected getTracker(req: Record<string, any>): string {
       // 使用用户ID或API密钥作为跟踪器
       return req.user?.id || req.headers['x-api-key'] || req.ip;
     }
     
     protected getLimit(req: Record<string, any>): number {
       // 根据用户角色或订阅类型设置限制
       if (req.user?.subscription === 'premium') {
         return 100; // 每时间窗口100个请求
       }
       return 20; // 默认限制
     }
   }
   ```

3. **基于路由的限流**: 对特定端点设置更严格的限制

   ```typescript
   // 使用装饰器指定特定路由的限流
   @Throttle(5, 60) // 60秒内最多5个请求
   @Get('sensitive-data')
   getSensitiveData() {
     // ...
   }
   ```

### API密钥管理

1. **密钥生成与验证**:

   ```typescript
   // api-key.service.ts
   @Injectable()
   export class ApiKeyService {
     constructor(
       @InjectRepository(ApiKey)
       private apiKeyRepository: Repository<ApiKey>,
       private cryptoService: CryptoService,
     ) {}
     
     async generateApiKey(userId: number): Promise<string> {
       // 生成随机密钥
       const apiKey = crypto.randomBytes(24).toString('base64');
       const hashedKey = await this.cryptoService.hash(apiKey);
       
       // 保存到数据库
       await this.apiKeyRepository.save({
         userId,
         hashedKey,
         expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1年后过期
       });
       
       return apiKey;
     }
     
     async validateApiKey(apiKey: string): Promise<ApiKey | null> {
       const apiKeys = await this.apiKeyRepository.find({
         where: {
           expiresAt: MoreThan(new Date()),
         },
         relations: ['user'],
       });
       
       for (const key of apiKeys) {
         if (await this.cryptoService.compare(apiKey, key.hashedKey)) {
           return key;
         }
       }
       
       return null;
     }
   }
   ```

2. **密钥权限范围(Scopes)**:

   ```typescript
   // api-key.entity.ts
   @Entity()
   export class ApiKey extends BaseEntity {
     // ...其他字段
     
     @Column('simple-array')
     scopes: string[];
     
     // 检查是否具有特定权限范围
     hasScope(scope: string): boolean {
       return this.scopes.includes(scope) || this.scopes.includes('*');
     }
   }
   
   // scope.guard.ts
   @Injectable()
   export class ScopeGuard implements CanActivate {
     constructor(private reflector: Reflector) {}
     
     canActivate(context: ExecutionContext): boolean {
       const requiredScopes = this.reflector.get<string[]>(
         'scopes',
         context.getHandler(),
       );
       
       if (!requiredScopes) {
         return true;
       }
       
       const { user, apiKey } = context.switchToHttp().getRequest();
       
       // 如果是API密钥认证，检查scope
       if (apiKey) {
         return requiredScopes.some(scope => apiKey.hasScope(scope));
       }
       
       // 否则检查用户权限
       return true;
     }
   }
   ```

### DDoS保护

1. **连接限制**:

   ```typescript
   // main.ts
   import * as helmet from 'helmet';
   
   async function bootstrap() {
     const app = await NestFactory.create(AppModule);
     
     // 使用Helmet增强安全性
     app.use(helmet());
     
     // 设置请求体大小限制
     app.use(express.json({ limit: '1mb' }));
     app.use(express.urlencoded({ extended: true, limit: '1mb' }));
     
     await app.listen(3000);
   }
   ```

2. **启用CORS保护**:

   ```typescript
   // main.ts
   async function bootstrap() {
     const app = await NestFactory.create(AppModule);
     
     app.enableCors({
       origin: [
         'https://your-frontend-domain.com',
         /\.your-domain\.com$/,
       ],
       methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
       allowedHeaders: ['Content-Type', 'Authorization', 'X-API-KEY'],
       credentials: true,
     });
     
     await app.listen(3000);
   }
   ```

## 版本控制策略

### URI版本控制

在NestJS中实现API版本控制:

```typescript
// main.ts
import { VersioningType } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  app.enableVersioning({
    type: VersioningType.URI,
    prefix: 'v',
  });
  
  await app.listen(3000);
}
```

控制器中使用版本:

```typescript
// 版本1 控制器
@Controller({ path: 'users', version: '1' })
export class UsersControllerV1 {
  @Get()
  findAll() {
    return 'This is the version 1 API';
  }
}

// 版本2 控制器
@Controller({ path: 'users', version: '2' })
export class UsersControllerV2 {
  @Get()
  findAll() {
    return 'This is the version 2 API with enhanced features';
  }
}
```

### API弃用策略

通过Swagger装饰器标记弃用的API:

```typescript
// 标记整个控制器为弃用
@ApiTags('Users')
@ApiDeprecated({ description: '此版本API将于2023年12月31日停止支持，请迁移到v2版本' })
@Controller({ path: 'users', version: '1' })
export class UsersControllerV1 {
  // ...
}

// 标记单个端点为弃用
@Get(':id')
@ApiOperation({ summary: '获取用户详情' })
@ApiDeprecated({ description: '请使用/v2/users/:id端点，提供了更丰富的用户信息' })
findOne(@Param('id') id: string) {
  // ...
}
```

### 版本迁移指南

创建版本迁移文档:

```typescript
// setupSwagger.ts
export function setupSwagger(app: INestApplication) {
  const config = new DocumentBuilder()
    .setTitle('开放平台API')
    .setDescription('开放平台API文档')
    .setVersion('1.0')
    .addBearerAuth()
    .addApiKey({ type: 'apiKey', name: 'X-API-KEY', in: 'header' }, 'api-key')
    .addExternalDoc('版本迁移指南', '/docs/migration-guide.html')
    .build();
    
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api-docs', app, document);
}
```

## 错误处理与状态码

### 统一异常过滤器

创建全局异常过滤器，统一处理错误响应格式:

```typescript
// global-exception.filter.ts
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);
  
  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();
    
    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = '服务器内部错误';
    let code = 'INTERNAL_ERROR';
    
    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse() as any;
      
      message = exceptionResponse.message || exception.message;
      code = exceptionResponse.code || this.mapHttpStatusToCode(status);
    } else {
      this.logger.error(
        `未处理的异常 ${request.method} ${request.url}`,
        exception.stack,
      );
    }
    
    response.status(status).json({
      code,
      message,
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }
  
  private mapHttpStatusToCode(status: number): string {
    const codeMap = {
      400: 'BAD_REQUEST',
      401: 'UNAUTHORIZED',
      403: 'FORBIDDEN',
      404: 'NOT_FOUND',
      409: 'CONFLICT',
      422: 'UNPROCESSABLE_ENTITY',
      500: 'INTERNAL_SERVER_ERROR',
    };
    
    return codeMap[status] || 'UNKNOWN_ERROR';
  }
}
```

### 业务异常定义

创建自定义业务异常:

```typescript
// api.exception.ts
export class ApiException extends HttpException {
  constructor(
    message: string,
    status: HttpStatus = HttpStatus.BAD_REQUEST,
    code: string = 'BAD_REQUEST',
  ) {
    super(
      {
        message,
        code,
      },
      status,
    );
  }
}

// 业务错误代码常量
export const ERROR_CODES = {
  API_KEY_INVALID: 'API_KEY_INVALID',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  VALIDATION_FAILED: 'VALIDATION_FAILED',
};

// 使用示例
if (!apiKey) {
  throw new ApiException(
    '无效的API密钥',
    HttpStatus.UNAUTHORIZED,
    ERROR_CODES.API_KEY_INVALID
  );
}
```

### 错误码规范

为开放平台定义统一的错误码规范:

| HTTP状态码 | 错误码 | 描述 |
|---------|-------|------|
| 400 | BAD_REQUEST | 请求参数错误 |
| 401 | UNAUTHORIZED | 未认证或认证失败 |
| 403 | FORBIDDEN | 权限不足 |
| 404 | NOT_FOUND | 资源不存在 |
| 409 | CONFLICT | 资源冲突 |
| 429 | TOO_MANY_REQUESTS | 请求过于频繁 |
| 500 | INTERNAL_ERROR | 服务器内部错误 |

业务错误码规范:

| 错误码 | 描述 |
|-------|------|
| API_KEY_INVALID | 无效的API密钥 |
| API_KEY_EXPIRED | API密钥已过期 |
| SCOPE_INSUFFICIENT | 权限范围不足 |
| QUOTA_EXCEEDED | 已超过配额限制 |
| RESOURCE_LOCKED | 资源被锁定 |

## API文档规范

### Swagger配置

设置完整的Swagger文档:

```typescript
// setupSwagger.ts
export function setupSwagger(app: INestApplication) {
  const config = new DocumentBuilder()
    .setTitle('开放平台API')
    .setDescription(`
      # 开放平台API文档
      
      ## 认证方式
      - Bearer Token认证：通过Authorization头部传递JWT
      - API密钥认证：通过X-API-KEY头部传递API密钥
      
      ## 错误处理
      所有API错误返回统一格式，包含错误码和描述信息
      
      ## 版本控制
      API使用URI版本控制，目前支持v1和v2版本
    `)
    .setVersion('1.0')
    .addTag('认证', '用户认证与授权相关接口')
    .addTag('用户', '用户管理相关接口')
    .addTag('服务', '服务管理相关接口')
    .addBearerAuth()
    .addApiKey({ type: 'apiKey', name: 'X-API-KEY', in: 'header' }, 'api-key')
    .addServer('https://api.example.com', '生产环境')
    .addServer('https://api.staging.example.com', '预发布环境')
    .build();
    
  const document = SwaggerModule.createDocument(app, config);
  
  // 自定义Swagger UI选项
  const customOptions: SwaggerCustomOptions = {
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
      docExpansion: 'none',
    },
    customCss: '.swagger-ui .topbar { display: none }',
  };
  
  SwaggerModule.setup('api-docs', app, document, customOptions);
}
```

### API示例规范

在DTO和控制器中添加详细的API示例:

```typescript
// create-service.dto.ts
export class CreateServiceDto {
  @ApiProperty({
    description: '服务名称',
    example: '智能OCR识别服务',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: '服务类型',
    example: 'ocr',
    enum: ['ocr', 'nlp', 'vision'],
  })
  @IsEnum(['ocr', 'nlp', 'vision'])
  type: string;

  @ApiProperty({
    description: '服务描述',
    example: '提供多种票据和文档的OCR识别功能',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}

// service.controller.ts
@ApiTags('服务')
@Controller('services')
export class ServiceController {
  @Post()
  @ApiOperation({ summary: '创建新服务', description: '创建一个新的API服务' })
  @ApiCreatedResponse({
    description: '服务创建成功',
    type: ServiceResponseDto,
  })
  @ApiBadRequestResponse({
    description: '请求参数错误',
    schema: {
      properties: {
        code: { example: 'VALIDATION_FAILED' },
        message: { example: '服务名称不能为空' },
        timestamp: { example: '2023-10-01T12:00:00Z' },
      },
    },
  })
  @ApiBody({ type: CreateServiceDto })
  create(@Body() createServiceDto: CreateServiceDto) {
    // ...
  }
}
```

## SDK开发规范

### SDK生成工具

使用NestJS和Swagger自动生成SDK:

```typescript
// sdk-generator.service.ts
@Injectable()
export class SdkGeneratorService {
  constructor(
    private configService: ConfigService,
    private httpService: HttpService,
  ) {}
  
  async generateSdk(language: string): Promise<Buffer> {
    const swaggerUrl = this.configService.get<string>('swagger.url');
    const generatorUrl = 'https://generator.swagger.io/api/gen/clients';
    
    try {
      const swaggerSpec = await this.httpService.get(swaggerUrl).toPromise();
      
      const { data } = await this.httpService.post(
        `${generatorUrl}/${language}`,
        {
          spec: swaggerSpec.data,
          options: {
            packageName: 'api-client',
            apiPackage: 'com.example.api',
            modelPackage: 'com.example.model',
          },
        }
      ).toPromise();
      
      const sdkUrl = data.link;
      const sdk = await this.httpService.get(sdkUrl, { responseType: 'arraybuffer' }).toPromise();
      
      return Buffer.from(sdk.data);
    } catch (error) {
      throw new Error(`SDK生成失败: ${error.message}`);
    }
  }
}
```

### SDK配置最佳实践

创建SDK配置指南:

```typescript
// sdk配置示例 (TypeScript)
const apiClient = new ApiClient({
  // 基本配置
  baseUrl: 'https://api.example.com',
  timeout: 30000, // 30秒超时
  
  // 认证配置
  auth: {
    type: 'apiKey', // 或 'jwt'
    apiKey: 'your-api-key',
    // 或者使用JWT
    // token: 'your-jwt-token',
  },
  
  // 重试配置
  retry: {
    maxRetries: 3,
    retryDelay: 1000, // ms
    retryCondition: (error) => {
      return error.status >= 500 || error.code === 'ECONNRESET';
    },
  },
  
  // 日志配置
  logging: {
    level: 'error', // 'debug' | 'info' | 'warn' | 'error'
    handler: (level, message, data) => {
      console[level](message, data);
    },
  },
});

// 使用SDK
try {
  const result = await apiClient.services.createService({
    name: '测试服务',
    type: 'ocr',
  });
  console.log('服务创建成功:', result);
} catch (error) {
  console.error('API调用失败:', error.message);
  console.error('错误码:', error.code);
  console.error('HTTP状态:', error.status);
}
```

## 监控与日志

### API调用监控

实现API调用统计和监控:

```typescript
// api-usage.interceptor.ts
@Injectable()
export class ApiUsageInterceptor implements NestInterceptor {
  constructor(
    private usageService: ApiUsageService,
  ) {}
  
  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    const { method, path, user, apiKey } = request;
    const userId = user?.id || apiKey?.userId || 'anonymous';
    const startTime = Date.now();
    
    return next.handle().pipe(
      tap(async () => {
        const duration = Date.now() - startTime;
        await this.usageService.trackApiCall({
          userId,
          method,
          path,
          duration,
          status: 'success',
          timestamp: new Date(),
        });
      }),
      catchError(error => {
        const duration = Date.now() - startTime;
        this.usageService.trackApiCall({
          userId,
          method,
          path,
          duration,
          status: 'error',
          errorCode: error.response?.code || 'UNKNOWN_ERROR',
          timestamp: new Date(),
        });
        throw error;
      }),
    );
  }
}
```

### 结构化日志

使用结构化日志格式化输出:

```typescript
// structured-logger.ts
@Injectable()
export class StructuredLogger implements LoggerService {
  private logger: Logger;
  
  constructor(private configService: ConfigService) {
    this.logger = new Logger();
  }
  
  log(message: string, context?: string, ...meta: any[]): void {
    this.logger.log(this.formatMessage('info', message, context, meta));
  }
  
  error(message: string, trace?: string, context?: string, ...meta: any[]): void {
    this.logger.error(
      this.formatMessage('error', message, context, meta),
      trace,
    );
  }
  
  warn(message: string, context?: string, ...meta: any[]): void {
    this.logger.warn(this.formatMessage('warn', message, context, meta));
  }
  
  debug(message: string, context?: string, ...meta: any[]): void {
    this.logger.debug(this.formatMessage('debug', message, context, meta));
  }
  
  private formatMessage(
    level: string,
    message: string,
    context?: string,
    meta?: any[],
  ): string {
    const logData = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context: context || 'Application',
      environment: this.configService.get<string>('NODE_ENV') || 'development',
      ...(meta && meta.length > 0 ? { meta: meta } : {}),
    };
    
    return JSON.stringify(logData);
  }
}
```

### 健康检查端点

实现API健康检查:

```typescript
// health.controller.ts
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    private redis: RedisHealthIndicator,
    private http: HttpHealthIndicator,
    private disk: DiskHealthIndicator,
    private memory: MemoryHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      // 数据库检查
      () => this.db.pingCheck('database'),
      
      // Redis检查
      () => this.redis.checkHealth('redis', { timeout: 300 }),
      
      // 外部依赖服务检查
      () => this.http.pingCheck('payment-service', 'https://payment.example.com/health'),
      
      // 磁盘空间检查
      () => this.disk.checkStorage('storage', { path: '/', thresholdPercent: 80 }),
      
      // 内存使用检查
      () => this.memory.checkHeap('memory_heap', 300 * 1024 * 1024), // 300MB
    ]);
  }
}
```

## 开发者体验优化

### API资源中心

设计开发者文档页面结构:

```
/docs
├── index.html                    # 文档首页
├── getting-started/              # 入门指南
│   ├── overview.md               # 平台概述
│   ├── authentication.md         # 认证指南
│   └── quickstart.md             # 快速开始
├── api-reference/                # API参考
│   ├── v1/                       # V1版API
│   └── v2/                       # V2版API
├── guides/                       # 开发指南
│   ├── best-practices.md         # 最佳实践
│   ├── error-handling.md         # 错误处理
│   └── rate-limits.md            # 速率限制
├── sdks/                         # SDK文档
│   ├── javascript.md             # JavaScript SDK
│   ├── php.md                    # PHP SDK
│   └── python.md                 # Python SDK
└── changelog.md                  # API变更日志
```

### 沙箱环境

实现开发者沙箱环境:

```typescript
// sandbox.middleware.ts
@Injectable()
export class SandboxMiddleware implements NestMiddleware {
  constructor(
    private configService: ConfigService,
    private sandboxService: SandboxService,
  ) {}

  use(req: Request, res: Response, next: Function) {
    // 检查是否是沙箱请求
    const isSandbox = req.headers['x-sandbox'] === 'true';
    
    if (isSandbox) {
      // 修改请求上下文，使用沙箱数据库连接
      req.sandbox = true;
      req.sandboxId = req.user?.id || 'default';
      
      // 设置沙箱环境
      this.sandboxService.setupSandboxContext(req.sandboxId);
    }
    
    next();
  }
}

// 在模块中应用中间件
@Module({
  providers: [SandboxService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(SandboxMiddleware)
      .forRoutes('*');
  }
}
```

### 用量统计仪表板

实现API用量统计:

```typescript
// usage-stats.controller.ts
@Controller('developer/stats')
@UseGuards(JwtAuthGuard)
export class UsageStatsController {
  constructor(private usageStatsService: UsageStatsService) {}

  @Get()
  async getUserStats(@Request() req) {
    const userId = req.user.id;
    
    const [dailyUsage, endpointBreakdown, errorStats] = await Promise.all([
      this.usageStatsService.getDailyUsage(userId),
      this.usageStatsService.getEndpointBreakdown(userId),
      this.usageStatsService.getErrorStats(userId),
    ]);
    
    return {
      dailyUsage,      // 每日调用次数
      endpointBreakdown, // 各端点调用占比
      errorStats,      // 错误统计
      quotaRemaining: await this.usageStatsService.getQuotaRemaining(userId),
    };
  }
}
```

通过以上规范，可以构建一个高质量、安全可靠、易于使用的开放平台。开发人员应该始终关注API一致性、安全性和开发体验，为第三方应用提供良好的集成体验。 