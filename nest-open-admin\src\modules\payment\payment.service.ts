import {
  Injectable,
  Logger,
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
  Optional,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import {
  OrderEntity,
  OrderType,
  PaymentStatus,
} from '../order/entities/order.entity';
import { UserEntity, UserStatus } from '../user/entities/user.entity';
import { ServiceEntity } from '../service/entities/service.entity';
import { ServiceStatus } from '../service/dto/service.dto';
import { ConfigService } from '@nestjs/config';

import { RedisService } from '@/shared/redis.service';
import { UserService } from '../user/user.service';
import { ServiceService } from '../service/service.service';
import { UserServiceService } from '../user-service/user-service.service';
import * as crypto from 'crypto';
import dayjs from 'dayjs';

export interface PaymentCallbackData {
  orderId: string;
  paymentId: string;
  amount: number;
  status: 'success' | 'failed' | 'cancelled';
  paymentMethod: 'alipay' | 'wechat' | 'bank';
  transactionId?: string;
  paidAt?: Date;
  metadata?: Record<string, any>;
}

export interface TopUpRequest {
  userId: number;
  amount: number;
  paymentMethod: 'alipay' | 'wechat' | 'bank';
  returnUrl?: string;
  notifyUrl?: string;
}

export interface ServicePurchaseRequest {
  userId: number;
  serviceId: number;
  quantity: number;
  paymentMethod: 'alipay' | 'wechat' | 'bank';
  returnUrl?: string;
  notifyUrl?: string;
}

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);
  private readonly PAYMENT_TIMEOUT = 30 * 60 * 1000; // 30分钟支付超时

  constructor(
    @InjectRepository(OrderEntity)
    private readonly orderRepository: Repository<OrderEntity>,
    private readonly dataSource: DataSource,
    private readonly configService: ConfigService,

    private readonly redisService: RedisService,
    private readonly userService: UserService,
    private readonly serviceService: ServiceService,
    private readonly userServiceService: UserServiceService,
  ) {}

  /**
   * 创建充值订单
   */
  async createTopUpOrder(request: TopUpRequest): Promise<{
    orderId: string;
    paymentUrl: string;
    qrCode?: string;
  }> {
    this.logger.log(
      `创建充值订单: 用户${request.userId}, 金额${request.amount}`,
    );

    try {
      // 验证用户
      const userDto = await this.userService.findOne(request.userId);
      if (!userDto || userDto.userStatus !== UserStatus.ACTIVE) {
        throw new BadRequestException('用户不存在或已禁用');
      }

      // 构造用户实体用于订单关联
      const user = new UserEntity();
      user.id = userDto.id;
      user.username = userDto.username;

      // 验证金额
      if (request.amount <= 0 || request.amount > 10000) {
        throw new BadRequestException('充值金额必须在0.01-10000元之间');
      }

      // 创建订单
      const order = new OrderEntity();
      order.user = user;
      order.type = OrderType.RECHARGE;
      order.amount = request.amount;
      order.status = PaymentStatus.PENDING;
      order.orderNumber = this.generateOrderNumber('TOP');

      const savedOrder = await this.orderRepository.save(order);

      // 调用第三方支付接口
      const paymentResult = await this.createPayment({
        orderId: savedOrder.orderNumber,
        amount: request.amount,
        subject: '账户充值',
        body: `用户${user.username}充值${request.amount}元`,
        paymentMethod: request.paymentMethod,
        returnUrl: request.returnUrl,
        notifyUrl: request.notifyUrl || this.getDefaultNotifyUrl(),
      });

      // 缓存支付信息
      await this.redisService.setex(
        `payment:${savedOrder.orderNumber}`,
        1800, // 30分钟
        JSON.stringify({
          orderId: savedOrder.id,
          orderNumber: savedOrder.orderNumber,
          userId: request.userId,
          amount: request.amount,
          type: 'topup',
          paymentId: paymentResult.paymentId,
        }),
      );

      this.logger.log(`充值订单创建成功: ${savedOrder.orderNumber}`);

      return {
        orderId: savedOrder.orderNumber,
        paymentUrl: paymentResult.paymentUrl,
        qrCode: paymentResult.qrCode,
      };
    } catch (error) {
      this.logger.error('创建充值订单失败', error);
      throw new InternalServerErrorException('创建充值订单失败');
    }
  }

  /**
   * 创建服务购买订单
   */
  async createServicePurchaseOrder(request: ServicePurchaseRequest): Promise<{
    orderId: string;
    paymentUrl: string;
    qrCode?: string;
  }> {
    this.logger.log(
      `创建服务购买订单: 用户${request.userId}, 服务${request.serviceId}, 数量${request.quantity}`,
    );

    try {
      // 验证用户
      const userDto = await this.userService.findOne(request.userId);
      if (!userDto || userDto.userStatus !== UserStatus.ACTIVE) {
        throw new BadRequestException('用户不存在或已禁用');
      }

      // 构造用户实体用于订单关联
      const user = new UserEntity();
      user.id = userDto.id;
      user.username = userDto.username;

      // 验证服务
      const serviceDto = await this.serviceService.findOne(request.serviceId);
      if (!serviceDto || serviceDto.serviceStatus !== ServiceStatus.ACTIVE) {
        throw new BadRequestException('服务不存在或已下架');
      }

      // 构造服务实体用于订单关联
      const service = new ServiceEntity();
      service.id = serviceDto.id!;
      service.name = serviceDto.name!;
      service.price = serviceDto.price!;

      // 计算总金额
      const totalAmount = service.price * request.quantity;

      if (totalAmount <= 0) {
        throw new BadRequestException('购买金额必须大于0');
      }

      // 创建订单
      const order = new OrderEntity();
      order.user = user;
      order.service = service;
      order.type = OrderType.SERVICE_PURCHASE;
      order.amount = totalAmount;
      order.purchaseCount = request.quantity;
      order.status = PaymentStatus.PENDING;
      order.orderNumber = this.generateOrderNumber('SRV');

      const savedOrder = await this.orderRepository.save(order);

      // 调用第三方支付接口
      const paymentResult = await this.createPayment({
        orderId: savedOrder.orderNumber,
        amount: totalAmount,
        subject: `购买${service.name}`,
        body: `用户${user.username}购买${service.name} ${request.quantity}次调用`,
        paymentMethod: request.paymentMethod,
        returnUrl: request.returnUrl,
        notifyUrl: request.notifyUrl || this.getDefaultNotifyUrl(),
      });

      // 缓存支付信息
      await this.redisService.setex(
        `payment:${savedOrder.orderNumber}`,
        1800, // 30分钟
        JSON.stringify({
          orderId: savedOrder.id,
          orderNumber: savedOrder.orderNumber,
          userId: request.userId,
          serviceId: request.serviceId,
          amount: totalAmount,
          quantity: request.quantity,
          type: 'service_purchase',
          paymentId: paymentResult.paymentId,
        }),
      );

      this.logger.log(`服务购买订单创建成功: ${savedOrder.orderNumber}`);

      return {
        orderId: savedOrder.orderNumber,
        paymentUrl: paymentResult.paymentUrl,
        qrCode: paymentResult.qrCode,
      };
    } catch (error) {
      this.logger.error('创建服务购买订单失败', error);
      throw new InternalServerErrorException('创建服务购买订单失败');
    }
  }

  /**
   * 处理支付回调
   */
  async handlePaymentCallback(callbackData: PaymentCallbackData): Promise<{
    success: boolean;
    message: string;
  }> {
    this.logger.log(
      `处理支付回调: 订单${callbackData.orderId}, 状态${callbackData.status}`,
    );

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 获取缓存的支付信息
      const paymentInfoStr = await this.redisService.get(
        `payment:${callbackData.orderId}`,
      );
      if (!paymentInfoStr) {
        throw new BadRequestException('支付信息不存在或已过期');
      }

      const paymentInfo = JSON.parse(paymentInfoStr);

      // 获取订单
      const order = await queryRunner.manager.findOne(OrderEntity, {
        where: { orderNumber: callbackData.orderId },
      });

      if (!order) {
        throw new BadRequestException('订单不存在');
      }

      // 检查订单状态
      if (order.status !== PaymentStatus.PENDING) {
        this.logger.warn(
          `订单${callbackData.orderId}状态异常: ${order.status}`,
        );
        return { success: true, message: '订单已处理' };
      }

      // 验证金额
      if (Math.abs(order.amount - callbackData.amount) > 0.01) {
        throw new BadRequestException('支付金额不匹配');
      }

      // 更新订单状态
      if (callbackData.status === 'success') {
        order.status = PaymentStatus.COMPLETED;
        order.paidAt = callbackData.paidAt || new Date();
        order.transactionId = callbackData.transactionId;

        // 处理业务逻辑
        if (order.type === OrderType.RECHARGE) {
          await this.processTopUpSuccess(queryRunner, order);
        } else if (order.type === OrderType.SERVICE_PURCHASE) {
          await this.processServicePurchaseSuccess(queryRunner, order);
        }
      } else if (callbackData.status === 'failed') {
        order.status = PaymentStatus.FAILED;
      } else if (callbackData.status === 'cancelled') {
        order.status = PaymentStatus.CANCELLED;
      }

      // 更新订单元数据
      order.metadata = {
        ...order.metadata,
        paymentCallback: callbackData,
        processedAt: Date.now(),
      };

      await queryRunner.manager.save(OrderEntity, order);
      await queryRunner.commitTransaction();

      // 清除缓存
      await this.redisService.del(`payment:${callbackData.orderId}`);

      // 发送事件
      const paymentCallbackEvent: any = {
        id: `payment_callback_${order.id}_${Date.now()}`,
        type: 'user_action' as const,
        source: 'payment-service',
        timestamp: Date.now(),
        data: {
          id: `payment_callback_${order.id}_${Date.now()}`,
          userId: order.user.id,
          action: 'payment_callback_processed',
          resource: 'payment_order',
          timestamp: Date.now(),
          success: callbackData.status === 'success',
          details: {
            orderId: order.id,
            orderNumber: order.orderNumber,
            amount: order.amount,
            status: order.status,
            callbackData,
          },
        },
        payload: {
          id: `payment_callback_${order.id}_${Date.now()}`,
          userId: order.user.id,
          action: 'payment_callback_processed',
          resource: 'payment_order',
          timestamp: Date.now(),
          success: callbackData.status === 'success',
          details: {
            orderId: order.id,
            orderNumber: order.orderNumber,
            amount: order.amount,
            status: order.status,
            callbackData,
          },
        },
        metadata: {
          serviceName: 'payment-service',
          operation: 'payment_callback',
          timestamp: Date.now(),
        },
      };

      // 用户操作事件已记录到日志
      this.logger.log(`用户操作事件: ${JSON.stringify(paymentCallbackEvent)}`);

      this.logger.log(
        `支付回调处理完成: 订单${callbackData.orderId}, 最终状态${order.status}`,
      );

      return {
        success: true,
        message: '支付回调处理成功',
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error('支付回调处理失败', error);
      throw new InternalServerErrorException('支付回调处理失败');
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 处理充值成功
   */
  private async processTopUpSuccess(
    queryRunner: any,
    order: OrderEntity,
  ): Promise<void> {
    // 这里可以实现充值成功后的业务逻辑
    // 比如增加用户余额、发送通知等

    this.logger.log(`处理充值成功: 用户${order.user.id}, 金额${order.amount}`);

    // 发送充值成功事件
    const topupSuccessEvent: any = {
      id: `topup_success_${order.id}_${Date.now()}`,
      type: 'user_action' as const,
      source: 'payment-service',
      timestamp: Date.now(),
      data: {
        id: `topup_success_${order.id}_${Date.now()}`,
        userId: order.user.id,
        action: 'topup_success',
        resource: 'user_balance',
        timestamp: Date.now(),
        success: true,
        details: {
          amount: order.amount,
          orderId: order.id,
          orderNumber: order.orderNumber,
        },
      },
      payload: {
        id: `topup_success_${order.id}_${Date.now()}`,
        userId: order.user.id,
        action: 'topup_success',
        resource: 'user_balance',
        timestamp: Date.now(),
        success: true,
        details: {
          amount: order.amount,
          orderId: order.id,
          orderNumber: order.orderNumber,
        },
      },
      metadata: {
        serviceName: 'payment-service',
        operation: 'topup_success',
        timestamp: Date.now(),
      },
    };

    // 用户操作事件已记录到日志
    this.logger.log(`用户操作事件: ${JSON.stringify(topupSuccessEvent)}`);
  }

  /**
   * 处理服务购买成功
   */
  private async processServicePurchaseSuccess(
    queryRunner: any,
    order: OrderEntity,
  ): Promise<void> {
    this.logger.log(
      `处理服务购买成功: 用户${order.user.id}, 服务${order.service?.id}, 数量${order.purchaseCount}`,
    );

    // 通过服务层增加用户服务次数
    const userService = await this.userServiceService.addServiceCount(
      order.user.id,
      order.service?.id!,
      order.purchaseCount || 0,
    );

    // 发送服务购买成功事件
    const servicePurchaseEvent: any = {
      id: `service_purchase_${order.id}_${Date.now()}`,
      type: 'user_action' as const,
      source: 'payment-service',
      timestamp: Date.now(),
      data: {
        id: `service_purchase_${order.id}_${Date.now()}`,
        userId: order.user.id,
        action: 'service_purchase_success',
        resource: 'user_service',
        timestamp: Date.now(),
        success: true,
        details: {
          serviceId: order.service?.id!,
          quantity: order.purchaseCount,
          orderId: order.id,
          orderNumber: order.orderNumber,
          userService,
        },
      },
      payload: {
        id: `service_purchase_${order.id}_${Date.now()}`,
        userId: order.user.id,
        action: 'service_purchase_success',
        resource: 'user_service',
        timestamp: Date.now(),
        success: true,
        details: {
          serviceId: order.service?.id!,
          quantity: order.purchaseCount,
          orderId: order.id,
          orderNumber: order.orderNumber,
          userService,
        },
      },
      metadata: {
        serviceName: 'payment-service',
        operation: 'service_purchase',
        timestamp: Date.now(),
      },
    };

    // 用户操作事件已记录到日志
    this.logger.log(`用户操作事件: ${JSON.stringify(servicePurchaseEvent)}`);
  }

  /**
   * 调用第三方支付接口（模拟实现）
   */
  private async createPayment(params: {
    orderId: string;
    amount: number;
    subject: string;
    body: string;
    paymentMethod: string;
    returnUrl?: string;
    notifyUrl: string;
  }): Promise<{
    paymentId: string;
    paymentUrl: string;
    qrCode?: string;
  }> {
    // 这里是模拟实现，实际应该调用支付宝、微信等支付接口
    const paymentId = `pay_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 模拟不同支付方式的返回
    if (params.paymentMethod === 'alipay') {
      return {
        paymentId,
        paymentUrl: `https://openapi.alipay.com/gateway.do?${this.buildAlipayParams(params)}`,
        qrCode: `alipay://pay?orderId=${params.orderId}&amount=${params.amount}`,
      };
    } else if (params.paymentMethod === 'wechat') {
      return {
        paymentId,
        paymentUrl: `https://api.mch.weixin.qq.com/pay/unifiedorder`,
        qrCode: `weixin://wxpay/bizpayurl?pr=${paymentId}`,
      };
    } else {
      return {
        paymentId,
        paymentUrl: `https://payment.example.com/pay?id=${paymentId}`,
      };
    }
  }

  /**
   * 构建支付宝支付参数（模拟）
   */
  private buildAlipayParams(params: any): string {
    // 这里是模拟实现，实际应该按照支付宝文档构建参数
    return `app_id=your_app_id&method=alipay.trade.page.pay&charset=UTF-8&sign_type=RSA2&timestamp=${new Date().toISOString()}&version=1.0&out_trade_no=${params.orderId}&total_amount=${params.amount}&subject=${encodeURIComponent(params.subject)}&body=${encodeURIComponent(params.body)}&return_url=${encodeURIComponent(params.returnUrl || '')}&notify_url=${encodeURIComponent(params.notifyUrl)}`;
  }

  /**
   * 生成订单号
   */
  private generateOrderNumber(prefix: string): string {
    const timestamp = dayjs().format('YYYYMMDDHHmmss');
    const random = Math.random().toString(36).substring(2, 6).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  }

  /**
   * 获取默认回调地址
   */
  private getDefaultNotifyUrl(): string {
    const baseUrl =
      this.configService.get('app.baseUrl') || 'http://localhost:3000';
    return `${baseUrl}/api/payment/callback`;
  }

  /**
   * 查询支付状态
   */
  async queryPaymentStatus(orderNumber: string): Promise<{
    orderNumber: string;
    status: PaymentStatus;
    amount: number;
    paidAt?: Date;
  }> {
    const order = await this.orderRepository.findOne({
      where: { orderNumber },
    });

    if (!order) {
      throw new BadRequestException('订单不存在');
    }

    return {
      orderNumber: order.orderNumber,
      status: order.status,
      amount: order.amount,
      paidAt: order.paidAt,
    };
  }
}
