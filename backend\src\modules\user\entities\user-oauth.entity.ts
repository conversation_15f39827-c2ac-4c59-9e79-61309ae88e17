import { Enti<PERSON>, Column, <PERSON>T<PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from '@/common/entities/base.entity';
import { UserEntity, ThirdPartyType } from './user.entity';

@Entity('user_oauth')
export class UserOauthEntity extends BaseEntity {
  @Column({ name: 'user_id', type: 'int', comment: '用户ID' })
  userId: number;

  @Column({
    type: 'enum',
    enum: ThirdPartyType,
    comment: '第三方平台类型'
  })
  providerType: ThirdPartyType;

  @Column({ type: 'varchar', length: 100, comment: '第三方平台唯一标识' })
  providerId: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '第三方平台用户名' })
  providerUsername?: string;

  @Column({ type: 'varchar', length: 500, nullable: true, comment: '第三方平台头像URL' })
  providerAvatar?: string;

  @Column({ type: 'varchar', length: 500, nullable: true, comment: '第三方平台用户信息JSON' })
  profileData?: string;

  @Column({ type: 'varchar', length: 500, nullable: true, comment: '访问令牌' })
  accessToken?: string;

  @Column({ type: 'varchar', length: 500, nullable: true, comment: '刷新令牌' })
  refreshToken?: string;

  @Column({ type: 'timestamp', nullable: true, comment: '令牌过期时间' })
  tokenExpiresAt?: Date;

  @Column({ type: 'timestamp', comment: '最后登录时间' })
  lastLoginAt: Date;

  // 关联关系
  @ManyToOne(() => UserEntity, user => user.id)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;
} 