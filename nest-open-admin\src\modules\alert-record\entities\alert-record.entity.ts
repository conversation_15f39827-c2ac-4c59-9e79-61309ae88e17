import {
  Entity,
  Column,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';
// 移除直接导入其他模块的Entity，使用字符串关系定义避免循环依赖
// import { ServiceEntity } from '../../service/entities/service.entity';
// import { UserEntity } from '../../user/entities/user.entity';

/**
 * 告警类型枚举
 */
export enum AlertType {
  QUOTA_EXCEEDED = 'quota_exceeded',
  LOW_BALANCE = 'low_balance',
  RATE_LIMIT = 'rate_limit',
  ERROR_RATE = 'error_rate',
  SERVICE_ERROR = 'service_error',
  HIGH_FREQUENCY = 'high_frequency',
  RESPONSE_TIME = 'response_time',
  SYSTEM_ERROR = 'system_error',
  SECURITY = 'security',
  PAYMENT = 'payment',
  OTHER = 'other',
}

/**
 * 告警级别枚举
 */
export enum AlertLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * 告警状态枚举
 */
export enum AlertStatus {
  PENDING = 'pending',
  ACKNOWLEDGED = 'acknowledged',
  RESOLVED = 'resolved',
  IGNORED = 'ignored',
}

@Entity('alert_records', {
  comment: '预警记录表',
})
@Index(['userId', 'type'])
@Index(['serviceId', 'status'])
@Index(['level', 'status'])
@Index(['createdAt'])
export class AlertRecordEntity extends BaseEntity {
  @Column({
    type: 'int',
    comment: '用户ID',
  })
  userId: number;

  @Column({
    type: 'int',
    nullable: true,
    comment: '服务ID',
  })
  serviceId?: number;

  @Column({
    type: 'enum',
    enum: AlertType,
    comment: '告警类型',
  })
  type: AlertType;

  @Column({
    type: 'enum',
    enum: AlertLevel,
    comment: '告警级别',
  })
  level: AlertLevel;

  @Column({
    type: 'varchar',
    length: 200,
    comment: '告警标题',
  })
  title: string;

  @Column({
    type: 'text',
    comment: '告警内容',
  })
  content: string;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '触发条件',
  })
  triggerCondition?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '当前值',
  })
  currentValue?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '阈值',
  })
  threshold?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: '相关数据',
  })
  metadata?: Record<string, any>;

  @Column({
    type: 'enum',
    enum: AlertStatus,
    default: AlertStatus.PENDING,
    comment: '告警状态',
  })
  status: AlertStatus;

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已通知',
  })
  notified: boolean;

  @Column({
    type: 'text',
    nullable: true,
    comment: '通知方式',
  })
  notificationMethods?: string[];

  @Column({
    type: 'text',
    nullable: true,
    comment: '处理备注',
  })
  resolveNote?: string;

  @Column({
    type: 'int',
    nullable: true,
    comment: '处理人ID',
  })
  resolvedBy?: number;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '处理时间',
  })
  resolvedAt?: Date;

  // 关联关系 - 使用字符串关系定义，避免循环引用
  @ManyToOne('UserEntity', 'alertRecords', { eager: false })
  @JoinColumn({ name: 'userId' })
  user?: any;

  @ManyToOne('ServiceEntity', 'alertRecords', { eager: false, nullable: true })
  @JoinColumn({ name: 'serviceId' })
  service?: any;

  @ManyToOne('UserEntity', { eager: false, nullable: true })
  @JoinColumn({ name: 'resolvedBy' })
  resolver?: any;
}
