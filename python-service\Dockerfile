# 基础镜像：选择体积小、与依赖兼容的 Python 3.10
FROM python:3.10-slim

ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    TZ=Asia/Shanghai \
    FLASK_RUN_PORT=8866 \
    TIMEOUT=300 \
    OCR_TIMEOUT=180 \
    PIP_TIMEOUT=300 \
    DISABLE_PADDLE_DOWNLOAD=true

WORKDIR /app

# 系统依赖：基础构建、图像与条码
RUN apt-get update && apt-get install -y --no-install-recommends \
    tzdata curl gcc g++ \
    libgl1 libglib2.0-0 libsm6 libxrender1 libxext6 \
    libzbar0 \
    && rm -rf /var/lib/apt/lists/*

# 时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 配置阿里云 PyPI 源
RUN python -m pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    python -m pip config set global.trusted-host mirrors.aliyun.com && \
    python -m pip install --upgrade pip setuptools wheel

# 复制依赖并安装（利用缓存）
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY app.py ./

# 创建日志目录
RUN mkdir -p /app/logs && chmod 777 /app/logs

EXPOSE 8866

# 健康检查脚本
RUN echo '#!/bin/sh\nset -e\ncurl -sf http://localhost:8866/health > /dev/null' > /app/healthcheck.sh && chmod +x /app/healthcheck.sh
HEALTHCHECK --interval=60s --timeout=10s --start-period=30s --retries=3 CMD /app/healthcheck.sh || exit 1

# 使用 gunicorn 启动（生产建议）
CMD ["gunicorn", "-w", "2", "-b", "0.0.0.0:8866", "app:app"] 