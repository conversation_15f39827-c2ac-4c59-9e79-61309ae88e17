# 使用Ubuntu 20.04作为基础镜像
FROM ubuntu:20.04

# 避免交互式提示
ENV DEBIAN_FRONTEND=noninteractive

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV FLASK_RUN_PORT=8866
ENV FLASK_APP=app.py
ENV TIMEOUT=300
ENV OCR_TIMEOUT=180
ENV PIP_TIMEOUT=300
ENV PYTHONIOENCODING=utf-8
ENV PYTHONGC=1
ENV PYTHONGCTHRESHOLD=50000
# 设置时区为亚洲/上海
ENV TZ=Asia/Shanghai

# 安装Python 3.8和所需依赖
RUN apt-get update && apt-get install -y \
    python3.8 \
    python3.8-dev \
    python3-pip \
    gcc \
    g++ \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxrender1 \
    libxext6 \
    curl \
    libssl1.1 \
    tzdata \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 配置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建Python符号链接
RUN ln -sf /usr/bin/python3.8 /usr/bin/python && \
    ln -sf /usr/bin/pip3 /usr/bin/pip

# 配置可靠的国内镜像源
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set global.trusted-host mirrors.aliyun.com

# 第一步：安装基础依赖（小包，下载快）
RUN pip install \
    flask==2.0.1 \
    flask-cors==3.0.10 \
    Werkzeug==2.0.1 \
    gunicorn==20.1.0 \
    requests==2.27.1 \
    python-dotenv==0.19.2 \
    python-multipart==0.0.5 \
    psutil==5.9.0

# 第二步：安装数据处理包（中等大小）
RUN pip install \
    numpy==1.21.1 \
    pandas==1.3.0

# 第三步：安装NLP相关包（小包）
RUN pip install \
    jieba==0.42.1 \
    cpca==0.5.5

# 第四步：安装地理位置相关包（中等大小）
RUN pip install \
    geopy==2.2.0 \
    shapely==1.8.0 \
    geojson==2.5.0


# 第六步：安装OpenCV（大包，单独安装）
RUN pip install \
    opencv-python-headless==********

# 第七步：安装PaddlePaddle（最大包，单独安装）
RUN pip install \
    paddlepaddle==2.5.0

# 第八步：安装PaddleOCR（依赖PaddlePaddle，单独安装）
RUN pip install \
    paddleocr==*******

# 创建模型目录
RUN mkdir -p /root/.paddlex/official_models

# 创建健康检查脚本，确保服务正常启动
RUN echo '#!/bin/bash\n\
echo "正在检查服务状态..."\n\
curl -s http://localhost:8866/health > /dev/null\n\
if [ $? -eq 0 ]; then\n\
    echo "服务健康检查成功"\n\
    exit 0\n\
else\n\
    echo "服务健康检查失败"\n\
    exit 1\n\
fi\n' > /app/healthcheck.sh && \
    chmod +x /app/healthcheck.sh

# 创建日志目录
RUN mkdir -p /app/logs && chmod 777 /app/logs

# 复制应用代码
COPY app.py .
COPY test_geocoding.py .

# 暴露端口
EXPOSE 8866

# 健康检查 - 使用更长的时间间隔和启动期
HEALTHCHECK --interval=120s --timeout=60s --start-period=300s --retries=2 \
    CMD /app/healthcheck.sh || exit 1

# 直接启动应用
CMD ["python", "app.py"]