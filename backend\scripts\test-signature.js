const crypto = require('crypto');

/**
 * 测试签名生成脚本
 * 验证前后端签名算法的一致性
 */
function testSignature() {
  console.log('===== 测试签名生成 =====');
  
  // 从日志中提取的实际参数
  const testData = {
    method: 'POST',
    path: '/v1/op/address/extract',
    queryParams: { mode: 'async' },
    bodyData: {
      text: '张三，13800138000，广东省深圳市南山区科技园',
      mode: 'async'
    },
    timestamp: '1754749990',
    nonce: 'eeac22c74c32cbebc352a0daf72fccd0',
    secretKey: 'sk-93f682d8f646ca950', // 从Redis获取的解密后的Secret Key
    expectedSignature: '/iovhoyT4F+UMDc/TyVY3BOxXzBAeL9ovMZxBgzLAI8='
  };
  
  console.log('测试参数:');
  console.log('- Method:', testData.method);
  console.log('- Path:', testData.path);
  console.log('- Query Params:', JSON.stringify(testData.queryParams));
  console.log('- Body Data:', JSON.stringify(testData.bodyData));
  console.log('- Timestamp:', testData.timestamp);
  console.log('- Secret Key:', testData.secretKey);
  console.log('- Expected Signature:', testData.expectedSignature);
  
  // 构建规范化请求字符串（模拟后端逻辑）
  const method = testData.method.toUpperCase();
  const path = testData.path;
  
  // 排序查询参数
  const sortedQuery = Object.keys(testData.queryParams)
    .sort()
    .map(key => `${key}=${encodeURIComponent(testData.queryParams[key])}`)
    .join('&');
  
  // JSON序列化请求体
  const bodyString = JSON.stringify(testData.bodyData);
  
  const canonicalRequest = [
    method,
    path,
    sortedQuery,
    bodyString,
    testData.timestamp
  ].join('\n');
  
  console.log('\n规范化请求字符串:');
  console.log(canonicalRequest);
  
  // 生成签名
  const hmac = crypto.createHmac('sha256', testData.secretKey);
  hmac.update(canonicalRequest);
  const calculatedSignature = hmac.digest('base64');
  
  console.log('\n签名结果:');
  console.log('- 计算的签名:', calculatedSignature);
  console.log('- 期望的签名:', testData.expectedSignature);
  console.log('- 签名匹配:', calculatedSignature === testData.expectedSignature ? '✅ 是' : '❌ 否');
  
  // 如果签名不匹配，尝试其他可能的Secret Key
  if (calculatedSignature !== testData.expectedSignature) {
    console.log('\n尝试其他可能的Secret Key...');
    
    // 尝试完整的Secret Key（可能前端使用的是完整版本）
    const fullSecretKey = 'sk-93f682d8f646ca950'; // 这可能不是完整的
    console.log('尝试完整Secret Key:', fullSecretKey);
    
    const hmac2 = crypto.createHmac('sha256', fullSecretKey);
    hmac2.update(canonicalRequest);
    const signature2 = hmac2.digest('base64');
    console.log('使用完整Secret Key的签名:', signature2);
    console.log('匹配:', signature2 === testData.expectedSignature ? '✅ 是' : '❌ 否');
  }
  
  console.log('\n===== 测试完成 =====');
}

// 运行测试
testSignature();
