import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsNotEmpty, IsOptional, IsPhoneNumber, IsString, ValidateIf } from 'class-validator';
import { CodeType } from './captcha/send-email-code.dto';

export class VerifyCodeDto {
  @ValidateIf(o => !o.phone)
  @IsEmail({}, { message: '邮箱格式不正确' })
  @ApiPropertyOptional({ description: '邮箱' })
  email?: string;
  
  @ValidateIf(o => !o.email)
  @IsPhoneNumber('CN', { message: '手机号格式不正确' })
  @ApiPropertyOptional({ description: '手机号' })
  phone?: string;
  
  @IsString()
  @IsNotEmpty({ message: '验证码不能为空' })
  @ApiProperty({ description: '验证码' })
  code: string;
  
  @IsEnum(CodeType, { message: '验证码类型无效' })
  @ApiProperty({ description: '验证码类型', enum: CodeType })
  type: CodeType;
} 