/**
 * 队列接口定义
 * 定义任务、队列状态等相关接口
 */

import { Job } from 'bull';
import { TaskType, TaskPriority, TaskStatus, AlertLevel, AlertType } from '../queue.constants';

// 基础任务接口
export interface BaseTask {
  id?: string;
  type: TaskType;
  priority?: TaskPriority;
  delay?: number;
  attempts?: number;
  createdAt?: Date;
  userId?: number;
  apiKeyId?: string; // API密钥ID
  serviceId?: number; // 服务ID
  requestId?: string; // 请求ID
}

// API调用任务数据
export interface ApiCallTaskData extends BaseTask {
  type: 'api_call';
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retryConfig?: {
    maxRetries: number;
    retryDelay: number;
  };
}

// 告警任务数据
export interface AlertTaskData extends BaseTask {
  type: TaskType;
  userId?: number;
  alertType?: AlertType;
  level?: AlertLevel;
  title?: string;
  message?: string;
  metadata?: Record<string, any>;
  channels?: ('email' | 'sms' | 'webhook')[];
}

// OCR识别任务数据
export interface OcrTaskData extends BaseTask {
  type: 'ocr_recognition_task';
  imageData: string; // Base64编码的图片数据
  filename?: string;
  callbackUrl?: string; // 回调URL，用于通知任务完成
  apiKeyId?: string; // API密钥ID
  serviceId?: number; // 服务ID
  requestId?: string; // 请求ID
}

// 地址提取任务数据
export interface AddressExtractionTaskData extends BaseTask {
  type: 'address_extraction';
  text: string;
  mode?: 'single' | 'multiple';
  callbackUrl?: string;
  apiKeyId?: string; // API密钥ID
  serviceId?: number; // 服务ID
  requestId?: string; // 请求ID
}

// 地理坐标逆解析任务数据
export interface ReverseGeocodingTaskData extends BaseTask {
  type: 'reverse_geocoding';
  coordinates: Array<{
    lat: number;
    lng: number;
    id?: string;
  }>;
  latitude?: number;
  longitude?: number;
  callbackUrl?: string;
  apiKeyId?: string; // API密钥ID
  serviceId?: number; // 服务ID
  requestId?: string; // 请求ID
}

// 任务结果接口
export interface TaskResult {
  success: boolean;
  data?: any;
  error?: string;
  duration?: number;
  retryCount?: number;
}

// 队列状态接口
export interface QueueStatus {
  name: string;
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
  paused: boolean;
}

// 队列统计接口
export interface QueueStats {
  processed: number;
  failed: number;
  avgProcessingTime: number;
  throughput: number;
}

// 系统健康状态接口
export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  queues: QueueStatus[];
  redis: {
    connected: boolean;
    memory: number;
  };
  timestamp: Date;
}

// 任务事件接口
export interface TaskEvent {
  jobId: string;
  queueName: string;
  taskType: TaskType;
  status: TaskStatus;
  data?: any;
  error?: string;
  timestamp: Date;
}

// 队列配置接口
export interface QueueConfig {
  concurrency?: number;
  removeOnComplete?: number;
  removeOnFail?: number;
  defaultJobOptions?: {
    attempts?: number;
    backoff?: {
      type: 'fixed' | 'exponential';
      delay: number;
    };
    delay?: number;
    removeOnComplete?: boolean;
    removeOnFail?: boolean;
  };
}

// 任务处理器接口
export interface TaskProcessor<T = any> {
  process(job: Job<T>): Promise<TaskResult>;
}