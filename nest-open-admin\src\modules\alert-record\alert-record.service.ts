import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In } from 'typeorm';
import { AlertRecordEntity, AlertType, AlertLevel, AlertStatus } from './entities/alert-record.entity';
import {
  CreateAlertRecordDto,
  UpdateAlertRecordDto,
  QueryAlertRecordDto,
  BatchProcessAlertDto,
  AlertRecordResponseDto,
  AlertRecordListResponseDto,
  AlertStatsDto,
} from './dto/alert-record.dto';
import { UserService } from '../user/user.service';
import { ServiceService } from '../service/service.service';

@Injectable()
export class AlertRecordService {
  private readonly logger = new Logger(AlertRecordService.name);

  constructor(
    @InjectRepository(AlertRecordEntity)
    private readonly alertRecordRepository: Repository<AlertRecordEntity>,
    private readonly userService: UserService,
    private readonly serviceService: ServiceService,
  ) {}

  /**
   * 创建告警记录
   */
  async create(createAlertRecordDto: CreateAlertRecordDto): Promise<AlertRecordResponseDto> {
    // 验证用户是否存在
    const user = await this.userService.findById(createAlertRecordDto.userId);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 验证服务是否存在（如果提供了serviceId）
    if (createAlertRecordDto.serviceId) {
      const service = await this.serviceService.findOne(createAlertRecordDto.serviceId);
      if (!service) {
        throw new NotFoundException('服务不存在');
      }
    }

    // 创建告警记录
    const alertRecord = this.alertRecordRepository.create({
      userId: createAlertRecordDto.userId,
      serviceId: createAlertRecordDto.serviceId,
      type: createAlertRecordDto.type,
      level: createAlertRecordDto.level,
      title: createAlertRecordDto.title,
      content: createAlertRecordDto.content,
      triggerCondition: createAlertRecordDto.triggerCondition,
      currentValue: createAlertRecordDto.currentValue,
      threshold: createAlertRecordDto.threshold,
      metadata: createAlertRecordDto.metadata,
      status: createAlertRecordDto.status || AlertStatus.PENDING,
      notified: createAlertRecordDto.notified || false,
      notificationMethods: createAlertRecordDto.notificationMethods,
    });

    const savedAlertRecord = await this.alertRecordRepository.save(alertRecord);
    
    this.logger.log(`告警记录创建成功: ${savedAlertRecord.id}`);
    
    return this.transformToResponseDto(savedAlertRecord);
  }

  /**
   * 分页查询告警记录列表
   */
  async findAll(queryDto: QueryAlertRecordDto = {}): Promise<AlertRecordListResponseDto> {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'DESC', ...filters } = queryDto;
    const skip = (page - 1) * limit;

    const queryBuilder = this.alertRecordRepository
      .createQueryBuilder('alert')
      .leftJoinAndSelect('alert.user', 'user')
      .leftJoinAndSelect('alert.service', 'service')
      .leftJoinAndSelect('alert.resolver', 'resolver');

    // 应用筛选条件
    if (filters.userId) {
      queryBuilder.andWhere('alert.userId = :userId', { userId: filters.userId });
    }

    if (filters.serviceId) {
      queryBuilder.andWhere('alert.serviceId = :serviceId', { serviceId: filters.serviceId });
    }

    if (filters.type) {
      queryBuilder.andWhere('alert.type = :type', { type: filters.type });
    }

    if (filters.level) {
      queryBuilder.andWhere('alert.level = :level', { level: filters.level });
    }

    if (filters.status) {
      queryBuilder.andWhere('alert.status = :status', { status: filters.status });
    }

    if (filters.notified !== undefined) {
      queryBuilder.andWhere('alert.notified = :notified', { notified: filters.notified });
    }

    if (filters.title) {
      queryBuilder.andWhere('alert.title LIKE :title', { title: `%${filters.title}%` });
    }

    if (filters.content) {
      queryBuilder.andWhere('alert.content LIKE :content', { content: `%${filters.content}%` });
    }

    if (filters.startDate && filters.endDate) {
      queryBuilder.andWhere('alert.createdAt BETWEEN :startDate AND :endDate', {
        startDate: filters.startDate,
        endDate: filters.endDate,
      });
    } else if (filters.startDate) {
      queryBuilder.andWhere('alert.createdAt >= :startDate', { startDate: filters.startDate });
    } else if (filters.endDate) {
      queryBuilder.andWhere('alert.createdAt <= :endDate', { endDate: filters.endDate });
    }

    // 排序
    queryBuilder.orderBy(`alert.${sortBy}`, sortOrder);

    // 分页
    queryBuilder.skip(skip).take(limit);

    const [alertRecords, total] = await queryBuilder.getManyAndCount();

    return {
      data: alertRecords.map(record => this.transformToResponseDto(record)),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * 查询单个告警记录
   */
  async findOne(id: number): Promise<AlertRecordResponseDto> {
    const alertRecord = await this.alertRecordRepository.findOne({
      where: { id },
      relations: ['user', 'service', 'resolver'],
    });

    if (!alertRecord) {
      throw new NotFoundException('告警记录不存在');
    }

    return this.transformToResponseDto(alertRecord);
  }

  /**
   * 更新告警记录
   */
  async update(id: number, updateAlertRecordDto: UpdateAlertRecordDto): Promise<AlertRecordResponseDto> {
    const alertRecord = await this.alertRecordRepository.findOne({ where: { id } });
    if (!alertRecord) {
      throw new NotFoundException('告警记录不存在');
    }

    // 如果状态变更为已解决，记录解决时间
    if (updateAlertRecordDto.status === AlertStatus.RESOLVED && alertRecord.status !== AlertStatus.RESOLVED) {
      updateAlertRecordDto.resolvedAt = new Date().toISOString();
    }

    // 验证处理人是否存在（如果提供了resolvedBy）
    if (updateAlertRecordDto.resolvedBy) {
      const resolver = await this.userService.findById(updateAlertRecordDto.resolvedBy);
      if (!resolver) {
        throw new NotFoundException('处理人不存在');
      }
    }

    // 更新记录
    Object.assign(alertRecord, updateAlertRecordDto);
    const updatedAlertRecord = await this.alertRecordRepository.save(alertRecord);

    this.logger.log(`告警记录更新成功: ${id}`);

    return this.transformToResponseDto(updatedAlertRecord);
  }

  /**
   * 删除告警记录
   */
  async remove(id: number): Promise<{ message: string }> {
    const alertRecord = await this.alertRecordRepository.findOne({ where: { id } });
    if (!alertRecord) {
      throw new NotFoundException('告警记录不存在');
    }

    await this.alertRecordRepository.softDelete(id);
    
    this.logger.log(`告警记录删除成功: ${id}`);
    
    return { message: '告警记录删除成功' };
  }

  /**
   * 批量处理告警记录
   */
  async batchProcess(batchProcessDto: BatchProcessAlertDto): Promise<{ message: string; processedCount: number }> {
    const { ids, action, note } = batchProcessDto;

    // 验证告警记录是否存在
    const alertRecords = await this.alertRecordRepository.findBy({ id: In(ids) });
    if (alertRecords.length !== ids.length) {
      throw new BadRequestException('部分告警记录不存在');
    }

    const updateData: Partial<AlertRecordEntity> = {
      resolveNote: note,
      resolvedAt: new Date(),
    };

    switch (action) {
      case 'acknowledge':
        updateData.status = AlertStatus.ACKNOWLEDGED;
        break;
      case 'resolve':
        updateData.status = AlertStatus.RESOLVED;
        break;
      case 'ignore':
        updateData.status = AlertStatus.IGNORED;
        break;
      default:
        throw new BadRequestException('无效的操作类型');
    }

    await this.alertRecordRepository.update({ id: In(ids) }, updateData);

    this.logger.log(`批量处理告警记录成功: ${ids.length} 条记录`);

    return {
      message: `批量${action}操作成功`,
      processedCount: ids.length,
    };
  }

  /**
   * 获取告警统计信息
   */
  async getStats(): Promise<AlertStatsDto> {
    const totalAlerts = await this.alertRecordRepository.count();
    const pendingAlerts = await this.alertRecordRepository.count({ where: { status: AlertStatus.PENDING } });
    const acknowledgedAlerts = await this.alertRecordRepository.count({ where: { status: AlertStatus.ACKNOWLEDGED } });
    const resolvedAlerts = await this.alertRecordRepository.count({ where: { status: AlertStatus.RESOLVED } });
    const ignoredAlerts = await this.alertRecordRepository.count({ where: { status: AlertStatus.IGNORED } });

    // 按类型统计
    const byTypeQuery = await this.alertRecordRepository
      .createQueryBuilder('alert')
      .select('alert.type', 'type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('alert.type')
      .getRawMany();

    const byType = Object.values(AlertType).reduce((acc, type) => {
      acc[type] = 0;
      return acc;
    }, {} as Record<AlertType, number>);

    byTypeQuery.forEach(item => {
      byType[item.type as AlertType] = parseInt(item.count);
    });

    // 按级别统计
    const byLevelQuery = await this.alertRecordRepository
      .createQueryBuilder('alert')
      .select('alert.level', 'level')
      .addSelect('COUNT(*)', 'count')
      .groupBy('alert.level')
      .getRawMany();

    const byLevel = Object.values(AlertLevel).reduce((acc, level) => {
      acc[level] = 0;
      return acc;
    }, {} as Record<AlertLevel, number>);

    byLevelQuery.forEach(item => {
      byLevel[item.level as AlertLevel] = parseInt(item.count);
    });

    // 按状态统计
    const byStatusQuery = await this.alertRecordRepository
      .createQueryBuilder('alert')
      .select('alert.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('alert.status')
      .getRawMany();

    const byStatus = Object.values(AlertStatus).reduce((acc, status) => {
      acc[status] = 0;
      return acc;
    }, {} as Record<AlertStatus, number>);

    byStatusQuery.forEach(item => {
      byStatus[item.status as AlertStatus] = parseInt(item.count);
    });

    // 计算平均处理时间（简化实现）
    const resolvedRecords = await this.alertRecordRepository.find({
      where: { status: AlertStatus.RESOLVED },
      select: ['createdAt', 'resolvedAt'],
    });

    let avgResolutionTime = 0;
    if (resolvedRecords.length > 0) {
      const totalResolutionTime = resolvedRecords.reduce((sum, record) => {
        if (record.resolvedAt) {
          const diff = new Date(record.resolvedAt).getTime() - new Date(record.createdAt).getTime();
          return sum + diff;
        }
        return sum;
      }, 0);
      avgResolutionTime = totalResolutionTime / resolvedRecords.length / (1000 * 60 * 60); // 转换为小时
    }

    return {
      totalAlerts,
      pendingAlerts,
      acknowledgedAlerts,
      resolvedAlerts,
      ignoredAlerts,
      byType,
      byLevel,
      byStatus,
      byService: {}, // 简化实现
      byUser: {}, // 简化实现
      byDay: {}, // 简化实现
      avgResolutionTime: Math.round(avgResolutionTime * 100) / 100,
      notificationSuccessRate: 95.5, // 简化实现
    };
  }

  /**
   * 转换为响应DTO
   */
  private transformToResponseDto(alertRecord: AlertRecordEntity): AlertRecordResponseDto {
    return {
      id: alertRecord.id,
      userId: alertRecord.userId,
      serviceId: alertRecord!.serviceId as number,
      type: alertRecord.type,
      level: alertRecord.level,
      title: alertRecord.title,
      content: alertRecord.content,
      triggerCondition: alertRecord.triggerCondition,
      currentValue: alertRecord.currentValue,
      threshold: alertRecord.threshold,
      metadata: alertRecord.metadata,
      status: alertRecord.status,
      notified: alertRecord.notified,
      notificationMethods: alertRecord.notificationMethods,
      resolveNote: alertRecord.resolveNote,
      resolvedBy: alertRecord.resolvedBy,
      resolvedAt: alertRecord.resolvedAt,
      createdAt: alertRecord.createdAt,
      updatedAt: alertRecord.updatedAt,
      user: alertRecord.user ? {
        id: alertRecord.user.id,
        username: alertRecord.user.username,
        email: alertRecord.user.email,
      } : undefined,
      service: alertRecord.service ? {
        id: alertRecord.service.id,
        code: alertRecord.service.code,
        name: alertRecord.service.name,
        type: alertRecord.service.type,
      } : undefined,
      resolver: alertRecord.resolver ? {
        id: alertRecord.resolver.id,
        username: alertRecord.resolver.username,
        email: alertRecord.resolver.email,
      } : undefined,
    };
  }
}
