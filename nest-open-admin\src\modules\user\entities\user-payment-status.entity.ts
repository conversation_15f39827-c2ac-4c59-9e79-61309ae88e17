import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';

/**
 * 用户付费状态实体
 * 用于跟踪用户的付费状态，决定是否还享受免费额度
 */
@Entity('user_payment_status')
export class UserPaymentStatusEntity extends BaseEntity {
  @Column({ default: false })
  hasPaid: boolean; // 用户是否已付费

  @Column({ nullable: true })
  lastPaymentDate: Date; // 最近一次付费时间

  @Column({ default: true })
  eligibleForFreeUsage: boolean; // 是否有资格获得免费使用额度

  @Column({ default: 0 })
  totalPaidAmount: number; // 总付费金额

  @Column({ type: 'simple-json', nullable: true })
  paymentHistory: { date: Date; amount: number; method: string }[]; // 付费历史

  // 关系 - 使用字符串关系定义，避免循环引用
  @ManyToOne('UserEntity', 'paymentStatus')
  @JoinColumn({ name: 'userId' })
  user: any;
} 