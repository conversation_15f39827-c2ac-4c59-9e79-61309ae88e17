import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDateString, IsOptional, IsInt } from 'class-validator';

export class CallStatisticsDto {
  @ApiProperty({ description: '日期', example: '2023-08-01' })
  date: string;

  @ApiProperty({ description: '总调用次数', example: 52 })
  totalCalls: number;

  @ApiProperty({ description: '成功调用次数', example: 48 })
  successCalls: number;

  @ApiProperty({ description: '失败调用次数', example: 4 })
  failedCalls: number;

  @ApiProperty({ description: '总耗时(ms)', example: 7650 })
  totalDuration: number;

  @ApiProperty({ description: '平均耗时(ms)', example: 159.38 })
  avgDuration: number;
}

export class CallStatisticsSummaryDto {
  @ApiProperty({ description: '总调用次数', example: 110 })
  totalCalls: number;

  @ApiProperty({ description: '成功调用次数', example: 100 })
  successCalls: number;

  @ApiProperty({ description: '失败调用次数', example: 10 })
  failedCalls: number;

  @ApiProperty({ description: '成功率', example: 0.91 })
  successRate: number;

  @ApiProperty({ description: '平均耗时(ms)', example: 159.7 })
  avgDuration: number;

  @ApiProperty({ description: '日期范围' })
  dateRange: {
    start: string;
    end: string;
  };
}

export class CallStatisticsResponseDto {
  @ApiProperty({ description: '统计数据列表', type: [CallStatisticsDto] })
  statistics: CallStatisticsDto[];

  @ApiProperty({ description: '汇总数据', type: CallStatisticsSummaryDto })
  summary: CallStatisticsSummaryDto;
}

export class CallStatisticsQueryDto {
  @ApiPropertyOptional({ description: '用户ID' })
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  userId?: number;

  @ApiPropertyOptional({ description: '服务ID' })
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  serviceId?: number;

  @ApiProperty({ description: '开始日期' })
  @IsDateString()
  startDate: string;

  @ApiProperty({ description: '结束日期' })
  @IsDateString()
  endDate: string;
} 