import { request } from '@/utils/request'
import type { Service, ServiceDetail, ServiceVersion } from '@/types/service'

// 后端响应格式
interface ServiceListResponse {
  data: Service[]
  meta: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

/**
 * 服务相关API
 */
export const serviceApi = {
  /**
   * 获取服务列表
   * @param params 查询参数
   */
  getServices(params?: {
    page?: number
    pageSize?: number
    status?: string
    category?: string
    keyword?: string
  }): Promise<ServiceListResponse> {
    // 将pageSize转换为limit，与后端参数保持一致
    const queryParams = {
      ...params,
      limit: params?.pageSize,
      page: params?.page || 1
    }
    delete queryParams.pageSize
    
    return request.get('services', { params: queryParams })
  },

  /**
   * 根据ID获取服务详情
   * @param id 服务ID
   */
  getServiceById(id: string): Promise<ServiceDetail> {
    return request.get(`services/${id}`)
  },

  /**
   * 根据代码获取服务详情
   * @param code 服务代码
   */
  getServiceByCode(code: string): Promise<ServiceDetail> {
    return request.get(`services/code/${code}`)
  },

  /**
   * 创建服务
   * @param data 服务数据
   */
  createService(data: Partial<Service>): Promise<Service> {
    return request.post('services', data)
  },

  /**
   * 更新服务
   * @param id 服务ID
   * @param data 更新数据
   */
  updateService(id: string, data: Partial<Service>): Promise<Service> {
    return request.put(`services/${id}`, data)
  },

  /**
   * 删除服务
   * @param id 服务ID
   */
  deleteService(id: string): Promise<void> {
    return request.delete(`services/${id}`)
  },

  /**
   * 搜索服务
   * @param keyword 关键词
   */
  searchServices(keyword: string): Promise<Service[]> {
    return request.get('services/search', {
      params: { keyword }
    })
  },

  /**
   * 获取服务分类
   */
  getServiceCategories(): Promise<string[]> {
    return request.get('services/categories')
  },

  /**
   * 获取服务版本列表
   * @param serviceId 服务ID
   */
  getServiceVersions(serviceId: string): Promise<ServiceVersion[]> {
    return request.get(`services/${serviceId}/versions`)
  },

  /**
   * 获取服务统计信息
   * @param serviceId 服务ID
   */
  getServiceStats(serviceId: string): Promise<{
    totalCalls: number
    successRate: number
    avgResponseTime: number
    errorCount: number
  }> {
    return request.get(`services/${serviceId}/stats`)
  },

  /**
   * 测试服务
   * @param serviceId 服务ID
   * @param testData 测试数据
   */
  testService(serviceId: string, testData: any): Promise<any> {
    return request.post(`services/${serviceId}/test`, testData)
  }
}