ARG NODE_VERSION=18.18.0
FROM node:${NODE_VERSION}-slim AS builder

WORKDIR /app

# 设置时区为亚洲/上海
ENV TZ=Asia/Shanghai

# 配置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装pnpm并允许构建脚本运行
RUN npm install -g pnpm@8.8.0 && \
    pnpm config set enable-pre-post-scripts true

# 复制package.json和pnpm-lock.yaml
COPY package.json pnpm-lock.yaml ./

# 安装依赖
RUN pnpm install

# 复制源代码
COPY . .

# 确保构建脚本运行
RUN pnpm config set unsafe-perm true && \
    pnpm run build

    # 检查构建结果
RUN ls -la dist || echo "构建失败，dist目录不存在"

# 生产阶段
FROM node:${NODE_VERSION}-slim

WORKDIR /app

# 设置时区为亚洲/上海
ENV TZ=Asia/Shanghai

# 配置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装pnpm并允许构建脚本运行
RUN npm install -g pnpm@8.8.0 && \
    pnpm config set enable-pre-post-scripts true

# 复制package.json和pnpm-lock.yaml
COPY --from=builder /app/package.json /app/pnpm-lock.yaml ./

# 只安装生产依赖
RUN pnpm install --prod

# 复制编译后的代码和配置文件
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/src/config ./dist/config


# 设置环境变量
ENV NODE_ENV=production \
    TZ=Asia/Shanghai

# 暴露端口
EXPOSE 8088

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD node -e "require('http').request({host: 'localhost', port: 8088, path: '/v1/op/ocr/test', timeout: 5000}, (res) => { process.exit(res.statusCode !== 200 ? 1 : 0); }).on('error', () => process.exit(1)).end()"

# 启动应用
CMD ["node", "dist/main.js"] 