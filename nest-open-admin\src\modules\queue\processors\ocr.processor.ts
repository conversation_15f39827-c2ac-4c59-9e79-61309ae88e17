/**
 * OCR识别处理器
 * 处理OCR识别任务的具体逻辑
 */

import { Process, Processor } from '@nestjs/bull';
import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { Job } from 'bull';
import { ModuleRef } from '@nestjs/core';
import { QUEUE_NAMES, TASK_TYPES } from '../queue.constants';
import { OcrTaskData, TaskResult } from '../interfaces/queue.interface';
import { OcrExecutorService } from '../../ocr/services/ocr-executor.service';
import { ApiUsageTrackerService } from '../../call-record/services/api-usage-tracker.service';

/**
 * OCR处理器
 * 处理OCR队列中的任务
 */
@Injectable()
@Processor(QUEUE_NAMES.OCR)
export class OcrProcessor implements OnModuleInit {
  private readonly logger = new Logger(OcrProcessor.name);
  private ocrExecutorService: OcrExecutorService | null = null;
  private apiUsageTrackerService: ApiUsageTrackerService | null = null;

  constructor(private readonly moduleRef: ModuleRef) {}

  /**
   * 模块初始化时，动态获取所需服务
   */
  async onModuleInit() {
    try {
      // 尝试获取OCR执行器服务
      try {
        this.ocrExecutorService = await this.moduleRef.resolve(OcrExecutorService, undefined, { strict: false });
        this.logger.log('成功获取OCR执行器服务');
      } catch (error) {
        this.logger.error(`获取OCR执行器服务失败: ${error.message}`);
        this.ocrExecutorService = null;
      }
      
      // 尝试获取API使用跟踪服务
      try {
        this.apiUsageTrackerService = await this.moduleRef.resolve(ApiUsageTrackerService, undefined, { strict: false });
        this.logger.log('成功获取API使用跟踪服务');
      } catch (error) {
        this.logger.warn(`获取API使用跟踪服务失败: ${error.message}`);
        this.apiUsageTrackerService = null;
      }
    } catch (error) {
      this.logger.error(`初始化服务失败: ${error.message}`);
    }
  }

  /**
   * 处理OCR识别任务
   * 使用TASK_TYPES常量
   */
  @Process(TASK_TYPES.OCR_RECOGNITION)
  async process(job: Job<OcrTaskData & Record<string, any>>): Promise<TaskResult> {
    try {
      this.logger.log(`开始处理OCR任务: ${job.id}, 数据: ${JSON.stringify({
        ...job.data,
        imageData: job.data.imageData ? `[Base64数据，长度: ${job.data.imageData.length}]` : undefined,
        image: job.data.image ? `[Base64数据，长度: ${job.data.image.length}]` : undefined,
        image_base64: job.data.image_base64 ? `[Base64数据，长度: ${job.data.image_base64.length}]` : undefined,
      }).substring(0, 200)}...`);
      
      // 打印关键字段，帮助调试
      this.logger.debug(`OCR任务数据字段: ${Object.keys(job.data).join(', ')}`);
      this.logger.debug(`图像数据是否存在: ${!!job.data.imageData}, 长度: ${job.data.imageData?.length || 0}`);
      this.logger.debug(`备用字段image是否存在: ${!!job.data.image}, 长度: ${job.data.image?.length || 0}`);
      this.logger.debug(`备用字段image_base64是否存在: ${!!job.data.image_base64}, 长度: ${job.data.image_base64?.length || 0}`);
      
      await this.updateProgress(job, 0, '任务开始处理');
      
      // 确保任务不会被自动删除
      job.opts.removeOnComplete = false;
      
      // 尝试修复缺失的imageData
      if (!job.data.imageData) {
        this.logger.warn(`任务 ${job.id} 缺少imageData字段，尝试从其他字段获取`);
        if (job.data.image) {
          this.logger.debug(`使用image字段作为imageData`);
          job.data.imageData = job.data.image;
        } else if (job.data.image_base64) {
          this.logger.debug(`使用image_base64字段作为imageData`);
          // 修复Base64填充问题
          const base64Data = job.data.image_base64.replace(/^data:image\/\w+;base64,/, '');
          const padding = base64Data.length % 4;
          if (padding) {
            // 添加必要的填充字符
            const paddedBase64 = base64Data + '='.repeat(4 - padding);
            this.logger.debug(`修复Base64填充: 原长度=${base64Data.length}, 填充后长度=${paddedBase64.length}, 添加了${4 - padding}个填充字符`);
            job.data.image_base64 = job.data.image_base64.startsWith('data:') 
              ? job.data.image_base64.replace(base64Data, paddedBase64)
              : paddedBase64;
          }
          job.data.imageData = job.data.image_base64;
        }
      }
      
      // 检查图像数据格式
      if (job.data.imageData && !job.data.imageData.startsWith('data:')) {
        this.logger.warn(`图像数据格式不正确，尝试添加前缀`);
        // 尝试添加前缀
        const mimeType = job.data.file?.mimetype || 'image/png';
        job.data.imageData = `data:${mimeType};base64,${job.data.imageData}`;
        this.logger.debug(`已添加前缀: ${job.data.imageData.substring(0, 50)}...`);
      }
      
      // 验证任务数据
      this.validateJobData(job.data);
      
      // 更新进度
      await this.updateProgress(job, 20, '正在准备OCR处理');
      
      // 获取OCR执行器服务（如果尚未获取）
      if (!this.ocrExecutorService) {
        this.logger.log('尝试获取OCR执行器服务');
        this.ocrExecutorService = await this.getServiceSafely<OcrExecutorService>(
          OcrExecutorService,
          () => this.createMockOcrExecutorService()
        );
        this.logger.log('OCR执行器服务获取成功');
      }
      
      // 更新进度
      await this.updateProgress(job, 40, '正在处理OCR识别');
      
      // 处理OCR识别
      this.logger.log(`调用OCR执行器处理图片数据，长度: ${job.data.imageData?.length || 0}`);
      const result = await this.ocrExecutorService.processOcrFromBase64(
        job.data.imageData,
        job.data.filename || job.data.file?.originalname,
        {
          userId: job.data.userId,
          apiKey: job.data.apiKeyId,
          serviceCode: job.data.serviceId?.toString(),
        }
      );
      
      this.logger.log(`OCR处理结果: ${JSON.stringify(result).substring(0, 200)}...`);
      
      // 更新进度
      await this.updateProgress(job, 80, '正在更新API使用记录');
      
      // 记录API使用情况
      if (job.data.userId && job.data.apiKeyId && job.data.serviceId) {
        try {
          // 获取API使用跟踪服务（如果尚未获取）
          if (!this.apiUsageTrackerService) {
            this.apiUsageTrackerService = await this.getServiceSafely<ApiUsageTrackerService>(
              ApiUsageTrackerService,
              () => this.createMockApiUsageTrackerService()
            );
          }
          
          if (this.apiUsageTrackerService) {
            // 使用trackApiCall方法，这是简化版的API调用记录
            await this.apiUsageTrackerService.trackApiCall({
              userId: job.data.userId,
              apiKeyId: job.data.apiKeyId,
              serviceId: job.data.serviceId,
              endpoint: 'OCR识别',
              responseTime: result.processingTime || 0,
              status: result.success ? 'completed' : 'failed',
              requestId: job.data.requestId || `req_${Date.now()}`,
              jobId: job.id.toString(),
              error: result.error,
            });
          }
        } catch (error) {
          this.logger.error(`记录API使用失败: ${error.message}`, error.stack);
          // 不中断主流程，继续返回结果
        }
      }
      
      await this.updateProgress(job, 100, '任务处理完成');
      this.logger.log(`OCR任务处理成功: ${job.id}`);
      
      // 构造标准化的返回结果结构
      const standardizedResult = {
        success: result.success,
        data: {
          rawData: {
            results: result.data?.results || result.data?.words_result || []
          },
          process_time: result.processingTime || 0,
          image_info: result.data?.image_info || {}
        },
        error: result.error,
        duration: result.processingTime,
      };
      
      // 打印最终返回结果
      this.logger.log(`OCR任务最终返回结果: ${JSON.stringify(standardizedResult).substring(0, 200)}...`);
      
      return standardizedResult;
    } catch (error) {
      this.logger.error(`OCR任务处理失败: ${job.id}, 错误: ${error.message}`, error.stack);
      
      try {
        await this.updateProgress(job, 100, `处理失败: ${error.message}`);
      } catch (err) {
        this.logger.error(`更新任务失败状态时出错: ${err.message}`);
      }
      
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 验证任务数据
   */
  private validateJobData(data: OcrTaskData & Record<string, any>): void {
    this.logger.debug(`验证任务数据: ${JSON.stringify(data).substring(0, 200)}...`);
    
    if (!data) {
      this.logger.error('任务数据不能为空');
      throw new Error('任务数据不能为空');
    }
    
    // 检查所有可能包含图像数据的字段
    if (!data.imageData && !data.image && !data.image_base64) {
      this.logger.error('缺少必要的图像数据，所有可能的字段都为空');
      throw new Error('缺少必要的图像数据');
    }
    
    // 使用第一个有效的图像数据字段
    const imageData = data.imageData || data.image || data.image_base64;
    
    // 验证Base64格式
    if (
      !imageData.startsWith('data:image/') && 
      !imageData.startsWith('data:application/octet-stream') &&
      !imageData.startsWith('data:application/pdf')
    ) {
      this.logger.error(`无效的图片数据格式: ${imageData.substring(0, 30)}...`);
      throw new Error('无效的图片数据格式');
    }
    
    // 如果使用的是备用字段，确保imageData字段存在
    if (!data.imageData) {
      this.logger.debug('使用备用字段作为imageData');
      data.imageData = imageData;
    }
    
    this.logger.debug('任务数据验证通过');
  }

  /**
   * 安全地获取服务实例
   */
  private async getServiceSafely<T>(
    serviceToken: any,
    fallbackFactory?: () => T
  ): Promise<T> {
    try {
      return await this.moduleRef.resolve(serviceToken, undefined, { strict: false });
    } catch (error) {
      this.logger.warn(
        `无法解析服务 ${serviceToken.name || serviceToken}，使用回退实现: ${error.message}`
      );
      
      if (fallbackFactory) {
        return fallbackFactory();
      }
      
      throw new Error(`无法获取服务 ${serviceToken.name || serviceToken}，且未提供回退实现`);
    }
  }

  /**
   * 处理任务进度更新
   */
  private async updateProgress(
    job: Job,
    progress: number,
    message?: string
  ): Promise<void> {
    try {
      await job.progress({
        progress: Math.min(100, Math.max(0, progress)),
        message: message || `处理进度: ${progress}%`,
        timestamp: Date.now(),
      });
    } catch (error) {
      this.logger.error(`更新任务进度失败: ${error.message}`);
    }
  }

  /**
   * 创建模拟OCR执行器服务
   */
  private createMockOcrExecutorService(): OcrExecutorService {
    const mockService = {
      processOcrFromBase64: async () => {
        this.logger.warn('使用模拟OCR执行器服务');
        return {
          success: false,
          error: '无法获取OCR执行器服务，使用模拟服务',
          processingTime: 0,
        };
      },
    };
    
    return mockService as unknown as OcrExecutorService;
  }

  /**
   * 创建模拟API使用跟踪服务
   */
  private createMockApiUsageTrackerService(): ApiUsageTrackerService {
    const mockService = {
      trackApiCall: async () => {
        this.logger.warn('使用模拟API使用跟踪服务');
        return 'mock-call-record-id';
      },
      trackApiUsage: async () => {
        this.logger.warn('使用模拟API使用跟踪服务');
        return true;
      },
    };
    
    return mockService as unknown as ApiUsageTrackerService;
  }
} 