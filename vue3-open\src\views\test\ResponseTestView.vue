<template>
  <div class="response-test-page">
    <div class="test-container">
      <h1>响应格式测试</h1>
      
      <div class="test-section">
        <h3>短信验证码测试</h3>
        <el-form inline>
          <el-form-item label="手机号">
            <el-input v-model="testPhone" placeholder="请输入手机号" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="testSendSms" :loading="loading">
              发送短信验证码
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="test-section">
        <h3>邮箱验证码测试</h3>
        <el-form inline>
          <el-form-item label="邮箱">
            <el-input v-model="testEmail" placeholder="请输入邮箱" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="testSendEmail" :loading="loading">
              发送邮箱验证码
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="test-section">
        <h3>响应日志</h3>
        <div class="log-container">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            <div class="log-time">{{ log.time }}</div>
            <div class="log-type" :class="log.type">{{ log.type.toUpperCase() }}</div>
            <div class="log-message">{{ log.message }}</div>
            <div v-if="log.data" class="log-data">
              <pre>{{ JSON.stringify(log.data, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const testPhone = ref('13900139004')
const testEmail = ref('<EMAIL>')
const loading = ref(false)
const logs = ref<Array<{
  time: string
  type: 'success' | 'error' | 'info'
  message: string
  data?: any
}>>([])

const addLog = (type: 'success' | 'error' | 'info', message: string, data?: any) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    type,
    message,
    data
  })
  
  // 限制日志数量
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20)
  }
}

const testSendSms = async () => {
  if (!testPhone.value) {
    ElMessage.warning('请输入手机号')
    return
  }

  try {
    loading.value = true
    addLog('info', `开始发送短信验证码到: ${testPhone.value}`)
    
    const response = await userStore.sendSmsCode(testPhone.value, 'login')
    
    addLog('success', '短信验证码发送成功', response)
    ElMessage.success('短信验证码发送成功')
  } catch (error: any) {
    addLog('error', `短信验证码发送失败: ${error.message}`, error)
    ElMessage.error(`发送失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

const testSendEmail = async () => {
  if (!testEmail.value) {
    ElMessage.warning('请输入邮箱')
    return
  }

  try {
    loading.value = true
    addLog('info', `开始发送邮箱验证码到: ${testEmail.value}`)
    
    const response = await userStore.sendEmailCode(testEmail.value, 'login')
    
    addLog('success', '邮箱验证码发送成功', response)
    ElMessage.success('邮箱验证码发送成功')
  } catch (error: any) {
    addLog('error', `邮箱验证码发送失败: ${error.message}`, error)
    ElMessage.error(`发送失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.response-test-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.test-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #eee;
}

.test-section:last-child {
  border-bottom: none;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 16px;
  background: #f9f9f9;
}

.log-item {
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 4px;
  border-left: 4px solid #ddd;
}

.log-item:last-child {
  margin-bottom: 0;
}

.log-time {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.log-type {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 8px;
}

.log-type.success {
  background: #f0f9ff;
  color: #059669;
  border-left-color: #059669;
}

.log-type.error {
  background: #fef2f2;
  color: #dc2626;
  border-left-color: #dc2626;
}

.log-type.info {
  background: #f0f9ff;
  color: #2563eb;
  border-left-color: #2563eb;
}

.log-message {
  font-weight: 500;
  margin-bottom: 8px;
}

.log-data {
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  overflow-x: auto;
}

.log-data pre {
  margin: 0;
  white-space: pre-wrap;
}
</style>
