import { ApiProperty } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';
import { UserStatus, UserType, UserVerificationStatus, TierEnum, RoleEnum } from '../entities/user.entity';

export class UserResponseDto {
  @ApiProperty({ description: '用户ID' })
  @Expose()
  id: number;

  @ApiProperty({ description: '用户名' })
  @Expose()
  username: string;

  @ApiProperty({ description: '邮箱' })
  @Expose()
  email: string;

  @Exclude()
  password: string;

  @ApiProperty({ description: '昵称' })
  @Expose()
  nickname: string;

  @ApiProperty({ description: '真实姓名', required: false })
  @Expose()
  realName?: string;

  @ApiProperty({ description: '手机号', required: false })
  @Expose()
  phone?: string;

  @ApiProperty({ description: '头像URL', required: false })
  @Expose()
  avatar?: string;

  @ApiProperty({ description: '用户类型', enum: UserType })
  @Expose()
  userType: UserType;

  @ApiProperty({ description: '认证状态', enum: UserVerificationStatus })
  @Expose()
  verificationStatus: UserVerificationStatus;

  @ApiProperty({ description: '账户状态', enum: UserStatus })
  @Expose()
  userStatus: UserStatus;

  @ApiProperty({ description: '角色', enum: RoleEnum })
  @Expose()
  role: RoleEnum;

  @ApiProperty({ description: '邮箱是否验证' })
  @Expose()
  emailVerified: boolean;

  @ApiProperty({ description: '手机是否验证' })
  @Expose()
  phoneVerified: boolean;

  @ApiProperty({ description: '最后登录时间', required: false })
  @Expose()
  lastLoginAt?: Date;

  @ApiProperty({ description: '最后登录IP', required: false })
  @Expose()
  lastLoginIp?: string;

  @ApiProperty({ description: '用户等级', enum: TierEnum })
  @Expose()
  tier: TierEnum;

  @ApiProperty({ description: '是否有资格获取免费额度' })
  @Expose()
  isFreeQuotaEligible: boolean;

  @ApiProperty({ description: '创建时间' })
  @Expose()
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  @Expose()
  updatedAt: Date;
}

export class UserListResponseDto {
  @ApiProperty({ description: '用户列表', type: [UserResponseDto] })
  @Expose()
  data: UserResponseDto[];

  @ApiProperty({ description: '总数' })
  @Expose()
  total: number;

  @ApiProperty({ description: '当前页码' })
  @Expose()
  page: number;

  @ApiProperty({ description: '每页条数' })
  @Expose()
  limit: number;

  @ApiProperty({ description: '总页数' })
  @Expose()
  totalPages: number;
} 