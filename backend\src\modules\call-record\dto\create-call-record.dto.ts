import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsNotEmpty, IsNumber, IsInt, Min } from 'class-validator';
import { CallStatus } from '../enums/call-status.enum';

export class CreateCallRecordDto {
  @ApiProperty({ description: '用户ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  userId: number;

  @ApiProperty({ description: '服务ID', example: 1 })
  @IsInt()
  @IsNotEmpty()
  serviceId: number;

  @ApiProperty({ description: 'HTTP方法', example: 'POST' })
  @IsString()
  @IsNotEmpty()
  method: string;

  @ApiPropertyOptional({ description: '请求唯一标识', example: 'req_abcdef123456' })
  @IsOptional()
  @IsString()
  requestId?: string;

  @ApiProperty({
    description: '调用状态',
    enum: CallStatus,
    example: CallStatus.SUCCESS
  })
  @IsEnum(CallStatus)
  status: CallStatus;

  @ApiPropertyOptional({ description: '失败原因', example: '服务内部错误' })
  @IsOptional()
  @IsString()
  failReason?: string;

  @ApiProperty({ description: '调用耗时(ms)', example: 156 })
  @IsInt()
  @Min(0)
  duration: number;

  @ApiPropertyOptional({ description: 'IP地址', example: '***********' })
  @IsOptional()
  @IsString()
  ipAddress?: string;

  @ApiPropertyOptional({ description: 'API密钥ID', example: 'key_12345' })
  @IsOptional()
  @IsString()
  apiKeyId?: string;
} 