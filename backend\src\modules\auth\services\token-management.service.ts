import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '@/shared/redis.service';
import { AuthUser } from '@/common/types/auth.types';

/**
 * 令牌管理服务
 * 专门负责JWT令牌的生成、验证、刷新和撤销
 */
@Injectable()
export class TokenManagementService {
  private readonly logger = new Logger(TokenManagementService.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * 生成访问令牌和刷新令牌
   */
  async generateTokens(user: any): Promise<{
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    refreshExpiresIn: number;
  }> {
    const payload = {
      sub: user.id,
      username: user.username,
      nickname: user.nickname,
      email: user.email,
      phone: user.phone,
      userType: user.userType,
      status: user.userStatus,
      tier: user.tier,
      role: user.role,
      verificationStatus: user.verificationStatus,
      balance: user.balance,
    };

    const accessTokenExpiry = this.configService.get<number>('auth.jwt.expiresIn', 86400); // 24小时
    const refreshTokenExpiry = this.configService.get<number>('auth.jwt.refreshExpiresIn', 604800); // 7天

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, {
        expiresIn: accessTokenExpiry,
        secret: this.configService.get<string>('auth.jwt.secret'),
      }),
      this.jwtService.signAsync(
        { sub: user.id, type: 'refresh' },
        {
          expiresIn: refreshTokenExpiry,
          secret: this.configService.get<string>('auth.jwt.refreshSecret'),
        }
      ),
    ]);

    // 存储刷新令牌到Redis（用于撤销检查）
    const refreshTokenKey = `refresh_token:${user.id}:${this.getTokenId(refreshToken)}`;
    await this.redisService.set(refreshTokenKey, 'valid', refreshTokenExpiry);

    this.logger.log(`令牌已生成: 用户${user.id}`);

    return {
      accessToken,
      refreshToken,
      expiresIn: accessTokenExpiry,
      refreshExpiresIn: refreshTokenExpiry,
    };
  }

  /**
   * 刷新访问令牌
   */
  async refreshAccessToken(refreshToken: string): Promise<{
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  }> {
    try {
      // 验证刷新令牌
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get<string>('auth.jwt.refreshSecret'),
      });

      if (!payload.sub || payload.type !== 'refresh') {
        throw new UnauthorizedException('无效的刷新令牌');
      }

      // 检查刷新令牌是否在黑名单中
      const tokenId = this.getTokenId(refreshToken);
      const refreshTokenKey = `refresh_token:${payload.sub}:${tokenId}`;
      const isValid = await this.redisService.get(refreshTokenKey);

      if (!isValid) {
        throw new UnauthorizedException('刷新令牌已失效');
      }

      // 这里应该从用户服务获取最新的用户信息
      // 为了简化，我们使用payload中的基本信息
      const userInfo = {
        id: payload.sub,
        // 其他字段需要从数据库获取最新信息
      };

      // 生成新的令牌对
      const newTokens = await this.generateTokens(userInfo);

      // 撤销旧的刷新令牌
      await this.redisService.del(refreshTokenKey);

      this.logger.log(`令牌已刷新: 用户${payload.sub}`);

      return newTokens;
    } catch (error) {
      this.logger.error(`刷新令牌失败: ${error.message}`);
      throw new UnauthorizedException('刷新令牌已失效或已过期');
    }
  }

  /**
   * 撤销令牌（登出）
   */
  async revokeTokens(accessToken: string, refreshToken: string): Promise<void> {
    try {
      const promises: Promise<any>[] = [];

      // 将访问令牌加入黑名单
      if (accessToken) {
        const accessTokenTtl = this.getTokenTtl(accessToken);
        if (accessTokenTtl > 0) {
          const blacklistKey = `token_blacklist:${this.getTokenId(accessToken)}`;
          promises.push(this.redisService.set(blacklistKey, 'revoked', accessTokenTtl));
        }
      }

      // 撤销刷新令牌
      if (refreshToken) {
        try {
          const payload = this.jwtService.verify(refreshToken, {
            secret: this.configService.get<string>('auth.jwt.refreshSecret'),
          });
          
          if (payload.sub) {
            const refreshTokenKey = `refresh_token:${payload.sub}:${this.getTokenId(refreshToken)}`;
            promises.push(this.redisService.del(refreshTokenKey));
          }
        } catch (error) {
          // 刷新令牌可能已过期，忽略错误
          this.logger.warn(`撤销刷新令牌时出错: ${error.message}`);
        }
      }

      await Promise.all(promises);
      this.logger.log('令牌已撤销');
    } catch (error) {
      this.logger.error(`撤销令牌失败: ${error.message}`, error.stack);
      // 即使撤销失败，也不抛出异常，因为从客户端角度看登出已完成
    }
  }

  /**
   * 检查访问令牌是否在黑名单中
   */
  async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const blacklistKey = `token_blacklist:${this.getTokenId(token)}`;
      return await this.redisService.exists(blacklistKey);
    } catch (error) {
      this.logger.error(`检查令牌黑名单失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 撤销用户的所有令牌（用于安全事件）
   */
  async revokeAllUserTokens(userId: number): Promise<void> {
    try {
      // 删除用户的所有刷新令牌
      const pattern = `refresh_token:${userId}:*`;
      const keys = await this.redisService.keys(pattern);
      
      if (keys.length > 0) {
        for (const key of keys) {
          await this.redisService.del(key);
        }
      }

      this.logger.warn(`用户${userId}的所有令牌已撤销`);
    } catch (error) {
      this.logger.error(`撤销用户所有令牌失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取令牌的唯一标识符
   */
  private getTokenId(token: string): string {
    // 使用令牌的最后16个字符作为标识符
    return token.slice(-16);
  }

  /**
   * 获取令牌的剩余有效时间（秒）
   */
  private getTokenTtl(token: string): number {
    try {
      const decoded = this.jwtService.decode(token) as any;
      if (!decoded || !decoded.exp) {
        return 0;
      }
      
      const now = Math.floor(Date.now() / 1000);
      const ttl = decoded.exp - now;
      
      return ttl > 0 ? ttl : 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 验证访问令牌
   */
  async validateAccessToken(token: string): Promise<{
    isValid: boolean;
    payload?: any;
    error?: string;
  }> {
    try {
      // 检查是否在黑名单中
      if (await this.isTokenBlacklisted(token)) {
        return { isValid: false, error: '令牌已被撤销' };
      }

      // 验证令牌
      const payload = this.jwtService.verify(token, {
        secret: this.configService.get<string>('auth.jwt.secret'),
      });

      return { isValid: true, payload };
    } catch (error) {
      let errorMessage = '令牌验证失败';
      
      if (error.name === 'TokenExpiredError') {
        errorMessage = '令牌已过期';
      } else if (error.name === 'JsonWebTokenError') {
        errorMessage = '无效的令牌';
      }

      return { isValid: false, error: errorMessage };
    }
  }

  /**
   * 获取令牌信息（不验证签名）
   */
  getTokenInfo(token: string): {
    header?: any;
    payload?: any;
    isExpired?: boolean;
  } {
    try {
      const decoded = this.jwtService.decode(token, { complete: true }) as any;
      
      if (!decoded) {
        return {};
      }

      const now = Math.floor(Date.now() / 1000);
      const isExpired = decoded.payload.exp ? decoded.payload.exp < now : false;

      return {
        header: decoded.header,
        payload: decoded.payload,
        isExpired,
      };
    } catch (error) {
      return {};
    }
  }

  /**
   * 清理过期的黑名单令牌（定时任务使用）
   */
  async cleanupExpiredBlacklistTokens(): Promise<void> {
    try {
      const pattern = 'token_blacklist:*';
      const keys = await this.redisService.keys(pattern);
      
      if (keys.length > 0) {
        // Redis会自动删除过期的key，这里只是记录日志
        this.logger.log(`黑名单令牌清理检查完成，共${keys.length}个条目`);
      }
    } catch (error) {
      this.logger.error(`清理过期黑名单令牌失败: ${error.message}`, error.stack);
    }
  }
}
