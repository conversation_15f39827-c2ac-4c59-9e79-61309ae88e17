import { Processor, Process, OnQueueActive, OnQueueCompleted, OnQueueFailed } from '@nestjs/bull';
import { Job } from 'bull';
import { StructuredLogger } from '../../../common/logging/structured-logger';
import { TaskService } from '../services/task.service';
import { TaskStatus } from '../interfaces/task-status.enum';
import { GeoJobData, GeoResult } from '../interfaces/queue-job.interface';

/**
 * 地理坐标处理器
 * 处理地址转坐标和坐标转地址任务
 */
@Processor('rev-geo')
export class GeoProcessor {
  constructor(
    private readonly taskService: TaskService,
    private readonly logger: StructuredLogger,
  ) {}

  /**
   * 处理地理坐标任务
   */
  @Process()
  async processGeoTask(job: Job<GeoJobData>): Promise<GeoResult> {
    const { taskId, data, operation, options } = job.data;
    
    try {
      // 更新任务状态为处理中
      await this.taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING, {
        progress: 0,
        message: '开始处理地理坐标任务',
      });
      
      let result: GeoResult;
      
      // 根据操作类型执行不同的处理逻辑
      if (operation === 'geocode') {
        // 地址转坐标
        if (typeof data !== 'string') {
          throw new Error('地址转坐标操作需要提供地址文本');
        }
        
        // 更新进度
        await this.taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING, {
          progress: 0.5,
          message: '地址转坐标处理中...',
        });
        
        // 模拟处理过程
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 地址转坐标处理逻辑（这里是模拟实现）
        result = await this.geocode(data, options);
      } else if (operation === 'reverse_geocode') {
        // 坐标转地址
        if (typeof data === 'string') {
          throw new Error('坐标转地址操作需要提供坐标数据');
        }
        
        // 更新进度
        await this.taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING, {
          progress: 0.5,
          message: '坐标转地址处理中...',
        });
        
        // 模拟处理过程
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 坐标转地址处理逻辑（这里是模拟实现）
        result = await this.reverseGeocode(data, options);
      } else {
        throw new Error(`不支持的操作类型: ${operation}`);
      }
      
      // 更新任务状态为完成
      await this.taskService.updateTaskStatus(taskId, TaskStatus.COMPLETED, {
        result,
        message: '处理完成',
        progress: 1,
      });
      
      this.logger.log(
        `地理坐标任务${taskId}完成，操作类型: ${operation}`,
        { 
          module: 'GeoProcessor',
          metadata: { confidence: result.confidence }
        }
      );
      
      return result;
    } catch (error) {
      // 更新任务状态为失败
      await this.taskService.updateTaskStatus(taskId, TaskStatus.FAILED, {
        error: error.message,
        message: '处理失败',
      });
      
      this.logger.error(
        `地理坐标任务${taskId}失败，操作类型: ${operation}`,
        error,
        { module: 'GeoProcessor' }
      );
      
      throw error;
    }
  }
  
  /**
   * 任务开始处理时触发
   */
  @OnQueueActive()
  async onActive(job: Job): Promise<void> {
    const { taskId } = job.data;
    await this.taskService.updateTaskStatus(taskId, TaskStatus.ACTIVE, {
      message: '任务开始处理',
    });
  }
  
  /**
   * 任务完成时触发
   */
  @OnQueueCompleted()
  async onCompleted(job: Job, result: GeoResult): Promise<void> {
    const { taskId, operation } = job.data;
    this.logger.debug(
      `地理坐标任务${taskId}完成，操作类型: ${operation}`,
      { module: 'GeoProcessor', metadata: { result } }
    );
  }
  
  /**
   * 任务失败时触发
   */
  @OnQueueFailed()
  async onFailed(job: Job, error: Error): Promise<void> {
    const { taskId, operation } = job.data;
    this.logger.error(
      `地理坐标任务${taskId}失败，操作类型: ${operation}`,
      error,
      { module: 'GeoProcessor' }
    );
  }
  
  /**
   * 地址转坐标实现
   * 实际项目中应调用真实的地图API
   */
  private async geocode(address: string, options?: any): Promise<GeoResult> {
    // 模拟地址转坐标处理
    // 根据地址特征生成模拟坐标
    let latitude = 39.9;
    let longitude = 116.3;
    
    // 简单模拟不同地址对应不同坐标
    if (address.includes('北京')) {
      latitude = 39.908823;
      longitude = 116.397470;
      
      if (address.includes('海淀')) {
        latitude += 0.02;
        longitude -= 0.01;
      } else if (address.includes('朝阳')) {
        latitude += 0.01;
        longitude += 0.02;
      }
    } else if (address.includes('上海')) {
      latitude = 31.230416;
      longitude = 121.473701;
    } else if (address.includes('广州')) {
      latitude = 23.129110;
      longitude = 113.264385;
    }
    
    return {
      location: {
        latitude,
        longitude,
      },
      confidence: 0.9,
    };
  }
  
  /**
   * 坐标转地址实现
   * 实际项目中应调用真实的地图API
   */
  private async reverseGeocode(
    location: { latitude: number; longitude: number },
    options?: any,
  ): Promise<GeoResult> {
    const { latitude, longitude } = location;
    
    // 模拟坐标转地址处理
    let province = '';
    let city = '';
    let district = '';
    let street = '';
    let adcode = '';
    
    // 简单模拟不同坐标范围对应不同地址
    if (latitude > 39 && latitude < 40 && longitude > 116 && longitude < 117) {
      province = '北京市';
      city = '北京市';
      
      if (latitude > 39.9) {
        district = '海淀区';
        street = '中关村大街';
        adcode = '110108';
      } else {
        district = '朝阳区';
        street = '建国路';
        adcode = '110105';
      }
    } else if (latitude > 31 && latitude < 32 && longitude > 121 && longitude < 122) {
      province = '上海市';
      city = '上海市';
      district = '浦东新区';
      street = '世纪大道';
      adcode = '310115';
    } else {
      province = '广东省';
      city = '广州市';
      district = '天河区';
      street = '天河路';
      adcode = '440106';
    }
    
    const formatted = `${province}${city}${district}${street}`;
    
    return {
      address: {
        formatted,
        province,
        city,
        district,
        street,
        streetNumber: '1号',
        adcode,
      },
      confidence: 0.85,
    };
  }
} 