/**
 * 队列管理器接口
 * 定义队列管理器的标准接口，便于依赖注入和测试
 */
export interface IQueueManager {
  /**
   * 添加任务到队列
   * @param queueName 队列名称
   * @param taskType 任务类型
   * @param data 任务数据
   * @param options 任务选项
   * @returns 任务对象
   */
  addJob<T = any>(
    queueName: string,
    taskType: string,
    data: T,
    options?: {
      priority?: number;
      delay?: number;
      attempts?: number;
      timeout?: number;
      removeOnComplete?: boolean | number;
      removeOnFail?: boolean | number;
      jobId?: string;
    }
  ): Promise<{ id: string }>;

  /**
   * 获取任务状态
   * @param queueName 队列名称
   * @param jobId 任务ID
   * @returns 任务状态
   */
  getJobStatus(queueName: string, jobId: string): Promise<{
    id: string;
    state: string;
    progress: number;
    result?: any;
    failedReason?: string;
    data?: any;
    timestamp: {
      created: number;
      processed?: number;
      finished?: number;
    };
  }>;

  /**
   * 检查任务是否存在
   * @param queueName 队列名称
   * @param jobId 任务ID
   * @returns 任务是否存在
   */
  jobExists(queueName: string, jobId: string): Promise<boolean>;

  /**
   * 移除任务
   * @param queueName 队列名称
   * @param jobId 任务ID
   * @returns 是否成功移除
   */
  removeJob(queueName: string, jobId: string): Promise<boolean>;

  /**
   * 获取所有队列名称
   * @returns 队列名称列表
   */
  getQueueNames(): string[];

  /**
   * 获取队列状态
   * @param queueName 队列名称
   * @returns 队列状态
   */
  getQueueStatus(queueName: string): Promise<{
    name: string;
    counts: {
      waiting: number;
      active: number;
      completed: number;
      failed: number;
      delayed?: number;
      paused?: number;
    };
    isPaused: boolean;
    workerCount: number;
  }>;

  /**
   * 暂停队列
   * @param queueName 队列名称
   * @returns 是否成功暂停
   */
  pauseQueue(queueName: string): Promise<boolean>;

  /**
   * 恢复队列
   * @param queueName 队列名称
   * @returns 是否成功恢复
   */
  resumeQueue(queueName: string): Promise<boolean>;
} 