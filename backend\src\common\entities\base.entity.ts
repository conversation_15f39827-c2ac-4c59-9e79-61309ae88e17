import {
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  Column,
  BeforeInsert,
} from 'typeorm';

/**
 * 基础实体类
 * 包含所有实体的公共字段
 */
export abstract class BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  createdAt: Date;
  @BeforeInsert()
  createDate() {
    // 更新entity前更新LastUpdatedDate
    this.createdAt = new Date();
  }

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  updatedAt: Date;

  @DeleteDateColumn({
    type: 'timestamp',
    nullable: true,
    comment: '删除时间',
  })
  deletedAt?: Date;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '创建者',
  })
  createdBy?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '更新者',
  })
  updatedBy?: string;

  @Column({
    type: 'tinyint',
    default: 1,
    comment: '状态：0-禁用，1-启用',
  })
  baseStatus: number;

  //0代表存在 1代表删除
  @Column({
    type: 'char',
    name: 'del_flag',
    default: '0',
    length: 1,
    comment: '删除标志',
  })
  public delFlag?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: '备注',
  })
  remark?: string;
}
