<template>
  <div class="history-panel">
    <div class="panel-header">
      <h3>请求历史</h3>
      <div class="header-actions">
        <el-button size="small" @click="$emit('clear-history')" :disabled="!hasHistory">
          <el-icon><Delete /></el-icon>
          清空
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="panel-filters" v-if="hasHistory">
      <el-input
        v-model="searchQuery"
        placeholder="搜索请求历史..."
        size="small"
        clearable
        class="search-input"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      
      <el-select
        v-model="statusFilter"
        placeholder="状态筛选"
        size="small"
        clearable
        class="status-filter"
      >
        <el-option label="全部" value="" />
        <el-option label="成功 (2xx)" value="success" />
        <el-option label="重定向 (3xx)" value="redirect" />
        <el-option label="客户端错误 (4xx)" value="client-error" />
        <el-option label="服务器错误 (5xx)" value="server-error" />
      </el-select>
    </div>

    <!-- 历史记录列表 -->
    <div class="history-list" v-if="hasHistory">
      <div
        v-for="(record, index) in filteredHistory"
        :key="record.id"
        class="history-item"
        :class="{ active: selectedRecord?.id === record.id }"
        @click="selectRecord(record)"
      >
        <div class="item-header">
          <div class="method-url">
            <el-tag
              :type="getMethodType(record.config.method)"
              size="small"
              class="method-tag"
            >
              {{ record.config.method }}
            </el-tag>
            <span class="url" :title="record.config.url">{{ getShortUrl(record.config.url) }}</span>
          </div>
          
          <div class="item-actions">
            <el-button
              size="small"
              text
              @click.stop="$emit('load-request', record)"
              title="加载到编辑器"
            >
              <el-icon><Upload /></el-icon>
            </el-button>
            <el-button
              size="small"
              text
              @click.stop="deleteRecord(index)"
              title="删除记录"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>
        
        <div class="item-meta">
          <div class="status-time">
            <el-tag
              :type="getStatusType(record.response?.status)"
              size="small"
              class="status-tag"
            >
              {{ record.response?.status || 'Error' }}
            </el-tag>
            <span class="duration">{{ record.response?.duration || 0 }}ms</span>
            <span class="timestamp">{{ formatTime(new Date(record.timestamp).getTime()) }}</span>
          </div>
        </div>
        
        <div class="item-preview" v-if="record.response?.data">
          <div class="preview-content">
            {{ getResponsePreview(record.response.data) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-empty description="暂无请求历史">
        <template #image>
          <el-icon size="60" color="#c0c4cc">
            <Clock />
          </el-icon>
        </template>
        <template #description>
          <p>发送请求后，历史记录将在这里显示</p>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Delete,
  Search,
  Upload,
  Clock
} from '@element-plus/icons-vue'
import type { RequestHistory } from '@/stores/playground'

interface Props {
  history: RequestHistory[]
  selectedRecord?: RequestHistory
}

const props = withDefaults(defineProps<Props>(), {
  history: () => [],
  selectedRecord: undefined
})

const emit = defineEmits<{
  'select-record': [record: RequestHistory]
  'load-request': [record: RequestHistory]
  'delete-record': [index: number]
  'clear-history': []
}>()

const searchQuery = ref('')
const statusFilter = ref('')

// 计算属性
const hasHistory = computed(() => props.history.length > 0)

const filteredHistory = computed(() => {
  let filtered = props.history

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(record => 
      record.config.url.toLowerCase().includes(query) ||
      record.config.method.toLowerCase().includes(query) ||
      (record.response?.status?.toString() || '').includes(query)
    )
  }

  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(record => {
      const status = record.response?.status
      if (!status) return statusFilter.value === 'server-error'
      
      switch (statusFilter.value) {
        case 'success':
          return status >= 200 && status < 300
        case 'redirect':
          return status >= 300 && status < 400
        case 'client-error':
          return status >= 400 && status < 500
        case 'server-error':
          return status >= 500
        default:
          return true
      }
    })
  }

  return filtered
})

// 方法
const selectRecord = (record: RequestHistory) => {
  emit('select-record', record)
}

const deleteRecord = async (index: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条请求记录吗？',
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    emit('delete-record', index)
    ElMessage.success('记录已删除')
  } catch {
    // 用户取消删除
  }
}

const getMethodType = (method: string) => {
  switch (method.toUpperCase()) {
    case 'GET':
      return 'success'
    case 'POST':
      return 'primary'
    case 'PUT':
      return 'warning'
    case 'DELETE':
      return 'danger'
    case 'PATCH':
      return 'info'
    default:
      return ''
  }
}

const getStatusType = (status?: number) => {
  if (!status) return 'danger'
  if (status >= 200 && status < 300) return 'success'
  if (status >= 300 && status < 400) return 'warning'
  if (status >= 400) return 'danger'
  return 'info'
}

const getShortUrl = (url: string) => {
  try {
    const urlObj = new URL(url)
    const path = urlObj.pathname + urlObj.search
    return path.length > 50 ? path.substring(0, 47) + '...' : path
  } catch {
    return url.length > 50 ? url.substring(0, 47) + '...' : url
  }
}

const formatTime = (timestamp: number) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }
  
  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000))
    return `${minutes}分钟前`
  }
  
  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return `${hours}小时前`
  }
  
  // 超过1天，显示具体时间
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getResponsePreview = (data: any) => {
  try {
    let preview = ''
    
    if (typeof data === 'string') {
      preview = data
    } else if (typeof data === 'object') {
      preview = JSON.stringify(data)
    } else {
      preview = String(data)
    }
    
    // 限制预览长度
    return preview.length > 100 ? preview.substring(0, 97) + '...' : preview
  } catch {
    return '无法预览响应内容'
  }
}
</script>

<style scoped>
.history-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f2f5;
  background: #fafbfc;
  flex-shrink: 0;
}

.panel-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.panel-filters {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f2f5;
  background: #fafbfc;
  flex-shrink: 0;
}

.search-input {
  margin-bottom: 12px;
}

.status-filter {
  width: 100%;
}

.history-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.history-item {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f2f5;
  cursor: pointer;
  transition: all 0.2s ease;
}

.history-item:hover {
  background: #f8f9fa;
}

.history-item.active {
  background: #e6f7ff;
  border-left: 4px solid #1890ff;
}

.history-item:last-child {
  border-bottom: none;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.method-url {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.method-tag {
  flex-shrink: 0;
  font-weight: 600;
}

.url {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.history-item:hover .item-actions {
  opacity: 1;
}

.item-meta {
  margin-bottom: 8px;
}

.status-time {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.status-tag {
  font-weight: 600;
}

.duration {
  color: #67c23a;
  font-weight: 500;
}

.timestamp {
  margin-left: auto;
}

.item-preview {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px 12px;
  margin-top: 8px;
}

.preview-content {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #6c757d;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .panel-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
  }
  
  .panel-filters {
    padding: 12px 16px;
  }
  
  .history-item {
    padding: 12px 16px;
  }
  
  .item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .method-url {
    width: 100%;
  }
  
  .item-actions {
    opacity: 1;
    align-self: flex-end;
  }
  
  .status-time {
    flex-wrap: wrap;
    gap: 4px;
  }
}

/* 滚动条样式 */
.history-list::-webkit-scrollbar {
  width: 6px;
}

.history-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.history-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>