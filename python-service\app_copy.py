#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地址提取Python NLP服务 - 原始版本 + 兜底功能
使用cpca、jieba分词、正则表达式和地址解析库进行智能地址提取
集成PaddleOCR提供物流面单OCR服务
"""

import re
import json
import logging
import os
import sys
import time
import datetime
from typing import List, Dict, Optional, Tuple

# 设置时区
os.environ['TZ'] = 'Asia/Shanghai'
try:
    time.tzset()  # 这个函数只在Unix系统上可用
    logging.info("时区设置为: Asia/Shanghai")
except AttributeError:
    logging.warning("time.tzset()在当前系统不可用，时区可能未正确设置")

# 配置日志
if not os.path.exists('/app/logs'):
    os.makedirs('/app/logs', exist_ok=True)

# 自定义日志格式化器，添加Asia/Shanghai时区
class TimeZoneFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        ct = datetime.datetime.fromtimestamp(record.created, datetime.timezone(datetime.timedelta(hours=8)))
        if datefmt:
            s = ct.strftime(datefmt)
        else:
            s = ct.strftime("%Y-%m-%d %H:%M:%S.%f")
        return s

# 配置日志
file_handler = logging.FileHandler('/app/logs/app.log')
stream_handler = logging.StreamHandler()

formatter = TimeZoneFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
stream_handler.setFormatter(formatter)

logging.basicConfig(
    level=logging.INFO,
    handlers=[file_handler, stream_handler]
)
logger = logging.getLogger(__name__)

# 其他导入
import jieba
import jieba.posseg as pseg
import cpca
from flask import Flask, request, jsonify
from flask_cors import CORS
import pandas as pd
import gc

# Geographic libraries for coordinate reverse geocoding
from geopy.geocoders import Nominatim
from geopy.distance import geodesic
from geopy.exc import GeocoderTimedOut, GeocoderServiceError
import geojson
from shapely.geometry import Point, Polygon
import requests

# 导入地图解析工具
try:
    from map_parsers import MockLocation
    logger.info("成功导入地图解析工具")
except ImportError as e:
    logger.warning(f"导入地图解析工具失败: {str(e)}，将使用默认解析逻辑")
    MockLocation = None

# PaddleOCR 相关导入
import base64, io
import numpy as np

# 添加OpenCV导入的错误处理
try:
    import cv2
    logger.info(f"成功导入OpenCV，版本: {cv2.__version__}")
except ImportError as e:
    logger.error(f"导入OpenCV失败: {str(e)}")
    cv2 = None

# 尝试导入PaddleOCR
try:
    from paddleocr import PaddleOCR
    logger.info("成功导入PaddleOCR")
except Exception as e:
    logger.critical(f"导入PaddleOCR失败: {str(e)}")
    PaddleOCR = None

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 设置较大的超时时间
timeout = int(os.environ.get('TIMEOUT', 300))
app.config['TIMEOUT'] = timeout

# OCR处理超时时间
OCR_TIMEOUT = int(os.environ.get('OCR_TIMEOUT', 180))
logger.info(f"OCR处理超时设置为: {OCR_TIMEOUT}秒")

# 检查环境变量，决定是否下载模型
DISABLE_PADDLE_DOWNLOAD = os.environ.get("DISABLE_PADDLE_DOWNLOAD", "false").lower() == "true"
logger.info(f"模型下载状态: {'禁用' if DISABLE_PADDLE_DOWNLOAD else '启用'}")

# 初始化PaddleOCR
try:
    logger.info("开始初始化PaddleOCR...")
    ocr = PaddleOCR(
        use_textline_orientation=True, 
        lang="ch", 
        download_models=not DISABLE_PADDLE_DOWNLOAD,
        use_gpu=False,
        enable_mkldnn=True,
        cpu_threads=2,
        det_db_thresh=0.3,
        det_db_box_thresh=0.5,
        rec_batch_num=1,
        det_limit_side_len=480
    )
    logger.info("PaddleOCR初始化成功")
except Exception as e:
    logger.error(f"PaddleOCR初始化失败: {e}")
    ocr = None

# 图像处理相关的配置
IMAGE_MAX_SIZE = 640
IMAGE_COMPRESS_QUALITY = 80
ENABLE_IMAGE_ENHANCEMENT = True

# 健康检查状态
HEALTH_STATUS = {
    'status': 'starting',
    'last_check': time.time(),
    'ocr_initialized': ocr is not None,
    'startup_time': time.time()
}

# 全局标志，指示OCR处理是否正在进行
OCR_PROCESSING = False

# 创建增强的地理编码器类
class EnhancedFallbackNominatim:
    """增强版带兜底功能的地理编码器"""

    def __init__(self, user_agent='EnhancedGeoService/2.0', timeout=15):
        self.user_agent = user_agent
        self.timeout = timeout

        # 本地Nominatim服务（优先级最高）
        self.local_nominatim = None
        try:
            local_url = os.getenv('NOMINATIM_LOCAL_URL', 'http://localhost:8080')
            if local_url and local_url != 'http://localhost:8080':
                domain = local_url.replace('http://', '').replace('https://', '')
                self.local_nominatim = Nominatim(
                    user_agent=user_agent,
                    timeout=timeout,
                    domain=domain
                )
                logger.info(f"本地Nominatim服务初始化成功: {domain}")
        except Exception as e:
            logger.warning(f"本地Nominatim服务初始化失败: {e}")

        # 主服务（国际）
        self.primary_nominatim = Nominatim(
            user_agent=user_agent,
            timeout=timeout
        )

        # 国内镜像服务
        self.china_mirrors = []
        china_domains = [
            'nominatim.openstreetmap.org.cn',
            'nominatim.openstreetmap.com.cn'
        ]

        for domain in china_domains:
            try:
                mirror = Nominatim(
                    user_agent=user_agent,
                    timeout=timeout,
                    domain=domain
                )
                self.china_mirrors.append(mirror)
                logger.info(f"国内镜像服务初始化成功: {domain}")
            except Exception as e:
                logger.warning(f"国内镜像服务初始化失败 {domain}: {e}")

        # 统计信息
        self.stats = {
            'total_requests': 0,
            'local_success': 0,
            'primary_success': 0,
            'mirror_success': 0,
            'api_success': 0,
            'failures': 0
        }
    
    def _get_api_keys(self):
        """获取API密钥配置"""
        return {
            'amap': os.getenv('AMAP_KEY', '941c8903ad31b2acf1b20118d2a54bb1'),
            'tencent': os.getenv('TENCENT_KEY', 'GT7BZ-QV2RD-KH74P-HKCNM-5AF76-3VBHL'),
            'baidu': os.getenv('BAIDU_KEY', 'V4GVHuRD0T0TImlunB7UoehFiFY4Rbme')
        }

    def _call_amap_api(self, coordinates, operation='reverse'):
        """调用高德地图API"""
        api_keys = self._get_api_keys()
        if not api_keys['amap']:
            return None

        try:
            if operation == 'reverse':
                url = "https://restapi.amap.com/v3/geocode/regeo"
                params = {
                    'key': api_keys['amap'],
                    'location': f"{coordinates[1]},{coordinates[0]}",
                    'output': 'json',
                    'radius': 1000,
                    'extensions': 'all'
                }
            else:  # forward
                url = "https://restapi.amap.com/v3/geocode/geo"
                params = {
                    'key': api_keys['amap'],
                    'address': coordinates,  # 对于正向编码，coordinates实际是地址
                    'output': 'json'
                }

            response = requests.get(url, params=params, timeout=self.timeout, headers={
                'User-Agent': self.user_agent
            })

            if response.status_code == 200:
                data = response.json()
                if data.get('status') == '1':
                    self.stats['api_success'] += 1
                    return self._parse_amap_response(data, operation)

        except Exception as e:
            logger.warning(f"高德地图API调用失败: {e}")

        return None

    def _parse_amap_response(self, data, operation):
        """解析高德地图API响应"""
        class MockLocation:
            def __init__(self, address, raw_data):
                self.address = address
                self.raw = raw_data

        if operation == 'reverse':
            regeocode = data.get('regeocode', {})
            formatted_address = regeocode.get('formatted_address', '')
            address_component = regeocode.get('addressComponent', {})

            return MockLocation(formatted_address, {
                'address': {
                    'country': address_component.get('country', ''),
                    'state': address_component.get('province', ''),
                    'city': address_component.get('city', ''),
                    'district': address_component.get('district', ''),
                    'road': address_component.get('township', ''),
                    'postcode': address_component.get('adcode', '')
                }
            })
        else:  # forward
            geocodes = data.get('geocodes', [])
            if geocodes:
                geocode = geocodes[0]
                location = geocode.get('location', '').split(',')
                if len(location) == 2:
                    # 为正向编码创建特殊的MockLocation
                    mock_loc = MockLocation(geocode.get('formatted_address', ''), {})
                    mock_loc.latitude = float(location[1])
                    mock_loc.longitude = float(location[0])
                    return mock_loc

        return None

    def _direct_http_request(self, coordinates, language='zh-CN'):
        """使用直接HTTP请求，优先使用API密钥"""
        # 首先尝试高德地图API
        amap_result = self._call_amap_api(coordinates, 'reverse')
        if amap_result:
            logger.info("高德地图API调用成功")
            return amap_result

        # 备用HTTP请求（无密钥）
        urls = [
            # 兜底使用国际源
            f"http://nominatim.openstreetmap.org/reverse?format=json&lat={coordinates[0]}&lon={coordinates[1]}&accept-language={language}"
        ]

        for url in urls:
            try:
                logger.info(f"尝试直接HTTP请求: {url}")
                response = requests.get(url, timeout=self.timeout, headers={
                    'User-Agent': self.user_agent
                })

                if response.status_code == 200:
                    try:
                        data = response.json()

                        # 手动构建类似geopy的结果对象
                        class MockLocation:
                            def __init__(self, json_data):
                                self.raw = json_data
                                self.address = json_data.get('display_name', '')

                                # 处理地址组件
                                address = {}
                                if 'address' in json_data:
                                    address = json_data['address']
                                else:
                                    logger.warning("返回数据中没有address字段")

                                # 格式化为与geopy兼容的结构
                                self.raw['address'] = address

                        return MockLocation(data)
                    except ValueError as e:
                        logger.warning(f"JSON解析失败: {e}")
                        continue
                else:
                    logger.warning(f"HTTP请求失败，状态码: {response.status_code}")
            except Exception as e:
                logger.warning(f"HTTP请求异常: {e}")

        logger.error("所有HTTP请求均失败")
        return None
    
    def _https_with_verify_disabled(self, coordinates, language='zh-CN'):
        """使用HTTPS但禁用SSL证书验证"""
        # 关闭SSL警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        urls = [
            # 中国镜像1（高德地图接口，国内访问较快）
            f"https://restapi.amap.com/v3/geocode/regeo?key=31b1efa5db6b22db18cad8611f0d2f6b&location={coordinates[1]},{coordinates[0]}&output=json",
            # 中国镜像2（腾讯地图接口，国内访问稳定）
            f"https://apis.map.qq.com/ws/geocoder/v1/?location={coordinates[0]},{coordinates[1]}&key=JGVBZ-JBTKP-FMVDW-V5KCD-G5KHH-4FBH5&output=json",
            # 国内镜像3（百度地图接口，国内访问稳定）
            f"https://api.map.baidu.com/reverse_geocoding/v3/?ak=xNGM5DlVQWtPyZZFu5kLhlfGEONGNbIl&output=json&coordtype=wgs84ll&location={coordinates[0]},{coordinates[1]}",
            # 兜底使用国际源
            f"https://nominatim.openstreetmap.org/reverse?format=json&lat={coordinates[0]}&lon={coordinates[1]}&accept-language={language}"
        ]
        
        for url in urls:
            try:
                logger.info(f"尝试HTTPS请求(禁用证书验证): {url}")
                response = requests.get(url, timeout=self.timeout, headers={
                    'User-Agent': self.user_agent
                }, verify=False)  # 禁用SSL证书验证
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        
                        # 手动构建类似geopy的结果对象
                        class MockLocation:
                            def __init__(self, json_data):
                                self.raw = json_data
                                self.address = json_data.get('display_name', '')
                                
                                # 处理地址组件
                                address = {}
                                if 'address' in json_data:
                                    address = json_data['address']
                                else:
                                    logger.warning("返回数据中没有address字段")
                                    
                                # 格式化为与geopy兼容的结构
                                self.raw['address'] = address
                        
                        return MockLocation(data)
                    except ValueError as e:
                        logger.warning(f"JSON解析失败: {e}")
                        continue
                else:
                    logger.warning(f"HTTPS请求失败，状态码: {response.status_code}")
            except Exception as e:
                logger.warning(f"HTTPS请求异常: {e}, URL: {url}")
        
        logger.error("所有HTTPS请求均失败")
        return None
    
    def reverse(self, coordinates, language='zh-CN'):
        """增强版反向地理编码，支持多级兜底"""
        self.stats['total_requests'] += 1

        # 1. 优先尝试本地Nominatim服务
        if self.local_nominatim:
            try:
                logger.info("尝试使用本地Nominatim服务...")
                result = self.local_nominatim.reverse(coordinates, language=language)
                if result:
                    self.stats['local_success'] += 1
                    logger.info("本地Nominatim服务调用成功")
                    return result
            except Exception as e:
                logger.warning(f"本地Nominatim服务失败: {e}")

        # 2. 尝试国内镜像服务
        for i, mirror in enumerate(self.china_mirrors):
            try:
                logger.info(f"尝试使用国内镜像服务 {i+1}...")
                result = mirror.reverse(coordinates, language=language)
                if result:
                    self.stats['mirror_success'] += 1
                    logger.info(f"国内镜像服务 {i+1} 调用成功")
                    return result
            except Exception as e:
                logger.warning(f"国内镜像服务 {i+1} 失败: {e}")

        # 3. 尝试国际主服务
        try:
            logger.info("尝试使用国际主服务...")
            result = self.primary_nominatim.reverse(coordinates, language=language)
            if result:
                self.stats['primary_success'] += 1
                logger.info("国际主服务调用成功")
                return result
        except Exception as e:
            logger.warning(f"国际主服务失败: {e}")

        # 4. 尝试禁用SSL验证的HTTPS请求
        try:
            logger.info("尝试禁用SSL验证的HTTPS请求...")
            result = self._https_with_verify_disabled(coordinates, language)
            if result:
                logger.info("禁用SSL验证的HTTPS请求成功")
                return result
        except Exception as e:
            logger.warning(f"禁用SSL验证的HTTPS请求失败: {e}")

        # 5. 最后尝试HTTP请求（包含API调用）
        try:
            logger.info("尝试HTTP请求（包含API调用）...")
            result = self._direct_http_request(coordinates, language)
            if result:
                logger.info("HTTP请求调用成功")
                return result
        except Exception as e:
            logger.warning(f"HTTP请求失败: {e}")

        # 所有方法都失败
        self.stats['failures'] += 1
        logger.error("所有地理编码服务尝试均失败")
        raise GeocoderServiceError("所有地理编码服务均不可用")

    def forward(self, address, language='zh-CN'):
        """增强版正向地理编码"""
        self.stats['total_requests'] += 1

        # 1. 优先尝试本地Nominatim服务
        if self.local_nominatim:
            try:
                logger.info("尝试使用本地Nominatim服务进行正向编码...")
                result = self.local_nominatim.geocode(address, language=language)
                if result:
                    self.stats['local_success'] += 1
                    return result
            except Exception as e:
                logger.warning(f"本地Nominatim正向编码失败: {e}")

        # 2. 尝试高德地图API
        try:
            logger.info("尝试使用高德地图API进行正向编码...")
            result = self._call_amap_api(address, 'forward')
            if result:
                logger.info("高德地图API正向编码成功")
                return result
        except Exception as e:
            logger.warning(f"高德地图API正向编码失败: {e}")

        # 3. 尝试国内镜像服务
        for i, mirror in enumerate(self.china_mirrors):
            try:
                logger.info(f"尝试使用国内镜像服务 {i+1} 进行正向编码...")
                result = mirror.geocode(address, language=language)
                if result:
                    self.stats['mirror_success'] += 1
                    return result
            except Exception as e:
                logger.warning(f"国内镜像服务 {i+1} 正向编码失败: {e}")

        # 4. 尝试国际主服务
        try:
            logger.info("尝试使用国际主服务进行正向编码...")
            result = self.primary_nominatim.geocode(address, language=language)
            if result:
                self.stats['primary_success'] += 1
                return result
        except Exception as e:
            logger.warning(f"国际主服务正向编码失败: {e}")

        # 所有方法都失败
        self.stats['failures'] += 1
        logger.error("所有正向地理编码服务尝试均失败")
        raise GeocoderServiceError("所有正向地理编码服务均不可用")

    def get_stats(self):
        """获取服务统计信息"""
        total = self.stats['total_requests']
        if total == 0:
            return self.stats

        return {
            **self.stats,
            'success_rate': f"{((total - self.stats['failures']) / total * 100):.1f}%",
            'local_rate': f"{(self.stats['local_success'] / total * 100):.1f}%",
            'primary_rate': f"{(self.stats['primary_success'] / total * 100):.1f}%",
            'mirror_rate': f"{(self.stats['mirror_success'] / total * 100):.1f}%",
            'api_rate': f"{(self.stats['api_success'] / total * 100):.1f}%"
        }

# 配置增强版地理编码器
geolocator = EnhancedFallbackNominatim(
    user_agent="enhanced_open_platform_geocoder",
    timeout=30
)

# 全局变量定义
provinces = [
    '北京市', '天津市', '上海市', '重庆市',
    '河北省', '山西省', '辽宁省', '吉林省', '黑龙江省',
    '江苏省', '浙江省', '安徽省', '福建省', '江西省',
    '山东省', '河南省', '湖北省', '湖南省', '广东省',
    '海南省', '四川省', '贵州省', '云南省', '陕西省',
    '甘肃省', '青海省', '台湾省',
    '内蒙古自治区', '广西壮族自治区', '西藏自治区',
    '宁夏回族自治区', '新疆维吾尔自治区',
    '香港特别行政区', '澳门特别行政区'
]

address_keywords = [
    '省', '市', '区', '县', '街道', '路', '街', '巷', '胡同', '弄', '里',
    '号', '栋', '楼', '层', '室', '座', '幢', '单元', '门牌', '小区',
    '花园', '广场', '大厦', '中心', '大楼', '商城', '商场', '写字楼',
    '工业园', '科技园', '开发区', '新区', '高新区', '经济区'
]

special_places = [
    '生产建设兵团', '公园', '博物馆', '博物院', '图书馆', '体育馆',
    '医院', '学校', '大学', '学院', '中学', '小学', '幼儿园'
]

class AddressExtractor:
    def __init__(self):
        """初始化地址提取器"""
        # 加载自定义词典
        self._load_custom_dict()
        
        # 省份映射
        self.provinces = {
            '北京': '北京市', '天津': '天津市', '上海': '上海市', '重庆': '重庆市',
            '河北': '河北省', '山西': '山西省', '辽宁': '辽宁省', '吉林': '吉林省',
            '黑龙江': '黑龙江省', '江苏': '江苏省', '浙江': '浙江省', '安徽': '安徽省',
            '福建': '福建省', '江西': '江西省', '山东': '山东省', '河南': '河南省',
            '湖北': '湖北省', '湖南': '湖南省', '广东': '广东省', '海南': '海南省',
            '四川': '四川省', '贵州': '贵州省', '云南': '云南省', '陕西': '陕西省',
            '甘肃': '甘肃省', '青海': '青海省', '台湾': '台湾省',
            '内蒙古': '内蒙古自治区', '广西': '广西壮族自治区', '西藏': '西藏自治区',
            '宁夏': '宁夏回族自治区', '新疆': '新疆维吾尔自治区',
            '香港': '香港特别行政区', '澳门': '澳门特别行政区'
        }
        
        # 地址关键词
        self.address_keywords = [
            '省', '市', '区', '县', '街道', '路', '街', '巷', '胡同', '弄', '里',
            '号', '栋', '楼', '层', '室', '座', '幢', '单元', '门牌', '小区',
            '花园', '广场', '大厦', '中心', '大楼', '商城', '商场', '写字楼',
            '工业园', '科技园', '开发区', '新区', '高新区', '经济区'
        ]
        
        # 特殊地名
        self.special_places = [
            '生产建设兵团', '公园', '博物馆', '博物院', '图书馆', '体育馆',
            '医院', '学校', '大学', '学院', '中学', '小学', '幼儿园'
        ]
    
    def _load_custom_dict(self):
        """加载自定义词典"""
        # 添加地址相关词汇
        address_words = [
            '建国门外大街', '陆家嘴环路', '天府大道', '解放南路', '大学东街',
            '科技园南区', '深圳湾科技生态园', '天府软件园', '内蒙古大学',
            '国贸大厦', '上海中心大厦', '生产建设兵团'
        ]
        
        for word in address_words:
            jieba.add_word(word)
    
    def extract_phone(self, text: str) -> List[str]:
        """提取手机号"""
        phone_pattern = r'1[3-9]\d{9}'
        phones = re.findall(phone_pattern, text)
        return list(set(phones))  # 去重
    
    def extract_name(self, text: str) -> List[str]:
        """提取姓名"""
        names = []
        
        # 使用jieba分词和词性标注
        words = pseg.cut(text)
        
        for word, flag in words:
            # 人名标记
            if flag == 'nr' and len(word) >= 2 and len(word) <= 4:
                if re.match(r'^[\u4e00-\u9fa5]+$', word):
                    names.append(word)
        
        # 正则表达式补充
        # 查找"收件人"、"寄件人"等关键词后的姓名
        name_patterns = [
            r'(?:收件人|寄件人|联系人)[：:：\s]*([\u4e00-\u9fa5]{2,4})(?:\s|先生|女士|$)',
            r'([\u4e00-\u9fa5]{2,4})(?:先生|女士)(?:\s|$|1[3-9]\d{9})',
            r'^([\u4e00-\u9fa5]{2,4})(?:\s|$|1[3-9]\d{9})'
        ]
        
        for pattern in name_patterns:
            matches = re.findall(pattern, text)
            names.extend(matches)
        
        return list(set(names))  # 去重
    
    def extract_address_components(self, text: str) -> Dict[str, str]:
        """提取地址组件 - 使用CPCA和jieba双重解析"""
        components = {
            'province': None,
            'city': None,
            'district': None,
            'street': None,
            'detail': None
        }
        
        try:
            # 方法1: 使用CPCA解析
            cpca_result = cpca.transform([text])
            if not cpca_result.empty:
                row = cpca_result.iloc[0]
                components['province'] = row['省'] if pd.notna(row['省']) and row['省'] else None
                components['city'] = row['市'] if pd.notna(row['市']) and row['市'] else None
                components['district'] = row['区'] if pd.notna(row['区']) and row['区'] else None
                components['detail'] = row['地址'] if pd.notna(row['地址']) and row['地址'] else None
        except Exception as e:
            logger.warning(f"CPCA解析失败: {e}")
        
        # 方法2: jieba分词作为兜底方案
        if not any([components['province'], components['city'], components['district']]):
            # 提取省份
            for short_name, full_name in self.provinces.items():
                if short_name in text or full_name in text:
                    components['province'] = full_name
                    break
            
            # 使用jieba分词
            words = list(jieba.cut(text))
            
            # 提取市、区、街道等
            for i, word in enumerate(words):
                if word.endswith('市') and len(word) > 1:
                    if not components['city']:
                        components['city'] = word
                elif word.endswith(('区', '县')) and len(word) > 1:
                    if not components['district']:
                        components['district'] = word
                elif word.endswith(('街道', '路', '街', '大街', '大道')) and len(word) > 1:
                    if not components['street']:
                        components['street'] = word
        
        # 提取街道信息（如果还没有）
        if not components['street']:
            street_pattern = r'([\u4e00-\u9fa5\d]+(?:街道|路|街|大街|大道|巷|胡同|弄))'
            street_matches = re.findall(street_pattern, text)
            if street_matches:
                components['street'] = street_matches[0]
        
        # 提取详细地址（如果还没有）
        if not components['detail']:
            detail_pattern = r'([\u4e00-\u9fa5\d\w\s]+(?:号|栋|楼|层|室|座|幢|单元)[\u4e00-\u9fa5\d\w\s]*)'
            detail_matches = re.findall(detail_pattern, text)
            if detail_matches:
                components['detail'] = detail_matches[0]
        
        return components
    
    def calculate_confidence(self, extracted_info: Dict) -> float:
        """计算置信度"""
        confidence = 0.0
        
        # 基础分数
        if extracted_info.get('name'):
            confidence += 0.2
        if extracted_info.get('phone'):
            confidence += 0.3
        if extracted_info.get('province'):
            confidence += 0.2
        if extracted_info.get('city'):
            confidence += 0.1
        if extracted_info.get('district'):
            confidence += 0.1
        if extracted_info.get('detail_address'):
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def extract_single(self, text: str) -> Dict:
        """提取单个地址信息"""
        # 提取各个组件
        phones = self.extract_phone(text)
        names = self.extract_name(text)
        address_components = self.extract_address_components(text)
        
        # 构建结果
        result = {
            'name': names[0] if names else None,
            'phone': phones[0] if phones else None,
            'province': address_components['province'],
            'city': address_components['city'],
            'district': address_components['district'],
            'street': address_components['street'],
            'detail_address': address_components['detail'],
            'extract_method': 'cpca_jieba'
        }
        
        # 构建完整地址
        address_parts = [
            result['province'],
            result['city'],
            result['district'],
            result['street'],
            result['detail_address']
        ]
        result['full_address'] = ''.join(filter(None, address_parts))
        
        # 计算置信度
        result['confidence'] = self.calculate_confidence(result)
        
        return result
    
    def extract_multiple(self, text: str) -> List[Dict]:
        """提取多个地址信息"""
        results = []
        
        # 预处理文本：清理特殊字符和多余空白
        text = re.sub(r'[Ā]', '', text)
        text = re.sub(r'\\n', '\n', text)
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'[：:]', ':', text)
        text = text.strip()
        
        # 按句号、分号等分割
        sentences = re.split(r'[。；;\n]', text)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 8:
                result = self.extract_single(sentence)
                if (result['name'] or result['phone'] or result['province'] or 
                    result['city'] or result['district']):
                    results.append(result)
        
        return results

# 创建全局提取器实例
extractor = AddressExtractor()

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    current_time = time.time()
    uptime = current_time - HEALTH_STATUS['startup_time']
    
    # 更新健康状态
    if uptime > 30 and HEALTH_STATUS['status'] == 'starting':
        HEALTH_STATUS['status'] = 'healthy'
    
    # 检查OCR是否已初始化
    if not HEALTH_STATUS['ocr_initialized'] and ocr is not None:
        HEALTH_STATUS['ocr_initialized'] = True
    
    HEALTH_STATUS['last_check'] = current_time
    
    return jsonify({
        'status': HEALTH_STATUS['status'],
        'timestamp': current_time,
        'uptime': uptime,
        'service': 'python-nlp-address-extractor',
        'ocr_initialized': HEALTH_STATUS['ocr_initialized']
    })

@app.route('/extract', methods=['POST'])
def extract_address():
    """地址提取API"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': '请提供JSON数据'
            }), 400
        
        # 获取参数
        text = data.get('text', '')
        texts = data.get('texts', [])
        
        if not text and not texts:
            return jsonify({
                'success': False,
                'error': '请提供text或texts参数'
            }), 400
        
        start_time = time.time()
        
        # 处理单个文本
        if text:
            result = extractor.extract_single(text)
            processing_time = time.time() - start_time
            
            return jsonify({
                'success': True,
                'result': result,
                'processing_time': f'{processing_time:.3f}s'
            })
        
        # 处理多个文本
        if texts:
            results = []
            for i, txt in enumerate(texts):
                try:
                    result = extractor.extract_single(txt)
                    results.append({
                        'index': i,
                        'text': txt,
                        'result': result
                    })
                except Exception as e:
                    results.append({
                        'index': i,
                        'text': txt,
                        'error': str(e)
                    })
            
            processing_time = time.time() - start_time
            
            return jsonify({
                'success': True,
                'results': results,
                'total_count': len(texts),
                'processing_time': f'{processing_time:.3f}s'
            })
            
    except Exception as e:
        logger.error(f"地址提取失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'处理失败: {str(e)}'
        }), 500

@app.route('/rev-geo', methods=['GET', 'POST'])
def reverse_geocode_coordinates():
    """地理坐标逆解析API - 增强版带兜底功能"""
    try:
        # 支持GET和POST两种方式
        if request.method == 'GET':
            latitude = request.args.get('latitude')
            longitude = request.args.get('longitude')
            coord_type = request.args.get('coordType', 'wgs84')
        else:
            data = request.get_json()

            if not data:
                return jsonify({
                    'success': False,
                    'error': '请提供JSON数据'
                }), 400

            latitude = data.get('latitude')
            longitude = data.get('longitude') or data.get('lng') or data.get('lon')
            coord_type = data.get('coordType', 'wgs84')

        if latitude is None or longitude is None:
            return jsonify({
                'success': False,
                'error': '请提供latitude和longitude参数'
            }), 400

        try:
            latitude = float(latitude)
            longitude = float(longitude)
        except (ValueError, TypeError):
            return jsonify({
                'success': False,
                'error': '坐标参数必须是有效的数字'
            }), 400

        # 验证坐标范围
        if not (-90 <= latitude <= 90) or not (-180 <= longitude <= 180):
            return jsonify({
                'success': False,
                'error': '坐标超出有效范围'
            }), 400

        start_time = time.time()

        try:
            # 使用增强版地理编码器
            location = geolocator.reverse((latitude, longitude), language='zh-CN')

            if location and location.address:
                address_parts = location.raw.get('address', {})

                result = {
                    'success': True,
                    'coordinates': {
                        'latitude': latitude,
                        'longitude': longitude,
                        'coord_type': coord_type
                    },
                    'address': {
                        'full_address': location.address,
                        'province': address_parts.get('state', ''),
                        'city': address_parts.get('city', '') or address_parts.get('town', ''),
                        'district': address_parts.get('suburb', '') or address_parts.get('district', ''),
                        'street': address_parts.get('road', '') or address_parts.get('street', ''),
                        'house_number': address_parts.get('house_number', ''),
                        'postcode': address_parts.get('postcode', ''),
                        'confidence': 0.85,
                        'resolution_method': 'enhanced_nominatim_with_fallback'
                    },
                    'processing_time': f'{time.time() - start_time:.3f}s',
                    'service_stats': geolocator.get_stats()
                }
            else:
                result = {
                    'success': False,
                    'error': '无法解析地址',
                    'coordinates': {
                        'latitude': latitude,
                        'longitude': longitude,
                        'coord_type': coord_type
                    }
                }

        except Exception as e:
            logger.error(f"地理坐标逆解析失败: {str(e)}")
            result = {
                'success': False,
                'error': f'解析失败: {str(e)}',
                'coordinates': {
                    'latitude': latitude,
                    'longitude': longitude,
                    'coord_type': coord_type
                }
            }

        return jsonify(result)

    except Exception as e:
        logger.error(f"地理坐标逆解析失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'处理失败: {str(e)}'
        }), 500

@app.route('/forward-geo', methods=['GET', 'POST'])
def forward_geocode_address():
    """正向地理编码API - 地址转坐标"""
    try:
        # 支持GET和POST两种方式
        if request.method == 'GET':
            address = request.args.get('address')
            coord_type = request.args.get('coordType', 'wgs84')
        else:
            data = request.get_json()

            if not data:
                return jsonify({
                    'success': False,
                    'error': '请提供JSON数据'
                }), 400

            address = data.get('address')
            coord_type = data.get('coordType', 'wgs84')

        if not address:
            return jsonify({
                'success': False,
                'error': '请提供address参数'
            }), 400

        start_time = time.time()

        try:
            # 使用增强版地理编码器
            location = geolocator.forward(address, language='zh-CN')

            if location and hasattr(location, 'latitude') and hasattr(location, 'longitude'):
                result = {
                    'success': True,
                    'address': address,
                    'coordinates': {
                        'latitude': location.latitude,
                        'longitude': location.longitude,
                        'coord_type': coord_type
                    },
                    'formatted_address': location.address if hasattr(location, 'address') else address,
                    'confidence': 0.85,
                    'processing_time': f'{time.time() - start_time:.3f}s',
                    'service_stats': geolocator.get_stats()
                }
            else:
                result = {
                    'success': False,
                    'error': '无法解析坐标',
                    'address': address
                }

        except Exception as e:
            logger.error(f"正向地理编码失败: {str(e)}")
            result = {
                'success': False,
                'error': f'解析失败: {str(e)}',
                'address': address
            }

        return jsonify(result)

    except Exception as e:
        logger.error(f"正向地理编码失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'处理失败: {str(e)}'
        }), 500

@app.route('/geo-stats', methods=['GET'])
def get_geo_stats():
    """获取地理编码服务统计信息"""
    try:
        stats = geolocator.get_stats()

        return jsonify({
            'success': True,
            'stats': stats,
            'service_info': {
                'version': '2.0.0',
                'features': [
                    'local_nominatim_support',
                    'china_mirror_fallback',
                    'api_integration',
                    'enhanced_error_handling',
                    'coordinate_validation',
                    'performance_statistics'
                ],
                'data_sources': [
                    'local_nominatim',
                    'china_mirrors',
                    'international_nominatim',
                    'amap_api',
                    'tencent_api',
                    'baidu_api'
                ]
            }
        })

    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'获取统计信息失败: {str(e)}'
        }), 500

@app.route('/predict/ocr_system', methods=["POST"])
def predict():
    """OCR识别接口，支持文件上传和base64图片输入"""
    try:
        # 设置全局标志，指示OCR处理开始
        global OCR_PROCESSING
        OCR_PROCESSING = True
        
        # 获取OCR参数
        max_size = request.args.get('max_size', default=IMAGE_MAX_SIZE, type=int)
        quality = request.args.get('quality', default=IMAGE_COMPRESS_QUALITY, type=int)
        enhance = request.args.get('enhance', default='true', type=str).lower() == 'true'
        
        # 记录开始时间
        start_time = time.time()
        
        # 支持文件上传和 base64
        if "images" in request.files:
            file = request.files["images"]
            img_bytes = file.read()
        elif request.json and "image_base64" in request.json:
            img_bytes = base64.b64decode(request.json["image_base64"])
        else:
            OCR_PROCESSING = False
            return jsonify({"error": "No image provided", "success": False}), 400
        
        # 获取原始图像尺寸
        original_size = len(img_bytes)
        
        # 转换为 OpenCV 格式
        try:
            nparr = np.frombuffer(img_bytes, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if img is None:
                OCR_PROCESSING = False
                return jsonify({"error": "Invalid image data", "success": False}), 400
                
        except Exception as e:
            OCR_PROCESSING = False
            return jsonify({"error": f"Image decode error: {str(e)}", "success": False}), 400
        
        # OCR 识别
        try:
            if ocr is None:
                OCR_PROCESSING = False
                return jsonify({"error": "OCR engine not initialized", "success": False}), 500
            
            # 执行OCR识别
            result = ocr.ocr(img)
            
        except Exception as e:
            OCR_PROCESSING = False
            return jsonify({"error": f"OCR processing error: {str(e)}", "success": False}), 500
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 重置OCR处理标志
        OCR_PROCESSING = False
        
        if result[0] is None:
            return jsonify({
                "results": [], 
                "success": True,
                "process_time": round(process_time, 3),
                "image_info": {
                    "original_size": original_size,
                    "processed_size": img.shape if 'img' in locals() else None
                }
            })
        
        return jsonify({
            "results": [[line[0], line[1][0], float(line[1][1])] for line in result[0]], 
            "success": True,
            "process_time": round(process_time, 3),
            "image_info": {
                "original_size": original_size,
                "processed_size": img.shape if 'img' in locals() else None
            }
        })
    except Exception as e:
        logger.error(f"OCR处理错误: {str(e)}")
        
        # 确保重置OCR处理标志
        OCR_PROCESSING = False
            
        return jsonify({"error": str(e), "success": False}), 500

if __name__ == '__main__':
    logger.info("启动Python集成服务(地址提取与OCR)...")
    
    app.run(debug=False, host='0.0.0.0', port=8866) 