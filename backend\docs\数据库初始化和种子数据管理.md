# 数据库初始化和种子数据管理

## 1. 概述

本文档描述了项目中数据库初始化和种子数据管理的完整方案，确保在不同环境（开发、测试、生产）中都能快速、安全地初始化数据库结构和完整的业务数据。

### 1.1 种子数据包含内容

- **用户数据**：管理员用户、测试用户（包含不同等级）
- **服务数据**：OCR识别、地址解析、地理坐标、AI对话等API服务
- **API密钥**：为每个用户自动生成API密钥
- **用户服务关联**：为用户分配服务使用次数（根据用户等级）
- **示例订单**：演示用的订单和订单项数据（仅开发/测试环境）

## 2. 设计原则

### 2.1 环境隔离
- **开发环境**：使用 `synchronize: true` 自动同步数据库结构
- **测试环境**：使用迁移脚本 + 种子数据
- **生产环境**：严格使用迁移脚本，禁用自动同步

### 2.2 数据安全
- 所有数据库结构变更必须通过迁移脚本
- 种子数据支持幂等性操作（可重复执行）
- 生产环境数据初始化需要确认机制

### 2.3 版本控制
- 迁移脚本按时间戳命名，确保执行顺序
- 种子数据支持版本管理和增量更新
- 支持回滚操作

## 3. 目录结构

```
backend/
├── src/
│   ├── database/
│   │   ├── migrations/           # 数据库迁移脚本
│   │   │   ├── 1704268800000-CreateUserTable.ts
│   │   │   ├── 1704268800001-CreateServiceTable.ts
│   │   │   └── 1704268800002-AddStoOcrService.ts
│   │   ├── seeds/               # 种子数据脚本
│   │   │   ├── 001-admin-user.seed.ts
│   │   │   ├── 002-default-services.seed.ts
│   │   │   ├── 003-api-keys.seed.ts
│   │   │   └── 004-system-config.seed.ts
│   │   └── factories/           # 数据工厂（测试用）
│   │       ├── user.factory.ts
│   │       ├── service.factory.ts
│   │       └── api-key.factory.ts
├── scripts/
│   ├── db-init.ts              # 数据库初始化脚本
│   ├── db-seed.ts              # 种子数据执行脚本
│   ├── db-reset.ts             # 数据库重置脚本
│   └── db-backup.ts            # 数据库备份脚本
└── docs/
    └── 数据库初始化和种子数据管理.md
```

## 4. 迁移脚本管理

### 4.1 迁移脚本命名规范
```
{timestamp}-{描述}.ts
例如：1704268800000-CreateUserTable.ts
```

### 4.2 迁移脚本模板
```typescript
import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUserTable1704268800000 implements MigrationInterface {
  name = 'CreateUserTable1704268800000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建表结构
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 回滚操作
  }
}
```

### 4.3 迁移脚本执行
```bash
# 生成迁移脚本
npm run migration:generate -- --name=CreateUserTable

# 执行迁移
npm run migration:run

# 回滚迁移
npm run migration:revert
```

## 5. 种子数据管理

### 5.1 种子数据分类
- **系统基础数据**：管理员用户、系统配置等
- **业务基础数据**：默认服务、API密钥等
- **测试数据**：仅用于开发和测试环境
- **演示数据**：用于演示环境的示例数据

### 5.2 种子数据特性
- **幂等性**：可重复执行，不会产生重复数据
- **环境感知**：根据环境变量决定执行哪些种子数据
- **依赖管理**：种子数据之间的依赖关系管理
- **版本控制**：支持种子数据的版本管理

### 5.3 种子数据执行策略
```typescript
interface SeedConfig {
  name: string;
  description: string;
  environments: string[];  // 适用环境
  dependencies: string[];  // 依赖的种子数据
  version: string;         // 版本号
  force?: boolean;         // 是否强制执行
}
```

## 6. 环境配置

### 6.1 开发环境 (dev.yml)
```yaml
database:
  synchronize: true        # 自动同步数据库结构
  dropSchema: false        # 不删除现有结构
  migrationsRun: false     # 不自动运行迁移
  seeds:
    autoRun: true          # 自动运行种子数据
    environments: ['development', 'test']
```

### 6.2 生产环境 (prod.yml)
```yaml
database:
  synchronize: false       # 禁用自动同步
  dropSchema: false        # 禁用删除结构
  migrationsRun: true      # 自动运行迁移
  seeds:
    autoRun: false         # 禁用自动运行种子数据
    environments: ['production']
```

## 7. 命令行工具（简化版）

### 7.1 核心命令
```bash
# 完整数据库初始化（推荐）
npm run db:init

# 种子数据初始化（包含完整业务数据）
npm run seed:init

# 重置数据库
npm run db:reset
```

### 7.2 迁移管理
```bash
# 生成迁移文件
npm run migration:generate -- --name=CreateUserTable

# 执行迁移
npm run migration:run

# 回滚迁移
npm run migration:revert

# 查看迁移状态
npm run migration:show
```

## 8. 最佳实践

### 8.1 迁移脚本最佳实践
1. **原子性**：每个迁移脚本应该是原子操作
2. **可回滚**：必须提供完整的回滚逻辑
3. **数据安全**：涉及数据变更时要备份
4. **测试验证**：在测试环境充分验证后再应用到生产环境

### 8.2 种子数据最佳实践
1. **幂等性**：使用 `INSERT ... ON CONFLICT` 或先检查再插入
2. **环境隔离**：敏感数据不应出现在种子脚本中
3. **版本管理**：重要的种子数据变更要记录版本
4. **依赖管理**：明确种子数据之间的依赖关系

### 8.3 生产环境部署流程
1. **备份数据库**：执行任何变更前先备份
2. **执行迁移**：按顺序执行迁移脚本
3. **验证结构**：确认数据库结构正确
4. **执行种子数据**：根据需要执行种子数据
5. **功能验证**：验证应用功能正常

## 9. 监控和日志

### 9.1 执行日志
- 记录每次迁移和种子数据的执行时间
- 记录执行结果和错误信息
- 支持日志查询和分析

### 9.2 状态监控
- 数据库结构版本监控
- 种子数据执行状态监控
- 异常情况告警

## 10. 安全考虑

### 10.1 权限控制
- 生产环境迁移需要特殊权限
- 种子数据执行需要审批流程
- 敏感数据加密存储

### 10.2 审计追踪
- 记录所有数据库变更操作
- 保留操作人员和时间信息
- 支持变更历史查询

## 11. 使用示例

### 11.1 开发环境快速开始
```bash
# 完整初始化（推荐）
npm run db:init

# 分步执行
npm run db:migrate          # 只执行迁移
npm run seed:run            # 只执行种子数据
npm run seed:status         # 查看种子数据状态
```

### 11.2 生产环境部署
```bash
# 生产环境初始化（需要确认）
npm run db:init -- --env production

# 只执行基础种子数据
npm run seed:run -- --env production --names admin-user,default-services

# 查看执行状态
npm run seed:status
```

### 11.3 测试环境管理
```bash
# 重置测试数据库
npm run db:reset -- --env test

# 执行测试种子数据
npm run seed:run -- --env test --force

# 回滚特定种子数据
npm run seed:revert -- --names api-keys
```

### 11.4 开发调试
```bash
# 强制重新执行种子数据
npm run seed:run -- --force --verbose

# 执行特定种子数据
npm run seed:run -- --names admin-user,system-config

# 并行执行（开发环境）
npm run seed:run -- --parallel
```

## 12. 故障恢复

### 12.1 迁移失败恢复
1. 停止应用服务
2. 恢复数据库备份
3. 检查迁移脚本问题
4. 修复后重新执行

### 12.2 种子数据问题恢复
1. 查看种子数据执行日志
2. 手动清理问题数据
3. 重新执行种子数据
4. 验证数据完整性

## 13. 扩展指南

### 13.1 添加新的种子数据
1. 创建种子数据类继承 `BaseSeed`
2. 实现 `execute` 方法
3. 在 `SeedRegistry` 中注册
4. 测试种子数据执行

### 13.2 自定义种子数据逻辑
```typescript
export class CustomSeed extends BaseSeed {
  config: SeedConfig = {
    name: 'custom-seed',
    description: '自定义种子数据',
    environments: ['development'],
    dependencies: ['admin-user'],
    version: '1.0.0',
    priority: 10,
  };

  protected async execute(dataSource: DataSource): Promise<SeedResult> {
    // 实现自定义逻辑
    return {
      success: true,
      message: '自定义种子数据执行成功',
      affectedRows: 1,
    };
  }
}
```
