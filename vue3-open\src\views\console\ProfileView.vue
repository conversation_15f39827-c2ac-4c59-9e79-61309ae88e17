<template>
  <div class="profile-page">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">个人资料</h1>
        <p class="page-subtitle">管理您的账户信息和偏好设置</p>
      </div>
    </div>
    <!-- 个人信息卡片 -->
    <div class="profile-section">
      <div class="info-card">
        <div class="card-header">
          <h3>基本信息</h3>
          <!-- <el-button type="primary" size="small" @click="editMode = !editMode">
            {{ editMode ? '取消编辑' : '编辑资料' }}
          </el-button> -->
        </div>

        <div class="profile-content">
          <div class="avatar-section">
            <div class="avatar-container">
              <el-avatar :size="80" :src="mutableUserInfo?.avatar || userInfo?.avatar" :icon="UserFilled" />
              <div v-if="editMode" class="avatar-upload">
                <el-upload class="avatar-uploader" action="#" :show-file-list="false"
                  :before-upload="beforeAvatarUpload" :on-success="handleAvatarSuccess">
                  <el-button size="small" type="text">
                    <el-icon>
                      <Camera />
                    </el-icon>
                    更换头像
                  </el-button>
                </el-upload>
              </div>
            </div>
          </div>

          <div class="info-form">
            <el-form ref="profileFormRef" :model="profileForm" :rules="profileRules" label-width="100px"
              :disabled="!editMode">
              <el-form-item label="用户名" prop="username">
                <el-input v-model="profileForm.username" placeholder="请输入用户名" disabled />
              </el-form-item>

              <el-form-item label="邮箱" prop="email">
                <el-input v-model="profileForm.email" placeholder="请输入邮箱" disabled />
                <template #append v-if="!mutableUserInfo?.emailVerified">
                  <el-button @click="sendVerificationEmail">验证邮箱</el-button>
                </template>
              </el-form-item>

              <el-form-item label="手机号" prop="phone">
                <el-input v-model="profileForm.phone" placeholder="请输入手机号" disabled />
              </el-form-item>

              <el-form-item label="真实姓名" prop="realName">
                <el-input v-model="profileForm.realName" placeholder="请输入真实姓名" />
              </el-form-item>

              <el-form-item label="公司" prop="company">
                <el-input v-model="profileForm.company" placeholder="请输入公司名称" />
              </el-form-item>

              <el-form-item label="职位" prop="position">
                <el-input v-model="profileForm.position" placeholder="请输入职位" />
              </el-form-item>

              <el-form-item label="个人简介" prop="bio">
                <el-input v-model="profileForm.bio" type="textarea" :rows="3" placeholder="请输入个人简介" maxlength="200"
                  show-word-limit />
              </el-form-item>

              <el-form-item v-if="editMode">
                <el-button type="primary" :loading="saving" @click="saveProfile">
                  保存修改
                </el-button>
                <el-button @click="cancelEdit">取消</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>

    <!-- 安全设置 -->
    <div class="security-section">
      <div class="security-card">
        <div class="card-header">
          <h3>安全设置</h3>
        </div>

        <div class="security-items">
          <div class="security-item">
            <div class="item-info">
              <div class="item-title">登录密码</div>
              <div class="item-desc">定期更换密码可以提高账户安全性</div>
            </div>
            <div class="item-action">
              <el-button size="small" @click="showPasswordDialog = true">
                修改密码
              </el-button>
            </div>
          </div>

          <div class="security-item">
            <div class="item-info">
              <div class="item-title">第三方账号绑定</div>
              <div class="item-desc">绑定第三方账号可以快速登录</div>
            </div>
            <div class="item-action">
              <el-button size="small" @click="showOAuthDialog = true">
                管理绑定
              </el-button>
            </div>
          </div>

          <!-- <div class="security-item">
            <div class="item-info">
              <div class="item-title">两步验证</div>
              <div class="item-desc">
                {{ mutableUserInfo?.twoFactorEnabled ? '已启用两步验证' : '未启用两步验证，建议开启以提高安全性' }}
              </div>
            </div>
            <div class="item-action">
              <el-switch v-if="mutableUserInfo" v-model="mutableUserInfo.twoFactorEnabled" @change="toggleTwoFactor" />
            </div>
          </div>

          <div class="security-item">
            <div class="item-info">
              <div class="item-title">登录通知</div>
              <div class="item-desc">新设备登录时发送邮件通知</div>
            </div>
            <div class="item-action">
              <el-switch v-if="mutableUserInfo" v-model="mutableUserInfo.loginNotification"
                @change="updateNotificationSettings" />
            </div>
          </div> -->
        </div>
      </div>
    </div>

    <!-- 偏好设置 -->
    <div class="preferences-section">
      <div class="preferences-card">
        <div class="card-header">
          <h3>偏好设置</h3>
        </div>

        <div class="preferences-form">
          <el-form label-width="120px">
            <el-form-item label="语言">
              <el-select v-model="preferences.language" style="width: 200px;">
                <el-option label="简体中文" value="zh-cn" />
                <el-option label="English" value="en" />
                <el-option label="繁體中文" value="zh-tw" />
              </el-select>
            </el-form-item>

            <el-form-item label="时区">
              <el-select v-model="preferences.timezone" style="width: 200px;">
                <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai" />
                <el-option label="东京时间 (UTC+9)" value="Asia/Tokyo" />
                <el-option label="纽约时间 (UTC-5)" value="America/New_York" />
                <el-option label="伦敦时间 (UTC+0)" value="Europe/London" />
              </el-select>
            </el-form-item>

            <el-form-item label="主题">
              <el-radio-group v-model="preferences.theme">
                <el-radio label="light">浅色主题</el-radio>
                <el-radio label="dark">深色主题</el-radio>
                <el-radio label="auto">跟随系统</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="邮件通知">
              <el-checkbox-group v-model="preferences.emailNotifications">
                <el-checkbox label="system">系统通知</el-checkbox>
                <el-checkbox label="billing">账单提醒</el-checkbox>
                <el-checkbox label="security">安全警告</el-checkbox>
                <el-checkbox label="marketing">产品更新</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" :loading="savingPreferences" @click="savePreferences">
                保存偏好
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 账户统计 -->
    <!-- <div class="stats-section">
      <div class="stats-card">
        <div class="card-header">
          <h3>账户统计</h3>
        </div>

        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-icon">
              <el-icon color="#409eff">
                <Calendar />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ accountStats.joinDays }}</div>
              <div class="stat-label">加入天数</div>
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-icon">
              <el-icon color="#67c23a">
                <DataAnalysis />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatNumber(accountStats.totalCalls) }}</div>
              <div class="stat-label">总调用次数</div>
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-icon">
              <el-icon color="#e6a23c">
                <Key />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ accountStats.apiKeys }}</div>
              <div class="stat-label">API密钥数</div>
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-icon">
              <el-icon color="#f56c6c">
                <Wallet />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">¥{{ accountStats.totalSpent }}</div>
              <div class="stat-label">总消费</div>
            </div>
          </div>
        </div>
      </div>
    </div> -->

    <!-- 修改密码对话框 -->
    <el-dialog v-model="showPasswordDialog" title="修改密码" width="500px">
      <el-form ref="passwordFormRef" :model="passwordForm" :rules="passwordRules" label-width="100px">
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input v-model="passwordForm.currentPassword" type="password" placeholder="请输入当前密码" show-password />
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" placeholder="请输入新密码" show-password />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" placeholder="请再次输入新密码" show-password />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showPasswordDialog = false">取消</el-button>
        <el-button type="primary" :loading="changingPassword" @click="changePassword">
          确认修改
        </el-button>
      </template>
    </el-dialog>

    <!-- 第三方账号绑定对话框 -->
    <el-dialog v-model="showOAuthDialog" title="第三方账号绑定" width="600px">
      <div class="oauth-accounts">
        <div class="oauth-item" v-for="provider in oauthProviders" :key="provider.type">
          <div class="provider-info">
            <div class="provider-icon">
              <el-icon :size="24">
                <component :is="provider.icon" />
              </el-icon>
            </div>
            <div class="provider-details">
              <div class="provider-name">{{ provider.name }}</div>
              <div class="provider-desc">{{ provider.description }}</div>
            </div>
          </div>
          <div class="provider-action">
            <template v-if="isProviderBound(provider.type)">
              <el-tag type="success" size="small">已绑定</el-tag>
              <el-button size="small" type="danger" text @click="unbindOAuth(provider.type)">
                解绑
              </el-button>
            </template>
            <template v-else>
              <el-button size="small" type="primary" @click="bindOAuth(provider.type)">
                绑定
              </el-button>
            </template>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showOAuthDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules, UploadProps, UploadFile } from 'element-plus'
import {
  UserFilled,
  Camera,
  Calendar,
  DataAnalysis,
  Key,
  Wallet
} from '@element-plus/icons-vue'
import { useUserStore, type UserInfo } from '@/stores/user'
import { useOAuthStore } from '@/stores/oauth'
import dayjs from 'dayjs'

// 定义上传响应类型
interface UploadResponse {
  code: number
  message: string
  data: {
    file: File
    url: string
  }
}

const userStore = useUserStore()
const oauthStore = useOAuthStore()

const {
  userInfo,
  loading,
  updateProfile,
  updatePassword,
  uploadAvatar,
  updateSettings,
  verifyEmail,
  enableTwoFactor,
  disableTwoFactor,
  getUserInfo,
  getUserApiKeysInfo
} = userStore

const {
  oauthAccounts,
  bindOAuthAccount,
  unbindOAuthAccount,
  getOAuthAccounts,
  getOAuthUrl
} = oauthStore

// 创建可修改的用户信息副本
const mutableUserInfo = ref<UserInfo | null>(null)

const editMode = ref(false)
const saving = ref(false)
const savingPreferences = ref(false)
const changingPassword = ref(false)
const showPasswordDialog = ref(false)
const showOAuthDialog = ref(false)
const profileFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()

// 第三方登录提供商配置
const oauthProviders = ref([
  {
    type: 'github',
    name: 'GitHub',
    description: '使用GitHub账号快速登录',
    icon: 'github'
  },
  {
    type: 'google',
    name: 'Google',
    description: '使用Google账号快速登录',
    icon: 'google'
  },
  {
    type: 'wechat',
    name: '微信',
    description: '使用微信账号快速登录',
    icon: 'wechat'
  }
])

// 计算属性

// 个人资料表单
const profileForm = reactive({
  username: '',
  email: '',
  phone: '',
  realName: '',
  company: '',
  position: '',
  bio: ''
})

// 密码修改表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 偏好设置
const preferences = reactive({
  language: 'zh-cn',
  timezone: 'Asia/Shanghai',
  theme: 'light',
  emailNotifications: ['system', 'billing', 'security']
})

// 账户统计
const accountStats = ref({
  joinDays: 0,
  totalCalls: 15420,
  apiKeys: 3,
  totalSpent: 1256.78
})

// 表单验证规则
const profileRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

const passwordRules: FormRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString()
}

// 初始化表单数据
const initFormData = () => {
  if (!userInfo) return
  // 同步可修改的用户信息
  mutableUserInfo.value = {
    ...userInfo,
    twoFactorEnabled: userInfo.settings?.twoFactorEnabled || false,
    loginNotification: userInfo.settings?.loginNotification || false
  }
  Object.assign(profileForm, {
    username: userInfo.username,
    email: userInfo.email,
    phone: userInfo.phone,
    realName: userInfo.realName,
    company: userInfo.company,
    position: userInfo.position,
    bio: userInfo.bio
  })
}

// 取消编辑
const cancelEdit = () => {
  editMode.value = false
  initFormData()
  profileFormRef.value?.clearValidate()
}

// 保存个人资料
const saveProfile = async () => {
  if (!profileFormRef.value) return

  try {
    await profileFormRef.value.validate()
    saving.value = true

    // 确保使用ID更新用户信息
    if (!userInfo || !userInfo.id) {
      throw new Error('用户ID不存在，无法更新信息')
    }

    await updateProfile({
      id: userInfo.id, // 添加ID字段
      realName: profileForm.realName,
      company: profileForm.company,
      position: profileForm.position,
      bio: profileForm.bio
    })

    editMode.value = false
    ElMessage.success('个人资料更新成功')
  } catch (error) {
    // 表单验证失败或API调用失败
    console.error('保存个人资料失败:', error)
    ElMessage.error('保存个人资料失败')
  } finally {
    saving.value = false
  }
}

// 头像上传前检查
const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/png') {
    ElMessage.error('头像只能是 JPG/PNG 格式!')
    return false
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('头像大小不能超过 2MB!')
    return false
  }
  return true
}

// 头像上传成功
const handleAvatarSuccess: UploadProps['onSuccess'] = async (response: UploadResponse, uploadFile: UploadFile) => {
  try {
    if (uploadFile.raw) {
      await uploadAvatar(uploadFile.raw)
    } else {
      throw new Error('上传文件不存在')
    }
  } catch (error) {
    console.error('头像上传失败:', error)
    ElMessage.error('头像上传失败')
  }
}

// 发送邮箱验证
const sendVerificationEmail = async () => {
  try {
    await verifyEmail()
    ElMessage.success('验证邮件已发送，请查收')
  } catch (error) {
    ElMessage.error('发送验证邮件失败')
  }
}

// 切换两步验证
const toggleTwoFactor = async (enabled: boolean) => {
  try {
    if (enabled) {
      await enableTwoFactor()
      ElMessage.success('两步验证已启用')
      // 更新可修改的用户信息
      if (mutableUserInfo.value) {
        mutableUserInfo.value.twoFactorEnabled = true
      }
      // 同步到 userInfo
      if (userInfo && userInfo.settings) {
        userInfo.settings.twoFactorEnabled = true
      }
    } else {
      await ElMessageBox.confirm(
        '确定要关闭两步验证吗？这会降低账户安全性。',
        '确认操作',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await disableTwoFactor()
      ElMessage.success('两步验证已关闭')
      // 更新可修改的用户信息
      if (mutableUserInfo.value) {
        mutableUserInfo.value.twoFactorEnabled = false
      }
      // 同步到 userInfo
      if (userInfo && userInfo.settings) {
        userInfo.settings.twoFactorEnabled = false
      }
    }
  } catch {
    // 用户取消或操作失败，恢复开关状态
    if (mutableUserInfo.value) {
      mutableUserInfo.value.twoFactorEnabled = !enabled
    }
  }
}

// 更新通知设置
const updateNotificationSettings = async () => {
  try {
    if (mutableUserInfo.value) {
      await updateSettings({ loginNotification: mutableUserInfo.value.loginNotification })
      ElMessage.success('通知设置已更新')
      // 同步到 userInfo
      if (userInfo && userInfo.settings) {
        userInfo.settings.loginNotification = mutableUserInfo.value.loginNotification
      }
    }
  } catch (error) {
    console.error('更新通知设置失败:', error)
    ElMessage.error('更新通知设置失败')
    // 恢复原始状态
    if (mutableUserInfo.value && userInfo?.settings) {
      mutableUserInfo.value.loginNotification = userInfo.settings.loginNotification || false
    }
  }
}

// 保存偏好设置
const savePreferences = async () => {
  try {
    savingPreferences.value = true

    await updateSettings(preferences)

    ElMessage.success('偏好设置已保存')
  } catch (error) {
    ElMessage.error('保存偏好设置失败')
  } finally {
    savingPreferences.value = false
  }
}

// 修改密码
const changePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    changingPassword.value = true

    // 调用修改密码API
    await updatePassword({
      oldPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword
    })

    // 关闭对话框
    showPasswordDialog.value = false

    // 重置表单
    Object.assign(passwordForm, {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })

    // 显示修改成功提示
    ElMessage.success('密码修改成功，系统将自动退出登录')

    // 延迟一小段时间后执行登出操作，让用户看到成功提示
    setTimeout(() => {
      try {
        // 调用userStore的登出方法，清理token和用户信息
        userStore.logout().then(() => {
          // 登出成功后跳转到登录页面
          window.location.href = '/login'
        }).catch(error => {
          console.error('登出失败:', error)
          // 即使登出API调用失败，也强制清除本地数据并跳转
          userStore.clearLocalData()
          window.location.href = '/login'
        })
      } catch (error) {
        console.error('登出过程中发生错误:', error)
        // 出现任何错误，也强制清除本地数据并跳转
        userStore.clearLocalData()
        window.location.href = '/login'
      }
    }, 1500)
  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error('修改密码失败，请检查当前密码是否正确')
  } finally {
    changingPassword.value = false
  }
}

// 计算加入天数
const calculateJoinDays = () => {
  if (!userInfo) return
  const joinDate = dayjs(userInfo.joinDate || userInfo.createdAt)
  const today = dayjs()
  accountStats.value.joinDays = today.diff(joinDate, 'day')
}

// 加载账户统计信息
const loadAccountStats = async () => {
  try {
    // 获取 API 密钥信息
    const apiKeysInfo = await getUserApiKeysInfo()
    if (apiKeysInfo && typeof apiKeysInfo === 'object' && 'list' in apiKeysInfo && Array.isArray(apiKeysInfo.list)) {
      accountStats.value.apiKeys = apiKeysInfo.list.length
    } else if (apiKeysInfo && Array.isArray(apiKeysInfo)) {
      accountStats.value.apiKeys = apiKeysInfo.length
    }

    // 这里可以添加更多统计信息的获取
    // 例如：总调用次数、总消费等
    // const usageInfo = await getUserUsageInfo()
    // accountStats.value.totalCalls = usageInfo.totalCalls
    // accountStats.value.totalSpent = usageInfo.totalSpent

  } catch (error) {
    console.error('获取账户统计信息失败:', error)
  }
}

// OAuth相关方法
const isProviderBound = (providerType: string): boolean => {
  return oauthAccounts.value.some(account => account.providerType === providerType)
}

const bindOAuth = async (providerType: string) => {
  try {
    // 获取授权URL并跳转
    const authUrl = getOAuthUrl(providerType, `${window.location.origin}/auth/oauth/callback`)

    // 保存绑定信息
    localStorage.setItem('oauth_bind_mode', 'true')
    localStorage.setItem('oauth_provider', providerType)

    // 跳转到第三方授权页面
    window.location.href = authUrl
  } catch (error: any) {
    ElMessage.error(`绑定${providerType}失败: ${error.message}`)
  }
}

const unbindOAuth = async (providerType: string) => {
  try {
    await ElMessageBox.confirm(
      `确定要解绑${getProviderName(providerType)}账号吗？`,
      '确认解绑',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await unbindOAuthAccount({ providerType: providerType as any })
    ElMessage.success('解绑成功')

    // 刷新绑定列表
    await getOAuthAccounts()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(`解绑失败: ${error.message}`)
    }
  }
}

const getProviderName = (provider: string): string => {
  const names: Record<string, string> = {
    github: 'GitHub',
    google: 'Google',
    wechat: '微信',
    alipay: '支付宝'
  }
  return names[provider] || provider
}

onMounted(async () => {
  try {
    // 获取最新的用户信息
    await getUserInfo()

    // 初始化表单数据
    initFormData()

    // 计算加入天数
    calculateJoinDays()

    // 获取账户统计信息
    await loadAccountStats()

    // 加载OAuth绑定信息
    try {
      await getOAuthAccounts()
    } catch (error) {
      console.error('加载OAuth绑定信息失败:', error)
    }
  } catch (error) {
    console.error('初始化个人信息页面失败:', error)
  }
})
</script>

<style scoped>
.profile-page {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #606266;
  margin: 0;
  font-size: 1rem;
}

.profile-section,
.security-section,
.preferences-section,
.stats-section {
  margin-bottom: 32px;
}

.info-card,
.security-card,
.preferences-card,
.stats-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.card-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.profile-content {
  display: flex;
  gap: 32px;
}

.avatar-section {
  flex-shrink: 0;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.avatar-upload {
  text-align: center;
}

.info-form {
  flex: 1;
}

.security-items {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.item-info {
  flex: 1;
}

.item-title {
  font-size: 1rem;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.item-desc {
  font-size: 14px;
  color: #606266;
}

.item-action {
  flex-shrink: 0;
}

.preferences-form {
  max-width: 600px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-content {
    flex-direction: column;
    gap: 24px;
  }

  .security-item {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .item-action {
    align-self: stretch;
    display: flex;
    justify-content: flex-end;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* OAuth绑定对话框样式 */
.oauth-accounts {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.oauth-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.oauth-item:hover {
  border-color: #409eff;
  background-color: #f5f7fa;
}

.provider-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.provider-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.provider-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.provider-name {
  font-weight: 600;
  color: #303133;
}

.provider-desc {
  font-size: 14px;
  color: #606266;
}

.provider-action {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>