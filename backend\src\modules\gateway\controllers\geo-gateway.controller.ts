import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  Req,
  UseGuards,
  UseInterceptors,
  Logger,
} from '@nestjs/common';
import { Request } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { UseAuthStrategy, AuthStrategy } from '@/common/decorators/auth-strategy.decorator';
import { GatewayService } from '../services/gateway.service';
import { 
  GeoCoordinateRequestDto,
  BatchRequestDto,
} from '../dto/gateway-request.dto';
import {
  SyncResponseDto,
  AsyncResponseDto,
  GeoCoordinateResponseDto,
  ErrorResponseDto,
} from '../dto/gateway-response.dto';
import { IGatewayRequest } from '../interfaces/gateway.interfaces';
import { ProcessingMode } from '../config/gateway.constants';
import { getClientIp } from '@/common/utils/ip.utils';

/**
 * 地理坐标网关控制器
 * 负责处理地理坐标转换相关的网关请求
 * 遵循控制器职责，专注于HTTP请求处理和参数验证
 */
@ApiTags('地理坐标网关')
@Controller('geo')
@UseAuthStrategy(AuthStrategy.API_KEY)
@ApiBearerAuth()
export class GeoGatewayController {
  private readonly logger = new Logger(GeoGatewayController.name);

  constructor(private readonly gatewayService: GatewayService) {}

  /**
   * 逆地理编码（坐标转地址）- GET方式
   */
  @Get('reverse')
  @ApiOperation({ summary: '逆地理编码（坐标转地址）' })
  @ApiResponse({ 
    status: 200, 
    description: '转换成功',
    type: SyncResponseDto,
  })
  @ApiResponse({ 
    status: 400, 
    description: '请求参数错误',
    type: ErrorResponseDto,
  })
  async reverseGeocodeGet(
    @Query('lat') lat: number,
    @Query('lng') lng: number,
    @Query('serviceId') serviceId: number,
    @Query('coordType') coordType: string = 'wgs84',
    @Req() req: Request
  ) {
    this.logger.log(`逆地理编码请求 (GET): ${lat}, ${lng}`);

    if (serviceId) {
      this.logger.debug(`使用前端传递的服务ID: ${serviceId}`);
    } else {
      this.logger.warn('前端未传递服务ID，将使用路径映射（不推荐）');
    }

    const gatewayRequest: IGatewayRequest = {
      path: '/v1/op/geo/reverse',
      method: 'GET',
      headers: req.headers as Record<string, string>,
      query: { lat, lng, coordType },
      mode: ProcessingMode.SYNC, // 地理编码通常很快
      serviceId, // 传递服务ID
      user: (req as any).user ? {
        id: (req as any).user.id,
        username: (req as any).user.username,
        email: (req as any).user.email,
      } : undefined,
      clientInfo: {
        ip: getClientIp(req),
        userAgent: req.headers['user-agent'],
        referer: req.headers['referer'],
        apiKey: req.headers['x-api-key'] as string,
        userId: (req as any).user?.id,
      },
    };

    try {
      const response = await this.gatewayService.handleRequest(gatewayRequest);

      // 直接返回GatewayService的响应，避免重复包装
      return response;
    } catch (error) {
      // 服务降级：当后端服务不可用时，返回模拟数据
      if (error.message?.includes('服务暂时不可用') || error.message?.includes('ECONNREFUSED')) {
        this.logger.warn(`地理服务不可用，返回模拟数据: ${lat}, ${lng}`);

        // 直接返回最终格式，避免被UnifiedInterceptor再次包装
        return {
          success: true,
          code: 200,
          message: '逆地理编码成功（模拟数据）',
          address: '广东省深圳市南山区科技园南路',
          formatted_address: '广东省深圳市南山区科技园南路',
          location: {
            lat: parseFloat(lat.toString()),
            lng: parseFloat(lng.toString())
          },
          address_components: {
            country: '中国',
            province: '广东省',
            city: '深圳市',
            district: '南山区',
            street: '科技园南路',
            street_number: ''
          },
          confidence: 0.8,
          source: 'mock_data',
          note: '当前返回模拟数据，实际服务正在开发中',
          timestamp: new Date().toISOString(),
          requestId: gatewayRequest.requestId || 'unknown'
        };
      }

      // 其他错误直接抛出
      throw error;
    }
  }

  /**
   * 逆地理编码（坐标转地址）- POST方式
   */
  @Post('reverse')
  @ApiOperation({ summary: '逆地理编码（坐标转地址）' })
  @ApiResponse({ 
    status: 200, 
    description: '转换成功',
    type: SyncResponseDto,
  })
  @ApiResponse({ 
    status: 400, 
    description: '请求参数错误',
    type: ErrorResponseDto,
  })
  async reverseGeocodePost(
    @Body() body: GeoCoordinateRequestDto,
    @Req() req: Request
  ) {
    this.logger.log(`逆地理编码请求 (POST): ${body.lat}, ${body.lng}`);

    const gatewayRequest: IGatewayRequest = {
      path: '/v1/op/geo/reverse',
      method: 'POST',
      headers: req.headers as Record<string, string>,
      body: {
        lat: body.lat,
        lng: body.lng,
        coordType: body.coordType || 'wgs84',
      },
      mode: body.mode || ProcessingMode.SYNC,
      user: (req as any).user ? {
        id: (req as any).user.id,
        username: (req as any).user.username,
        email: (req as any).user.email,
      } : undefined,
      clientInfo: {
        ip: getClientIp(req),
        userAgent: req.headers['user-agent'],
        referer: req.headers['referer'],
        apiKey: req.headers['x-api-key'] as string,
        userId: (req as any).user?.id,
      },
    };

    try {
      const response = await this.gatewayService.handleRequest(gatewayRequest);

      // 直接返回GatewayService的响应，避免重复包装
      return response;
    } catch (error) {
      // 服务降级：当后端服务不可用时，返回模拟数据
      if (error.message?.includes('服务暂时不可用') || error.message?.includes('ECONNREFUSED')) {
        this.logger.warn(`地理服务不可用，返回模拟数据: ${body.lat}, ${body.lng}`);

        // 直接返回最终格式，避免被UnifiedInterceptor再次包装
        return {
          success: true,
          code: 200,
          message: '逆地理编码成功（模拟数据）',
          data: {
            address: '广东省深圳市南山区科技园南路',
            formatted_address: '广东省深圳市南山区科技园南路',
            location: {
              lat: parseFloat((body.lat || 22.543096).toString()),
              lng: parseFloat((body.lng || 114.057865).toString())
            },
            address_components: {
              country: '中国',
              province: '广东省',
              city: '深圳市',
              district: '南山区',
              street: '科技园南路',
              street_number: ''
            },
            confidence: 0.8,
            source: 'mock_data',
            note: '当前返回模拟数据，实际服务正在开发中'
          },
          timestamp: new Date().toISOString(),
          requestId: gatewayRequest.requestId || 'unknown'
        };
      }

      // 其他错误直接抛出
      throw error;
    }
  }

  /**
   * 正地理编码（地址转坐标）- GET方式
   */
  @Get('forward')
  @ApiOperation({ summary: '正地理编码（地址转坐标）' })
  @ApiResponse({ 
    status: 200, 
    description: '转换成功',
    type: SyncResponseDto,
  })
  @ApiResponse({ 
    status: 400, 
    description: '请求参数错误',
    type: ErrorResponseDto,
  })
  async forwardGeocodeGet(
    @Query('address') address: string,
    @Query('coordType') coordType: string = 'wgs84',
    @Req() req: Request
  ) {
    this.logger.log(`正地理编码请求 (GET): ${address}`);

    const gatewayRequest: IGatewayRequest = {
      path: '/v1/op/geo/forward',
      method: 'GET',
      headers: req.headers as Record<string, string>,
      query: { address, coordType },
      mode: ProcessingMode.SYNC,
      user: (req as any).user ? {
        id: (req as any).user.id,
        username: (req as any).user.username,
        email: (req as any).user.email,
      } : undefined,
      clientInfo: {
        ip: getClientIp(req),
        userAgent: req.headers['user-agent'],
        referer: req.headers['referer'],
        apiKey: req.headers['x-api-key'] as string,
        userId: (req as any).user?.id,
      },
    };

    try {
      const response = await this.gatewayService.handleRequest(gatewayRequest);

      // 直接返回GatewayService的响应，避免重复包装
      return response;
    } catch (error) {
      // 服务降级：当后端服务不可用时，返回模拟数据
      if (error.message?.includes('服务暂时不可用') || error.message?.includes('ECONNREFUSED')) {
        this.logger.warn(`地理服务不可用，返回模拟数据: ${address}`);

        // 根据地址生成模拟坐标
        let lat = 39.908823; // 默认北京坐标
        let lng = 116.397470;

        if (address?.includes('深圳') || address?.includes('广东')) {
          lat = 22.543096;
          lng = 114.057865;
        } else if (address?.includes('上海')) {
          lat = 31.230416;
          lng = 121.473701;
        } else if (address?.includes('广州')) {
          lat = 23.129110;
          lng = 113.264385;
        }

        // 直接返回最终格式，避免被UnifiedInterceptor再次包装
        return {
          success: true,
          code: 200,
          message: '正地理编码成功（模拟数据）',
          data: {
            address: address || '未知地址',
            location: {
              lat: lat,
              lng: lng
            },
            confidence: 0.8,
            source: 'mock_data',
            note: '当前返回模拟数据，实际服务正在开发中'
          },
          timestamp: new Date().toISOString(),
          requestId: gatewayRequest.requestId || 'unknown'
        };
      }

      // 其他错误直接抛出
      throw error;
    }
  }

  /**
   * 正地理编码（地址转坐标）- POST方式
   */
  @Post('forward')
  @ApiOperation({ summary: '正地理编码（地址转坐标）' })
  @ApiResponse({ 
    status: 200, 
    description: '转换成功',
    type: SyncResponseDto,
  })
  @ApiResponse({ 
    status: 400, 
    description: '请求参数错误',
    type: ErrorResponseDto,
  })
  async forwardGeocodePost(
    @Body() body: GeoCoordinateRequestDto,
    @Req() req: Request
  ) {
    this.logger.log(`正地理编码请求 (POST): ${body.address}`);

    const gatewayRequest: IGatewayRequest = {
      path: '/v1/op/geo/forward',
      method: 'POST',
      headers: req.headers as Record<string, string>,
      body: {
        address: body.address,
        coordType: body.coordType || 'wgs84',
      },
      mode: body.mode || ProcessingMode.SYNC,
      user: (req as any).user ? {
        id: (req as any).user.id,
        username: (req as any).user.username,
        email: (req as any).user.email,
      } : undefined,
      clientInfo: {
        ip: getClientIp(req),
        userAgent: req.headers['user-agent'],
        referer: req.headers['referer'],
        apiKey: req.headers['x-api-key'] as string,
        userId: (req as any).user?.id,
      },
    };

    try {
      const response = await this.gatewayService.handleRequest(gatewayRequest);

      // 直接返回GatewayService的响应，避免重复包装
      return response;
    } catch (error) {
      // 服务降级：当后端服务不可用时，返回模拟数据
      if (error.message?.includes('服务暂时不可用') || error.message?.includes('ECONNREFUSED')) {
        this.logger.warn(`地理服务不可用，返回模拟数据: ${body.address}`);

        // 根据地址生成模拟坐标
        let lat = 39.908823; // 默认北京坐标
        let lng = 116.397470;

        if (body.address?.includes('深圳') || body.address?.includes('广东')) {
          lat = 22.543096;
          lng = 114.057865;
        } else if (body.address?.includes('上海')) {
          lat = 31.230416;
          lng = 121.473701;
        } else if (body.address?.includes('广州')) {
          lat = 23.129110;
          lng = 113.264385;
        }

        // 直接返回最终格式，避免被UnifiedInterceptor再次包装
        return {
          success: true,
          code: 200,
          message: '正地理编码成功（模拟数据）',
          data: {
            address: body.address || '未知地址',
            location: {
              lat: lat,
              lng: lng
            },
            confidence: 0.8,
            source: 'mock_data',
            note: '当前返回模拟数据，实际服务正在开发中'
          },
          timestamp: new Date().toISOString(),
          requestId: gatewayRequest.requestId || 'unknown'
        };
      }

      // 其他错误直接抛出
      throw error;
    }
  }

  /**
   * 批量地理编码
   */
  @Post('batch')
  @ApiOperation({ summary: '批量地理编码' })
  @ApiResponse({ 
    status: 202, 
    description: '批量处理已接受',
    type: AsyncResponseDto,
  })
  @ApiResponse({ 
    status: 400, 
    description: '请求参数错误',
    type: ErrorResponseDto,
  })
  async batchGeocode(
    @Body() body: BatchRequestDto & { operation: 'forward' | 'reverse' },
    @Req() req: Request
  ) {
    this.logger.log(`批量地理编码请求: ${body.operation}, ${body.items?.length || 0} 个项目`);

    // 批量请求强制使用异步模式
    const gatewayRequest: IGatewayRequest = {
      path: `/v1/op/geo/${body.operation}/batch`,
      method: 'POST',
      headers: req.headers as Record<string, string>,
      body: {
        operation: body.operation,
        items: body.items,
        batchOptions: body.batchOptions,
      },
      mode: ProcessingMode.ASYNC, // 强制异步
      user: (req as any).user ? {
        id: (req as any).user.id,
        username: (req as any).user.username,
        email: (req as any).user.email,
      } : undefined,
      clientInfo: {
        ip: getClientIp(req),
        userAgent: req.headers['user-agent'],
        referer: req.headers['referer'],
        apiKey: req.headers['x-api-key'] as string,
        userId: (req as any).user?.id,
      },
    };

    const response = await this.gatewayService.handleRequest(gatewayRequest);
    
    return {
      ...response,
      statusUrl: `/v1/op/tasks/${response.taskId}`,
      eventsUrl: `/v1/op/tasks/${response.taskId}/events`,
    };
  }

  /**
   * 坐标系转换
   */
  @Post('transform')
  @ApiOperation({ summary: '坐标系转换' })
  @ApiResponse({ 
    status: 200, 
    description: '转换成功',
    type: SyncResponseDto,
  })
  @ApiResponse({ 
    status: 400, 
    description: '请求参数错误',
    type: ErrorResponseDto,
  })
  async transformCoordinate(
    @Body() body: {
      lat: number;
      lng: number;
      fromCoordType: string;
      toCoordType: string;
    },
    @Req() req: Request
  ) {
    this.logger.log(`坐标系转换请求: ${body.fromCoordType} -> ${body.toCoordType}`);

    const gatewayRequest: IGatewayRequest = {
      path: '/v1/op/geo/transform',
      method: 'POST',
      headers: req.headers as Record<string, string>,
      body: {
        lat: body.lat,
        lng: body.lng,
        fromCoordType: body.fromCoordType,
        toCoordType: body.toCoordType,
      },
      mode: ProcessingMode.SYNC, // 坐标转换很快
      user: (req as any).user ? {
        id: (req as any).user.id,
        username: (req as any).user.username,
        email: (req as any).user.email,
      } : undefined,
      clientInfo: {
        ip: getClientIp(req),
        userAgent: req.headers['user-agent'],
        referer: req.headers['referer'],
        apiKey: req.headers['x-api-key'] as string,
        userId: (req as any).user?.id,
      },
    };

    const response = await this.gatewayService.handleRequest(gatewayRequest);
    
    return {
      ...response,
      data: this.formatGeoResponse(response.data),
    };
  }

  /**
   * 格式化地理坐标响应数据
   */
  private formatGeoResponse(data: any): GeoCoordinateResponseDto {
    if (!data) {
      return {};
    }

    // 如果数据已经是标准格式，直接返回
    if (data.lat !== undefined || data.lng !== undefined || data.address !== undefined) {
      return data;
    }

    // 尝试从不同的数据结构中提取信息
    const result: GeoCoordinateResponseDto = {};

    // 提取坐标
    if (data.latitude !== undefined || data.lat !== undefined) {
      result.lat = data.latitude || data.lat;
    }
    if (data.longitude !== undefined || data.lng !== undefined) {
      result.lng = data.longitude || data.lng;
    }

    // 提取地址信息
    if (data.address || data.formatted_address) {
      result.address = data.address || data.formatted_address;
    }

    // 提取行政区划信息
    if (data.province || data.administrative_area_level_1) {
      result.province = data.province || data.administrative_area_level_1;
    }
    if (data.city || data.locality) {
      result.city = data.city || data.locality;
    }
    if (data.district || data.sublocality || data.administrative_area_level_3) {
      result.district = data.district || data.sublocality || data.administrative_area_level_3;
    }
    if (data.street || data.route) {
      result.street = data.street || data.route;
    }
    if (data.streetNumber || data.street_number) {
      result.streetNumber = data.streetNumber || data.street_number;
    }

    // 提取POI信息
    if (data.poi || data.point_of_interest) {
      result.poi = data.poi || data.point_of_interest;
    }

    // 提取置信度和精度信息
    if (data.confidence !== undefined) {
      result.confidence = data.confidence;
    }
    if (data.level || data.precision) {
      result.level = data.level || data.precision;
    }

    return result;
  }

  /**
   * 获取地理编码能力信息
   */
  @Get('capabilities')
  @ApiOperation({ summary: '获取地理编码能力信息' })
  @ApiResponse({ 
    status: 200, 
    description: '处理能力信息',
  })
  async getGeoCapabilities() {
    this.logger.log('获取地理编码能力信息');

    return {
      success: true,
      code: 200,
      data: {
        supportedOperations: ['forward', 'reverse', 'transform', 'batch'],
        supportedCoordTypes: ['wgs84', 'gcj02', 'bd09'],
        processingModes: [
          {
            mode: ProcessingMode.SYNC,
            description: '同步处理，适合单个坐标快速转换',
            avgResponseTime: '0.5-2秒',
          },
          {
            mode: ProcessingMode.ASYNC,
            description: '异步处理，适合批量处理',
            avgResponseTime: '2-10秒',
          },
        ],
        features: [
          '正地理编码（地址转坐标）',
          '逆地理编码（坐标转地址）',
          '坐标系转换',
          '批量处理',
          '行政区划解析',
          'POI识别',
          '精度评估',
        ],
        supportedRegions: [
          '中国大陆',
          '港澳台地区',
        ],
        accuracy: {
          province: '100%',
          city: '99%',
          district: '95%',
          street: '85%',
          building: '70%',
        },
      },
    };
  }
}
