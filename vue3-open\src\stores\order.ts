import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import { request } from '@/utils/request'
import type {
  Order,
  OrderStats,
  OrderQueryParams,
  CreateOrderForm,
  OrderPayment,
  OrderRefund
} from '@/types/order'

export const useOrderStore = defineStore('order', () => {
  // 状态
  const loading = ref(false)
  const orders = ref<Order[]>([])
  const currentOrder = ref<Order | null>(null)
  const stats = ref<OrderStats>({
    totalOrders: 0,
    pendingOrders: 0,
    paidOrders: 0,
    completedOrders: 0,
    cancelledOrders: 0,
    totalAmount: 0,
    paidAmount: 0,
    refundedAmount: 0
  })
  const pagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })
  const lastUpdated = ref<Date | null>(null)

  // 计算属性
  const pendingOrders = computed(() => 
    orders.value.filter(order => order.status === 'pending')
  )
  
  const paidOrders = computed(() => 
    orders.value.filter(order => order.paymentStatus === 'paid')
  )
  
  const completedOrders = computed(() => 
    orders.value.filter(order => order.status === 'completed')
  )
  
  const hasData = computed(() => orders.value.length > 0)

  // 获取订单列表
  const fetchOrders = async (params?: OrderQueryParams) => {
    try {
      loading.value = true
      const queryParams = {
        page: pagination.value.page,
        pageSize: pagination.value.pageSize,
        ...params
      }
      
      const response: Record<string, any> = await request.get('/op/orders', { params: queryParams })
      
      orders.value = response.records || response.data || []
      pagination.value.total = response.total || 0
      lastUpdated.value = new Date()
      
      return response
    } catch (error) {
      console.error('获取订单列表失败:', error)
      ElMessage.error('获取订单列表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取订单详情
  const fetchOrderDetail = async (orderId: string) => {
    try {
      loading.value = true
      const response: Record<string, any> = await request.get(`/op/orders/${orderId}`)
      currentOrder.value = response as any
      return response
    } catch (error) {
      console.error('获取订单详情失败:', error)
      ElMessage.error('获取订单详情失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取订单统计
  const fetchOrderStats = async (timeRange?: { start: string; end: string }) => {
    try {
      const response: Record<string, any> = await request.get('/op/orders/stats', {
        params: timeRange
      })
      stats.value = response as any
      return response
    } catch (error) {
      console.error('获取订单统计失败:', error)
      throw error
    }
  }

  // 创建订单
  const createOrder = async (form: CreateOrderForm) => {
    try {
      loading.value = true
      const response = await request.post('/op/orders', form)
      ElMessage.success('订单创建成功')
      await fetchOrders() // 刷新列表
      return response
    } catch (error) {
      console.error('创建订单失败:', error)
      ElMessage.error('创建订单失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 支付订单
  const payOrder = async (orderId: string, paymentMethod: string) => {
    try {
      loading.value = true
      const response = await request.post(`/op/orders/${orderId}/pay`, {
        paymentMethod
      })
      ElMessage.success('支付请求已提交')
      await fetchOrderDetail(orderId) // 刷新订单详情
      return response
    } catch (error) {
      console.error('支付订单失败:', error)
      ElMessage.error('支付订单失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 取消订单
  const cancelOrder = async (orderId: string, reason?: string) => {
    try {
      loading.value = true
      const response = await request.post(`/op/orders/${orderId}/cancel`, {
        reason
      })
      ElMessage.success('订单已取消')
      await fetchOrders() // 刷新列表
      return response
    } catch (error) {
      console.error('取消订单失败:', error)
      ElMessage.error('取消订单失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 申请退款
  const requestRefund = async (orderId: string, reason: string) => {
    try {
      loading.value = true
      const response = await request.post(`/op/orders/${orderId}/refund`, {
        reason
      })
      ElMessage.success('退款申请已提交')
      await fetchOrderDetail(orderId) // 刷新订单详情
      return response
    } catch (error) {
      console.error('申请退款失败:', error)
      ElMessage.error('申请退款失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 确认收货
  const confirmOrder = async (orderId: string) => {
    try {
      loading.value = true
      const response = await request.post(`/op/orders/${orderId}/confirm`)
      ElMessage.success('订单已确认')
      await fetchOrders() // 刷新列表
      return response
    } catch (error) {
      console.error('确认订单失败:', error)
      ElMessage.error('确认订单失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 重置状态
  const resetState = () => {
    orders.value = []
    currentOrder.value = null
    stats.value = {
      totalOrders: 0,
      pendingOrders: 0,
      paidOrders: 0,
      completedOrders: 0,
      cancelledOrders: 0,
      totalAmount: 0,
      paidAmount: 0,
      refundedAmount: 0
    }
    pagination.value = {
      page: 1,
      pageSize: 20,
      total: 0
    }
    lastUpdated.value = null
  }

  return {
    // 状态
    loading,
    orders,
    currentOrder,
    stats,
    pagination,
    lastUpdated,
    
    // 计算属性
    pendingOrders,
    paidOrders,
    completedOrders,
    hasData,
    
    // 方法
    fetchOrders,
    fetchOrderDetail,
    fetchOrderStats,
    createOrder,
    payOrder,
    cancelOrder,
    requestRefund,
    confirmOrder,
    resetState
  }
})