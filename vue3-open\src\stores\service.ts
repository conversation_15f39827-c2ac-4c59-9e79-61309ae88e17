import { defineStore } from 'pinia'
import { ref } from 'vue'
import { request } from '@/utils/request'
import service from '@/utils/request'
import { ElMessage } from 'element-plus'

export interface Service {
  id: string | number
  name: string
  code: string
  type: string
  description: string
  category: string
  serviceStatus: 'active' | 'inactive' | 'maintenance' | 'deprecated'
  version?: string
  endpoint?: string
  icon?: string
  color?: string
  price: number
  unit?: string
  accuracy?: number
  responseTime?: number
  supportedFormats?: string[]
  freeQuota?: number
  pricing: {
    type: 'free' | 'pay_per_use' | 'subscription'
    price: number
    unit: string
  }
  pricingTiers?: {
    min: number
    max?: number
    price: number
  }[]
  features?: string,
  limitations?: {
    rateLimit: number
    maxFileSize?: number
    supportedFormats?: string[]
  }
  documentation?: {
    overview: string
    parameters: Record<string, unknown>[]
    examples: Record<string, unknown>[]
  }
  createdAt: string
  updatedAt: string
}

export interface ServiceUsage {
  serviceId: number
  serviceName: string
  totalCalls: number
  successCalls: number
  failedCalls: number
  totalCost: number
  lastUsedAt: string
}

export interface UsageRecord {
  id: number
  serviceId: number
  serviceName: string
  status: 'success' | 'failed'
  cost: number
  processingTime: number
  requestParams: Record<string, unknown>
  responseResult: unknown
  errorMessage?: string
  createdAt: string
}

export const useServiceStore = defineStore('service', () => {
  // 状态
  const services = ref<Service[]>([])
  const currentService = ref<Service | null>(null)
  const serviceUsage = ref<ServiceUsage[]>([])
  const usageRecords = ref<UsageRecord[]>([])
  const loading = ref(false)

  // 获取服务列表
  const getServices = async () => {
    try {
      loading.value = true
      const response: Record<string, any> = await request.get('/services')
      services.value = response?.data || []
      return services.value
    } catch (error) {
      console.error('获取服务列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取服务详情
  const getServiceDetail = async (id: number) => {
    try {
      const response = await request.get(`/services/${id}`)
      return response
    } catch (error) {
      console.error('获取服务详情失败:', error)
      throw error
    }
  }

  // 获取服务详情并设置当前服务
  const fetchServiceDetail = async (serviceCode: string) => {
    try {
      loading.value = true
      const response = await request.get(`/services/code/${serviceCode}`)
      currentService.value = response as any
      return response
    } catch (error) {
      console.error('获取服务详情失败:', error)
      currentService.value = null
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取服务列表（支持筛选）
  const fetchServices = async (params?: Record<string, unknown>) => {
    try {
      loading.value = true
      const response: Record<string, any> = await request.get('/services', { params })
      services.value = response.records || response
      return response
    } catch (error) {
      console.error('获取服务列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 根据ID获取服务
  const getServiceById = async (id: string) => {
    try {
      const response = await request.get(`/services/${id}`)
      return response
    } catch (error) {
      console.error('获取服务失败:', error)
      throw error
    }
  }

  // 获取服务版本历史
  const getServiceVersions = async (serviceId: string) => {
    try {
      const response: Record<string, any> = await request.get(`/services/${serviceId}/versions`)
      return response.records || response
    } catch (error) {
      console.error('获取服务版本历史失败:', error)
      throw error
    }
  }

  // 获取服务使用统计
  const getServiceUsage = async (params?: Record<string, unknown>) => {
    try {
      loading.value = true
      const response: Record<string, any> = await request.get('/op/usage/stats', { params })
      serviceUsage.value = response.serviceTypeStats || []
      return response
    } catch (error) {
      console.error('获取服务使用统计失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取使用记录
  const getUsageRecords = async (params?: Record<string, unknown>) => {
    try {
      loading.value = true
      const response: Record<string, any> = await request.get('/op/usage/records', { params })
      usageRecords.value = response.records || response
      return response
    } catch (error) {
      console.error('获取使用记录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 调用服务（同步）
  const callServiceSync = async (serviceCode: string, data: unknown) => {
    try {
      const response = await request.post(`/admin/gateway/v1/service/${serviceCode}/sync`, data)
      ElMessage.success('服务调用成功')
      return response
    } catch (error) {
      console.error('服务调用失败:', error)
      throw error
    }
  }

  // 调用服务（异步）
  const callServiceAsync = async (serviceCode: string, data: unknown) => {
    try {
      const response = await request.post(`/admin/gateway/v1/service/${serviceCode}/async`, data)
      ElMessage.success('异步任务已提交')
      return response
    } catch (error) {
      console.error('异步服务调用失败:', error)
      throw error
    }
  }

  // 获取异步任务状态
  const getTaskStatus = async (queueName: string, jobId: string) => {
    try {
      const response = await request.get(`/admin/gateway/v1/service/status/${queueName}/${jobId}`)
      return response
    } catch (error) {
      console.error('获取任务状态失败:', error)
      throw error
    }
  }

  // OCR识别（文件上传）
  const ocrRecognize = async (file: File, options?: Record<string, unknown>) => {
    try {
      const formData = new FormData()
      formData.append('image', file)
      if (options) {
        formData.append('options', JSON.stringify(options))
      }

      const response = await request.post('/admin/gateway/v1/service/ocr/sync', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })

      ElMessage.success('OCR识别完成')
      return response
    } catch (error) {
      console.error('OCR识别失败:', error)
      throw error
    }
  }

  // OCR识别（Base64）
  const ocrRecognizeBase64 = async (imageData: string, options?: Record<string, unknown>) => {
    try {
      const response = await request.post('/admin/gateway/v1/service/ocr/sync', {
        imageData,
        imageType: 'base64',
        options,
      })

      ElMessage.success('OCR识别完成')
      return response
    } catch (error) {
      console.error('OCR识别失败:', error)
      throw error
    }
  }

  // 测试API
  const testApi = async (config: Record<string, any>) => {
    try {
      const response: Record<string, any> = await service.request({
        method: config.method || 'POST',
        url: config.url,
        data: config.data,
        headers: config.headers,
      })
      return response
    } catch (error) {
      console.error('API测试失败:', error)
      throw error
    }
  }

  return {
    // 状态
    services,
    currentService,
    serviceUsage,
    usageRecords,
    loading,

    // 方法
    getServices,
    getServiceDetail,
    getServiceById,
    fetchServiceDetail,
    fetchServices,
    getServiceVersions,
    getServiceUsage,
    getUsageRecords,
    callServiceSync,
    callServiceAsync,
    getTaskStatus,
    ocrRecognize,
    ocrRecognizeBase64,
    testApi,
  }
})
