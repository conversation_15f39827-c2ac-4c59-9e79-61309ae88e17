import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { DataSource, DataSourceOptions } from 'typeorm';
import { join } from 'path';

/**
 * 数据库配置工厂
 * 根据环境变量生成不同的数据库配置
 */
export const createDatabaseConfig = (configService: ConfigService): TypeOrmModuleOptions => {
  const isProduction = configService.get('NODE_ENV') === 'production';
  const isTest = configService.get('NODE_ENV') === 'test';
  
  const baseConfig: TypeOrmModuleOptions = {
    type: 'mysql',
    host: configService.get('database.host', 'localhost'),
    port: configService.get('database.port', 3306),
    username: configService.get('database.username', 'root'),
    password: configService.get('database.password', ''),
    database: configService.get('database.database', 'open_backend'),
    charset: 'utf8mb4',
    timezone: '+08:00',
    
    // 实体文件路径
    entities: [join(__dirname, '../..', '**', '*.entity{.ts,.js}')],
    
    // 迁移文件路径
    migrations: [join(__dirname, '..', 'migrations', '*{.ts,.js}')],
    
    // 订阅者文件路径
    subscribers: [join(__dirname, '..', 'subscribers', '*{.ts,.js}')],
    
    // 日志配置
    logging: configService.get('database.logging', !isProduction),
    logger: 'advanced-console',
    
    // 连接池配置
    extra: {
      connectionLimit: configService.get('database.connectionLimit', 10),
      acquireTimeout: configService.get('database.acquireTimeout', 60000),
      timeout: configService.get('database.timeout', 60000),
    },
  };

  // 环境特定配置
  if (isProduction) {
    return {
      ...baseConfig,
      synchronize: false,           // 生产环境禁用自动同步
      dropSchema: false,            // 生产环境禁用删除结构
      migrationsRun: true,          // 生产环境自动运行迁移
      cache: {                      // 启用查询缓存
        duration: 30000,            // 缓存30秒
      },
    };
  } else if (isTest) {
    return {
      ...baseConfig,
      database: configService.get('database.testDatabase', 'open_backend_test'),
      synchronize: true,            // 测试环境自动同步
      dropSchema: true,             // 测试环境每次重建
      migrationsRun: false,         // 测试环境不运行迁移
      logging: false,               // 测试环境关闭日志
    } as TypeOrmModuleOptions;
  } else {
    // 开发环境
    return {
      ...baseConfig,
      synchronize: configService.get('database.synchronize', true),
      dropSchema: false,
      migrationsRun: false,         // 开发环境手动运行迁移
    };
  }
};

/**
 * 创建数据源配置（用于CLI工具）
 * 从配置文件读取数据库配置
 */
export const createDataSourceConfig = (): DataSourceOptions => {
  const isProduction = process.env.NODE_ENV === 'production';

  // 尝试从配置文件读取数据库配置
  let dbConfig = {
    host: 'localhost',
    port: 3306,
    username: 'root',
    password: '123456',
    database: 'openapidb',
  };

  // 从环境变量读取数据库配置，如果没有则使用默认值
  dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || '123456',
    database: process.env.DB_DATABASE || 'openapidb',
  };

  return {
    type: 'mysql',
    host: dbConfig.host,
    port: dbConfig.port,
    username: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    charset: 'utf8mb4',
    timezone: '+08:00',

    entities: [join(__dirname, '../..', '**', '*.entity{.ts,.js}')],
    migrations: [join(__dirname, '..', 'migrations', '*{.ts,.js}')],
    subscribers: [join(__dirname, '..', 'subscribers', '*{.ts,.js}')],

    synchronize: false,             // CLI工具总是禁用自动同步
    migrationsRun: false,           // CLI工具手动控制迁移
    logging: !isProduction,
  };
};

/**
 * 默认数据源（用于TypeORM CLI）
 */
export default new DataSource(createDataSourceConfig());
