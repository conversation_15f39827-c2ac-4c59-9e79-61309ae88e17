# 支付配置文件
payment:
  # 支付宝配置
  alipay:
    # 应用配置
    appId: '${ALIPAY_APP_ID:-}' # 支付宝应用ID
    privateKey: '${ALIPAY_PRIVATE_KEY:-}' # 应用私钥
    publicKey: '${ALIPAY_PUBLIC_KEY:-}' # 支付宝公钥
    
    # 网关配置
    gatewayUrl: '${ALIPAY_GATEWAY_URL:-https://openapi.alipay.com/gateway.do}' # 正式环境
    sandboxGatewayUrl: 'https://openapi.alipaydev.com/gateway.do' # 沙箱环境
    
    # 回调配置
    notifyUrl: '${ALIPAY_NOTIFY_URL:-http://localhost:8088/api/v1/payments/callback/alipay}'
    returnUrl: '${ALIPAY_RETURN_URL:-http://localhost:3000/payment/return}'
    
    # 签名配置
    signType: 'RSA2' # 签名算法
    charset: 'utf-8' # 字符编码
    format: 'JSON' # 数据格式
    version: '1.0' # 接口版本
    
    # 环境配置
    sandbox: '${ALIPAY_SANDBOX:-true}' # 是否使用沙箱环境
    
    # 超时配置
    timeout: 30000 # 请求超时时间（毫秒）
    
    # 支持的支付方式
    paymentMethods:
      - 'alipay.trade.page.pay' # 电脑网站支付
      - 'alipay.trade.wap.pay' # 手机网站支付
      - 'alipay.trade.precreate' # 扫码支付
      - 'alipay.trade.app.pay' # APP支付
    
    # 费率配置（仅供参考，实际以支付宝协议为准）
    feeRate: 0.006 # 0.6%
    
  # 微信支付配置
  wechat:
    # 应用配置
    appId: '${WECHAT_APP_ID:-}' # 微信应用ID
    mchId: '${WECHAT_MCH_ID:-}' # 商户号
    apiKey: '${WECHAT_API_KEY:-}' # API密钥
    
    # 证书配置
    certPath: '${WECHAT_CERT_PATH:-./certs/wechat/apiclient_cert.pem}' # 证书文件路径
    keyPath: '${WECHAT_KEY_PATH:-./certs/wechat/apiclient_key.pem}' # 私钥文件路径
    
    # 网关配置
    gatewayUrl: '${WECHAT_GATEWAY_URL:-https://api.mch.weixin.qq.com}' # 正式环境
    sandboxGatewayUrl: 'https://api.mch.weixin.qq.com/sandboxnew' # 沙箱环境
    
    # 回调配置
    notifyUrl: '${WECHAT_NOTIFY_URL:-http://localhost:8088/api/v1/payments/callback/wechat}'
    
    # 签名配置
    signType: 'MD5' # 签名算法
    
    # 环境配置
    sandbox: '${WECHAT_SANDBOX:-true}' # 是否使用沙箱环境
    
    # 超时配置
    timeout: 30000 # 请求超时时间（毫秒）
    
    # 支持的支付方式
    paymentMethods:
      - 'JSAPI' # 公众号支付
      - 'NATIVE' # 扫码支付
      - 'APP' # APP支付
      - 'MWEB' # H5支付
    
    # 费率配置（仅供参考，实际以微信支付协议为准）
    feeRate: 0.006 # 0.6%
    
  # 银行支付配置（示例）
  bank:
    # 银行网关配置
    gatewayUrl: '${BANK_GATEWAY_URL:-https://payment.bank.com}'
    
    # 商户配置
    merchantId: '${BANK_MERCHANT_ID:-}'
    merchantKey: '${BANK_MERCHANT_KEY:-}'
    
    # 回调配置
    notifyUrl: '${BANK_NOTIFY_URL:-http://localhost:8088/api/v1/payments/callback/bank}'
    returnUrl: '${BANK_RETURN_URL:-http://localhost:3000/payment/return}'
    
    # 签名配置
    signType: 'SHA256'
    charset: 'utf-8'
    
    # 超时配置
    timeout: 30000
    
    # 费率配置
    feeRate: 0.005 # 0.5%
    
  # 通用配置
  common:
    # 默认货币
    defaultCurrency: 'CNY'
    
    # 支付超时时间（分钟）
    paymentTimeout: 30
    
    # 重试配置
    retry:
      maxAttempts: 3 # 最大重试次数
      backoffMs: 1000 # 重试间隔（毫秒）
      
    # 日志配置
    logging:
      enabled: true
      level: 'info' # debug, info, warn, error
      sensitiveFields: # 敏感字段，日志中会被脱敏
        - 'privateKey'
        - 'apiKey'
        - 'merchantKey'
        - 'password'
        
    # 安全配置
    security:
      # IP白名单（支付回调IP验证）
      ipWhitelist:
        alipay:
          - '************/24'
          - '************/24'
        wechat:
          - '*************/24'
          - '************/24'
      
      # 签名验证
      verifySignature: true
      
      # 回调验证
      verifyCallback: true
      
    # 监控配置
    monitoring:
      # 性能监控
      performance:
        enabled: true
        slowQueryThreshold: 5000 # 慢查询阈值（毫秒）
        
      # 错误监控
      error:
        enabled: true
        alertThreshold: 10 # 错误告警阈值（每小时）
        
      # 业务监控
      business:
        enabled: true
        successRateThreshold: 0.95 # 成功率告警阈值
        
    # 限流配置
    rateLimit:
      # 每个用户每分钟最大支付次数
      userPaymentPerMinute: 10
      
      # 每个IP每分钟最大支付次数
      ipPaymentPerMinute: 50
      
    # 风控配置
    riskControl:
      # 单笔支付金额限制
      singlePaymentLimit: 50000 # 500元
      
      # 每日支付金额限制
      dailyPaymentLimit: 500000 # 5000元
      
      # 异常检测
      anomalyDetection:
        enabled: true
        # 短时间内大量支付检测
        rapidPaymentThreshold: 5 # 5分钟内超过5笔
        rapidPaymentWindow: 300 # 5分钟窗口
        
# 开发环境特殊配置
development:
  payment:
    # 开发环境强制使用沙箱
    alipay:
      sandbox: true
      gatewayUrl: 'https://openapi.alipaydev.com/gateway.do'
      
    wechat:
      sandbox: true
      gatewayUrl: 'https://api.mch.weixin.qq.com/sandboxnew'
      
    # 开发环境放宽限制
    common:
      rateLimit:
        userPaymentPerMinute: 100
        ipPaymentPerMinute: 500
        
      riskControl:
        singlePaymentLimit: 1000000 # 10000元
        dailyPaymentLimit: 10000000 # 100000元
        
# 生产环境特殊配置
production:
  payment:
    # 生产环境使用正式网关
    alipay:
      sandbox: false
      gatewayUrl: 'https://openapi.alipay.com/gateway.do'
      
    wechat:
      sandbox: false
      gatewayUrl: 'https://api.mch.weixin.qq.com'
      
    # 生产环境严格限制
    common:
      security:
        verifySignature: true
        verifyCallback: true
        
      monitoring:
        performance:
          slowQueryThreshold: 3000
        error:
          alertThreshold: 5
        business:
          successRateThreshold: 0.98