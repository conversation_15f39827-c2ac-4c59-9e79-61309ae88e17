import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OcrEntity, OcrSourceType } from './entities/ocr.entity';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import * as fs from 'fs';
import * as path from 'path';
import FormData from 'form-data';
import LogisticsParser from './logisticsParser';
import { promisify } from 'util';
import sharp from 'sharp';

// 将fs的异步函数转换为Promise形式
const stat = promisify(fs.stat);

// 定义图片类型
const IMAGE_MIME_TYPES = {
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.png': 'image/png',
  '.gif': 'image/gif',
  '.bmp': 'image/bmp',
  '.webp': 'image/webp',
};

// 文件大小阈值 (1MB)
const FILE_SIZE_THRESHOLD = 1024 * 1024;

interface CallInfo {
  user?: any;
  apiKey?: any;
  serviceCode: string;
}

@Injectable()
export class OcrService {
  private readonly logger = new Logger(OcrService.name);
  private readonly ocrServiceUrl: string;

  constructor(
    @InjectRepository(OcrEntity)
    private readonly ocrRecordRepository: Repository<OcrEntity>,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly logisticsParser: LogisticsParser,
  ) {
    this.ocrServiceUrl = this.configService.get<string>('OCR_SERVICE_URL', 'http://127.0.0.1:8866/predict/ocr_system');
  }

  /**
   * 处理OCR识别请求 - 文件上传方式
   * @param file 上传的文件
   * @param callInfo 调用信息，用于记录API调用
   * @returns 识别结果
   */
  async processOcrRequest(file: any, callInfo?: CallInfo) {
    const startTime = Date.now();
    const record = new OcrEntity();
    record.originalFilename = file.originalname;
    record.filePath = file.path;
    record.sourceType = OcrSourceType.FILE;
    let processedFilePath = file.path; // 默认使用原始文件路径

    try {
      // 检查文件大小并在必要时进行压缩
      const fileStats = await stat(file.path);
      const originalSize = fileStats.size;
      
      if (originalSize > FILE_SIZE_THRESHOLD) {
        this.logger.log(`文件大小(${originalSize/1024}KB)超过阈值，进行压缩处理`);
        processedFilePath = await this.compressImage(file.path);
        
        // 记录原始大小和压缩后大小
        const compressedStats = await stat(processedFilePath);
        this.logger.log(`图片压缩完成: 从 ${originalSize/1024}KB 压缩到 ${compressedStats.size/1024}KB`);
      } else {
        this.logger.log(`文件大小(${originalSize/1024}KB)未超过阈值，不进行压缩`);
      }
      
      // 调用PaddleOCR服务进行识别
      const ocrResult = await this.callOcrService(processedFilePath);
      // console.log('ocrResult?.results:', ocrResult);
      const resData = await this.parseWaybillData(ocrResult?.results);
      // 保存识别结果
      record.ocrResult = JSON.stringify(ocrResult);
      record.isSuccess = true;
      record.processingTime = Date.now() - startTime;

      await this.ocrRecordRepository.save(record);

      // 如果创建了临时压缩文件，删除它
      if (processedFilePath !== file.path) {
        try {
          fs.unlinkSync(processedFilePath);
          this.logger.log(`已删除临时压缩文件: ${processedFilePath}`);
        } catch (err) {
          this.logger.warn(`删除临时压缩文件失败: ${err.message}`);
        }
      }

      return {
        success: true,
        data: resData,
        processingTime: record.processingTime,
      };
    } catch (error) {
      this.logger.error(`OCR识别失败: ${error.message}`, error.stack);

      // 保存错误信息
      record.isSuccess = false;
      record.errorMessage = error.message;
      record.processingTime = Date.now() - startTime;

      await this.ocrRecordRepository.save(record);

      // 如果创建了临时压缩文件，尝试删除它
      if (processedFilePath !== file.path) {
        try {
          fs.existsSync(processedFilePath) && fs.unlinkSync(processedFilePath);
        } catch (err) {
          // 忽略删除临时文件的错误
        }
      }

      return {
        success: false,
        error: error.message,
        processingTime: record.processingTime,
      };
    }
  }

  /**
   * 处理OCR识别请求 - Base64字符串方式
   * @param image_base64 Base64编码的图片数据
   * @param filename 可选的文件名
   * @param callInfo 调用信息，用于记录API调用
   * @returns 识别结果
   */
  async processOcrRequestFromBase64(image_base64: string, filename?: string, callInfo?: CallInfo) {
    const startTime = Date.now();
    const record = new OcrEntity();
    record.originalFilename = filename || 'base64-image';
    record.sourceType = OcrSourceType.BASE64;
    let tempFilePath;
    let processedFilePath;

    try {
      // 将Base64转换为临时文件
      tempFilePath = await this.saveBase64AsFile(image_base64, filename);
      record.filePath = tempFilePath;
      
      // 检查文件大小并在必要时进行压缩
      const fileStats = await stat(tempFilePath);
      const originalSize = fileStats.size;
      
      if (originalSize > FILE_SIZE_THRESHOLD) {
        this.logger.log(`Base64图片大小(${originalSize/1024}KB)超过阈值，进行压缩处理`);
        processedFilePath = await this.compressImage(tempFilePath);
        
        // 记录原始大小和压缩后大小
        const compressedStats = await stat(processedFilePath);
        this.logger.log(`Base64图片压缩完成: 从 ${originalSize/1024}KB 压缩到 ${compressedStats.size/1024}KB`);
      } else {
        this.logger.log(`Base64图片大小(${originalSize/1024}KB)未超过阈值，不进行压缩`);
        processedFilePath = tempFilePath; // 使用原始文件
      }

      // 调用PaddleOCR服务进行识别
      const ocrResult = await this.callOcrService(processedFilePath);
      const resData = await this.parseWaybillData(ocrResult?.results || []);
      
      // 保存识别结果
      record.ocrResult = JSON.stringify(ocrResult);
      record.isSuccess = true;
      record.processingTime = Date.now() - startTime;

      await this.ocrRecordRepository.save(record);

      // 清理临时文件
      this.cleanupTempFiles([tempFilePath, processedFilePath]);

      return {
        success: true,
        data: resData,
        processingTime: record.processingTime,
      };
    } catch (error) {
      this.logger.error(`OCR识别失败: ${error.message}`, error.stack);

      // 保存错误信息
      record.isSuccess = false;
      record.errorMessage = error.message;
      record.processingTime = Date.now() - startTime;

      await this.ocrRecordRepository.save(record);

      // 清理临时文件
      this.cleanupTempFiles([tempFilePath, processedFilePath]);

      return {
        success: false,
        error: error.message,
        processingTime: record.processingTime,
      };
    }
  }

  /**
   * 清理临时文件
   * @param filePaths 文件路径数组
   */
  private cleanupTempFiles(filePaths: string[]): void {
    if (!filePaths || filePaths.length === 0) return;
    
    // 过滤出不为undefined且不重复的文件路径
    const uniquePaths = [...new Set(filePaths.filter(Boolean))];
    
    for (const filePath of uniquePaths) {
      try {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          this.logger.debug(`已删除临时文件: ${filePath}`);
        }
      } catch (err) {
        this.logger.warn(`删除临时文件失败 ${filePath}: ${err.message}`);
      }
    }
  }

  /**
   * 压缩图片
   * @param filePath 原始图片路径
   * @returns 压缩后的图片路径
   */
  private async compressImage(filePath: string): Promise<string> {
    try {
      // 创建临时文件名
      const ext = path.extname(filePath).toLowerCase();
      const dir = path.dirname(filePath);
      const baseName = path.basename(filePath, ext);
      const compressedFilePath = path.join(dir, `${baseName}_compressed${ext}`);

      try {
        // 使用Sharp库进行压缩
        await this.compressWithSharp(filePath, compressedFilePath);
        this.logger.log(`使用Sharp库压缩图片成功`);
        return compressedFilePath;
      } catch (err) {
        this.logger.warn(`Sharp库压缩失败: ${err.message}`);
        
        // 备选方案：使用Node.js内置方法进行基本复制
        await this.compressWithNodeJs(filePath, compressedFilePath);
        this.logger.log(`使用文件复制替代压缩完成`);
        return compressedFilePath;
      }
    } catch (error) {
      this.logger.error(`压缩图片失败: ${error.message}`, error.stack);
      // 如果压缩失败，返回原始文件路径
      return filePath;
    }
  }

  /**
   * 使用sharp库压缩图片
   */
  private async compressWithSharp(inputPath: string, outputPath: string): Promise<void> {
    // 根据文件扩展名确定输出格式
    const ext = path.extname(outputPath).toLowerCase();
    
    let pipeline = sharp(inputPath)
      .resize({
        width: 1800,
        height: 1800,
        fit: 'inside',
        withoutEnlargement: true
      });
    
    // 根据输出文件类型设置不同的压缩选项
    if (ext === '.jpg' || ext === '.jpeg') {
      pipeline = pipeline.jpeg({ quality: 75 });
    } else if (ext === '.png') {
      pipeline = pipeline.png({ quality: 75 });
    } else if (ext === '.webp') {
      pipeline = pipeline.webp({ quality: 75 });
    } else {
      // 对于其他格式，使用默认处理
      this.logger.warn(`未知图片格式 ${ext}，使用默认处理`);
    }
    
    await pipeline.toFile(outputPath);
  }

  /**
   * 使用Node.js内置方法进行基本图片复制
   * 注意：这只是简单的文件复制，没有实际压缩，仅作为备用方案
   */
  private async compressWithNodeJs(inputPath: string, outputPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const readStream = fs.createReadStream(inputPath);
      const writeStream = fs.createWriteStream(outputPath);
      
      readStream.on('error', reject);
      writeStream.on('error', reject);
      writeStream.on('finish', resolve);
      
      readStream.pipe(writeStream);
    });
  }

  /**
   * 解析OCR识别结果为结构化物流数据
   * @param ocrResults OCR识别原始结果
   * @returns 结构化物流数据
   */
  parseWaybillData(ocrResults: any[]): any {
    try {
      this.logger.debug(`开始解析OCR结果, 共${ocrResults?.length || 0}条记录`);
      
      // 1. 数据清洗：过滤有效文本（置信度>0.5且为字符串）
      const validTexts = ocrResults?.filter((item) => 
        item[1] && 
        typeof item[1] === 'string' && 
        item[2] > 0.5 && 
        (item[1].length >= 3 || /^[A-Z0-9-]{4,}$/i.test(item[1]))
      ).map((item) => item[1]) || [];
      
      this.logger.debug(`过滤后有效文本数量: ${validTexts.length}`);
      
      if (validTexts.length === 0) {
        this.logger.warn('OCR结果中没有有效文本，无法进行物流单解析');
        return {
          logisticsCompany: '未知',
          bestCode: '',
          trackingNumber: '',
          mailNetwork: '',
          sender: { name: '', phone: '', address: '' },
          receiver: { name: '', phone: '', address: '' }
        };
      }
      
      // 2. 使用LogisticsParser一次性解析所有数据
      const parsedResult = this.logisticsParser.extract(validTexts);
      this.logger.debug(`物流解析结果: ${JSON.stringify(parsedResult)}`);
      
      // 3. 构建结果数据结构
      const result: any = {
        logisticsCompany: parsedResult?.company || '未知',
        bestCode: parsedResult?.bestCode || '',
        trackingNumber: parsedResult?.trackingNumber || '',
        mailNetwork: parsedResult?.mailNetwork || '',
        sender: parsedResult?.sender || { name: '', phone: '', address: '' },
        receiver: parsedResult?.receiver || { name: '', phone: '', address: '' }
      };
      
      // 4. 格式化数据结构 - 将phones数组转换为单个phone字段
      if (result.sender && result.sender.phones && result.sender.phones.length > 0) {
        result.sender.phone = result.sender.phones[0];
      }
      
      if (result.receiver && result.receiver.phones && result.receiver.phones.length > 0) {
        result.receiver.phone = result.receiver.phones[0];
      }
      
      this.logger.debug(`物流数据解析完成: ${JSON.stringify(result).substring(0, 200)}...`);
      return result;
    } catch (error) {
      this.logger.error(`解析物流数据失败: ${error.message}`, error.stack);
      return {
        error: `解析物流数据失败: ${error.message}`,
        logisticsCompany: '未知',
        bestCode: '',
        trackingNumber: '',
        mailNetwork: '',
        sender: { name: '', phone: '', address: '' },
        receiver: { name: '', phone: '', address: '' }
      };
    }
  }

  /**
   * 检测签收时间
   * @param texts 文本数组
   * @returns 格式化后的时间字符串 (YYYY-MM-DD HH:mm)
   */
  private detectSignTime(texts: string[]): string | null {
    // 1. 定义时间匹配模式（优先级从高到低）
    const timePatterns = [
      // 精确格式：2025/03/25 16:26
      /(\d{4}[\/-]\d{1,2}[\/-]\d{1,2}\s+\d{1,2}:\d{2})/,
      // 日期格式：2025-03-25 或 2025/03/25
      /(\d{4}[\/-]\d{1,2}[\/-]\d{1,2})/,
      // 中文格式：3月25日16时26分
      /(\d{1,2}月\d{1,2}日\d{1,2}时\d{1,2}分)/,
      // 打印时间标签：打印时间：03/25 16:26
      /打印时间[:：]\s*(\d{1,2}[\/-]\d{1,2}\s+\d{1,2}:\d{2})/,
    ];

    // 2. 遍历匹配
    for (const pattern of timePatterns) {
      for (const text of texts) {
        const match = text.match(pattern);
        if (match) {
          try {
            return this.normalizeTimeString(match[1]);
          } catch (e) {
            this.logger.warn(`时间格式转换失败: ${match[1]}`, e);
          }
        }
      }
    }

    // 3. 尝试从"签收人/签收时间"文本提取
    const signText = texts.find((t) => t.includes('签收时间'));
    if (signText) {
      const timePart = signText.split(/签收时间[:：]?/)[1]?.trim();
      if (timePart) return this.normalizeTimeString(timePart);
    }

    return null;
  }

  /**
   * 标准化时间字符串
   * @param rawTime 原始时间文本
   * @returns 统一格式的时间字符串
   */
  private normalizeTimeString(rawTime: string): string {
    // 处理中文日期（3月25日16时 → 03-25 16:00）
    let normalized = rawTime
      .replace(/(\d+)月(\d+)日/, (_, m, d) => `${m.padStart(2, '0')}-${d.padStart(2, '0')}`)
      .replace(/(\d+)时(\d+)分?/, (_, h, m) => ` ${h.padStart(2, '0')}:${(m || '00').padStart(2, '0')}`)
      .replace(/[年月]/g, '-')
      .replace(/[日号]/g, '');

    // 补全年份（如果是短格式）
    if (/^\d{1,2}-\d{1,2}/.test(normalized)) {
      const currentYear = new Date().getFullYear();
      normalized = `${currentYear}-${normalized}`;
    }

    // 确保完整时间格式
    if (!normalized.includes(':')) {
      normalized += ' 00:00';
    }

    return normalized;
  }

  /**
   * 获取OCR任务状态
   * @param jobId 任务ID
   * @returns 任务状态
   */
  async getOcrTaskStatus(jobId: string) {
    // 由于我们移除了直接对队列的依赖，这个方法需要修改
    // 我们可以通过网关服务来查询任务状态
    this.logger.warn(`getOcrTaskStatus方法已废弃，请使用网关API查询任务状态`);
    return {
      success: false,
      jobId,
      error: '此方法已废弃，请使用网关API查询任务状态',
    };
  }

  /**
   * 将Base64字符串保存为临时文件
   * @param image_base64 Base64编码的图片数据
   * @param filename 可选的文件名
   * @returns 临时文件路径
   */
  private async saveBase64AsFile(image_base64: string, filename?: string): Promise<string> {
    // 移除可能存在的Base64前缀
    // console.log('image_base64:', image_base64)
    const base64Image = image_base64.replace(/^data:image\/(png|jpg|jpeg|gif|bmp|webp);base64,/, '');

    // 创建临时文件名
    const randomName = Array(32)
      .fill(null)
      .map(() => Math.round(Math.random() * 16).toString(16))
      .join('');

    // 确定文件扩展名
    let ext = '.png';
    if (filename) {
      const match = filename.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/);
      if (match) {
        ext = match[0];
      }
    }

    // 确保上传目录存在
    const uploadDir = './up-ocr';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // 保存文件路径
    const filePath = path.join(uploadDir, `${randomName}${ext}`);

    // 将Base64数据写入文件
    fs.writeFileSync(filePath, Buffer.from(base64Image, 'base64'));

    return filePath;
  }

  /**
   * 调用PaddleOCR服务
   * @param filePath 文件路径
   * @returns OCR识别结果
   */
  private async callOcrService(filePath: string) {
    try {
      const formData = new FormData();
      
      // 使用字段名'images'，与Python服务匹配
      formData.append('images', fs.createReadStream(filePath));
      
      this.logger.debug(`正在调用OCR服务 URL: ${this.ocrServiceUrl}`);
      this.logger.debug(`使用FormData字段: images, 文件路径: ${filePath}`);
      
      const response = await firstValueFrom(
        this.httpService.post(this.ocrServiceUrl, formData, {
          headers: {
            ...formData.getHeaders(),
            'Accept': 'application/json',
          },
          maxContentLength: Infinity, // 允许大文件
          maxBodyLength: Infinity,
          timeout: 60000, // 增加超时时间到60秒
        }),
      );

      this.logger.debug(`OCR服务返回结果: ${JSON.stringify(response.data).substring(0, 300)}...`);
      
      return response.data;
    } catch (error) {
      this.logger.error(`调用OCR服务失败: ${error.message}`, error.stack);
      throw new Error(`调用OCR服务失败: ${error.message}`);
    }
  }

  /**
   * 获取OCR记录列表
   */
  async findAll(page = 1, limit = 10) {
    const [items, total] = await this.ocrRecordRepository.findAndCount({
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      items,
      total,
      page,
      limit,
    };
  }

  /**
   * 获取单条OCR记录
   */
  async findOne(id: number) {
    const record = await this.ocrRecordRepository.findOne({ where: { id } });
    if (!record) {
      throw new HttpException('记录不存在', HttpStatus.NOT_FOUND);
    }
    return record;
  }
}
