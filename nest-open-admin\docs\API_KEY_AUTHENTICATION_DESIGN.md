﻿# API密钥认证设计

## 双密钥认证机制

本系统采用双密钥认证机制（X-API-KEY和X-SECRET-KEY），这种设计提供了更高级别的安全性和灵活性。


### 为什么使用两个密钥？

1. **增强安全性**：双密钥认证类似于用户名和密码的组合，单一密钥被截获的风险更高。
2. **职责分离**：
   - X-API-KEY：用于识别API调用者的身份
   - X-SECRET-KEY：用于验证调用者的合法性
3. **增强可审计性**：API密钥可以在日志中安全记录，而Secret Key永远不会被记录，提高了审计追踪的安全性。
4. **灵活的权限管理**：可以为同一个API密钥分配不同的Secret Key，实现更细粒度的访问控制。


### 认证流程

1. 客户端在请求头中同时提供X-API-KEY和X-SECRET-KEY`n2. 系统首先验证API密钥的格式和有效性
3. 如果API密钥有效，系统会检查对应的Secret Key是否匹配
4. 验证通过后，系统会检查API密钥的权限和状态
5. 所有检查都通过后，请求被授权处理


### 技术实现

1. API密钥和Secret Key都以哈希形式存储在数据库中
2. API密钥信息（不包括Secret Key的哈希）被缓存在Redis中，提高认证性能
3. 认证守卫（UnifiedAuthGuard）负责从请求中提取和验证密钥
4. 验证过程采用熔断器模式，防止认证服务被过载

### 最佳实践

1. **API密钥轮换**：定期更新API密钥和Secret Key
2. **最小权限原则**：为每个API密钥只分配必要的权限
3. **监控异常使用**：监控API密钥的使用模式，检测潜在的安全威胁
4. **安全传输**：始终通过HTTPS传输API密钥和Secret Key
