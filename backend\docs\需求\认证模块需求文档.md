# 认证模块需求文档

## 1. 文档概述

本文档描述了开放平台认证模块的需求规范，该模块基于单一职责原则设计，专注于用户认证、注册、登录和授权管理。认证模块作为平台安全体系的核心组件，与用户模块、共享服务模块等紧密协作，提供统一的身份验证和授权服务。认证模块不直接实现认证守卫、JWT策略等功能，而是利用系统已有的全局认证基础设施。

## 2. 功能需求

### 2.1 用户注册

#### 2.1.1 邮箱注册
- **流程**：
  1. 用户输入邮箱地址
  2. 调用共享模块的邮件服务发送验证码
  3. 调用共享模块的验证码服务进行拖拽滑动图形轨迹验证
  4. 用户输入验证码和密码
  5. 验证验证码有效性
  6. 调用用户模块创建用户实体
  7. 调用密钥模块创建并关联API密钥
  8. 生成JWT令牌并返回

#### 2.1.2 手机号注册
- **流程**：
  1. 用户输入手机号
  2. 调用共享模块的短信服务发送验证码
  3. 调用共享模块的验证码服务进行拖拽滑动图形轨迹验证
  4. 用户输入验证码和密码
  5. 验证验证码有效性
  6. 调用用户模块创建用户实体
  7. 调用密钥模块创建并关联API密钥
  8. 生成JWT令牌并返回

### 2.2 用户登录

#### 2.2.1 邮箱验证码登录
- **流程**：
  1. 用户输入邮箱地址
  2. 调用共享模块的邮件服务发送验证码
  3. 调用共享模块的验证码服务进行拖拽滑动图形轨迹验证
  4. 用户输入验证码
  5. 验证验证码有效性
  6. 调用用户模块查询用户信息
  7. 更新用户登录信息（时间、IP等）
  8. 生成JWT令牌并返回

#### 2.2.2 手机号验证码登录
- **流程**：
  1. 用户输入手机号
  2. 调用共享模块的短信服务发送验证码
  3. 调用共享模块的验证码服务进行拖拽滑动图形轨迹验证
  4. 用户输入验证码
  5. 验证验证码有效性
  6. 调用用户模块查询用户信息
  7. 更新用户登录信息（时间、IP等）
  8. 生成JWT令牌并返回

#### 2.2.3 手机号快捷登录（静默注册）
- **流程**：
  1. 用户输入手机号
  2. 调用共享模块的短信服务发送验证码
  3. 调用共享模块的验证码服务进行拖拽滑动图形轨迹验证
  4. 用户输入验证码
  5. 验证验证码有效性
  6. 调用用户模块查询用户信息
  7. 若用户不存在，调用用户模块创建用户并关联API密钥
  8. 更新用户登录信息（时间、IP等）
  9. 生成JWT令牌并返回

### 2.3 密码管理

#### 2.3.1 找回密码
- **流程**：
  1. 用户输入邮箱或手机号
  2. 调用共享模块发送验证码（邮件/短信）
  3. 调用共享模块的验证码服务进行拖拽滑动图形轨迹验证
  4. 用户输入验证码
  5. 验证验证码有效性
  6. 用户设置新密码
  7. 调用用户模块更新密码哈希值

#### 2.3.2 修改密码
- **流程**：
  1. 用户输入当前密码
  2. 验证当前密码正确性
  3. 用户输入新密码和确认密码
  4. 验证新密码格式和一致性
  5. 调用用户模块更新密码哈希值

### 2.4 令牌管理

#### 2.4.1 令牌生成
- 使用全局JwtService生成包含用户ID、角色等关键信息的令牌
- 设置合理的令牌有效期
- 基于CoreAuthService提供令牌管理服务

#### 2.4.2 刷新令牌
- 支持通过刷新令牌获取新的访问令牌
- 实现令牌轮换机制提高安全性

#### 2.4.3 令牌撤销
- 支持用户登出时撤销令牌
- 维护令牌黑名单

### 2.5 验证码服务集成

#### 2.5.1 图形验证码
- 集成共享模块的图形验证码生成与验证服务
- 支持防机器人攻击的验证码策略

#### 2.5.2 拖拽滑动验证
- 集成共享模块的拖拽滑动验证服务
- 分析验证轨迹防止自动化攻击

#### 2.5.3 邮件/短信验证码
- 集成共享模块的邮件和短信验证码服务
- 管理验证码发送频率和有效期

### 2.6 第三方登录

#### 2.6.1 微信登录
- **流程**：
  1. 前端获取微信授权码(code)
  2. 后端使用code和应用凭证(appid, secret)向微信服务器换取access_token和openid
  3. 使用access_token和openid获取用户信息
  4. 根据openid查询系统中是否已存在关联用户
  5. 若已存在关联用户，则更新用户信息并生成JWT令牌
  6. 若不存在关联用户，则创建新用户、绑定openid并生成JWT令牌

#### 2.6.2 其他第三方登录(支付宝、GitHub等)
- **流程**：
  1. 类似微信登录流程，使用OAuth2.0标准流程实现
  2. 支持不同第三方账号绑定到同一个系统用户
  3. 支持账号解绑操作

## 3. 技术规范

### 3.1 数据传输对象(DTO)

#### 3.1.1 注册DTO

```typescript
export class RegisterDto {
  @IsOptional()
  @IsEmail()
  @ApiPropertyOptional({ description: '邮箱' })
  email?: string;
  
  @IsOptional()
  @IsPhoneNumber('CN')
  @ApiPropertyOptional({ description: '手机号' })
  phone?: string;
  
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: '密码' })
  password: string;
  
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: '确认密码' })
  confirmPassword: string;
  
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: '邮箱验证码' })
  emailCode?: string;
  
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: '短信验证码' })
  smsCode?: string;
  
  @IsObject()
  @ApiProperty({ description: '安全验证数据' })
  securityVerification: {
    level: number;
    behaviorPattern: any;
    verificationTimestamp: number;
  };
  
  @IsOptional()
  @IsEnum(UserType)
  @ApiPropertyOptional({ description: '用户类型', enum: UserType })
  userType?: UserType;
  
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: '用户昵称' })
  nickname?: string;
}
```

#### 3.1.2 登录DTO

```typescript
export class LoginDto {
  @IsOptional()
  @IsEmail()
  @ApiPropertyOptional({ description: '邮箱' })
  email?: string;
  
  @IsOptional()
  @IsPhoneNumber('CN')
  @ApiPropertyOptional({ description: '手机号' })
  phone?: string;
  
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: '密码' })
  password?: string;
  
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: '邮箱验证码' })
  emailCode?: string;
  
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: '短信验证码' })
  smsCode?: string;
  
  @IsObject()
  @ApiProperty({ description: '安全验证数据' })
  securityVerification: {
    level: number;
    behaviorPattern: any;
    verificationTimestamp: number;
  };
  
  @IsOptional()
  @IsBoolean()
  @ApiPropertyOptional({ description: '是否为快捷登录(静默注册)' })
  isSilentRegister?: boolean;
}
```

#### 3.1.3 第三方登录DTO

```typescript
// 微信登录DTO
export class WechatLoginDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: '微信授权码' })
  code: string;
}

// OAuth账号绑定DTO
export class OAuthBindDto {
  @IsEnum(ThirdPartyType)
  @ApiProperty({ description: '第三方平台类型', enum: ThirdPartyType })
  providerType: ThirdPartyType;
  
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: '授权码' })
  code: string;
}

// OAuth账号解绑DTO
export class OAuthUnbindDto {
  @IsEnum(ThirdPartyType)
  @ApiProperty({ description: '第三方平台类型', enum: ThirdPartyType })
  providerType: ThirdPartyType;
}
```

### 3.2 API接口定义

#### 3.2.1 用户注册

```
POST /auth/register
Content-Type: application/json

Request:
{
  "email": "<EMAIL>",
  "password": "Password123",
  "confirmPassword": "Password123",
  "emailCode": "123456",
  "securityVerification": {
    "level": 2,
    "behaviorPattern": { ... },
    "verificationTimestamp": 1625097600000
  },
  "nickname": "用户昵称"
}

Response:
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "userId": 123,
    "email": "<EMAIL>",
    "nickname": "用户昵称",
    "token": "eyJhb...", // JWT令牌
    "apiKeys": [{
      "id": "key_123456",
      "name": "默认密钥",
      "createdAt": "2023-10-01T08:00:00Z"
    }]
  }
}
```

#### 3.2.2 用户登录

```
POST /auth/login
Content-Type: application/json

Request:
{
  "email": "<EMAIL>",
  "emailCode": "123456",
  "securityVerification": {
    "level": 2,
    "behaviorPattern": { ... },
    "verificationTimestamp": 1625097600000
  }
}

Response:
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "userId": 123,
    "email": "<EMAIL>",
    "nickname": "用户昵称",
    "token": "eyJhb...", // JWT令牌
    "refreshToken": "eyJyZW...", // 刷新令牌
    "expiresIn": 86400 // 令牌有效期(秒)
  }
}
```

#### 3.2.3 第三方登录

```
POST /auth/wechat/login
Content-Type: application/json

Request:
{
  "code": "0817YgkNhE27...12345"
}

Response:
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "userId": 123,
    "username": "wx_1234567890",
    "nickname": "微信用户",
    "token": "eyJhb...",
    "refreshToken": "eyJyZW...",
    "expiresIn": 86400,
    "isNewUser": true
  }
}
```

#### 3.2.4 绑定第三方账号

```
POST /auth/oauth/bind
Content-Type: application/json
Authorization: Bearer eyJhb...

Request:
{
  "providerType": "wechat",
  "code": "0817YgkNhE27...12345"
}

Response:
{
  "code": 200,
  "message": "微信账号绑定成功",
  "data": {
    "message": "微信账号绑定成功"
  }
}
```

## 4. 系统架构

### 4.1 模块结构

```
auth/
├── dto/
│   ├── login.dto.ts               # 登录DTO
│   ├── register.dto.ts            # 注册DTO
│   ├── refresh-token.dto.ts       # 刷新令牌DTO
│   ├── reset-password.dto.ts      # 重置密码DTO
│   ├── change-password.dto.ts     # 修改密码DTO
│   ├── captcha/
│   │   ├── send-sms-code.dto.ts   # 发送短信验证码DTO
│   │   ├── send-email-code.dto.ts # 发送邮箱验证码DTO
│   │   └── verify-code.dto.ts     # 验证验证码DTO
│   └── oauth/
│       ├── wechat-login.dto.ts    # 微信登录DTO
│       ├── oauth-bind.dto.ts      # 第三方账号绑定DTO
│       └── oauth-unbind.dto.ts    # 第三方账号解绑DTO
├── services/
│   ├── auth.service.ts            # 认证核心服务
│   └── oauth.service.ts           # 第三方认证服务
├── controllers/
│   └── auth.controller.ts         # 认证接口控制器
└── auth.module.ts                 # 认证模块定义
```

### 4.2 模块间协作关系

```mermaid
graph TD
    Client[客户端] -- "API请求" --> Auth[认证模块]
    Auth -- "创建/查询用户" --> User[用户模块]
    Auth -- "创建API密钥" --> ApiKey[密钥模块]
    Auth -- "验证码服务" --> Shared[共享服务模块]
    Auth -- "使用认证服务" --> Core[核心认证服务]
    Shared -- "验证码生成/验证" --> Captcha[验证码服务]
    Shared -- "邮件发送" --> Email[邮件服务]
    Shared -- "短信发送" --> SMS[短信服务]
    Core -- "JWT管理" --> JWT[JWT服务]
    Core -- "缓存管理" --> Redis[Redis服务]
    
    subgraph "认证流程"
        ClientReq[客户端请求] --> VerifyCode[验证码验证]
        VerifyCode --> AuthProcess[认证处理]
        AuthProcess --> UserLookup[用户查询/创建]
        UserLookup --> TokenGen[令牌生成]
        TokenGen --> Response[响应结果]
    end
    
    subgraph "全局守卫"
        Req[请求] --> UnifiedGuard[统一认证守卫]
        UnifiedGuard -- "验证令牌" --> Core
        Core -- "验证结果" --> Handler[处理器]
    end
```

## 5. 实现要点

### 5.1 与全局认证系统集成

- **统一认证守卫**：
  - 使用全局的`UnifiedAuthGuard`而不是自定义JWT守卫
  - 使用全局的`Public`装饰器标记公开接口
  - 使用全局的`AuthPermission`装饰器管理权限

- **全局核心认证服务**：
  - 使用共享模块的`CoreAuthService`处理JWT验证
  - 使用共享模块的JwtService创建令牌
  - 使用统一的令牌生成格式和载荷

- **事件驱动通信**：
  - 使用事件发射器在认证成功/失败时通知其他模块
  - 订阅用户模块事件以响应用户状态变更

### 5.2 安全与隐私保护

- **密码安全**：
  - 使用bcrypt进行密码哈希，设置适当的盐轮数
  - 实现密码复杂度校验逻辑
  - 密码传输过程中使用HTTPS加密

- **令牌安全**：
  - 使用强密钥签名JWT令牌
  - 设置合理的令牌有效期（默认24小时）
  - 实现令牌轮换和撤销机制

- **防自动化攻击**：
  - 集成滑动验证、图形验证等多重验证机制
  - 实现请求频率限制
  - 监控异常登录行为

### 5.3 验证码管理

- **验证码策略**：
  - 邮箱/手机号60秒内只能请求1次验证码
  - 验证码有效期5分钟
  - 同一验证码最多尝试3次，超过则失效

- **验证码存储**：
  - 使用Redis缓存验证码
  - 使用唯一标识符关联验证码
  - 定期清理过期验证码

### 5.4 登录失败处理

- **账号锁定机制**：
  - 连续5次登录失败，锁定账号15分钟
  - 锁定期间拒绝所有登录尝试
  - 提供锁定状态查询API

- **风险控制**：
  - 记录登录IP和设备指纹
  - 检测异常登录地点和设备
  - 对可疑行为增加额外验证

### 5.5 第三方账号管理

- **安全绑定**：
  - 确保一个第三方账号只能绑定一个平台用户
  - 绑定前验证用户身份和第三方账号有效性
  - 保存必要的令牌信息用于后续API调用

- **统一身份**：
  - 将第三方账号身份映射到平台统一用户体系
  - 支持多个第三方账号绑定到同一平台用户
  - 提供解绑机制，确保用户账号自主管理

## 6. 配置项

```yaml
# 认证模块配置项
auth:
  jwt:
    secret: ${JWT_SECRET}
    refreshSecret: ${JWT_REFRESH_SECRET}
    expiresIn: 86400 # 24小时
    refreshExpiresIn: 604800 # 7天
  security:
    rateLimit:
      loginAttempts: 5 # 最大尝试次数
      lockDuration: 900 # 锁定时长(秒)
    verification:
      codeLength: 6 # 验证码长度
      codeExpiry: 300 # 验证码有效期(秒)
      cooldown: 60 # 发送冷却时间(秒)
      maxAttempts: 3 # 最大尝试次数
  passwordPolicy:
    minLength: 8
    requireLowercase: true
    requireUppercase: true
    requireNumbers: true
    requireSpecialChars: true
  features:
    silentRegister: true # 是否启用静默注册
    multiFactorAuth: false # 是否启用多因素认证
  oauth:
    wechat:
      appId: ${WECHAT_APPID}
      appSecret: ${WECHAT_SECRET}
    alipay:
      appId: ${ALIPAY_APPID}
      appSecret: ${ALIPAY_SECRET}
    github:
      clientId: ${GITHUB_CLIENT_ID}
      clientSecret: ${GITHUB_CLIENT_SECRET}
    google:
      clientId: ${GOOGLE_CLIENT_ID}
      clientSecret: ${GOOGLE_CLIENT_SECRET}
```

## 7. 扩展点

### 7.1 多因素认证

- 支持TOTP时间基础一次性密码
- 支持SMS作为第二因素认证
- 可配置的多因素认证策略

### 7.2 OAuth2.0集成

- 支持更多第三方平台登录(Facebook、Twitter等)
- 统一的OAuth提供商适配器
- 第三方账号与平台账号关联

### 7.3 高级安全特性

- 基于机器学习的行为异常检测
- 自适应认证策略
- 地理位置感知的安全规则

## 8. 错误处理

- 统一的错误码定义与国际化错误消息
- 详细的错误日志记录
- 对客户端友好的错误提示
- 敏感错误信息的安全处理 