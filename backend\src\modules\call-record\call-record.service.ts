import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, Between, LessThan } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { CallRecordEntity } from './entities/call-record.entity';
import { CallStatisticsEntity } from './entities/call-statistics.entity';
import {
  CreateCallRecordDto,
  CallRecordQueryDto,
  CallRecordListResponseDto,
  CallRecordResponseDto,
  CallStatisticsQueryDto,
  CallStatisticsResponseDto,
  CallStatisticsDto,
  CallStatisticsSummaryDto,
} from './dto';
import { CallStatus } from './enums/call-status.enum';

@Injectable()
export class CallRecordService {
  private readonly logger = new Logger(CallRecordService.name);

  constructor(
    @InjectRepository(CallRecordEntity)
    private callRecordRepository: Repository<CallRecordEntity>,
    @InjectRepository(CallStatisticsEntity)
    private callStatisticsRepository: Repository<CallStatisticsEntity>,
    private dataSource: DataSource,
    private configService: ConfigService,
  ) {}

  /**
   * 创建调用记录
   * @param createCallRecordDto 创建调用记录DTO
   * @returns 创建的调用记录
   */
  async create(createCallRecordDto: CreateCallRecordDto): Promise<CallRecordResponseDto> {
    try {
      const callRecord = this.callRecordRepository.create({
        ...createCallRecordDto,
      });
      
      const saved = await this.callRecordRepository.save(callRecord);
      return new CallRecordResponseDto(saved);
    } catch (error) {
      this.logger.error(
        `创建调用记录失败: ${error.message}`,
        error.stack,
      );
      
      // 确保调用记录失败不影响主业务流程
      throw new BadRequestException(`创建调用记录失败: ${error.message}`);
    }
  }

  /**
   * 查询调用记录列表
   * @param queryDto 查询参数
   * @returns 调用记录列表和分页信息
   */
  async findAll(queryDto: CallRecordQueryDto): Promise<CallRecordListResponseDto> {
    const {
      page = 1,
      limit = 10,
      userId,
      serviceId,
      status,
      requestId,
      apiKeyId,
      startDate,
      endDate,
      minDuration,
      maxDuration,
      ipAddress,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = queryDto;
    
    const queryBuilder = this.callRecordRepository.createQueryBuilder('record');
    
    // 添加过滤条件
    if (userId) {
      queryBuilder.andWhere('record.userId = :userId', { userId });
    }
    
    if (serviceId) {
      queryBuilder.andWhere('record.serviceId = :serviceId', { serviceId });
    }
    
    if (status) {
      queryBuilder.andWhere('record.status = :status', { status });
    }
    
    if (requestId) {
      queryBuilder.andWhere('record.requestId = :requestId', { requestId });
    }
    
    if (apiKeyId) {
      queryBuilder.andWhere('record.apiKeyId = :apiKeyId', { apiKeyId });
    }
    
    if (startDate) {
      queryBuilder.andWhere('record.createdAt >= :startDate', { 
        startDate: new Date(startDate) 
      });
    }
    
    if (endDate) {
      queryBuilder.andWhere('record.createdAt <= :endDate', { 
        endDate: new Date(endDate) 
      });
    }
    
    if (minDuration !== undefined) {
      queryBuilder.andWhere('record.duration >= :minDuration', { minDuration });
    }
    
    if (maxDuration !== undefined) {
      queryBuilder.andWhere('record.duration <= :maxDuration', { maxDuration });
    }
    
    if (ipAddress) {
      queryBuilder.andWhere('record.ipAddress = :ipAddress', { ipAddress });
    }
    
    // 添加排序
    queryBuilder.orderBy(`record.${sortBy}`, sortOrder as 'ASC' | 'DESC');
    
    // 添加分页
    queryBuilder
      .skip((page - 1) * limit)
      .take(limit);
    
    // 执行查询
    const [records, total] = await queryBuilder.getManyAndCount();
    
    return new CallRecordListResponseDto(records, total, page, limit);
  }

  /**
   * 根据ID查询调用记录
   * @param id 调用记录ID
   * @returns 调用记录
   */
  async findOne(id: string): Promise<CallRecordResponseDto> {
    const record = await this.callRecordRepository.findOne({ where: { id } });
    
    if (!record) {
      throw new NotFoundException(`ID为${id}的调用记录不存在`);
    }
    
    return new CallRecordResponseDto(record);
  }

  /**
   * 查询用户服务调用记录
   * @param userId 用户ID
   * @param serviceId 服务ID
   * @param queryParams 查询参数
   * @returns 调用记录和分页信息
   */
  async getUserServiceCallRecords(
    userId: number,
    serviceId: number,
    queryParams: CallRecordQueryDto,
  ): Promise<CallRecordListResponseDto> {
    // 验证参数
    if (!userId || isNaN(Number(userId))) {
      throw new BadRequestException('用户ID必须是有效的数字');
    }
    
    if (!serviceId || isNaN(Number(serviceId))) {
      throw new BadRequestException('服务ID必须是有效的数字');
    }
    
    // 合并查询参数
    const mergedQueryParams: CallRecordQueryDto = {
      ...queryParams,
      userId: Number(userId),
      serviceId: Number(serviceId),
    };
    
    try {
      return await this.findAll(mergedQueryParams);
    } catch (error) {
      this.logger.error(
        `获取用户服务调用记录失败: ${error.message}`,
        error.stack
      );
      throw new BadRequestException(`获取用户服务调用记录失败: ${error.message}`);
    }
  }

  /**
   * 获取调用统计数据
   * @param queryDto 查询参数
   * @returns 调用统计数据
   */
  async getCallStatistics(queryDto: CallStatisticsQueryDto): Promise<CallStatisticsResponseDto> {
    const { userId, serviceId, startDate, endDate } = queryDto;
    
    const whereConditions: any = {
      date: Between(new Date(startDate), new Date(endDate)),
    };
    
    if (userId) {
      whereConditions.userId = userId;
    }
    
    if (serviceId) {
      whereConditions.serviceId = serviceId;
    }
    
    // 查询统计数据
    const statistics = await this.callStatisticsRepository.find({
      where: whereConditions,
      order: { date: 'ASC' }
    });
    
    // 转换为DTO格式
    const statsDto: CallStatisticsDto[] = statistics.map(stat => ({
      date: stat.date.toISOString().split('T')[0],
      totalCalls: stat.totalCalls,
      successCalls: stat.successCalls,
      failedCalls: stat.failedCalls,
      totalDuration: stat.totalDuration,
      avgDuration: stat.avgDuration,
    }));
    
    // 计算汇总数据
    const totalCalls = statistics.reduce((sum, stat) => sum + stat.totalCalls, 0);
    const successCalls = statistics.reduce((sum, stat) => sum + stat.successCalls, 0);
    const failedCalls = statistics.reduce((sum, stat) => sum + stat.failedCalls, 0);
    const totalDuration = statistics.reduce((sum, stat) => sum + stat.totalDuration, 0);
    
    const summary: CallStatisticsSummaryDto = {
      totalCalls,
      successCalls,
      failedCalls,
      successRate: totalCalls > 0 ? successCalls / totalCalls : 0,
      avgDuration: totalCalls > 0 ? totalDuration / totalCalls : 0,
      dateRange: {
        start: startDate,
        end: endDate,
      },
    };
    
    return {
      statistics: statsDto,
      summary,
    };
  }

  /**
   * 生成每日调用统计
   * 该方法由schedule模块的定时任务调用，每天凌晨2:00执行
   */
  async generateDailyStatistics(): Promise<void> {
    // 获取昨天的日期
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);
    
    const endDate = new Date(yesterday);
    endDate.setDate(endDate.getDate() + 1);
    
    try {
      this.logger.log(`开始生成 ${yesterday.toISOString().split('T')[0]} 的调用统计`);
      
      // 按用户和服务统计
      const stats = await this.callRecordRepository
        .createQueryBuilder('record')
        .select('record.userId', 'userId')
        .addSelect('record.serviceId', 'serviceId')
        .addSelect('COUNT(*)', 'totalCalls')
        .addSelect('SUM(CASE WHEN record.status = :successStatus THEN 1 ELSE 0 END)', 'successCalls')
        .addSelect('SUM(CASE WHEN record.status != :successStatus THEN 1 ELSE 0 END)', 'failedCalls')
        .addSelect('SUM(record.duration)', 'totalDuration')
        .addSelect('AVG(record.duration)', 'avgDuration')
        .where('record.createdAt >= :startDate', { startDate: yesterday })
        .andWhere('record.createdAt < :endDate', { endDate })
        .setParameter('successStatus', CallStatus.SUCCESS)
        .groupBy('record.userId')
        .addGroupBy('record.serviceId')
        .getRawMany();
      
      // 保存统计结果
      const entities = stats.map(stat => this.callStatisticsRepository.create({
        userId: stat.userId,
        serviceId: stat.serviceId,
        date: yesterday,
        totalCalls: parseInt(stat.totalCalls),
        successCalls: parseInt(stat.successCalls),
        failedCalls: parseInt(stat.failedCalls),
        totalDuration: parseInt(stat.totalDuration) || 0,
        avgDuration: parseFloat(stat.avgDuration) || 0
      }));
      
      if (entities.length > 0) {
        await this.callStatisticsRepository.save(entities);
      }
      
      this.logger.log(`调用统计生成完成 - 共生成 ${entities.length} 条统计记录`);
    } catch (error) {
      this.logger.error(`生成调用统计失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 清理过期调用记录
   * 该方法由schedule模块的定时任务调用，每两个月执行一次
   */
  async cleanupCallRecords(): Promise<void> {
    try {
      // 计算截止日期（2个月前）
      const cutoffDate = new Date();
      cutoffDate.setMonth(cutoffDate.getMonth() - 2);
      cutoffDate.setDate(1);
      cutoffDate.setHours(0, 0, 0, 0);
      
      this.logger.log(`开始清理调用记录 - 清理 ${cutoffDate.toISOString().split('T')[0]} 之前的记录`);
      
      // 批量删除数据，避免锁表
      const batchSize = this.configService.get<number>('callRecords.cleanupBatchSize', 10000);
      let totalDeleted = 0;
      let batchDeleted = 0;
      
      do {
        // 先查询要删除的记录ID
        const recordsToDelete = await this.callRecordRepository
          .createQueryBuilder('record')
          .select('record.id')
          .where('record.createdAt < :cutoffDate', { cutoffDate })
          .limit(batchSize)
          .getMany();
        
        if (recordsToDelete.length === 0) {
          break;
        }
        
        const ids = recordsToDelete.map(record => record.id);
        
        // 然后删除这些记录
        const result = await this.callRecordRepository
          .createQueryBuilder()
          .delete()
          .where('id IN (:...ids)', { ids })
          .execute();
        
        batchDeleted = result.affected || 0;
        totalDeleted += batchDeleted;
        
        // 每批次间隔，减少数据库压力
        if (batchDeleted > 0) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } while (batchDeleted > 0);
      
      this.logger.log(`调用记录清理完成 - 共删除 ${totalDeleted} 条记录`);
    } catch (error) {
      this.logger.error(`调用记录清理失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取API密钥的使用统计
   * @param apiKeyId API密钥ID
   * @returns 使用统计数据
   */
  async getApiKeyUsageStats(apiKeyId: string): Promise<{
    todayCalls: number;
    monthlyCalls: number;
    totalCalls: number;
  }> {
    try {
      const now = new Date();
      const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000 - 1);
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

      // 今日调用次数
      const todayCalls = await this.callRecordRepository.count({
        where: {
          apiKeyId,
          createdAt: Between(todayStart, todayEnd)
        }
      });

      // 本月调用次数
      const monthlyCalls = await this.callRecordRepository.count({
        where: {
          apiKeyId,
          createdAt: Between(monthStart, monthEnd)
        }
      });

      // 总调用次数
      const totalCalls = await this.callRecordRepository.count({
        where: {
          apiKeyId
        }
      });

      return {
        todayCalls,
        monthlyCalls,
        totalCalls
      };
    } catch (error) {
      this.logger.warn(`获取API密钥使用统计失败: ${error.message}`);
      return {
        todayCalls: 0,
        monthlyCalls: 0,
        totalCalls: 0
      };
    }
  }
}
