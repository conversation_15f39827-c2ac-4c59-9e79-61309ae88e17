// 测试TaskService的getTaskStatus方法
const Redis = require('ioredis');

async function testTaskService() {
  console.log('=== 测试TaskService.getTaskStatus方法 ===');
  
  const redis = new Redis({
    host: 'localhost',
    port: 6379,
    password: '',
    db: 0,
    keyPrefix: '',
  });

  const taskId = '05d6de1d-c760-4d1a-8345-b47b327e6e89';
  
  try {
    console.log(`1. 测试Redis hgetall方法...`);
    const task = await redis.hgetall(`task:${taskId}`);
    console.log('   Redis原始数据:', task);
    
    if (!task || Object.keys(task).length === 0) {
      console.log('   ❌ 任务不存在');
      return;
    }
    
    console.log('\n2. 模拟TaskService.getTaskStatus方法...');
    
    const result = {
      taskId,
      status: task.status,
      type: task.type,
      createdAt: parseInt(task.createdAt),
    };
    
    if (task.progress) {
      result.progress = parseFloat(task.progress);
    }
    
    if (task.message) {
      result.message = task.message;
    }
    
    if (task.error) {
      result.error = task.error;
    }
    
    if (task.result) {
      try {
        result.result = JSON.parse(task.result);
      } catch (e) {
        result.result = task.result;
      }
    }
    
    if (task.updatedAt) {
      result.updatedAt = parseInt(task.updatedAt);
    }
    
    console.log('   ✅ 解析后的任务状态:', result);
    
    console.log('\n3. 检查状态转换...');
    console.log(`   原始状态: ${task.status}`);
    console.log(`   是否为completed: ${task.status === 'completed'}`);
    console.log(`   是否为failed: ${task.status === 'failed'}`);
    console.log(`   是否为timeout: ${task.status === 'timeout'}`);
    
    console.log('\n4. 检查结果数据...');
    if (task.result) {
      try {
        const parsedResult = JSON.parse(task.result);
        console.log('   解析后的结果:', parsedResult);
      } catch (e) {
        console.log('   结果解析失败:', e.message);
        console.log('   原始结果:', task.result);
      }
    }
    
    console.log('\n✅ TaskService.getTaskStatus方法测试完成');
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
  } finally {
    await redis.quit();
  }
}

testTaskService().catch(console.error);
