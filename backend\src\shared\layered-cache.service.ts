import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { RedisService } from './redis.service';
import { ConfigService } from '@nestjs/config';
import LRU from 'lru-cache';

/**
 * 缓存选项接口
 */
export interface CacheOptions {
  ttl?: number; // 过期时间（秒）
  localOnly?: boolean; // 是否只使用本地缓存
  redisOnly?: boolean; // 是否只使用Redis缓存
  localTtl?: number; // 本地缓存过期时间（秒）
}

/**
 * 分层缓存服务
 * 结合本地内存缓存和Redis缓存，提供更高效的缓存策略
 */
@Injectable()
export class LayeredCacheService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(LayeredCacheService.name);
  private localCache: LRU<string, any>;
  private readonly defaultTtl: number;
  private readonly defaultLocalTtl: number;
  private readonly maxLocalItems: number;
  private syncInterval: NodeJS.Timeout;

  constructor(
    private readonly redisService: RedisService,
    private readonly configService: ConfigService,
  ) {
    // 从配置中读取缓存参数，或使用默认值
    this.defaultTtl = this.configService.get<number>('cache.defaultTtl', 3600); // 默认1小时
    this.defaultLocalTtl = this.configService.get<number>('cache.defaultLocalTtl', 300); // 默认5分钟，避免频繁过期
    this.maxLocalItems = this.configService.get<number>('cache.maxLocalItems', 1000);
  }

  onModuleInit() {
    // 初始化本地LRU缓存 - 适配v7 API
    this.localCache = new LRU({
      max: this.maxLocalItems,
      ttl: this.defaultLocalTtl * 1000, // 转换为毫秒
      updateAgeOnGet: true, // 读取时更新过期时间
      dispose: (key, value) => {
        const keyStr = typeof key === 'string' ? key : JSON.stringify(key);
        this.logger.verbose(`本地缓存项过期: ${keyStr}`);
      }
    });

    // 定期从Redis同步热点数据到本地缓存
    const syncIntervalMs = this.configService.get<number>('cache.syncInterval', 30000); // 默认30秒
    this.syncInterval = setInterval(() => this.syncHotKeysFromRedis(), syncIntervalMs);
    
    this.logger.log(`分层缓存服务已启动，本地缓存容量: ${this.maxLocalItems}项，同步间隔: ${syncIntervalMs}ms`);
  }

  onModuleDestroy() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    this.logger.log('分层缓存服务已关闭');
  }

  /**
   * 设置缓存
   * @param key 缓存键
   * @param value 缓存值
   * @param options 缓存选项
   */
  async set(key: string, value: any, options: CacheOptions = {}): Promise<void> {
    const ttl = options.ttl || this.defaultTtl;
    const localTtl = options.localTtl || this.defaultLocalTtl;
    
    try {
      // 序列化值
      const serializedValue = JSON.stringify(value);
      
      // 设置本地缓存（除非指定只使用Redis）
      if (!options.redisOnly) {
        // 使用v7 API设置缓存
        this.localCache.set(key, value, { ttl: localTtl * 1000 });
      }
      
      // 设置Redis缓存（除非指定只使用本地缓存）
      if (!options.localOnly) {
        await this.redisService.setex(key, ttl, serializedValue);
      }
    } catch (error) {
      this.logger.error(`设置缓存失败: ${key}, ${error.message}`, error.stack);
      
      // 如果Redis失败但未指定只使用Redis，仍然尝试设置本地缓存
      if (!options.redisOnly && !options.localOnly) {
        this.localCache.set(key, value, { ttl: localTtl * 1000 });
      }
    }
  }

  /**
   * 获取缓存
   * @param key 缓存键
   * @param options 缓存选项
   * @returns 缓存值，不存在则返回null
   */
  async get<T>(key: string, options: CacheOptions = {}): Promise<T | null> {
    try {
      // 先检查本地缓存（除非指定只使用Redis）
      if (!options.redisOnly) {
        const localValue = this.localCache.get(key) as T;
        if (localValue !== undefined) {
          this.logger.debug(`本地缓存命中: ${key}`);
          return localValue;
        }
      }
      
      // 如果本地缓存未命中且未指定只使用本地缓存，则查询Redis
      if (!options.localOnly) {
        const redisValue = await this.redisService.get(key);
        if (redisValue) {
          this.logger.debug(`Redis缓存命中: ${key}`);
          
          try {
            // 解析Redis值
            const parsedValue = JSON.parse(redisValue) as T;
            
            // 更新本地缓存（除非指定只使用Redis）
            if (!options.redisOnly) {
              const localTtl = options.localTtl || this.defaultLocalTtl;
              this.localCache.set(key, parsedValue, { ttl: localTtl * 1000 });
            }
            
            return parsedValue;
          } catch (parseError) {
            this.logger.error(`解析Redis缓存值失败: ${key}, ${parseError.message}`);
            return null;
          }
        }
      }
      
      // 缓存未命中
      this.logger.debug(`缓存未命中: ${key}`);
      return null;
    } catch (error) {
      this.logger.error(`获取缓存失败: ${key}, ${error.message}`, error.stack);
      
      // 如果Redis失败但未指定只使用Redis，仍然尝试从本地缓存获取
      if (!options.redisOnly) {
        return this.localCache.get(key) as T || null;
      }
      
      return null;
    }
  }

  /**
   * 直接从Redis获取字符串值（不进行JSON解析）
   * @param key 缓存键
   * @returns 字符串值，不存在则返回null
   */
  async getRedisString(key: string): Promise<string | null> {
    try {
      // 直接从Redis获取字符串值
      const redisValue = await this.redisService.get(key);

      if (redisValue === null || redisValue === undefined) {
        this.logger.debug(`Redis字符串缓存未命中: ${key}`);
        return null;
      }

      this.logger.debug(`Redis字符串缓存命中: ${key}`);
      return redisValue;
    } catch (error) {
      this.logger.error(`获取Redis字符串缓存失败: ${key}, ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 直接设置Redis字符串值（不进行JSON序列化）
   * @param key 缓存键
   * @param value 字符串值
   * @param ttl 过期时间（秒）
   */
  async setRedisString(key: string, value: string, ttl: number = 86400): Promise<void> {
    try {
      // 直接设置Redis字符串值
      await this.redisService.setex(key, ttl, value);

      this.logger.debug(`Redis字符串缓存已设置: ${key}, TTL=${ttl}秒`);
    } catch (error) {
      this.logger.error(`设置Redis字符串缓存失败: ${key}, ${error.message}`, error.stack);
    }
  }

  /**
   * 删除缓存
   * @param key 缓存键
   * @param options 缓存选项
   */
  async delete(key: string, options: CacheOptions = {}): Promise<void> {
    try {
      // 删除本地缓存（除非指定只使用Redis）
      if (!options.redisOnly) {
        this.localCache.delete(key);
      }
      
      // 删除Redis缓存（除非指定只使用本地缓存）
      if (!options.localOnly) {
        await this.redisService.del(key);
      }
    } catch (error) {
      this.logger.error(`删除缓存失败: ${key}, ${error.message}`, error.stack);
      
      // 如果Redis失败但未指定只使用Redis，仍然尝试删除本地缓存
      if (!options.redisOnly && !options.localOnly) {
        this.localCache.delete(key);
      }
    }
  }

  /**
   * 检查缓存是否存在
   * @param key 缓存键
   * @param options 缓存选项
   * @returns 是否存在
   */
  async exists(key: string, options: CacheOptions = {}): Promise<boolean> {
    try {
      // 先检查本地缓存（除非指定只使用Redis）
      if (!options.redisOnly && this.localCache.has(key)) {
        return true;
      }
      
      // 如果本地缓存不存在且未指定只使用本地缓存，则查询Redis
      if (!options.localOnly) {
        // RedisService.exists已经返回布尔值
        return await this.redisService.exists(key);
      }
      
      return false;
    } catch (error) {
      this.logger.error(`检查缓存是否存在失败: ${key}, ${error.message}`, error.stack);
      
      // 如果Redis失败但未指定只使用Redis，仍然尝试检查本地缓存
      if (!options.redisOnly) {
        return this.localCache.has(key);
      }
      
      return false;
    }
  }

  /**
   * 设置缓存过期时间
   * @param key 缓存键
   * @param ttl 过期时间（秒）
   * @param options 缓存选项
   */
  async expire(key: string, ttl: number, options: CacheOptions = {}): Promise<void> {
    try {
      // 设置本地缓存过期时间（除非指定只使用Redis）
      if (!options.redisOnly) {
        const value = this.localCache.get(key);
        if (value !== undefined) {
          // 在v7中，我们需要重新设置值以更新TTL
          this.localCache.set(key, value, { ttl: ttl * 1000 });
        }
      }
      
      // 设置Redis缓存过期时间（除非指定只使用本地缓存）
      if (!options.localOnly) {
        await this.redisService.expire(key, ttl);
      }
    } catch (error) {
      this.logger.error(`设置缓存过期时间失败: ${key}, ${error.message}`, error.stack);
    }
  }

  /**
   * 获取缓存的剩余过期时间
   * @param key 缓存键
   * @returns 剩余过期时间（秒），-1表示永不过期，-2表示不存在
   */
  async ttl(key: string): Promise<number> {
    try {
      return await this.redisService.ttl(key);
    } catch (error) {
      this.logger.error(`获取缓存过期时间失败: ${key}, ${error.message}`, error.stack);
      return -2;
    }
  }

  /**
   * 增加缓存计数器
   * @param key 缓存键
   * @param increment 增量，默认为1
   * @returns 增加后的值
   */
  async increment(key: string, increment: number = 1): Promise<number> {
    try {
      // 增加Redis计数器
      const newValue = await this.redisService.incrby(key, increment);
      
      // 更新本地缓存
      this.localCache.set(key, newValue);
      
      return newValue;
    } catch (error) {
      this.logger.error(`增加缓存计数器失败: ${key}, ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 减少缓存计数器
   * @param key 缓存键
   * @param decrement 减量，默认为1
   * @returns 减少后的值
   */
  async decrement(key: string, decrement: number = 1): Promise<number> {
    try {
      // 减少Redis计数器
      const newValue = await this.redisService.decrby(key, decrement);
      
      // 更新本地缓存
      this.localCache.set(key, newValue);
      
      return newValue;
    } catch (error) {
      this.logger.error(`减少缓存计数器失败: ${key}, ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 从Redis同步热点数据到本地缓存
   */
  private async syncHotKeysFromRedis(): Promise<void> {
    try {
      // 获取热点键列表（例如从Redis的访问统计中）
      const hotKeys = await this.getHotKeys();
      
      if (hotKeys.length === 0) {
        return;
      }
      
      this.logger.verbose(`同步热点键: ${hotKeys.length}个`);
      
      // 批量获取热点键的值和TTL
      const pipeline = this.redisService.pipeline();
      
      for (const key of hotKeys) {
        pipeline.get(key);
        pipeline.ttl(key);
      }
      
      const results = await pipeline.exec();
      
      if (!results) {
        return;
      }
      
      // 更新本地缓存
      for (let i = 0; i < hotKeys.length; i++) {
        const key = hotKeys[i];
        // 处理pipeline.exec()返回的结果
        // 每个结果是一个[error, result]数组
        const valueResult = results[i * 2];
        const ttlResult = results[i * 2 + 1];
        
        if (!valueResult || !ttlResult || valueResult[0] || ttlResult[0]) {
          // 如果有错误，跳过这个键
          continue;
        }
        
        const value = valueResult[1] as string | null;
        const ttl = ttlResult[1] as number;
        
        if (value && ttl > 0) {
          try {
            const parsedValue = JSON.parse(value);
            const localTtl = Math.min(ttl, this.defaultLocalTtl);
            
            this.localCache.set(key, parsedValue, { ttl: localTtl * 1000 });
          } catch (error) {
            this.logger.error(`解析热点键值失败: ${key}, ${error.message}`);
          }
        }
      }
    } catch (error) {
      this.logger.error(`同步热点键失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取热点键列表
   * @returns 热点键列表
   */
  private async getHotKeys(): Promise<string[]> {
    try {
      // 这里可以实现自己的热点键获取逻辑
      // 例如：从Redis的访问统计中获取，或者使用Redis的OBJECT FREQ命令
      
      // 简单示例：从预定义的热点键模式中获取
      const hotKeyPatterns = [
        'api_key:*',
        'user:*',
        'service:*',
        'config:*'
      ];
      
      const keys: string[] = [];
      
      // 对每个模式执行SCAN命令
      for (const pattern of hotKeyPatterns) {
        let cursor = '0';
        let scanKeys: string[] = [];
        
        // 最多获取100个键
        do {
          try {
            // 使用Redis的scan命令
            const scanResult = await this.redisService.scan(cursor, 'MATCH', pattern, 'COUNT', '20');
            
            if (Array.isArray(scanResult) && scanResult.length === 2) {
              cursor = scanResult[0];
              scanKeys = scanResult[1];
              
              keys.push(...scanKeys);
            } else {
              this.logger.error(`Scan命令返回了意外的结果格式: ${JSON.stringify(scanResult)}`);
              break;
            }
          } catch (error) {
            this.logger.error(`执行Scan命令失败: ${error.message}`);
            break;
          }
          
          // 如果已经获取足够多的键，提前退出
          if (keys.length >= this.maxLocalItems / 2) {
            break;
          }
        } while (cursor !== '0' && keys.length < this.maxLocalItems / 2);
        
        // 如果已经获取足够多的键，提前退出
        if (keys.length >= this.maxLocalItems / 2) {
          break;
        }
      }
      
      return keys.slice(0, this.maxLocalItems / 2);
    } catch (error) {
      this.logger.error(`获取热点键失败: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * 清空本地缓存
   */
  clearLocalCache(): void {
    this.localCache.clear();
    this.logger.log('本地缓存已清空');
  }

  /**
   * 获取本地缓存统计信息
   */
  getLocalCacheStats(): any {
    return {
      size: this.localCache.size,
      maxSize: this.maxLocalItems,
      usagePercentage: (this.localCache.size / this.maxLocalItems) * 100
    };
  }
} 