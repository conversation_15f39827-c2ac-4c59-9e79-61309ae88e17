# 网关模块 (Gateway Module)

## 概述

网关模块是一个智能API网关，负责统一处理外部API请求，提供路由转发、处理模式决策、任务管理等功能。支持同步、异步和代理异步三种处理模式，能够根据请求特征自动选择最优的处理方式。

## 核心功能

### 1. 智能路由
- 动态路由配置和管理
- 支持路径参数和查询参数
- 路由健康检查和故障转移
- 路由性能监控和统计

### 2. 处理模式
- **同步模式 (SYNC)**: 直接转发请求并等待响应
- **异步模式 (ASYNC)**: 将任务加入队列，立即返回任务ID
- **代理异步模式 (PROXY_ASYNC)**: 网关代理等待任务完成后返回结果

### 3. 任务管理
- 任务状态跟踪和进度监控
- 支持SSE实时事件推送
- 任务结果缓存和过期清理
- 批量任务处理

### 4. 监控和日志
- 请求/响应性能监控
- 错误统计和告警
- 详细的访问日志
- 健康状态检查

## 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Controllers   │    │    Services     │    │   External      │
│                 │    │                 │    │   Services      │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ GatewayController│───▶│ GatewayService  │───▶│ OCR Service     │
│ OcrGatewayCtrl  │    │ RouterService   │    │ Address Service │
│ AddressGatewayCtrl│   │ ProxyService    │    │ Geo Service     │
│ GeoGatewayCtrl  │    │ TaskService     │    └─────────────────┘
└─────────────────┘    │ ProcessingMode  │
                       │ RouteConfig     │
                       └─────────────────┘
```

## API 端点

### OCR 服务
```typescript
POST /v1/op/ocr/upload          // 文件上传OCR识别
POST /v1/op/ocr/recognize       // Base64图片OCR识别
POST /v1/op/ocr/batch           // 批量OCR识别
GET  /v1/op/ocr/capabilities    // 获取OCR能力信息
```

### 地址服务
```typescript
POST /v1/op/address/extract         // 地址提取
POST /v1/op/address/extract/batch   // 批量地址提取
POST /v1/op/address/normalize       // 地址标准化
POST /v1/op/address/validate        // 地址验证
GET  /v1/op/address/capabilities    // 获取地址处理能力
```

### 地理坐标服务
```typescript
GET  /v1/op/geo/reverse         // 逆地理编码 (坐标转地址)
POST /v1/op/geo/reverse         // 逆地理编码 (POST方式)
GET  /v1/op/geo/forward         // 正地理编码 (地址转坐标)
POST /v1/op/geo/forward         // 正地理编码 (POST方式)
POST /v1/op/geo/batch           // 批量地理编码
POST /v1/op/geo/transform       // 坐标系转换
GET  /v1/op/geo/capabilities    // 获取地理编码能力
```

### 任务管理
```typescript
GET  /v1/op/tasks/:taskId           // 获取任务状态
GET  /v1/op/tasks/:taskId/events    // 任务事件流 (SSE)
GET  /v1/op/tasks                   // 获取任务列表
POST /v1/op/tasks/cleanup           // 清理过期任务
```

### 系统管理
```typescript
GET  /v1/op/health              // 健康检查
GET  /v1/op/stats               // 统计信息
GET  /v1/op/routes              // 路由配置
POST /v1/op/routes/reset        // 重置路由配置
```

## 使用示例

### 1. OCR识别 (文件上传)
```typescript
const formData = new FormData();
formData.append('file', file);
formData.append('type', 'logistics');
formData.append('mode', 'proxy-async');

const response = await fetch('/v1/op/ocr/upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-jwt-token'
  },
  body: formData
});

const result = await response.json();
console.log(result);
```

### 2. 地址提取
```typescript
const response = await fetch('/v1/op/address/extract', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-jwt-token'
  },
  body: JSON.stringify({
    text: '收件人:李四，电话:13900139000，地址:上海市浦东新区张江高科技园区1号楼3层',
    mode: 'sync'
  })
});

const result = await response.json();
console.log(result.data);
```

### 3. 任务状态查询
```typescript
// 轮询方式
const taskId = 'task_1234567890';
const response = await fetch(`/v1/op/tasks/${taskId}`, {
  headers: {
    'Authorization': 'Bearer your-jwt-token'
  }
});

const task = await response.json();
console.log(task.status, task.progress);

// SSE方式
const eventSource = new EventSource(`/v1/op/tasks/${taskId}/events`);
eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('任务更新:', data);
  
  if (data.type === 'final') {
    eventSource.close();
  }
};
```

## 配置说明

### 环境变量
```bash
# 服务端点配置
OCR_SERVICE_URL=http://localhost:8866
ADDRESS_SERVICE_URL=http://localhost:8866
GEO_SERVICE_URL=http://localhost:8866

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 网关配置
GATEWAY_MAX_FILE_SIZE=5242880  # 5MB
GATEWAY_MAX_PROXY_WAIT_TIME=120000  # 2分钟
GATEWAY_DEFAULT_TIMEOUT=30000  # 30秒
```

### 路由配置
路由配置支持动态修改，可通过API进行管理：

```typescript
// 更新路由配置
await fetch('/v1/op/routes/%2Fv1%2Fop%2Focr/config', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-jwt-token'
  },
  body: JSON.stringify({
    timeout: 60000,
    maxProxyWaitTime: 90000,
    allowAsync: true,
    allowProxyAsync: true
  })
});
```

## 错误处理

网关模块提供统一的错误处理机制：

```typescript
{
  "code": 400,
  "errorCode": "GATEWAY_INVALID_PROCESSING_MODE",
  "message": "无效的处理模式",
  "details": ["mode字段必须是sync、async或proxy-async之一"],
  "timestamp": "2023-10-01T10:00:00Z",
  "requestId": "req_1234567890"
}
```

## 性能优化

### 1. 处理模式自动选择
- 小文件/短文本：自动选择同步模式
- 大文件/长文本：自动选择异步模式
- 中等复杂度：选择代理异步模式

### 2. 缓存策略
- 路由配置缓存 (1小时)
- 任务结果缓存 (24小时)
- 服务健康状态缓存 (30秒)

### 3. 连接池管理
- HTTP连接复用
- 超时和重试机制
- 熔断器模式

## 监控指标

### 关键指标
- 请求总数和成功率
- 平均响应时间
- 并发请求数
- 队列任务数量
- 服务健康状态

### 监控端点
```typescript
GET /v1/op/stats    // 获取详细统计信息
GET /v1/op/health   // 获取健康状态
```

## 开发指南

### 添加新的服务路由
1. 在 `gateway.constants.ts` 中添加路由配置
2. 在相应的控制器中添加端点
3. 更新路由服务的初始化逻辑

### 自定义处理模式决策
可以通过继承 `ProcessingModeService` 来实现自定义的处理模式决策逻辑。

### 扩展任务类型
在 `TaskResultService` 中添加新的任务类型处理逻辑。

## 故障排查

### 常见问题
1. **路由不匹配**: 检查路由配置和请求路径
2. **服务不可用**: 检查目标服务状态和网络连接
3. **任务超时**: 调整超时配置或检查服务性能
4. **文件上传失败**: 检查文件大小和格式限制

### 日志查看
```bash
# 查看网关日志
docker logs -f backend-container | grep Gateway

# 查看特定请求日志
docker logs -f backend-container | grep "req_1234567890"
```

## 测试

运行单元测试：
```bash
npm run test src/modules/gateway
```

运行集成测试：
```bash
npm run test:e2e gateway
```
