const mysql = require('mysql2/promise');

async function resetUserService() {
  const connection = await mysql.createConnection({
    host: 'localhost', 
    user: 'root', 
    password: '123456', 
    database: 'openapidb'
  });
  
  try {
    console.log('=== 重置用户4的地址解析服务次数 ===');
    
    // 重置地址解析服务（service_id = 2）的次数
    const [result] = await connection.execute(
      'UPDATE user_service SET used_count = 0, remaining_count = total_count WHERE user_id = ? AND service_id = ?',
      [4, 2]
    );
    
    console.log('更新结果:', result.affectedRows, '条记录被更新');
    
    // 验证更新结果
    const [userServiceData] = await connection.execute(
      'SELECT * FROM user_service WHERE user_id = ? AND service_id = ?', 
      [4, 2]
    );
    
    if (userServiceData.length > 0) {
      const service = userServiceData[0];
      console.log('\n=== 更新后的地址解析服务状态 ===');
      console.log('服务ID:', service.service_id);
      console.log('总次数:', service.total_count);
      console.log('已用次数:', service.used_count);
      console.log('剩余次数:', service.remaining_count);
      console.log('是否启用:', service.enabled);
      
      if (service.remaining_count > 0) {
        console.log('\n✅ 地址解析服务次数重置成功！');
        console.log(`现在有 ${service.remaining_count} 次可用调用`);
      } else {
        console.log('\n❌ 重置失败，剩余次数仍为0');
      }
    }
    
  } catch (error) {
    console.error('重置失败:', error.message);
  } finally {
    await connection.end();
  }
}

resetUserService();
