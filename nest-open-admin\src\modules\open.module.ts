import { <PERSON>ware<PERSON><PERSON>umer, <PERSON><PERSON><PERSON>, OnModuleInit, RequestMethod, Logger } from '@nestjs/common';
import { RouterModule } from '@nestjs/core';
import { OPEN_PREFIX } from '../common/constant';

// 新的模块结构
import { ServiceModule } from './service/service.module';
import { UserModule } from './user/user.module';
import { AuthModule } from './auth/auth.module';
import { ApiKeyModule } from './api-key/api-key.module';
import { UserServiceModule } from './user-service/user-service.module';
import { OrderModule } from './order/order.module';
import { CallRecordModule } from './call-record/call-record.module';
import { AlertRecordModule } from './alert-record/alert-record.module';
import { OcrModule } from './ocr/ocr.module';
import { AddressModule } from './address/address.module';
import { ScheduleModule } from './schedule/schedule.module';
import { PaymentModule } from './payment/payment.module';
import { QueueModule } from './queue/queue.module';
import { GatewayModule } from './gateway/gateway.module';
import { ApiKeyService } from './api-key/api-key.service';
// import { AuthEventListener } from '../common/listeners/auth-event.listener';

/**
 * open模块
 */
@Module({
  imports: [
    RouterModule.register([
      {
        path: OPEN_PREFIX,
        module: AuthModule,
      },
      {
        path: OPEN_PREFIX,
        module: UserModule,
      },
      {
        path: OPEN_PREFIX,
        module: ServiceModule,
      },
      {
        path: OPEN_PREFIX,
        module: ApiKeyModule,
      },
      {
        path: OPEN_PREFIX,
        module: OrderModule,
      },
      {
        path: OPEN_PREFIX,
        module: UserServiceModule,
      },
      {
        path: OPEN_PREFIX,
        module: CallRecordModule,
      },
      {
        path: OPEN_PREFIX,
        module: AlertRecordModule,
      },
      {
        path: OPEN_PREFIX,
        module: ScheduleModule,
      },
      {
        path: OPEN_PREFIX,
        module: PaymentModule,
      },
      {
        path: OPEN_PREFIX,
        module: QueueModule,
      },
      {
        path: OPEN_PREFIX,
        module: GatewayModule,
      },
      {
        path: OPEN_PREFIX,
        module: OcrModule,
      },
      {
        path: OPEN_PREFIX,
        module: AddressModule,
      },
    ]),
    AuthModule,
    UserModule,
    ServiceModule,
    ApiKeyModule,
    OrderModule,
    UserServiceModule,
    CallRecordModule,
    AlertRecordModule,
    OcrModule,
    AddressModule,
    ScheduleModule,
    PaymentModule,
    GatewayModule,
  ],
  providers: [
    // AuthEventListener
  ],
  exports: [
    UserModule,
    AuthModule,
    ApiKeyModule,
    OrderModule,
    PaymentModule,
  ],
})
export class OpenModule implements OnModuleInit {
  private readonly logger = new Logger(OpenModule.name);

  constructor(private readonly apiKeyService: ApiKeyService) {}

  /**
   * 模块初始化时加载API密钥到缓存
   * 这是系统中唯一负责初始化API密钥缓存的地方
   */
  async onModuleInit() {
    try {
      this.logger.log('应用启动，准备预加载API密钥到Redis缓存...');
      
      // 延迟3秒，确保数据库连接已经建立
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 记录开始时间，用于计算加载耗时
      const startTime = Date.now();
      
      // 加载所有API密钥到缓存
      const loadedCount = await this.apiKeyService.loadApiKeysToCache();
      
      // 计算耗时
      const duration = Date.now() - startTime;
      this.logger.log(`成功预加载 ${loadedCount} 个API密钥到缓存，耗时 ${duration}ms`);
      
      // 验证一些重要的API密钥是否已成功加载到缓存
      this.logger.debug('开始验证API密钥缓存状态...');
      await this.verifyCacheState();
    } catch (error) {
      this.logger.error('预加载API密钥到缓存失败:', error.message);
      this.logger.error('系统将继续启动，但API密钥认证可能不可用');
      // 不抛出异常，允许应用继续启动
    }
  }
  
  /**
   * 验证缓存状态
   * 检查一些关键API密钥是否成功加载到缓存
   */
  private async verifyCacheState(): Promise<void> {
    try {
      // 获取所有活跃的API密钥
      const apiKeysResponse = await this.apiKeyService.findAll({ 
        keyStatus: 'ACTIVE',
        limit: 5 // 只获取前5个作为样本
      } as any);
      
      if (apiKeysResponse.data.length === 0) {
        this.logger.warn('没有找到活跃的API密钥，系统可能处于初始化状态');
        return;
      }
      
      // 检查样本API密钥是否已经缓存
      let cachedCount = 0;
      for (const apiKey of apiKeysResponse.data) {
        const cacheKey = `api_key:${apiKey.apiKey}`;
        const secretKeyRedisKey = `api_key_secret:${apiKey.id}`;
        
        const [cachedData, cachedSecretKey] = await Promise.all([
          this.apiKeyService['redisService'].get(cacheKey),
          this.apiKeyService['redisService'].get(secretKeyRedisKey)
        ]);
        
        if (cachedData && cachedSecretKey) {
          cachedCount++;
        } else if (!cachedData) {
          this.logger.warn(`API密钥 #${apiKey.id} (${apiKey.apiKey}) 基本信息未缓存`);
        } else if (!cachedSecretKey) {
          this.logger.warn(`API密钥 #${apiKey.id} (${apiKey.apiKey}) Secret Key未缓存`);
        }
      }
      
      this.logger.debug(`验证样本中 ${cachedCount}/${apiKeysResponse.data.length} 个API密钥完全缓存`);
    } catch (error) {
      this.logger.error('验证API密钥缓存状态失败:', error.message);
    }
  }
}
