import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  ManyToOne,
  JoinColumn,
  Index,
  AfterLoad,
} from 'typeorm';
import { BaseEntity } from '../../../common/entities/base.entity';
import { Exclude } from 'class-transformer';

@Entity('user_service', {
  comment: '用户服务表',
})
@Index(['userId', 'serviceId'], { unique: true })
export class UserServiceEntity extends BaseEntity {
  @Column({ type: 'int', name: 'user_id', comment: '用户ID' })
  userId: number;

  @Column({ type: 'int', name: 'service_id', comment: '服务ID' })
  serviceId: number;

  @Column({ name: 'total_count', default: 0, comment: '总调用次数' })
  totalCount: number;

  @Column({ name: 'used_count', default: 0, comment: '已用次数' })
  usedCount: number;

  @Column({ name: 'remaining_count', default: 0, comment: '剩余调用次数' })
  remainingCount: number;

  @Column({ name: 'free_count', default: 0, comment: '免费次数' })
  freeCount: number;

  @Column({ name: 'purchased_count', default: 0, comment: '购买次数' })
  purchasedCount: number;

  @Column({ name: 'free_used_today', default: 0, comment: '今日已用免费次数' })
  freeUsedToday: number;

  @Column({ name: 'last_reset_date', type: 'date', nullable: true, comment: '上次免费额度重置日期' })
  lastResetDate?: Date;

  @Column({ name: 'alert_sent', default: false, comment: '是否已发送预警' })
  alertSent: boolean;

  @Column({ name: 'enabled', default: true, comment: '是否启用' })
  enabled: boolean;

  @Column({ name: 'last_used_at', type: 'timestamp', nullable: true, comment: '最近一次使用时间' })
  lastUsedAt?: Date;

  @Column({ name: 'notes', type: 'varchar', length: 500, nullable: true, comment: '备注信息' })
  notes?: string;

  // 关系 - 使用字符串关系定义，避免循环引用
  @ManyToOne('UserEntity', 'userServices')
  @JoinColumn({ name: 'user_id' })
  user: any;

  @ManyToOne('ServiceEntity', 'userServices')
  @JoinColumn({ name: 'service_id' })
  service: any;

  /**
   * 在从数据库加载实体后自动计算剩余次数
   * 确保 remainingCount 字段始终与 totalCount - usedCount 保持一致
   */
  @AfterLoad()
  calculateRemainingCount(): void {
    this.remainingCount = this.totalCount - this.usedCount;
  }
}
