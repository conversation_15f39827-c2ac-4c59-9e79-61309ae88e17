import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '@/shared/redis.service';
import { EmailService } from '@/shared/email.service';
import { SmsService } from '@/shared/sms.service';
import { CaptchaService } from '@/shared/captcha.service';
import { CodeType } from '../dto/captcha/send-email-code.dto';

/**
 * 验证码管理服务
 * 专门负责各种验证码的生成、发送、验证和管理
 */
@Injectable()
export class VerificationCodeService {
  private readonly logger = new Logger(VerificationCodeService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly emailService: EmailService,
    private readonly smsService: SmsService,
    private readonly captchaService: CaptchaService,
  ) {}

  /**
   * 发送邮箱验证码
   */
  async sendEmailCode(email: string, type: CodeType, securityVerification?: any): Promise<{
    message: string;
    cooldownSeconds: number;
  }> {
    // 验证安全验证数据
    if (securityVerification) {
      await this.captchaService.validateSecurityVerification(securityVerification);
    }

    // 检查冷却时间
    const cooldown = this.configService.get<number>('auth.security.verification.cooldown', 60);
    const cooldownKey = `email_code_cooldown:${email}`;
    const remainingCooldown = await this.checkCooldown(cooldownKey);

    if (remainingCooldown > 0) {
      throw new BadRequestException(`请等待 ${remainingCooldown} 秒后再次发送验证码`);
    }

    try {
      // 生成验证码
      const code = this.generateCode();
      const codeExpiry = this.configService.get<number>('auth.security.verification.codeExpiry', 300);

      // 存储验证码
      const codeKey = `email_code:${email}:${type}`;
      await this.redisService.set(codeKey, code, codeExpiry);

      // 设置冷却时间
      await this.redisService.set(cooldownKey, cooldown.toString(), cooldown);

      // 重置尝试次数
      const attemptsKey = `email_code_attempts:${email}:${type}`;
      await this.redisService.set(attemptsKey, '0', codeExpiry);

      // 发送验证码邮件
      await this.emailService.sendVerificationCode(email, code, type);

      this.logger.log(`邮箱验证码已发送: ${this.maskEmail(email)}, 类型: ${type}`);

      return {
        message: `验证码已发送到 ${this.maskEmail(email)}`,
        cooldownSeconds: cooldown,
      };
    } catch (error) {
      this.logger.error(`发送邮箱验证码失败: ${error.message}`, error.stack);
      throw new BadRequestException('发送验证码失败，请稍后再试');
    }
  }

  /**
   * 发送短信验证码
   */
  async sendSmsCode(phone: string, type: CodeType, securityVerification?: any): Promise<{
    message: string;
    cooldownSeconds: number;
  }> {
    // 验证安全验证数据
    if (securityVerification) {
      await this.captchaService.validateSecurityVerification(securityVerification);
    }

    // 检查冷却时间
    const cooldown = this.configService.get<number>('auth.security.verification.cooldown', 60);
    const cooldownKey = `sms_code_cooldown:${phone}`;
    const remainingCooldown = await this.checkCooldown(cooldownKey);

    if (remainingCooldown > 0) {
      throw new BadRequestException(`请等待 ${remainingCooldown} 秒后再次发送验证码`);
    }

    try {
      // 生成验证码
      const code = this.generateCode();
      const codeExpiry = this.configService.get<number>('auth.security.verification.codeExpiry', 300);

      // 存储验证码
      const codeKey = `sms_code:${phone}:${type}`;
      await this.redisService.set(codeKey, code, codeExpiry);

      // 设置冷却时间
      await this.redisService.set(cooldownKey, cooldown.toString(), cooldown);

      // 重置尝试次数
      const attemptsKey = `sms_code_attempts:${phone}:${type}`;
      await this.redisService.set(attemptsKey, '0', codeExpiry);

      // 发送验证码短信
      await this.smsService.sendVerificationCode(phone, code, type);

      this.logger.log(`短信验证码已发送: ${this.maskPhone(phone)}, 类型: ${code}-${type}`);

      return {
        message: `验证码已发送到 ${this.maskPhone(phone)}`,
        cooldownSeconds: cooldown,
      };
    } catch (error) {
      this.logger.error(`发送短信验证码失败: ${error.message}`, error.stack);
      throw new BadRequestException('发送验证码失败，请稍后再试');
    }
  }

  /**
   * 验证验证码
   */
  async verifyCode(identifier: string, code: string, type: CodeType, isEmail: boolean = true): Promise<boolean> {
    const prefix = isEmail ? 'email_code' : 'sms_code';
    const codeKey = `${prefix}:${identifier}:${type}`;
    const attemptsKey = `${prefix}_attempts:${identifier}:${type}`;

    // 获取存储的验证码
    const storedCode = await this.redisService.get(codeKey);
    if (!storedCode) {
      throw new BadRequestException('验证码已过期或不存在');
    }

    // 检查尝试次数
    const maxAttempts = this.configService.get<number>('auth.security.verification.maxAttempts', 3);
    const attempts = parseInt(await this.redisService.get(attemptsKey) || '0', 10);

    if (attempts >= maxAttempts) {
      // 删除验证码和尝试次数记录
      await this.redisService.del(codeKey);
      await this.redisService.del(attemptsKey);
      throw new BadRequestException('验证码尝试次数过多，请重新获取验证码');
    }

    // 增加尝试次数
    const codeTtl = await this.redisService.ttl(codeKey);
    const attemptsTtl = codeTtl > 0 ? codeTtl : 300; // 如果获取不到TTL，默认5分钟
    await this.redisService.set(attemptsKey, (attempts + 1).toString(), attemptsTtl);

    // 验证码是否匹配
    if (storedCode !== code) {
      this.logger.warn(`验证码错误: ${isEmail ? this.maskEmail(identifier) : this.maskPhone(identifier)}, 尝试次数: ${attempts + 1}`);
      throw new BadRequestException('验证码错误');
    }

    // 验证成功，删除验证码和尝试次数记录
    await this.redisService.del(codeKey);
    await this.redisService.del(attemptsKey);

    this.logger.log(`验证码验证成功: ${isEmail ? this.maskEmail(identifier) : this.maskPhone(identifier)}, 类型: ${type}`);
    return true;
  }

  /**
   * 检查冷却时间
   */
  private async checkCooldown(cooldownKey: string): Promise<number> {
    const ttl = await this.redisService.ttl(cooldownKey);
    return ttl > 0 ? ttl : 0;
  }

  /**
   * 生成验证码
   */
  private generateCode(): string {
    const codeLength = this.configService.get<number>('auth.security.verification.codeLength', 6);
    return Array.from({ length: codeLength }, () => Math.floor(Math.random() * 10)).join('');
  }

  /**
   * 掩码邮箱地址
   */
  private maskEmail(email: string): string {
    const [username, domain] = email.split('@');
    const maskedUsername = username.length <= 2
      ? '*'.repeat(username.length)
      : username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);
    return `${maskedUsername}@${domain}`;
  }

  /**
   * 掩码手机号
   */
  private maskPhone(phone: string): string {
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }

  /**
   * 获取验证码剩余有效时间
   */
  async getCodeTtl(identifier: string, type: CodeType, isEmail: boolean = true): Promise<number> {
    const prefix = isEmail ? 'email_code' : 'sms_code';
    const codeKey = `${prefix}:${identifier}:${type}`;
    return await this.redisService.ttl(codeKey);
  }

  /**
   * 获取冷却剩余时间
   */
  async getCooldownTtl(identifier: string, isEmail: boolean = true): Promise<number> {
    const prefix = isEmail ? 'email_code_cooldown' : 'sms_code_cooldown';
    const cooldownKey = `${prefix}:${identifier}`;
    return await this.redisService.ttl(cooldownKey);
  }

  /**
   * 清除验证码（用于管理员操作或紧急情况）
   */
  async clearCode(identifier: string, type: CodeType, isEmail: boolean = true): Promise<void> {
    const prefix = isEmail ? 'email_code' : 'sms_code';
    const codeKey = `${prefix}:${identifier}:${type}`;
    const attemptsKey = `${prefix}_attempts:${identifier}:${type}`;
    
    await Promise.all([
      this.redisService.del(codeKey),
      this.redisService.del(attemptsKey),
    ]);

    this.logger.log(`验证码已清除: ${isEmail ? this.maskEmail(identifier) : this.maskPhone(identifier)}, 类型: ${type}`);
  }

  /**
   * 批量清除过期验证码（定时任务使用）
   */
  async cleanupExpiredCodes(): Promise<void> {
    try {
      const patterns = [
        'email_code:*',
        'sms_code:*',
        'email_code_attempts:*',
        'sms_code_attempts:*',
        'email_code_cooldown:*',
        'sms_code_cooldown:*',
      ];

      for (const pattern of patterns) {
        const keys = await this.redisService.keys(pattern);
        if (keys.length > 0) {
          // 检查每个key的TTL，删除已过期的
          for (const key of keys) {
            const ttl = await this.redisService.ttl(key);
            if (ttl === -1) { // 没有设置过期时间的异常数据
              await this.redisService.del(key);
            }
          }
        }
      }

      this.logger.log('过期验证码清理完成');
    } catch (error) {
      this.logger.error(`清理过期验证码失败: ${error.message}`, error.stack);
    }
  }
}
