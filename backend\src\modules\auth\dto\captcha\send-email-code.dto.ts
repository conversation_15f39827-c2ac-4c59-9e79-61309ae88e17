import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsNotEmpty, IsObject, IsString } from 'class-validator';

// 验证码类型枚举
export enum CodeType {
  REGISTER = 'register',
  LOGIN = 'login',
  RESET = 'reset'
}

export class SendEmailCodeDto {
  @IsEmail({}, { message: '邮箱格式不正确' })
  @IsNotEmpty({ message: '邮箱不能为空' })
  @ApiProperty({ description: '邮箱' })
  email: string;
  
  @IsEnum(CodeType, { message: '验证码类型无效' })
  @ApiProperty({ description: '验证码类型', enum: CodeType })
  type: CodeType;
  
  @IsObject()
  @ApiProperty({ description: '安全验证数据' })
  securityVerification: {
    level: number;
    behaviorPattern: any;
    verificationTimestamp: number;
  };
} 