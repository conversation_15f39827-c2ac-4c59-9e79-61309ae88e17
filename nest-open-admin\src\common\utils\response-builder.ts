import { HttpStatus } from '@nestjs/common';

/**
 * 标准响应格式接口
 */
export interface StandardResponse<T = any> {
  success: boolean;
  code: number;
  message: string;
  data?: T;
  timestamp: string;
  requestId?: string;
  duration?: number;
  cached?: boolean;
  pagination?: PaginationInfo;
  meta?: Record<string, any>;
}

/**
 * 分页信息接口
 */
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * 响应构建器选项
 */
export interface ResponseBuilderOptions {
  requestId?: string;
  duration?: number;
  cached?: boolean;
  pagination?: PaginationInfo;
  meta?: Record<string, any>;
}

/**
 * 响应构建器工具类
 * 提供标准化的响应格式构建方法
 */
export class ResponseBuilder {
  /**
   * 构建成功响应
   */
  static success<T>(
    data?: T,
    message: string = 'Success',
    options?: ResponseBuilderOptions,
  ): StandardResponse<T> {
    return this.build({
      success: true,
      code: HttpStatus.OK,
      message,
      data,
      ...options,
    });
  }

  /**
   * 构建创建成功响应
   */
  static created<T>(
    data?: T,
    message: string = 'Created successfully',
    options?: ResponseBuilderOptions,
  ): StandardResponse<T> {
    return this.build({
      success: true,
      code: HttpStatus.OK,
      message,
      data,
      ...options,
    });
  }

  /**
   * 构建无内容响应
   */
  static noContent(
    message: string = 'No content',
    options?: ResponseBuilderOptions,
  ): StandardResponse<null> {
    return this.build({
      success: true,
      code: HttpStatus.OK,
      message,
      data: null,
      ...options,
    });
  }

  /**
   * 构建错误响应
   */
  static error(
    message: string = 'Internal Server Error',
    code: number = HttpStatus.INTERNAL_SERVER_ERROR,
    data?: any,
    options?: ResponseBuilderOptions,
  ): StandardResponse<any> {
    return this.build({
      success: false,
      code,
      message,
      data,
      ...options,
    });
  }

  /**
   * 构建验证错误响应
   */
  static validationError(
    errors: any,
    message: string = 'Validation failed',
    options?: ResponseBuilderOptions,
  ): StandardResponse<any> {
    return this.build({
      success: false,
      code: HttpStatus.BAD_REQUEST,
      message,
      data: { errors },
      ...options,
    });
  }

  /**
   * 构建未授权响应
   */
  static unauthorized(
    message: string = 'Unauthorized',
    options?: ResponseBuilderOptions,
  ): StandardResponse<null> {
    return this.build({
      success: false,
      code: HttpStatus.UNAUTHORIZED,
      message,
      data: null,
      ...options,
    });
  }

  /**
   * 构建禁止访问响应
   */
  static forbidden(
    message: string = 'Forbidden',
    options?: ResponseBuilderOptions,
  ): StandardResponse<null> {
    return this.build({
      success: false,
      code: HttpStatus.FORBIDDEN,
      message,
      data: null,
      ...options,
    });
  }

  /**
   * 构建未找到响应
   */
  static notFound(
    message: string = 'Not found',
    options?: ResponseBuilderOptions,
  ): StandardResponse<null> {
    return this.build({
      success: false,
      code: HttpStatus.NOT_FOUND,
      message,
      data: null,
      ...options,
    });
  }

  /**
   * 构建冲突响应
   */
  static conflict(
    message: string = 'Conflict',
    data?: any,
    options?: ResponseBuilderOptions,
  ): StandardResponse<any> {
    return this.build({
      success: false,
      code: HttpStatus.CONFLICT,
      message,
      data,
      ...options,
    });
  }

  /**
   * 构建限流响应
   */
  static tooManyRequests(
    message: string = 'Too many requests',
    retryAfter?: number,
    options?: ResponseBuilderOptions,
  ): StandardResponse<any> {
    const data = retryAfter ? { retryAfter } : undefined;
    return this.build({
      success: false,
      code: HttpStatus.TOO_MANY_REQUESTS,
      message,
      data,
      ...options,
    });
  }

  /**
   * 构建分页响应
   */
  static paginated<T>(
    data: T[],
    pagination: PaginationInfo,
    message: string = 'Success',
    options?: Omit<ResponseBuilderOptions, 'pagination'>,
  ): StandardResponse<T[]> {
    return this.build({
      success: true,
      code: HttpStatus.OK,
      message,
      data,
      pagination,
      ...options,
    });
  }

  /**
   * 构建自定义响应
   */
  static custom<T>(
    success: boolean,
    code: number,
    message: string,
    data?: T,
    options?: ResponseBuilderOptions,
  ): StandardResponse<T> {
    return this.build({
      success,
      code,
      message,
      data,
      ...options,
    });
  }

  /**
   * 核心构建方法
   */
  private static build<T>(params: {
    success: boolean;
    code: number;
    message: string;
    data?: T;
    requestId?: string;
    duration?: number;
    cached?: boolean;
    pagination?: PaginationInfo;
    meta?: Record<string, any>;
  }): StandardResponse<T> {
    const response: StandardResponse<T> = {
      success: params.success,
      code: params.code,
      message: params.message,
      timestamp: new Date().toISOString(),
    };

    // 只在有数据时添加data字段
    if (params.data !== undefined) {
      response.data = params.data;
    }

    // 添加可选字段
    if (params.requestId) {
      response.requestId = params.requestId;
    }

    if (params.duration !== undefined) {
      response.duration = params.duration;
    }

    if (params.cached !== undefined) {
      response.cached = params.cached;
    }

    if (params.pagination) {
      response.pagination = params.pagination;
    }

    if (params.meta) {
      response.meta = params.meta;
    }

    return response;
  }

  /**
   * 创建分页信息
   */
  static createPagination(
    page: number,
    limit: number,
    total: number,
  ): PaginationInfo {
    const totalPages = Math.ceil(total / limit);
    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * 从查询参数创建分页信息
   */
  static createPaginationFromQuery(
    query: { page?: string | number; limit?: string | number },
    total: number,
    defaultLimit: number = 10,
    maxLimit: number = 100,
  ): PaginationInfo {
    const page = Math.max(1, parseInt(String(query.page || 1), 10));
    const limit = Math.min(
      maxLimit,
      Math.max(1, parseInt(String(query.limit || defaultLimit), 10)),
    );

    return this.createPagination(page, limit, total);
  }

  /**
   * 检查响应是否成功
   */
  static isSuccess(response: StandardResponse): boolean {
    return response.success && response.code >= 200 && response.code < 300;
  }

  /**
   * 检查响应是否为错误
   */
  static isError(response: StandardResponse): boolean {
    return !response.success || response.code >= 400;
  }

  /**
   * 从错误对象创建响应
   */
  static fromError(
    error: any,
    defaultMessage: string = 'Internal Server Error',
    options?: ResponseBuilderOptions,
  ): StandardResponse<any> {
    let code = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = defaultMessage;
    let data: any;

    if (error?.status || error?.statusCode) {
      code = error.status || error.statusCode;
    }

    if (error?.message) {
      message = error.message;
    }

    if (error?.response) {
      data = error.response;
    } else if (error?.details) {
      data = error.details;
    }

    return this.error(message, code, data, options);
  }
}